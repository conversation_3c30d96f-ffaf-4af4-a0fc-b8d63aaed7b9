<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549584658434" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1200" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.v400{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(0,255,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.499999999999999" x2="5.916666666666666" y1="3.583333333333334" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_3" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_4" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="14.07232884821164" y2="14.07232884821164"/>
  </symbol>
  <symbol id="Accessory:避雷器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000003" y2="8.666666666666668"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="12.6" y2="18.6"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="2" x="12.00516117969822" xlink:href="#terminal" y="7.908504801097395"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:熔断器_0" viewBox="0,0,10,18">
   <use terminal-index="0" type="0" x="5.016666666666667" xlink:href="#terminal" y="1.083333333333336"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="1" y2="17"/>
   <rect fill-opacity="0" height="16.08" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.06,9.06) scale(1,1) translate(0,0)" width="9.58" x="0.27" y="1.02"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变带负荷_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <rect fill-opacity="0" height="4.32" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.06,5.25) scale(1,1) translate(0,0)" width="3.43" x="8.34" y="3.09"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.0235180220435" x2="10.0235180220435" y1="0.5833333333333091" y2="9.290410445610181"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="11.6895889186774" y1="10.61388616667628" y2="13.9574037777906"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869541" x2="11.6895889186774" y1="13.9574037777906" y2="13.9574037777906"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627346" x2="8.329445933869529" y1="10.61388616667628" y2="13.9574037777906"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="23.31619780557639" y2="23.31619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="24.15207720835499" y2="24.15207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="24.98795661113357" y2="24.98795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="23.31619780557638"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="28.23101083579602" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.09764705882352" x2="9.277450980392153" y1="29.58333333333332" y2="28.23848223081002"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.09764705882353" x2="10.9178431372549" y1="29.58333333333332" y2="28.23848223081002"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.277450980392169" x2="10.91784313725491" y1="28.23848223081002" y2="28.23848223081002"/>
  </symbol>
  <symbol id="Accessory:10kV母线PT带消谐装置_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="9.062458524793254" xlink:href="#terminal" y="41.29040359122629"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,25.61,15.59) scale(1,1) translate(0,0)" width="11" x="20.11" y="13.09"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.61245852479332" x2="29.61245852479332" y1="19.34040359122622" y2="12.34040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.41666666666667" x2="25.8624585247933" y1="23.94225544307809" y2="23.94225544307809"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.86245852479331" x2="25.86245852479331" y1="23.93299618381883" y2="21.09040359122624"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.6124585247933" x2="21.6124585247933" y1="21.34040359122623" y2="19.34040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.61245852479333" x2="29.61245852479333" y1="12.34040359122622" y2="10.34040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.062458524793264" x2="9.062458524793264" y1="31.39040359122627" y2="25.89040359122627"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.64772110866599" x2="25.64772110866599" y1="10.08674821859629" y2="3.405450010721147"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.71438777533266" x2="23.71438777533266" y1="3.016127685651949" y2="3.016127685651949"/>
   <ellipse cx="5.54" cy="23.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.55048569403981" x2="13.55048569403981" y1="35.78084700683308" y2="35.78084700683308"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.7608382776216" x2="23.48402243293775" y1="3.254208141873306" y2="3.254208141873306"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.01083827762157" x2="24.4006890996044" y1="2.00420814187332" y2="2.00420814187332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.51083827762159" x2="25.06735576627107" y1="0.5042081418732955" y2="0.5042081418732955"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="25.30654513693459" y2="22.68563107372743"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="22.79040359122621" y2="22.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="25.40299989806297" y2="22.78208583485582"/>
   <ellipse cx="5.54" cy="16.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="23.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.8644003886642" x2="13.8644003886642" y1="36.19213090500035" y2="36.19213090500035"/>
   <ellipse cx="12.54" cy="16.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="18.07076893362115" y2="18.07076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="24.32076893362116" y2="24.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="16.72611741162254" y2="17.85543752773795"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="18.24992879670395" y2="16.72611741162254"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="16.72611741162252" y2="14.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="17.57076893362115" y2="17.57076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="23.97611741162255" y2="21.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="25.49992879670398" y2="23.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="23.97611741162255" y2="25.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="16.72611741162253" y2="14.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="18.24992879670396" y2="16.72611741162255"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="16.72611741162254" y2="17.85543752773796"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.09103363843511" x2="9.09103363843511" y1="31.15822158129307" y2="41.41276827235689"/>
  </symbol>
  <symbol id="Compensator:勐养电容器2_0" viewBox="0,0,65,40">
   <use terminal-index="0" type="0" x="1.038011695906434" xlink:href="#terminal" y="23.04093567251462"/>
   <path d="M 38.9327 13.6842 L 29.2251 13.6842 L 29.2251 23.2749" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 16.5936 13.0994 L 16.5936 23.2749 L 32.7339 23.2749" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 4.42982 23.1579 L 4.42982 4.91228 L 63.3772 4.91228 L 63.3772 23.1579 L 61.9737 23.1579" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.885964912280699" x2="0.8040935672514564" y1="23.04093567251462" y2="23.04093567251462"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="48.34795321637426" x2="48.34795321637426" y1="15.02923976608188" y2="12.22222222222223"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="49.75146198830409" x2="49.75146198830409" y1="14.32748538011696" y2="12.92397660818714"/>
   <rect fill-opacity="0" height="12.4" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,37.82,13.63) scale(1,1) translate(0,0)" width="5.61" x="35.01" y="7.43"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="46.94444444444444" x2="44.13742690058479" y1="13.62573099415205" y2="13.62573099415205"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="46.94444444444444" x2="46.94444444444444" y1="15.73099415204679" y2="11.52046783625732"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="40.62865497076023" x2="58.87426900584795" y1="35.38011695906433" y2="35.38011695906433"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38.99122807017543" x2="34.78070175438596" y1="13.62573099415205" y2="12.22222222222222"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38.99122807017543" x2="34.78070175438596" y1="13.62573099415205" y2="15.02923976608188"/>
   <rect fill-opacity="0" height="5.61" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,42.85,23.33) scale(1,1) translate(0,0)" width="2.81" x="41.45" y="20.53"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="61.83333333333333" x2="59.92690058479532" y1="32.39872849481935" y2="32.39872849481935"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="61.71637426900585" x2="61.71637426900585" y1="14.73684210526316" y2="32.28070175438597"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="59.80994152046784" x2="61.83333333333333" y1="14.76023391812867" y2="14.76023391812867"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="58.44152046783626" x2="61.91520467836257" y1="23.29824561403509" y2="23.29824561403509"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.55653021442495" x2="58.7014294996751" y1="29.66601689408707" y2="29.66601689408707"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="37.2719298245614" x2="37.2719298245614" y1="29.70760233918129" y2="23.29824561403509"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="58.67543859649122" x2="58.67543859649122" y1="23.45029239766082" y2="29.70760233918129"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.73391812865497" x2="52.35964912280703" y1="23.29824561403509" y2="23.29824561403509"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="53.76315789473684" x2="58.70142949967511" y1="23.29824561403509" y2="23.29824561403509"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="40.42982456140351" x2="37.33690708252111" y1="29.6140350877193" y2="29.6140350877193"/>
   <path d="M 45.4808 29.614 A 4.15988 2.55083 -90 0 1 40.3792 29.614" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 50.5825 29.614 A 4.15988 2.55083 -90 0 1 45.4808 29.614" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 55.5438 29.614 A 4.15988 2.55083 -90 0 1 50.4422 29.614" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="52.35964912280701" x2="52.35964912280701" y1="21.19298245614035" y2="25.40350877192983"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="53.76315789473684" x2="53.76315789473684" y1="21.19298245614035" y2="25.40350877192983"/>
   <path d="M 16.4064 13.0409 A 9.60234 9.97329 -360 1 1 6.80409 23.0142" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV塘古地" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass"/>
 <g id="OtherClass">
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="48" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,131.667,64.1667) scale(1,1) translate(0,0)" writing-mode="lr" x="131.67" xml:space="preserve" y="81.17" zvalue="3060">塘古地</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="11" id="513" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,756.893,762.595) scale(1,1) translate(0,0)" writing-mode="lr" x="756.89" xml:space="preserve" y="766.1" zvalue="1918">10kV#Ⅰ母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="11" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,639.362,346.5) scale(1,1) translate(0,0)" writing-mode="lr" x="639.36" xml:space="preserve" y="350" zvalue="2265">35kV#Ⅰ母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="11" id="349" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1039.58,594.593) scale(1,1) translate(0,0)" writing-mode="lr" x="1039.58" xml:space="preserve" y="598.09" zvalue="2348">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="11" id="405" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,974.296,179.552) scale(1,1) translate(0,0)" writing-mode="lr" x="974.3" xml:space="preserve" y="183.05" zvalue="3056">35kV皮塘线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="190" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,770.286,413.375) scale(1,1) translate(0,0)" writing-mode="lr" x="770.29" xml:space="preserve" y="416.88" zvalue="4017">3611</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="194" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,790.472,577.264) scale(1,1) translate(0,0)" writing-mode="lr" x="790.47" xml:space="preserve" y="581.76" zvalue="4020">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="195" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1211.7,401.653) scale(1,1) translate(0,0)" writing-mode="lr" x="1211.7" xml:space="preserve" y="405.15" zvalue="4026">3901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="222" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1180.03,565.937) scale(1,1) translate(0,0)" writing-mode="lr" x="1180.03" xml:space="preserve" y="570.4400000000001" zvalue="4030">35kV#Ⅰ母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="382" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,874.055,850.114) scale(1,1) translate(0,0)" writing-mode="lr" x="874.0599999999999" xml:space="preserve" y="853.61" zvalue="4110">06117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="381" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,921.065,857.629) scale(1,1) translate(0,0)" writing-mode="lr" x="921.0599999999999" xml:space="preserve" y="861.13" zvalue="4115">061</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="380" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,906.046,1036.25) scale(1,1) translate(0,0)" writing-mode="lr" x="906.05" xml:space="preserve" y="1039.25" zvalue="4123">10kV#1出线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="378" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,933.969,912.296) scale(1,1) translate(0,0)" writing-mode="lr" x="933.97" xml:space="preserve" y="915.8" zvalue="4126">0616</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,762.876,457.694) scale(1,1) translate(0,0)" writing-mode="lr" x="762.88" xml:space="preserve" y="461.19" zvalue="4280">36117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1161.45,446.266) scale(1,1) translate(0,0)" writing-mode="lr" x="1161.45" xml:space="preserve" y="449.77" zvalue="4285">39017</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1001.84,494.033) scale(1,1) translate(0,0)" writing-mode="lr" x="1001.84" xml:space="preserve" y="497.53" zvalue="4291">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1010.11,437.379) scale(1,1) translate(0,0)" writing-mode="lr" x="1010.11" xml:space="preserve" y="440.88" zvalue="4293">3011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,913.936,464.431) scale(1,1) translate(0,0)" writing-mode="lr" x="913.9400000000001" xml:space="preserve" y="467.93" zvalue="4295">30117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1004.69,665.462) scale(1,1) translate(0,0)" writing-mode="lr" x="1004.69" xml:space="preserve" y="668.96" zvalue="4303">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1014.38,744.522) scale(1,1) translate(0,0)" writing-mode="lr" x="1014.38" xml:space="preserve" y="748.02" zvalue="4305">0011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,919.65,710.146) scale(1,1) translate(0,0)" writing-mode="lr" x="919.65" xml:space="preserve" y="713.65" zvalue="4307">00117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,933.969,816.962) scale(1,1) translate(0,0)" writing-mode="lr" x="933.97" xml:space="preserve" y="820.46" zvalue="4318">0611</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,876.055,952.114) scale(1,1) translate(0,0)" writing-mode="lr" x="876.0599999999999" xml:space="preserve" y="955.61" zvalue="4321">06167</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1008.06,850.114) scale(1,1) translate(0,0)" writing-mode="lr" x="1008.06" xml:space="preserve" y="853.61" zvalue="4331">06217</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1055.06,857.629) scale(1,1) translate(0,0)" writing-mode="lr" x="1055.06" xml:space="preserve" y="861.13" zvalue="4334">062</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="8" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1040.05,1036.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1040.05" xml:space="preserve" y="1039.25" zvalue="4336">10kV#2出线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1067.97,912.296) scale(1,1) translate(0,0)" writing-mode="lr" x="1067.97" xml:space="preserve" y="915.8" zvalue="4338">0626</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1067.97,816.962) scale(1,1) translate(0,0)" writing-mode="lr" x="1067.97" xml:space="preserve" y="820.46" zvalue="4340">0621</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1010.06,952.114) scale(1,1) translate(0,0)" writing-mode="lr" x="1010.06" xml:space="preserve" y="955.61" zvalue="4342">06267</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1186.06,850.114) scale(1,1) translate(0,0)" writing-mode="lr" x="1186.06" xml:space="preserve" y="853.61" zvalue="4352">06317</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1233.06,857.629) scale(1,1) translate(0,0)" writing-mode="lr" x="1233.06" xml:space="preserve" y="861.13" zvalue="4355">063</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1245.97,912.296) scale(1,1) translate(0,0)" writing-mode="lr" x="1245.97" xml:space="preserve" y="915.8" zvalue="4359">0636</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1245.97,816.962) scale(1,1) translate(0,0)" writing-mode="lr" x="1245.97" xml:space="preserve" y="820.46" zvalue="4361">0631</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1188.06,952.114) scale(1,1) translate(0,0)" writing-mode="lr" x="1188.06" xml:space="preserve" y="955.61" zvalue="4363">06367</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="114" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1222.25,1051.55) scale(1,1) translate(0,0)" writing-mode="lr" x="1222.25" xml:space="preserve" y="1056.05" zvalue="4372">10kV1号电容器</text>
 </g>
 <g id="BusbarSectionClass">
  <g id="512">
   <path class="kv10" d="M 779.89 786.64 L 1350.75 786.64" stroke-width="3" zvalue="1917"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674242658309" ObjectName="10kV#Ⅰ母线"/>
   <cge:TPSR_Ref TObjectID="9288674242658309"/></metadata>
  <path d="M 779.89 786.64 L 1350.75 786.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv35" d="M 714 365 L 1328.12 365" stroke-width="3" zvalue="2264"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV#Ⅰ母线"/>
   </metadata>
  <path d="M 714 365 L 1328.12 365" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="340">
   <g id="3400">
    <use class="kv35" height="30" transform="rotate(0,982.833,588.093) scale(-2.15278,2.10494) translate(-1425.54,-292.131)" width="24" x="957" xlink:href="#PowerTransformer2:可调两卷变_0" y="556.52" zvalue="2347"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874431856644" ObjectName="35"/>
    </metadata>
   </g>
   <g id="3401">
    <use class="kv10" height="30" transform="rotate(0,982.833,588.093) scale(-2.15278,2.10494) translate(-1425.54,-292.131)" width="24" x="957" xlink:href="#PowerTransformer2:可调两卷变_1" y="556.52" zvalue="2347"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874432053252" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399447543812" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399447543812"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,982.833,588.093) scale(-2.15278,2.10494) translate(-1425.54,-292.131)" width="24" x="957" y="556.52"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="99">
   <use class="kv35" height="26" transform="rotate(270,1009.45,278.936) scale(0.811869,1) translate(232.787,0)" width="12" x="1004.578035999088" xlink:href="#Accessory:避雷器_0" y="265.9361531986531" zvalue="2771"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV皮塘线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1009.45,278.936) scale(0.811869,1) translate(232.787,0)" width="12" x="1004.578035999088" y="265.9361531986531"/></g>
  <g id="216">
   <use class="kv35" height="42" transform="rotate(0,1180.03,521.501) scale(-1.45299,-1.45299) translate(-1985.38,-870.904)" width="30" x="1158.23717948718" xlink:href="#Accessory:10kV母线PT带消谐装置_0" y="490.9885290148447" zvalue="4029"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV#Ⅰ母线电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,1180.03,521.501) scale(-1.45299,-1.45299) translate(-1985.38,-870.904)" width="30" x="1158.23717948718" y="490.9885290148447"/></g>
  <g id="217">
   <use class="kv35" height="26" transform="rotate(0,1213.8,475.228) scale(-0.811869,1) translate(-2710.01,0)" width="12" x="1208.931932102985" xlink:href="#Accessory:避雷器_0" y="462.2275575301893" zvalue="4031"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV#Ⅰ母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1213.8,475.228) scale(-0.811869,1) translate(-2710.01,0)" width="12" x="1208.931932102985" y="462.2275575301893"/></g>
  <g id="218">
   <use class="kv35" height="18" transform="rotate(0,1188.61,471.731) scale(1.04167,1.11111) translate(-47.3359,-46.1731)" width="10" x="1183.397435897436" xlink:href="#Accessory:熔断器_0" y="461.7307692307693" zvalue="4032"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV#Ⅰ母线电压互感器熔断器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1188.61,471.731) scale(1.04167,1.11111) translate(-47.3359,-46.1731)" width="10" x="1183.397435897436" y="461.7307692307693"/></g>
  <g id="408">
   <use class="kv10" height="26" transform="rotate(0,887.304,982.583) scale(-0.811869,1) translate(-1981.35,0)" width="12" x="882.4330143540673" xlink:href="#Accessory:避雷器_0" y="969.5825358851675" zvalue="4111"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#1出线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,887.304,982.583) scale(-0.811869,1) translate(-1981.35,0)" width="12" x="882.4330143540673" y="969.5825358851675"/></g>
  <g id="86">
   <use class="kv10" height="26" transform="rotate(0,1021.3,982.583) scale(-0.811869,1) translate(-2280.4,0)" width="12" x="1016.433014354067" xlink:href="#Accessory:避雷器_0" y="969.5825358851675" zvalue="4332"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#2出线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1021.3,982.583) scale(-0.811869,1) translate(-2280.4,0)" width="12" x="1016.433014354067" y="969.5825358851675"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="406">
   <use class="kv35" height="30" transform="rotate(0,973.974,203.552) scale(2.32143,0.625) translate(-549.791,116.506)" width="7" x="965.8493117242504" xlink:href="#ACLineSegment:线路_0" y="194.1771978021977" zvalue="3055"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV皮塘线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,973.974,203.552) scale(2.32143,0.625) translate(-549.791,116.506)" width="7" x="965.8493117242504" y="194.1771978021977"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="187">
   <use class="kv35" height="30" transform="rotate(0,794.215,411.233) scale(0.766667,0.603049) translate(239.968,264.736)" width="15" x="788.4650377002337" xlink:href="#Disconnector:刀闸_0" y="402.1875000000002" zvalue="4016"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号站用变3611"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,794.215,411.233) scale(0.766667,0.603049) translate(239.968,264.736)" width="15" x="788.4650377002337" y="402.1875000000002"/></g>
  <g id="211">
   <use class="kv35" height="30" transform="rotate(0,1188.97,401.733) scale(0.766667,0.603049) translate(360.109,258.483)" width="15" x="1183.215037700234" xlink:href="#Disconnector:刀闸_0" y="392.6875000000002" zvalue="4025"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV#Ⅰ母线电压互感器3901"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1188.97,401.733) scale(0.766667,0.603049) translate(360.109,258.483)" width="15" x="1183.215037700234" y="392.6875000000002"/></g>
  <g id="387">
   <use class="kv10" height="30" transform="rotate(0,906.786,910.154) scale(0.766667,0.603049) translate(274.228,593.146)" width="15" x="901.0364646822054" xlink:href="#Disconnector:刀闸_0" y="901.1081349206352" zvalue="4125"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#1出线0616"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,906.786,910.154) scale(0.766667,0.603049) translate(274.228,593.146)" width="15" x="901.0364646822054" y="901.1081349206352"/></g>
  <g id="359">
   <use class="kv35" height="30" transform="rotate(0,981.479,437.129) scale(-0.831307,0.609625) translate(-2163.39,274.061)" width="15" x="975.2443311249698" xlink:href="#Disconnector:刀闸_0" y="427.9850674482213" zvalue="4292"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变35kV侧3011"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,981.479,437.129) scale(-0.831307,0.609625) translate(-2163.39,274.061)" width="15" x="975.2443311249698" y="427.9850674482213"/></g>
  <g id="48">
   <use class="kv10" height="30" transform="rotate(0,985.753,744.272) scale(-0.831307,0.609625) translate(-2172.81,470.741)" width="15" x="979.5183193548576" xlink:href="#Disconnector:刀闸_0" y="735.1279245910785" zvalue="4304"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变10kV侧0011"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,985.753,744.272) scale(-0.831307,0.609625) translate(-2172.81,470.741)" width="15" x="979.5183193548576" y="735.1279245910785"/></g>
  <g id="57">
   <use class="kv10" height="30" transform="rotate(0,906.786,814.821) scale(0.766667,0.603049) translate(274.228,530.394)" width="15" x="901.0364646911619" xlink:href="#Disconnector:刀闸_0" y="805.7748015873017" zvalue="4317"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#1出线0611"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,906.786,814.821) scale(0.766667,0.603049) translate(274.228,530.394)" width="15" x="901.0364646911619" y="805.7748015873017"/></g>
  <g id="83">
   <use class="kv10" height="30" transform="rotate(0,1040.79,910.154) scale(0.766667,0.603049) translate(315.011,593.146)" width="15" x="1035.036464682205" xlink:href="#Disconnector:刀闸_0" y="901.1081349206352" zvalue="4337"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#2出线0626"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1040.79,910.154) scale(0.766667,0.603049) translate(315.011,593.146)" width="15" x="1035.036464682205" y="901.1081349206352"/></g>
  <g id="82">
   <use class="kv10" height="30" transform="rotate(0,1040.79,814.821) scale(0.766667,0.603049) translate(315.011,530.394)" width="15" x="1035.036464691162" xlink:href="#Disconnector:刀闸_0" y="805.7748015873017" zvalue="4339"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#2出线0621"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1040.79,814.821) scale(0.766667,0.603049) translate(315.011,530.394)" width="15" x="1035.036464691162" y="805.7748015873017"/></g>
  <g id="108">
   <use class="kv10" height="30" transform="rotate(0,1218.79,910.154) scale(0.766667,0.603049) translate(369.185,593.146)" width="15" x="1213.036464682205" xlink:href="#Disconnector:刀闸_0" y="901.1081349206352" zvalue="4358"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV1号电容器0636"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1218.79,910.154) scale(0.766667,0.603049) translate(369.185,593.146)" width="15" x="1213.036464682205" y="901.1081349206352"/></g>
  <g id="106">
   <use class="kv10" height="30" transform="rotate(0,1218.79,814.821) scale(0.766667,0.603049) translate(369.185,530.394)" width="15" x="1213.036464691162" xlink:href="#Disconnector:刀闸_0" y="805.7748015873017" zvalue="4360"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV1号电容器0631"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1218.79,814.821) scale(0.766667,0.603049) translate(369.185,530.394)" width="15" x="1213.036464691162" y="805.7748015873017"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="191">
   <use class="kv35" height="30" transform="rotate(0,794.046,504.514) scale(3.03241,3.03241) translate(-511.869,-307.654)" width="20" x="763.7222222222218" xlink:href="#EnergyConsumer:站用变带负荷_0" y="459.0277777777777" zvalue="4019"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,794.046,504.514) scale(3.03241,3.03241) translate(-511.869,-307.654)" width="20" x="763.7222222222218" y="459.0277777777777"/></g>
  <g id="389">
   <use class="kv10" height="30" transform="rotate(0,906.786,1005.68) scale(1.04167,-1.02778) translate(-36.0215,-1983.77)" width="12" x="900.5364646873777" xlink:href="#EnergyConsumer:负荷_0" y="990.2658730158731" zvalue="4122"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#1出线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,906.786,1005.68) scale(1.04167,-1.02778) translate(-36.0215,-1983.77)" width="12" x="900.5364646873777" y="990.2658730158731"/></g>
  <g id="84">
   <use class="kv10" height="30" transform="rotate(0,1040.79,1005.68) scale(1.04167,-1.02778) translate(-41.3815,-1983.77)" width="12" x="1034.536464687378" xlink:href="#EnergyConsumer:负荷_0" y="990.2658730158731" zvalue="4335"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#2出线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1040.79,1005.68) scale(1.04167,-1.02778) translate(-41.3815,-1983.77)" width="12" x="1034.536464687378" y="990.2658730158731"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="223">
   <path class="kv35" d="M 1189.01 446.5 L 1213.78 446.5 L 1213.78 462.86" stroke-width="1" zvalue="4036"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="20" LinkObjectIDznd="217@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1189.01 446.5 L 1213.78 446.5 L 1213.78 462.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="224">
   <path class="kv35" d="M 1188.62 462.93 L 1188.66 492.02" stroke-width="1" zvalue="4037"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="" LinkObjectIDznd="218@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1188.62 462.93 L 1188.66 492.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1">
   <path class="kv35" d="M 973.97 212.83 L 973.97 365" stroke-width="1" zvalue="4272"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="406@0" LinkObjectIDznd="43@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 973.97 212.83 L 973.97 365" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="2">
   <path class="kv35" d="M 997.08 278.91 L 973.97 278.91" stroke-width="1" zvalue="4273"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="1" MaxPinNum="2"/>
   </metadata>
  <path d="M 997.08 278.91 L 973.97 278.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="5">
   <path class="kv35" d="M 794.28 402.49 L 794.28 365" stroke-width="1" zvalue="4276"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="187@0" LinkObjectIDznd="43@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 794.28 402.49 L 794.28 365" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="6">
   <path class="kv35" d="M 794.26 420.12 L 794.26 461.61" stroke-width="1" zvalue="4277"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="187@1" LinkObjectIDznd="191@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 794.26 420.12 L 794.26 461.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="kv35" d="M 772.38 442.58 L 794.26 442.58" stroke-width="1" zvalue="4281"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="4@0" LinkObjectIDznd="6" MaxPinNum="2"/>
   </metadata>
  <path d="M 772.38 442.58 L 794.26 442.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="kv35" d="M 1189.03 392.99 L 1189.03 365" stroke-width="1" zvalue="4286"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211@0" LinkObjectIDznd="43@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1189.03 392.99 L 1189.03 365" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="kv35" d="M 1189.01 410.62 L 1189.01 462.93" stroke-width="1" zvalue="4287"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211@1" LinkObjectIDznd="224" MaxPinNum="2"/>
   </metadata>
  <path d="M 1189.01 410.62 L 1189.01 462.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="21">
   <path class="kv35" d="M 1170.96 431.15 L 1189.01 431.15" stroke-width="1" zvalue="4288"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="18@0" LinkObjectIDznd="20" MaxPinNum="2"/>
   </metadata>
  <path d="M 1170.96 431.15 L 1189.01 431.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="37">
   <path class="kv35" d="M 981.44 487.64 L 981.43 446.12" stroke-width="1" zvalue="4296"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="358@0" LinkObjectIDznd="359@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 981.44 487.64 L 981.43 446.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv35" d="M 962.37 466.9 L 981.43 466.9" stroke-width="1" zvalue="4298"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="367@0" LinkObjectIDznd="37" MaxPinNum="2"/>
   </metadata>
  <path d="M 962.37 466.9 L 981.43 466.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="kv35" d="M 981.41 428.29 L 981.41 365" stroke-width="1" zvalue="4299"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="359@0" LinkObjectIDznd="43@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 981.41 428.29 L 981.41 365" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv35" d="M 981.52 502.42 L 981.52 558.78" stroke-width="1" zvalue="4300"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="358@1" LinkObjectIDznd="340@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 981.52 502.42 L 981.52 558.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv10" d="M 984.3 659.06 L 984.3 617.66" stroke-width="1" zvalue="4310"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@0" LinkObjectIDznd="340@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 984.3 659.06 L 984.3 617.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="kv10" d="M 984.38 673.85 L 984.38 735.43" stroke-width="1" zvalue="4311"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@1" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 984.38 673.85 L 984.38 735.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv10" d="M 985.7 753.26 L 985.7 786.64" stroke-width="1" zvalue="4312"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@1" LinkObjectIDznd="512@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 985.7 753.26 L 985.7 786.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="53">
   <path class="kv10" d="M 968.09 712.61 L 984.38 712.61" stroke-width="1" zvalue="4313"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@0" LinkObjectIDznd="51" MaxPinNum="2"/>
   </metadata>
  <path d="M 968.09 712.61 L 984.38 712.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv10" d="M 906.85 806.07 L 906.85 786.64" stroke-width="1" zvalue="4322"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57@0" LinkObjectIDznd="512@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 906.85 806.07 L 906.85 786.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv10" d="M 906.83 823.71 L 906.76 850.06" stroke-width="1" zvalue="4323"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57@1" LinkObjectIDznd="403@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 906.83 823.71 L 906.76 850.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="kv10" d="M 906.84 866.99 L 906.85 901.41" stroke-width="1" zvalue="4324"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="403@1" LinkObjectIDznd="387@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 906.84 866.99 L 906.85 901.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="kv10" d="M 906.83 919.04 L 906.79 991.81" stroke-width="1" zvalue="4325"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="387@1" LinkObjectIDznd="389@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 906.83 919.04 L 906.79 991.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv10" d="M 887.28 970.22 L 887.28 960 L 906.81 960" stroke-width="1" zvalue="4326"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="408@0" LinkObjectIDznd="63" MaxPinNum="2"/>
   </metadata>
  <path d="M 887.28 970.22 L 887.28 960 L 906.81 960" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv10" d="M 888.42 937 L 906.82 937" stroke-width="1" zvalue="4327"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@0" LinkObjectIDznd="63" MaxPinNum="2"/>
   </metadata>
  <path d="M 888.42 937 L 906.82 937" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv10" d="M 886.42 835 L 906.8 835" stroke-width="1" zvalue="4328"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="409@0" LinkObjectIDznd="61" MaxPinNum="2"/>
   </metadata>
  <path d="M 886.42 835 L 906.8 835" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv10" d="M 1040.85 806.07 L 1040.85 786.64" stroke-width="1" zvalue="4343"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@0" LinkObjectIDznd="512@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1040.85 806.07 L 1040.85 786.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv10" d="M 1040.83 823.71 L 1040.76 850.06" stroke-width="1" zvalue="4344"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@1" LinkObjectIDznd="85@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1040.83 823.71 L 1040.76 850.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv10" d="M 1040.84 866.99 L 1040.85 901.41" stroke-width="1" zvalue="4345"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85@1" LinkObjectIDznd="83@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1040.84 866.99 L 1040.85 901.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv10" d="M 1040.83 919.04 L 1040.79 991.81" stroke-width="1" zvalue="4346"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="83@1" LinkObjectIDznd="84@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1040.83 919.04 L 1040.79 991.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv10" d="M 1021.28 970.22 L 1021.28 960 L 1040.79 960" stroke-width="1" zvalue="4347"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@0" LinkObjectIDznd="76" MaxPinNum="2"/>
   </metadata>
  <path d="M 1021.28 970.22 L 1021.28 960 L 1040.79 960" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv10" d="M 1022.42 937 L 1040.83 937" stroke-width="1" zvalue="4348"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81@0" LinkObjectIDznd="76" MaxPinNum="2"/>
   </metadata>
  <path d="M 1022.42 937 L 1040.83 937" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv10" d="M 1020.42 835 L 1040.8 835" stroke-width="1" zvalue="4349"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="87@0" LinkObjectIDznd="79" MaxPinNum="2"/>
   </metadata>
  <path d="M 1020.42 835 L 1040.8 835" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="kv10" d="M 1218.85 806.07 L 1218.85 786.64" stroke-width="1" zvalue="4364"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@0" LinkObjectIDznd="512@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1218.85 806.07 L 1218.85 786.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="kv10" d="M 1218.83 823.71 L 1218.76 850.06" stroke-width="1" zvalue="4365"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@1" LinkObjectIDznd="110@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1218.83 823.71 L 1218.76 850.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="kv10" d="M 1218.84 866.99 L 1218.85 901.41" stroke-width="1" zvalue="4366"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110@1" LinkObjectIDznd="108@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1218.84 866.99 L 1218.85 901.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv10" d="M 1218.83 919.04 L 1218.83 978.67" stroke-width="1" zvalue="4367"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@1" LinkObjectIDznd="113@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1218.83 919.04 L 1218.83 978.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv10" d="M 1200.42 937 L 1218.83 937" stroke-width="1" zvalue="4369"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@0" LinkObjectIDznd="101" MaxPinNum="2"/>
   </metadata>
  <path d="M 1200.42 937 L 1218.83 937" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv10" d="M 1198.42 835 L 1218.8 835" stroke-width="1" zvalue="4370"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@0" LinkObjectIDznd="103" MaxPinNum="2"/>
   </metadata>
  <path d="M 1198.42 835 L 1218.8 835" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="409">
   <use class="kv10" height="30" transform="rotate(270,875.485,835.008) scale(-0.763595,-0.763595) translate(-2023.43,-1932.08)" width="12" x="870.9031867422539" xlink:href="#GroundDisconnector:地刀12_0" y="823.554042648234" zvalue="4109"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#1出线06117"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,875.485,835.008) scale(-0.763595,-0.763595) translate(-2023.43,-1932.08)" width="12" x="870.9031867422539" y="823.554042648234"/></g>
  <g id="4">
   <use class="kv35" height="30" transform="rotate(270,761.446,442.588) scale(-0.763595,-0.763595) translate(-1760.05,-1025.75)" width="12" x="756.8645750515556" xlink:href="#GroundDisconnector:地刀12_0" y="431.1343482730224" zvalue="4279"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号站用变36117"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,761.446,442.588) scale(-0.763595,-0.763595) translate(-1760.05,-1025.75)" width="12" x="756.8645750515556" y="431.1343482730224"/></g>
  <g id="18">
   <use class="kv35" height="30" transform="rotate(270,1160.02,431.16) scale(-0.763595,-0.763595) translate(-2680.59,-999.35)" width="12" x="1155.436003622984" xlink:href="#GroundDisconnector:地刀12_0" y="419.705776844451" zvalue="4284"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV#Ⅰ母线电压互感器39017"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1160.02,431.16) scale(-0.763595,-0.763595) translate(-2680.59,-999.35)" width="12" x="1155.436003622984" y="419.705776844451"/></g>
  <g id="367">
   <use class="kv35" height="30" transform="rotate(90,951.436,466.888) scale(-0.763595,0.763595) translate(-2198.85,141)" width="12" x="946.854481879659" xlink:href="#GroundDisconnector:地刀12_0" y="455.4339568801705" zvalue="4294"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变35kV侧30117"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,951.436,466.888) scale(-0.763595,0.763595) translate(-2198.85,141)" width="12" x="946.854481879659" y="455.4339568801705"/></g>
  <g id="47">
   <use class="kv10" height="30" transform="rotate(90,957.15,712.602) scale(-0.763595,0.763595) translate(-2212.05,217.072)" width="12" x="952.5687675939448" xlink:href="#GroundDisconnector:地刀12_0" y="701.1482425944561" zvalue="4306"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变10kV侧00117"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,957.15,712.602) scale(-0.763595,0.763595) translate(-2212.05,217.072)" width="12" x="952.5687675939448" y="701.1482425944561"/></g>
  <g id="59">
   <use class="kv10" height="30" transform="rotate(270,877.485,937.008) scale(-0.763595,-0.763595) translate(-2028.05,-2167.66)" width="12" x="872.9031867422539" xlink:href="#GroundDisconnector:地刀12_0" y="925.5540426482341" zvalue="4320"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#1出线06167"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,877.485,937.008) scale(-0.763595,-0.763595) translate(-2028.05,-2167.66)" width="12" x="872.9031867422539" y="925.5540426482341"/></g>
  <g id="87">
   <use class="kv10" height="30" transform="rotate(270,1009.48,835.008) scale(-0.763595,-0.763595) translate(-2332.92,-1932.08)" width="12" x="1004.903186742254" xlink:href="#GroundDisconnector:地刀12_0" y="823.554042648234" zvalue="4330"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#2出线06217"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1009.48,835.008) scale(-0.763595,-0.763595) translate(-2332.92,-1932.08)" width="12" x="1004.903186742254" y="823.554042648234"/></g>
  <g id="81">
   <use class="kv10" height="30" transform="rotate(270,1011.48,937.008) scale(-0.763595,-0.763595) translate(-2337.54,-2167.66)" width="12" x="1006.903186742254" xlink:href="#GroundDisconnector:地刀12_0" y="925.5540426482341" zvalue="4341"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#2出线06267"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1011.48,937.008) scale(-0.763595,-0.763595) translate(-2337.54,-2167.66)" width="12" x="1006.903186742254" y="925.5540426482341"/></g>
  <g id="112">
   <use class="kv10" height="30" transform="rotate(270,1187.48,835.008) scale(-0.763595,-0.763595) translate(-2744.03,-1932.08)" width="12" x="1182.903186742254" xlink:href="#GroundDisconnector:地刀12_0" y="823.554042648234" zvalue="4351"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV1号电容器06317"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1187.48,835.008) scale(-0.763595,-0.763595) translate(-2744.03,-1932.08)" width="12" x="1182.903186742254" y="823.554042648234"/></g>
  <g id="105">
   <use class="kv10" height="30" transform="rotate(270,1189.48,937.008) scale(-0.763595,-0.763595) translate(-2748.65,-2167.66)" width="12" x="1184.903186742254" xlink:href="#GroundDisconnector:地刀12_0" y="925.5540426482341" zvalue="4362"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV1号电容器06367"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1189.48,937.008) scale(-0.763595,-0.763595) translate(-2748.65,-2167.66)" width="12" x="1184.903186742254" y="925.5540426482341"/></g>
 </g>
 <g id="BreakerClass">
  <g id="403">
   <use class="kv10" height="20" transform="rotate(0,906.786,858.532) scale(0.813198,0.885392) translate(207.367,109.985)" width="10" x="902.7204770012269" xlink:href="#Breaker:开关_0" y="849.6777289920715" zvalue="4114"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#1出线061"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,906.786,858.532) scale(0.813198,0.885392) translate(207.367,109.985)" width="10" x="902.7204770012269" y="849.6777289920715"/></g>
  <g id="358">
   <use class="kv35" height="20" transform="rotate(0,981.467,495.033) scale(0.777778,0.7732) translate(279.308,142.938)" width="10" x="977.5785183376738" xlink:href="#Breaker:开关_0" y="487.3012352876032" zvalue="4290"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变35kV侧301"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,981.467,495.033) scale(0.777778,0.7732) translate(279.308,142.938)" width="10" x="977.5785183376738" y="487.3012352876032"/></g>
  <g id="49">
   <use class="kv10" height="20" transform="rotate(0,984.325,666.462) scale(0.777778,0.7732) translate(280.124,193.223)" width="10" x="980.4356620122516" xlink:href="#Breaker:开关_0" y="658.7298067161746" zvalue="4302"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变10kV侧001"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,984.325,666.462) scale(0.777778,0.7732) translate(280.124,193.223)" width="10" x="980.4356620122516" y="658.7298067161746"/></g>
  <g id="85">
   <use class="kv10" height="20" transform="rotate(0,1040.79,858.532) scale(0.813198,0.885392) translate(238.149,109.985)" width="10" x="1036.720477001227" xlink:href="#Breaker:开关_0" y="849.6777289920715" zvalue="4333"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV#2出线062"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1040.79,858.532) scale(0.813198,0.885392) translate(238.149,109.985)" width="10" x="1036.720477001227" y="849.6777289920715"/></g>
  <g id="110">
   <use class="kv10" height="20" transform="rotate(0,1218.79,858.532) scale(0.813198,0.885392) translate(279.038,109.985)" width="10" x="1214.720477001227" xlink:href="#Breaker:开关_0" y="849.6777289920715" zvalue="4354"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV1号电容器063"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1218.79,858.532) scale(0.813198,0.885392) translate(279.038,109.985)" width="10" x="1214.720477001227" y="849.6777289920715"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="113">
   <use class="kv10" height="40" transform="rotate(270,1216.07,1007.27) scale(-0.909091,0.909091) translate(-2556.7,98.9091)" width="65" x="1186.523415886886" xlink:href="#Compensator:勐养电容器2_0" y="989.090909090909" zvalue="4371"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV1号电容器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(270,1216.07,1007.27) scale(-0.909091,0.909091) translate(-2556.7,98.9091)" width="65" x="1186.523415886886" y="989.090909090909"/></g>
 </g>
</svg>