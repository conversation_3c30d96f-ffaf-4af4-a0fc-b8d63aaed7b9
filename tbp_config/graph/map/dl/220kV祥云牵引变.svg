<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1200" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.v400{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(0,255,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Accessory:避雷器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000003" y2="8.666666666666668"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="12.6" y2="18.6"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.499999999999999" x2="5.916666666666666" y1="3.583333333333334" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_3" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_4" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="14.07232884821164" y2="14.07232884821164"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:两卷变PT0926_0" viewBox="0,0,20,35">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="4.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="4.5" y2="15.5"/>
   <path d="M 7 17 L 10 22 L 13 17" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 7 25.75 L 10 30.75 L 13 25.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="9.82" cy="19.93" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.82" cy="27.62" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:四卷变三接地_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="18" xlink:href="#terminal" y="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="4" y1="6.5" y2="6.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="14" y1="11" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="4" y1="6.25" y2="22.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="4" y1="14.25" y2="14.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="5" y1="23" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="6" y1="22" y2="22"/>
   <ellipse cx="18.25" cy="7" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="18.08" cy="14.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.25" cy="10.92" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.82" cy="11.02" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="14" y1="13.5" y2="10.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="14" y1="8.888888888888889" y2="10.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16" x2="14" y1="8.888888888888889" y2="10.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.25" x2="18.25" y1="4.638888888888889" y2="6.638888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.25" x2="18.25" y1="4.638888888888889" y2="6.638888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.25" x2="18.25" y1="9.250000000000002" y2="6.638888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="18" y1="12.38888888888889" y2="14.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="18" y1="17" y2="14.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16" x2="18" y1="12.38888888888889" y2="14.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="23.94444444444444" y1="11.71612466124661" y2="11.71612466124661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="21.31481481481482" y1="11.71612466124661" y2="9.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.94444444444444" x2="22.62962962962963" y1="11.71612466124661" y2="9.25"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="220kV祥云牵引变" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass"/>
 <g id="OtherClass">
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="48" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237,100.5) scale(1,1) translate(0,0)" writing-mode="lr" x="237" xml:space="preserve" y="117.5" zvalue="2630">祥云签引变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="24" id="103" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,895.419,93.2951) scale(1,1) translate(0,0)" writing-mode="lr" x="895.42" xml:space="preserve" y="102.3" zvalue="2628">220kV丁云牵线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="24" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1391.8,93.2951) scale(1,1) translate(0,0)" writing-mode="lr" x="1391.8" xml:space="preserve" y="102.3" zvalue="2629">220kV祥云牵线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,994.282,561) scale(1,1) translate(0,0)" writing-mode="lr" x="994.28" xml:space="preserve" y="568.5" zvalue="2633">111QF</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="139" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1292.94,561) scale(1,1) translate(0,0)" writing-mode="lr" x="1292.94" xml:space="preserve" y="568.5" zvalue="2634">112QF</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="101" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,759.793,219.497) scale(1,1) translate(0,0)" writing-mode="lr" x="759.79" xml:space="preserve" y="227" zvalue="2637">2419</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="136" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1527.43,219.497) scale(1,1) translate(0,0)" writing-mode="lr" x="1527.43" xml:space="preserve" y="227" zvalue="2638">2429</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,770.314,368.987) scale(1,1) translate(0,0)" writing-mode="lr" x="770.3099999999999" xml:space="preserve" y="376.49" zvalue="2641">24197</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1516.91,368.987) scale(1,1) translate(0,0)" writing-mode="lr" x="1516.91" xml:space="preserve" y="376.49" zvalue="2642">24297</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,843.793,415.497) scale(1,1) translate(0,0)" writing-mode="lr" x="843.79" xml:space="preserve" y="423" zvalue="2647">2416</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1443.43,415.497) scale(1,1) translate(0,0)" writing-mode="lr" x="1443.43" xml:space="preserve" y="423" zvalue="2648">2426</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1093.2,410.938) scale(1,1) translate(0,0)" writing-mode="lr" x="1093.2" xml:space="preserve" y="418.44" zvalue="2650">24167</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="98" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1183.2,412.938) scale(1,1) translate(0,0)" writing-mode="lr" x="1183.2" xml:space="preserve" y="420.44" zvalue="2651">24267</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="35" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,741.757,838) scale(1,1) translate(0,0)" writing-mode="lr" x="741.76" xml:space="preserve" y="850.5" zvalue="2655">1T</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="35" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1500.41,856.875) scale(1,1) translate(0,0)" writing-mode="lr" x="1500.41" xml:space="preserve" y="869.38" zvalue="2657">2T</text>
 </g>
 <g id="ACLineSegmentClass">
  <g id="144">
   <use class="kv220" height="30" transform="rotate(0,900.599,163.475) scale(8.68612,3.08225) translate(-770.015,-79.2034)" width="7" x="870.1971796289519" xlink:href="#ACLineSegment:线路_0" y="117.2408408480088" zvalue="2626"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="220kV丁云牵线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,900.599,163.475) scale(8.68612,3.08225) translate(-770.015,-79.2034)" width="7" x="870.1971796289519" y="117.2408408480088"/></g>
  <g id="145">
   <use class="kv220" height="30" transform="rotate(0,1386.62,163.475) scale(-8.68612,3.08225) translate(-1519.36,-79.2034)" width="7" x="1356.222218299202" xlink:href="#ACLineSegment:线路_0" y="117.2408408480088" zvalue="2627"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="220kV祥云牵线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1386.62,163.475) scale(-8.68612,3.08225) translate(-1519.36,-79.2034)" width="7" x="1356.222218299202" y="117.2408408480088"/></g>
 </g>
 <g id="BreakerClass">
  <g id="140">
   <use class="kv220" height="20" transform="rotate(0,903.19,548.15) scale(3.15,2.835) translate(-605.713,-336.449)" width="10" x="887.4400157567204" xlink:href="#Breaker:开关_0" y="519.8" zvalue="2631"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="220kV丁云牵线111"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,903.19,548.15) scale(3.15,2.835) translate(-605.713,-336.449)" width="10" x="887.4400157567204" y="519.8"/></g>
  <g id="141">
   <use class="kv220" height="20" transform="rotate(0,1384.03,548.15) scale(-3.15,2.835) translate(-1812.66,-336.449)" width="10" x="1368.282202542482" xlink:href="#Breaker:开关_0" y="519.8" zvalue="2632"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="220kV祥云牵线112"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1384.03,548.15) scale(-3.15,2.835) translate(-1812.66,-336.449)" width="10" x="1368.282202542482" y="519.8"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="137">
   <use class="kv220" height="30" transform="rotate(270,768.793,267.004) scale(-3.3324,2.44376) translate(-982.002,-136.088)" width="15" x="743.7995391446445" xlink:href="#Disconnector:刀闸_0" y="230.3474677524395" zvalue="2635"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="220kV丁云牵线2419"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,768.793,267.004) scale(-3.3324,2.44376) translate(-982.002,-136.088)" width="15" x="743.7995391446445" y="230.3474677524395"/></g>
  <g id="138">
   <use class="kv220" height="30" transform="rotate(90,1518.43,267.004) scale(3.3324,2.44376) translate(-1045.28,-136.088)" width="15" x="1493.436741687126" xlink:href="#Disconnector:刀闸_0" y="230.3474677524395" zvalue="2636"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="220kV祥云牵线2429"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1518.43,267.004) scale(3.3324,2.44376) translate(-1045.28,-136.088)" width="15" x="1493.436741687126" y="230.3474677524395"/></g>
  <g id="129">
   <use class="kv220" height="30" transform="rotate(180,901.793,422.004) scale(-3.3324,2.44376) translate(-1154.91,-227.661)" width="15" x="876.7995391446445" xlink:href="#Disconnector:刀闸_0" y="385.3474677524395" zvalue="2645"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="220kV丁云牵线2416"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,901.793,422.004) scale(-3.3324,2.44376) translate(-1154.91,-227.661)" width="15" x="876.7995391446445" y="385.3474677524395"/></g>
  <g id="130">
   <use class="kv220" height="30" transform="rotate(180,1385.43,422.004) scale(3.3324,2.44376) translate(-952.191,-227.661)" width="15" x="1360.436741687126" xlink:href="#Disconnector:刀闸_0" y="385.3474677524395" zvalue="2646"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="220kV祥云牵线2426"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1385.43,422.004) scale(3.3324,2.44376) translate(-952.191,-227.661)" width="15" x="1360.436741687126" y="385.3474677524395"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="134">
   <use class="kv220" height="30" transform="rotate(270,773.5,333.938) scale(2.36667,2.36667) translate(-438.469,-172.337)" width="12" x="759.3" xlink:href="#GroundDisconnector:地刀12_0" y="298.4381006262946" zvalue="2639"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="220kV丁云牵线24197"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,773.5,333.938) scale(2.36667,2.36667) translate(-438.469,-172.337)" width="12" x="759.3" y="298.4381006262946"/></g>
  <g id="135">
   <use class="kv220" height="30" transform="rotate(90,1513.72,333.938) scale(-2.36667,2.36667) translate(-2145.12,-172.337)" width="12" x="1499.522218299202" xlink:href="#GroundDisconnector:地刀12_0" y="298.4381006262946" zvalue="2640"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="220kV祥云牵线24297"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1513.72,333.938) scale(-2.36667,2.36667) translate(-2145.12,-172.337)" width="12" x="1499.522218299202" y="298.4381006262946"/></g>
  <g id="126">
   <use class="kv220" height="30" transform="rotate(0,1040.5,416.938) scale(2.36667,2.36667) translate(-592.652,-220.267)" width="12" x="1026.3" xlink:href="#GroundDisconnector:地刀12_0" y="381.4381006262946" zvalue="2649"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="220kV丁云牵线24167"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1040.5,416.938) scale(2.36667,2.36667) translate(-592.652,-220.267)" width="12" x="1026.3" y="381.4381006262946"/></g>
  <g id="127">
   <use class="kv220" height="30" transform="rotate(0,1246.72,416.938) scale(-2.36667,2.36667) translate(-1765.31,-220.267)" width="12" x="1232.522218299202" xlink:href="#GroundDisconnector:地刀12_0" y="381.4381006262946" zvalue="2650"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="220kV祥云牵线24267"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1246.72,416.938) scale(-2.36667,2.36667) translate(-1765.31,-220.267)" width="12" x="1232.522218299202" y="381.4381006262946"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="131">
   <use class="kv220" height="26" transform="rotate(90,476.261,267.958) scale(-2.00708,2.51838) translate(-707.51,-141.818)" width="12" x="464.2186482798976" xlink:href="#Accessory:避雷器_0" y="235.2186482798978" zvalue="2643"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="220kV丁云牵线避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,476.261,267.958) scale(-2.00708,2.51838) translate(-707.51,-141.818)" width="12" x="464.2186482798976" y="235.2186482798978"/></g>
  <g id="132">
   <use class="kv220" height="26" transform="rotate(270,1810.96,267.958) scale(2.00708,2.51838) translate(-902.631,-141.818)" width="12" x="1798.918648279898" xlink:href="#Accessory:避雷器_0" y="235.2186482798978" zvalue="2644"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="220kV祥云牵线避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1810.96,267.958) scale(2.00708,2.51838) translate(-902.631,-141.818)" width="12" x="1798.918648279898" y="235.2186482798978"/></g>
  <g id="123">
   <use class="kv220" height="35" transform="rotate(0,903.4,785.125) scale(10.2643,10.2643) translate(-722.743,-546.509)" width="20" x="800.7571586138632" xlink:href="#Accessory:两卷变PT0926_0" y="605.5" zvalue="2652"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="220kV丁云牵线1T"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,903.4,785.125) scale(10.2643,10.2643) translate(-722.743,-546.509)" width="20" x="800.7571586138632" y="605.5"/></g>
  <g id="125">
   <use class="kv220" height="35" transform="rotate(0,1383.82,785.125) scale(-10.2643,10.2643) translate(-1426,-546.509)" width="20" x="1281.179345399624" xlink:href="#Accessory:两卷变PT0926_0" y="605.5" zvalue="2654"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="220kV祥云牵线2T"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1383.82,785.125) scale(-10.2643,10.2643) translate(-1426,-546.509)" width="20" x="1281.179345399624" y="605.5"/></g>
  <g id="106">
   <use class="kv220" height="30" transform="rotate(0,562.5,473.5) scale(5.63333,5.63333) translate(-393.148,-319.947)" width="30" x="478.0000000000006" xlink:href="#Accessory:四卷变三接地_0" y="389.0000000000007" zvalue="2671"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="220kV丁云牵线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,562.5,473.5) scale(5.63333,5.63333) translate(-393.148,-319.947)" width="30" x="478.0000000000006" y="389.0000000000007"/></g>
  <g id="107">
   <use class="kv220" height="30" transform="rotate(0,1724.72,473.5) scale(-5.63333,5.63333) translate(-1961.39,-319.947)" width="30" x="1640.222218299202" xlink:href="#Accessory:四卷变三接地_0" y="389.0000000000007" zvalue="2672"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="220kV祥云牵线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1724.72,473.5) scale(-5.63333,5.63333) translate(-1961.39,-319.947)" width="30" x="1640.222218299202" y="389.0000000000007"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="121">
   <path class="kv220" d="M 900.6 209.25 L 900.6 385.97" stroke-width="1" zvalue="2656"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="144@0" LinkObjectIDznd="129@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 900.6 209.25 L 900.6 385.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="122">
   <path class="kv220" d="M 1386.62 209.25 L 1386.62 385.97" stroke-width="1" zvalue="2658"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@0" LinkObjectIDznd="130@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1386.62 209.25 L 1386.62 385.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="kv220" d="M 902.09 457.45 L 902.09 521.03" stroke-width="1" zvalue="2659"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@0" LinkObjectIDznd="140@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 902.09 457.45 L 902.09 521.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="119">
   <path class="kv220" d="M 1385.14 457.45 L 1385.14 521.03" stroke-width="1" zvalue="2660"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@0" LinkObjectIDznd="141@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1385.14 457.45 L 1385.14 521.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="116">
   <path class="kv220" d="M 804.82 267.21 L 900.6 267.21" stroke-width="1" zvalue="2661"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@1" LinkObjectIDznd="121" MaxPinNum="2"/>
   </metadata>
  <path d="M 804.82 267.21 L 900.6 267.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv220" d="M 1482.4 267.21 L 1386.62 267.21" stroke-width="1" zvalue="2662"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="138@1" LinkObjectIDznd="122" MaxPinNum="2"/>
   </metadata>
  <path d="M 1482.4 267.21 L 1386.62 267.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="kv220" d="M 900.6 343 L 1040.47 343 L 1040.47 383.04" stroke-width="1" zvalue="2663"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="121" LinkObjectIDznd="126@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 900.6 343 L 1040.47 343 L 1040.47 383.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="kv220" d="M 1386.62 343 L 1246.75 343 L 1246.75 383.04" stroke-width="1" zvalue="2664"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="122" LinkObjectIDznd="127@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1386.62 343 L 1246.75 343 L 1246.75 383.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv220" d="M 903.4 575.22 L 903.4 651.69" stroke-width="1" zvalue="2665"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@1" LinkObjectIDznd="123@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 903.4 575.22 L 903.4 651.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv220" d="M 1383.82 575.22 L 1383.82 651.69" stroke-width="1" zvalue="2666"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="141@1" LinkObjectIDznd="125@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1383.82 575.22 L 1383.82 651.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv220" d="M 733.35 267.3 L 507.41 267.3" stroke-width="1" zvalue="2667"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@0" LinkObjectIDznd="131@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 733.35 267.3 L 507.41 267.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv220" d="M 1553.87 267.3 L 1779.82 267.3" stroke-width="1" zvalue="2668"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="138@0" LinkObjectIDznd="132@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1553.87 267.3 L 1779.82 267.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv220" d="M 739.6 333.97 L 655 333.97 L 655 267.3" stroke-width="1" zvalue="2669"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="134@0" LinkObjectIDznd="110" MaxPinNum="2"/>
   </metadata>
  <path d="M 739.6 333.97 L 655 333.97 L 655 267.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv220" d="M 1547.62 333.97 L 1632.22 333.97 L 1632.22 267.3" stroke-width="1" zvalue="2670"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="135@0" LinkObjectIDznd="111" MaxPinNum="2"/>
   </metadata>
  <path d="M 1547.62 333.97 L 1632.22 333.97 L 1632.22 267.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="kv220" d="M 579.4 400.27 L 579.4 267.3" stroke-width="1" zvalue="2673"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@0" LinkObjectIDznd="110" MaxPinNum="2"/>
   </metadata>
  <path d="M 579.4 400.27 L 579.4 267.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv220" d="M 1707.82 400.27 L 1707.82 267.3" stroke-width="1" zvalue="2674"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="111" MaxPinNum="2"/>
   </metadata>
  <path d="M 1707.82 400.27 L 1707.82 267.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
</svg>