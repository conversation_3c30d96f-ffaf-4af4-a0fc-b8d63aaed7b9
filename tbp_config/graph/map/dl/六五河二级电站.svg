<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1200" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.v400{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(0,255,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.499999999999999" x2="5.916666666666666" y1="3.583333333333334" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_3" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_4" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="14.07232884821164" y2="14.07232884821164"/>
  </symbol>
  <symbol id="Accessory:三卷PT带容断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,6.5) scale(1,1) translate(0,0)" width="4" x="13" y="3"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Breaker:刀闸绘制规范_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.077060931899643" xlink:href="#terminal" y="0.7027600849256537"/>
   <use terminal-index="1" type="0" x="5.077060931899643" xlink:href="#terminal" y="19.29723991507431"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.835125448028674" x2="6.249999999999999" y1="2.796178343949066" y2="4.704883227176235"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.077060931899643" x2="5.077060931899643" y1="12.58598726114649" y2="19.23566878980892"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.077060931899643" x2="0.333333333333333" y1="12.5859872611465" y2="5.083333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.077060931899643" x2="5.077060931899643" y1="3.658174097664537" y2="0.7027600849256821"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.249999999999999" x2="3.835125448028674" y1="2.796178343949066" y2="4.704883227176235"/>
  </symbol>
  <symbol id="Breaker:刀闸绘制规范_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.077060931899643" xlink:href="#terminal" y="0.7027600849256537"/>
   <use terminal-index="1" type="0" x="5.077060931899643" xlink:href="#terminal" y="19.29723991507431"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="1" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="8" y1="4" y2="4"/>
  </symbol>
  <symbol id="Breaker:刀闸绘制规范_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.077060931899643" xlink:href="#terminal" y="0.7027600849256537"/>
   <use terminal-index="1" type="0" x="5.077060931899643" xlink:href="#terminal" y="19.29723991507431"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.077060931899643" x2="5.077060931899643" y1="3.658174097664537" y2="0.7027600849256821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.077060931899643" x2="5.077060931899643" y1="12.58598726114649" y2="19.23566878980892"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.835125448028674" x2="6.249999999999999" y1="2.796178343949066" y2="4.704883227176235"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.249999999999999" x2="3.835125448028674" y1="2.796178343949066" y2="4.704883227176235"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8" x2="1.666666666666666" y1="13.5" y2="2.749999999999997"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.666666666666666" x2="8" y1="14" y2="3.249999999999998"/>
  </symbol>
  <symbol id="PowerTransformer2:D-Y_0" viewBox="0,0,30,50">
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.75" ry="14.75" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="0" type="1" x="15.00731595793324" xlink:href="#terminal" y="0.3902606310013716"/>
  </symbol>
  <symbol id="PowerTransformer2:D-Y_1" viewBox="0,0,30,50">
   <ellipse cx="15" cy="35.25" fill-opacity="0" rx="14.75" ry="14.75" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="1" type="1" x="15.00325153685922" xlink:href="#terminal" y="49.86055225321343"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15" x2="20" y1="37.74761215261901" y2="42.91666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.25" x2="15" y1="43.08333333333334" y2="37.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01666666666667" x2="15.01666666666667" y1="37.5" y2="32.91666666666667"/>
   <use terminal-index="2" type="2" x="15" xlink:href="#terminal" y="37.75085860895189"/>
  </symbol>
  <symbol id="Ground:大地_0" viewBox="0,0,12,18">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6" x2="6" y1="13.08333333333334" y2="0.1666666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.5833333333333313" x2="11.25" y1="12.99453511141348" y2="12.99453511141348"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.083333333333333" x2="8" y1="17.63116790988687" y2="17.63116790988687"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666667" x2="10.33333333333333" y1="15.40451817731685" y2="15.40451817731685"/>
  </symbol>
  <symbol id="Accessory:附属接地变_0" viewBox="0,0,15,15">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="1.5"/>
   <ellipse cx="7.57" cy="7.91" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.561718118722353" x2="7.561718118722353" y1="4.914465958746223" y2="7.513454058763156"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.1552656081938" x2="7.561718118722348" y1="10.11244215878007" y2="7.513454058763136"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.96817062925088" x2="7.561718118722333" y1="10.11244215878007" y2="7.513454058763136"/>
  </symbol>
  <symbol id="Disconnector:站外刀闸_0" viewBox="0,0,30,14">
   <use terminal-index="0" type="0" x="0.4166666666666661" xlink:href="#terminal" y="10.66666666666667"/>
   <use terminal-index="1" type="0" x="29.91666666666666" xlink:href="#terminal" y="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.75" x2="19.75" y1="7" y2="12"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="8.75" y1="2" y2="7"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="21.75" y1="2" y2="7"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="18.75" y1="4.75" y2="0.75"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="23.25" x2="29.58333333333333" y1="10.58333333333333" y2="10.58333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.583333333333336" x2="23.41666666666666" y1="3.583333333333333" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.833333333333339" x2="0.6666666666666714" y1="10.57515993898413" y2="10.57515993898413"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.92232884821164" x2="5.92232884821164" y1="13.33333333333333" y2="8.083333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.583333333333332" x2="19.58333333333333" y1="6.833333333333333" y2="11.83333333333333"/>
  </symbol>
  <symbol id="Disconnector:站外刀闸_1" viewBox="0,0,30,14">
   <use terminal-index="0" type="0" x="0.4166666666666661" xlink:href="#terminal" y="10.66666666666667"/>
   <use terminal-index="1" type="0" x="29.91666666666666" xlink:href="#terminal" y="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="17" y1="8" y2="4"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,17,10.5) scale(1,1) translate(0,0)" width="14" x="10" y="8"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.33333333333333" x2="6.000000000000004" y1="10.55" y2="10.55"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="23.25" x2="29.58333333333333" y1="10.58333333333333" y2="10.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.833333333333339" x2="0.6666666666666714" y1="10.57515993898413" y2="10.57515993898413"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.92232884821164" x2="5.92232884821164" y1="13.33333333333333" y2="8.083333333333332"/>
  </symbol>
  <symbol id="Disconnector:站外刀闸_2" viewBox="0,0,30,14">
   <use terminal-index="0" type="0" x="0.4166666666666661" xlink:href="#terminal" y="10.66666666666667"/>
   <use terminal-index="1" type="0" x="29.91666666666666" xlink:href="#terminal" y="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="21" y1="13" y2="7"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="21" y1="7" y2="13"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="14" y1="6" y2="3"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="23.25" x2="29.58333333333333" y1="10.58333333333333" y2="10.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.833333333333339" x2="0.6666666666666714" y1="10.57515993898413" y2="10.57515993898413"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.92232884821164" x2="5.92232884821164" y1="13.33333333333333" y2="8.083333333333332"/>
  </symbol>
  <symbol id="EnergyConsumer:厂站用变_0" viewBox="0,0,15,25">
   <use terminal-index="0" type="0" x="5.916666666666666" xlink:href="#terminal" y="1.58333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.41666666666667" x2="5.916666666666666" y1="17.16666666666666" y2="20.33333333333333"/>
   <ellipse cx="5.8" cy="5.8" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.86" cy="11.79" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.926184092940137" x2="5.926184092940137" y1="10.47013412501683" y2="12.14189293057399"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.606255585344062" x2="5.926184092940133" y1="13.81365173613114" y2="12.14189293057398"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.246112600536196" x2="5.926184092940125" y1="13.81365173613114" y2="12.14189293057398"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.926184092940137" x2="13.41666666666667" y1="12.14189293057399" y2="12.14189293057399"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.41657730116176" x2="13.41657730116176" y1="12.16666666666666" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.009517426273471" x2="6.009517426273471" y1="3.303467458350164" y2="4.975226263907325"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.689588918677395" x2="6.009517426273467" y1="6.64698506946448" y2="4.975226263907319"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.246112600536196" x2="5.926184092940125" y1="6.563651736131146" y2="4.891892930573985"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.914160137763698" x2="5.914160137763698" y1="22.49434593889296" y2="15.91678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.906472232006561" x2="4.753286368439581" y1="24.43333333333333" y2="22.50505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.906472232006569" x2="7.05965809557355" y1="24.43333333333333" y2="22.50505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.753286368439605" x2="7.059658095573566" y1="22.50505857643125" y2="22.50505857643125"/>
  </symbol>
  <symbol id="Accessory:带熔断器的2卷变PT_0" viewBox="0,0,15,35">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="2.5"/>
   <path d="M 4.5 17.25 L 7.5 22.25 L 10.5 17.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333333" x2="7.583333333333333" y1="15.08333333333333" y2="2.416666666666666"/>
   <rect fill-opacity="0" height="6" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.42,9.58) scale(1,1) translate(0,0)" width="4" x="5.42" y="6.58"/>
   <path d="M 4.5 26 L 7.5 31 L 10.5 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="7.32" cy="20.18" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.32" cy="27.87" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="六五河二级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass"/>
 <g id="OtherClass">
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="87" id="2" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,177.125,52.5) scale(1,1) translate(0,0)" width="348.25" x="3" y="9" zvalue="1302"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="48" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,177.125,52.5) scale(1,1) translate(0,0)" writing-mode="lr" x="177.13" xml:space="preserve" y="69.5" zvalue="1302">六五河二级电站</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,945.882,140.262) scale(1,1) translate(0,0)" writing-mode="lr" x="945.88" xml:space="preserve" y="144.76" zvalue="1475">35kV六五河二级站T接线路</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,876.876,335.648) scale(1,1) translate(0,0)" writing-mode="lr" x="876.88" xml:space="preserve" y="339.15" zvalue="1477">3116</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,801.926,339.007) scale(1,1) translate(0,0)" writing-mode="lr" x="801.9299999999999" xml:space="preserve" y="342.51" zvalue="1565">31160</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="148" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,683.571,244) scale(1,1) translate(0,0)" writing-mode="lr" x="683.5700000000001" xml:space="preserve" y="248.5" zvalue="1763">3119</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,571.254,239.089) scale(1,1) translate(0,0)" writing-mode="lr" x="571.25" xml:space="preserve" y="243.59" zvalue="1765">31197</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="12" id="147" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,851.5,97) scale(1,1) translate(0,0)" writing-mode="lr" x="851.5" xml:space="preserve" y="101" zvalue="1773">至六五河一级电站</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,877.5,387) scale(1,1) translate(0,0)" writing-mode="lr" x="877.5" xml:space="preserve" y="391.5" zvalue="1776">311</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,894.421,498.25) scale(1,1) translate(0,0)" writing-mode="lr" x="894.42" xml:space="preserve" y="502.75" zvalue="1779">1B</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,942.5,499) scale(1,1) translate(0,0)" writing-mode="lr" x="942.5" xml:space="preserve" y="503.5" zvalue="1780">(6680kVA)</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="113" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,946.5,426.5) scale(1,1) translate(0,0)" writing-mode="lr" x="946.5" xml:space="preserve" y="430.5" zvalue="1781">Hello World</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,557.012,576.004) scale(1,1) translate(0,0)" writing-mode="lr" x="557.01" xml:space="preserve" y="580.5" zvalue="1784">0.4kV</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,525,648) scale(1,1) translate(0,0)" writing-mode="lr" x="525" xml:space="preserve" y="652.5" zvalue="1797">411</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,543.75,916.5) scale(1,1) translate(0,0)" writing-mode="lr" x="543.75" xml:space="preserve" y="921" zvalue="1800">1F</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,585.5,917) scale(1,1) translate(0,0)" writing-mode="lr" x="585.5" xml:space="preserve" y="921.5" zvalue="1847">1700kW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,993.5,298.75) scale(1,1) translate(0,0)" writing-mode="lr" x="993.5" xml:space="preserve" y="303.25" zvalue="1854">厂变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,835,648) scale(1,1) translate(0,0)" writing-mode="lr" x="835" xml:space="preserve" y="652.5" zvalue="1867">412</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,853.75,916.5) scale(1,1) translate(0,0)" writing-mode="lr" x="853.75" xml:space="preserve" y="921" zvalue="1869">2F</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,895.5,917) scale(1,1) translate(0,0)" writing-mode="lr" x="895.5" xml:space="preserve" y="921.5" zvalue="1870">1700kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1149,648) scale(1,1) translate(0,0)" writing-mode="lr" x="1149" xml:space="preserve" y="652.5" zvalue="1876">413</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1167.75,916.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1167.75" xml:space="preserve" y="921" zvalue="1878">3F</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1209.5,917) scale(1,1) translate(0,0)" writing-mode="lr" x="1209.5" xml:space="preserve" y="921.5" zvalue="1879">1600kW</text>
 </g>
 <g id="ACLineSegmentClass">
  <g id="112">
   <use class="kv35" height="30" transform="rotate(0,853.307,140.125) scale(5.04226,1.35753) translate(-669.928,-31.5416)" width="7" x="835.6594337430042" xlink:href="#ACLineSegment:线路_0" y="119.7619047619048" zvalue="1474"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV六五河二级站T接线路"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,853.307,140.125) scale(5.04226,1.35753) translate(-669.928,-31.5416)" width="7" x="835.6594337430042" y="119.7619047619048"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="114">
   <use class="kv35" height="30" transform="rotate(0,853.21,335.556) scale(-1.11111,0.814815) translate(-1620.27,73.4848)" width="15" x="844.8764839732156" xlink:href="#Disconnector:刀闸_0" y="323.3333333333331" zvalue="1476"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV六五河二级站T接线路3116"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,853.21,335.556) scale(-1.11111,0.814815) translate(-1620.27,73.4848)" width="15" x="844.8764839732156" y="323.3333333333331"/></g>
  <g id="125">
   <use class="kv35" height="30" transform="rotate(180,650.857,241) scale(1.42857,-1.04762) translate(-192.043,-470.331)" width="15" x="640.1428571428571" xlink:href="#Disconnector:刀闸_0" y="225.2857142857144" zvalue="1762"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV六五河二级站T接线路3119"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,650.857,241) scale(1.42857,-1.04762) translate(-192.043,-470.331)" width="15" x="640.1428571428571" y="225.2857142857144"/></g>
  <g id="7">
   <use class="kv35" height="14" transform="rotate(90,1038,218) scale(1,1) translate(0,0)" width="30" x="1023" xlink:href="#Disconnector:站外刀闸_0" y="211" zvalue="1852"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="厂变刀闸"/>
   </metadata>
  <rect fill="white" height="14" opacity="0" stroke="white" transform="rotate(90,1038,218) scale(1,1) translate(0,0)" width="30" x="1023" y="211"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="5">
   <path class="kv35" d="M 853.31 160.28 L 853.31 323.74" stroke-width="1" zvalue="1486"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@0" LinkObjectIDznd="114@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 853.31 160.28 L 853.31 323.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="kv35" d="M 597.79 181.94 L 597.79 197.18 L 708.68 197.18" stroke-width="1" zvalue="1769"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132@0" LinkObjectIDznd="131@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 597.79 181.94 L 597.79 197.18 L 708.68 197.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="136">
   <path class="kv35" d="M 650.73 225.81 L 650.73 197.18" stroke-width="1" zvalue="1770"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@0" LinkObjectIDznd="135" MaxPinNum="2"/>
   </metadata>
  <path d="M 650.73 225.81 L 650.73 197.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="143">
   <path class="kv35" d="M 650.77 256.45 L 650.77 280 L 853.31 280" stroke-width="1" zvalue="1771"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@1" LinkObjectIDznd="5" MaxPinNum="2"/>
   </metadata>
  <path d="M 650.77 256.45 L 650.77 280 L 853.31 280" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="144">
   <path class="kv35" d="M 602.92 232.15 L 602.92 219 L 650.73 219" stroke-width="1" zvalue="1772"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="127@0" LinkObjectIDznd="136" MaxPinNum="2"/>
   </metadata>
  <path d="M 602.92 232.15 L 602.92 219 L 650.73 219" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="kv35" d="M 853.65 375.38 L 853.65 347.57" stroke-width="1" zvalue="1776"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@0" LinkObjectIDznd="114@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 853.65 375.38 L 853.65 347.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="9">
   <path class="kv35" d="M 825.92 345.03 L 825.92 360 L 853.65 360" stroke-width="1" zvalue="1777"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@0" LinkObjectIDznd="8" MaxPinNum="2"/>
   </metadata>
  <path d="M 825.92 345.03 L 825.92 360 L 853.65 360" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="kv35" d="M 853.65 463.07 L 853.65 398.62" stroke-width="1" zvalue="1779"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@0" LinkObjectIDznd="1@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 853.65 463.07 L 853.65 398.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="v400" d="M 853.65 535.8 L 853.65 592" stroke-width="1" zvalue="1784"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@1" LinkObjectIDznd="115@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 853.65 535.8 L 853.65 592" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="kv35" d="M 1035.6 249.75 L 1035.6 232.92" stroke-width="1" zvalue="1854"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="17@0" LinkObjectIDznd="7@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1035.6 249.75 L 1035.6 232.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv35" d="M 1034.33 203.42 L 1034.33 184 L 853.31 184" stroke-width="1" zvalue="1855"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="7@0" LinkObjectIDznd="5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1034.33 203.42 L 1034.33 184 L 853.31 184" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="v400" d="M 853.64 517.99 L 911 517.99" stroke-width="1" zvalue="1858"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@2" LinkObjectIDznd="30@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 853.64 517.99 L 911 517.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="v400" d="M 544.65 636.38 L 544.65 592" stroke-width="1" zvalue="1862"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="23@0" LinkObjectIDznd="115@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 544.65 636.38 L 544.65 592" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="v400" d="M 544.65 659.62 L 544.65 850.75" stroke-width="1" zvalue="1863"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="23@1" LinkObjectIDznd="16@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 544.65 659.62 L 544.65 850.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="v400" d="M 459.11 732.04 L 459.11 705 L 544.65 705" stroke-width="1" zvalue="1864"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="54@0" LinkObjectIDznd="74" MaxPinNum="2"/>
   </metadata>
  <path d="M 459.11 732.04 L 459.11 705 L 544.65 705" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="v400" d="M 854.65 659.62 L 854.65 850.75" stroke-width="1" zvalue="1872"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="87@1" LinkObjectIDznd="86@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 854.65 659.62 L 854.65 850.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="v400" d="M 769.11 732.04 L 769.11 705 L 854.65 705" stroke-width="1" zvalue="1873"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@0" LinkObjectIDznd="83" MaxPinNum="2"/>
   </metadata>
  <path d="M 769.11 732.04 L 769.11 705 L 854.65 705" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="v400" d="M 1168.65 659.62 L 1168.65 850.75" stroke-width="1" zvalue="1881"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="95@1" LinkObjectIDznd="94@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1168.65 659.62 L 1168.65 850.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="v400" d="M 1083.11 732.04 L 1083.11 705 L 1168.65 705" stroke-width="1" zvalue="1882"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@0" LinkObjectIDznd="91" MaxPinNum="2"/>
   </metadata>
  <path d="M 1083.11 732.04 L 1083.11 705 L 1168.65 705" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="v400" d="M 854.65 636.38 L 854.65 592" stroke-width="1" zvalue="1883"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="87@0" LinkObjectIDznd="115@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 854.65 636.38 L 854.65 592" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="v400" d="M 1168.65 636.38 L 1168.65 592" stroke-width="1" zvalue="1884"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="95@0" LinkObjectIDznd="115@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1168.65 636.38 L 1168.65 592" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="v400" d="M 1320.61 664.54 L 1320.61 592" stroke-width="1" zvalue="1887"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="15@0" LinkObjectIDznd="115@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1320.61 664.54 L 1320.61 592" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="53">
   <use class="kv35" height="30" transform="rotate(180,825.926,334.089) scale(-0.763595,0.763595) translate(-1908.97,99.8862)" width="12" x="821.3447569719574" xlink:href="#GroundDisconnector:地刀12_0" y="322.6348948311083" zvalue="1564"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV六五河二级站T接线路31160"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,825.926,334.089) scale(-0.763595,0.763595) translate(-1908.97,99.8862)" width="12" x="821.3447569719574" y="322.6348948311083"/></g>
  <g id="127">
   <use class="kv35" height="30" transform="rotate(0,602.926,243.089) scale(0.763595,0.763595) translate(185.245,71.7131)" width="12" x="598.3447569274326" xlink:href="#GroundDisconnector:地刀12_0" y="231.6348948311083" zvalue="1764"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV六五河二级站T接线路31197"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,602.926,243.089) scale(0.763595,0.763595) translate(185.245,71.7131)" width="12" x="598.3447569274326" y="231.6348948311083"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="131">
   <use class="kv35" height="30" transform="rotate(0,708.54,155.323) scale(-2.85714,-2.85714) translate(-928.672,-181.829)" width="30" x="665.6832519332521" xlink:href="#Accessory:三卷PT带容断器_0" y="112.4657065516137" zvalue="1766"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV六五河二级站T接线路电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,708.54,155.323) scale(-2.85714,-2.85714) translate(-928.672,-181.829)" width="30" x="665.6832519332521" y="112.4657065516137"/></g>
  <g id="132">
   <use class="kv35" height="26" transform="rotate(0,597.745,166.478) scale(1.25,-1.25) translate(-118.049,-296.41)" width="12" x="590.244838494839" xlink:href="#Accessory:避雷器1_0" y="150.2275560493142" zvalue="1768"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV六五河二级站T接线路避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,597.745,166.478) scale(1.25,-1.25) translate(-118.049,-296.41)" width="12" x="590.244838494839" y="150.2275560493142"/></g>
  <g id="16">
   <use class="v400" height="15" transform="rotate(0,543.75,871.75) scale(3.5,3.5) translate(-369.643,-603.929)" width="15" x="517.5" xlink:href="#Accessory:附属接地变_0" y="845.5" zvalue="1799"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1F"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,543.75,871.75) scale(3.5,3.5) translate(-369.643,-603.929)" width="15" x="517.5" y="845.5"/></g>
  <g id="54">
   <use class="v400" height="35" transform="rotate(0,459.107,777.25) scale(3.01429,3.01429) translate(-291.69,-484.145)" width="15" x="436.5" xlink:href="#Accessory:带熔断器的2卷变PT_0" y="724.5" zvalue="1861"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1F电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,459.107,777.25) scale(3.01429,3.01429) translate(-291.69,-484.145)" width="15" x="436.5" y="724.5"/></g>
  <g id="86">
   <use class="v400" height="15" transform="rotate(0,853.75,871.75) scale(3.5,3.5) translate(-591.071,-603.929)" width="15" x="827.5" xlink:href="#Accessory:附属接地变_0" y="845.5" zvalue="1868"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2F"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,853.75,871.75) scale(3.5,3.5) translate(-591.071,-603.929)" width="15" x="827.5" y="845.5"/></g>
  <g id="84">
   <use class="v400" height="35" transform="rotate(0,769.107,777.25) scale(3.01429,3.01429) translate(-498.846,-484.145)" width="15" x="746.5" xlink:href="#Accessory:带熔断器的2卷变PT_0" y="724.5" zvalue="1871"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2F电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,769.107,777.25) scale(3.01429,3.01429) translate(-498.846,-484.145)" width="15" x="746.5" y="724.5"/></g>
  <g id="94">
   <use class="v400" height="15" transform="rotate(0,1167.75,871.75) scale(3.5,3.5) translate(-815.357,-603.929)" width="15" x="1141.5" xlink:href="#Accessory:附属接地变_0" y="845.5" zvalue="1877"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3F"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,1167.75,871.75) scale(3.5,3.5) translate(-815.357,-603.929)" width="15" x="1141.5" y="845.5"/></g>
  <g id="92">
   <use class="v400" height="35" transform="rotate(0,1083.11,777.25) scale(3.01429,3.01429) translate(-708.675,-484.145)" width="15" x="1060.5" xlink:href="#Accessory:带熔断器的2卷变PT_0" y="724.5" zvalue="1880"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3F电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1083.11,777.25) scale(3.01429,3.01429) translate(-708.675,-484.145)" width="15" x="1060.5" y="724.5"/></g>
  <g id="15">
   <use class="v400" height="35" transform="rotate(0,1320.61,709.75) scale(3.01429,3.01429) translate(-867.384,-439.038)" width="15" x="1298" xlink:href="#Accessory:带熔断器的2卷变PT_0" y="657" zvalue="1886"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0.66kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1320.61,709.75) scale(3.01429,3.01429) translate(-867.384,-439.038)" width="15" x="1298" y="657"/></g>
 </g>
 <g id="BreakerClass">
  <g id="1">
   <use class="kv35" height="20" transform="rotate(0,853.75,387) scale(-1.25,1.25) translate(-1535.5,-74.9)" width="10" x="847.5" xlink:href="#Breaker:刀闸绘制规范_0" y="374.5" zvalue="1775"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV六五河二级站T接线路311"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,853.75,387) scale(-1.25,1.25) translate(-1535.5,-74.9)" width="10" x="847.5" y="374.5"/></g>
  <g id="23">
   <use class="v400" height="20" transform="rotate(0,544.75,648) scale(-1.25,1.25) translate(-979.3,-127.1)" width="10" x="538.5" xlink:href="#Breaker:刀闸绘制规范_0" y="635.5" zvalue="1796"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1F411"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,544.75,648) scale(-1.25,1.25) translate(-979.3,-127.1)" width="10" x="538.5" y="635.5"/></g>
  <g id="87">
   <use class="v400" height="20" transform="rotate(0,854.75,648) scale(-1.25,1.25) translate(-1537.3,-127.1)" width="10" x="848.5" xlink:href="#Breaker:刀闸绘制规范_0" y="635.5" zvalue="1866"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2F412"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,854.75,648) scale(-1.25,1.25) translate(-1537.3,-127.1)" width="10" x="848.5" y="635.5"/></g>
  <g id="95">
   <use class="v400" height="20" transform="rotate(0,1168.75,648) scale(-1.25,1.25) translate(-2102.5,-127.1)" width="10" x="1162.5" xlink:href="#Breaker:刀闸绘制规范_0" y="635.5" zvalue="1875"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3F413"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1168.75,648) scale(-1.25,1.25) translate(-2102.5,-127.1)" width="10" x="1162.5" y="635.5"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="52">
   <g id="520">
    <use class="kv35" height="50" transform="rotate(0,853.643,499.25) scale(1.47,1.47) translate(-265.883,-147.874)" width="30" x="831.59" xlink:href="#PowerTransformer2:D-Y_0" y="462.5" zvalue="1778"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="35"/>
    </metadata>
   </g>
   <g id="521">
    <use class="v400" height="50" transform="rotate(0,853.643,499.25) scale(1.47,1.47) translate(-265.883,-147.874)" width="30" x="831.59" xlink:href="#PowerTransformer2:D-Y_1" y="462.5" zvalue="1778"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1号主变"/>
   </metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,853.643,499.25) scale(1.47,1.47) translate(-265.883,-147.874)" width="30" x="831.59" y="462.5"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="115">
   <path class="v400" d="M 403.23 592 L 1395.01 592" stroke-width="6" zvalue="1783"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0.4kV母线"/>
   </metadata>
  <path d="M 403.23 592 L 1395.01 592" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="17">
   <use class="kv35" height="25" transform="rotate(0,1042.85,299.75) scale(4.58,4.58) translate(-788.303,-189.552)" width="15" x="1008.5" xlink:href="#EnergyConsumer:厂站用变_0" y="242.5" zvalue="1853"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="厂变"/>
   </metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1042.85,299.75) scale(4.58,4.58) translate(-788.303,-189.552)" width="15" x="1008.5" y="242.5"/></g>
 </g>
 <g id="GroundClass">
  <g id="30">
   <use class="v400" height="18" transform="rotate(0,911,531.119) scale(1.5,1.5) translate(-300.667,-172.54)" width="12" x="902" xlink:href="#Ground:大地_0" y="517.6187621551593" zvalue="1857"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1号主变接地"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,911,531.119) scale(1.5,1.5) translate(-300.667,-172.54)" width="12" x="902" y="517.6187621551593"/></g>
 </g>
</svg>