<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="sys" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.v400{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(0,255,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="State:运行备用停止_0" viewBox="0,0,20,13">
   <rect fill="rgb(85,255,0)" height="12" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,10.04,6.67) scale(1,1) translate(0,0)" width="18.42" x="0.83" y="0.67"/>
   <text Plane="0" fill="none" font-family="Helvetica" font-size="13" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" text-anchor="middle" x="10.04166666666666" xml:space="preserve" y="10.66666666666666">运行</text>
  </symbol>
  <symbol id="State:运行备用停止_1" viewBox="0,0,20,13">
   <rect fill="rgb(255,255,0)" height="12" rx="0" ry="0" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,10.06,6.58) scale(1,1) translate(0,0)" width="18.42" x="0.85" y="0.58"/>
   <text Plane="0" fill="none" font-family="Helvetica" font-size="13" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" text-anchor="middle" x="10.05833333333333" xml:space="preserve" y="10.58333333333333">备用</text>
  </symbol>
  <symbol id="State:运行备用停止_2" viewBox="0,0,20,13">
   <rect fill="rgb(255,0,0)" height="12" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,10.04,6.67) scale(1,1) translate(0,0)" width="18.42" x="0.83" y="0.67"/>
   <text Plane="0" fill="none" font-family="Helvetica" font-size="13" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" text-anchor="middle" x="10.04166666666666" xml:space="preserve" y="10.66666666666666">停止</text>
  </symbol>
  <symbol id="SubStation:厂站_0" viewBox="0,0,30,30">
   <ellipse cx="15" cy="15" rx="14.92" ry="14.92" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="" InitShowingPlane="0" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass">
  
  
  
  
  
  
  
 </g>
 <g id="OtherClass">
  <rect Plane="0" fill="rgb(0,28,28)" fill-opacity="1" height="1058.93" id="60000" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,961.429,529.964) scale(1,1) translate(0,0)" width="1920" x="1.43" y="0.5" zvalue="-1"/>
  <rect Plane="5" fill="rgb(85,170,255)" fill-opacity="0.5" height="730.42" id="739" rx="20" ry="20" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="0" stroke-width="1" transform="rotate(0,928.208,486.125) scale(1,1) translate(0,0)" visible="false" width="1661.08" x="97.67" y="120.92" zvalue="113423"/>
  <rect Plane="5" fill="rgb(0,170,255)" fill-opacity="0.5" height="79" id="738" rx="20" ry="20" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="0" stroke-width="2" transform="rotate(0,1159,253.5) scale(1,1) translate(0,0)" visible="false" width="1128" x="595" y="214" zvalue="113424"/>
  <rect Plane="5" fill="rgb(0,170,255)" fill-opacity="0.5" height="194" id="737" rx="20" ry="20" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="0" stroke-width="2" transform="rotate(0,936,403) scale(1,1) translate(0,0)" visible="false" width="1574" x="149" y="306" zvalue="113425"/>
  <rect Plane="5" fill="rgb(0,170,255)" fill-opacity="0.5" height="79" id="736" rx="20" ry="20" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="0" stroke-width="2" transform="rotate(0,365.444,253.5) scale(1,1) translate(0,0)" visible="false" width="426.89" x="152" y="214" zvalue="113426"/>
  <text Plane="5" fill="rgb(255,255,255)" font-family="FangSong" font-size="35" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,956.917,39.25) scale(1,1) translate(0,0)" writing-mode="lr" x="956.92" xml:space="preserve" y="51.75" zvalue="113427">盈 江 厂  站  图  索  引</text>
  <rect Plane="5" fill="rgb(0,170,255)" fill-opacity="0.5" height="158.44" id="734" rx="20" ry="20" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="0" stroke-width="2" transform="rotate(0,251.603,944.442) scale(1,1) translate(0,-3.84236e-13)" visible="false" width="306.35" x="98.43000000000001" y="865.22" zvalue="113428"/>
  
  <text Plane="5" fill="rgb(0,0,0)" font-family="SimSun" font-size="21" id="733" stroke="rgb(0,0,0)" text-anchor="start" transform="rotate(0,217.189,255.348) scale(1,1) translate(0,0)" writing-mode="lr" x="160.69" xml:space="preserve" y="262.85" zvalue="113429">  500kV</text>
  <text Plane="5" fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,366.86,254.237) scale(1,1) translate(0,0)" writing-mode="lr" x="366.86" xml:space="preserve" y="263.74" zvalue="113432">德宏变</text>
  <text Plane="5" fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1074.38,254.237) scale(1,1) translate(0,0)" writing-mode="lr" x="1074.38" xml:space="preserve" y="263.74" zvalue="113434">傣龙变</text>
  <text Plane="5" fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,661.276,341.487) scale(1,1) translate(0,0)" writing-mode="lr" x="661.28" xml:space="preserve" y="350.99" zvalue="113437">勐嘎变</text>
  <text Plane="5" fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,792.276,341.487) scale(1,1) translate(0,0)" writing-mode="lr" x="792.28" xml:space="preserve" y="350.99" zvalue="113438">盏西变</text>
  <text Plane="5" fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1218.37,253.083) scale(1,1) translate(0,0)" writing-mode="lr" x="1218.37" xml:space="preserve" y="262.58" zvalue="113439">卡场变</text>
  <text Plane="5" fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,950.367,253.083) scale(1,1) translate(0,0)" writing-mode="lr" x="950.37" xml:space="preserve" y="262.58" zvalue="113440">盈江变</text>
  <rect Plane="5" fill="rgb(0,170,255)" fill-opacity="0.5" height="248.22" id="719" rx="20" ry="20" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="0" stroke-width="2" transform="rotate(0,935,636.111) scale(1,1) translate(0,0)" visible="false" width="1576" x="147" y="512" zvalue="113443"/>
  
  <text Plane="5" fill="rgb(0,0,0)" font-family="SimSun" font-size="21" id="718" stroke="rgb(0,0,0)" text-anchor="start" transform="rotate(0,220.5,545.527) scale(1,1) translate(0,0)" writing-mode="lr" x="164" xml:space="preserve" y="553.03" zvalue="113444">  35kV</text>
  <rect Plane="5" fill="rgb(0,170,255)" fill-opacity="0.5" height="158.44" id="717" rx="20" ry="20" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="0" stroke-width="2" transform="rotate(0,589.19,945.442) scale(1,1) translate(0,-3.8468e-13)" visible="false" width="306.35" x="436.01" y="866.22" zvalue="113445"/>
  <rect Plane="5" fill="rgb(0,170,255)" fill-opacity="0.5" height="158.44" id="716" rx="20" ry="20" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="0" stroke-width="2" transform="rotate(0,926.778,946.442) scale(1,1) translate(0,-3.85124e-13)" visible="false" width="306.35" x="773.6" y="867.22" zvalue="113446"/>
  <rect Plane="5" fill="rgb(0,170,255)" fill-opacity="0.5" height="158.44" id="715" rx="20" ry="20" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="0" stroke-width="2" transform="rotate(0,1264.37,948.442) scale(1,1) translate(0,-3.86012e-13)" visible="false" width="306.35" x="1111.19" y="869.22" zvalue="113447"/>
  <rect Plane="5" fill="rgb(0,170,255)" fill-opacity="0.5" height="158.44" id="714" rx="20" ry="20" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="0" stroke-width="2" transform="rotate(0,1601.95,943.442) scale(1,1) translate(0,-3.83792e-13)" visible="false" width="306.35" x="1448.78" y="864.22" zvalue="113448"/>
  <rect Plane="5" fill="rgb(0,0,0)" fill-opacity="0" height="50.64" id="713" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,205.618,173.318) scale(1,1) translate(0,1.47882e-13)" visible="false" width="95.68000000000001" x="157.78" y="148" zvalue="113449"/>
  <text Plane="5" fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,205.618,173.318) scale(1,1) translate(0,0)" writing-mode="lr" x="205.62" xml:space="preserve" y="182.82" zvalue="113449">变电站</text>
  <rect Plane="5" fill="rgb(0,0,0)" fill-opacity="0" height="50.64" id="712" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,332.285,173.318) scale(1,1) translate(0,1.47882e-13)" visible="false" width="95.68000000000001" x="284.44" y="148" zvalue="113450"/>
  <text Plane="5" fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,332.285,173.318) scale(1,1) translate(0,0)" writing-mode="lr" x="332.28" xml:space="preserve" y="182.82" zvalue="113450">发电站</text>
  <rect Plane="5" fill="rgb(0,0,0)" fill-opacity="0" height="50.64" id="711" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,458.951,173.318) scale(1,1) translate(0,1.47882e-13)" visible="false" width="95.68000000000001" x="411.11" y="148" zvalue="113451"/>
  <text Plane="5" fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,458.951,173.318) scale(1,1) translate(0,0)" writing-mode="lr" x="458.95" xml:space="preserve" y="182.82" zvalue="113451">用户站</text>
  <rect Plane="5" fill="rgb(0,0,0)" fill-opacity="0" height="91.11" id="710" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,253.889,946.444) scale(1,1) translate(4.73695e-14,0)" visible="false" width="223.33" x="142.22" y="900.89" zvalue="113452"/>
  <text Plane="5" fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,253.889,946.444) scale(1,1) translate(0,0)" writing-mode="lr" x="253.89" xml:space="preserve" y="955.9400000000001" zvalue="113452">芒市片区</text>
  <rect Plane="5" fill="rgb(0,0,0)" fill-opacity="0" height="91.11" id="709" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,592.5,946.444) scale(1,1) translate(-1.06766e-13,0)" visible="false" width="223.33" x="480.83" y="900.89" zvalue="113453"/>
  <text Plane="5" fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,592.5,946.444) scale(1,1) translate(0,0)" writing-mode="lr" x="592.5" xml:space="preserve" y="955.9400000000001" zvalue="113453">盈江片区</text>
  <rect Plane="5" fill="rgb(0,0,0)" fill-opacity="0" height="91.11" id="708" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,931.111,946.444) scale(1,1) translate(-1.81953e-13,0)" visible="false" width="223.33" x="819.4400000000001" y="900.89" zvalue="113454"/>
  <text Plane="5" fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,931.111,946.444) scale(1,1) translate(0,0)" writing-mode="lr" x="931.11" xml:space="preserve" y="955.9400000000001" zvalue="113454">梁河片区</text>
  <rect Plane="5" fill="rgb(0,0,0)" fill-opacity="0" height="91.11" id="707" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,1269.72,946.444) scale(1,1) translate(-2.5714e-13,0)" visible="false" width="223.33" x="1158.06" y="900.89" zvalue="113455"/>
  <text Plane="5" fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1269.72,946.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1269.72" xml:space="preserve" y="955.9400000000001" zvalue="113455">陇川片区</text>
  <rect Plane="5" fill="rgb(0,0,0)" fill-opacity="0" height="91.11" id="706" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,1608.33,946.444) scale(1,1) translate(4.9849e-13,0)" visible="false" width="223.33" x="1496.67" y="900.89" zvalue="113456"/>
  <text Plane="5" fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1608.33,946.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1608.33" xml:space="preserve" y="955.9400000000001" zvalue="113456">瑞丽地调</text>
  <rect Plane="5" fill="rgb(0,170,255)" fill-opacity="0.5" height="65.56" id="705" rx="20" ry="20" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="0" stroke-width="2" transform="rotate(0,934.667,806.333) scale(1,1) translate(0,0)" visible="false" width="1576" x="146.67" y="773.5599999999999" zvalue="113457"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1411.428571428572" x2="1558.095571428571" y1="716.5714285714287" y2="716.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1411.428571428572" x2="1558.095571428571" y1="750.5714285714287" y2="750.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1411.428571428572" x2="1411.428571428572" y1="716.5714285714287" y2="750.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1558.095571428571" x2="1558.095571428571" y1="716.5714285714287" y2="750.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1558.095271428571" x2="1704.762271428571" y1="716.5714285714287" y2="716.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1558.095271428571" x2="1704.762271428571" y1="750.5714285714287" y2="750.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1558.095271428571" x2="1558.095271428571" y1="716.5714285714287" y2="750.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1704.762271428571" x2="1704.762271428571" y1="716.5714285714287" y2="750.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1704.761871428571" x2="1851.428871428571" y1="716.5714285714287" y2="716.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1704.761871428571" x2="1851.428871428571" y1="750.5714285714287" y2="750.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1704.761871428571" x2="1704.761871428571" y1="716.5714285714287" y2="750.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1851.428871428571" x2="1851.428871428571" y1="716.5714285714287" y2="750.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1411.428571428572" x2="1558.095571428571" y1="750.5714285714287" y2="750.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1411.428571428572" x2="1558.095571428571" y1="784.5714285714287" y2="784.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1411.428571428572" x2="1411.428571428572" y1="750.5714285714287" y2="784.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1558.095571428571" x2="1558.095571428571" y1="750.5714285714287" y2="784.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1558.095271428571" x2="1704.762271428571" y1="750.5714285714287" y2="750.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1558.095271428571" x2="1704.762271428571" y1="784.5714285714287" y2="784.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1558.095271428571" x2="1558.095271428571" y1="750.5714285714287" y2="784.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1704.762271428571" x2="1704.762271428571" y1="750.5714285714287" y2="784.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1704.761871428571" x2="1851.428871428571" y1="750.5714285714287" y2="750.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1704.761871428571" x2="1851.428871428571" y1="784.5714285714287" y2="784.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1704.761871428571" x2="1704.761871428571" y1="750.5714285714287" y2="784.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1851.428871428571" x2="1851.428871428571" y1="750.5714285714287" y2="784.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1411.428571428572" x2="1558.095571428571" y1="784.5714285714287" y2="784.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1411.428571428572" x2="1558.095571428571" y1="818.5714285714287" y2="818.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1411.428571428572" x2="1411.428571428572" y1="784.5714285714287" y2="818.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1558.095571428571" x2="1558.095571428571" y1="784.5714285714287" y2="818.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1558.095271428571" x2="1704.762271428571" y1="784.5714285714287" y2="784.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1558.095271428571" x2="1704.762271428571" y1="818.5714285714287" y2="818.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1558.095271428571" x2="1558.095271428571" y1="784.5714285714287" y2="818.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1704.762271428571" x2="1704.762271428571" y1="784.5714285714287" y2="818.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1704.761871428571" x2="1851.428871428571" y1="784.5714285714287" y2="784.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1704.761871428571" x2="1851.428871428571" y1="818.5714285714287" y2="818.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1704.761871428571" x2="1704.761871428571" y1="784.5714285714287" y2="818.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1851.428871428571" x2="1851.428871428571" y1="784.5714285714287" y2="818.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1411.428571428572" x2="1558.095571428571" y1="818.5714285714287" y2="818.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1411.428571428572" x2="1558.095571428571" y1="852.5714285714287" y2="852.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1411.428571428572" x2="1411.428571428572" y1="818.5714285714287" y2="852.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1558.095571428571" x2="1558.095571428571" y1="818.5714285714287" y2="852.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1558.095271428571" x2="1704.762271428571" y1="818.5714285714287" y2="818.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1558.095271428571" x2="1704.762271428571" y1="852.5714285714287" y2="852.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1558.095271428571" x2="1558.095271428571" y1="818.5714285714287" y2="852.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1704.762271428571" x2="1704.762271428571" y1="818.5714285714287" y2="852.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1704.761871428571" x2="1851.428871428571" y1="818.5714285714287" y2="818.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1704.761871428571" x2="1851.428871428571" y1="852.5714285714287" y2="852.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1704.761871428571" x2="1704.761871428571" y1="818.5714285714287" y2="852.5714285714287"/>
  <line stroke="rgb(85,255,255)" stroke-width="1" x1="1851.428871428571" x2="1851.428871428571" y1="818.5714285714287" y2="852.5714285714287"/>
  <rect fill="rgb(0,0,0)" fill-opacity="0.42" height="259.14" id="751" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,270.833,368.429) scale(1,1) translate(0,0)" width="462.91" x="39.38" y="238.86" zvalue="115409"/>
  <rect fill="rgb(0,0,0)" fill-opacity="0.42" height="317.17" id="750" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,283.95,723.579) scale(1,1) translate(0,0)" width="470" x="48.95" y="564.99" zvalue="115410"/>
  
  <rect fill="none" fill-opacity="0" height="770.86" id="749" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,912.639,567.283) scale(1,1) translate(0,0)" width="747.27" x="539" y="181.85" zvalue="115411"/>
  <rect fill="none" fill-opacity="0" height="768.46" id="748" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,916.308,563.769) scale(1,1) translate(0,0)" width="746.46" x="543.08" y="179.54" zvalue="115412"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="59.99966666666694" x2="131.388566666667" y1="250.1666333333334" y2="250.1666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="59.99966666666694" x2="131.388566666667" y1="305.9999333333334" y2="305.9999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="59.99966666666694" x2="59.99966666666694" y1="250.1666333333334" y2="305.9999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="131.388566666667" x2="131.388566666667" y1="250.1666333333334" y2="305.9999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="131.3886666666669" x2="202.777566666667" y1="250.1666333333334" y2="250.1666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="131.3886666666669" x2="202.777566666667" y1="305.9999333333334" y2="305.9999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="131.3886666666669" x2="131.3886666666669" y1="250.1666333333334" y2="305.9999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="202.777566666667" x2="202.777566666667" y1="250.1666333333334" y2="305.9999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="202.7777666666669" x2="252.999966666667" y1="250.1666333333334" y2="250.1666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="202.7777666666669" x2="252.999966666667" y1="305.9999333333334" y2="305.9999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="202.7777666666669" x2="202.7777666666669" y1="250.1666333333334" y2="305.9999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="252.999966666667" x2="252.999966666667" y1="250.1666333333334" y2="305.9999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="252.999966666667" x2="341.333266666667" y1="250.1666333333334" y2="250.1666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="252.999966666667" x2="341.333266666667" y1="305.9999333333334" y2="305.9999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="252.999966666667" x2="252.999966666667" y1="250.1666333333334" y2="305.9999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="341.333266666667" x2="341.333266666667" y1="250.1666333333334" y2="305.9999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="341.333366666667" x2="393.000066666667" y1="250.1666333333334" y2="250.1666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="341.333366666667" x2="393.000066666667" y1="305.9999333333334" y2="305.9999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="341.333366666667" x2="341.333366666667" y1="250.1666333333334" y2="305.9999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="393.000066666667" x2="393.000066666667" y1="250.1666333333334" y2="305.9999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="392.9996666666669" x2="486.3329666666669" y1="250.1666333333334" y2="250.1666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="392.9996666666669" x2="486.3329666666669" y1="305.9999333333334" y2="305.9999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="392.9996666666669" x2="392.9996666666669" y1="250.1666333333334" y2="305.9999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="486.3329666666669" x2="486.3329666666669" y1="250.1666333333334" y2="305.9999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="59.99966666666694" x2="131.388566666667" y1="306.0000333333334" y2="306.0000333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="59.99966666666694" x2="131.388566666667" y1="330.1667333333334" y2="330.1667333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="59.99966666666694" x2="59.99966666666694" y1="306.0000333333334" y2="330.1667333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="131.388566666667" x2="131.388566666667" y1="306.0000333333334" y2="330.1667333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="131.3886666666669" x2="202.777566666667" y1="306.0000333333334" y2="306.0000333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="131.3886666666669" x2="202.777566666667" y1="330.1667333333334" y2="330.1667333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="131.3886666666669" x2="131.3886666666669" y1="306.0000333333334" y2="330.1667333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="202.777566666667" x2="202.777566666667" y1="306.0000333333334" y2="330.1667333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="202.7777666666669" x2="252.999966666667" y1="306.0000333333334" y2="306.0000333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="202.7777666666669" x2="252.999966666667" y1="330.1667333333334" y2="330.1667333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="202.7777666666669" x2="202.7777666666669" y1="306.0000333333334" y2="330.1667333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="252.999966666667" x2="252.999966666667" y1="306.0000333333334" y2="330.1667333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="252.999966666667" x2="341.333266666667" y1="306.0000333333334" y2="306.0000333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="252.999966666667" x2="341.333266666667" y1="330.1667333333334" y2="330.1667333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="252.999966666667" x2="252.999966666667" y1="306.0000333333334" y2="330.1667333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="341.333266666667" x2="341.333266666667" y1="306.0000333333334" y2="330.1667333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="341.333366666667" x2="393.000066666667" y1="306.0000333333334" y2="306.0000333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="341.333366666667" x2="393.000066666667" y1="330.1667333333334" y2="330.1667333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="341.333366666667" x2="341.333366666667" y1="306.0000333333334" y2="330.1667333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="393.000066666667" x2="393.000066666667" y1="306.0000333333334" y2="330.1667333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="392.9996666666669" x2="486.3329666666669" y1="306.0000333333334" y2="306.0000333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="392.9996666666669" x2="486.3329666666669" y1="330.1667333333334" y2="330.1667333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="392.9996666666669" x2="392.9996666666669" y1="306.0000333333334" y2="330.1667333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="486.3329666666669" x2="486.3329666666669" y1="306.0000333333334" y2="330.1667333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="59.99966666666694" x2="131.388566666667" y1="330.1666333333334" y2="330.1666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="59.99966666666694" x2="131.388566666667" y1="359.3333333333334" y2="359.3333333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="59.99966666666694" x2="59.99966666666694" y1="330.1666333333334" y2="359.3333333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="131.388566666667" x2="131.388566666667" y1="330.1666333333334" y2="359.3333333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="131.3886666666669" x2="202.777566666667" y1="330.1666333333334" y2="330.1666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="131.3886666666669" x2="202.777566666667" y1="359.3333333333334" y2="359.3333333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="131.3886666666669" x2="131.3886666666669" y1="330.1666333333334" y2="359.3333333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="202.777566666667" x2="202.777566666667" y1="330.1666333333334" y2="359.3333333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="202.7777666666669" x2="252.999966666667" y1="330.1666333333334" y2="330.1666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="202.7777666666669" x2="252.999966666667" y1="359.3333333333334" y2="359.3333333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="202.7777666666669" x2="202.7777666666669" y1="330.1666333333334" y2="359.3333333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="252.999966666667" x2="252.999966666667" y1="330.1666333333334" y2="359.3333333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="252.999966666667" x2="341.333266666667" y1="330.1666333333334" y2="330.1666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="252.999966666667" x2="341.333266666667" y1="359.3333333333334" y2="359.3333333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="252.999966666667" x2="252.999966666667" y1="330.1666333333334" y2="359.3333333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="341.333266666667" x2="341.333266666667" y1="330.1666333333334" y2="359.3333333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="341.333366666667" x2="393.000066666667" y1="330.1666333333334" y2="330.1666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="341.333366666667" x2="393.000066666667" y1="359.3333333333334" y2="359.3333333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="341.333366666667" x2="341.333366666667" y1="330.1666333333334" y2="359.3333333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="393.000066666667" x2="393.000066666667" y1="330.1666333333334" y2="359.3333333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="392.9996666666669" x2="486.3329666666669" y1="330.1666333333334" y2="330.1666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="392.9996666666669" x2="486.3329666666669" y1="359.3333333333334" y2="359.3333333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="392.9996666666669" x2="392.9996666666669" y1="330.1666333333334" y2="359.3333333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="486.3329666666669" x2="486.3329666666669" y1="330.1666333333334" y2="359.3333333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="59.99966666666694" x2="131.388566666667" y1="359.3333333333334" y2="359.3333333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="59.99966666666694" x2="131.388566666667" y1="387.6666333333334" y2="387.6666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="59.99966666666694" x2="59.99966666666694" y1="359.3333333333334" y2="387.6666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="131.388566666667" x2="131.388566666667" y1="359.3333333333334" y2="387.6666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="131.3886666666669" x2="202.777566666667" y1="359.3333333333334" y2="359.3333333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="131.3886666666669" x2="202.777566666667" y1="387.6666333333334" y2="387.6666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="131.3886666666669" x2="131.3886666666669" y1="359.3333333333334" y2="387.6666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="202.777566666667" x2="202.777566666667" y1="359.3333333333334" y2="387.6666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="202.7777666666669" x2="252.999966666667" y1="359.3333333333334" y2="359.3333333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="202.7777666666669" x2="252.999966666667" y1="387.6666333333334" y2="387.6666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="202.7777666666669" x2="202.7777666666669" y1="359.3333333333334" y2="387.6666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="252.999966666667" x2="252.999966666667" y1="359.3333333333334" y2="387.6666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="252.999966666667" x2="341.333266666667" y1="359.3333333333334" y2="359.3333333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="252.999966666667" x2="341.333266666667" y1="387.6666333333334" y2="387.6666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="252.999966666667" x2="252.999966666667" y1="359.3333333333334" y2="387.6666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="341.333266666667" x2="341.333266666667" y1="359.3333333333334" y2="387.6666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="341.333366666667" x2="393.000066666667" y1="359.3333333333334" y2="359.3333333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="341.333366666667" x2="393.000066666667" y1="387.6666333333334" y2="387.6666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="341.333366666667" x2="341.333366666667" y1="359.3333333333334" y2="387.6666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="393.000066666667" x2="393.000066666667" y1="359.3333333333334" y2="387.6666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="392.9996666666669" x2="486.3329666666669" y1="359.3333333333334" y2="359.3333333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="392.9996666666669" x2="486.3329666666669" y1="387.6666333333334" y2="387.6666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="392.9996666666669" x2="392.9996666666669" y1="359.3333333333334" y2="387.6666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="486.3329666666669" x2="486.3329666666669" y1="359.3333333333334" y2="387.6666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="59.99966666666694" x2="131.388566666667" y1="387.6666333333334" y2="387.6666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="59.99966666666694" x2="131.388566666667" y1="410.0277333333333" y2="410.0277333333333"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="59.99966666666694" x2="59.99966666666694" y1="387.6666333333334" y2="410.0277333333333"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="131.388566666667" x2="131.388566666667" y1="387.6666333333334" y2="410.0277333333333"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="131.3886666666669" x2="202.777566666667" y1="387.6666333333334" y2="387.6666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="131.3886666666669" x2="202.777566666667" y1="410.0277333333333" y2="410.0277333333333"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="131.3886666666669" x2="131.3886666666669" y1="387.6666333333334" y2="410.0277333333333"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="202.777566666667" x2="202.777566666667" y1="387.6666333333334" y2="410.0277333333333"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="202.7777666666669" x2="252.999966666667" y1="387.6666333333334" y2="387.6666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="202.7777666666669" x2="252.999966666667" y1="410.0277333333333" y2="410.0277333333333"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="202.7777666666669" x2="202.7777666666669" y1="387.6666333333334" y2="410.0277333333333"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="252.999966666667" x2="252.999966666667" y1="387.6666333333334" y2="410.0277333333333"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="252.999966666667" x2="341.333266666667" y1="387.6666333333334" y2="387.6666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="252.999966666667" x2="341.333266666667" y1="410.0277333333333" y2="410.0277333333333"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="252.999966666667" x2="252.999966666667" y1="387.6666333333334" y2="410.0277333333333"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="341.333266666667" x2="341.333266666667" y1="387.6666333333334" y2="410.0277333333333"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="341.333366666667" x2="393.000066666667" y1="387.6666333333334" y2="387.6666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="341.333366666667" x2="393.000066666667" y1="410.0277333333333" y2="410.0277333333333"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="341.333366666667" x2="341.333366666667" y1="387.6666333333334" y2="410.0277333333333"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="393.000066666667" x2="393.000066666667" y1="387.6666333333334" y2="410.0277333333333"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="392.9996666666669" x2="486.3329666666669" y1="387.6666333333334" y2="387.6666333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="392.9996666666669" x2="486.3329666666669" y1="410.0277333333333" y2="410.0277333333333"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="392.9996666666669" x2="392.9996666666669" y1="387.6666333333334" y2="410.0277333333333"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="486.3329666666669" x2="486.3329666666669" y1="387.6666333333334" y2="410.0277333333333"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="59.99966666666694" x2="131.388566666667" y1="410.0277333333333" y2="410.0277333333333"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="59.99966666666694" x2="131.388566666667" y1="438.4999333333334" y2="438.4999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="59.99966666666694" x2="59.99966666666694" y1="410.0277333333333" y2="438.4999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="131.388566666667" x2="131.388566666667" y1="410.0277333333333" y2="438.4999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="131.3886666666669" x2="202.777566666667" y1="410.0277333333333" y2="410.0277333333333"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="131.3886666666669" x2="202.777566666667" y1="438.4999333333334" y2="438.4999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="131.3886666666669" x2="131.3886666666669" y1="410.0277333333333" y2="438.4999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="202.777566666667" x2="202.777566666667" y1="410.0277333333333" y2="438.4999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="202.7777666666669" x2="252.999966666667" y1="410.0277333333333" y2="410.0277333333333"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="202.7777666666669" x2="252.999966666667" y1="438.4999333333334" y2="438.4999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="202.7777666666669" x2="202.7777666666669" y1="410.0277333333333" y2="438.4999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="252.999966666667" x2="252.999966666667" y1="410.0277333333333" y2="438.4999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="252.999966666667" x2="341.333266666667" y1="410.0277333333333" y2="410.0277333333333"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="252.999966666667" x2="341.333266666667" y1="438.4999333333334" y2="438.4999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="252.999966666667" x2="252.999966666667" y1="410.0277333333333" y2="438.4999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="341.333266666667" x2="341.333266666667" y1="410.0277333333333" y2="438.4999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="341.333366666667" x2="393.000066666667" y1="410.0277333333333" y2="410.0277333333333"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="341.333366666667" x2="393.000066666667" y1="438.4999333333334" y2="438.4999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="341.333366666667" x2="341.333366666667" y1="410.0277333333333" y2="438.4999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="393.000066666667" x2="393.000066666667" y1="410.0277333333333" y2="438.4999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="392.9996666666669" x2="486.3329666666669" y1="410.0277333333333" y2="410.0277333333333"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="392.9996666666669" x2="486.3329666666669" y1="438.4999333333334" y2="438.4999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="392.9996666666669" x2="392.9996666666669" y1="410.0277333333333" y2="438.4999333333334"/>
  <line stroke="rgb(85,170,255)" stroke-width="1" x1="486.3329666666669" x2="486.3329666666669" y1="410.0277333333333" y2="438.4999333333334"/>
  <rect fill="rgb(13,10,93)" fill-opacity="0.7" height="37.86" id="744" rx="20" ry="99" stroke="rgb(85,85,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,500.647,124.744) scale(1,1) translate(0,0)" width="122.5" x="439.4" y="105.82" zvalue="115416"/>
  <rect fill="rgb(10,23,93)" fill-opacity="0.61" height="55" id="743" rx="16" ry="16" stroke="rgb(85,85,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(43,432.629,124.298) scale(1,1) translate(-2.6987e-13,1.07467e-14)" width="55" x="405.13" y="96.8" zvalue="115417"/>
  <rect fill="rgb(10,16,93)" fill-opacity="0.78" height="45" id="741" rx="12" ry="12" stroke="rgb(85,85,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(43,432.042,123.762) scale(1,1) translate(-1.81873e-13,-1.34908e-13)" width="45" x="409.54" y="101.26" zvalue="115419"/>
  <rect fill="rgb(0,0,0)" fill-opacity="0.42" height="317.17" id="729" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,1630.84,722.67) scale(1,1) translate(0,0)" width="470" x="1395.84" y="564.08" zvalue="115421"/>
  
  <rect fill="none" fill-opacity="0" height="157.68" id="727" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,1628.87,812.494) scale(1,1) translate(0,1.62904e-13)" width="464.55" x="1396.59" y="733.65" zvalue="115422"/>
  <rect fill="rgb(0,193,142)" fill-opacity="0.3" height="24.5" id="721" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,291.011,233.833) scale(1,1) translate(0,0)" width="473.98" x="54.02" y="221.58" zvalue="115424"/>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="18" stroke="rgb(0,255,255)" text-anchor="middle" text-weight="bolder" transform="rotate(0,146.79,223.087) scale(1,1) translate(0,0)" writing-mode="lr" x="146.79" xml:space="preserve" y="230.59" zvalue="115426">电网规模</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="19" stroke="rgb(0,255,255)" text-anchor="middle" text-weight="bolder" transform="rotate(0,1494.15,223.391) scale(1,1) translate(0,0)" writing-mode="lr" x="1494.15" xml:space="preserve" y="230.89" zvalue="115427">电网负荷</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="20" stroke="rgb(0,255,255)" text-anchor="middle" text-weight="bolder" transform="rotate(0,1502.6,561.329) scale(1,1) translate(0,0)" writing-mode="lr" x="1502.6" xml:space="preserve" y="568.83" zvalue="115444">电网出力</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="32" id="682" stroke="rgb(0,255,255)" text-anchor="middle" text-weight="bolder" transform="rotate(0,955.625,42.4583) scale(1,1) translate(0,0)" writing-mode="lr" x="955.63" xml:space="preserve" y="53.96" zvalue="115447">大理供电局地调一体化系统</text>
  <path d="M 52.5 244.314 L 105.75 244.314 L 69.1244 220.002 L 52.5 220.002 z" fill="rgb(5,142,103)" fill-opacity="0.88" id="674" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" zvalue="115455"/>
  <text fill="rgb(0,255,0)" font-family="FangSong" font-size="19" id="21" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1072.92,845.765) scale(1,1) translate(0,0)" writing-mode="lr" x="1072.92" xml:space="preserve" y="852.27" zvalue="115458">南涧县</text>
  <rect fill="rgb(0,48,35)" fill-opacity="0.7" height="37.86" id="665" rx="20" ry="99" stroke="rgb(0,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,191.25,124.744) scale(1,1) translate(0,0)" width="122.5" x="130" y="105.82" zvalue="115464"/>
  <rect fill="rgb(10,23,93)" fill-opacity="0.61" height="55" id="664" rx="16" ry="16" stroke="rgb(0,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(43,121.689,125.548) scale(1,1) translate(-1.88227e-13,8.70838e-14)" width="55" x="94.19" y="98.05" zvalue="115465"/>
  <rect fill="rgb(0,61,44)" fill-opacity="0.78" height="45" id="663" rx="12" ry="12" stroke="rgb(0,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(43,121.689,125.012) scale(1,1) translate(-6.60729e-14,-1.13811e-13)" width="45" x="99.19" y="102.51" zvalue="115466"/>
  
  <rect fill="none" fill-opacity="0" height="9.199999999999999" id="661" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,500.661,143.399) scale(1,1) translate(0,0)" width="101.25" x="450.04" y="138.8" zvalue="115468"/>
  
  <rect fill="none" fill-opacity="0" height="17.95" id="660" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,193.946,144.024) scale(1,1) translate(0,0)" width="101.25" x="143.32" y="135.05" zvalue="115469"/>
  
  <rect fill="none" fill-opacity="0" height="25" id="658" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,1630,239.798) scale(1,1) translate(0,0)" width="475" x="1392.5" y="227.3" zvalue="115471"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="21" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,511.058,122.78) scale(1,1) translate(0,0)" writing-mode="lr" x="511.06" xml:space="preserve" y="130.28" zvalue="115473">配网监控</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="21" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.665,122.78) scale(1,1) translate(0,0)" writing-mode="lr" x="199.66" xml:space="preserve" y="130.28" zvalue="115474">主网监控</text>
  
  <rect fill="none" fill-opacity="0" height="33.37" id="654" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,120.332,123.815) scale(1,1) translate(2.27596e-14,0)" width="35.66" x="102.5" y="107.13" zvalue="115475"/>
  
  <rect fill="none" fill-opacity="0" height="42.12" id="653" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,432.797,124.44) scale(1,1) translate(0,0)" width="44.41" x="410.59" y="103.38" zvalue="115476"/>
  
  <rect fill="none" fill-opacity="0" height="25" id="652" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,951.875,66.2143) scale(1,1) translate(0,0)" width="563.75" x="670" y="53.71" zvalue="115477"/>
  <rect fill="rgb(65,24,8)" fill-opacity="0.7" height="37.86" id="650" rx="20" ry="99" stroke="rgb(255,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,1706.04,124.744) scale(1,1) translate(0,0)" width="122.5" x="1644.79" y="105.82" zvalue="115479"/>
  
  <rect fill="none" fill-opacity="0" height="6.7" id="649" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,1712.02,143.399) scale(1,1) translate(0,0)" width="101.25" x="1661.39" y="140.05" zvalue="115480"/>
  <rect fill="rgb(13,10,93)" fill-opacity="0.7" height="37.86" id="648" rx="20" ry="99" stroke="rgb(85,85,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,1146.23,124.744) scale(1,1) translate(-2.40915e-13,0)" width="122.5" x="1084.98" y="105.82" zvalue="115481"/>
  <rect fill="rgb(10,23,93)" fill-opacity="0.61" height="55" id="647" rx="16" ry="16" stroke="rgb(85,85,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(43,1076.73,125.548) scale(1,1) translate(-2.32977e-13,-1.08855e-13)" width="55" x="1049.23" y="98.05" zvalue="115482"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="21" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1156.46,126.155) scale(1,1) translate(0,0)" writing-mode="lr" x="1156.46" xml:space="preserve" y="133.65" zvalue="115483">系统维护</text>
  <rect fill="rgb(10,16,93)" fill-opacity="0.78" height="45" id="645" rx="12" ry="12" stroke="rgb(85,85,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(43,1076.73,125.012) scale(1,1) translate(1.17044e-13,-1.36573e-13)" width="45" x="1054.23" y="102.51" zvalue="115484"/>
  
  <rect fill="none" fill-opacity="0" height="23.71" id="644" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,1076.68,124.897) scale(1,1) translate(1.18168e-13,0)" width="24.63" x="1064.37" y="113.04" zvalue="115485"/>
  <rect fill="rgb(0,48,35)" fill-opacity="0.7" height="37.86" id="643" rx="20" ry="99" stroke="rgb(0,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,1420.84,124.744) scale(1,1) translate(0,0)" width="122.5" x="1359.59" y="105.82" zvalue="115486"/>
  <rect fill="rgb(10,23,93)" fill-opacity="0.61" height="55" id="642" rx="16" ry="16" stroke="rgb(0,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(43,1360.82,126.798) scale(1,1) translate(0,9.92182e-14)" width="55" x="1333.32" y="99.3" zvalue="115487"/>
  <rect fill="rgb(0,61,44)" fill-opacity="0.78" height="45" id="641" rx="12" ry="12" stroke="rgb(0,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(43,1360.82,126.262) scale(1,1) translate(-8.91499e-13,1.26719e-13)" width="45" x="1338.32" y="103.76" zvalue="115488"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="21" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1713.91,123.905) scale(1,1) translate(0,0)" writing-mode="lr" x="1713.91" xml:space="preserve" y="131.4" zvalue="115489">驾驶舱</text>
  <rect fill="rgb(76,5,58)" fill-opacity="0.7" height="37.86" id="639" rx="20" ry="99" stroke="rgb(242,91,31)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,839.441,124.744) scale(1,1) translate(0,0)" width="122.5" x="778.1900000000001" y="105.82" zvalue="115490"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="21" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1428.99,123.905) scale(1,1) translate(0,0)" writing-mode="lr" x="1428.99" xml:space="preserve" y="131.4" zvalue="115491">备调</text>
  <rect fill="rgb(76,5,58)" fill-opacity="0.78" height="45" id="637" rx="12" ry="12" stroke="rgb(242,91,31)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(43,771.724,124.762) scale(1,1) translate(0,0)" width="45" x="749.22" y="102.26" zvalue="115492"/>
  
  <rect fill="none" fill-opacity="0" height="27" id="636" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,1361.02,124.262) scale(1,1) translate(1.49655e-13,1.2297e-14)" width="26.08" x="1347.97" y="110.76" zvalue="115493"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="21" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,847.214,123.905) scale(1,1) translate(0,0)" writing-mode="lr" x="847.21" xml:space="preserve" y="131.4" zvalue="115494">高级应用</text>
  <rect fill="rgb(76,5,58)" fill-opacity="0.61" height="55" id="634" rx="16" ry="16" stroke="rgb(242,91,31)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(43,771.367,125.048) scale(1,1) translate(-3.30343e-13,-2.16599e-14)" width="55" x="743.87" y="97.55" zvalue="115495"/>
  
  <rect fill="none" fill-opacity="0" height="9.199999999999999" id="633" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,840.625,143.149) scale(1,1) translate(0,0)" width="101.25" x="790" y="138.55" zvalue="115496"/>
  
  <rect fill="none" fill-opacity="0" height="17.95" id="632" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,1429.12,143.226) scale(1,1) translate(0,0)" width="101.25" x="1378.5" y="134.25" zvalue="115497"/>
  
  <rect fill="none" fill-opacity="0" height="23.75" id="631" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,771.25,120.875) scale(1,1) translate(0,0)" width="27.5" x="757.5" y="109" zvalue="115498"/>
  
  <rect fill="none" fill-opacity="0" height="9.199999999999999" id="630" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,1147.12,143.851) scale(1,1) translate(0,0)" width="101.25" x="1096.5" y="139.25" zvalue="115499"/>
  
  <rect fill="none" fill-opacity="0" height="127.67" id="629" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,909.423,1005.29) scale(1,1) translate(0,1.04523e-13)" width="1150" x="334.42" y="941.46" zvalue="115500"/>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="28" stroke="rgb(0,255,255)" text-anchor="middle" text-weight="bolder" transform="rotate(0,142.79,568.606) scale(1,1) translate(0,0)" writing-mode="lr" x="142.79" xml:space="preserve" y="576.11" zvalue="115504">配网概况</text>
  <rect fill="rgb(10,23,93)" fill-opacity="0.61" height="55" id="621" rx="16" ry="16" stroke="rgb(255,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(43,1642.37,128.048) scale(1,1) translate(1.61358e-12,-4.46521e-14)" width="55" x="1614.87" y="100.55" zvalue="115508"/>
  <rect fill="rgb(60,22,7)" fill-opacity="0.78" height="45" id="620" rx="12" ry="12" stroke="rgb(255,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(43,1642.37,127.512) scale(1,1) translate(-1.07905e-12,0)" width="45" x="1619.87" y="105.01" zvalue="115509"/>
  
  <rect fill="none" fill-opacity="0" height="26.85" id="619" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,1642.56,125.827) scale(1,1) translate(0,-2.49589e-14)" width="26.89" x="1629.11" y="112.4" zvalue="115510"/>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="19" id="616" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,300.5,264.643) scale(1,1) translate(0,0)" writing-mode="lr" x="262" xml:space="preserve" y="271.14" zvalue="115513">主变容量</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="19" id="615" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,371.5,264.643) scale(1,1) translate(0,0)" writing-mode="lr" x="351.5" xml:space="preserve" y="271.14" zvalue="115514">线路</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="19" id="614" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,445,264.643) scale(1,1) translate(0,0)" writing-mode="lr" x="406.5" xml:space="preserve" y="271.14" zvalue="115515">线路长度</text>
  
  <rect fill="none" fill-opacity="0" height="25" id="588" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,1638.45,577.929) scale(1,1) translate(0,0)" width="563.75" x="1356.57" y="565.4299999999999" zvalue="115541"/>
  <rect fill="rgb(0,193,142)" fill-opacity="0.3" height="24.5" id="587" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,1628.51,227.75) scale(1,1) translate(0,0)" width="473.98" x="1391.52" y="215.5" zvalue="115542"/>
  <rect fill="rgb(0,193,142)" fill-opacity="0.3" height="24.5" id="583" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,1634.01,568) scale(1,1) translate(1.54934e-13,0)" width="476.98" x="1395.52" y="555.75" zvalue="115546"/>
  
  <rect fill="none" fill-opacity="0" height="25" id="580" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,265.375,587.214) scale(1,1) translate(0,0)" width="458.75" x="36" y="574.71" zvalue="115549"/>
  
  <rect fill="none" fill-opacity="0" height="25" id="579" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,282.875,250) scale(1,1) translate(0,0)" width="458.75" x="53.5" y="237.5" zvalue="115550"/>
  
  <rect fill="none" fill-opacity="0" height="26.85" id="574" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,1360.44,123.423) scale(1,1) translate(0,-2.44249e-14)" width="26.89" x="1347" y="110" zvalue="115555"/>
  <text fill="rgb(0,255,0)" font-family="FangSong" font-size="19" id="29" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,726.108,469.91) scale(1,1) translate(0,0)" writing-mode="lr" x="726.11" xml:space="preserve" y="476.41" zvalue="115580">云龙县</text>
  <text fill="rgb(0,255,0)" font-family="FangSong" font-size="19" id="30" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1036.3,312.351) scale(1,1) translate(0,0)" writing-mode="lr" x="1036.3" xml:space="preserve" y="318.85" zvalue="115581">鹤庆县</text>
  <text fill="rgb(0,255,0)" font-family="FangSong" font-size="19" id="31" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,851.97,437.215) scale(1,1) translate(0,0)" writing-mode="lr" x="851.97" xml:space="preserve" y="443.72" zvalue="115582">洱源县</text>
  <text fill="rgb(0,255,0)" font-family="FangSong" font-size="19" id="32" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,766.267,619.31) scale(1,1) translate(0,0)" writing-mode="lr" x="766.27" xml:space="preserve" y="625.8099999999999" zvalue="115583">永平县</text>
  <text fill="rgb(0,255,0)" font-family="FangSong" font-size="19" id="33" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1172.56,474.527) scale(1,1) translate(0,0)" writing-mode="lr" x="1172.56" xml:space="preserve" y="481.03" zvalue="115584">宾川县</text>
  <text fill="rgb(0,255,0)" font-family="FangSong" font-size="19" id="34" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,981.121,724.649) scale(1,1) translate(0,0)" writing-mode="lr" x="981.12" xml:space="preserve" y="731.15" zvalue="115585">巍山县</text>
  <text fill="rgb(0,255,0)" font-family="FangSong" font-size="19" id="35" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,997.107,468.205) scale(1,1) translate(0,0)" writing-mode="lr" x="997.11" xml:space="preserve" y="474.71" zvalue="115586">洱海</text>
  <text fill="rgb(0,255,0)" font-family="FangSong" font-size="19" id="36" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,823.47,296.946) scale(1,1) translate(0,0)" writing-mode="lr" x="823.47" xml:space="preserve" y="303.45" zvalue="115587">剑川县</text>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="12" id="37" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,1048.91,382.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1048.91" xml:space="preserve" y="386.78" zvalue="115598">黄坪变</text>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="12" id="38" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,1065.5,702.701) scale(1,1) translate(0,0)" writing-mode="lr" x="1065.5" xml:space="preserve" y="706.7" zvalue="115599">丁家庄变</text>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="12" id="39" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,1038.01,480.606) scale(1,1) translate(0,0)" writing-mode="lr" x="1038.01" xml:space="preserve" y="484.61" zvalue="115600">海东变</text>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="12" id="40" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,791.143,674.445) scale(1,1) translate(0,0)" writing-mode="lr" x="791.14" xml:space="preserve" y="678.4400000000001" zvalue="115601">苏屯变</text>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="12" id="41" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,1071.91,588.986) scale(1,1) translate(0,0)" writing-mode="lr" x="1071.91" xml:space="preserve" y="592.99" zvalue="115602">大理变</text>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="12" id="42" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,1008.92,641.441) scale(1,1) translate(0,0)" writing-mode="lr" x="1008.92" xml:space="preserve" y="645.4400000000001" zvalue="115603">下关变</text>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="12" id="43" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,1204.4,656.165) scale(1,1) translate(0,0)" writing-mode="lr" x="1204.4" xml:space="preserve" y="660.17" zvalue="115604">祥云变</text>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="12" id="44" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,954.83,412.637) scale(1,1) translate(0,0)" writing-mode="lr" x="954.83" xml:space="preserve" y="416.64" zvalue="115605">洱源变</text>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="12" id="45" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,833.256,333.542) scale(1,1) translate(0,0)" writing-mode="lr" x="833.26" xml:space="preserve" y="337.54" zvalue="115606">剑川变</text>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="12" id="46" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,960.554,294.009) scale(1,1) translate(0,0)" writing-mode="lr" x="960.55" xml:space="preserve" y="298.01" zvalue="115607">羊龙潭变</text>
  <rect fill="rgb(0,170,0)" fill-opacity="1" height="22.86" id="360" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,612.952,203.143) scale(1,1) translate(-1.3071e-13,-4.6826e-13)" width="48.57" x="588.67" y="191.71" zvalue="115628"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,612.952,203.143) scale(1,1) translate(0,0)" writing-mode="lr" x="612.95" xml:space="preserve" y="207.14" zvalue="115628">2</text>
  <rect fill="rgb(0,85,255)" fill-opacity="1" height="22.86" id="355" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,562.452,203.143) scale(1,1) translate(-1.19497e-13,-4.6826e-13)" width="48.57" x="538.17" y="191.71" zvalue="115633"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,562.452,203.143) scale(1,1) translate(0,0)" writing-mode="lr" x="562.45" xml:space="preserve" y="207.14" zvalue="115633">1</text>
  <text fill="rgb(0,255,0)" font-family="FangSong" font-size="19" id="49" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,983.808,532.327) scale(1,1) translate(0,0)" writing-mode="lr" x="983.8099999999999" xml:space="preserve" y="538.83" zvalue="115639">大理市</text>
  <text fill="rgb(0,255,0)" font-family="FangSong" font-size="19" id="50" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1137.97,745.506) scale(1,1) translate(0,0)" writing-mode="lr" x="1137.97" xml:space="preserve" y="752.01" zvalue="115640">弥渡县</text>
  <text fill="rgb(0,255,0)" font-family="FangSong" font-size="19" id="51" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1214.74,597.836) scale(1,1) translate(0,0)" writing-mode="lr" x="1214.74" xml:space="preserve" y="604.34" zvalue="115641">祥云县</text>
  <rect fill="rgb(0,193,142)" fill-opacity="0.3" height="24.5" id="341" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,284.761,575.149) scale(1,1) translate(5.68846e-15,0)" width="467.05" x="51.24" y="562.9" zvalue="115643"/>
  <path d="M 48.5714 588.309 L 101.821 588.309 L 65.1958 563.996 L 48.5714 563.996 z" fill="rgb(5,142,103)" fill-opacity="0.88" id="340" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" zvalue="115644"/>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="12" id="315" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,684.182,336.773) scale(1,1) translate(0,0)" writing-mode="lr" x="684.1799999999999" xml:space="preserve" y="340.77" zvalue="115659">福贡变</text>
  <text fill="rgb(0,255,0)" font-family="FangSong" font-size="19" id="52" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,891.464,623.296) scale(1,1) translate(0,0)" writing-mode="lr" x="891.46" xml:space="preserve" y="629.8" zvalue="115679">漾濞县</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="30" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,1631.46,737.34) scale(1,1) translate(0,0)" writing-mode="lr" x="1611.46" xml:space="preserve" y="744.84" zvalue="115705">出力</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="29" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,1778.46,737.34) scale(1,1) translate(0,0)" writing-mode="lr" x="1735.46" xml:space="preserve" y="744.84" zvalue="115706">装机容量</text>
  
  <text Plane="5" fill="rgb(0,0,0)" font-family="SimSun" font-size="21" id="732" stroke="rgb(0,0,0)" text-anchor="start" transform="rotate(0,657.604,254.237) scale(1,1) translate(0,0)" writing-mode="lr" x="606.27" xml:space="preserve" y="261.74" zvalue="113430">  220kV</text>
  
  <text Plane="5" fill="rgb(0,0,0)" font-family="SimSun" font-size="21" id="731" stroke="rgb(0,0,0)" text-anchor="start" transform="rotate(0,220.5,342.598) scale(1,1) translate(0,0)" writing-mode="lr" x="164" xml:space="preserve" y="350.1" zvalue="113431">  110kV</text>
  
  <text Plane="5" fill="rgb(0,0,0)" font-family="SimSun" font-size="21" id="704" stroke="rgb(0,0,0)" text-anchor="start" transform="rotate(0,226.833,805.971) scale(1,1) translate(0,0)" writing-mode="lr" x="170.33" xml:space="preserve" y="813.47" zvalue="113458"> 10kV</text>
  
  <rect fill="none" fill-opacity="0" height="127.67" id="747" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,246.345,438.333) scale(1,1) translate(0,-8.31557e-14)" width="388.45" x="52.12" y="374.5" zvalue="115413"/>
  
  <rect fill="none" fill-opacity="0" height="157.68" id="745" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,283.214,812.494) scale(1,1) translate(-1.13107e-14,1.62904e-13)" width="464.55" x="50.94" y="733.65" zvalue="115415"/>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="742" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,502,44.1369) scale(1,1) translate(0,0)" writing-mode="lr" x="502" xml:space="preserve" y="51.64" zvalue="115418">安全运行：              天</text>
  
  <rect fill="none" fill-opacity="0" height="17.5" id="740" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,378.375,42.7619) scale(1,1) translate(0,0)" width="18.75" x="369" y="34.01" zvalue="115420"/>
  <rect fill="rgb(0,0,0)" fill-opacity="0.42" height="271.47" id="726" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,1626.67,381.819) scale(1,1) translate(0,-5.46415e-14)" width="468.89" x="1392.22" y="246.08" zvalue="115423"/>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="19" id="701" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,169,264.643) scale(1,1) translate(0,0)" writing-mode="lr" x="139.5" xml:space="preserve" y="271.14" zvalue="115428">变电站</text>
  <text fill="rgb(0,255,255)" font-family="SimSun" font-size="19" id="700" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,102.5,346.146) scale(1,1) translate(0,0)" writing-mode="lr" x="75.5" xml:space="preserve" y="352.65" zvalue="115429">220kV</text>
  <text fill="rgb(0,255,255)" font-family="SimSun" font-size="19" id="699" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,102.5,372.778) scale(1,1) translate(0,0)" writing-mode="lr" x="75.5" xml:space="preserve" y="379.28" zvalue="115430">110kV</text>
  <text fill="rgb(0,255,255)" font-family="SimSun" font-size="19" id="698" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,97.5,399.409) scale(1,1) translate(0,0)" writing-mode="lr" x="75.5" xml:space="preserve" y="405.91" zvalue="115431">35kV</text>
  <text fill="rgb(0,255,255)" font-family="SimSun" font-size="19" id="697" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,103,318.014) scale(1,1) translate(0,0)" writing-mode="lr" x="75.5" xml:space="preserve" y="324.51" zvalue="115432">500kV</text>
  <text fill="rgb(0,255,255)" font-family="SimSun" font-size="19" id="696" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,168.5,424.541) scale(1,1) translate(0,0)" writing-mode="lr" x="151.5" xml:space="preserve" y="431.04" zvalue="115433">143</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="19" id="695" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,366.5,291.382) scale(1,1) translate(0,0)" writing-mode="lr" x="351.5" xml:space="preserve" y="297.88" zvalue="115434">/条</text>
  <text fill="rgb(0,255,255)" font-family="SimSun" font-size="19" id="694" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,229,424.541) scale(1,1) translate(0,0)" writing-mode="lr" x="212" xml:space="preserve" y="431.04" zvalue="115435">255</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="19" id="693" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,296.5,451.305) scale(1,1) translate(0,0)" writing-mode="lr" x="258" xml:space="preserve" y="457.81" zvalue="115436">供电用户</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="19" id="692" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,110,452.673) scale(1,1) translate(0,0)" writing-mode="lr" x="71.5" xml:space="preserve" y="459.17" zvalue="115437">供电容量</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="19" id="691" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,110,479.305) scale(1,1) translate(0,0)" writing-mode="lr" x="71.5" xml:space="preserve" y="485.81" zvalue="115438">装机容量</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="19" id="690" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,306,479.305) scale(1,1) translate(0,0)" writing-mode="lr" x="258" xml:space="preserve" y="485.81" zvalue="115439">新能源容量</text>
  <text fill="rgb(0,255,255)" font-family="SimSun" font-size="19" id="689" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,231.308,453.613) scale(1,1) translate(0,0)" writing-mode="lr" x="212.31" xml:space="preserve" y="460.11" zvalue="115440">MW</text>
  <text fill="rgb(0,255,255)" font-family="SimSun" font-size="19" id="688" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,197.278,479.972) scale(1,1) translate(0,0)" writing-mode="lr" x="153.78" xml:space="preserve" y="486.47" zvalue="115441">14690MW</text>
  <text fill="rgb(0,255,255)" font-family="SimSun" font-size="19" id="687" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,418.5,479.305) scale(1,1) translate(0,0)" writing-mode="lr" x="380.5" xml:space="preserve" y="485.81" zvalue="115442">2502MW</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="19" id="686" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,412.5,453.18) scale(1,1) translate(0,0)" writing-mode="lr" x="363" xml:space="preserve" y="459.68" zvalue="115443">132.89万户</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="684" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,104.725,1018.33) scale(1,1) translate(0,0)" writing-mode="lr" x="104.73" xml:space="preserve" y="1025.83" zvalue="115445">大理市</text>
  
  <rect fill="none" fill-opacity="0" height="41.25" id="683" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,93.625,41.125) scale(1,1) translate(0,0)" width="192.25" x="-2.5" y="20.5" zvalue="115446"/>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="19" id="681" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,1467.25,46.1369) scale(1,1) translate(0,0)" writing-mode="lr" x="1367.25" xml:space="preserve" y="52.64" zvalue="115448">电网频率：           Hz</text>
  
  <rect fill="none" fill-opacity="0" height="45" id="680" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,1590.17,45.3214) scale(1,1) translate(0,0)" width="45" x="1567.67" y="22.82" zvalue="115449"/>
  <path d="M 1.25 71.0833 L 594 71.0833 L 630.25 43.5833 L 697 43.5833" fill="none" id="679" stroke="rgb(0,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="115450"/>
  <line fill="none" id="678" stroke="rgb(0,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" x1="400" x2="508.75" y1="70.08333333333348" y2="70.08333333333348" zvalue="115451"/>
  <line fill="none" id="677" stroke="rgb(0,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" x1="1375.821428571429" x2="1484.571428571429" y1="69.33333333333343" y2="69.33333333333343" zvalue="115452"/>
  <rect fill="rgb(0,0,71)" fill-opacity="1" height="1.97" id="676" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,283.5,494.066) scale(1,1) translate(0,-1.09486e-13)" width="429" x="69" y="493.08" zvalue="115453"/>
  <rect fill="rgb(0,0,71)" fill-opacity="1" height="1.97" id="675" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,293.071,305.066) scale(1,1) translate(0,-6.75201e-14)" width="429" x="78.56999999999999" y="304.08" zvalue="115454"/>
  <path d="M 62.1756 244.314 L 95.6195 219.833 L 52.5 220.002 L 52.5 244.314 z" fill="rgb(4,96,98)" fill-opacity="0.88" id="673" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" zvalue="115456"/>
  
  <rect fill="none" fill-opacity="0" height="39.5" id="672" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,79,223.583) scale(1,1) translate(0,0)" width="40" x="59" y="203.83" zvalue="115457"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="670" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,165.5,318.014) scale(1,1) translate(0,0)" writing-mode="lr" x="159.5" xml:space="preserve" y="324.51" zvalue="115459">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="669" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,166,344.646) scale(1,1) translate(0,0)" writing-mode="lr" x="159.5" xml:space="preserve" y="351.15" zvalue="115460">8</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="668" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,167.5,371.278) scale(1,1) translate(0,0)" writing-mode="lr" x="155.5" xml:space="preserve" y="377.78" zvalue="115461">34</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="667" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,167.5,397.909) scale(1,1) translate(0,0)" writing-mode="lr" x="155.5" xml:space="preserve" y="404.41" zvalue="115462">99</text>
  <path d="M 1925 70.5 L 1304 70.5 L 1267.75 43 L 1201 43" fill="none" id="662" stroke="rgb(0,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="115467"/>
  
  <rect fill="none" fill-opacity="0" height="25" id="659" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,272.5,41.75) scale(1,1) translate(0,0)" width="147.5" x="198.75" y="29.25" zvalue="115470"/>
  
  <rect fill="none" fill-opacity="0" height="80" id="657" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,1852.59,55.5) scale(1,1) translate(0,0)" width="101.25" x="1801.96" y="15.5" zvalue="115472"/>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="628" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,109.073,631.734) scale(1,1) translate(0,0)" writing-mode="lr" x="66.06999999999999" xml:space="preserve" y="639.23" zvalue="115501">终端接入</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="627" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,107.407,767.421) scale(1,1) translate(0,0)" writing-mode="lr" x="64.41" xml:space="preserve" y="774.92" zvalue="115502">馈线接入</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="626" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,108.073,812.65) scale(1,1) translate(0,0)" writing-mode="lr" x="66.06999999999999" xml:space="preserve" y="820.15" zvalue="115503">自愈投入</text>
  <text fill="rgb(5,216,209)" font-family="SimSun" font-size="21" id="624" stroke="rgb(5,216,209)" text-anchor="start" text-weight="bolder" transform="rotate(0,196.042,630.9) scale(1,1) translate(0,0)" writing-mode="lr" x="156.42" xml:space="preserve" y="638.4" zvalue="115505">error</text>
  <text fill="rgb(5,216,209)" font-family="SimSun" font-size="21" id="623" stroke="rgb(5,216,209)" text-anchor="start" text-weight="bolder" transform="rotate(0,196.042,769.421) scale(1,1) translate(0,0)" writing-mode="lr" x="156.42" xml:space="preserve" y="776.92" zvalue="115506">error</text>
  <text fill="rgb(5,216,209)" font-family="SimSun" font-size="21" id="622" stroke="rgb(5,216,209)" text-anchor="start" text-weight="bolder" transform="rotate(0,196.042,812.65) scale(1,1) translate(0,0)" writing-mode="lr" x="156.42" xml:space="preserve" y="820.15" zvalue="115507">error</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="19" id="618" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,94.5,264.643) scale(1,1) translate(0,0)" writing-mode="lr" x="75.5" xml:space="preserve" y="271.14" zvalue="115511">电压</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="19" id="617" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,227.5,264.643) scale(1,1) translate(0,0)" writing-mode="lr" x="208" xml:space="preserve" y="271.14" zvalue="115512">主变</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="19" id="613" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,95.5,424.541) scale(1,1) translate(0,0)" writing-mode="lr" x="75.5" xml:space="preserve" y="431.04" zvalue="115516">合计</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="612" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,226.5,318.014) scale(1,1) translate(0,0)" writing-mode="lr" x="220" xml:space="preserve" y="324.51" zvalue="115517">4</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="611" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,227.5,344.646) scale(1,1) translate(0,0)" writing-mode="lr" x="216" xml:space="preserve" y="351.15" zvalue="115518">16</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="610" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,228,371.278) scale(1,1) translate(0,0)" writing-mode="lr" x="216" xml:space="preserve" y="377.78" zvalue="115519">64</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="609" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,229,397.909) scale(1,1) translate(0,0)" writing-mode="lr" x="212" xml:space="preserve" y="404.41" zvalue="115520">171</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="608" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,297,318.014) scale(1,1) translate(0,0)" writing-mode="lr" x="274" xml:space="preserve" y="324.51" zvalue="115521">3000</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="607" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,298,342.979) scale(1,1) translate(0,0)" writing-mode="lr" x="275.5" xml:space="preserve" y="349.48" zvalue="115522">2580</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="606" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,296.5,371.278) scale(1,1) translate(0,0)" writing-mode="lr" x="274" xml:space="preserve" y="377.78" zvalue="115523">2608</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="605" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,297,397.909) scale(1,1) translate(0,0)" writing-mode="lr" x="266" xml:space="preserve" y="404.41" zvalue="115524">1018.6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="604" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,367,317.18) scale(1,1) translate(0,0)" writing-mode="lr" x="355.5" xml:space="preserve" y="323.68" zvalue="115525">24</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="603" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,367.5,344.646) scale(1,1) translate(0,0)" writing-mode="lr" x="355.5" xml:space="preserve" y="351.15" zvalue="115526">43</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="602" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,368.5,371.278) scale(1,1) translate(0,0)" writing-mode="lr" x="351.5" xml:space="preserve" y="377.78" zvalue="115527">118</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="601" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,368.5,397.909) scale(1,1) translate(0,0)" writing-mode="lr" x="351.5" xml:space="preserve" y="404.41" zvalue="115528">234</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="600" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,437,318.014) scale(1,1) translate(0,0)" writing-mode="lr" x="414.5" xml:space="preserve" y="324.51" zvalue="115529">1612</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="599" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,437,344.646) scale(1,1) translate(0,0)" writing-mode="lr" x="414.5" xml:space="preserve" y="351.15" zvalue="115530">1678</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="598" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,437,371.278) scale(1,1) translate(0,0)" writing-mode="lr" x="414.5" xml:space="preserve" y="377.78" zvalue="115531">2516</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="597" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,437.5,397.909) scale(1,1) translate(0,0)" writing-mode="lr" x="414.5" xml:space="preserve" y="404.41" zvalue="115532">3101</text>
  <text fill="rgb(0,255,255)" font-family="SimSun" font-size="19" id="596" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,301.5,424.541) scale(1,1) translate(0,0)" writing-mode="lr" x="270" xml:space="preserve" y="431.04" zvalue="115533">9206.6</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="19" id="595" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,154.5,291.382) scale(1,1) translate(0,0)" writing-mode="lr" x="139.5" xml:space="preserve" y="297.88" zvalue="115534">/座</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="19" id="594" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,222.5,291.382) scale(1,1) translate(0,0)" writing-mode="lr" x="208" xml:space="preserve" y="297.88" zvalue="115535">/台</text>
  <text fill="rgb(0,255,255)" font-family="SimSun" font-size="19" id="593" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,286,289.953) scale(1,1) translate(0,0)" writing-mode="lr" x="262" xml:space="preserve" y="296.45" zvalue="115536">/MVA</text>
  <text fill="rgb(0,255,255)" font-family="SimSun" font-size="19" id="592" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,425.5,291.382) scale(1,1) translate(0,0)" writing-mode="lr" x="406.5" xml:space="preserve" y="297.88" zvalue="115537">/KM</text>
  <text fill="rgb(0,255,255)" font-family="SimSun" font-size="19" id="591" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,369,424.541) scale(1,1) translate(0,0)" writing-mode="lr" x="351.5" xml:space="preserve" y="431.04" zvalue="115538">419</text>
  <text fill="rgb(0,255,255)" font-family="SimSun" font-size="19" id="590" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,437,425.375) scale(1,1) translate(0,0)" writing-mode="lr" x="414.5" xml:space="preserve" y="431.87" zvalue="115539">8907</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="19" id="589" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,95.5,291.382) scale(1,1) translate(0,0)" writing-mode="lr" x="75.5" xml:space="preserve" y="297.88" zvalue="115540">等级</text>
  <path d="M 1391.5 239.481 L 1444.75 239.481 L 1408.12 215.169 L 1391.5 215.169 z" fill="rgb(5,142,103)" fill-opacity="0.88" id="586" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" zvalue="115543"/>
  <path d="M 1401.18 239.481 L 1434.62 215 L 1391.5 215.169 L 1391.5 239.481 z" fill="rgb(4,96,98)" fill-opacity="0.88" id="585" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" zvalue="115544"/>
  
  <rect fill="none" fill-opacity="0" height="28.2" id="584" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,1415.73,221.815) scale(1,1) translate(-3.10363e-13,2.30609e-14)" width="35.96" x="1397.75" y="207.71" zvalue="115545"/>
  <path d="M 1397.5 579.731 L 1450.75 579.731 L 1414.12 555.419 L 1397.5 555.419 z" fill="rgb(5,142,103)" fill-opacity="0.88" id="582" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" zvalue="115547"/>
  <path d="M 1407.18 579.731 L 1440.62 555.25 L 1397.5 555.419 L 1397.5 579.731 z" fill="rgb(4,96,98)" fill-opacity="0.88" id="581" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" zvalue="115548"/>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="578" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,227,631.734) scale(1,1) translate(0,0)" writing-mode="lr" x="217" xml:space="preserve" y="639.23" zvalue="115551">台</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="577" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,228.5,771.442) scale(1,1) translate(0,0)" writing-mode="lr" x="217" xml:space="preserve" y="778.9400000000001" zvalue="115552">条</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="576" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,228.5,812.65) scale(1,1) translate(0,0)" writing-mode="lr" x="217" xml:space="preserve" y="820.15" zvalue="115553">条</text>
  
  <rect fill="none" fill-opacity="0" height="33.53" id="575" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,1429.47,564.235) scale(1,1) translate(0,0)" width="47.05" x="1405.95" y="547.47" zvalue="115554"/>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="572" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,231.225,1018.33) scale(1,1) translate(0,0)" writing-mode="lr" x="231.23" xml:space="preserve" y="1025.83" zvalue="115557">祥云县</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="571" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,356.725,1018.33) scale(1,1) translate(0,0)" writing-mode="lr" x="356.73" xml:space="preserve" y="1025.83" zvalue="115558">漾濞县</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="570" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1844.73,1018.33) scale(1,1) translate(0,0)" writing-mode="lr" x="1844.73" xml:space="preserve" y="1025.83" zvalue="115559">洱源县</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="569" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1466.73,1018.33) scale(1,1) translate(0,0)" writing-mode="lr" x="1466.73" xml:space="preserve" y="1025.83" zvalue="115560">永平县</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="568" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1592.23,1018.33) scale(1,1) translate(0,0)" writing-mode="lr" x="1592.23" xml:space="preserve" y="1025.83" zvalue="115561">云龙县</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="567" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,482.225,1018.33) scale(1,1) translate(0,0)" writing-mode="lr" x="482.23" xml:space="preserve" y="1025.83" zvalue="115562">南涧县</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="566" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,608.725,1018.33) scale(1,1) translate(0,0)" writing-mode="lr" x="608.73" xml:space="preserve" y="1025.83" zvalue="115563">巍山县</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="565" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,734.725,1018.33) scale(1,1) translate(0,0)" writing-mode="lr" x="734.73" xml:space="preserve" y="1025.83" zvalue="115564">弥渡县</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="564" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1719.23,1018.33) scale(1,1) translate(0,0)" writing-mode="lr" x="1719.23" xml:space="preserve" y="1025.83" zvalue="115565">鹤庆县</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="563" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1214.73,1018.33) scale(1,1) translate(0,0)" writing-mode="lr" x="1214.73" xml:space="preserve" y="1025.83" zvalue="115566">宾川县</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="562" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1341.23,1018.33) scale(1,1) translate(0,0)" writing-mode="lr" x="1341.23" xml:space="preserve" y="1025.83" zvalue="115567">剑川县</text>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="12" id="512" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,1279.4,446.631) scale(1,1) translate(0,0)" writing-mode="lr" x="1279.4" xml:space="preserve" y="450.63" zvalue="115617">至仁和</text>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="12" id="377" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,1277.59,572.899) scale(1,1) translate(0,0)" writing-mode="lr" x="1277.59" xml:space="preserve" y="576.9" zvalue="115620">至和平</text>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="19" glyph-orientation-vertical="0" id="366" letter-spacing="3" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,663.143,675.897) scale(1,1) translate(0,0)" writing-mode="tb" x="663.14" xml:space="preserve" y="675.9" zvalue="115622">保山</text>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="12" id="365" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,734.333,770.917) scale(1,1) translate(0,0)" writing-mode="lr" x="734.33" xml:space="preserve" y="774.92" zvalue="115623">至兰城</text>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="19" glyph-orientation-vertical="0" id="362" letter-spacing="3" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,662.773,292.629) scale(1,1) translate(0,0)" writing-mode="tb" x="662.77" xml:space="preserve" y="292.63" zvalue="115626">怒江</text>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="19" glyph-orientation-vertical="0" id="361" letter-spacing="3" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,1148.29,318) scale(1,1) translate(0,0)" writing-mode="tb" x="1148.29" xml:space="preserve" y="318" zvalue="115627">丽江</text>
  <line fill="none" id="359" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="614" x2="643.3333333333334" y1="823.0000000000002" y2="823.0000000000002" zvalue="115629"/>
  <line fill="none" id="358" stroke="rgb(255,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="613.9999999999999" x2="643.3333333333333" y1="844.3333333333335" y2="844.3333333333335" zvalue="115630"/>
  <text fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="357" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,667.556,822.722) scale(1,1) translate(0,0)" writing-mode="lr" x="667.5599999999999" xml:space="preserve" y="827.22" zvalue="115631">500kV</text>
  <text fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="356" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,666.222,843.278) scale(1,1) translate(0,0)" writing-mode="lr" x="666.22" xml:space="preserve" y="847.78" zvalue="115632">220kV</text>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="13" id="353" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,762.111,822.722) scale(1,1) translate(0,0)" writing-mode="lr" x="762.11" xml:space="preserve" y="827.22" zvalue="115635">500kV电站</text>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="13" id="351" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,762.136,843.278) scale(1,1) translate(0,0)" writing-mode="lr" x="762.14" xml:space="preserve" y="847.78" zvalue="115637">220kV电站</text>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="19" glyph-orientation-vertical="0" id="350" letter-spacing="3" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,1282.53,736.833) scale(1,1) translate(0,0)" writing-mode="tb" x="1282.53" xml:space="preserve" y="736.83" zvalue="115638">楚雄</text>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="19" glyph-orientation-vertical="0" id="346" letter-spacing="3" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,926.214,870.714) scale(1,1) translate(0,0)" writing-mode="tb" x="926.21" xml:space="preserve" y="870.71" zvalue="115642">临沧</text>
  <path d="M 58.247 588.309 L 91.6909 563.828 L 48.5714 563.996 L 48.5714 588.309 z" fill="rgb(4,96,98)" fill-opacity="0.88" id="339" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" zvalue="115645"/>
  
  <rect fill="none" fill-opacity="0" height="34.17" id="338" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,79.25,567.238) scale(1,1) translate(0,-1.22159e-13)" width="31.5" x="63.5" y="550.15" zvalue="115646"/>
  
  <rect fill="none" fill-opacity="0" height="17.5" id="336" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,1339.88,46.7619) scale(1,1) translate(0,0)" width="18.75" x="1330.5" y="38.01" zvalue="115648"/>
  
  <rect fill="none" fill-opacity="0" height="127.67" id="335" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,1621.93,462.583) scale(1,1) translate(-3.08753e-13,4.42701e-14)" width="462.86" x="1390.5" y="398.75" zvalue="115649"/>
  <text fill="rgb(5,216,209)" font-family="SimSun" font-size="21" id="334" stroke="rgb(5,216,209)" text-anchor="start" text-weight="bolder" transform="rotate(0,208.583,676.963) scale(1,1) translate(0,0)" writing-mode="lr" x="156.42" xml:space="preserve" y="684.46" zvalue="115650">error</text>
  <text fill="rgb(0,255,255)" font-family="SimSun" font-size="21" id="333" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,83.0733,676.963) scale(1,1) translate(0,0)" writing-mode="lr" x="66.06999999999999" xml:space="preserve" y="684.46" zvalue="115651">FTU</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="327" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,227,676.963) scale(1,1) translate(0,0)" writing-mode="lr" x="217" xml:space="preserve" y="684.46" zvalue="115652">台</text>
  <text fill="rgb(5,216,209)" font-family="SimSun" font-size="21" id="326" stroke="rgb(5,216,209)" text-anchor="start" text-weight="bolder" transform="rotate(0,208.583,722.692) scale(1,1) translate(0,0)" writing-mode="lr" x="156.42" xml:space="preserve" y="730.1900000000001" zvalue="115653">error</text>
  <text fill="rgb(0,255,255)" font-family="SimSun" font-size="21" id="325" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,85.0733,722.192) scale(1,1) translate(0,0)" writing-mode="lr" x="66.06999999999999" xml:space="preserve" y="729.6900000000001" zvalue="115654">DTU</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="324" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,227,722.192) scale(1,1) translate(0,0)" writing-mode="lr" x="217" xml:space="preserve" y="729.6900000000001" zvalue="115655">台</text>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="12" id="323" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,776.379,202.667) scale(1,1) translate(0,0)" writing-mode="lr" x="776.38" xml:space="preserve" y="206.67" zvalue="115658">维西变</text>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="12" id="309" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,1102.38,337.125) scale(1,1) translate(0,0)" writing-mode="lr" x="1102.38" xml:space="preserve" y="341.13" zvalue="115664">丽江变</text>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="12" id="308" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,1196.11,314.941) scale(1,1) translate(0,0)" writing-mode="lr" x="1196.11" xml:space="preserve" y="318.94" zvalue="115665">太安变</text>
  <text fill="rgb(0,255,255)" font-family="SimSun" font-size="19" id="88" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,187.654,453.846) scale(1,1) translate(0,0)" writing-mode="lr" x="156.15" xml:space="preserve" y="460.35" zvalue="115681">9206.6</text>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="19" id="86" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,911.278,196.389) scale(1,1) translate(0,0)" writing-mode="lr" x="911.28" xml:space="preserve" y="202.89" zvalue="115683">香格里拉</text>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="12" id="83" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,1290.04,624.462) scale(1,1) translate(0,0)" writing-mode="lr" x="1290.04" xml:space="preserve" y="628.46" zvalue="115687">至鹿城</text>
  <text fill="rgb(85,255,255)" font-family="FangSong" font-size="12" id="80" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,677.389,735.361) scale(1,1) translate(0,0)" writing-mode="lr" x="677.39" xml:space="preserve" y="739.36" zvalue="115690">至保山</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="75" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,1513.46,767.483) scale(1,1) translate(0,0)" writing-mode="lr" x="1491.46" xml:space="preserve" y="774.98" zvalue="115695">风电</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="50" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,1513.46,801.08) scale(1,1) translate(0,0)" writing-mode="lr" x="1491.46" xml:space="preserve" y="808.58" zvalue="115696">光伏</text>
  
  <rect fill="none" fill-opacity="0" height="33.53" id="36" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,1454.67,800.87) scale(1,1) translate(0,-1.39285e-12)" width="47.05" x="1431.14" y="784.11" zvalue="115697"/>
  
  <rect fill="none" fill-opacity="0" height="33.53" id="35" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,1450.67,763.051) scale(1,1) translate(-1.58445e-12,-6.62835e-13)" width="47.05" x="1427.14" y="746.29" zvalue="115698"/>
  
  <rect fill="none" fill-opacity="0" height="33.53" id="34" stroke="none" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-opacity="0" stroke-width="1" transform="rotate(0,1454.67,838.189) scale(1,1) translate(0,1.36795e-12)" width="47.05" x="1431.14" y="821.42" zvalue="115701"/>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="33" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,1513.46,833.709) scale(1,1) translate(0,0)" writing-mode="lr" x="1491.46" xml:space="preserve" y="841.21" zvalue="115702">水电</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="31" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,1492.46,737.34) scale(1,1) translate(0,0)" writing-mode="lr" x="1459.46" xml:space="preserve" y="744.84" zvalue="115704">新能源</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="28" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,331.573,631.734) scale(1,1) translate(0,0)" writing-mode="lr" x="278.07" xml:space="preserve" y="639.23" zvalue="115707">终端在线率</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="27" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,331.573,767.421) scale(1,1) translate(0,0)" writing-mode="lr" x="278.07" xml:space="preserve" y="774.92" zvalue="115708">遥控成功率</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="26" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,331.573,676.963) scale(1,1) translate(0,0)" writing-mode="lr" x="278.07" xml:space="preserve" y="684.46" zvalue="115709">应控必控率</text>
  <text fill="rgb(0,255,255)" font-family="FangSong" font-size="21" id="25" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,331.573,722.192) scale(1,1) translate(0,0)" writing-mode="lr" x="278.07" xml:space="preserve" y="729.6900000000001" zvalue="115710">终端投运率</text>
  <text fill="rgb(5,216,209)" font-family="SimSun" font-size="21" id="24" stroke="rgb(5,216,209)" text-anchor="start" text-weight="bolder" transform="rotate(0,438.11,630.9) scale(1,1) translate(0,0)" writing-mode="lr" x="401.43" xml:space="preserve" y="638.4" zvalue="115711">error</text>
  <text fill="rgb(5,216,209)" font-family="SimSun" font-size="21" id="23" stroke="rgb(5,216,209)" text-anchor="start" text-weight="bolder" transform="rotate(0,438.11,676.963) scale(1,1) translate(0,0)" writing-mode="lr" x="401.43" xml:space="preserve" y="684.46" zvalue="115712">error</text>
  <text fill="rgb(5,216,209)" font-family="SimSun" font-size="21" id="22" stroke="rgb(5,216,209)" text-anchor="start" text-weight="bolder" transform="rotate(0,438.11,722.692) scale(1,1) translate(0,0)" writing-mode="lr" x="401.43" xml:space="preserve" y="730.1900000000001" zvalue="115713">error</text>
  <text fill="rgb(5,216,209)" font-family="SimSun" font-size="21" id="21" stroke="rgb(5,216,209)" text-anchor="start" text-weight="bolder" transform="rotate(0,438.11,769.421) scale(1,1) translate(0,0)" writing-mode="lr" x="401.43" xml:space="preserve" y="776.92" zvalue="115714">error</text>
  <text fill="rgb(0,255,255)" font-family="SimSun" font-size="21" id="20" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,1778.46,770.982) scale(1,1) translate(0,0)" writing-mode="lr" x="1734.46" xml:space="preserve" y="778.48" zvalue="115715">1047MW</text>
  <text fill="rgb(0,255,255)" font-family="SimSun" font-size="21" id="18" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,1778.46,804.623) scale(1,1) translate(0,0)" writing-mode="lr" x="1724.46" xml:space="preserve" y="812.12" zvalue="115716">603.79MW</text>
  <text fill="rgb(0,255,255)" font-family="SimSun" font-size="21" id="17" stroke="rgb(0,255,255)" text-anchor="start" transform="rotate(0,1778.46,838.265) scale(1,1) translate(0,0)" writing-mode="lr" x="1717.96" xml:space="preserve" y="845.76" zvalue="115717">960.423MW</text>
 </g>
 <g id="ButtonClass">
  <g Plane="5" href="500kV德宏变.svg"><rect fill-opacity="0" height="41.96" width="102.91" x="315.41" y="233.26" zvalue="113432"/></g>
  <g Plane="5" href="220kV傣龙变.svg"><rect fill-opacity="0" height="44.17" width="94.73" x="1027.02" y="232.15" zvalue="113434"/></g>
  <g Plane="5" href="110kV勐嘎变.svg"><rect fill-opacity="0" height="38.37" width="102.55" x="610" y="322.3" zvalue="113437"/></g>
  <g Plane="5" href="110kV盏西变.svg"><rect fill-opacity="0" height="38.37" width="102.55" x="741" y="322.3" zvalue="113438"/></g>
  <g Plane="5" href="220kV卡场变.svg"><rect fill-opacity="0" height="44.17" width="94.73" x="1171" y="231" zvalue="113439"/></g>
  <g Plane="5" href="220kV盈江变.svg"><rect fill-opacity="0" height="44.17" width="94.73" x="903" y="231" zvalue="113440"/></g>
  <g href="大理地调二级.svg"><rect fill-opacity="0" height="33.1" width="85.40000000000001" x="104.09" y="206.54" zvalue="115426"/></g>
  <g href="大理地调二级.svg"><rect fill-opacity="0" height="31.12" width="86.3" x="1451" y="207.83" zvalue="115427"/></g>
  <g href="大理地调二级索引-南涧.svg"><rect fill-opacity="0" height="27.27" width="71.81999999999999" x="1037.02" y="832.13" zvalue="115458"/></g>
  <g href="配网首页.svg"><rect fill-opacity="0" height="32.11" width="117.86" x="452.13" y="106.73" zvalue="115473"/></g>
  <g href="大理地调二级.svg"><rect fill-opacity="0" height="32.11" width="117.86" x="140.74" y="106.73" zvalue="115474"/></g>
  <g href="运维中心.svg"><rect fill-opacity="0" height="32.11" width="117.86" x="1097.53" y="110.1" zvalue="115483"/></g>
  <g href="云南大理驾驶舱操控台主页面.svg"><rect fill-opacity="0" height="32.11" width="117.86" x="1654.99" y="107.85" zvalue="115489"/></g>
  <g href="首页高级应用.svg"><rect fill-opacity="0" height="32.11" width="117.86" x="788.29" y="107.85" zvalue="115494"/></g>
  <g href="大理地调二级索引-云龙.svg"><rect fill-opacity="0" height="27.27" width="71.81999999999999" x="690.2" y="456.27" zvalue="115580"/></g>
  <g href="大理地调二级索引-鹤庆.svg"><rect fill-opacity="0" height="27.27" width="71.81999999999999" x="1000.39" y="298.71" zvalue="115581"/></g>
  <g href="大理地调二级索引-洱源.svg"><rect fill-opacity="0" height="27.27" width="71.81999999999999" x="816.0599999999999" y="423.58" zvalue="115582"/></g>
  <g href="大理地调二级索引-永平.svg"><rect fill-opacity="0" height="27.27" width="71.81999999999999" x="730.36" y="605.67" zvalue="115583"/></g>
  <g href="大理地调二级索引-宾川.svg"><rect fill-opacity="0" height="27.27" width="71.81999999999999" x="1136.65" y="460.89" zvalue="115584"/></g>
  <g href="大理地调二级索引-巍山.svg"><rect fill-opacity="0" height="27.27" width="71.81999999999999" x="945.21" y="711.01" zvalue="115585"/></g>
  <g href="大理地调二级索引-剑川.svg"><rect fill-opacity="0" height="27.27" width="71.81999999999999" x="787.5599999999999" y="283.31" zvalue="115587"/></g>
  <g href="500kV黄坪变.svg"><rect fill-opacity="0" height="9.56" rx="33" ry="99" width="48.16" x="1024.83" y="378" zvalue="115598"/></g>
  <g href="220kV丁家庄变.svg"><rect fill-opacity="0" height="19.9" rx="33" ry="99" width="48.16" x="1041.42" y="692.75" zvalue="115599"/></g>
  <g href="220kV海东变.svg"><rect fill-opacity="0" height="19.9" rx="33" ry="99" width="48.16" x="1013.93" y="470.66" zvalue="115600"/></g>
  <g href="220kV苏屯变.svg"><rect fill-opacity="0" height="19.9" rx="33" ry="99" width="48.16" x="767.0599999999999" y="664.49" zvalue="115601"/></g>
  <g href="500kV大理变.svg"><rect fill-opacity="0" height="19.9" rx="33" ry="99" width="48.16" x="1047.83" y="579.04" zvalue="115602"/></g>
  <g href="220kV下关变.svg"><rect fill-opacity="0" height="19.9" rx="33" ry="99" width="48.16" x="984.84" y="631.49" zvalue="115603"/></g>
  <g href="220kV祥云变.svg"><rect fill-opacity="0" height="19.9" rx="33" ry="99" width="48.16" x="1180.32" y="646.22" zvalue="115604"/></g>
  <g href="220kV洱源变.svg"><rect fill-opacity="0" height="19.9" rx="33" ry="99" width="48.16" x="930.75" y="402.69" zvalue="115605"/></g>
  <g href="220kV剑川变.svg"><rect fill-opacity="0" height="19.9" rx="33" ry="99" width="48.16" x="809.1799999999999" y="323.59" zvalue="115606"/></g>
  <g href="220kV羊龙潭变.svg"><rect fill-opacity="0" height="19.9" rx="33" ry="99" width="48.16" x="936.47" y="284.06" zvalue="115607"/></g>
  <g href="新能源.svg"><rect fill-opacity="0" height="22.86" width="48.57" x="588.67" y="191.71" zvalue="115628"/></g>
  <g href="大理首页.svg"><rect fill-opacity="0" height="22.86" width="48.57" x="538.17" y="191.71" zvalue="115633"/></g>
  <g href="大理地调二级索引-大理.svg"><rect fill-opacity="0" height="27.27" width="71.81999999999999" x="947.9" y="518.6900000000001" zvalue="115639"/></g>
  <g href="大理地调二级索引-弥渡.svg"><rect fill-opacity="0" height="27.27" width="71.81999999999999" x="1102.06" y="731.87" zvalue="115640"/></g>
  <g href="大理地调二级索引-祥云.svg"><rect fill-opacity="0" height="27.27" width="71.81999999999999" x="1178.83" y="584.2" zvalue="115641"/></g>
  <g href="大理地调二级索引-漾濞.svg"><rect fill-opacity="0" height="27.27" width="57.5" x="862.71" y="609.66" zvalue="115679"/></g>
 </g>
 <g id="SubStationClass">
  <g id="536">
   <use class="kv220" height="30" transform="rotate(0,1007.75,495.148) scale(0.431792,0.413582) translate(1317.61,693.275)" width="30" x="1001.276218180185" xlink:href="#SubStation:厂站_0" y="488.9440138501687" zvalue="115593"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="海东变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1007.75,495.148) scale(0.431792,0.413582) translate(1317.61,693.275)" width="30" x="1001.276218180185" y="488.9440138501687"/></g>
  <g id="380">
   <use class="kv220" height="30" transform="rotate(0,691.232,317.793) scale(0.385165,0.41044) translate(1094.19,447.638)" width="30" x="685.4545454545455" xlink:href="#SubStation:厂站_0" y="311.6363636363637" zvalue="115657"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="苏屯变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,691.232,317.793) scale(0.385165,0.41044) translate(1094.19,447.638)" width="30" x="685.4545454545455" y="311.6363636363637"/></g>
  <g id="541">
   <use class="kv220" height="30" transform="rotate(0,945.423,432.191) scale(0.431792,0.413582) translate(1235.59,604.008)" width="30" x="938.9460692845512" xlink:href="#SubStation:厂站_0" y="425.9869970212308" zvalue="115588"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="洱源变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,945.423,432.191) scale(0.431792,0.413582) translate(1235.59,604.008)" width="30" x="938.9460692845512" y="425.9869970212308"/></g>
  <g id="540">
   <use class="kv220" height="30" transform="rotate(0,1040.58,634.859) scale(0.431792,0.413582) translate(1360.8,891.372)" width="30" x="1034.101223846884" xlink:href="#SubStation:厂站_0" y="628.6550400520874" zvalue="115589"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="下关变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1040.58,634.859) scale(0.431792,0.413582) translate(1360.8,891.372)" width="30" x="1034.101223846884" y="628.6550400520874"/></g>
  <g id="539">
   <use class="kv220" height="30" transform="rotate(0,863.954,334.982) scale(0.431792,0.413582) translate(1128.38,466.175)" width="30" x="857.4768716347083" xlink:href="#SubStation:厂站_0" y="328.7778085023106" zvalue="115590"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="剑川变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,863.954,334.982) scale(0.431792,0.413582) translate(1128.38,466.175)" width="30" x="857.4768716347083" y="328.7778085023106"/></g>
  <g id="538">
   <use class="kv500" height="30" transform="rotate(0,1008.02,408.524) scale(0.431792,0.413582) translate(1317.96,570.451)" width="30" x="1001.544584322518" xlink:href="#SubStation:厂站_0" y="402.3201858540478" zvalue="115591"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="黄坪变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1008.02,408.524) scale(0.431792,0.413582) translate(1317.96,570.451)" width="30" x="1001.544584322518" y="402.3201858540478"/></g>
  <g id="537">
   <use class="kv220" height="30" transform="rotate(0,979.248,312.641) scale(0.431792,0.413582) translate(1280.1,434.498)" width="30" x="972.7707368869902" xlink:href="#SubStation:厂站_0" y="306.4373632305573" zvalue="115592"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="羊龙潭变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,979.248,312.641) scale(0.431792,0.413582) translate(1280.1,434.498)" width="30" x="972.7707368869902" y="306.4373632305573"/></g>
  <g id="535">
   <use class="kv500" height="30" transform="rotate(0,1034.8,568.501) scale(0.431792,0.413582) translate(1353.2,797.282)" width="30" x="1028.32348279176" xlink:href="#SubStation:厂站_0" y="562.2968127131219" zvalue="115594"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="大理变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1034.8,568.501) scale(0.431792,0.413582) translate(1353.2,797.282)" width="30" x="1028.32348279176" y="562.2968127131219"/></g>
  <g id="534">
   <use class="kv220" height="30" transform="rotate(0,1098.48,705.527) scale(0.431792,0.413582) translate(1437,991.573)" width="30" x="1092.004001548366" xlink:href="#SubStation:厂站_0" y="699.323586901938" zvalue="115595"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="丁家庄变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1098.48,705.527) scale(0.431792,0.413582) translate(1437,991.573)" width="30" x="1092.004001548366" y="699.323586901938"/></g>
  <g id="533">
   <use class="kv220" height="30" transform="rotate(0,1169.39,657.444) scale(0.431792,0.413582) translate(1530.31,923.395)" width="30" x="1162.913092457457" xlink:href="#SubStation:厂站_0" y="651.2402535686047" zvalue="115596"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="祥云变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1169.39,657.444) scale(0.431792,0.413582) translate(1530.31,923.395)" width="30" x="1162.913092457457" y="651.2402535686047"/></g>
  <g id="532">
   <use class="kv220" height="30" transform="rotate(0,825.668,669.995) scale(0.431792,0.413582) translate(1078,941.191)" width="30" x="819.1906519626283" xlink:href="#SubStation:厂站_0" y="663.7909423077265" zvalue="115597"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="苏屯变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,825.668,669.995) scale(0.431792,0.413582) translate(1078,941.191)" width="30" x="819.1906519626283" y="663.7909423077265"/></g>
  <g id="511">
   <use class="kv500" height="30" transform="rotate(0,1275.07,467.109) scale(0.431792,0.413582) translate(1669.38,653.519)" width="30" x="1268.593876003672" xlink:href="#SubStation:厂站_0" y="460.9053841723987" zvalue="115618"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="牛井变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1275.07,467.109) scale(0.431792,0.413582) translate(1669.38,653.519)" width="30" x="1268.593876003672" y="460.9053841723987"/></g>
  <g id="364">
   <use class="kv500" height="30" transform="rotate(0,700.333,755.665) scale(0.431792,0.413582) translate(913.066,1062.66)" width="30" x="693.8562333780976" xlink:href="#SubStation:厂站_0" y="749.4611555083425" zvalue="115624"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="至兰城"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,700.333,755.665) scale(0.431792,0.413582) translate(913.066,1062.66)" width="30" x="693.8562333780976" y="749.4611555083425"/></g>
  <g id="354">
   <use class="kv220" height="30" transform="rotate(0,709.524,845.692) scale(0.385165,0.41044) translate(1123.38,1205.92)" width="30" x="703.7461130401647" xlink:href="#SubStation:厂站_0" y="839.5353478277998" zvalue="115634"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="苏屯变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,709.524,845.692) scale(0.385165,0.41044) translate(1123.38,1205.92)" width="30" x="703.7461130401647" y="839.5353478277998"/></g>
  <g id="352">
   <use class="kv500" height="30" transform="rotate(0,709.524,822.517) scale(0.385165,0.41044) translate(1123.38,1172.63)" width="30" x="703.7461130401648" xlink:href="#SubStation:厂站_0" y="816.3607501887024" zvalue="115636"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="至兰城"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,709.524,822.517) scale(0.385165,0.41044) translate(1123.38,1172.63)" width="30" x="703.7461130401648" y="816.3607501887024"/></g>
  <g id="379">
   <use class="kv220" height="30" transform="rotate(0,790.474,217.944) scale(0.385165,0.41044) translate(1252.61,304.215)" width="30" x="784.6969696969697" xlink:href="#SubStation:厂站_0" y="211.7878787878789" zvalue="115656"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="苏屯变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,790.474,217.944) scale(0.385165,0.41044) translate(1252.61,304.215)" width="30" x="784.6969696969697" y="211.7878787878789"/></g>
  <g id="311">
   <use class="kv220" height="30" transform="rotate(0,1098.74,351.573) scale(0.385165,0.41044) translate(1744.68,496.161)" width="30" x="1092.958333333333" xlink:href="#SubStation:厂站_0" y="345.4166666666666" zvalue="115662"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="苏屯变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1098.74,351.573) scale(0.385165,0.41044) translate(1744.68,496.161)" width="30" x="1092.958333333333" y="345.4166666666666"/></g>
  <g id="310">
   <use class="kv500" height="30" transform="rotate(0,1196.84,340.443) scale(0.385165,0.41044) translate(1901.29,480.173)" width="30" x="1191.066176470588" xlink:href="#SubStation:厂站_0" y="334.2867647058823" zvalue="115663"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="至兰城"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1196.84,340.443) scale(0.385165,0.41044) translate(1901.29,480.173)" width="30" x="1191.066176470588" y="334.2867647058823"/></g>
  <g id="328">
   <use class="kv500" height="30" transform="rotate(0,1283.48,609.204) scale(0.431792,0.413582) translate(1680.44,854.995)" width="30" x="1277" xlink:href="#SubStation:厂站_0" y="603" zvalue="115684"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="牛井变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1283.48,609.204) scale(0.431792,0.413582) translate(1680.44,854.995)" width="30" x="1277" y="603"/></g>
  <g id="84">
   <use class="kv500" height="30" transform="rotate(0,1281.48,589.204) scale(0.431792,0.413582) translate(1677.81,826.637)" width="30" x="1275" xlink:href="#SubStation:厂站_0" y="583" zvalue="115686"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="牛井变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1281.48,589.204) scale(0.431792,0.413582) translate(1677.81,826.637)" width="30" x="1275" y="583"/></g>
  <g id="82">
   <use class="kv220" height="30" transform="rotate(0,675.777,716.379) scale(0.385165,0.41044) translate(1069.52,1020.17)" width="30" x="670" xlink:href="#SubStation:厂站_0" y="710.2222222222222" zvalue="115688"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="苏屯变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,675.777,716.379) scale(0.385165,0.41044) translate(1069.52,1020.17)" width="30" x="670" y="710.2222222222222"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="337">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="21" id="337" stroke="rgb(85,255,255)" suffix=" MW" text-anchor="middle" text-weight="bolder" transform="rotate(0,1626.03,557.232) scale(1,1) translate(3.42766e-13,-1.20459e-13)" writing-mode="lr" x="1626.03" xml:space="preserve" y="567.8" zvalue="1">ddd.dd MW</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132451037188" ObjectName="总出力"/>
   </metadata>
  </g>
  <g id="301">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="301" stroke="rgb(255,255,0)" suffix="MW" text-anchor="middle" transform="rotate(0,104,993) scale(1,1) translate(0,0)" writing-mode="lr" x="104" xml:space="preserve" y="1001.19" zvalue="1">ddd.ddMW</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132401098756" ObjectName="大理城区负荷总加"/>
   </metadata>
  </g>
  <g id="300">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="300" stroke="rgb(255,255,0)" suffix="MW" text-anchor="middle" transform="rotate(0,232,993) scale(1,1) translate(0,0)" writing-mode="lr" x="232" xml:space="preserve" y="1001.19" zvalue="1">ddd.ddMW</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132401426436" ObjectName="祥云有功总加"/>
   </metadata>
  </g>
  <g id="299">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="299" stroke="rgb(255,255,0)" suffix="MW" text-anchor="middle" transform="rotate(0,356.725,993) scale(1,1) translate(0,0)" writing-mode="lr" x="356.73" xml:space="preserve" y="1001.19" zvalue="1">ddd.ddMW</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132401819652" ObjectName="大理漾濞网供负荷"/>
   </metadata>
  </g>
  <g id="298">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="298" stroke="rgb(255,255,0)" suffix="MW" text-anchor="middle" transform="rotate(0,482.725,993) scale(1,1) translate(0,0)" writing-mode="lr" x="482.73" xml:space="preserve" y="1001.19" zvalue="1">ddd.ddMW</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132401295364" ObjectName="南涧有功总加"/>
   </metadata>
  </g>
  <g id="279">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="279" stroke="rgb(255,255,0)" suffix="MW" text-anchor="middle" transform="rotate(0,608.725,993) scale(1,1) translate(0,0)" writing-mode="lr" x="608.73" xml:space="preserve" y="1001.19" zvalue="1">ddd.ddMW</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132401688580" ObjectName="巍山有功总加"/>
   </metadata>
  </g>
  <g id="278">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="278" stroke="rgb(255,255,0)" suffix="MW" text-anchor="middle" transform="rotate(0,734.725,993) scale(1,1) translate(0,0)" writing-mode="lr" x="734.73" xml:space="preserve" y="1001.19" zvalue="1">ddd.ddMW</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132401491972" ObjectName="弥渡有功总加"/>
   </metadata>
  </g>
  <g id="216">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="216" stroke="rgb(255,255,0)" suffix="MW" text-anchor="middle" transform="rotate(0,1214.73,989) scale(1,1) translate(0,0)" writing-mode="lr" x="1214.73" xml:space="preserve" y="997.1900000000001" zvalue="1">ddd.ddMW</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132401229828" ObjectName="宾川有功总加"/>
   </metadata>
  </g>
  <g id="213">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="213" stroke="rgb(255,255,0)" suffix="MW" text-anchor="middle" transform="rotate(0,1340.73,989) scale(1,1) translate(0,0)" writing-mode="lr" x="1340.73" xml:space="preserve" y="997.1900000000001" zvalue="1">ddd.ddMW</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132401623044" ObjectName="剑川有功总加"/>
   </metadata>
  </g>
  <g id="201">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="201" stroke="rgb(255,255,0)" suffix="MW" text-anchor="middle" transform="rotate(0,1466.73,989) scale(1,1) translate(0,0)" writing-mode="lr" x="1466.73" xml:space="preserve" y="997.1900000000001" zvalue="1">ddd.ddMW</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132401754116" ObjectName="永平有功总加"/>
   </metadata>
  </g>
  <g id="195">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="195" stroke="rgb(255,255,0)" suffix="MW" text-anchor="middle" transform="rotate(0,1592.73,989) scale(1,1) translate(0,0)" writing-mode="lr" x="1592.73" xml:space="preserve" y="997.1900000000001" zvalue="1">ddd.ddMW</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132401557508" ObjectName="云龙有功总加"/>
   </metadata>
  </g>
  <g id="193">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="193" stroke="rgb(255,255,0)" suffix="MW" text-anchor="middle" transform="rotate(0,1718.73,989) scale(1,1) translate(0,0)" writing-mode="lr" x="1718.73" xml:space="preserve" y="997.1900000000001" zvalue="1">ddd.ddMW</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132401885188" ObjectName="大理鹤庆网供负荷"/>
   </metadata>
  </g>
  <g id="322">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="21" id="322" stroke="rgb(85,255,255)" suffix=" MW" text-anchor="middle" transform="rotate(0,1630.5,766.265) scale(1,1) translate(0,0)" writing-mode="lr" x="1630.5" xml:space="preserve" y="776.47" zvalue="1">ddd.dd MW</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132400181252" ObjectName="风电有功总加"/>
   </metadata>
  </g>
  <g id="651">
   <text Format="f4.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" id="651" stroke="rgb(255,255,255)" text-anchor="middle" text-weight="bolder" transform="rotate(0,541.083,42.0119) scale(1,1) translate(0,0)" writing-mode="lr" x="541.08" xml:space="preserve" y="54.97" zvalue="1">dddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="4785074604081154" ObjectName="大理市"/>
   </metadata>
  </g>
  <g id="550">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="21" id="550" stroke="rgb(85,255,255)" suffix=" MW" text-anchor="middle" text-weight="bolder" transform="rotate(0,1638.53,219.232) scale(1,1) translate(3.45541e-13,2.27041e-14)" writing-mode="lr" x="1638.53" xml:space="preserve" y="229.8" zvalue="1">ddd.dd MW</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132400115716" ObjectName="中调关口有功总加"/>
   </metadata>
  </g>
  <g id="151">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="151" stroke="rgb(255,255,0)" suffix="MW" text-anchor="middle" transform="rotate(0,1844.73,989) scale(1,1) translate(0,0)" writing-mode="lr" x="1844.73" xml:space="preserve" y="997.1900000000001" zvalue="1">ddd.ddMW</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132401360900" ObjectName="洱源有功总加"/>
   </metadata>
  </g>
  <g id="312">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="312" stroke="rgb(255,255,255)" text-anchor="middle" text-weight="bolder" transform="rotate(0,1489.42,42.0119) scale(1,1) translate(0,0)" writing-mode="lr" x="1489.42" xml:space="preserve" y="53.47" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="321">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="21" id="321" stroke="rgb(85,255,255)" suffix=" MW" text-anchor="middle" transform="rotate(0,1630.5,799.987) scale(1,1) translate(0,0)" writing-mode="lr" x="1630.5" xml:space="preserve" y="810.2" zvalue="1">ddd.dd MW</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132400246788" ObjectName="光伏有功总加"/>
   </metadata>
  </g>
  <g id="32">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="21" id="32" stroke="rgb(85,255,255)" suffix=" MW" text-anchor="middle" transform="rotate(0,1630.5,833.709) scale(1,1) translate(0,0)" writing-mode="lr" x="1630.5" xml:space="preserve" y="843.92" zvalue="1">ddd.dd MW</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132450971652" ObjectName="水电出力"/>
   </metadata>
  </g>
 </g>
 <g id="ClockClass">
  
 </g>
</svg>