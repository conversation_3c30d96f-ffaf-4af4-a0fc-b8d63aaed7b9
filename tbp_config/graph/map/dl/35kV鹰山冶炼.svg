<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1200" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.v400{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(0,255,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Accessory:避雷器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000003" y2="8.666666666666668"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="12.6" y2="18.6"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.499999999999999" x2="5.916666666666666" y1="3.583333333333334" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_3" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_4" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="14.07232884821164" y2="14.07232884821164"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:放电间隙2_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="5.083333333333336"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="9" y2="14"/>
   <path d="M 15.0179 14.1078 L 11.5 18.1667 L 18.5802 18.1667 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 15.0333 9.11336 L 11.5154 5.05453 L 18.4321 5.05453 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:带熔断器四卷PT_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12" y2="1"/>
   <ellipse cx="15.25" cy="16.75" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.08" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.25" cy="20.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="18.82" cy="20.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="11" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11" y1="23.25" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.25" x2="15.25" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.25" x2="15.25" y1="19" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.25" x2="15.25" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="15" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="15" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="20.94444444444444" y1="21.46612466124661" y2="21.46612466124661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="18.31481481481482" y1="21.46612466124661" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.94444444444444" x2="19.62962962962963" y1="21.46612466124661" y2="19"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.01,6.21) scale(1,1) translate(0,0)" width="4.92" x="12.55" y="2.5"/>
  </symbol>
  <symbol id="Accessory:站用变0924_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="0" x="11" xlink:href="#terminal" y="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.41666666666666" x2="20.41666666666666" y1="23.83333333333333" y2="31.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.41666666666666" x2="25.41666666666666" y1="29.83333333333333" y2="29.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.41666666666666" x2="26.41666666666666" y1="28.83333333333333" y2="28.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.41666666666667" x2="24.41666666666666" y1="23.83333333333333" y2="23.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.96666666666667" x2="24.96666666666667" y1="30.83333333333334" y2="30.83333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.41666666666666" x2="24.41666666666666" y1="23.83333333333333" y2="28.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.41666666666666" x2="11.41666666666667" y1="31.83333333333334" y2="35.83333333333334"/>
   <path d="M 10.0833 36 L 12.0833 36 L 11.0833 38 L 10.0833 36 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.11666666666667" x2="11.11666666666667" y1="30.58333333333333" y2="43"/>
   <ellipse cx="10.98" cy="12.66" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.98" cy="24" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.97838478538902" x2="10.97838478538902" y1="20.99779929207955" y2="23.59678739209649"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.57193227486047" x2="10.97838478538901" y1="26.1957754921134" y2="23.59678739209647"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.72838478538902" x2="10.72838478538902" y1="9.747799292079552" y2="12.34678739209649"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.32193227486047" x2="10.72838478538901" y1="14.9457754921134" y2="12.34678739209647"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.384837295917544" x2="10.978384785389" y1="26.1957754921134" y2="23.59678739209647"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.134837295917544" x2="10.728384785389" y1="14.9457754921134" y2="12.34678739209647"/>
   <path d="M 10.0833 43 L 12.0833 43 L 11.0833 45 L 10.0833 43 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:熔断器2_0" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="7"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="6.916666666666668" y2="23.25"/>
   <rect fill-opacity="0" height="16.08" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.06,15.06) scale(1,1) translate(0,0)" width="9.58" x="0.27" y="7.02"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV鹰山冶炼" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass"/>
 <g id="OtherClass">
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="48" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,157,100.5) scale(1,1) translate(0,0)" writing-mode="lr" x="157" xml:space="preserve" y="117.5" zvalue="2486">鹰山冶炼</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="27" id="517" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,345.286,625) scale(1,1) translate(0,0)" writing-mode="lr" x="345.29" xml:space="preserve" y="634.5" zvalue="1258">35kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="509" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,483.419,63.2951) scale(1,1) translate(0,0)" writing-mode="lr" x="483.42" xml:space="preserve" y="69.3" zvalue="1281">35kV鹰山冶炼厂线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="21" id="352" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1032.32,1125.45) scale(1,1) translate(0,0)" writing-mode="lr" x="1032.32" xml:space="preserve" y="1132.95" zvalue="2460">35kV1号主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,559.014,244.004) scale(1,1) translate(0,0)" writing-mode="lr" x="559.01" xml:space="preserve" y="251.5" zvalue="2490">3618</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,615.314,353.987) scale(1,1) translate(0,0)" writing-mode="lr" x="615.3099999999999" xml:space="preserve" y="360.49" zvalue="2492">36187</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,557.8,493.004) scale(1,1) translate(0,0)" writing-mode="lr" x="557.8" xml:space="preserve" y="500.5" zvalue="2497">3616</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="21" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,778,606.5) scale(1,1) translate(0,0)" writing-mode="lr" x="778" xml:space="preserve" y="614" zvalue="2507">35kVI母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,766.014,755.004) scale(1,1) translate(0,0)" writing-mode="lr" x="766.01" xml:space="preserve" y="762.5" zvalue="2513">3621</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="21" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,696,1083.5) scale(1,1) translate(0,0)" writing-mode="lr" x="696" xml:space="preserve" y="1091" zvalue="2518">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1089.51,765.004) scale(1,1) translate(0,0)" writing-mode="lr" x="1089.51" xml:space="preserve" y="772.5" zvalue="2524">3011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1121,878) scale(1,1) translate(0,0)" writing-mode="lr" x="1121" xml:space="preserve" y="885.5" zvalue="2525">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="21" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,936.314,990.638) scale(1,1) translate(0,0)" writing-mode="lr" x="936.3099999999999" xml:space="preserve" y="998.14" zvalue="2527">30167</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="35" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1252,272) scale(1,1) translate(0,0)" writing-mode="lr" x="1252" xml:space="preserve" y="284.5" zvalue="2534">35kV预留间隔3</text>
 </g>
 <g id="BusbarSectionClass">
  <g id="937">
   <path class="kv35" d="M 179 662.57 L 1625 662.57" stroke-width="6" zvalue="1257"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV#Ⅰ母线"/>
   </metadata>
  <path d="M 179 662.57 L 1625 662.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="885">
   <use class="kv35" height="30" transform="rotate(0,497.599,128.724) scale(5.25755,1.86563) translate(-388.053,-46.7419)" width="7" x="479.1971796289519" xlink:href="#ACLineSegment:线路_0" y="100.7394952368143" zvalue="1280"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV鹰山冶炼厂线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,497.599,128.724) scale(5.25755,1.86563) translate(-388.053,-46.7419)" width="7" x="479.1971796289519" y="100.7394952368143"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="364">
   <use class="kv35" height="30" transform="rotate(180,1032.32,1077.2) scale(2.09383,2.06591) translate(-532.727,-539.797)" width="12" x="1019.7560737456" xlink:href="#EnergyConsumer:负荷_0" y="1046.215300468091" zvalue="2459"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号主变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1032.32,1077.2) scale(2.09383,2.06591) translate(-532.727,-539.797)" width="12" x="1019.7560737456" y="1046.215300468091"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="360">
   <use class="kv35" height="26" transform="rotate(270,606.261,180.958) scale(2.00708,2.51838) translate(-298.157,-89.3638)" width="12" x="594.2186482798977" xlink:href="#Accessory:避雷器_0" y="148.2186482798978" zvalue="2466"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV鹰山冶炼厂线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,606.261,180.958) scale(2.00708,2.51838) translate(-298.157,-89.3638)" width="12" x="594.2186482798977" y="148.2186482798978"/></g>
  <g id="7">
   <use class="kv35" height="30" transform="rotate(0,500.5,386.5) scale(3.83333,3.83333) translate(-327.435,-243.174)" width="30" x="443" xlink:href="#Accessory:放电间隙2_0" y="329" zvalue="2492"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV鹰山冶炼厂线放电间隙"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,500.5,386.5) scale(3.83333,3.83333) translate(-327.435,-243.174)" width="30" x="443" y="329"/></g>
  <g id="9">
   <use class="kv35" height="26" transform="rotate(270,614.261,423.958) scale(2.00708,2.51838) translate(-302.171,-235.873)" width="12" x="602.2186482798977" xlink:href="#Accessory:避雷器_0" y="391.2186482798978" zvalue="2494"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV鹰山冶炼厂线避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,614.261,423.958) scale(2.00708,2.51838) translate(-302.171,-235.873)" width="12" x="602.2186482798977" y="391.2186482798978"/></g>
  <g id="29">
   <use class="kv35" height="30" transform="rotate(0,628,602) scale(3.66667,3.66667) translate(-416.727,-397.818)" width="30" x="573" xlink:href="#Accessory:带熔断器四卷PT_0" y="547" zvalue="2506"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kVI母电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,628,602) scale(3.66667,3.66667) translate(-416.727,-397.818)" width="30" x="573" y="547"/></g>
  <g id="58">
   <use class="kv35" height="50" transform="rotate(0,691,974.667) scale(-3.53333,3.53333) translate(-848.566,-635.484)" width="30" x="638" xlink:href="#Accessory:站用变0924_0" y="886.3333333333333" zvalue="2517"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,691,974.667) scale(-3.53333,3.53333) translate(-848.566,-635.484)" width="30" x="638" y="886.3333333333333"/></g>
  <g id="62">
   <use class="kv35" height="30" transform="rotate(0,705.5,843.5) scale(2.3,3.3) translate(-392.261,-553.394)" width="10" x="694" xlink:href="#Accessory:熔断器2_0" y="794" zvalue="2518"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号站用变熔断"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,705.5,843.5) scale(2.3,3.3) translate(-392.261,-553.394)" width="10" x="694" y="794"/></g>
  <g id="90">
   <use class="kv35" height="26" transform="rotate(270,1126.26,942.958) scale(2.00708,2.51838) translate(-559.074,-548.788)" width="12" x="1114.218648279898" xlink:href="#Accessory:避雷器_0" y="910.2186482798978" zvalue="2528"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号主变避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1126.26,942.958) scale(2.00708,2.51838) translate(-559.074,-548.788)" width="12" x="1114.218648279898" y="910.2186482798978"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="3">
   <use class="kv35" height="30" transform="rotate(0,499.007,250.004) scale(-3.3324,2.44376) translate(-631.258,-126.044)" width="15" x="474.0140625325685" xlink:href="#Disconnector:刀闸_0" y="213.3474677524395" zvalue="2489"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV鹰山冶炼厂线3618"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,499.007,250.004) scale(-3.3324,2.44376) translate(-631.258,-126.044)" width="15" x="474.0140625325685" y="213.3474677524395"/></g>
  <g id="10">
   <use class="kv35" height="30" transform="rotate(0,500.793,491.004) scale(-3.3324,2.44376) translate(-633.58,-268.426)" width="15" x="475.7995391446445" xlink:href="#Disconnector:刀闸_0" y="454.3474677524395" zvalue="2496"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV鹰山冶炼厂线3616"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,500.793,491.004) scale(-3.3324,2.44376) translate(-633.58,-268.426)" width="15" x="475.7995391446445" y="454.3474677524395"/></g>
  <g id="48">
   <use class="kv35" height="30" transform="rotate(0,706.007,761.004) scale(-3.3324,2.44376) translate(-900.376,-427.94)" width="15" x="681.0140625325686" xlink:href="#Disconnector:刀闸_0" y="724.3474677524395" zvalue="2512"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号站用变3621"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,706.007,761.004) scale(-3.3324,2.44376) translate(-900.376,-427.94)" width="15" x="681.0140625325686" y="724.3474677524395"/></g>
  <g id="69">
   <use class="kv35" height="30" transform="rotate(0,1030.01,758.004) scale(-3.3324,2.44376) translate(-1321.6,-426.168)" width="15" x="1005.014062532569" xlink:href="#Disconnector:刀闸_0" y="721.3474677524395" zvalue="2523"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号主变3011"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1030.01,758.004) scale(-3.3324,2.44376) translate(-1321.6,-426.168)" width="15" x="1005.014062532569" y="721.3474677524395"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="5">
   <use class="kv35" height="30" transform="rotate(270,607.314,311.813) scale(2.2209,2.2209) translate(-326.535,-153.1)" width="12" x="593.9881190059474" xlink:href="#GroundDisconnector:地刀12_0" y="278.499156279084" zvalue="2491"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV鹰山冶炼厂线36187"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,607.314,311.813) scale(2.2209,2.2209) translate(-326.535,-153.1)" width="12" x="593.9881190059474" y="278.499156279084"/></g>
  <g id="89">
   <use class="kv35" height="30" transform="rotate(90,936.314,947.813) scale(2.2209,2.2209) translate(-507.397,-502.73)" width="12" x="922.9881190059475" xlink:href="#GroundDisconnector:地刀12_0" y="914.499156279084" zvalue="2526"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号主变30167"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,936.314,947.813) scale(2.2209,2.2209) translate(-507.397,-502.73)" width="12" x="922.9881190059475" y="914.499156279084"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="12">
   <path class="kv35" d="M 497.6 156.43 L 497.6 214.56" stroke-width="1" zvalue="2497"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="885@0" LinkObjectIDznd="3@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 497.6 156.43 L 497.6 214.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="kv35" d="M 575.12 180.89 L 497.6 180.89" stroke-width="1" zvalue="2498"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="360@0" LinkObjectIDznd="12" MaxPinNum="2"/>
   </metadata>
  <path d="M 575.12 180.89 L 497.6 180.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="21">
   <path class="kv35" d="M 500.5 348.49 L 500.5 455.56" stroke-width="1" zvalue="2502"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="7@0" LinkObjectIDznd="10@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 500.5 348.49 L 500.5 455.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="22">
   <path class="kv35" d="M 500.5 348.49 L 500.5 286.03" stroke-width="1" zvalue="2503"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="21" LinkObjectIDznd="3@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 500.5 348.49 L 500.5 286.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv35" d="M 575.5 311.84 L 500.5 311.84" stroke-width="1" zvalue="2504"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="5@0" LinkObjectIDznd="22" MaxPinNum="2"/>
   </metadata>
  <path d="M 575.5 311.84 L 500.5 311.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="28">
   <path class="kv35" d="M 583.12 423.89 L 500.5 423.89" stroke-width="1" zvalue="2505"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@0" LinkObjectIDznd="21" MaxPinNum="2"/>
   </metadata>
  <path d="M 583.12 423.89 L 500.5 423.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="34">
   <path class="kv35" d="M 500.59 527.03 L 500.59 662.57" stroke-width="1" zvalue="2509"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="10@1" LinkObjectIDznd="937@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 500.59 527.03 L 500.59 662.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv35" d="M 500.59 530 L 628 530 L 628 550.67" stroke-width="1" zvalue="2510"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="34" LinkObjectIDznd="29@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 500.59 530 L 628 530 L 628 550.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv35" d="M 705.71 725.56 L 705.71 662.57" stroke-width="1" zvalue="2519"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@0" LinkObjectIDznd="937@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 705.71 725.56 L 705.71 662.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="kv35" d="M 705.8 797.03 L 705.8 817.1" stroke-width="1" zvalue="2520"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@1" LinkObjectIDznd="62@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 705.8 797.03 L 705.8 817.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv35" d="M 705.8 817.1 L 705.8 907.53" stroke-width="1" zvalue="2521"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67" LinkObjectIDznd="58@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 705.8 817.1 L 705.8 907.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv35" d="M 1029.71 722.56 L 1029.71 662.57" stroke-width="1" zvalue="2529"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@0" LinkObjectIDznd="937@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1029.71 722.56 L 1029.71 662.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv35" d="M 1029.8 794.03 L 1029.8 816.03 L 1030.15 816.03 L 1030.15 838.03" stroke-width="1" zvalue="2530"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@1" LinkObjectIDznd="85@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1029.8 794.03 L 1029.8 816.03 L 1030.15 816.03 L 1030.15 838.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="kv35" d="M 1030.46 892.22 L 1030.46 1049.31" stroke-width="1" zvalue="2531"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85@1" LinkObjectIDznd="364@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1030.46 892.22 L 1030.46 1049.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv35" d="M 968.12 947.79 L 1030.46 947.79" stroke-width="1" zvalue="2532"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@0" LinkObjectIDznd="94" MaxPinNum="2"/>
   </metadata>
  <path d="M 968.12 947.79 L 1030.46 947.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv35" d="M 1095.12 942.89 L 1030.46 942.89" stroke-width="1" zvalue="2533"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@0" LinkObjectIDznd="94" MaxPinNum="2"/>
   </metadata>
  <path d="M 1095.12 942.89 L 1030.46 942.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="85">
   <use class="kv35" height="20" transform="rotate(0,1030.25,865.15) scale(3.15,2.835) translate(-692.437,-541.632)" width="10" x="1014.5" xlink:href="#Breaker:开关_0" y="836.8" zvalue="2524"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV1号主变301"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1030.25,865.15) scale(3.15,2.835) translate(-692.437,-541.632)" width="10" x="1014.5" y="836.8"/></g>
 </g>
</svg>