<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1200" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.v400{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(0,255,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.16666666666667" x2="7.583333333333333" y1="6.416666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.1666666666666661" x2="5.916666666666666" y1="3.666666666666666" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5" x2="10.25" y1="4.583333333333332" y2="20.5"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_3" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="5.166666666666668" y2="21"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.999999999999998" y2="20.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_4" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="14.07232884821164" y2="14.07232884821164"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:三卷PT带容断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,6.5) scale(1,1) translate(0,0)" width="4" x="13" y="3"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Breaker:刀闸绘制规范_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.077060931899643" xlink:href="#terminal" y="0.7027600849256537"/>
   <use terminal-index="1" type="0" x="5.077060931899643" xlink:href="#terminal" y="19.29723991507431"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.835125448028674" x2="6.249999999999999" y1="2.796178343949066" y2="4.704883227176235"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.077060931899643" x2="5.077060931899643" y1="12.58598726114649" y2="19.23566878980892"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.077060931899643" x2="0.333333333333333" y1="12.5859872611465" y2="5.083333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.077060931899643" x2="5.077060931899643" y1="3.658174097664537" y2="0.7027600849256821"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.249999999999999" x2="3.835125448028674" y1="2.796178343949066" y2="4.704883227176235"/>
  </symbol>
  <symbol id="Breaker:刀闸绘制规范_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.077060931899643" xlink:href="#terminal" y="0.7027600849256537"/>
   <use terminal-index="1" type="0" x="5.077060931899643" xlink:href="#terminal" y="19.29723991507431"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="1" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="8" y1="4" y2="4"/>
  </symbol>
  <symbol id="Breaker:刀闸绘制规范_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.077060931899643" xlink:href="#terminal" y="0.7027600849256537"/>
   <use terminal-index="1" type="0" x="5.077060931899643" xlink:href="#terminal" y="19.29723991507431"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.077060931899643" x2="5.077060931899643" y1="3.658174097664537" y2="0.7027600849256821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.077060931899643" x2="5.077060931899643" y1="12.58598726114649" y2="19.23566878980892"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.835125448028674" x2="6.249999999999999" y1="2.796178343949066" y2="4.704883227176235"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.249999999999999" x2="3.835125448028674" y1="2.796178343949066" y2="4.704883227176235"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8" x2="1.666666666666666" y1="13.5" y2="2.749999999999997"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.666666666666666" x2="8" y1="14" y2="3.249999999999998"/>
  </symbol>
  <symbol id="PowerTransformer2:D-Y_0" viewBox="0,0,30,50">
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.75" ry="14.75" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="0" type="1" x="15.00731595793324" xlink:href="#terminal" y="0.3902606310013716"/>
  </symbol>
  <symbol id="PowerTransformer2:D-Y_1" viewBox="0,0,30,50">
   <ellipse cx="15" cy="35.25" fill-opacity="0" rx="14.75" ry="14.75" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="1" type="1" x="15.00325153685922" xlink:href="#terminal" y="49.86055225321343"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15" x2="20" y1="37.74761215261901" y2="42.91666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.25" x2="15" y1="43.08333333333334" y2="37.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01666666666667" x2="15.01666666666667" y1="37.5" y2="32.91666666666667"/>
   <use terminal-index="2" type="2" x="15" xlink:href="#terminal" y="37.75085860895189"/>
  </symbol>
  <symbol id="Accessory:PT象达_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15.5" xlink:href="#terminal" y="7.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.25" x2="18.25" y1="12.08333333333333" y2="12.08333333333333"/>
   <ellipse cx="15.65" cy="12.68" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.9" cy="18.28" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.25" x2="18.25" y1="18.33333333333333" y2="18.33333333333333"/>
  </symbol>
  <symbol id="Ground:大地_0" viewBox="0,0,12,18">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6" x2="6" y1="13.08333333333334" y2="0.1666666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.5833333333333313" x2="11.25" y1="12.99453511141348" y2="12.99453511141348"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.083333333333333" x2="8" y1="17.63116790988687" y2="17.63116790988687"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666667" x2="10.33333333333333" y1="15.40451817731685" y2="15.40451817731685"/>
  </symbol>
  <symbol id="Generator:发电机12_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="13"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="15" y1="19" y2="13"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="30" y2="13"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="19" y2="30"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="20" y1="20" y2="30"/>
   <ellipse cx="10.25" cy="22.83" fill-opacity="0" rx="2" ry="2" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="24" y1="13" y2="13"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="15" y1="20" y2="13"/>
   <ellipse cx="19.75" cy="22.83" fill-opacity="0" rx="2" ry="2" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="10.25" cy="27.33" fill-opacity="0" rx="2" ry="2" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="15" cy="22.83" fill-opacity="0" rx="2" ry="2" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="15" cy="27.33" fill-opacity="0" rx="2" ry="2" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="19.75" cy="27.33" fill-opacity="0" rx="2" ry="2" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="14.9" cy="13.08" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:电流互感器11_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="6"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="10" y2="10"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="6" y2="14"/>
   <ellipse cx="6" cy="10" fill-opacity="0" rx="2.83" ry="2.83" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="19" y1="10" y2="10"/>
   <ellipse cx="23.75" cy="10" fill-opacity="0" rx="2.83" ry="2.83" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="15" cy="10" fill-opacity="0" rx="2.83" ry="2.83" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.75" x2="23.75" y1="6" y2="14"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.75" x2="27.75" y1="10" y2="10"/>
   <ellipse cx="6" cy="19.75" fill-opacity="0" rx="2.83" ry="2.83" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="15.75" y2="23.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="19.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="6" y2="14"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.75" x2="27.75" y1="19.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.75" x2="23.75" y1="15.75" y2="23.75"/>
   <ellipse cx="23.75" cy="19.75" fill-opacity="0" rx="2.83" ry="2.83" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="15" cy="19.75" fill-opacity="0" rx="2.83" ry="2.83" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="15.75" y2="23.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="19" y1="19.75" y2="19.75"/>
  </symbol>
  <symbol id="Accessory:电流互感器13_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.75" xlink:href="#terminal" y="19"/>
   <ellipse cx="5.83" cy="15.33" fill-opacity="0" rx="2.83" ry="2.83" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.833333333333337" x2="9.833333333333336" y1="15.33333333333333" y2="15.33333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.833333333333336" x2="5.833333333333336" y1="11.33333333333333" y2="19.33333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.58333333333333" x2="23.58333333333333" y1="11.33333333333333" y2="19.33333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.83333333333333" x2="14.83333333333333" y1="11.33333333333333" y2="19.33333333333333"/>
   <ellipse cx="14.83" cy="15.33" fill-opacity="0" rx="2.83" ry="2.83" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="23.58" cy="15.33" fill-opacity="0" rx="2.83" ry="2.83" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.83333333333333" x2="18.83333333333333" y1="15.33333333333333" y2="15.33333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="27.58333333333333" y1="15.33333333333333" y2="15.33333333333333"/>
  </symbol>
  <symbol id="Accessory:电流互感器14_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="24"/>
   <path d="M 10.5816 17.7655 L 7.86054 18.9671" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.221088435374146" x2="9.221088435374146" y1="18.66666666666666" y2="22.27131782945736"/>
   <path d="M 7.86054 17.7655 L 10.5816 18.9671" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 9.22109 9.35465 L 6.5 14.1609 L 6.5 22.5717" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.5" x2="22.48639455782313" y1="22.57170542635659" y2="22.57170542635659"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.499999999999996" x2="22.25963718820862" y1="23.7732558139535" y2="23.7732558139535"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.221088435374147" x2="7.860544217687073" y1="5.750000000000018" y2="9.354651162790713"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.221088435374147" x2="10.58163265306122" y1="5.750000000000018" y2="9.354651162790713"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.221088435374147" x2="11.9421768707483" y1="15.36240310077519" y2="17.76550387596899"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.221088435374146" x2="9.221088435374146" y1="9.354651162790697" y2="15.36240310077519"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.860544217687069" x2="10.58163265306122" y1="9.354651162790701" y2="9.354651162790701"/>
   <ellipse cx="9.220000000000001" cy="12.06" fill-opacity="0" rx="1.5" ry="1.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="20.45" cy="12.06" fill-opacity="0" rx="1.5" ry="1.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.44557823129252" x2="19.08503401360544" y1="5.750000000000018" y2="9.354651162790713"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.44557823129251" x2="21.80612244897959" y1="5.750000000000018" y2="9.354651162790713"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.08503401360544" x2="21.80612244897959" y1="9.354651162790701" y2="9.354651162790701"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.44557823129251" x2="20.44557823129251" y1="9.354651162790697" y2="15.36240310077519"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.44557823129252" x2="23.16666666666666" y1="15.36240310077519" y2="17.76550387596899"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.44557823129251" x2="20.44557823129251" y1="18.66666666666666" y2="22.27131782945736"/>
   <path d="M 19.085 17.7655 L 21.8061 18.9671" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 21.8061 17.7655 L 19.085 18.9671" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.00340136054422" x2="15.00340136054422" y1="9.354651162790697" y2="15.36240310077519"/>
   <path d="M 15.0034 9.35465 L 12.2823 14.1609 L 12.2823 22.5717" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.00340136054422" x2="13.64285714285714" y1="5.750000000000018" y2="9.354651162790713"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.64285714285714" x2="16.36394557823129" y1="9.354651162790701" y2="9.354651162790701"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.00340136054422" x2="16.36394557823129" y1="5.750000000000018" y2="9.354651162790713"/>
   <ellipse cx="15" cy="12.06" fill-opacity="0" rx="1.5" ry="1.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.00340136054422" x2="15.00340136054422" y1="18.66666666666666" y2="22.27131782945736"/>
   <path d="M 20.4456 9.35465 L 17.7245 14.1609 L 17.7245 22.5717" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 16.3639 17.7655 L 13.6429 18.9671" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.00340136054422" x2="17.72448979591837" y1="15.36240310077519" y2="17.76550387596899"/>
   <path d="M 13.6429 17.7655 L 16.3639 18.9671" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Fuse:融丝1_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="9" xlink:href="#terminal" y="4.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16666666666667" x2="19.16666666666667" y1="19.91666666666666" y2="19.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.16666666666667" x2="9.166666666666668" y1="16.91666666666666" y2="11.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.16666666666667" x2="19.16666666666667" y1="19.91666666666666" y2="16.91666666666666"/>
   <path d="M 4.25 4.66667 L 13.9167 4.66667 L 9.08333 11.3056 L 4.25 4.66667 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.08333333333333" x2="9.08333333333333" y1="17.94444444444444" y2="11.30555555555555"/>
   <path d="M 4.33333 24.5833 L 14 24.5833 L 9.16667 17.9444 L 4.33333 24.5833 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="26.19791666666667" x2="26.19791666666667" y1="22.51556547784938" y2="19.74999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="25.47916666666667" x2="26.94791666666667" y1="23.48929458524702" y2="23.48929458524702"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="24.16666666666667" x2="28.16666666666667" y1="22.49655304194982" y2="22.49655304194982"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="24.57291666666667" x2="27.82291666666667" y1="23.01255040731219" y2="23.01255040731219"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="六五河一级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass"/>
 <g id="OtherClass">
  
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="48" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,309.452,100) scale(1,1) translate(0,0)" writing-mode="lr" x="309.45" xml:space="preserve" y="117" zvalue="1917">        六五河一级电站</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,887.215,148.262) scale(1,1) translate(0,0)" writing-mode="lr" x="887.21" xml:space="preserve" y="152.76" zvalue="1475">35kV六富线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,910.21,319.648) scale(1,1) translate(0,0)" writing-mode="lr" x="910.21" xml:space="preserve" y="323.15" zvalue="1477">3116</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,847.926,332.341) scale(1,1) translate(0,0)" writing-mode="lr" x="847.9299999999999" xml:space="preserve" y="335.84" zvalue="1565">31160</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,935.857,268.714) scale(1,1) translate(0,0)" writing-mode="lr" x="935.86" xml:space="preserve" y="273.21" zvalue="1696">3118</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1097.96,238.472) scale(1,1) translate(0,0)" writing-mode="lr" x="1097.96" xml:space="preserve" y="242.97" zvalue="1759">对侧至六五河二级电站</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="148" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,820.134,225.51) scale(1,1) translate(0,0)" writing-mode="lr" x="820.13" xml:space="preserve" y="230.01" zvalue="1763">3119</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,740.076,223.214) scale(1,1) translate(0,0)" writing-mode="lr" x="740.08" xml:space="preserve" y="227.71" zvalue="1765">31197</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="12" id="147" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,892.389,126.778) scale(1,1) translate(0,0)" writing-mode="lr" x="892.39" xml:space="preserve" y="130.78" zvalue="1773">对侧到35kV富恒变电站</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,909.5,415) scale(1,1) translate(0,0)" writing-mode="lr" x="909.5" xml:space="preserve" y="419.5" zvalue="1776">311</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,929.75,530.25) scale(1,1) translate(0,0)" writing-mode="lr" x="929.75" xml:space="preserve" y="534.75" zvalue="1779">1B</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,974.5,531) scale(1,1) translate(0,0)" writing-mode="lr" x="974.5" xml:space="preserve" y="535.5" zvalue="1780">(5000kVA)</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,522.038,624.638) scale(1,1) translate(0,0)" writing-mode="lr" x="522.04" xml:space="preserve" y="629.14" zvalue="1784">0.4kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1173.27,542.032) scale(1,1) translate(0,0)" writing-mode="lr" x="1173.27" xml:space="preserve" y="546.53" zvalue="1787">4901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,677.438,651.465) scale(1,1) translate(0,0)" writing-mode="lr" x="677.4400000000001" xml:space="preserve" y="655.96" zvalue="1795">4111</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,685,715.091) scale(1,1) translate(0,0)" writing-mode="lr" x="685" xml:space="preserve" y="719.59" zvalue="1797">411</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,684.74,1042.7) scale(1,1) translate(0,0)" writing-mode="lr" x="684.74" xml:space="preserve" y="1047.2" zvalue="1853">1F</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,718.227,1041.21) scale(1,1) translate(0,0)" writing-mode="lr" x="718.23" xml:space="preserve" y="1045.71" zvalue="1862">(1250kW)</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,857.881,654.145) scale(1,1) translate(0,0)" writing-mode="lr" x="857.88" xml:space="preserve" y="658.65" zvalue="1870">4121</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,865.538,717.772) scale(1,1) translate(0,0)" writing-mode="lr" x="865.54" xml:space="preserve" y="722.27" zvalue="1872">412</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,866.356,1042.7) scale(1,1) translate(0,0)" writing-mode="lr" x="866.36" xml:space="preserve" y="1047.2" zvalue="1875">2F</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,898.115,1043.46) scale(1,1) translate(0,0)" writing-mode="lr" x="898.12" xml:space="preserve" y="1047.96" zvalue="1883">(1250kW)</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1038.22,651) scale(1,1) translate(0,0)" writing-mode="lr" x="1038.22" xml:space="preserve" y="655.5" zvalue="1888">4131</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1045.78,714.626) scale(1,1) translate(0,0)" writing-mode="lr" x="1045.78" xml:space="preserve" y="719.13" zvalue="1890">413</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="96" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1045.52,1042.7) scale(1,1) translate(0,0)" writing-mode="lr" x="1045.52" xml:space="preserve" y="1047.2" zvalue="1893">3F</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1078.01,1045.98) scale(1,1) translate(0,0)" writing-mode="lr" x="1078.01" xml:space="preserve" y="1050.48" zvalue="1897">(1250kW)</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="12" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,987.556,216.111) scale(1,1) translate(0,0)" writing-mode="lr" x="987.5599999999999" xml:space="preserve" y="220.11" zvalue="1915">35kV六五河二级电站T接线路</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,111.375,340.786) scale(1,1) translate(0,0)" writing-mode="lr" x="111.38" xml:space="preserve" y="346.79" zvalue="1918">图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,111.375,371.786) scale(1,1) translate(0,0)" writing-mode="lr" x="111.38" xml:space="preserve" y="377.79" zvalue="1919">修改日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,216.679,340.786) scale(1,1) translate(0,0)" writing-mode="lr" x="216.68" xml:space="preserve" y="346.79" zvalue="1920">六五河一级-01-2022</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,216.679,372.786) scale(1,1) translate(0,0)" writing-mode="lr" x="216.68" xml:space="preserve" y="378.79" zvalue="1921">2023-03-20</text>
 </g>
 <g id="ACLineSegmentClass">
  <g id="112">
   <use class="kv35" height="30" transform="rotate(0,885.307,188.125) scale(5.04226,1.35753) translate(-695.582,-44.1833)" width="7" x="867.6594337430042" xlink:href="#ACLineSegment:线路_0" y="167.7619047619048" zvalue="1474"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV六富线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,885.307,188.125) scale(5.04226,1.35753) translate(-695.582,-44.1833)" width="7" x="867.6594337430042" y="167.7619047619048"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="114">
   <use class="kv35" height="30" transform="rotate(0,886.543,319.556) scale(-1.11111,0.814815) translate(-1683.6,69.8485)" width="15" x="878.209817306549" xlink:href="#Disconnector:刀闸_0" y="307.3333333333333" zvalue="1476"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变35kV侧3116"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,886.543,319.556) scale(-1.11111,0.814815) translate(-1683.6,69.8485)" width="15" x="878.209817306549" y="307.3333333333333"/></g>
  <g id="123">
   <use class="kv35" height="30" transform="rotate(90,935.857,239) scale(1.42857,-1.04762) translate(-277.543,-466.422)" width="15" x="925.1428571428571" xlink:href="#Disconnector:刀闸_0" y="223.2857142857144" zvalue="1695"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV六富线3118"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,935.857,239) scale(1.42857,-1.04762) translate(-277.543,-466.422)" width="15" x="925.1428571428571" y="223.2857142857144"/></g>
  <g id="125">
   <use class="kv35" height="30" transform="rotate(180,796.615,221.361) scale(0.607889,-0.445785) translate(510.906,-726.238)" width="15" x="792.0562169506126" xlink:href="#Disconnector:刀闸_0" y="214.6740686289143" zvalue="1762"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV六富线3119"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,796.615,221.361) scale(0.607889,-0.445785) translate(510.906,-726.238)" width="15" x="792.0562169506126" y="214.6740686289143"/></g>
  <g id="118">
   <use class="v400" height="30" transform="rotate(180,1201.21,540.032) scale(1.11111,0.814815) translate(-119.288,119.957)" width="15" x="1192.876483973216" xlink:href="#Disconnector:刀闸_0" y="527.8095238095236" zvalue="1786"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0.4kV母线CT4901"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1201.21,540.032) scale(1.11111,0.814815) translate(-119.288,119.957)" width="15" x="1192.876483973216" y="527.8095238095236"/></g>
  <g id="22">
   <use class="v400" height="30" transform="rotate(0,706.21,652.465) scale(-1.11111,0.814815) translate(-1340.97,145.51)" width="15" x="697.8764839732157" xlink:href="#Disconnector:刀闸_0" y="640.242424242424" zvalue="1794"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1F4111"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,706.21,652.465) scale(-1.11111,0.814815) translate(-1340.97,145.51)" width="15" x="697.8764839732157" y="640.242424242424"/></g>
  <g id="92">
   <use class="v400" height="30" transform="rotate(0,886.652,655.145) scale(-1.11111,0.814815) translate(-1683.81,146.119)" width="15" x="878.3189764823617" xlink:href="#Disconnector:刀闸_0" y="642.9230769230769" zvalue="1869"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2F4121"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,886.652,655.145) scale(-1.11111,0.814815) translate(-1683.81,146.119)" width="15" x="878.3189764823617" y="642.9230769230769"/></g>
  <g id="109">
   <use class="v400" height="30" transform="rotate(0,1066.99,652) scale(-1.11111,0.814815) translate(-2026.44,145.404)" width="15" x="1058.654261750993" xlink:href="#Disconnector:刀闸_0" y="639.7777777777778" zvalue="1887"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3F4131"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1066.99,652) scale(-1.11111,0.814815) translate(-2026.44,145.404)" width="15" x="1058.654261750993" y="639.7777777777778"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="53">
   <use class="kv35" height="30" transform="rotate(90,849.26,344.089) scale(-0.763595,0.763595) translate(-1962.86,102.982)" width="12" x="844.6780903052907" xlink:href="#GroundDisconnector:地刀12_0" y="332.6348948311086" zvalue="1564"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变35kV侧31160"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,849.26,344.089) scale(-0.763595,0.763595) translate(-1962.86,102.982)" width="12" x="844.6780903052907" y="332.6348948311086"/></g>
  <g id="127">
   <use class="kv35" height="30" transform="rotate(0,776.22,222.25) scale(0.324926,0.324926) translate(1608.64,451.624)" width="12" x="774.2702032939787" xlink:href="#GroundDisconnector:地刀12_0" y="217.3757850061369" zvalue="1764"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV六富线31197"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,776.22,222.25) scale(0.324926,0.324926) translate(1608.64,451.624)" width="12" x="774.2702032939787" y="217.3757850061369"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="168">
   <use class="kv35" height="30" transform="rotate(90,1000,238.25) scale(1.5625,1.54167) translate(-356.625,-75.5845)" width="12" x="990.625" xlink:href="#EnergyConsumer:负荷_0" y="215.125" zvalue="1758"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="对侧至六五河二级电站"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1000,238.25) scale(1.5625,1.54167) translate(-356.625,-75.5845)" width="12" x="990.625" y="215.125"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="47">
   <path class="kv35" d="M 979.19 238.25 L 951.3 238.25" stroke-width="1" zvalue="1760"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="168@0" LinkObjectIDznd="123@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 979.19 238.25 L 951.3 238.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="kv35" d="M 774.03 196.23 L 774.03 202.71 L 821.22 202.71" stroke-width="1" zvalue="1769"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132@0" LinkObjectIDznd="131@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 774.03 196.23 L 774.03 202.71 L 821.22 202.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="136">
   <path class="kv35" d="M 796.56 214.9 L 795.53 202.71" stroke-width="1" zvalue="1770"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@0" LinkObjectIDznd="135" MaxPinNum="2"/>
   </metadata>
  <path d="M 796.56 214.9 L 795.53 202.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="144">
   <path class="kv35" d="M 776.22 217.6 L 776.22 203.52 L 795.6 203.52" stroke-width="1" zvalue="1772"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="127@0" LinkObjectIDznd="136" MaxPinNum="2"/>
   </metadata>
  <path d="M 776.22 217.6 L 776.22 203.52 L 795.6 203.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="v400" d="M 886.55 567.8 L 886.55 624" stroke-width="1" zvalue="1784"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@1" LinkObjectIDznd="115@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 886.55 567.8 L 886.55 624" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="12">
   <path class="v400" d="M 1201.11 551.85 L 1201.11 624" stroke-width="1" zvalue="1789"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@0" LinkObjectIDznd="115@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1201.11 551.85 L 1201.11 624" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="v400" d="M 1228.34 574.17 L 1201.11 574.17" stroke-width="1" zvalue="1790"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="3@0" LinkObjectIDznd="12" MaxPinNum="2"/>
   </metadata>
  <path d="M 1228.34 574.17 L 1201.11 574.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="21">
   <path class="v400" d="M 706.11 640.65 L 706.11 624" stroke-width="1" zvalue="1800"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="22@0" LinkObjectIDznd="115@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 706.11 640.65 L 706.11 624" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="v400" d="M 704.65 703.47 L 704.65 664.48" stroke-width="1" zvalue="1801"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="23@0" LinkObjectIDznd="22@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 704.65 703.47 L 704.65 664.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="v400" d="M 772.76 658.43 L 772.76 624" stroke-width="1" zvalue="1867"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="19@0" LinkObjectIDznd="115@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 772.76 658.43 L 772.76 624" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="v400" d="M 885.19 706.15 L 885.19 667.16" stroke-width="1" zvalue="1873"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@0" LinkObjectIDznd="92@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 885.19 706.15 L 885.19 667.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="v400" d="M 886.55 643.33 L 886.55 624" stroke-width="1" zvalue="1884"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@0" LinkObjectIDznd="115@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 886.55 643.33 L 886.55 624" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="v400" d="M 886.55 549.99 L 937.38 549.99 L 937.38 889.08 L 885.35 889.08" stroke-width="1" zvalue="1885"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@2" LinkObjectIDznd="68" MaxPinNum="2"/>
   </metadata>
  <path d="M 886.55 549.99 L 937.38 549.99 L 937.38 889.08 L 885.35 889.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="v400" d="M 1065.43 703 L 1065.43 664.01" stroke-width="1" zvalue="1891"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@0" LinkObjectIDznd="109@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1065.43 703 L 1065.43 664.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="v400" d="M 1066.89 640.18 L 1066.89 624" stroke-width="1" zvalue="1901"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@0" LinkObjectIDznd="115@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1066.89 640.18 L 1066.89 624" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="32">
   <path class="kv35" d="M 885.64 378.73 L 885.64 331.57" stroke-width="1" zvalue="1907"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="27@0" LinkObjectIDznd="114@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 885.64 378.73 L 885.64 331.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="kv35" d="M 886.45 307.74 L 886.45 208.28" stroke-width="1" zvalue="1908"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="112@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 886.45 307.74 L 886.45 208.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv35" d="M 860.2 344.1 L 885.64 344.1" stroke-width="1" zvalue="1909"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@0" LinkObjectIDznd="32" MaxPinNum="2"/>
   </metadata>
  <path d="M 860.2 344.1 L 885.64 344.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="37">
   <path class="kv35" d="M 920.66 239.13 L 886.45 239.13" stroke-width="1" zvalue="1910"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="123@0" LinkObjectIDznd="33" MaxPinNum="2"/>
   </metadata>
  <path d="M 920.66 239.13 L 886.45 239.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="kv35" d="M 796.58 227.93 L 796.58 248 L 886.45 248" stroke-width="1" zvalue="1911"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@1" LinkObjectIDznd="33" MaxPinNum="2"/>
   </metadata>
  <path d="M 796.58 227.93 L 796.58 248 L 886.45 248" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="v400" d="M 1201.14 528.02 L 1201.14 493.41" stroke-width="1" zvalue="1913"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@1" LinkObjectIDznd="5@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1201.14 528.02 L 1201.14 493.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv35" d="M 885.64 378.73 L 885.65 403.38" stroke-width="1" zvalue="1931"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="" LinkObjectIDznd="1@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 885.64 378.73 L 885.65 403.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="v400" d="M 885.19 729.39 L 885.19 830.91" stroke-width="1" zvalue="1936"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@1" LinkObjectIDznd="31@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 885.19 729.39 L 885.19 830.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="v400" d="M 1065.43 726.25 L 1065.35 830.91" stroke-width="1" zvalue="1940"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@1" LinkObjectIDznd="50@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1065.43 726.25 L 1065.35 830.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="v400" d="M 704.65 726.71 L 704.65 830.91" stroke-width="1" zvalue="1944"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="23@1" LinkObjectIDznd="2@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 704.65 726.71 L 704.65 830.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="v400" d="M 703.81 889.23 L 746.92 889.23 L 746.92 624" stroke-width="1" zvalue="1946"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67" LinkObjectIDznd="115@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 703.81 889.23 L 746.92 889.23 L 746.92 624" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="v400" d="M 1065.35 886.15 L 1106.92 886.15 L 1106.92 624" stroke-width="1" zvalue="1947"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70" LinkObjectIDznd="115@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1065.35 886.15 L 1106.92 886.15 L 1106.92 624" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="v400" d="M 703.81 856.71 L 703.81 976.22" stroke-width="1" zvalue="1948"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="2@1" LinkObjectIDznd="7@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 703.81 856.71 L 703.81 976.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="v400" d="M 885.35 856.71 L 885.35 916.47 L 886.73 916.47 L 886.73 976.22" stroke-width="1" zvalue="1949"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="31@1" LinkObjectIDznd="89@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 885.35 856.71 L 885.35 916.47 L 886.73 916.47 L 886.73 976.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="v400" d="M 886.43 907.79 L 885.35 907.79" stroke-width="1" zvalue="1950"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="88@0" LinkObjectIDznd="68" MaxPinNum="2"/>
   </metadata>
  <path d="M 886.43 907.79 L 885.35 907.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="v400" d="M 1065.35 856.71 L 1065.35 916.47 L 1065.89 916.47 L 1065.89 976.22" stroke-width="1" zvalue="1951"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@1" LinkObjectIDznd="106@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1065.35 856.71 L 1065.35 916.47 L 1065.89 916.47 L 1065.89 976.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="v400" d="M 1066.67 907.79 L 1065.35 907.79" stroke-width="1" zvalue="1952"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@0" LinkObjectIDznd="70" MaxPinNum="2"/>
   </metadata>
  <path d="M 1066.67 907.79 L 1065.35 907.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="v400" d="M 704.36 907.79 L 703.81 907.79" stroke-width="1" zvalue="1953"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="16@0" LinkObjectIDznd="67" MaxPinNum="2"/>
   </metadata>
  <path d="M 704.36 907.79 L 703.81 907.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv35" d="M 886.56 495.07 L 886.56 465.17" stroke-width="1" zvalue="1957"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@0" LinkObjectIDznd="73@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 886.56 495.07 L 886.56 465.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv35" d="M 886.12 439.37 L 886.12 426.62" stroke-width="1" zvalue="1958"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@0" LinkObjectIDznd="1@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 886.12 439.37 L 886.12 426.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="131">
   <use class="kv35" height="30" transform="rotate(0,821.161,184.903) scale(-1.21578,-1.21578) translate(-1493.34,-333.753)" width="30" x="802.9242183237984" xlink:href="#Accessory:三卷PT带容断器_0" y="166.6666666666668" zvalue="1766"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV六富线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,821.161,184.903) scale(-1.21578,-1.21578) translate(-1493.34,-333.753)" width="30" x="802.9242183237984" y="166.6666666666668"/></g>
  <g id="132">
   <use class="kv35" height="26" transform="rotate(0,774.015,189.65) scale(0.531903,-0.531903) translate(678.358,-552.285)" width="12" x="770.8235093266164" xlink:href="#Accessory:避雷器1_0" y="182.7351666081543" zvalue="1768"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV六富线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,774.015,189.65) scale(0.531903,-0.531903) translate(678.358,-552.285)" width="12" x="770.8235093266164" y="182.7351666081543"/></g>
  <g id="3">
   <use class="v400" height="30" transform="rotate(270,1241.23,575.054) scale(1.75833,1.75833) translate(-523.943,-236.634)" width="30" x="1214.857142857143" xlink:href="#Accessory:PT象达_0" y="548.6785714285717" zvalue="1788"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0.4kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1241.23,575.054) scale(1.75833,1.75833) translate(-523.943,-236.634)" width="30" x="1214.857142857143" y="548.6785714285717"/></g>
  <g id="27">
   <use class="kv35" height="30" transform="rotate(0,886,373) scale(1.43333,1.43333) translate(-261.36,-106.267)" width="30" x="864.5" xlink:href="#Accessory:电流互感器13_0" y="351.5" zvalue="1905"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1号主变CT2"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,886,373) scale(1.43333,1.43333) translate(-261.36,-106.267)" width="30" x="864.5" y="351.5"/></g>
  <g id="5">
   <use class="v400" height="30" transform="rotate(0,1200.68,477.589) scale(1.75833,1.75833) translate(-506.456,-194.6)" width="30" x="1174.309523809524" xlink:href="#Accessory:电流互感器14_0" y="451.2142857142858" zvalue="1912"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0.4kV母线CT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1200.68,477.589) scale(1.75833,1.75833) translate(-506.456,-194.6)" width="30" x="1174.309523809524" y="451.2142857142858"/></g>
  <g id="2">
   <use class="v400" height="30" transform="rotate(0,703.808,843.808) scale(1.43333,1.43333) translate(-206.279,-248.605)" width="30" x="682.3076923076924" xlink:href="#Accessory:电流互感器11_0" y="822.3076934814453" zvalue="1933"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1FCT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,703.808,843.808) scale(1.43333,1.43333) translate(-206.279,-248.605)" width="30" x="682.3076923076924" y="822.3076934814453"/></g>
  <g id="31">
   <use class="v400" height="30" transform="rotate(0,885.346,843.808) scale(1.43333,1.43333) translate(-261.163,-248.605)" width="30" x="863.8461538461538" xlink:href="#Accessory:电流互感器11_0" y="822.307693527295" zvalue="1935"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2FCT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,885.346,843.808) scale(1.43333,1.43333) translate(-261.163,-248.605)" width="30" x="863.8461538461538" y="822.307693527295"/></g>
  <g id="50">
   <use class="v400" height="30" transform="rotate(0,1065.35,843.808) scale(1.43333,1.43333) translate(-315.581,-248.605)" width="30" x="1043.846153846154" xlink:href="#Accessory:电流互感器11_0" y="822.3076933714059" zvalue="1939"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3FCT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1065.35,843.808) scale(1.43333,1.43333) translate(-315.581,-248.605)" width="30" x="1043.846153846154" y="822.3076933714059"/></g>
  <g id="73">
   <use class="kv35" height="30" transform="rotate(0,886.115,452.269) scale(1.43333,1.43333) translate(-261.395,-130.233)" width="30" x="864.6153846153846" xlink:href="#Accessory:电流互感器11_0" y="430.7692307692309" zvalue="1955"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1号主变CT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,886.115,452.269) scale(1.43333,1.43333) translate(-261.395,-130.233)" width="30" x="864.6153846153846" y="430.7692307692309"/></g>
 </g>
 <g id="BreakerClass">
  <g id="1">
   <use class="kv35" height="20" transform="rotate(0,885.75,415) scale(-1.25,1.25) translate(-1593.1,-80.5)" width="10" x="879.5" xlink:href="#Breaker:刀闸绘制规范_0" y="402.5" zvalue="1775"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变35kV侧311"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,885.75,415) scale(-1.25,1.25) translate(-1593.1,-80.5)" width="10" x="879.5" y="402.5"/></g>
  <g id="23">
   <use class="v400" height="20" transform="rotate(0,704.75,715.091) scale(-1.25,1.25) translate(-1267.3,-140.518)" width="10" x="698.5" xlink:href="#Breaker:刀闸绘制规范_0" y="702.5909090909091" zvalue="1796"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1F411"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,704.75,715.091) scale(-1.25,1.25) translate(-1267.3,-140.518)" width="10" x="698.5" y="702.5909090909091"/></g>
  <g id="91">
   <use class="v400" height="20" transform="rotate(0,885.288,717.772) scale(-1.25,1.25) translate(-1592.27,-141.054)" width="10" x="879.0384615384617" xlink:href="#Breaker:刀闸绘制规范_0" y="705.271561771562" zvalue="1871"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2F412"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,885.288,717.772) scale(-1.25,1.25) translate(-1592.27,-141.054)" width="10" x="879.0384615384617" y="705.271561771562"/></g>
  <g id="108">
   <use class="v400" height="20" transform="rotate(0,1065.53,714.626) scale(-1.25,1.25) translate(-1916.7,-140.425)" width="10" x="1059.277777777778" xlink:href="#Breaker:刀闸绘制规范_0" y="702.1262626262628" zvalue="1889"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3F413"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1065.53,714.626) scale(-1.25,1.25) translate(-1916.7,-140.425)" width="10" x="1059.277777777778" y="702.1262626262628"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="52">
   <g id="520">
    <use class="kv35" height="50" transform="rotate(0,886.55,531.25) scale(1.47,1.47) translate(-276.405,-158.105)" width="30" x="864.5" xlink:href="#PowerTransformer2:D-Y_0" y="494.5" zvalue="1778"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="35"/>
    </metadata>
   </g>
   <g id="521">
    <use class="v400" height="50" transform="rotate(0,886.55,531.25) scale(1.47,1.47) translate(-276.405,-158.105)" width="30" x="864.5" xlink:href="#PowerTransformer2:D-Y_1" y="494.5" zvalue="1778"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变"/>
   </metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,886.55,531.25) scale(1.47,1.47) translate(-276.405,-158.105)" width="30" x="864.5" y="494.5"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="115">
   <path class="v400" d="M 564.5 624 L 1304.5 624" stroke-width="6" zvalue="1783"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0.4kV母线"/>
   </metadata>
  <path d="M 564.5 624 L 1304.5 624" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundClass">
  <g id="19">
   <use class="v400" height="18" transform="rotate(0,772.758,668.105) scale(1.10567,1.10567) translate(-73.2205,-62.9016)" width="12" x="766.1235431235431" xlink:href="#Ground:大地_0" y="658.1538461538462" zvalue="1791"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0.4kV母线接地"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,772.758,668.105) scale(1.10567,1.10567) translate(-73.2205,-62.9016)" width="12" x="766.1235431235431" y="658.1538461538462"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="7">
   <use class="v400" height="30" transform="rotate(0,705.115,981.641) scale(2.70833,2.70833) translate(-419.14,-593.564)" width="30" x="664.4902122966641" xlink:href="#Generator:发电机12_0" y="941.015747070312" zvalue="1852"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1F"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,705.115,981.641) scale(2.70833,2.70833) translate(-419.14,-593.564)" width="30" x="664.4902122966641" y="941.015747070312"/></g>
  <g id="89">
   <use class="v400" height="30" transform="rotate(0,886.731,981.641) scale(2.70833,2.70833) translate(-533.697,-593.564)" width="30" x="846.1055969120487" xlink:href="#Generator:发电机12_0" y="941.0157470283534" zvalue="1874"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2F"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,886.731,981.641) scale(2.70833,2.70833) translate(-533.697,-593.564)" width="30" x="846.1055969120487" y="941.0157470283534"/></g>
  <g id="106">
   <use class="v400" height="30" transform="rotate(0,1065.89,981.641) scale(2.70833,2.70833) translate(-646.708,-593.564)" width="30" x="1025.267990074442" xlink:href="#Generator:发电机12_0" y="941.0157470426175" zvalue="1892"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3F"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1065.89,981.641) scale(2.70833,2.70833) translate(-646.708,-593.564)" width="30" x="1025.267990074442" y="941.0157470426175"/></g>
 </g>
 <g id="FuseClass">
  <g id="16">
   <use class="v400" height="30" transform="rotate(0,711.812,920.838) scale(1.24242,1.24242) translate(-135.254,-176.039)" width="30" x="693.1758183572698" xlink:href="#Fuse:融丝1_0" y="902.2020152698861" zvalue="1855"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1F电缆"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,711.812,920.838) scale(1.24242,1.24242) translate(-135.254,-176.039)" width="30" x="693.1758183572698" y="902.2020152698861"/></g>
  <g id="88">
   <use class="v400" height="30" transform="rotate(0,893.889,920.838) scale(1.24242,1.24242) translate(-170.781,-176.039)" width="30" x="875.2527414341932" xlink:href="#Fuse:融丝1_0" y="902.2020152833638" zvalue="1876"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2F电缆"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,893.889,920.838) scale(1.24242,1.24242) translate(-170.781,-176.039)" width="30" x="875.2527414341932" y="902.2020152833638"/></g>
  <g id="105">
   <use class="v400" height="30" transform="rotate(0,1074.13,920.838) scale(1.24242,1.24242) translate(-205.95,-176.039)" width="30" x="1055.492057673509" xlink:href="#Fuse:融丝1_0" y="902.2020152747031" zvalue="1894"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3F电缆"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1074.13,920.838) scale(1.24242,1.24242) translate(-205.95,-176.039)" width="30" x="1055.492057673509" y="902.2020152747031"/></g>
 </g>
</svg>