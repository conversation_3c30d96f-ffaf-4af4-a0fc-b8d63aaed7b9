<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1200" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(255,255,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.v400{stroke:rgb(0,255,0);fill:none}
.v380{stroke:rgb(0,255,0);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.16666666666667" x2="7.583333333333333" y1="6.416666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.1666666666666661" x2="5.916666666666666" y1="3.666666666666666" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5" x2="10.25" y1="4.583333333333332" y2="20.5"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_3" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="5.166666666666668" y2="21"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.999999999999998" y2="20.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_4" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="6.166666666666668" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="5.999999999999998" y2="21.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="14.07232884821164" y2="14.07232884821164"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Compensator:串联电阻_0" viewBox="0,0,15,22">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="5.25"/>
   <use terminal-index="1" type="0" x="7.5" xlink:href="#terminal" y="16.25"/>
   <path d="M 7.71392 9.13493 A 2.96392 1.81747 0 0 0 7.71392 5.5" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 7.71392 12.7699 A 2.96392 1.81747 0 0 0 7.71392 9.13493" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 7.71392 16.3048 A 2.96392 1.81747 0 0 0 7.71392 12.6699" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Accessory:220kV线路PT_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="12.90667828048158" xlink:href="#terminal" y="37.28457520218116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27" x2="13" y1="26.75" y2="26.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.08333333333334" x2="29.08333333333334" y1="14.5" y2="14.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27" x2="27" y1="22.75" y2="26.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.93916361389983" x2="12.93916361389983" y1="33.96630546659506" y2="37.26790853551449"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.93916361389983" x2="12.93916361389983" y1="30.79012345679012" y2="22.71704491718448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.91963465760507" x2="12.91963465760507" y1="19.74560215515698" y2="13.06430394728183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.01963465760507" x2="13.01963465760507" y1="22.75831495554599" y2="22.75831495554599"/>
   <ellipse cx="23.37" cy="20.6" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.556517488649495" x2="16.39655620359946" y1="22.67498162221265" y2="22.67498162221265"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.01963465760507" x2="13.01963465760507" y1="33.90122531314907" y2="33.90122531314907"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.639850821982819" x2="16.47988953693279" y1="19.66306207843401" y2="19.66306207843401"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.639850821982824" x2="16.47988953693279" y1="33.90122531314905" y2="33.90122531314905"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.639850821982824" x2="16.47988953693279" y1="30.80597243603708" y2="30.80597243603708"/>
   <ellipse cx="26.96" cy="14.43" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="10.72318415531615" x2="15" y1="12.99639541176734" y2="12.99639541176734"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.47318415531615" x2="14.08333333333333" y1="11.74639541176734" y2="11.74639541176734"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.97318415531615" x2="13.41666666666667" y1="10.24639541176734" y2="10.24639541176734"/>
   <ellipse cx="30.62" cy="20.6" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.25" x2="25.25" y1="20.5" y2="20.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.08333333333334" x2="33.08333333333334" y1="20.41666666666667" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Breaker:刀闸绘制规范_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.077060931899643" xlink:href="#terminal" y="0.7027600849256537"/>
   <use terminal-index="1" type="0" x="5.077060931899643" xlink:href="#terminal" y="19.29723991507431"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.835125448028674" x2="6.249999999999999" y1="2.796178343949066" y2="4.704883227176235"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.077060931899643" x2="5.077060931899643" y1="12.58598726114649" y2="19.23566878980892"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.077060931899643" x2="0.333333333333333" y1="12.5859872611465" y2="5.083333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.077060931899643" x2="5.077060931899643" y1="3.658174097664537" y2="0.7027600849256821"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.249999999999999" x2="3.835125448028674" y1="2.796178343949066" y2="4.704883227176235"/>
  </symbol>
  <symbol id="Breaker:刀闸绘制规范_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.077060931899643" xlink:href="#terminal" y="0.7027600849256537"/>
   <use terminal-index="1" type="0" x="5.077060931899643" xlink:href="#terminal" y="19.29723991507431"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="1" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="8" y1="4" y2="4"/>
  </symbol>
  <symbol id="Breaker:刀闸绘制规范_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.077060931899643" xlink:href="#terminal" y="0.7027600849256537"/>
   <use terminal-index="1" type="0" x="5.077060931899643" xlink:href="#terminal" y="19.29723991507431"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.077060931899643" x2="5.077060931899643" y1="3.658174097664537" y2="0.7027600849256821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.077060931899643" x2="5.077060931899643" y1="12.58598726114649" y2="19.23566878980892"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.835125448028674" x2="6.249999999999999" y1="2.796178343949066" y2="4.704883227176235"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.249999999999999" x2="3.835125448028674" y1="2.796178343949066" y2="4.704883227176235"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8" x2="1.666666666666666" y1="13.5" y2="2.749999999999997"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.666666666666666" x2="8" y1="14" y2="3.249999999999998"/>
  </symbol>
  <symbol id="Accessory:三卷PT带容断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,6.5) scale(1,1) translate(0,0)" width="4" x="13" y="3"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
  </symbol>
  <symbol id="GroundDisconnector:12547_0" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="14.5" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.77607149197439" x2="33.45507703101916" y1="38.59219204932567" y2="38.59219204932567"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.96414179204035" x2="14.96414179204035" y1="19.56938379593636" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.53290622076525" x2="33.53290622076525" y1="22.06726518291196" y2="38.52333818738227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.72817975850747" x2="33.72817975850747" y1="18.16909267494318" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.416666666666625" x2="14.8889734851558" y1="30.74450652239035" y2="19.51308960898588"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.84076764320101" x2="14.84076764320101" y1="32.44927516103209" y2="38.6666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.85395107780631" x2="12.1757277082614" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.61798904427344" x2="30.93976567472853" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.6532657596445" x2="12.86778351328125" y1="7.44637056159085" y2="7.44637056159085"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.41730372611165" x2="31.6318214797484" y1="7.446370561590845" y2="7.446370561590845"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.00950311944433" x2="14.2744904231945" y1="6.499999999999968" y2="6.499999999999968"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.77354108591146" x2="33.03852838966164" y1="6.499999999999968" y2="6.499999999999968"/>
   <rect fill-opacity="0" height="14.22" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,33.51,25.24) scale(-1,1) translate(-2360.48,0)" width="8.140000000000001" x="29.44" y="18.13"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
  </symbol>
  <symbol id="GroundDisconnector:12547_1" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="14.5" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.53290622076525" x2="33.53290622076525" y1="22.06726518291196" y2="38.52333818738227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.77607149197439" x2="33.45507703101916" y1="38.59219204932567" y2="38.59219204932567"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.96414179204035" x2="14.96414179204035" y1="19.56938379593636" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.72817975850747" x2="33.72817975850747" y1="18.16909267494318" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.8889734851558" x2="14.8889734851558" y1="32.25" y2="19.51308960898588"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.84076764320101" x2="14.84076764320101" y1="32.44927516103209" y2="38.6666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.85395107780631" x2="12.1757277082614" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.61798904427344" x2="30.93976567472853" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.6532657596445" x2="12.86778351328125" y1="7.44637056159085" y2="7.44637056159085"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.41730372611165" x2="31.6318214797484" y1="7.446370561590845" y2="7.446370561590845"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.00950311944433" x2="14.2744904231945" y1="6.499999999999968" y2="6.499999999999968"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.77354108591146" x2="33.03852838966164" y1="6.499999999999968" y2="6.499999999999968"/>
   <rect fill-opacity="0" height="14.22" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,33.51,25.24) scale(-1,1) translate(-2360.48,0)" width="8.140000000000001" x="29.44" y="18.13"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
  </symbol>
  <symbol id="GroundDisconnector:12547_2" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="14.5" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.88620689655174" x2="9.700000000000015" y1="19.49501474926254" y2="32.31404129793511"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="9.699999999999994" x2="20.24999999999999" y1="19.33333333333333" y2="32.38333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.77607149197439" x2="33.45507703101916" y1="38.59219204932567" y2="38.59219204932567"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.53290622076525" x2="33.53290622076525" y1="22.06726518291196" y2="38.52333818738227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.96414179204035" x2="14.96414179204035" y1="19.56938379593636" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.72817975850747" x2="33.72817975850747" y1="18.16909267494318" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.84076764320101" x2="14.84076764320101" y1="32.44927516103209" y2="38.6666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.85395107780631" x2="12.1757277082614" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.61798904427344" x2="30.93976567472853" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.6532657596445" x2="12.86778351328125" y1="7.44637056159085" y2="7.44637056159085"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.41730372611165" x2="31.6318214797484" y1="7.446370561590845" y2="7.446370561590845"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.00950311944433" x2="14.2744904231945" y1="6.499999999999968" y2="6.499999999999968"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.77354108591146" x2="33.03852838966164" y1="6.499999999999968" y2="6.499999999999968"/>
   <rect fill-opacity="0" height="14.22" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,33.51,25.24) scale(-1,1) translate(-2360.48,0)" width="8.140000000000001" x="29.44" y="18.13"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.027974625923065" x2="10.46799242340996" y1="19.93170597265525" y2="19.93170597265525"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="12.0194189818494"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="5.416666666666668" y2="12.02321291373541"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.92126160277786" x2="6.666666666666666" y1="12.10207526123773" y2="18.50000000000001"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.18646053143751" x2="24.5" y1="12.18904818695178" y2="19.00000000000001"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 7.33333 43.5833 L 23 43.5833 L 15 31.5 z" fill-opacity="0" stroke="rgb(122,122,122)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(122,122,122)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸3_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="4"/>
   <use terminal-index="1" type="0" x="7.5" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="7" y2="23"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.418004115226339" x2="1.959670781893005" y1="3.763815276003976" y2="8.574160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="6.686947459912011" y2="11.49729228749822"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="6.686947459912011" y2="11.49729228749822"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="3.680481942670642" y2="8.490826770256849"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="23.52315435646374" y2="18.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="26.32918883922238" y2="21.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="23.52315435646374" y2="18.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="26.32918883922238" y2="21.51884401163617"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸3_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="4"/>
   <use terminal-index="1" type="0" x="7.5" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="3.5" y1="23" y2="11"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.418004115226339" x2="1.959670781893005" y1="3.763815276003976" y2="8.574160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="6.686947459912011" y2="11.49729228749822"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="6.686947459912011" y2="11.49729228749822"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="3.680481942670642" y2="8.490826770256849"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="23.52315435646374" y2="18.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="26.32918883922238" y2="21.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="23.52315435646374" y2="18.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="26.32918883922238" y2="21.51884401163617"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸3_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="4"/>
   <use terminal-index="1" type="0" x="7.5" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.66666666666667" x2="4.666666666666667" y1="9.833333333333334" y2="20.83333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.416666666666667" x2="10.41666666666667" y1="9.5" y2="20.5"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.418004115226339" x2="1.959670781893005" y1="3.763815276003976" y2="8.574160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="6.686947459912011" y2="11.49729228749822"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="6.686947459912011" y2="11.49729228749822"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="3.680481942670642" y2="8.490826770256849"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="23.52315435646374" y2="18.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.501337448559672" x2="2.043004115226339" y1="26.32918883922238" y2="21.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="23.52315435646374" y2="18.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.50133744855967" x2="12.959670781893" y1="26.32918883922238" y2="21.51884401163617"/>
  </symbol>
  <symbol id="EnergyConsumer:厂站用变_0" viewBox="0,0,15,25">
   <use terminal-index="0" type="0" x="5.916666666666666" xlink:href="#terminal" y="1.58333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.41666666666667" x2="5.916666666666666" y1="17.16666666666666" y2="20.33333333333333"/>
   <ellipse cx="5.8" cy="5.8" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.86" cy="11.79" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.926184092940137" x2="5.926184092940137" y1="10.47013412501683" y2="12.14189293057399"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.606255585344062" x2="5.926184092940133" y1="13.81365173613114" y2="12.14189293057398"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.246112600536196" x2="5.926184092940125" y1="13.81365173613114" y2="12.14189293057398"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.926184092940137" x2="13.41666666666667" y1="12.14189293057399" y2="12.14189293057399"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.41657730116176" x2="13.41657730116176" y1="12.16666666666666" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.009517426273471" x2="6.009517426273471" y1="3.303467458350164" y2="4.975226263907325"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.689588918677395" x2="6.009517426273467" y1="6.64698506946448" y2="4.975226263907319"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.246112600536196" x2="5.926184092940125" y1="6.563651736131146" y2="4.891892930573985"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.914160137763698" x2="5.914160137763698" y1="22.49434593889296" y2="15.91678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.906472232006561" x2="4.753286368439581" y1="24.43333333333333" y2="22.50505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.906472232006569" x2="7.05965809557355" y1="24.43333333333333" y2="22.50505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.753286368439605" x2="7.059658095573566" y1="22.50505857643125" y2="22.50505857643125"/>
  </symbol>
  <symbol id="DollyBreaker:手车_0" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="0.3833333333333346" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="21.8" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.0246913580247" x2="0.3833333333333346" y1="11.2962962962963" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.07407407407407" x2="21.53229166666667" y1="11.2962962962963" y2="21.41666666666666"/>
  </symbol>
  <symbol id="DollyBreaker:手车_1" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="21" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="1" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="11" y1="1" y2="11"/>
  </symbol>
  <symbol id="DollyBreaker:手车_2" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <path d="M 3.6066 1.05 L 18.5833 9.95" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 18.4234 1 L 3.5 10" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
  <symbol id="Accessory:线路PT带避雷器0904_0" viewBox="0,0,20,40">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="12.91666666666667" y1="23.75" y2="23.75"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,11) scale(1,1) translate(0,0)" width="6" x="7" y="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.988213005145578" x2="9.988213005145578" y1="1.029523490692871" y2="18.75"/>
   <ellipse cx="9.76" cy="32.23" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.76" cy="24.17" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="12.91666666666667" y1="32.91666666666667" y2="32.91666666666667"/>
  </symbol>
  <symbol id="Accessory:RT1122_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="22.91666666666667" y2="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="18.16666666666666" y2="20.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="18.16666666666666" y2="20.16666666666666"/>
   <path d="M 13 11 L 17 11 L 15 8 L 13 11 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="15.41666666666666" y2="18.16666666666666"/>
   <ellipse cx="14.95" cy="18.02" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.95" cy="9.92" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:厂站用变DY_0" viewBox="0,0,15,25">
   <use terminal-index="0" type="0" x="6.25" xlink:href="#terminal" y="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.91666666666667" x2="6.416666666666666" y1="16.66666666666666" y2="19.83333333333333"/>
   <ellipse cx="6.3" cy="5.3" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.36" cy="11.29" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.426184092940137" x2="6.426184092940137" y1="9.97013412501683" y2="11.64189293057399"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.106255585344062" x2="6.426184092940133" y1="13.31365173613114" y2="11.64189293057398"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.746112600536196" x2="6.426184092940125" y1="13.31365173613114" y2="11.64189293057398"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.426184092940137" x2="13.91666666666667" y1="11.64189293057399" y2="11.64189293057399"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.91657730116176" x2="13.91657730116176" y1="11.66666666666666" y2="16.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.414160137763698" x2="6.414160137763698" y1="21.99434593889296" y2="15.41678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.406472232006561" x2="5.253286368439581" y1="23.93333333333333" y2="22.00505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.40647223200657" x2="7.55965809557355" y1="23.93333333333333" y2="22.00505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.253286368439605" x2="7.559658095573566" y1="22.00505857643125" y2="22.00505857643125"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.265896357419217" x2="8.628056755122467" y1="1.734555672949975" y2="6.528558961992624"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.903735959715972" x2="8.628056755122469" y1="6.528558961992625" y2="6.528558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.265896357419203" x2="3.903735959715955" y1="1.734555672949975" y2="6.528558961992624"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="徐村电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass"/>
 <g id="OtherClass">
  
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="48" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,188,91.2321) scale(1,1) translate(0,0)" writing-mode="lr" x="188" xml:space="preserve" y="108.23" zvalue="3761">      徐村电站</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,322.794,469.77) scale(1,1) translate(0,0)" writing-mode="lr" x="322.79" xml:space="preserve" y="474.27" zvalue="3274">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,870.162,451.784) scale(1,1) translate(0,0)" writing-mode="lr" x="870.16" xml:space="preserve" y="456.28" zvalue="3492">1321</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="233" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,893.136,162.476) scale(1,1) translate(0,0)" writing-mode="lr" x="893.14" xml:space="preserve" y="166.98" zvalue="3496">110kV徐下Ⅱ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="232" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,927.44,275.636) scale(1,1) translate(0,0)" writing-mode="lr" x="927.4400000000001" xml:space="preserve" y="280.14" zvalue="3509">13267</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="231" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,929.172,334.524) scale(1,1) translate(0,0)" writing-mode="lr" x="929.17" xml:space="preserve" y="339.02" zvalue="3512">13260</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="230" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,872.93,387.254) scale(1,1) translate(0,0)" writing-mode="lr" x="872.9299999999999" xml:space="preserve" y="391.75" zvalue="3514">132</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="229" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,869.012,322.895) scale(1,1) translate(0,0)" writing-mode="lr" x="869.01" xml:space="preserve" y="327.4" zvalue="3516">1326</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="228" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,929.422,393.413) scale(1,1) translate(0,0)" writing-mode="lr" x="929.42" xml:space="preserve" y="397.91" zvalue="3518">13217</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1447.53,431.784) scale(1,1) translate(0,0)" writing-mode="lr" x="1447.53" xml:space="preserve" y="436.28" zvalue="3526">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1468.62,470.08) scale(1,1) translate(0,0)" writing-mode="lr" x="1468.62" xml:space="preserve" y="474.58" zvalue="3528">19010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1468.35,341.373) scale(1,1) translate(0,0)" writing-mode="lr" x="1468.35" xml:space="preserve" y="345.87" zvalue="3531">19017</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,589.571,650.31) scale(1,1) translate(0,0)" writing-mode="lr" x="589.5700000000001" xml:space="preserve" y="654.8099999999999" zvalue="3537">1号主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,399.698,658.21) scale(1,1) translate(0,0)" writing-mode="lr" x="399.7" xml:space="preserve" y="662.71" zvalue="3538">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,505.93,575.254) scale(1,1) translate(0,0)" writing-mode="lr" x="505.93" xml:space="preserve" y="579.75" zvalue="3542">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,503.039,518.895) scale(1,1) translate(0,0)" writing-mode="lr" x="503.04" xml:space="preserve" y="523.4" zvalue="3544">1011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,558.422,529.413) scale(1,1) translate(0,0)" writing-mode="lr" x="558.42" xml:space="preserve" y="533.91" zvalue="3546">10117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,590.794,665.032) scale(1,1) translate(0,0)" writing-mode="lr" x="590.79" xml:space="preserve" y="669.03" zvalue="3550">(31.5MVA)</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,955.571,650.31) scale(1,1) translate(0,0)" writing-mode="lr" x="955.5700000000001" xml:space="preserve" y="654.8099999999999" zvalue="3553">2号主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,765.698,658.21) scale(1,1) translate(0,0)" writing-mode="lr" x="765.7" xml:space="preserve" y="662.71" zvalue="3555">1020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,871.93,575.254) scale(1,1) translate(0,0)" writing-mode="lr" x="871.9299999999999" xml:space="preserve" y="579.75" zvalue="3558">102</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,869.039,518.895) scale(1,1) translate(0,0)" writing-mode="lr" x="869.04" xml:space="preserve" y="523.4" zvalue="3560">1021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,924.422,529.413) scale(1,1) translate(0,0)" writing-mode="lr" x="924.42" xml:space="preserve" y="533.91" zvalue="3562">10217</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,956.794,665.032) scale(1,1) translate(0,0)" writing-mode="lr" x="956.79" xml:space="preserve" y="669.03" zvalue="3566">(31.5MVA)</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1485.57,650.31) scale(1,1) translate(0,0)" writing-mode="lr" x="1485.57" xml:space="preserve" y="654.8099999999999" zvalue="3570">3号主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1295.7,658.21) scale(1,1) translate(0,0)" writing-mode="lr" x="1295.7" xml:space="preserve" y="662.71" zvalue="3572">1030</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1401.93,575.254) scale(1,1) translate(0,0)" writing-mode="lr" x="1401.93" xml:space="preserve" y="579.75" zvalue="3575">103</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1399.04,518.895) scale(1,1) translate(0,0)" writing-mode="lr" x="1399.04" xml:space="preserve" y="523.4" zvalue="3577">1031</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1454.42,529.413) scale(1,1) translate(0,0)" writing-mode="lr" x="1454.42" xml:space="preserve" y="533.91" zvalue="3579">10317</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="12" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1486.79,665.032) scale(1,1) translate(0,0)" writing-mode="lr" x="1486.79" xml:space="preserve" y="669.03" zvalue="3583">(31.5MVA)</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="138" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,112.683,829.476) scale(1,1) translate(0,0)" writing-mode="lr" x="112.68" xml:space="preserve" y="833.98" zvalue="3586">074</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="137" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,159.662,882.08) scale(1,1) translate(0,0)" writing-mode="lr" x="159.66" xml:space="preserve" y="886.58" zvalue="3588">07467</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="136" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,130.016,1001.53) scale(1,1) translate(0,0)" writing-mode="lr" x="130.02" xml:space="preserve" y="1006.03" zvalue="3589">1号厂用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="139" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,262.794,838.587) scale(1,1) translate(0,0)" writing-mode="lr" x="262.79" xml:space="preserve" y="843.09" zvalue="3590">0713</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="140" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,339.238,829.476) scale(1,1) translate(0,0)" writing-mode="lr" x="339.24" xml:space="preserve" y="833.98" zvalue="3593">071</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="141" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,324.645,916.733) scale(1,1) translate(0,0)" writing-mode="lr" x="324.64" xml:space="preserve" y="921.23" zvalue="3595">07167</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,305.238,1038.64) scale(1,1) translate(0,0)" writing-mode="lr" x="305.24" xml:space="preserve" y="1043.14" zvalue="3597">1号发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="144" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,418.877,937.476) scale(1,1) translate(0,0)" writing-mode="lr" x="418.88" xml:space="preserve" y="941.98" zvalue="3599">0711</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="145" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,552.127,935.254) scale(1,1) translate(0,0)" writing-mode="lr" x="552.13" xml:space="preserve" y="939.75" zvalue="3603">0712</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,631.683,1095.25) scale(1,1) translate(0,0)" writing-mode="lr" x="631.6799999999999" xml:space="preserve" y="1099.75" zvalue="3606">1号励磁变压器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="148" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,632.683,829.476) scale(1,1) translate(0,0)" writing-mode="lr" x="632.6799999999999" xml:space="preserve" y="833.98" zvalue="3608">075</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,686.561,878.302) scale(1,1) translate(0,0)" writing-mode="lr" x="686.5599999999999" xml:space="preserve" y="882.8" zvalue="3610">07567</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="147" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,668.738,1000.59) scale(1,1) translate(0,0)" writing-mode="lr" x="668.74" xml:space="preserve" y="1005.09" zvalue="3612">2号厂用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,368.571,1038.59) scale(1,1) translate(0,0)" writing-mode="lr" x="368.57" xml:space="preserve" y="1043.09" zvalue="3663">(28.6MW)</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="162" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,718.683,829.476) scale(1,1) translate(0,0)" writing-mode="lr" x="718.6799999999999" xml:space="preserve" y="833.98" zvalue="3666">076</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="161" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,765.662,882.08) scale(1,1) translate(0,0)" writing-mode="lr" x="765.66" xml:space="preserve" y="886.58" zvalue="3668">07667</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,736.016,1001.53) scale(1,1) translate(0,0)" writing-mode="lr" x="736.02" xml:space="preserve" y="1006.03" zvalue="3670">3号厂用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="159" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,868.794,838.587) scale(1,1) translate(0,0)" writing-mode="lr" x="868.79" xml:space="preserve" y="843.09" zvalue="3672">0723</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,945.238,829.476) scale(1,1) translate(0,0)" writing-mode="lr" x="945.24" xml:space="preserve" y="833.98" zvalue="3675">072</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="157" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,929.719,916.733) scale(1,1) translate(0,0)" writing-mode="lr" x="929.72" xml:space="preserve" y="921.23" zvalue="3677">07267</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,911.238,1038.64) scale(1,1) translate(0,0)" writing-mode="lr" x="911.24" xml:space="preserve" y="1043.14" zvalue="3679">2号发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="155" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1020.39,937.476) scale(1,1) translate(0,0)" writing-mode="lr" x="1020.39" xml:space="preserve" y="941.98" zvalue="3681">0721</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1158.13,935.254) scale(1,1) translate(0,0)" writing-mode="lr" x="1158.13" xml:space="preserve" y="939.75" zvalue="3684">0722</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1237.68,1095.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1237.68" xml:space="preserve" y="1099.75" zvalue="3688">2号励磁变压器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="163" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,974.571,1038.59) scale(1,1) translate(0,0)" writing-mode="lr" x="974.5700000000001" xml:space="preserve" y="1043.09" zvalue="3711">(28.6MW)</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="268" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1308.68,829.476) scale(1,1) translate(0,0)" writing-mode="lr" x="1308.68" xml:space="preserve" y="833.98" zvalue="3719">077</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="267" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1355.66,882.08) scale(1,1) translate(0,0)" writing-mode="lr" x="1355.66" xml:space="preserve" y="886.58" zvalue="3721">07767</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="266" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1326.02,1001.53) scale(1,1) translate(0,0)" writing-mode="lr" x="1326.02" xml:space="preserve" y="1006.03" zvalue="3723">4号厂用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="265" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1458.79,838.587) scale(1,1) translate(0,0)" writing-mode="lr" x="1458.79" xml:space="preserve" y="843.09" zvalue="3725">0733</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="264" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1535.24,829.476) scale(1,1) translate(0,0)" writing-mode="lr" x="1535.24" xml:space="preserve" y="833.98" zvalue="3728">073</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="263" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1519.72,916.733) scale(1,1) translate(0,0)" writing-mode="lr" x="1519.72" xml:space="preserve" y="921.23" zvalue="3730">07367</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="215" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1501.24,1038.64) scale(1,1) translate(0,0)" writing-mode="lr" x="1501.24" xml:space="preserve" y="1043.14" zvalue="3732">3号发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="214" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1610.39,937.476) scale(1,1) translate(0,0)" writing-mode="lr" x="1610.39" xml:space="preserve" y="941.98" zvalue="3734">0731</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="210" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1748.13,935.254) scale(1,1) translate(0,0)" writing-mode="lr" x="1748.13" xml:space="preserve" y="939.75" zvalue="3737">0732</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="209" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1827.68,1095.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1827.68" xml:space="preserve" y="1099.75" zvalue="3741">3号励磁变压器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="271" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1564.57,1038.59) scale(1,1) translate(0,0)" writing-mode="lr" x="1564.57" xml:space="preserve" y="1043.09" zvalue="3753">(28.6MW)</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="101" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,194.208,207.905) scale(1,1) translate(0,0)" writing-mode="lr" x="194.21" xml:space="preserve" y="213.9" zvalue="3759">图号         徐村电站-02-2023</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,178.75,238.738) scale(1,1) translate(0,0)" writing-mode="lr" x="178.75" xml:space="preserve" y="244.74" zvalue="3760">修改日期    2023-05-17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="19" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,921,204) scale(1,1) translate(0,0)" writing-mode="lr" x="921" xml:space="preserve" y="210.5" zvalue="3763">A</text>
 </g>
 <g id="BusbarSectionClass">
  <g id="18">
   <path class="kv110" d="M 290.27 487.44 L 1806.9 487.44" stroke-width="6" zvalue="3273"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110kV母线"/>
   </metadata>
  <path d="M 290.27 487.44 L 1806.9 487.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="259">
   <use class="kv110" height="30" transform="rotate(0,894.298,452.784) scale(0.831307,-0.609625) translate(180.21,-1201.37)" width="15" x="888.0629900778581" xlink:href="#Disconnector:刀闸_0" y="443.6398257300967" zvalue="3491"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110kV徐下Ⅱ回线1321"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,894.298,452.784) scale(0.831307,-0.609625) translate(180.21,-1201.37)" width="15" x="888.0629900778581" y="443.6398257300967"/></g>
  <g id="240">
   <use class="kv110" height="30" transform="rotate(0,894.298,323.895) scale(0.831307,0.609625) translate(180.21,201.551)" width="15" x="888.0629900778581" xlink:href="#Disconnector:刀闸_0" y="314.750936841208" zvalue="3515"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110kV徐下Ⅱ回线1326"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,894.298,323.895) scale(0.831307,0.609625) translate(180.21,201.551)" width="15" x="888.0629900778581" y="314.750936841208"/></g>
  <g id="261">
   <use class="kv110" height="30" transform="rotate(0,1424.3,432.784) scale(0.831307,-0.609625) translate(287.76,-1148.56)" width="15" x="1418.062990077858" xlink:href="#Disconnector:刀闸_0" y="423.6398257300967" zvalue="3525"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110kV母线电压互感器1901"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1424.3,432.784) scale(0.831307,-0.609625) translate(287.76,-1148.56)" width="15" x="1418.062990077858" y="423.6398257300967"/></g>
  <g id="22">
   <use class="kv110" height="30" transform="rotate(0,528.298,519.895) scale(0.831307,0.609625) translate(105.94,327.06)" width="15" x="522.0629900778581" xlink:href="#Disconnector:刀闸_0" y="510.750936841208" zvalue="3543"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1号主变110kV侧1011"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,528.298,519.895) scale(0.831307,0.609625) translate(105.94,327.06)" width="15" x="522.0629900778581" y="510.750936841208"/></g>
  <g id="44">
   <use class="kv110" height="30" transform="rotate(0,894.298,519.895) scale(0.831307,0.609625) translate(180.21,327.06)" width="15" x="888.0629900778581" xlink:href="#Disconnector:刀闸_0" y="510.750936841208" zvalue="3559"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2号主变110kV侧1021"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,894.298,519.895) scale(0.831307,0.609625) translate(180.21,327.06)" width="15" x="888.0629900778581" y="510.750936841208"/></g>
  <g id="60">
   <use class="kv110" height="30" transform="rotate(0,1424.3,519.895) scale(0.831307,0.609625) translate(287.76,327.06)" width="15" x="1418.062990077858" xlink:href="#Disconnector:刀闸_0" y="510.750936841208" zvalue="3576"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#3号主变110kV侧1031"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1424.3,519.895) scale(0.831307,0.609625) translate(287.76,327.06)" width="15" x="1418.062990077858" y="510.750936841208"/></g>
  <g id="66">
   <use class="kv0" height="30" transform="rotate(0,135.238,830.476) scale(1.11111,1.11111) translate(-12.6905,-81.381)" width="15" x="126.9047619047615" xlink:href="#Disconnector:联体手车刀闸3_0" y="813.8095238095237" zvalue="3585"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号厂用变074"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,135.238,830.476) scale(1.11111,1.11111) translate(-12.6905,-81.381)" width="15" x="126.9047619047615" y="813.8095238095237"/></g>
  <g id="71">
   <use class="kv0" height="30" transform="rotate(0,361.238,830.476) scale(1.11111,1.11111) translate(-35.2905,-81.381)" width="15" x="352.9047619047615" xlink:href="#Disconnector:联体手车刀闸3_0" y="813.8095238095237" zvalue="3592"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号发电机071"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,361.238,830.476) scale(1.11111,1.11111) translate(-35.2905,-81.381)" width="15" x="352.9047619047615" y="813.8095238095237"/></g>
  <g id="80">
   <use class="kv0" height="30" transform="rotate(0,655.238,830.476) scale(1.11111,1.11111) translate(-64.6905,-81.381)" width="15" x="646.9047619047615" xlink:href="#Disconnector:联体手车刀闸3_0" y="813.8095238095237" zvalue="3607"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2号厂用变075"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,655.238,830.476) scale(1.11111,1.11111) translate(-64.6905,-81.381)" width="15" x="646.9047619047615" y="813.8095238095237"/></g>
  <g id="203">
   <use class="kv0" height="30" transform="rotate(0,741.238,830.476) scale(1.11111,1.11111) translate(-73.2905,-81.381)" width="15" x="732.9047619047615" xlink:href="#Disconnector:联体手车刀闸3_0" y="813.8095238095237" zvalue="3665"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3号厂用变076"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,741.238,830.476) scale(1.11111,1.11111) translate(-73.2905,-81.381)" width="15" x="732.9047619047615" y="813.8095238095237"/></g>
  <g id="193">
   <use class="kv0" height="30" transform="rotate(0,967.238,830.476) scale(1.11111,1.11111) translate(-95.8905,-81.381)" width="15" x="958.9047619047615" xlink:href="#Disconnector:联体手车刀闸3_0" y="813.8095238095237" zvalue="3674"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2号发电机072"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,967.238,830.476) scale(1.11111,1.11111) translate(-95.8905,-81.381)" width="15" x="958.9047619047615" y="813.8095238095237"/></g>
  <g id="296">
   <use class="kv0" height="30" transform="rotate(0,1331.24,830.476) scale(1.11111,1.11111) translate(-132.29,-81.381)" width="15" x="1322.904761904761" xlink:href="#Disconnector:联体手车刀闸3_0" y="813.8095238095237" zvalue="3718"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="4号厂用变077"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1331.24,830.476) scale(1.11111,1.11111) translate(-132.29,-81.381)" width="15" x="1322.904761904761" y="813.8095238095237"/></g>
  <g id="291">
   <use class="kv0" height="30" transform="rotate(0,1557.24,830.476) scale(1.11111,1.11111) translate(-154.89,-81.381)" width="15" x="1548.904761904761" xlink:href="#Disconnector:联体手车刀闸3_0" y="813.8095238095237" zvalue="3727"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3号发电机073"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1557.24,830.476) scale(1.11111,1.11111) translate(-154.89,-81.381)" width="15" x="1548.904761904761" y="813.8095238095237"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="258">
   <use class="kv110" height="26" transform="rotate(90,811.048,291.167) scale(1.25,1.25) translate(-160.71,-54.9833)" width="12" x="803.5476190476193" xlink:href="#Accessory:避雷器1_0" y="274.9166666666664" zvalue="3493"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110kV徐下Ⅱ回线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,811.048,291.167) scale(1.25,1.25) translate(-160.71,-54.9833)" width="12" x="803.5476190476193" y="274.9166666666664"/></g>
  <g id="251">
   <use class="kv110" height="40" transform="rotate(90,977.738,231.438) scale(2.125,2.125) translate(-495.126,-100.026)" width="40" x="935.2380952380954" xlink:href="#Accessory:220kV线路PT_0" y="188.938388019056" zvalue="3501"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110kV徐下Ⅱ回线A相电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,977.738,231.438) scale(2.125,2.125) translate(-495.126,-100.026)" width="40" x="935.2380952380954" y="188.938388019056"/></g>
  <g id="581">
   <use class="kv110" height="30" transform="rotate(0,1423.56,350.794) scale(2.85714,-2.85714) translate(-897.454,-445.714)" width="30" x="1380.698412698413" xlink:href="#Accessory:三卷PT带容断器_0" y="307.936507936508" zvalue="3529"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1423.56,350.794) scale(2.85714,-2.85714) translate(-897.454,-445.714)" width="30" x="1380.698412698413" y="307.936507936508"/></g>
  <g id="70">
   <use class="kv0" height="18" transform="rotate(0,233.775,935.476) scale(5.49383,5.49383) translate(-157.519,-724.754)" width="15" x="192.5714285714286" xlink:href="#Accessory:PT8_0" y="886.031746031746" zvalue="3590"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0713电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,233.775,935.476) scale(5.49383,5.49383) translate(-157.519,-724.754)" width="15" x="192.5714285714286" y="886.031746031746"/></g>
  <g id="75">
   <use class="kv0" height="30" transform="rotate(0,446.889,1011.9) scale(-2.85714,2.85714) translate(-575.443,-629.881)" width="30" x="404.0317460317463" xlink:href="#Accessory:三卷PT带容断器_0" y="969.047619047619" zvalue="3600"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号发电机0711电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,446.889,1011.9) scale(-2.85714,2.85714) translate(-575.443,-629.881)" width="30" x="404.0317460317463" y="969.047619047619"/></g>
  <g id="77">
   <use class="kv0" height="40" transform="rotate(0,557.127,1043.44) scale(3.37037,-3.37037) translate(-356.27,-1305.62)" width="30" x="506.5714285714288" xlink:href="#Accessory:带熔断器的线路PT1_0" y="976.0317460317461" zvalue="3603"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号发电机0712电压互感器1"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,557.127,1043.44) scale(3.37037,-3.37037) translate(-356.27,-1305.62)" width="30" x="506.5714285714288" y="976.0317460317461"/></g>
  <g id="78">
   <use class="kv0" height="40" transform="rotate(0,491.571,1043.81) scale(3.27778,3.27778) translate(-318.823,-679.804)" width="20" x="458.7936507936508" xlink:href="#Accessory:线路PT带避雷器0904_0" y="978.2539682539684" zvalue="3604"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号发电机0712电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,491.571,1043.81) scale(3.27778,3.27778) translate(-318.823,-679.804)" width="20" x="458.7936507936508" y="978.2539682539684"/></g>
  <g id="79">
   <use class="kv0" height="30" transform="rotate(0,608.349,1029.37) scale(4,-4) translate(-411.262,-1241.71)" width="30" x="548.3492063492064" xlink:href="#Accessory:RT1122_0" y="969.3650793650794" zvalue="3605"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号励磁变压器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,608.349,1029.37) scale(4,-4) translate(-411.262,-1241.71)" width="30" x="548.3492063492064" y="969.3650793650794"/></g>
  <g id="196">
   <use class="kv0" height="18" transform="rotate(0,839.775,935.476) scale(5.49383,5.49383) translate(-653.213,-724.754)" width="15" x="798.5714285714284" xlink:href="#Accessory:PT8_0" y="886.031746031746" zvalue="3673"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0723电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,839.775,935.476) scale(5.49383,5.49383) translate(-653.213,-724.754)" width="15" x="798.5714285714284" y="886.031746031746"/></g>
  <g id="187">
   <use class="kv0" height="30" transform="rotate(0,1052.89,1011.9) scale(-2.85714,2.85714) translate(-1393.54,-629.881)" width="30" x="1010.031746031746" xlink:href="#Accessory:三卷PT带容断器_0" y="969.047619047619" zvalue="3682"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2号发电机0721电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1052.89,1011.9) scale(-2.85714,2.85714) translate(-1393.54,-629.881)" width="30" x="1010.031746031746" y="969.047619047619"/></g>
  <g id="185">
   <use class="kv0" height="40" transform="rotate(0,1163.13,1043.44) scale(3.37037,-3.37037) translate(-782.468,-1305.62)" width="30" x="1112.571428571429" xlink:href="#Accessory:带熔断器的线路PT1_0" y="976.0317460317461" zvalue="3685"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2号发电机0722电压互感器1"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1163.13,1043.44) scale(3.37037,-3.37037) translate(-782.468,-1305.62)" width="30" x="1112.571428571429" y="976.0317460317461"/></g>
  <g id="184">
   <use class="kv0" height="40" transform="rotate(0,1097.57,1043.81) scale(3.27778,3.27778) translate(-739.941,-679.804)" width="20" x="1064.793650793651" xlink:href="#Accessory:线路PT带避雷器0904_0" y="978.2539682539684" zvalue="3686"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2号发电机0722电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1097.57,1043.81) scale(3.27778,3.27778) translate(-739.941,-679.804)" width="20" x="1064.793650793651" y="978.2539682539684"/></g>
  <g id="183">
   <use class="kv0" height="30" transform="rotate(0,1214.35,1029.37) scale(4,-4) translate(-865.762,-1241.71)" width="30" x="1154.349206349206" xlink:href="#Accessory:RT1122_0" y="969.3650793650794" zvalue="3687"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2号励磁变压器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1214.35,1029.37) scale(4,-4) translate(-865.762,-1241.71)" width="30" x="1154.349206349206" y="969.3650793650794"/></g>
  <g id="292">
   <use class="kv0" height="18" transform="rotate(0,1429.78,935.476) scale(5.49383,5.49383) translate(-1135.82,-724.754)" width="15" x="1388.571428571428" xlink:href="#Accessory:PT8_0" y="886.031746031746" zvalue="3726"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0733电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1429.78,935.476) scale(5.49383,5.49383) translate(-1135.82,-724.754)" width="15" x="1388.571428571428" y="886.031746031746"/></g>
  <g id="287">
   <use class="kv0" height="30" transform="rotate(0,1642.89,1011.9) scale(-2.85714,2.85714) translate(-2190.04,-629.881)" width="30" x="1600.031746031746" xlink:href="#Accessory:三卷PT带容断器_0" y="969.047619047619" zvalue="3735"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3号发电机0731电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1642.89,1011.9) scale(-2.85714,2.85714) translate(-2190.04,-629.881)" width="30" x="1600.031746031746" y="969.047619047619"/></g>
  <g id="285">
   <use class="kv0" height="40" transform="rotate(0,1753.13,1043.44) scale(3.37037,-3.37037) translate(-1197.41,-1305.62)" width="30" x="1702.571428571429" xlink:href="#Accessory:带熔断器的线路PT1_0" y="976.0317460317461" zvalue="3738"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3号发电机0732电压互感器1"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1753.13,1043.44) scale(3.37037,-3.37037) translate(-1197.41,-1305.62)" width="30" x="1702.571428571429" y="976.0317460317461"/></g>
  <g id="284">
   <use class="kv0" height="40" transform="rotate(0,1687.57,1043.81) scale(3.27778,3.27778) translate(-1149.94,-679.804)" width="20" x="1654.793650793651" xlink:href="#Accessory:线路PT带避雷器0904_0" y="978.2539682539684" zvalue="3739"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3号发电机0732电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1687.57,1043.81) scale(3.27778,3.27778) translate(-1149.94,-679.804)" width="20" x="1654.793650793651" y="978.2539682539684"/></g>
  <g id="283">
   <use class="kv0" height="30" transform="rotate(0,1804.35,1029.37) scale(4,-4) translate(-1308.26,-1241.71)" width="30" x="1744.349206349206" xlink:href="#Accessory:RT1122_0" y="969.3650793650794" zvalue="3740"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3号励磁变压器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1804.35,1029.37) scale(4,-4) translate(-1308.26,-1241.71)" width="30" x="1744.349206349206" y="969.3650793650794"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="256">
   <use class="kv110" height="30" transform="rotate(0,893.136,194.56) scale(4.61534,1.24259) translate(-686.968,-34.3452)" width="7" x="876.9821318316529" xlink:href="#ACLineSegment:线路_0" y="175.9206349206347" zvalue="3495"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110kV徐下Ⅱ回线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,893.136,194.56) scale(4.61534,1.24259) translate(-686.968,-34.3452)" width="7" x="876.9821318316529" y="175.9206349206347"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="252">
   <use class="kv110" height="22" transform="rotate(0,919.338,240.642) scale(1.83749,2.27788) translate(-412.735,-120.942)" width="15" x="905.5570339774465" xlink:href="#Compensator:串联电阻_0" y="215.5850047130273" zvalue="3500"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110kV徐下Ⅱ回线A相电阻"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,919.338,240.642) scale(1.83749,2.27788) translate(-412.735,-120.942)" width="15" x="905.5570339774465" y="215.5850047130273"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="246">
   <path class="kv110" d="M 919.34 227.54 L 919.34 216.37 L 941.01 216.37" stroke-width="1" zvalue="3506"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="252@0" LinkObjectIDznd="251@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 919.34 227.54 L 919.34 216.37 L 941.01 216.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="245">
   <path class="kv110" d="M 826.51 291.21 L 893.14 291.21" stroke-width="1" zvalue="3507"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="258@0" LinkObjectIDznd="1" MaxPinNum="2"/>
   </metadata>
  <path d="M 826.51 291.21 L 893.14 291.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="243">
   <path class="kv110" d="M 919.84 291.21 L 894.37 291.21" stroke-width="1" zvalue="3510"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="244@0" LinkObjectIDznd="245" MaxPinNum="2"/>
   </metadata>
  <path d="M 919.84 291.21 L 894.37 291.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="238">
   <path class="kv110" d="M 894.35 377.92 L 894.35 332.88" stroke-width="1" zvalue="3519"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="241@0" LinkObjectIDznd="240@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 894.35 377.92 L 894.35 332.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="237">
   <path class="kv110" d="M 894.35 443.8 L 894.35 398.58" stroke-width="1" zvalue="3520"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="259@1" LinkObjectIDznd="241@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 894.35 443.8 L 894.35 398.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="236">
   <path class="kv110" d="M 918.72 408.99 L 894.35 408.99" stroke-width="1" zvalue="3521"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="239@0" LinkObjectIDznd="237" MaxPinNum="2"/>
   </metadata>
  <path d="M 918.72 408.99 L 894.35 408.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="235">
   <path class="kv110" d="M 918.72 348.99 L 894.35 348.99" stroke-width="1" zvalue="3522"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="242@0" LinkObjectIDznd="238" MaxPinNum="2"/>
   </metadata>
  <path d="M 918.72 348.99 L 894.35 348.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="260">
   <path class="kv110" d="M 894.37 461.63 L 894.37 487.44" stroke-width="1" zvalue="3523"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="259@0" LinkObjectIDznd="18@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 894.37 461.63 L 894.37 487.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="kv110" d="M 1424.35 423.8 L 1424.35 392.65" stroke-width="1" zvalue="3531"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="261@1" LinkObjectIDznd="581@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1424.35 423.8 L 1424.35 392.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="9">
   <path class="kv110" d="M 1468.57 392.65 L 1424.35 392.65" stroke-width="1" zvalue="3532"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="4@0" LinkObjectIDznd="8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1468.57 392.65 L 1424.35 392.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv110" d="M 1424.37 441.63 L 1424.37 487.44" stroke-width="1" zvalue="3533"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="261@0" LinkObjectIDznd="18@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1424.37 441.63 L 1424.37 487.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="12">
   <path class="kv110" d="M 1460.72 453 L 1424.37 453" stroke-width="1" zvalue="3534"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="262@0" LinkObjectIDznd="10" MaxPinNum="2"/>
   </metadata>
  <path d="M 1460.72 453 L 1424.37 453" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="kv110" d="M 479.06 638.16 L 528.1 638.16" stroke-width="1" zvalue="3539"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="524@0" LinkObjectIDznd="525@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 479.06 638.16 L 528.1 638.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv110" d="M 528.37 511.05 L 528.37 487.44" stroke-width="1" zvalue="3546"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="22@0" LinkObjectIDznd="18@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 528.37 511.05 L 528.37 487.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv110" d="M 528.35 528.88 L 528.35 565.92" stroke-width="1" zvalue="3547"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="22@1" LinkObjectIDznd="17@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 528.35 528.88 L 528.35 565.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv110" d="M 527.35 586.58 L 527.35 620.85" stroke-width="1" zvalue="3548"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="17@1" LinkObjectIDznd="525@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 527.35 586.58 L 527.35 620.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv110" d="M 542.72 544.99 L 528.35 544.99" stroke-width="1" zvalue="3549"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="23@0" LinkObjectIDznd="25" MaxPinNum="2"/>
   </metadata>
  <path d="M 542.72 544.99 L 528.35 544.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="kv110" d="M 845.06 638.16 L 894.1 638.16" stroke-width="1" zvalue="3556"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@0" LinkObjectIDznd="48@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 845.06 638.16 L 894.1 638.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv110" d="M 894.35 528.88 L 894.35 565.92" stroke-width="1" zvalue="3563"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@1" LinkObjectIDznd="45@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 894.35 528.88 L 894.35 565.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv110" d="M 893.35 586.58 L 893.35 620.85" stroke-width="1" zvalue="3564"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@1" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 893.35 586.58 L 893.35 620.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="kv110" d="M 908.72 544.99 L 894.35 544.99" stroke-width="1" zvalue="3565"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@0" LinkObjectIDznd="42" MaxPinNum="2"/>
   </metadata>
  <path d="M 908.72 544.99 L 894.35 544.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv110" d="M 894.37 511.05 L 894.37 487.44" stroke-width="1" zvalue="3567"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@0" LinkObjectIDznd="18@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 894.37 511.05 L 894.37 487.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="kv110" d="M 1375.06 638.16 L 1424.1 638.16" stroke-width="1" zvalue="3573"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@0" LinkObjectIDznd="64@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1375.06 638.16 L 1424.1 638.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv110" d="M 1424.35 528.88 L 1424.35 565.92" stroke-width="1" zvalue="3580"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@1" LinkObjectIDznd="61@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1424.35 528.88 L 1424.35 565.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv110" d="M 1423.35 586.58 L 1423.35 620.85" stroke-width="1" zvalue="3581"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@1" LinkObjectIDznd="64@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1423.35 586.58 L 1423.35 620.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv110" d="M 1438.72 544.99 L 1424.35 544.99" stroke-width="1" zvalue="3582"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@0" LinkObjectIDznd="58" MaxPinNum="2"/>
   </metadata>
  <path d="M 1438.72 544.99 L 1424.35 544.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv110" d="M 1424.37 511.05 L 1424.37 487.44" stroke-width="1" zvalue="3584"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@0" LinkObjectIDznd="10" MaxPinNum="2"/>
   </metadata>
  <path d="M 1424.37 511.05 L 1424.37 487.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="84">
   <path class="kv0" d="M 135.24 818.25 L 135.24 793.81 L 655.24 793.81 L 655.24 818.25" stroke-width="1" zvalue="3612"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66@0" LinkObjectIDznd="80@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 135.24 818.25 L 135.24 793.81 L 655.24 793.81 L 655.24 818.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv0" d="M 361.24 818.25 L 361.24 793.81" stroke-width="1" zvalue="3613"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71@0" LinkObjectIDznd="84" MaxPinNum="2"/>
   </metadata>
  <path d="M 361.24 818.25 L 361.24 793.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="kv0" d="M 361.24 842.7 L 361.24 974.24" stroke-width="1" zvalue="3614"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71@1" LinkObjectIDznd="73@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 361.24 842.7 L 361.24 974.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="kv0" d="M 336.6 903.81 L 361.24 903.81" stroke-width="1" zvalue="3615"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72@0" LinkObjectIDznd="86" MaxPinNum="2"/>
   </metadata>
  <path d="M 336.6 903.81 L 361.24 903.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="kv0" d="M 361.24 903.81 L 608.35 903.81 L 608.35 985.37" stroke-width="1" zvalue="3616"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86" LinkObjectIDznd="79@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 361.24 903.81 L 608.35 903.81 L 608.35 985.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv0" d="M 447.03 921.6 L 447.03 903.81" stroke-width="1" zvalue="3617"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@0" LinkObjectIDznd="88" MaxPinNum="2"/>
   </metadata>
  <path d="M 447.03 921.6 L 447.03 903.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv0" d="M 447.03 970.05 L 447.07 933.02" stroke-width="1" zvalue="3618"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="75@0" LinkObjectIDznd="74@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 447.03 970.05 L 447.07 933.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv0" d="M 521.03 921.6 L 521.03 903.81" stroke-width="1" zvalue="3619"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="76@0" LinkObjectIDznd="88" MaxPinNum="2"/>
   </metadata>
  <path d="M 521.03 921.6 L 521.03 903.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv0" d="M 491.57 981.26 L 491.57 966.03 L 557.13 966.03 L 557.13 981.09" stroke-width="1" zvalue="3620"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="78@0" LinkObjectIDznd="77@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 491.57 981.26 L 491.57 966.03 L 557.13 966.03 L 557.13 981.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv0" d="M 521.07 933.02 L 521.07 966.03" stroke-width="1" zvalue="3621"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="76@1" LinkObjectIDznd="92" MaxPinNum="2"/>
   </metadata>
  <path d="M 521.07 933.02 L 521.07 966.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="kv0" d="M 228.67 889.54 L 228.67 833.02" stroke-width="1" zvalue="3622"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@0" LinkObjectIDznd="69@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 228.67 889.54 L 228.67 833.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv0" d="M 229.03 821.6 L 229.03 793.81" stroke-width="1" zvalue="3623"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@0" LinkObjectIDznd="84" MaxPinNum="2"/>
   </metadata>
  <path d="M 229.03 821.6 L 229.03 793.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv0" d="M 135.24 842.7 L 135.24 896.08" stroke-width="1" zvalue="3624"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66@1" LinkObjectIDznd="68@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 135.24 842.7 L 135.24 896.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv0" d="M 148.72 864.99 L 135.24 864.99" stroke-width="1" zvalue="3625"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@0" LinkObjectIDznd="96" MaxPinNum="2"/>
   </metadata>
  <path d="M 148.72 864.99 L 135.24 864.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv0" d="M 654.49 892.42 L 654.49 842.7" stroke-width="1" zvalue="3626"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="83@0" LinkObjectIDznd="80@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 654.49 892.42 L 654.49 842.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="kv0" d="M 670.72 864.99 L 654.49 864.99" stroke-width="1" zvalue="3627"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81@0" LinkObjectIDznd="98" MaxPinNum="2"/>
   </metadata>
  <path d="M 670.72 864.99 L 654.49 864.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="177">
   <path class="kv0" d="M 967.24 842.7 L 967.24 974.24" stroke-width="1" zvalue="3697"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="193@1" LinkObjectIDznd="190@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 967.24 842.7 L 967.24 974.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="176">
   <path class="kv0" d="M 942.6 903.81 L 967.24 903.81" stroke-width="1" zvalue="3698"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@0" LinkObjectIDznd="177" MaxPinNum="2"/>
   </metadata>
  <path d="M 942.6 903.81 L 967.24 903.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="175">
   <path class="kv0" d="M 967.24 903.81 L 1214.35 903.81 L 1214.35 985.37" stroke-width="1" zvalue="3699"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176" LinkObjectIDznd="183@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 967.24 903.81 L 1214.35 903.81 L 1214.35 985.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="174">
   <path class="kv0" d="M 1053.03 921.6 L 1053.03 903.81" stroke-width="1" zvalue="3700"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="188@0" LinkObjectIDznd="175" MaxPinNum="2"/>
   </metadata>
  <path d="M 1053.03 921.6 L 1053.03 903.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="173">
   <path class="kv0" d="M 1053.03 970.05 L 1053.07 933.02" stroke-width="1" zvalue="3701"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="187@0" LinkObjectIDznd="188@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1053.03 970.05 L 1053.07 933.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="kv0" d="M 1127.03 921.6 L 1127.03 903.81" stroke-width="1" zvalue="3702"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@0" LinkObjectIDznd="175" MaxPinNum="2"/>
   </metadata>
  <path d="M 1127.03 921.6 L 1127.03 903.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="171">
   <path class="kv0" d="M 1097.57 981.26 L 1097.57 966.03 L 1163.13 966.03 L 1163.13 981.09" stroke-width="1" zvalue="3703"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="184@0" LinkObjectIDznd="185@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1097.57 981.26 L 1097.57 966.03 L 1163.13 966.03 L 1163.13 981.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="170">
   <path class="kv0" d="M 1127.07 933.02 L 1127.07 966.03" stroke-width="1" zvalue="3704"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@1" LinkObjectIDznd="171" MaxPinNum="2"/>
   </metadata>
  <path d="M 1127.07 933.02 L 1127.07 966.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="169">
   <path class="kv0" d="M 834.67 889.54 L 834.67 833.02" stroke-width="1" zvalue="3705"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@0" LinkObjectIDznd="197@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 834.67 889.54 L 834.67 833.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="167">
   <path class="kv0" d="M 741.24 842.7 L 741.24 896.08" stroke-width="1" zvalue="3707"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="203@1" LinkObjectIDznd="198@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 741.24 842.7 L 741.24 896.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="166">
   <path class="kv0" d="M 754.72 864.99 L 741.24 864.99" stroke-width="1" zvalue="3708"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="201@0" LinkObjectIDznd="167" MaxPinNum="2"/>
   </metadata>
  <path d="M 754.72 864.99 L 741.24 864.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="204">
   <path class="kv0" d="M 528.07 697.92 L 528.07 793.81" stroke-width="1" zvalue="3712"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="525@1" LinkObjectIDznd="84" MaxPinNum="2"/>
   </metadata>
  <path d="M 528.07 697.92 L 528.07 793.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="206">
   <path class="kv0" d="M 741.24 818.25 L 741.24 780.48 L 967.24 780.48 L 967.24 818.25" stroke-width="1" zvalue="3714"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="203@0" LinkObjectIDznd="193@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 741.24 818.25 L 741.24 780.48 L 967.24 780.48 L 967.24 818.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="207">
   <path class="kv0" d="M 835.03 821.6 L 835.03 780.48" stroke-width="1" zvalue="3715"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="197@0" LinkObjectIDznd="206" MaxPinNum="2"/>
   </metadata>
  <path d="M 835.03 821.6 L 835.03 780.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="208">
   <path class="kv0" d="M 894.07 697.92 L 894.07 780.48" stroke-width="1" zvalue="3716"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@1" LinkObjectIDznd="206" MaxPinNum="2"/>
   </metadata>
  <path d="M 894.07 697.92 L 894.07 780.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="282">
   <path class="kv0" d="M 1557.24 842.7 L 1557.24 974.24" stroke-width="1" zvalue="3742"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="291@1" LinkObjectIDznd="289@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1557.24 842.7 L 1557.24 974.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="281">
   <path class="kv0" d="M 1532.6 903.81 L 1557.24 903.81" stroke-width="1" zvalue="3743"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="290@0" LinkObjectIDznd="282" MaxPinNum="2"/>
   </metadata>
  <path d="M 1532.6 903.81 L 1557.24 903.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="280">
   <path class="kv0" d="M 1557.24 903.81 L 1804.35 903.81 L 1804.35 985.37" stroke-width="1" zvalue="3744"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="281" LinkObjectIDznd="283@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1557.24 903.81 L 1804.35 903.81 L 1804.35 985.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="279">
   <path class="kv0" d="M 1643.03 921.6 L 1643.03 903.81" stroke-width="1" zvalue="3745"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="288@0" LinkObjectIDznd="280" MaxPinNum="2"/>
   </metadata>
  <path d="M 1643.03 921.6 L 1643.03 903.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="278">
   <path class="kv0" d="M 1643.03 970.05 L 1643.07 933.02" stroke-width="1" zvalue="3746"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="287@0" LinkObjectIDznd="288@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1643.03 970.05 L 1643.07 933.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="277">
   <path class="kv0" d="M 1717.03 921.6 L 1717.03 903.81" stroke-width="1" zvalue="3747"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="286@0" LinkObjectIDznd="280" MaxPinNum="2"/>
   </metadata>
  <path d="M 1717.03 921.6 L 1717.03 903.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="276">
   <path class="kv0" d="M 1687.57 981.26 L 1687.57 966.03 L 1753.13 966.03 L 1753.13 981.09" stroke-width="1" zvalue="3748"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="284@0" LinkObjectIDznd="285@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1687.57 981.26 L 1687.57 966.03 L 1753.13 966.03 L 1753.13 981.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="275">
   <path class="kv0" d="M 1717.07 933.02 L 1717.07 966.03" stroke-width="1" zvalue="3749"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="286@1" LinkObjectIDznd="276" MaxPinNum="2"/>
   </metadata>
  <path d="M 1717.07 933.02 L 1717.07 966.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="274">
   <path class="kv0" d="M 1424.67 889.54 L 1424.67 833.02" stroke-width="1" zvalue="3750"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="292@0" LinkObjectIDznd="293@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1424.67 889.54 L 1424.67 833.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="273">
   <path class="kv0" d="M 1331.24 842.7 L 1331.24 896.08" stroke-width="1" zvalue="3751"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="296@1" LinkObjectIDznd="294@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1331.24 842.7 L 1331.24 896.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="272">
   <path class="kv0" d="M 1344.72 864.99 L 1331.24 864.99" stroke-width="1" zvalue="3752"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="295@0" LinkObjectIDznd="273" MaxPinNum="2"/>
   </metadata>
  <path d="M 1344.72 864.99 L 1331.24 864.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="270">
   <path class="kv0" d="M 1331.24 818.25 L 1331.24 780.48 L 1557.24 780.48 L 1557.24 818.25" stroke-width="1" zvalue="3754"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="296@0" LinkObjectIDznd="291@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1331.24 818.25 L 1331.24 780.48 L 1557.24 780.48 L 1557.24 818.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="269">
   <path class="kv0" d="M 1425.03 821.6 L 1425.03 780.48" stroke-width="1" zvalue="3755"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="293@0" LinkObjectIDznd="270" MaxPinNum="2"/>
   </metadata>
  <path d="M 1425.03 821.6 L 1425.03 780.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="298">
   <path class="kv0" d="M 1424.07 697.92 L 1424.07 780.48" stroke-width="1" zvalue="3757"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="64@1" LinkObjectIDznd="270" MaxPinNum="2"/>
   </metadata>
  <path d="M 1424.07 697.92 L 1424.07 780.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1">
   <path class="kv110" d="M 893.14 213.01 L 893.14 315.05" stroke-width="1" zvalue="3762"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="256@0" LinkObjectIDznd="240@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 893.14 213.01 L 893.14 315.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="244">
   <use class="kv110" height="30" transform="rotate(90,930.773,291.217) scale(0.763595,-0.763595) translate(286.745,-676.14)" width="12" x="926.1917529493835" xlink:href="#GroundDisconnector:地刀12_0" y="279.7631693343826" zvalue="3508"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110kV徐下Ⅱ回线13267"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,930.773,291.217) scale(0.763595,-0.763595) translate(286.745,-676.14)" width="12" x="926.1917529493835" y="279.7631693343826"/></g>
  <g id="242">
   <use class="kv110" height="30" transform="rotate(90,929.662,348.995) scale(0.763595,-0.763595) translate(286.401,-809.583)" width="12" x="925.0806418382724" xlink:href="#GroundDisconnector:地刀12_0" y="337.5409471121603" zvalue="3511"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110kV徐下Ⅱ回线13260"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,929.662,348.995) scale(0.763595,-0.763595) translate(286.401,-809.583)" width="12" x="925.0806418382724" y="337.5409471121603"/></g>
  <g id="239">
   <use class="kv110" height="30" transform="rotate(90,929.662,408.995) scale(0.763595,-0.763595) translate(286.401,-948.159)" width="12" x="925.0806418382724" xlink:href="#GroundDisconnector:地刀12_0" y="397.5409471121603" zvalue="3517"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110kV徐下Ⅱ回线13217"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,929.662,408.995) scale(0.763595,-0.763595) translate(286.401,-948.159)" width="12" x="925.0806418382724" y="397.5409471121603"/></g>
  <g id="262">
   <use class="kv110" height="30" transform="rotate(270,1471.66,452.995) scale(0.763595,0.763595) translate(454.201,136.699)" width="12" x="1467.080641838272" xlink:href="#GroundDisconnector:地刀12_0" y="441.5409471121603" zvalue="3527"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110kV母线电压互感器19010"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1471.66,452.995) scale(0.763595,0.763595) translate(454.201,136.699)" width="12" x="1467.080641838272" y="441.5409471121603"/></g>
  <g id="4">
   <use class="kv110" height="45" transform="rotate(0,1477.46,374.873) scale(1.11111,1.11111) translate(-145.246,-34.9873)" width="45" x="1452.460317460318" xlink:href="#GroundDisconnector:12547_0" y="349.8730158730162" zvalue="3530"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110kV母线电压互感器19017"/>
   </metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,1477.46,374.873) scale(1.11111,1.11111) translate(-145.246,-34.9873)" width="45" x="1452.460317460318" y="349.8730158730162"/></g>
  <g id="524">
   <use class="kv110" height="40" transform="rotate(0,455.941,659.21) scale(-1.725,-1.725) translate(-705.754,-1026.86)" width="40" x="421.4407202756548" xlink:href="#GroundDisconnector:中性点地刀12_0" y="624.7096791418771" zvalue="3537"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1号主变1010"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,455.941,659.21) scale(-1.725,-1.725) translate(-705.754,-1026.86)" width="40" x="421.4407202756548" y="624.7096791418771"/></g>
  <g id="23">
   <use class="kv110" height="30" transform="rotate(90,553.662,544.995) scale(0.763595,-0.763595) translate(169.993,-1262.26)" width="12" x="549.0806418382724" xlink:href="#GroundDisconnector:地刀12_0" y="533.5409471121604" zvalue="3545"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1号主变110kV侧10117"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,553.662,544.995) scale(0.763595,-0.763595) translate(169.993,-1262.26)" width="12" x="549.0806418382724" y="533.5409471121604"/></g>
  <g id="47">
   <use class="kv110" height="40" transform="rotate(0,821.941,659.21) scale(-1.725,-1.725) translate(-1283.93,-1026.86)" width="40" x="787.4407202756549" xlink:href="#GroundDisconnector:中性点地刀12_0" y="624.7096791418771" zvalue="3554"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2号主变1020"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,821.941,659.21) scale(-1.725,-1.725) translate(-1283.93,-1026.86)" width="40" x="787.4407202756549" y="624.7096791418771"/></g>
  <g id="43">
   <use class="kv110" height="30" transform="rotate(90,919.662,544.995) scale(0.763595,-0.763595) translate(283.305,-1262.26)" width="12" x="915.0806418382724" xlink:href="#GroundDisconnector:地刀12_0" y="533.5409471121604" zvalue="3561"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2号主变110kV侧10217"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,919.662,544.995) scale(0.763595,-0.763595) translate(283.305,-1262.26)" width="12" x="915.0806418382724" y="533.5409471121604"/></g>
  <g id="63">
   <use class="kv110" height="40" transform="rotate(0,1351.94,659.21) scale(-1.725,-1.725) translate(-2121.17,-1026.86)" width="40" x="1317.440720275655" xlink:href="#GroundDisconnector:中性点地刀12_0" y="624.7096791418771" zvalue="3571"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#3号主变1030"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1351.94,659.21) scale(-1.725,-1.725) translate(-2121.17,-1026.86)" width="40" x="1317.440720275655" y="624.7096791418771"/></g>
  <g id="59">
   <use class="kv110" height="30" transform="rotate(90,1449.66,544.995) scale(0.763595,-0.763595) translate(447.39,-1262.26)" width="12" x="1445.080641838272" xlink:href="#GroundDisconnector:地刀12_0" y="533.5409471121604" zvalue="3578"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#3号主变110kV侧10317"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1449.66,544.995) scale(0.763595,-0.763595) translate(447.39,-1262.26)" width="12" x="1445.080641838272" y="533.5409471121604"/></g>
  <g id="67">
   <use class="kv0" height="30" transform="rotate(90,159.662,864.995) scale(0.763595,-0.763595) translate(48.0122,-2001.33)" width="12" x="155.0806418382724" xlink:href="#GroundDisconnector:地刀12_0" y="853.5409471121604" zvalue="3587"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号厂用变07467"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,159.662,864.995) scale(0.763595,-0.763595) translate(48.0122,-2001.33)" width="12" x="155.0806418382724" y="853.5409471121604"/></g>
  <g id="72">
   <use class="kv0" height="30" transform="rotate(270,325.662,903.818) scale(-0.763595,-0.763595) translate(-753.566,-2091)" width="12" x="321.0806418382724" xlink:href="#GroundDisconnector:地刀12_0" y="892.3643598105732" zvalue="3594"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号发电机07167"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,325.662,903.818) scale(-0.763595,-0.763595) translate(-753.566,-2091)" width="12" x="321.0806418382724" y="892.3643598105732"/></g>
  <g id="81">
   <use class="kv0" height="30" transform="rotate(90,681.662,864.995) scale(0.763595,-0.763595) translate(209.621,-2001.33)" width="12" x="677.0806418382724" xlink:href="#GroundDisconnector:地刀12_0" y="853.5409471121604" zvalue="3609"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2号厂用变07567"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,681.662,864.995) scale(0.763595,-0.763595) translate(209.621,-2001.33)" width="12" x="677.0806418382724" y="853.5409471121604"/></g>
  <g id="201">
   <use class="kv0" height="30" transform="rotate(90,765.662,864.995) scale(0.763595,-0.763595) translate(235.627,-2001.33)" width="12" x="761.0806418382724" xlink:href="#GroundDisconnector:地刀12_0" y="853.5409471121604" zvalue="3667"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3号厂用变07667"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,765.662,864.995) scale(0.763595,-0.763595) translate(235.627,-2001.33)" width="12" x="761.0806418382724" y="853.5409471121604"/></g>
  <g id="191">
   <use class="kv0" height="30" transform="rotate(270,931.662,903.818) scale(-0.763595,-0.763595) translate(-2153.18,-2091)" width="12" x="927.0806418382724" xlink:href="#GroundDisconnector:地刀12_0" y="892.3643598105732" zvalue="3676"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2号发电机07267"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,931.662,903.818) scale(-0.763595,-0.763595) translate(-2153.18,-2091)" width="12" x="927.0806418382724" y="892.3643598105732"/></g>
  <g id="295">
   <use class="kv0" height="30" transform="rotate(90,1355.66,864.995) scale(0.763595,-0.763595) translate(418.288,-2001.33)" width="12" x="1351.080641838272" xlink:href="#GroundDisconnector:地刀12_0" y="853.5409471121604" zvalue="3720"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="4号厂用变07767"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1355.66,864.995) scale(0.763595,-0.763595) translate(418.288,-2001.33)" width="12" x="1351.080641838272" y="853.5409471121604"/></g>
  <g id="290">
   <use class="kv0" height="30" transform="rotate(270,1521.66,903.818) scale(-0.763595,-0.763595) translate(-3515.84,-2091)" width="12" x="1517.080641838272" xlink:href="#GroundDisconnector:地刀12_0" y="892.3643598105732" zvalue="3729"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3号发电机07367"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1521.66,903.818) scale(-0.763595,-0.763595) translate(-3515.84,-2091)" width="12" x="1517.080641838272" y="892.3643598105732"/></g>
 </g>
 <g id="BreakerClass">
  <g id="241">
   <use class="kv110" height="20" transform="rotate(0,894.263,388.254) scale(1.11111,1.11111) translate(-88.8707,-37.7143)" width="10" x="888.7074606690009" xlink:href="#Breaker:刀闸绘制规范_0" y="377.1428571428571" zvalue="3513"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="110kV徐下Ⅱ回线132"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,894.263,388.254) scale(1.11111,1.11111) translate(-88.8707,-37.7143)" width="10" x="888.7074606690009" y="377.1428571428571"/></g>
  <g id="17">
   <use class="kv110" height="20" transform="rotate(0,527.263,576.254) scale(1.11111,1.11111) translate(-52.1707,-56.5143)" width="10" x="521.7074606690009" xlink:href="#Breaker:刀闸绘制规范_0" y="565.1428571428571" zvalue="3541"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1号主变110kV侧101"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,527.263,576.254) scale(1.11111,1.11111) translate(-52.1707,-56.5143)" width="10" x="521.7074606690009" y="565.1428571428571"/></g>
  <g id="45">
   <use class="kv110" height="20" transform="rotate(0,893.263,576.254) scale(1.11111,1.11111) translate(-88.7707,-56.5143)" width="10" x="887.7074606690009" xlink:href="#Breaker:刀闸绘制规范_0" y="565.1428571428571" zvalue="3557"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2号主变110kV侧102"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,893.263,576.254) scale(1.11111,1.11111) translate(-88.7707,-56.5143)" width="10" x="887.7074606690009" y="565.1428571428571"/></g>
  <g id="61">
   <use class="kv110" height="20" transform="rotate(0,1423.26,576.254) scale(1.11111,1.11111) translate(-141.771,-56.5143)" width="10" x="1417.707460669001" xlink:href="#Breaker:刀闸绘制规范_0" y="565.1428571428571" zvalue="3574"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#3号主变110kV侧103"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1423.26,576.254) scale(1.11111,1.11111) translate(-141.771,-56.5143)" width="10" x="1417.707460669001" y="565.1428571428571"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="525">
   <g id="5250">
    <use class="kv110" height="50" transform="rotate(0,528.071,659.31) scale(1.56667,1.56667) translate(-182.505,-224.307)" width="30" x="504.57" xlink:href="#PowerTransformer2:Y-D_0" y="620.14" zvalue="3536"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="110"/>
    </metadata>
   </g>
   <g id="5251">
    <use class="kv0" height="50" transform="rotate(0,528.071,659.31) scale(1.56667,1.56667) translate(-182.505,-224.307)" width="30" x="504.57" xlink:href="#PowerTransformer2:Y-D_1" y="620.14" zvalue="3536"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="0"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1号主变"/>
   </metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,528.071,659.31) scale(1.56667,1.56667) translate(-182.505,-224.307)" width="30" x="504.57" y="620.14"/></g>
  <g id="48">
   <g id="480">
    <use class="kv110" height="50" transform="rotate(0,894.071,659.31) scale(1.56667,1.56667) translate(-314.888,-224.307)" width="30" x="870.5700000000001" xlink:href="#PowerTransformer2:Y-D_0" y="620.14" zvalue="3552"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="110"/>
    </metadata>
   </g>
   <g id="481">
    <use class="kv0" height="50" transform="rotate(0,894.071,659.31) scale(1.56667,1.56667) translate(-314.888,-224.307)" width="30" x="870.5700000000001" xlink:href="#PowerTransformer2:Y-D_1" y="620.14" zvalue="3552"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="0"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2号主变"/>
   </metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,894.071,659.31) scale(1.56667,1.56667) translate(-314.888,-224.307)" width="30" x="870.5700000000001" y="620.14"/></g>
  <g id="64">
   <g id="640">
    <use class="kv110" height="50" transform="rotate(0,1424.07,659.31) scale(1.56667,1.56667) translate(-506.59,-224.307)" width="30" x="1400.57" xlink:href="#PowerTransformer2:Y-D_0" y="620.14" zvalue="3569"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="110"/>
    </metadata>
   </g>
   <g id="641">
    <use class="kv0" height="50" transform="rotate(0,1424.07,659.31) scale(1.56667,1.56667) translate(-506.59,-224.307)" width="30" x="1400.57" xlink:href="#PowerTransformer2:Y-D_1" y="620.14" zvalue="3569"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="0"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#3号主变"/>
   </metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1424.07,659.31) scale(1.56667,1.56667) translate(-506.59,-224.307)" width="30" x="1400.57" y="620.14"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="68">
   <use class="kv0" height="25" transform="rotate(0,129.405,938.532) scale(-3.88889,3.88889) translate(-141.014,-661.084)" width="15" x="100.2380952380953" xlink:href="#EnergyConsumer:厂站用变_0" y="889.9206349206348" zvalue="3588"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号厂用变"/>
   </metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,129.405,938.532) scale(-3.88889,3.88889) translate(-141.014,-661.084)" width="15" x="100.2380952380953" y="889.9206349206348"/></g>
  <g id="83">
   <use class="kv0" height="25" transform="rotate(0,659.349,937.143) scale(3.88889,3.88889) translate(-468.136,-660.052)" width="15" x="630.1825396825398" xlink:href="#EnergyConsumer:厂站用变DY_0" y="888.5317460317464" zvalue="3611"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2号厂用变"/>
   </metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,659.349,937.143) scale(3.88889,3.88889) translate(-468.136,-660.052)" width="15" x="630.1825396825398" y="888.5317460317464"/></g>
  <g id="198">
   <use class="kv0" height="25" transform="rotate(0,735.405,938.532) scale(-3.88889,3.88889) translate(-902.842,-661.084)" width="15" x="706.2380952380954" xlink:href="#EnergyConsumer:厂站用变_0" y="889.9206349206348" zvalue="3669"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3号厂用变"/>
   </metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,735.405,938.532) scale(-3.88889,3.88889) translate(-902.842,-661.084)" width="15" x="706.2380952380954" y="889.9206349206348"/></g>
  <g id="294">
   <use class="kv0" height="25" transform="rotate(0,1325.4,938.532) scale(-3.88889,3.88889) translate(-1644.56,-661.084)" width="15" x="1296.238095238095" xlink:href="#EnergyConsumer:厂站用变_0" y="889.9206349206348" zvalue="3722"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="4号厂用变"/>
   </metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1325.4,938.532) scale(-3.88889,3.88889) translate(-1644.56,-661.084)" width="15" x="1296.238095238095" y="889.9206349206348"/></g>
 </g>
 <g id="DollyBreakerClass">
  <g id="69">
   <use class="kv0" height="22" transform="rotate(0,229.016,832.698) scale(1.11111,1.11111) translate(-21.6794,-82.0476)" width="22" x="216.7936507936508" xlink:href="#DollyBreaker:手车_0" y="820.4761904761905" zvalue="3589"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0713"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,229.016,832.698) scale(1.11111,1.11111) translate(-21.6794,-82.0476)" width="22" x="216.7936507936508" y="820.4761904761905"/></g>
  <g id="74">
   <use class="kv0" height="22" transform="rotate(0,447.016,932.698) scale(1.11111,1.11111) translate(-43.4794,-92.0476)" width="22" x="434.7936507936508" xlink:href="#DollyBreaker:手车_0" y="920.4761904761905" zvalue="3598"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号发电机0711"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,447.016,932.698) scale(1.11111,1.11111) translate(-43.4794,-92.0476)" width="22" x="434.7936507936508" y="920.4761904761905"/></g>
  <g id="76">
   <use class="kv0" height="22" transform="rotate(0,521.016,932.698) scale(1.11111,1.11111) translate(-50.8794,-92.0476)" width="22" x="508.793650793651" xlink:href="#DollyBreaker:手车_0" y="920.4761904761905" zvalue="3602"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号发电机0712"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,521.016,932.698) scale(1.11111,1.11111) translate(-50.8794,-92.0476)" width="22" x="508.793650793651" y="920.4761904761905"/></g>
  <g id="197">
   <use class="kv0" height="22" transform="rotate(0,835.016,832.698) scale(1.11111,1.11111) translate(-82.2794,-82.0476)" width="22" x="822.793650793651" xlink:href="#DollyBreaker:手车_0" y="820.4761904761905" zvalue="3671"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0723"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,835.016,832.698) scale(1.11111,1.11111) translate(-82.2794,-82.0476)" width="22" x="822.793650793651" y="820.4761904761905"/></g>
  <g id="188">
   <use class="kv0" height="22" transform="rotate(0,1053.02,932.698) scale(1.11111,1.11111) translate(-104.079,-92.0476)" width="22" x="1040.793650793651" xlink:href="#DollyBreaker:手车_0" y="920.4761904761905" zvalue="3680"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2号发电机0721"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1053.02,932.698) scale(1.11111,1.11111) translate(-104.079,-92.0476)" width="22" x="1040.793650793651" y="920.4761904761905"/></g>
  <g id="186">
   <use class="kv0" height="22" transform="rotate(0,1127.02,932.698) scale(1.11111,1.11111) translate(-111.479,-92.0476)" width="22" x="1114.793650793651" xlink:href="#DollyBreaker:手车_0" y="920.4761904761905" zvalue="3683"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2号发电机0722"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1127.02,932.698) scale(1.11111,1.11111) translate(-111.479,-92.0476)" width="22" x="1114.793650793651" y="920.4761904761905"/></g>
  <g id="293">
   <use class="kv0" height="22" transform="rotate(0,1425.02,832.698) scale(1.11111,1.11111) translate(-141.279,-82.0476)" width="22" x="1412.793650793651" xlink:href="#DollyBreaker:手车_0" y="820.4761904761905" zvalue="3724"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0733"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1425.02,832.698) scale(1.11111,1.11111) translate(-141.279,-82.0476)" width="22" x="1412.793650793651" y="820.4761904761905"/></g>
  <g id="288">
   <use class="kv0" height="22" transform="rotate(0,1643.02,932.698) scale(1.11111,1.11111) translate(-163.079,-92.0476)" width="22" x="1630.793650793651" xlink:href="#DollyBreaker:手车_0" y="920.4761904761905" zvalue="3733"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3号发电机0731"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1643.02,932.698) scale(1.11111,1.11111) translate(-163.079,-92.0476)" width="22" x="1630.793650793651" y="920.4761904761905"/></g>
  <g id="286">
   <use class="kv0" height="22" transform="rotate(0,1717.02,932.698) scale(1.11111,1.11111) translate(-170.479,-92.0476)" width="22" x="1704.793650793651" xlink:href="#DollyBreaker:手车_0" y="920.4761904761905" zvalue="3736"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3号发电机0732"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1717.02,932.698) scale(1.11111,1.11111) translate(-170.479,-92.0476)" width="22" x="1704.793650793651" y="920.4761904761905"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="73">
   <use class="kv0" height="30" transform="rotate(0,361.238,996.365) scale(1.5,1.5) translate(-112.913,-324.622)" width="30" x="338.7380952380948" xlink:href="#Generator:发电机_0" y="973.8650793650793" zvalue="3596"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="1号发电机"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,361.238,996.365) scale(1.5,1.5) translate(-112.913,-324.622)" width="30" x="338.7380952380948" y="973.8650793650793"/></g>
  <g id="190">
   <use class="kv0" height="30" transform="rotate(0,967.238,996.365) scale(1.5,1.5) translate(-314.913,-324.622)" width="30" x="944.7380952380948" xlink:href="#Generator:发电机_0" y="973.8650793650793" zvalue="3678"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="2号发电机"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,967.238,996.365) scale(1.5,1.5) translate(-314.913,-324.622)" width="30" x="944.7380952380948" y="973.8650793650793"/></g>
  <g id="289">
   <use class="kv0" height="30" transform="rotate(0,1557.24,996.365) scale(1.5,1.5) translate(-511.579,-324.622)" width="30" x="1534.738095238095" xlink:href="#Generator:发电机_0" y="973.8650793650793" zvalue="3731"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3号发电机"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1557.24,996.365) scale(1.5,1.5) translate(-511.579,-324.622)" width="30" x="1534.738095238095" y="973.8650793650793"/></g>
 </g>
</svg>