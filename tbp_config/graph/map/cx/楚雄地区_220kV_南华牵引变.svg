<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-252" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="-348 -997 2388 1300">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape10_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="0" y1="13" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="34" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="28" y2="34"/>
   </symbol>
   <symbol id="breaker2:shape10_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="13" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="34" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="28" y2="34"/>
   </symbol>
   <symbol id="breaker2:shape10-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="0" y1="13" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="28" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="34" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="13" y2="5"/>
   </symbol>
   <symbol id="breaker2:shape10-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="13" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="34" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="28" y2="34"/>
   </symbol>
   <symbol id="capacitor:shape39">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="5" x2="15" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="55" x2="55" y1="20" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="55" x2="65" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="15" x2="15" y1="20" y2="0"/>
   </symbol>
   <symbol id="capacitor:shape42">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0375704" x1="8" x2="12" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0375704" x1="2" x2="6" y1="14" y2="14"/>
    <circle cx="10" cy="14" fillStyle="0" r="4" stroke-width="0.25"/>
    <circle cx="4" cy="14" fillStyle="0" r="4" stroke-width="0.25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0375704" x1="8" x2="12" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0375704" x1="2" x2="6" y1="26" y2="26"/>
    <circle cx="10" cy="26" fillStyle="0" r="4" stroke-width="0.25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0166979" x1="10" x2="10" y1="10" y2="8"/>
    <circle cx="4" cy="26" fillStyle="0" r="4" stroke-width="0.25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0939259" x1="10" x2="20" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0632133" x1="20" x2="20" y1="37" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0166979" x1="10" x2="10" y1="30" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0166979" x1="10" x2="10" y1="18" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0166979" x1="20" x2="10" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0751407" x1="10" x2="20" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0375704" x1="18" x2="22" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0375704" x1="18" x2="22" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0632133" x1="20" x2="20" y1="25" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0375704" x1="18" x2="22" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0375704" x1="18" x2="22" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0632133" x1="20" x2="20" y1="13" y2="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="load:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="5" y2="29"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,19 9,31 17,19 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape81_0">
    <ellipse cx="34" cy="92" fillStyle="0" rx="33.5" ry="33" stroke-width="0.693878"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.693878" x1="35" x2="25" y1="85" y2="106"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.693878" x1="35" x2="45" y1="85" y2="106"/>
   </symbol>
   <symbol id="transformer2:shape81_1">
    <ellipse cx="34" cy="44" fillStyle="0" rx="33.5" ry="33" stroke-width="0.693878"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.693878" x1="35" x2="25" y1="32" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.693878" x1="35" x2="45" y1="32" y2="53"/>
   </symbol>
   <symbol id="voltageTransformer:shape124">
    <ellipse cx="33" cy="63" rx="13" ry="12.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="48" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="4" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="2" y1="45" y2="45"/>
    <ellipse cx="33" cy="80" rx="13" ry="12.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="79" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="57" y2="66"/>
    <rect height="19" stroke-width="0.311623" width="12" x="27" y="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="6" y2="51"/>
   </symbol>
   <symbol id="voltageTransformer:shape125">
    <circle cx="8" cy="22" fillStyle="0" r="7" stroke-width="0.492782"/>
    <polyline points="51,14 51,8 " stroke-width="1"/>
    <polyline points="51,22 51,14 " stroke-width="1"/>
    <polyline points="51,14 26,14 " stroke-width="1"/>
    <polyline points="26,31 47,31 51,31 51,22 " stroke-width="1"/>
    <polyline points="51,22 39,22 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="56" x2="46" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07362" x1="53" x2="49" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="54" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="20" x2="24" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="32" x2="36" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="20" x2="24" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="32" x2="32" y1="21" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="36" x2="32" y1="24" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="28" x2="32" y1="24" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="16" x2="20" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="24" x2="20" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="20" x2="20" y1="16" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="24" x2="20" y1="31" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="20" x2="20" y1="28" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="16" x2="20" y1="31" y2="28"/>
    <circle cx="32" cy="22" fillStyle="0" r="7" stroke-width="0.492782"/>
    <circle cx="20" cy="17" fillStyle="0" r="7" stroke-width="0.492782"/>
    <circle cx="20" cy="27" fillStyle="0" r="7" stroke-width="0.492782"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="9" x2="11" y1="24" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="7" x2="5" y1="24" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="5" x2="11" y1="20" y2="20"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_27f7630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27f8790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_27f9180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_27fa0c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_27fb390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_27fbfa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27fcb50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_27fd580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2016310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2016310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2800910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2800910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28024f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28024f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2803500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2805190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2805de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2806cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_28075a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2808d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2809a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_280a320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_280aae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_280bbc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_280c540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_280d030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_280d9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_280ee70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_280fa10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2810a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2811680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_281fe50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2812f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2814560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2815a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1310" width="2398" x="-353" y="-1002"/>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="101,183 103,183 105,182 107,182 109,181 110,180 112,179 113,177 114,175 115,174 115,172 115,170 115,168 115,166 114,165 113,163 112,162 110,160 109,159 107,158 105,158 103,157 101,157 100,157 98,158 96,158 94,159 93,160 91,162 90,163 89,165 88,166 88,168 88,170 " stroke="rgb(60,120,255)" stroke-width="0.0972"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="454,194 455,194 455,194 456,194 457,194 457,193 458,193 459,192 459,192 459,191 460,190 460,190 460,189 460,188 460,187 460,187 459,186 459,185 459,185 458,184 457,184 457,183 456,183 455,183 455,183 454,183 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="454,205 455,205 455,205 456,205 457,205 457,204 458,204 459,203 459,203 459,202 460,201 460,201 460,200 460,199 460,198 460,198 459,197 459,196 459,196 458,195 457,195 457,194 456,194 455,194 455,194 454,194 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="454,216 455,216 455,216 456,216 457,216 457,215 458,215 459,214 459,214 459,213 460,212 460,212 460,211 460,210 460,209 460,209 459,208 459,207 459,207 458,206 457,206 457,205 456,205 455,205 455,205 454,205 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="454,227 455,227 455,227 456,227 457,227 457,226 458,226 459,225 459,225 459,224 460,223 460,223 460,222 460,221 460,220 460,220 459,219 459,218 459,218 458,217 457,217 457,216 456,216 455,216 455,216 454,216 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="632,194 633,194 633,194 634,194 635,194 635,193 636,193 637,192 637,192 637,191 638,190 638,190 638,189 638,188 638,187 638,187 637,186 637,185 637,185 636,184 635,184 635,183 634,183 633,183 633,183 632,183 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="632,205 633,205 633,205 634,205 635,205 635,204 636,204 637,203 637,203 637,202 638,201 638,201 638,200 638,199 638,198 638,198 637,197 637,196 637,196 636,195 635,195 635,194 634,194 633,194 633,194 632,194 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="632,216 633,216 633,216 634,216 635,216 635,215 636,215 637,214 637,214 637,213 638,212 638,212 638,211 638,210 638,209 638,209 637,208 637,207 637,207 636,206 635,206 635,205 634,205 633,205 633,205 632,205 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="632,227 633,227 633,227 634,227 635,227 635,226 636,226 637,225 637,225 637,224 638,223 638,223 638,222 638,221 638,220 638,220 637,219 637,218 637,218 636,217 635,217 635,216 634,216 633,216 633,216 632,216 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1888,183 1890,183 1892,182 1894,182 1896,181 1897,180 1899,179 1900,177 1901,175 1902,174 1902,172 1902,170 1902,168 1902,166 1901,165 1900,163 1899,162 1897,160 1896,159 1894,158 1892,158 1890,157 1888,157 1887,157 1885,158 1883,158 1881,159 1880,160 1878,162 1877,163 1876,165 1875,166 1875,168 1875,170 " stroke="rgb(60,120,255)" stroke-width="0.0972"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1805,185 1807,185 1809,184 1811,184 1813,183 1814,182 1816,181 1817,179 1818,177 1819,176 1819,174 1819,172 1819,170 1819,168 1818,167 1817,165 1816,164 1814,162 1813,161 1811,160 1809,160 1807,159 1805,159 1804,159 1802,160 1800,160 1798,161 1797,162 1795,164 1794,165 1793,167 1792,168 1792,170 1792,172 " stroke="rgb(60,120,255)" stroke-width="0.0972"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1299,193 1300,193 1300,193 1301,193 1302,193 1302,192 1303,192 1304,191 1304,191 1304,190 1305,189 1305,189 1305,188 1305,187 1305,186 1305,186 1304,185 1304,184 1304,184 1303,183 1302,183 1302,182 1301,182 1300,182 1300,182 1299,182 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1299,204 1300,204 1300,204 1301,204 1302,204 1302,203 1303,203 1304,202 1304,202 1304,201 1305,200 1305,200 1305,199 1305,198 1305,197 1305,197 1304,196 1304,195 1304,195 1303,194 1302,194 1302,193 1301,193 1300,193 1300,193 1299,193 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1299,215 1300,215 1300,215 1301,215 1302,215 1302,214 1303,214 1304,213 1304,213 1304,212 1305,211 1305,211 1305,210 1305,209 1305,208 1305,208 1304,207 1304,206 1304,206 1303,205 1302,205 1302,204 1301,204 1300,204 1300,204 1299,204 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1299,226 1300,226 1300,226 1301,226 1302,226 1302,225 1303,225 1304,224 1304,224 1304,223 1305,222 1305,222 1305,221 1305,220 1305,219 1305,219 1304,218 1304,217 1304,217 1303,216 1302,216 1302,215 1301,215 1300,215 1300,215 1299,215 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1477,193 1478,193 1478,193 1479,193 1480,193 1480,192 1481,192 1482,191 1482,191 1482,190 1483,189 1483,189 1483,188 1483,187 1483,186 1483,186 1482,185 1482,184 1482,184 1481,183 1480,183 1480,182 1479,182 1478,182 1478,182 1477,182 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1477,204 1478,204 1478,204 1479,204 1480,204 1480,203 1481,203 1482,202 1482,202 1482,201 1483,200 1483,200 1483,199 1483,198 1483,197 1483,197 1482,196 1482,195 1482,195 1481,194 1480,194 1480,193 1479,193 1478,193 1478,193 1477,193 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1477,215 1478,215 1478,215 1479,215 1480,215 1480,214 1481,214 1482,213 1482,213 1482,212 1483,211 1483,211 1483,210 1483,209 1483,208 1483,208 1482,207 1482,206 1482,206 1481,205 1480,205 1480,204 1479,204 1478,204 1478,204 1477,204 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1477,226 1478,226 1478,226 1479,226 1480,226 1480,225 1481,225 1482,224 1482,224 1482,223 1483,222 1483,222 1483,221 1483,220 1483,219 1483,219 1482,218 1482,217 1482,217 1481,216 1480,216 1480,215 1479,215 1478,215 1478,215 1477,215 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="599" x2="583" y1="-491" y2="-507"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="583" x2="599" y1="-491" y2="-507"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="766" x2="782" y1="-491" y2="-507"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="782" x2="766" y1="-491" y2="-507"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="654" x2="654" y1="-339" y2="-339"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="654" x2="663" y1="-340" y2="-340"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="659" x2="659" y1="-337" y2="-343"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="656" x2="656" y1="-337" y2="-343"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="654" x2="654" y1="-320" y2="-320"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="654" x2="663" y1="-321" y2="-321"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="659" x2="659" y1="-318" y2="-324"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="656" x2="656" y1="-318" y2="-324"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="654" x2="654" y1="-301" y2="-301"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="654" x2="663" y1="-302" y2="-302"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="659" x2="659" y1="-299" y2="-305"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="656" x2="656" y1="-299" y2="-305"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="726" x2="726" y1="-339" y2="-339"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="726" x2="735" y1="-340" y2="-340"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="731" x2="731" y1="-337" y2="-343"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="728" x2="728" y1="-337" y2="-343"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="726" x2="726" y1="-320" y2="-320"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="726" x2="735" y1="-321" y2="-321"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="731" x2="731" y1="-318" y2="-324"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="728" x2="728" y1="-318" y2="-324"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="726" x2="726" y1="-301" y2="-301"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="726" x2="735" y1="-302" y2="-302"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="731" x2="731" y1="-299" y2="-305"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="728" x2="728" y1="-299" y2="-305"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="959" x2="981" y1="-381" y2="-381"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="959" x2="970" y1="-381" y2="-363"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="981" x2="970" y1="-381" y2="-363"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="959" x2="981" y1="-265" y2="-265"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="981" x2="970" y1="-265" y2="-283"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="959" x2="970" y1="-265" y2="-283"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1090" x2="1112" y1="-383" y2="-383"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1090" x2="1101" y1="-383" y2="-365"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1112" x2="1101" y1="-383" y2="-365"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1090" x2="1112" y1="-267" y2="-267"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1112" x2="1101" y1="-267" y2="-285"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1090" x2="1101" y1="-267" y2="-285"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1096" x2="949" y1="-420" y2="-420"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="998" x2="968" y1="-422" y2="-422"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="998" x2="998" y1="-470" y2="-422"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="977" x2="998" y1="-470" y2="-470"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1128" x2="1098" y1="-422" y2="-422"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1128" x2="1128" y1="-470" y2="-422"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1107" x2="1128" y1="-470" y2="-470"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1097" x2="966" y1="-514" y2="-514"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="962" x2="950" y1="-523" y2="-523"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="958" x2="954" y1="-529" y2="-529"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="960" x2="952" y1="-526" y2="-526"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="956" x2="956" y1="-523" y2="-517"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1097" x2="1085" y1="-522" y2="-522"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1093" x2="1089" y1="-528" y2="-528"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1095" x2="1087" y1="-525" y2="-525"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1091" x2="1091" y1="-522" y2="-516"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="1026" x2="1043" y1="-745" y2="-745"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="1035" x2="1043" y1="-761" y2="-745"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="1035" x2="1026" y1="-761" y2="-745"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="1027" x2="1035" y1="-787" y2="-795"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="1035" x2="1043" y1="-795" y2="-787"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="1035" x2="1035" y1="-803" y2="-795"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="1027" x2="1035" y1="-795" y2="-795"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="984" x2="1011" y1="-794" y2="-794"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="984" x2="984" y1="-801" y2="-788"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="978" x2="978" y1="-796" y2="-792"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="981" x2="981" y1="-798" y2="-791"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="1035" x2="1035" y1="-819" y2="-852"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="1026" x2="1043" y1="-852" y2="-852"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="1035" x2="1043" y1="-868" y2="-852"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="1035" x2="1026" y1="-868" y2="-852"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="998" x2="998" y1="-794" y2="-828"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="998" x2="1035" y1="-828" y2="-852"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(60,120,255)" stroke-dasharray="10 5 " stroke-width="0.510204" x1="1035" x2="1035" y1="-868" y2="-876"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="1035" x2="1035" y1="-704" y2="-731"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1029" x2="1041" y1="-704" y2="-704"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1033" x2="1037" y1="-698" y2="-698"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1031" x2="1039" y1="-701" y2="-701"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="969" x2="1035" y1="-678" y2="-731"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="1101" x2="1035" y1="-678" y2="-731"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1310" x2="1294" y1="-494" y2="-510"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1294" x2="1310" y1="-494" y2="-510"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1477" x2="1493" y1="-494" y2="-510"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1493" x2="1477" y1="-494" y2="-510"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1365" x2="1365" y1="-342" y2="-342"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1365" x2="1374" y1="-343" y2="-343"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1370" x2="1370" y1="-340" y2="-346"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1367" x2="1367" y1="-340" y2="-346"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1365" x2="1365" y1="-323" y2="-323"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1365" x2="1374" y1="-324" y2="-324"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1370" x2="1370" y1="-321" y2="-327"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1367" x2="1367" y1="-321" y2="-327"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1365" x2="1365" y1="-304" y2="-304"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1365" x2="1374" y1="-305" y2="-305"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1370" x2="1370" y1="-302" y2="-308"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1367" x2="1367" y1="-302" y2="-308"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1437" x2="1437" y1="-342" y2="-342"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1437" x2="1446" y1="-343" y2="-343"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1442" x2="1442" y1="-340" y2="-346"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1439" x2="1439" y1="-340" y2="-346"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1437" x2="1437" y1="-323" y2="-323"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1437" x2="1446" y1="-324" y2="-324"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1442" x2="1442" y1="-321" y2="-327"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1439" x2="1439" y1="-321" y2="-327"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1437" x2="1437" y1="-304" y2="-304"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1437" x2="1446" y1="-305" y2="-305"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1442" x2="1442" y1="-302" y2="-308"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1439" x2="1439" y1="-302" y2="-308"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="108" x2="108" y1="-105" y2="-105"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="108" x2="117" y1="-106" y2="-106"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="113" x2="113" y1="-103" y2="-109"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="110" x2="110" y1="-103" y2="-109"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="108" x2="108" y1="-86" y2="-86"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="108" x2="117" y1="-87" y2="-87"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="113" x2="113" y1="-84" y2="-90"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="110" x2="110" y1="-84" y2="-90"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="109" x2="109" y1="73" y2="73"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="109" x2="118" y1="73" y2="73"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="114" x2="114" y1="76" y2="70"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="111" x2="111" y1="76" y2="70"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.491429" x1="101" x2="101" y1="146" y2="170"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.48" x1="88" x2="101" y1="170" y2="170"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.504" x1="101" x2="101" y1="183" y2="209"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="109" x2="109" y1="197" y2="197"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="109" x2="118" y1="197" y2="197"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="114" x2="114" y1="200" y2="194"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="111" x2="111" y1="200" y2="194"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="107" x2="95" y1="209" y2="209"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="103" x2="99" y1="215" y2="215"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="105" x2="97" y1="212" y2="212"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(60,120,255)" stroke-dasharray="10 5 " stroke-width="1" x1="450" x2="464" y1="100" y2="100"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(60,120,255)" stroke-dasharray="10 5 " stroke-width="1" x1="484" x2="498" y1="28" y2="28"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.388889" x1="422" x2="415" y1="180" y2="173"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.388889" x1="415" x2="422" y1="180" y2="173"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.715488" x1="454" x2="454" y1="174" y2="183"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="454" x2="454" y1="227" y2="280"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="460" x2="454" y1="267" y2="280"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="461" x2="461" y1="-105" y2="-105"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="461" x2="470" y1="-106" y2="-106"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="466" x2="466" y1="-103" y2="-109"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="463" x2="463" y1="-103" y2="-109"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="461" x2="461" y1="-86" y2="-86"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="461" x2="470" y1="-87" y2="-87"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="466" x2="466" y1="-84" y2="-90"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="463" x2="463" y1="-84" y2="-90"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="350" x2="350" y1="-106" y2="-106"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="350" x2="359" y1="-107" y2="-107"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="355" x2="355" y1="-104" y2="-110"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="352" x2="352" y1="-104" y2="-110"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="350" x2="350" y1="-87" y2="-87"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="350" x2="359" y1="-88" y2="-88"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="355" x2="355" y1="-85" y2="-91"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="352" x2="352" y1="-85" y2="-91"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="639" x2="639" y1="-106" y2="-106"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="639" x2="648" y1="-107" y2="-107"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="644" x2="644" y1="-104" y2="-110"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="641" x2="641" y1="-104" y2="-110"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="639" x2="639" y1="-87" y2="-87"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="639" x2="648" y1="-88" y2="-88"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="644" x2="644" y1="-85" y2="-91"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="641" x2="641" y1="-85" y2="-91"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(60,120,255)" stroke-dasharray="10 5 " stroke-width="1" x1="628" x2="642" y1="100" y2="100"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(60,120,255)" stroke-dasharray="10 5 " stroke-width="1" x1="662" x2="676" y1="28" y2="28"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.388889" x1="600" x2="593" y1="180" y2="173"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.388889" x1="593" x2="600" y1="180" y2="173"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.715488" x1="632" x2="632" y1="174" y2="183"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="632" x2="632" y1="227" y2="280"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="638" x2="632" y1="267" y2="280"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1895" x2="1895" y1="-105" y2="-105"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1895" x2="1904" y1="-106" y2="-106"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1900" x2="1900" y1="-103" y2="-109"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1897" x2="1897" y1="-103" y2="-109"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1895" x2="1895" y1="-86" y2="-86"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1895" x2="1904" y1="-87" y2="-87"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1900" x2="1900" y1="-84" y2="-90"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1897" x2="1897" y1="-84" y2="-90"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1896" x2="1896" y1="30" y2="30"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1896" x2="1905" y1="30" y2="30"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1901" x2="1901" y1="33" y2="27"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1898" x2="1898" y1="33" y2="27"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.491429" x1="1888" x2="1888" y1="146" y2="170"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.48" x1="1875" x2="1888" y1="170" y2="170"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.504" x1="1888" x2="1888" y1="183" y2="209"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1896" x2="1896" y1="197" y2="197"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1896" x2="1905" y1="197" y2="197"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1901" x2="1901" y1="200" y2="194"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1898" x2="1898" y1="200" y2="194"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1894" x2="1882" y1="209" y2="209"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1890" x2="1886" y1="215" y2="215"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1892" x2="1884" y1="212" y2="212"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1813" x2="1813" y1="31" y2="31"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1813" x2="1822" y1="31" y2="31"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1818" x2="1818" y1="34" y2="28"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1815" x2="1815" y1="34" y2="28"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.491429" x1="1805" x2="1805" y1="148" y2="172"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.48" x1="1792" x2="1805" y1="172" y2="172"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.504" x1="1805" x2="1805" y1="185" y2="211"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1813" x2="1813" y1="199" y2="199"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1813" x2="1822" y1="199" y2="199"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1818" x2="1818" y1="202" y2="196"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1815" x2="1815" y1="202" y2="196"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1811" x2="1799" y1="211" y2="211"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1809" x2="1801" y1="214" y2="214"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(60,120,255)" stroke-dasharray="10 5 " stroke-width="1" x1="1295" x2="1309" y1="99" y2="99"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(60,120,255)" stroke-dasharray="10 5 " stroke-width="1" x1="1330" x2="1344" y1="27" y2="27"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.388889" x1="1267" x2="1260" y1="179" y2="172"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.388889" x1="1260" x2="1267" y1="179" y2="172"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.715488" x1="1299" x2="1299" y1="173" y2="182"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1299" x2="1299" y1="226" y2="279"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1305" x2="1299" y1="266" y2="279"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1306" x2="1306" y1="-106" y2="-106"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1306" x2="1315" y1="-107" y2="-107"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1311" x2="1311" y1="-104" y2="-110"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1308" x2="1308" y1="-104" y2="-110"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1306" x2="1306" y1="-87" y2="-87"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1306" x2="1315" y1="-88" y2="-88"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1311" x2="1311" y1="-85" y2="-91"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1308" x2="1308" y1="-85" y2="-91"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1612" x2="1612" y1="-109" y2="-109"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1612" x2="1621" y1="-110" y2="-110"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1617" x2="1617" y1="-107" y2="-113"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1614" x2="1614" y1="-107" y2="-113"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1612" x2="1612" y1="-90" y2="-90"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1612" x2="1621" y1="-91" y2="-91"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1617" x2="1617" y1="-88" y2="-94"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1614" x2="1614" y1="-88" y2="-94"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1484" x2="1484" y1="-107" y2="-107"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1484" x2="1493" y1="-108" y2="-108"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1489" x2="1489" y1="-105" y2="-111"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1486" x2="1486" y1="-105" y2="-111"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1484" x2="1484" y1="-88" y2="-88"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1484" x2="1493" y1="-89" y2="-89"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1489" x2="1489" y1="-86" y2="-92"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1486" x2="1486" y1="-86" y2="-92"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(60,120,255)" stroke-dasharray="10 5 " stroke-width="1" x1="1473" x2="1487" y1="99" y2="99"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(60,120,255)" stroke-dasharray="10 5 " stroke-width="1" x1="1507" x2="1521" y1="27" y2="27"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.388889" x1="1445" x2="1438" y1="179" y2="172"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.388889" x1="1438" x2="1445" y1="179" y2="172"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.715488" x1="1477" x2="1477" y1="173" y2="182"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1477" x2="1477" y1="226" y2="279"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1483" x2="1477" y1="266" y2="279"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-194496">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 638.000000 -704.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29525" ObjectName="SW-CX_NHNQ.CX_NHNQ_111BK"/>
     <cge:Meas_Ref ObjectId="194496"/>
    <cge:TPSR_Ref TObjectID="29525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194544">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1348.000000 -711.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29536" ObjectName="SW-CX_NHNQ.CX_NHNQ_112BK"/>
     <cge:Meas_Ref ObjectId="194544"/>
    <cge:TPSR_Ref TObjectID="29536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194507">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 638.000000 -350.000000)" xlink:href="#breaker2:shape10_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29529" ObjectName="SW-CX_NHNQ.CX_NHNQ_201BBK"/>
     <cge:Meas_Ref ObjectId="194507"/>
    <cge:TPSR_Ref TObjectID="29529"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194503">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 710.000000 -350.000000)" xlink:href="#breaker2:shape10_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29526" ObjectName="SW-CX_NHNQ.CX_NHNQ_201ABK"/>
     <cge:Meas_Ref ObjectId="194503"/>
    <cge:TPSR_Ref TObjectID="29526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194555">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1349.000000 -353.000000)" xlink:href="#breaker2:shape10_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29540" ObjectName="SW-CX_NHNQ.CX_NHNQ_202BBK"/>
     <cge:Meas_Ref ObjectId="194555"/>
    <cge:TPSR_Ref TObjectID="29540"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194551">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1421.000000 -353.000000)" xlink:href="#breaker2:shape10_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29537" ObjectName="SW-CX_NHNQ.CX_NHNQ_202ABK"/>
     <cge:Meas_Ref ObjectId="194551"/>
    <cge:TPSR_Ref TObjectID="29537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 92.000000 -31.000000)" xlink:href="#breaker2:shape10_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 445.000000 -31.000000)" xlink:href="#breaker2:shape10_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 334.000000 -32.000000)" xlink:href="#breaker2:shape10_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 623.000000 -32.000000)" xlink:href="#breaker2:shape10_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1879.000000 -31.000000)" xlink:href="#breaker2:shape10_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1290.000000 -32.000000)" xlink:href="#breaker2:shape10_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1596.000000 -35.000000)" xlink:href="#breaker2:shape10_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1468.000000 -33.000000)" xlink:href="#breaker2:shape10_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_202c8a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 381.000000 -363.000000)" xlink:href="#voltageTransformer:shape124"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_202d360">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 236.000000 -361.000000)" xlink:href="#voltageTransformer:shape124"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_205b5a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1779.000000 -363.000000)" xlink:href="#voltageTransformer:shape124"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fbebb0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1634.000000 -361.000000)" xlink:href="#voltageTransformer:shape124"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f62080">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1503.000000 -774.000000)" xlink:href="#voltageTransformer:shape125"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f66e30">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 451.000000 -768.000000)" xlink:href="#voltageTransformer:shape125"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1001.000000 -149.000000)" xlink:href="#capacitor:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 81.000000 131.000000)" xlink:href="#capacitor:shape42"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1868.000000 131.000000)" xlink:href="#capacitor:shape42"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1785.000000 133.000000)" xlink:href="#capacitor:shape42"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_NHNQ.CX_NHNQ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="42104"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 613.000000 -556.000000)" xlink:href="#transformer2:shape81_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 613.000000 -556.000000)" xlink:href="#transformer2:shape81_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="29547" ObjectName="TF-CX_NHNQ.CX_NHNQ_1T"/>
    <cge:TPSR_Ref TObjectID="29547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_NHNQ.CX_NHNQ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="42108"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1324.000000 -559.000000)" xlink:href="#transformer2:shape81_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1324.000000 -559.000000)" xlink:href="#transformer2:shape81_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="29548" ObjectName="TF-CX_NHNQ.CX_NHNQ_2T"/>
    <cge:TPSR_Ref TObjectID="29548"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1faa800">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 752.000000 -451.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_202db70">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 547.000000 -452.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f0f4b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 293.000000 -364.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f102c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 437.000000 -363.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fd8bf0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1463.000000 -454.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fd9840">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1258.000000 -455.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1efec90">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1691.000000 -364.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1effad0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1835.000000 -363.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fc6bd0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 12.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1eb44a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 373.000000 251.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1edaa80">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 551.000000 251.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f5ac90">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1906.000000 14.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f660c0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1539.000000 -871.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f6b160">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 374.000000 -867.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e947e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1218.000000 250.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1eec770">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1396.000000 250.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -241.000000 -849.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-194445" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 580.000000 -745.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194445" ObjectName="CX_NHNQ:CX_NHNQ_111BK_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-194446" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 580.000000 -731.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194446" ObjectName="CX_NHNQ:CX_NHNQ_111BK_Ub"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-194447" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 580.000000 -717.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194447" ObjectName="CX_NHNQ:CX_NHNQ_111BK_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-194448" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 580.000000 -703.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194448" ObjectName="CX_NHNQ:CX_NHNQ_111BK_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-194453" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 580.000000 -689.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194453" ObjectName="CX_NHNQ:CX_NHNQ_111BK_Hz"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-194469" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1276.000000 -729.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194469" ObjectName="CX_NHNQ:CX_NHNQ_112BK_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-194470" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1276.000000 -715.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194470" ObjectName="CX_NHNQ:CX_NHNQ_112BK_Ub"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-194471" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1276.000000 -701.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194471" ObjectName="CX_NHNQ:CX_NHNQ_112BK_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-194472" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1276.000000 -687.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194472" ObjectName="CX_NHNQ:CX_NHNQ_112BK_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-194478" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1276.000000 -673.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194478" ObjectName="CX_NHNQ:CX_NHNQ_112BK_Hz"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-215374" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -214.000000 -719.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215374" ObjectName="CX_NHQ:CX_NHQ_sumP"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-194451" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 766.000000 -752.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194451" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29525"/>
     <cge:Term_Ref ObjectID="42058"/>
    <cge:TPSR_Ref TObjectID="29525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-194452" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 766.000000 -752.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194452" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29525"/>
     <cge:Term_Ref ObjectID="42058"/>
    <cge:TPSR_Ref TObjectID="29525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-194442" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 766.000000 -752.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194442" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29525"/>
     <cge:Term_Ref ObjectID="42058"/>
    <cge:TPSR_Ref TObjectID="29525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-194475" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1480.000000 -752.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194475" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29536"/>
     <cge:Term_Ref ObjectID="42080"/>
    <cge:TPSR_Ref TObjectID="29536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-194476" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1480.000000 -752.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194476" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29536"/>
     <cge:Term_Ref ObjectID="42080"/>
    <cge:TPSR_Ref TObjectID="29536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-194466" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1480.000000 -752.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194466" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29536"/>
     <cge:Term_Ref ObjectID="42080"/>
    <cge:TPSR_Ref TObjectID="29536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-194462" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 596.000000 -396.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194462" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29529"/>
     <cge:Term_Ref ObjectID="42066"/>
    <cge:TPSR_Ref TObjectID="29529"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-194463" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 596.000000 -396.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194463" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29529"/>
     <cge:Term_Ref ObjectID="42066"/>
    <cge:TPSR_Ref TObjectID="29529"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="I" PreSymbol="0" appendix="" decimal="2" id="ME-194460" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 596.000000 -396.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194460" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29529"/>
     <cge:Term_Ref ObjectID="42066"/>
    <cge:TPSR_Ref TObjectID="29529"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-194487" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1306.000000 -396.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194487" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29540"/>
     <cge:Term_Ref ObjectID="42088"/>
    <cge:TPSR_Ref TObjectID="29540"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-194488" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1306.000000 -396.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194488" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29540"/>
     <cge:Term_Ref ObjectID="42088"/>
    <cge:TPSR_Ref TObjectID="29540"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="I" PreSymbol="0" appendix="" decimal="2" id="ME-194485" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1306.000000 -396.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194485" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29540"/>
     <cge:Term_Ref ObjectID="42088"/>
    <cge:TPSR_Ref TObjectID="29540"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-194481" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1481.000000 -396.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194481" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29537"/>
     <cge:Term_Ref ObjectID="42082"/>
    <cge:TPSR_Ref TObjectID="29537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-194482" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1481.000000 -396.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194482" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29537"/>
     <cge:Term_Ref ObjectID="42082"/>
    <cge:TPSR_Ref TObjectID="29537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="I" PreSymbol="0" appendix="" decimal="2" id="ME-194479" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1481.000000 -396.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194479" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29537"/>
     <cge:Term_Ref ObjectID="42082"/>
    <cge:TPSR_Ref TObjectID="29537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-194456" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 770.000000 -396.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194456" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29526"/>
     <cge:Term_Ref ObjectID="42060"/>
    <cge:TPSR_Ref TObjectID="29526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-194457" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 770.000000 -396.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194457" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29526"/>
     <cge:Term_Ref ObjectID="42060"/>
    <cge:TPSR_Ref TObjectID="29526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="I" PreSymbol="0" appendix="" decimal="2" id="ME-194454" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 770.000000 -396.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194454" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29526"/>
     <cge:Term_Ref ObjectID="42060"/>
    <cge:TPSR_Ref TObjectID="29526"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="-230" y="-908"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="-230" y="-908"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-278" y="-922"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-278" y="-922"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="-230" y="-908"/></g>
   <g href="cx_索引_接线图_客户变.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-278" y="-922"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e7a5f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 706.000000 751.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e7b4f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 720.000000 721.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e7bdb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 695.000000 736.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e7c400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1419.000000 751.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e7c6a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1433.000000 721.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e7c8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1408.000000 736.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e7cc10" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 508.000000 700.033333) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e7d4f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 516.000000 745.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e7d6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 516.000000 730.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e7d900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 516.000000 715.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e7de80" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 524.000000 686.033333) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e81290" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 1210.000000 684.033333) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e81500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1218.000000 729.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e81740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1218.000000 714.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e81980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1218.000000 699.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e81bc0" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 1226.000000 670.033333) translate(0,12)">F(Hz):</text>
   <metadata/></g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-194511">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 638.000000 -751.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29532" ObjectName="SW-CX_NHNQ.CX_NHNQ_2616SW"/>
     <cge:Meas_Ref ObjectId="194511"/>
    <cge:TPSR_Ref TObjectID="29532"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194559">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1348.000000 -758.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29543" ObjectName="SW-CX_NHNQ.CX_NHNQ_2626SW"/>
     <cge:Meas_Ref ObjectId="194559"/>
    <cge:TPSR_Ref TObjectID="29543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194515">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 584.000000 -803.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29533" ObjectName="SW-CX_NHNQ.CX_NHNQ_26167SW"/>
     <cge:Meas_Ref ObjectId="194515"/>
    <cge:TPSR_Ref TObjectID="29533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194563">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1375.000000 -810.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29544" ObjectName="SW-CX_NHNQ.CX_NHNQ_26267SW"/>
     <cge:Meas_Ref ObjectId="194563"/>
    <cge:TPSR_Ref TObjectID="29544"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194516">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 559.000000 -869.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29534" ObjectName="SW-CX_NHNQ.CX_NHNQ_2619SW"/>
     <cge:Meas_Ref ObjectId="194516"/>
    <cge:TPSR_Ref TObjectID="29534"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194564">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1399.000000 -874.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29545" ObjectName="SW-CX_NHNQ.CX_NHNQ_2629SW"/>
     <cge:Meas_Ref ObjectId="194564"/>
    <cge:TPSR_Ref TObjectID="29545"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194517">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 524.000000 -915.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29535" ObjectName="SW-CX_NHNQ.CX_NHNQ_26197SW"/>
     <cge:Meas_Ref ObjectId="194517"/>
    <cge:TPSR_Ref TObjectID="29535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194565">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1491.000000 -922.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29546" ObjectName="SW-CX_NHNQ.CX_NHNQ_26297SW"/>
     <cge:Meas_Ref ObjectId="194565"/>
    <cge:TPSR_Ref TObjectID="29546"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194508">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 637.000000 -392.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29530" ObjectName="SW-CX_NHNQ.CX_NHNQ_201BXC"/>
     <cge:Meas_Ref ObjectId="194508"/>
    <cge:TPSR_Ref TObjectID="29530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194508">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 637.000000 -261.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29531" ObjectName="SW-CX_NHNQ.CX_NHNQ_201BXC1"/>
     <cge:Meas_Ref ObjectId="194508"/>
    <cge:TPSR_Ref TObjectID="29531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194504">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 709.000000 -392.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29527" ObjectName="SW-CX_NHNQ.CX_NHNQ_201XC"/>
     <cge:Meas_Ref ObjectId="194504"/>
    <cge:TPSR_Ref TObjectID="29527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194504">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 709.000000 -261.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29528" ObjectName="SW-CX_NHNQ.CX_NHNQ_201XC1"/>
     <cge:Meas_Ref ObjectId="194504"/>
    <cge:TPSR_Ref TObjectID="29528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 260.000000 -283.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 405.000000 -286.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 874.000000 -235.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1149.000000 -235.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 874.000000 -203.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1149.000000 -203.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 961.000000 -396.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1092.000000 -398.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 961.000000 -491.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1092.000000 -490.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194556">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1348.000000 -395.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29541" ObjectName="SW-CX_NHNQ.CX_NHNQ_202BXC"/>
     <cge:Meas_Ref ObjectId="194556"/>
    <cge:TPSR_Ref TObjectID="29541"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194556">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1348.000000 -264.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29542" ObjectName="SW-CX_NHNQ.CX_NHNQ_202BXC1"/>
     <cge:Meas_Ref ObjectId="194556"/>
    <cge:TPSR_Ref TObjectID="29542"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194552">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1420.000000 -395.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29538" ObjectName="SW-CX_NHNQ.CX_NHNQ_202AXC"/>
     <cge:Meas_Ref ObjectId="194552"/>
    <cge:TPSR_Ref TObjectID="29538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194552">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1420.000000 -264.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29539" ObjectName="SW-CX_NHNQ.CX_NHNQ_202AXC1"/>
     <cge:Meas_Ref ObjectId="194552"/>
    <cge:TPSR_Ref TObjectID="29539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1658.000000 -283.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1803.000000 -286.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 91.000000 -114.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 91.000000 -2.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 92.000000 57.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 445.000000 124.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 420.000000 124.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 479.000000 51.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 444.000000 -114.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 444.000000 -2.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 333.000000 -115.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 333.000000 -3.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 622.000000 -115.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 622.000000 -2.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 623.000000 124.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 598.000000 124.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 657.000000 51.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1878.000000 -114.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1878.000000 -2.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1290.000000 123.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1265.000000 123.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1325.000000 50.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1289.000000 -115.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1289.000000 -3.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1595.000000 -118.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1595.000000 -6.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1467.000000 -116.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1467.000000 -3.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1468.000000 123.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1443.000000 123.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1502.000000 50.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1796.000000 90.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1879.000000 89.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 638.000000 -929.000000)" xlink:href="#load:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1348.000000 -936.000000)" xlink:href="#load:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-220KV" id="g_20437a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-680 647,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="29547@0" ObjectIDZND0="29525@0" Pin0InfoVect0LinkObjId="SW-194496_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1faa230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="647,-680 647,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2043990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-739 647,-756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29525@1" ObjectIDZND0="29532@0" Pin0InfoVect0LinkObjId="SW-194511_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194496_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="647,-739 647,-756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2043b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-874 647,-934 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="29534@x" ObjectIDND1="29533@x" ObjectIDND2="29532@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-194516_0" Pin1InfoVect1LinkObjId="SW-194515_0" Pin1InfoVect2LinkObjId="SW-194511_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="647,-874 647,-934 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2043d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-874 600,-874 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="29533@x" ObjectIDND1="29532@x" ObjectIDND2="0@x" ObjectIDZND0="29534@1" Pin0InfoVect0LinkObjId="SW-194516_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-194515_0" Pin1InfoVect1LinkObjId="SW-194511_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="647,-874 600,-874 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2043f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="565,-920 576,-920 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29535@1" ObjectIDZND0="g_2071ae0@0" Pin0InfoVect0LinkObjId="g_2071ae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194517_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="565,-920 576,-920 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2044150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-792 647,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="29532@1" ObjectIDZND0="29533@x" ObjectIDZND1="29534@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-194515_0" Pin0InfoVect1LinkObjId="SW-194516_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194511_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="647,-792 647,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2044340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-808 647,-874 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="load" ObjectIDND0="29533@x" ObjectIDND1="29532@x" ObjectIDZND0="29534@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-194516_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-194515_0" Pin1InfoVect1LinkObjId="SW-194511_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="647,-808 647,-874 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2044530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="567,-808 589,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2017320@0" ObjectIDZND0="29533@0" Pin0InfoVect0LinkObjId="SW-194515_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2017320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="567,-808 589,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2044720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="625,-808 647,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" EndDevType2="switch" ObjectIDND0="29533@1" ObjectIDZND0="29534@x" ObjectIDZND1="0@x" ObjectIDZND2="29532@x" Pin0InfoVect0LinkObjId="SW-194516_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-194511_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194515_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="625,-808 647,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2023d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="518,-874 518,-920 529,-920 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="29534@x" ObjectIDND1="g_1f66e30@0" ObjectIDND2="g_1f6b160@0" ObjectIDZND0="29535@0" Pin0InfoVect0LinkObjId="SW-194517_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-194516_0" Pin1InfoVect1LinkObjId="g_1f66e30_0" Pin1InfoVect2LinkObjId="g_1f6b160_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="518,-874 518,-920 529,-920 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2016840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="518,-874 564,-874 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="29535@x" ObjectIDND1="g_1f66e30@0" ObjectIDND2="g_1f6b160@0" ObjectIDZND0="29534@0" Pin0InfoVect0LinkObjId="SW-194516_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-194517_0" Pin1InfoVect1LinkObjId="g_1f66e30_0" Pin1InfoVect2LinkObjId="g_1f6b160_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="518,-874 564,-874 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_20221b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1357,-684 1357,-719 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="29548@0" ObjectIDZND0="29536@0" Pin0InfoVect0LinkObjId="SW-194544_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fd8560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1357,-684 1357,-719 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_20223a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1357,-746 1357,-763 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29536@1" ObjectIDZND0="29543@0" Pin0InfoVect0LinkObjId="SW-194559_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194544_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1357,-746 1357,-763 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2022590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1357,-816 1380,-816 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="29545@x" ObjectIDND1="0@x" ObjectIDND2="29543@x" ObjectIDZND0="29544@0" Pin0InfoVect0LinkObjId="SW-194563_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-194564_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-194559_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1357,-816 1380,-816 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2022d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1357,-799 1357,-817 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="29543@1" ObjectIDZND0="29544@x" ObjectIDZND1="29545@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-194563_0" Pin0InfoVect1LinkObjId="SW-194564_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194559_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1357,-799 1357,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fecce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1416,-815 1434,-815 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29544@1" ObjectIDZND0="g_1f4b080@0" Pin0InfoVect0LinkObjId="g_1f4b080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194563_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1416,-815 1434,-815 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1feced0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1357,-878 1405,-878 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="29544@x" ObjectIDND1="29543@x" ObjectIDND2="0@x" ObjectIDZND0="29545@0" Pin0InfoVect0LinkObjId="SW-194564_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-194563_0" Pin1InfoVect1LinkObjId="SW-194559_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1357,-878 1405,-878 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fed720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1357,-878 1357,-817 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29545@x" ObjectIDND1="0@x" ObjectIDZND0="29544@x" ObjectIDZND1="29543@x" Pin0InfoVect0LinkObjId="SW-194563_0" Pin0InfoVect1LinkObjId="SW-194559_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-194564_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1357,-878 1357,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fed910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1357,-941 1357,-878 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="29545@x" ObjectIDZND1="29544@x" ObjectIDZND2="29543@x" Pin0InfoVect0LinkObjId="SW-194564_0" Pin0InfoVect1LinkObjId="SW-194563_0" Pin0InfoVect2LinkObjId="SW-194559_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1357,-941 1357,-878 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fa8db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1440,-879 1481,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="29545@1" ObjectIDZND0="29546@x" ObjectIDZND1="g_1f62080@0" ObjectIDZND2="g_1f660c0@0" Pin0InfoVect0LinkObjId="SW-194565_0" Pin0InfoVect1LinkObjId="g_1f62080_0" Pin0InfoVect2LinkObjId="g_1f660c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194564_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1440,-879 1481,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fa8fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1496,-927 1481,-927 1481,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="29546@0" ObjectIDZND0="29545@x" ObjectIDZND1="g_1f62080@0" ObjectIDZND2="g_1f660c0@0" Pin0InfoVect0LinkObjId="SW-194564_0" Pin0InfoVect1LinkObjId="g_1f62080_0" Pin0InfoVect2LinkObjId="g_1f660c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194565_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1496,-927 1481,-927 1481,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fa9190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1532,-927 1550,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29546@1" ObjectIDZND0="g_2043170@0" Pin0InfoVect0LinkObjId="g_2043170_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194565_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1532,-927 1550,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fa9380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-537 591,-537 591,-515 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="29547@x" ObjectIDND1="g_202db70@0" ObjectIDND2="29530@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1faa230_0" Pin1InfoVect1LinkObjId="g_202db70_0" Pin1InfoVect2LinkObjId="SW-194508_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="647,-537 591,-537 591,-515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fa9570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-567 647,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="29547@1" ObjectIDZND0="g_202db70@0" ObjectIDZND1="29530@x" Pin0InfoVect0LinkObjId="g_202db70_0" Pin0InfoVect1LinkObjId="SW-194508_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1faa230_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="647,-567 647,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fa9e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-537 776,-537 776,-515 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer2" ObjectIDND0="29527@x" ObjectIDND1="g_1faa800@0" ObjectIDND2="29547@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-194504_0" Pin1InfoVect1LinkObjId="g_1faa800_0" Pin1InfoVect2LinkObjId="g_1faa230_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="719,-537 776,-537 776,-515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1faa230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-537 719,-550 672,-578 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="29527@x" ObjectIDND1="g_1faa800@0" ObjectIDZND0="29547@x" Pin0InfoVect0LinkObjId="g_1faa610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-194504_0" Pin1InfoVect1LinkObjId="g_1faa800_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-537 719,-550 672,-578 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1faa420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-459 604,-459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="29547@x" ObjectIDND1="29530@x" ObjectIDZND0="g_202db70@0" Pin0InfoVect0LinkObjId="g_202db70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1faa230_0" Pin1InfoVect1LinkObjId="SW-194508_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="647,-459 604,-459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1faa610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-459 647,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_202db70@0" ObjectIDND1="29530@x" ObjectIDZND0="29547@x" Pin0InfoVect0LinkObjId="g_1faa230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_202db70_0" Pin1InfoVect1LinkObjId="SW-194508_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="647,-459 647,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1f9e870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-416 647,-459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="29530@0" ObjectIDZND0="g_202db70@0" ObjectIDZND1="29547@x" Pin0InfoVect0LinkObjId="g_202db70_0" Pin0InfoVect1LinkObjId="g_1faa230_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194508_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="647,-416 647,-459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1f9ea60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-391 647,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29529@0" ObjectIDZND0="29530@1" Pin0InfoVect0LinkObjId="SW-194508_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194507_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="647,-391 647,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1f9ff40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-268 647,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29531@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194508_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="647,-268 647,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1f034b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-416 719,-459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="29527@0" ObjectIDZND0="29547@x" ObjectIDZND1="g_1faa800@0" Pin0InfoVect0LinkObjId="g_1faa230_0" Pin0InfoVect1LinkObjId="g_1faa800_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194504_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="719,-416 719,-459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1f036d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-391 719,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29526@0" ObjectIDZND0="29527@1" Pin0InfoVect0LinkObjId="SW-194504_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194503_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-391 719,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1f04c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-268 719,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29528@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194504_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-268 719,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1f04e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-537 719,-459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="29547@x" ObjectIDZND0="29527@x" ObjectIDZND1="g_1faa800@0" Pin0InfoVect0LinkObjId="SW-194504_0" Pin0InfoVect1LinkObjId="g_1faa800_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1faa230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="719,-537 719,-459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1f05080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-459 756,-459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="29547@x" ObjectIDND1="29527@x" ObjectIDZND0="g_1faa800@0" Pin0InfoVect0LinkObjId="g_1faa800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1faa230_0" Pin1InfoVect1LinkObjId="SW-194504_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-459 756,-459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1f052a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-355 647,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29529@1" ObjectIDZND0="29531@1" Pin0InfoVect0LinkObjId="SW-194508_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194507_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="647,-355 647,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1f054c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-355 719,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29526@1" ObjectIDZND0="29528@1" Pin0InfoVect0LinkObjId="SW-194504_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194503_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-355 719,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f056e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="269,-344 300,-344 300,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_202d360@0" ObjectIDND1="0@x" ObjectIDZND0="g_1f0f4b0@0" Pin0InfoVect0LinkObjId="g_1f0f4b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_202d360_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="269,-344 300,-344 300,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f0f290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="269,-344 269,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_1f0f4b0@0" ObjectIDND1="0@x" ObjectIDZND0="g_202d360@0" Pin0InfoVect0LinkObjId="g_202d360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f0f4b0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="269,-344 269,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f100a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="413,-343 444,-343 444,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_202c8a0@0" ObjectIDND1="0@x" ObjectIDZND0="g_1f102c0@0" Pin0InfoVect0LinkObjId="g_1f102c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_202c8a0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="413,-343 444,-343 444,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f10eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="414,-343 414,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_1f102c0@0" ObjectIDND1="0@x" ObjectIDZND0="g_202c8a0@0" Pin0InfoVect0LinkObjId="g_202c8a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f102c0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="414,-343 414,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f81f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="269,-208 269,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="269,-208 269,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f11950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="269,-324 269,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="g_1f0f4b0@0" ObjectIDZND1="g_202d360@0" Pin0InfoVect0LinkObjId="g_1f0f4b0_0" Pin0InfoVect1LinkObjId="g_202d360_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="269,-324 269,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f13d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="414,-240 414,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="414,-240 414,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f13fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="414,-327 414,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="g_1f102c0@0" ObjectIDZND1="g_202c8a0@0" Pin0InfoVect0LinkObjId="g_1f102c0_0" Pin0InfoVect1LinkObjId="g_202c8a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="414,-327 414,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f926f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="879,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="879,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f92910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="915,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="915,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f557e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1190,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1190,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f55a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1148,-240 1154,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1148,-240 1154,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ff7850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="879,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="879,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ff7a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="915,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="915,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ff9de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1190,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1190,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ffa000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1148,-208 1154,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1148,-208 1154,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f78100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1006,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="capacitor" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1006,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f78320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1066,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1066,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1fee1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="970,-159 970,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="970,-159 970,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ff05e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1101,-159 1101,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1101,-159 1101,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_202ac50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="970,-437 970,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="970,-437 970,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_202ae70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="970,-532 970,-678 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="970,-532 970,-678 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_202bd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1101,-439 1101,-495 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1101,-439 1101,-495 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_202bfc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1101,-531 1101,-678 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1101,-531 1101,-678 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1f55260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1358,-540 1302,-540 1302,-518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="29548@x" ObjectIDND1="g_1fd9840@0" ObjectIDND2="29541@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1fd8560_0" Pin1InfoVect1LinkObjId="g_1fd9840_0" Pin1InfoVect2LinkObjId="SW-194556_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1358,-540 1302,-540 1302,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1f55480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1358,-570 1358,-540 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="29548@1" ObjectIDZND0="g_1fd9840@0" ObjectIDZND1="29541@x" Pin0InfoVect0LinkObjId="g_1fd9840_0" Pin0InfoVect1LinkObjId="SW-194556_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fd8560_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1358,-570 1358,-540 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fd8110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1430,-540 1487,-540 1487,-518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="29548@x" ObjectIDND1="29538@x" ObjectIDND2="g_1fd8bf0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1fd8560_0" Pin1InfoVect1LinkObjId="SW-194552_0" Pin1InfoVect2LinkObjId="g_1fd8bf0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1430,-540 1487,-540 1487,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fd8560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1430,-540 1430,-553 1383,-581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="29538@x" ObjectIDND1="g_1fd8bf0@0" ObjectIDZND0="29548@x" Pin0InfoVect0LinkObjId="g_1fd89c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-194552_0" Pin1InfoVect1LinkObjId="g_1fd8bf0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1430,-540 1430,-553 1383,-581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fd8790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1358,-462 1315,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="29548@x" ObjectIDND1="29541@x" ObjectIDZND0="g_1fd9840@0" Pin0InfoVect0LinkObjId="g_1fd9840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1fd8560_0" Pin1InfoVect1LinkObjId="SW-194556_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1358,-462 1315,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fd89c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1358,-462 1358,-540 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_1fd9840@0" ObjectIDND1="29541@x" ObjectIDZND0="29548@x" Pin0InfoVect0LinkObjId="g_1fd8560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1fd9840_0" Pin1InfoVect1LinkObjId="SW-194556_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1358,-462 1358,-540 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fcd130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1358,-419 1358,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="29541@0" ObjectIDZND0="29548@x" ObjectIDZND1="g_1fd9840@0" Pin0InfoVect0LinkObjId="g_1fd8560_0" Pin0InfoVect1LinkObjId="g_1fd9840_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194556_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1358,-419 1358,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fcd390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1358,-394 1358,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29540@0" ObjectIDZND0="29541@1" Pin0InfoVect0LinkObjId="SW-194556_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194555_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1358,-394 1358,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1fceb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1358,-271 1358,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29542@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194556_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1358,-271 1358,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2054220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1430,-419 1430,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="29538@0" ObjectIDZND0="29548@x" ObjectIDZND1="g_1fd8bf0@0" Pin0InfoVect0LinkObjId="g_1fd8560_0" Pin0InfoVect1LinkObjId="g_1fd8bf0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194552_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1430,-419 1430,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2054450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1430,-394 1430,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29537@0" ObjectIDZND0="29538@1" Pin0InfoVect0LinkObjId="SW-194552_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194551_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1430,-394 1430,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2055d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1430,-271 1430,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29539@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194552_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1430,-271 1430,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2055fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1430,-540 1430,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="29548@x" ObjectIDZND0="29538@x" ObjectIDZND1="g_1fd8bf0@0" Pin0InfoVect0LinkObjId="SW-194552_0" Pin0InfoVect1LinkObjId="g_1fd8bf0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fd8560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1430,-540 1430,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2056220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1430,-462 1467,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="29548@x" ObjectIDND1="29538@x" ObjectIDZND0="g_1fd8bf0@0" Pin0InfoVect0LinkObjId="g_1fd8bf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1fd8560_0" Pin1InfoVect1LinkObjId="SW-194552_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1430,-462 1467,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2056480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1358,-358 1358,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29540@1" ObjectIDZND0="29542@1" Pin0InfoVect0LinkObjId="SW-194556_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194555_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1358,-358 1358,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_20566e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1430,-358 1430,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29537@1" ObjectIDZND0="29539@1" Pin0InfoVect0LinkObjId="SW-194552_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194551_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1430,-358 1430,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1efe8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,-344 1698,-344 1698,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1fbebb0@0" ObjectIDND1="0@x" ObjectIDZND0="g_1efec90@0" Pin0InfoVect0LinkObjId="g_1efec90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1fbebb0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1667,-344 1698,-344 1698,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1efeaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,-344 1667,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_1efec90@0" ObjectIDND1="0@x" ObjectIDZND0="g_1fbebb0@0" Pin0InfoVect0LinkObjId="g_1fbebb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1efec90_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1667,-344 1667,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1eff870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1811,-343 1842,-343 1842,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_205b5a0@0" ObjectIDND1="0@x" ObjectIDZND0="g_1effad0@0" Pin0InfoVect0LinkObjId="g_1effad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_205b5a0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1811,-343 1842,-343 1842,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2008010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1812,-343 1812,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_1effad0@0" ObjectIDND1="0@x" ObjectIDZND0="g_205b5a0@0" Pin0InfoVect0LinkObjId="g_205b5a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1effad0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1812,-343 1812,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2057e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,-208 1667,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1667,-208 1667,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2058070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,-324 1667,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="g_1efec90@0" ObjectIDZND1="g_1fbebb0@0" Pin0InfoVect0LinkObjId="g_1efec90_0" Pin0InfoVect1LinkObjId="g_1fbebb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1667,-324 1667,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_205a9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1812,-240 1812,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1812,-240 1812,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_205ac10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1812,-327 1812,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_205b5a0@0" ObjectIDZND1="g_1effad0@0" Pin0InfoVect0LinkObjId="g_205b5a0_0" Pin0InfoVect1LinkObjId="g_1effad0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1812,-327 1812,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1fd28c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="101,-159 101,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="101,-159 101,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1fc3250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="101,-121 101,-72 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="101,-121 101,-72 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1fc64b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="101,-36 101,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="101,-36 101,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1fc6710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="101,5 77,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_1fc6bd0@0" Pin0InfoVect0LinkObjId="g_1fc6bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="101,5 77,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1fc6970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="101,-9 101,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_1fc6bd0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1fc6bd0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="101,-9 101,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f850f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="101,5 101,16 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_1fc6bd0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1fc6bd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="101,5 101,16 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ed18d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="101,52 101,94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="101,52 101,94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ed1b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="101,128 101,147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="101,128 101,147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1fa3940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="429,83 429,60 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_1fa4060@0" Pin0InfoVect0LinkObjId="g_1fa4060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="429,83 429,60 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1fa3ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="454,132 429,132 429,119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="454,132 429,132 429,119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1fa3e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="454,119 454,132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="454,119 454,132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1fa4af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="454,83 454,56 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="454,83 454,56 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1fa4d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="454,56 454,-9 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="454,56 454,-9 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1eb2260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="488,46 488,56 454,56 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="488,46 488,56 454,56 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1eb2970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="419,184 419,192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_1eb2bd0@0" Pin0InfoVect0LinkObjId="g_1eb2bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="419,184 419,192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1eb4240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="454,244 430,244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_1eb44a0@0" Pin0InfoVect0LinkObjId="g_1eb44a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="454,244 430,244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1eb6d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="454,155 454,176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="454,155 454,176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1eb6f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="454,132 454,155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="454,132 454,155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1eb7170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="454,155 419,155 419,169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="454,155 419,155 419,169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f16140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="454,-159 454,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="454,-159 454,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f1a150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="454,-121 454,-72 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="454,-121 454,-72 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f1d450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="454,-36 454,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="454,-36 454,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f970e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="343,-159 343,-139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="343,-159 343,-139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f9b0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="343,-122 343,-73 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="343,-122 343,-73 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f9e3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="343,-37 343,-27 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="343,-37 343,-27 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f8c410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,-159 632,-139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="632,-159 632,-139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f90420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,-122 632,-73 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="632,-122 632,-73 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ea5730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,-37 632,-27 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="632,-37 632,-27 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1eab3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="607,83 607,60 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_1eabb10@0" Pin0InfoVect0LinkObjId="g_1eabb10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="607,83 607,60 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1eab650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,132 607,132 607,119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="632,132 607,132 607,119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1eab8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,119 632,132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="632,119 632,132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1eac5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,83 632,56 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="632,83 632,56 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1eac800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,56 632,-9 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="632,56 632,-9 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ed8840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="666,46 666,56 632,56 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="666,46 666,56 632,56 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ed8f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="597,184 597,192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_1ed91b0@0" Pin0InfoVect0LinkObjId="g_1ed91b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="597,184 597,192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1eda820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,244 608,244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_1edaa80@0" Pin0InfoVect0LinkObjId="g_1edaa80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="632,244 608,244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1edc1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,155 632,176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="632,155 632,176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1edc3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,132 632,155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="632,132 632,155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1edc590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,155 597,155 597,169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="632,155 597,155 597,169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1edd8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="488,1 666,1 666,10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="488,1 666,1 666,10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1eddab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="488,1 488,10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="488,1 488,10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ec15f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1888,-159 1888,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1888,-159 1888,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ec5580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1888,-121 1888,-72 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1888,-121 1888,-72 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ec87e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1888,-36 1888,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1888,-36 1888,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ec8a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1888,5 1910,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_1f5ac90@0" Pin0InfoVect0LinkObjId="g_1f5ac90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1888,5 1910,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ecdb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1888,128 1888,147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1888,128 1888,147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f5fe60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1805,130 1805,149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1805,130 1805,149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f60d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1888,-9 1888,-3 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_1f5ac90@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1f5ac90_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1888,-9 1888,-3 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f60f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1888,-3 1888,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_1f5ac90@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1f5ac90_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1888,-3 1888,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1f65110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1523,-879 1542,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1f62080@0" ObjectIDND1="29545@x" ObjectIDND2="29546@x" ObjectIDZND0="g_1f660c0@0" Pin0InfoVect0LinkObjId="g_1f660c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1f62080_0" Pin1InfoVect1LinkObjId="SW-194564_0" Pin1InfoVect2LinkObjId="SW-194565_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1523,-879 1542,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1f65c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1523,-809 1523,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1f62080@0" ObjectIDZND0="29545@x" ObjectIDZND1="29546@x" ObjectIDZND2="g_1f660c0@0" Pin0InfoVect0LinkObjId="SW-194564_0" Pin0InfoVect1LinkObjId="SW-194565_0" Pin0InfoVect2LinkObjId="g_1f660c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f62080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1523,-809 1523,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1f65e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1523,-879 1481,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1f62080@0" ObjectIDND1="g_1f660c0@0" ObjectIDZND0="29545@x" ObjectIDZND1="29546@x" Pin0InfoVect0LinkObjId="SW-194564_0" Pin0InfoVect1LinkObjId="SW-194565_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f62080_0" Pin1InfoVect1LinkObjId="g_1f660c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1523,-879 1481,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1f6a1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="471,-874 431,-874 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="29535@x" ObjectIDND1="29534@x" ObjectIDND2="g_1f66e30@0" ObjectIDZND0="g_1f6b160@0" Pin0InfoVect0LinkObjId="g_1f6b160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-194517_0" Pin1InfoVect1LinkObjId="SW-194516_0" Pin1InfoVect2LinkObjId="g_1f66e30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="471,-874 431,-874 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1f6aca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="518,-874 471,-874 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="29535@x" ObjectIDND1="29534@x" ObjectIDZND0="g_1f66e30@0" ObjectIDZND1="g_1f6b160@0" Pin0InfoVect0LinkObjId="g_1f66e30_0" Pin0InfoVect1LinkObjId="g_1f6b160_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-194517_0" Pin1InfoVect1LinkObjId="SW-194516_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="518,-874 471,-874 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_1f6af00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="471,-874 471,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="29535@x" ObjectIDND1="29534@x" ObjectIDND2="g_1f6b160@0" ObjectIDZND0="g_1f66e30@0" Pin0InfoVect0LinkObjId="g_1f66e30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-194517_0" Pin1InfoVect1LinkObjId="SW-194516_0" Pin1InfoVect2LinkObjId="g_1f6b160_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="471,-874 471,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f6c640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="343,-10 343,1 488,1 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="343,-10 343,1 488,1 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f72c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1274,82 1274,59 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_1f73320@0" Pin0InfoVect0LinkObjId="g_1f73320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1274,82 1274,59 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f72e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1299,131 1274,131 1274,118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1299,131 1274,131 1274,118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f730c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1299,118 1299,131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1299,118 1299,131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f73db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1299,82 1299,55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1299,82 1299,55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e929b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1333,45 1333,55 1299,55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1333,45 1333,55 1299,55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e93060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1264,183 1264,191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_1e932c0@0" Pin0InfoVect0LinkObjId="g_1e932c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1264,183 1264,191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e94580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1299,243 1275,243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_1e947e0@0" Pin0InfoVect0LinkObjId="g_1e947e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1299,243 1275,243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e960c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1299,154 1299,175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1299,154 1299,175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e962b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1299,131 1299,154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1299,131 1299,154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e964a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1299,154 1264,154 1264,168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1299,154 1264,154 1264,168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e9d060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1299,-122 1299,-73 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1299,-122 1299,-73 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ea02c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1299,-37 1299,-27 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1299,-37 1299,-27 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20cf310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1605,-125 1605,-76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1605,-125 1605,-76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20d2610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1605,-40 1605,-30 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1605,-40 1605,-30 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20dad60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1477,-123 1477,-74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1477,-123 1477,-74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20ddfc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1477,-38 1477,-28 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1477,-38 1477,-28 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ee61b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1452,82 1452,59 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_1ee68d0@0" Pin0InfoVect0LinkObjId="g_1ee68d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1452,82 1452,59 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ee6410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1477,131 1452,131 1452,118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1477,131 1452,131 1452,118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ee6670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1477,118 1477,131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1477,118 1477,131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ee7360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1477,82 1477,55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1477,82 1477,55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ee75c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1477,55 1477,-10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1477,55 1477,-10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1eea530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1511,45 1511,55 1477,55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1511,45 1511,55 1477,55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1eeac40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1442,183 1442,191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_1eeaea0@0" Pin0InfoVect0LinkObjId="g_1eeaea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1442,183 1442,191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1eec510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1477,243 1453,243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_1eec770@0" Pin0InfoVect0LinkObjId="g_1eec770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1477,243 1453,243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1eedce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1477,154 1477,175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1477,154 1477,175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1eeded0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1477,131 1477,154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1477,131 1477,154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1eee0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1477,154 1442,154 1442,168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1477,154 1442,154 1442,168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1eef3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1605,-159 1605,-142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1605,-159 1605,-142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1eef5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1299,-159 1299,-139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1299,-159 1299,-139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1eef7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1477,-159 1477,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1477,-159 1477,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ef3d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1605,-13 1605,9 1334,9 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1605,-13 1605,9 1334,9 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ef3fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1299,55 1299,-10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1299,55 1299,-10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ef6960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1805,96 1805,85 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1805,96 1805,85 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ef6bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1805,49 1805,-3 1888,-3 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_1f5ac90@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1f5ac90_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1805,49 1805,-3 1888,-3 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ef95c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1888,94 1888,84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1888,94 1888,84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ef9820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1888,48 1888,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_1f5ac90@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1f5ac90_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1888,48 1888,5 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-194436" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -55.000000 -817.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29520" ObjectName="DYN-CX_NHNQ"/>
     <cge:Meas_Ref ObjectId="194436"/>
    </metadata>
   </g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="591" cy="-499" fill="none" fillStyle="0" r="16" stroke="rgb(255,255,255)" stroke-width="1.0625"/>
   <circle DF8003:Layer="PUBLIC" cx="774" cy="-499" fill="none" fillStyle="0" r="16" stroke="rgb(255,255,255)" stroke-width="1.0625"/>
   <circle DF8003:Layer="PUBLIC" cx="646" cy="-320" fill="none" fillStyle="0" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="646" cy="-301" fill="none" fillStyle="0" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="646" cy="-339" fill="none" fillStyle="0" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="718" cy="-320" fill="none" fillStyle="0" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="718" cy="-301" fill="none" fillStyle="0" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="718" cy="-339" fill="none" fillStyle="0" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="937" cy="-420" fill="none" fillStyle="0" r="12" stroke="rgb(60,120,255)" stroke-width="0.8125"/>
   <ellipse DF8003:Layer="PUBLIC" cx="1035" cy="-794" fill="none" fillStyle="0" rx="24" ry="24.5" stroke="rgb(60,120,255)" stroke-width="0.510204"/>
   <circle DF8003:Layer="PUBLIC" cx="1035" cy="-755" fill="none" fillStyle="0" r="24" stroke="rgb(60,120,255)" stroke-width="0.510204"/>
   <circle DF8003:Layer="PUBLIC" cx="1302" cy="-502" fill="none" fillStyle="0" r="16" stroke="rgb(255,255,255)" stroke-width="1.0625"/>
   <circle DF8003:Layer="PUBLIC" cx="1485" cy="-502" fill="none" fillStyle="0" r="16" stroke="rgb(255,255,255)" stroke-width="1.0625"/>
   <circle DF8003:Layer="PUBLIC" cx="1357" cy="-323" fill="none" fillStyle="0" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1357" cy="-304" fill="none" fillStyle="0" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1357" cy="-342" fill="none" fillStyle="0" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1429" cy="-323" fill="none" fillStyle="0" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1429" cy="-304" fill="none" fillStyle="0" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1429" cy="-342" fill="none" fillStyle="0" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="100" cy="-105" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="100" cy="-86" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <ellipse DF8003:Layer="PUBLIC" cx="101" cy="73" fill="none" fillStyle="0" rx="7.5" ry="8" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <ellipse DF8003:Layer="PUBLIC" cx="101" cy="197" fill="none" fillStyle="0" rx="7.5" ry="8" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="471" cy="100" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="0.4375"/>
   <circle DF8003:Layer="PUBLIC" cx="505" cy="28" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="0.4375"/>
   <circle DF8003:Layer="PUBLIC" cx="418" cy="176" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="453" cy="-105" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="453" cy="-86" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="342" cy="-106" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="342" cy="-87" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="631" cy="-106" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="631" cy="-87" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="649" cy="100" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="0.4375"/>
   <circle DF8003:Layer="PUBLIC" cx="683" cy="28" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="0.4375"/>
   <circle DF8003:Layer="PUBLIC" cx="596" cy="176" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1887" cy="-105" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1887" cy="-86" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <ellipse DF8003:Layer="PUBLIC" cx="1888" cy="30" fill="none" fillStyle="0" rx="7.5" ry="8" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <ellipse DF8003:Layer="PUBLIC" cx="1888" cy="197" fill="none" fillStyle="0" rx="7.5" ry="8" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <ellipse DF8003:Layer="PUBLIC" cx="1805" cy="31" fill="none" fillStyle="0" rx="7.5" ry="8" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <ellipse DF8003:Layer="PUBLIC" cx="1805" cy="199" fill="none" fillStyle="0" rx="7.5" ry="8" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1316" cy="99" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="0.4375"/>
   <circle DF8003:Layer="PUBLIC" cx="1351" cy="27" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="0.4375"/>
   <circle DF8003:Layer="PUBLIC" cx="1263" cy="175" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1298" cy="-106" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1298" cy="-87" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1604" cy="-109" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1604" cy="-90" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1476" cy="-107" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1476" cy="-88" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1494" cy="99" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="0.4375"/>
   <circle DF8003:Layer="PUBLIC" cx="1528" cy="27" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="0.4375"/>
   <circle DF8003:Layer="PUBLIC" cx="1441" cy="175" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="0,-240 882,-240 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="0,-240 882,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="915,-240 1152,-240 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="915,-240 1152,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1190,-240 2005,-240 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1190,-240 2005,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="0,-208 882,-208 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="0,-208 882,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="915,-208 1152,-208 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="915,-208 1152,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1190,-208 2005,-208 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1190,-208 2005,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2,-159 1008,-159 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="2,-159 1008,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1065,-159 2005,-159 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1065,-159 2005,-159 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="915" cy="-240" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1148" cy="-240" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="915" cy="-208" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1148" cy="-208" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="647" cy="-208" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="879" cy="-208" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="719" cy="-240" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="879" cy="-240" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1006" cy="-159" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1190" cy="-240" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1430" cy="-240" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1190" cy="-208" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1358" cy="-208" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1066" cy="-159" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="101" cy="-159" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1888" cy="-159" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="454" cy="-159" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="343" cy="-159" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="632" cy="-159" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1605" cy="-159" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1299" cy="-159" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1477" cy="-159" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="269" cy="-208" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="414" cy="-240" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="970" cy="-159" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1101" cy="-159" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1667" cy="-208" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1812" cy="-240" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="-347" y="-809"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="-346" y="-929"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(60,120,255)" stroke-width="1" width="14" x="963" y="-482"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(60,120,255)" stroke-width="1" width="14" x="1093" y="-482"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_209b710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -337.000000 -763.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_209b710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -337.000000 -763.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_209b710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -337.000000 -763.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_209b710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -337.000000 -763.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_209b710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -337.000000 -763.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_209b710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -337.000000 -763.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_209b710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -337.000000 -763.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cd65a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cd65a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cd65a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cd65a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cd65a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cd65a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cd65a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cd65a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cd65a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cd65a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cd65a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cd65a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cd65a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cd65a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cd65a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cd65a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cd65a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cd65a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="18" graphid="g_1ced300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -200.000000 -896.500000) translate(0,15)">南华牵引变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2044910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 589.500000 -990.000000) translate(0,17)">500kV鹿城变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1d0fad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 663.000000 -912.000000) translate(0,17)">鹿</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1d0fad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 663.000000 -912.000000) translate(0,38)">南</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1d0fad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 663.000000 -912.000000) translate(0,59)">牵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1d0fad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 663.000000 -912.000000) translate(0,80)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2045230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1293.000000 -997.000000) translate(0,17)">220kV紫溪变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2023bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1330.000000 -912.000000) translate(0,17)">紫</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2023bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1330.000000 -912.000000) translate(0,38)">南</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2023bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1330.000000 -912.000000) translate(0,59)">牵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2023bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1330.000000 -912.000000) translate(0,80)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fa99a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 543.000000 -506.500000) translate(0,12)">YD7</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fa9c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 795.000000 -506.500000) translate(0,12)">YD8</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_202e580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 568.000000 -482.500000) translate(0,12)">5F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_202e8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 782.000000 -480.500000) translate(0,12)">3F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_202eb40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 619.000000 -435.500000) translate(0,12)">AC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_202ed30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 727.000000 -435.500000) translate(0,12)">BC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_202f010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 543.000000 -434.500000) translate(0,12)">2×TJ-240</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_202f200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -435.500000) translate(0,12)">4×TJ-240</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f110d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 312.000000 -395.500000) translate(0,12)">9F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f05c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 457.000000 -395.500000) translate(0,12)">7F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f115e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 227.000000 -395.500000) translate(0,12)">2FU</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f7f670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 374.000000 -395.500000) translate(0,12)">1FU</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f7f9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 220.000000 -457.500000) translate(0,12)">5TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f7fbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 365.000000 -457.500000) translate(0,12)">3TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f141d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 222.000000 -313.500000) translate(0,12)">2605</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_200ca50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 368.000000 -313.500000) translate(0,12)">2603</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f76c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 880.000000 -265.500000) translate(0,12)">2101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f76ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1155.000000 -265.500000) translate(0,12)">2102</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f771d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 879.500000 -198.500000) translate(0,12)">2103</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f773b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1154.500000 -198.500000) translate(0,12)">2104</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ff0ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 931.000000 -427.500000) translate(0,12)">M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f79500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 885.000000 -427.500000) translate(0,12)">2631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_202c3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 908.000000 -517.500000) translate(0,12)">26310</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1f53440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 998.000000 -905.000000) translate(0,17)">至交流屏</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fd7a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1254.000000 -509.500000) translate(0,12)">YD5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fda590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1279.000000 -485.500000) translate(0,12)">6F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_207b2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1493.000000 -483.500000) translate(0,12)">4F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_207b530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1330.000000 -438.500000) translate(0,12)">AC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_207b770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1438.000000 -438.500000) translate(0,12)">BC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_207b9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1254.000000 -437.500000) translate(0,12)">2×TJ-240</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_207bbf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1470.000000 -438.500000) translate(0,12)">4×TJ-240</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1efd2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 599.000000 -327.500000) translate(0,12)">5TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1efd8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 749.000000 -327.500000) translate(0,12)">3TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1efdb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 927.000000 -477.500000) translate(0,12)">5FU</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1efdd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1059.000000 -477.500000) translate(0,12)">6FU</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1efdfb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1068.000000 -779.500000) translate(0,12)">3T</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1efe1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1504.000000 -510.500000) translate(0,12)">YD6</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1efe430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1312.000000 -327.500000) translate(0,12)">6TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1efe670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1458.000000 -327.500000) translate(0,12)">4TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2008270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1710.000000 -395.500000) translate(0,12)">10F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2008760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1855.000000 -395.500000) translate(0,12)">8F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20089a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1625.000000 -395.500000) translate(0,12)">4FU</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2008be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1772.000000 -395.500000) translate(0,12)">3FU</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2008e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1618.000000 -457.500000) translate(0,12)">6TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2009060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1763.000000 -457.500000) translate(0,12)">4TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_205ae70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1620.000000 -313.500000) translate(0,12)">2606</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_205b360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1766.000000 -313.500000) translate(0,12)">2604</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc1280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -27.000000 -247.500000) translate(0,12)">BC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc1770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -27.500000 -215.500000) translate(0,12)">AC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fc19b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -27.500000 -166.500000) translate(0,12)">AC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_201ba10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2019.000000 -247.500000) translate(0,12)">BC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_201bea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2018.500000 -215.500000) translate(0,12)">AC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_201c0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2019.000000 -166.500000) translate(0,12)">BC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f86e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 51.000000 66.500000) translate(0,12)">15TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ecebf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 55.000000 -57.500000) translate(0,12)">235B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ecee30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -18.500000) translate(0,12)">15F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ecf070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 48.000000 32.500000) translate(0,12)">2351B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ecf2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 53.000000 -101.500000) translate(0,12)">13TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ed1d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 66.000000 164.500000) translate(0,12)">3L</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ed4b00" transform="matrix(0.538462 -0.000000 -0.000000 0.538462 467.769231 95.961538) translate(0,12)">M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fa5140" transform="matrix(0.538462 -0.000000 -0.000000 0.538462 501.769231 23.961538) translate(0,12)">M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eb53e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 401.500000 287.500000) translate(0,12)">广通北下行（AC）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f9e650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 355.000000 -58.500000) translate(0,12)">242</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f879b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 365.000000 -105.500000) translate(0,12)">11TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f87bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 477.000000 -105.500000) translate(0,12)">9TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f87e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 468.000000 -58.500000) translate(0,12)">252</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f88070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 499.000000 40.500000) translate(0,12)">2522</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f882b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 383.000000 94.500000) translate(0,12)">2520</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f884f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 485.000000 92.500000) translate(0,12)">2521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f88730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 352.000000 136.500000) translate(0,12)">2×LBGLJ-240</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f88c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 366.000000 175.500000) translate(0,12)">3YDQ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f89050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 468.000000 199.500000) translate(0,12)">3K</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f89520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 396.000000 219.500000) translate(0,12)">13F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ea5990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 655.000000 -106.500000) translate(0,12)">7TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ea5fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 646.000000 -59.500000) translate(0,12)">232</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ea8880" transform="matrix(0.538462 -0.000000 -0.000000 0.538462 645.769231 95.961538) translate(0,12)">M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eacbf0" transform="matrix(0.538462 -0.000000 -0.000000 0.538462 679.769231 23.961538) translate(0,12)">M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1edb9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 579.500000 287.500000) translate(0,12)">广通北上行（AC）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1edc7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 677.000000 40.500000) translate(0,12)">2322</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1edcb40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 561.000000 94.500000) translate(0,12)">2320</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1edcd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 663.000000 92.500000) translate(0,12)">2321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1edcfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 530.000000 136.500000) translate(0,12)">2×LBGLJ-240</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1edd210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 544.000000 175.500000) translate(0,12)">1YDQ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1edd440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 646.000000 199.500000) translate(0,12)">1K</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1edd680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 574.000000 219.500000) translate(0,12)">11F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eca7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1838.000000 23.500000) translate(0,12)">14TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ecadd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1842.000000 -57.500000) translate(0,12)">235A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ecb010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1928.000000 -16.500000) translate(0,12)">16F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ecb250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1834.000000 59.500000) translate(0,12)">2351A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ecb490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1840.000000 -101.500000) translate(0,12)">14TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ecddb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1844.000000 103.500000) translate(0,12)">1C</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f5d2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1755.000000 24.500000) translate(0,12)">18TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f5d7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1751.000000 61.500000) translate(0,12)">2352A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f600c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1761.000000 105.500000) translate(0,12)">2C</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f61110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 58.000000 103.500000) translate(0,12)">3C</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f61540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 50.000000 188.500000) translate(0,12)">17TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f61780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1767.000000 167.500000) translate(0,12)">2L</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f619c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1853.000000 167.500000) translate(0,12)">1L</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f61c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1757.000000 206.500000) translate(0,12)">20TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f61e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1840.000000 206.500000) translate(0,12)">16TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f6bf10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 399.000000 -898.500000) translate(0,12)">1F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f6c400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1567.000000 -898.500000) translate(0,12)">2F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f70090" transform="matrix(0.538462 -0.000000 -0.000000 0.538462 1312.769231 94.961538) translate(0,12)">M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f741a0" transform="matrix(0.538462 -0.000000 -0.000000 0.538462 1347.769231 22.961538) translate(0,12)">M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e95660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1246.500000 286.500000) translate(0,12)">大理下行（BC）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20d2870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1617.000000 -61.500000) translate(0,12)">241</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20d2ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1627.000000 -108.500000) translate(0,12)">8TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20d30e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1322.000000 -106.500000) translate(0,12)">12TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20d3320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1313.000000 -59.500000) translate(0,12)">251</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20d3560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1344.000000 39.500000) translate(0,12)">2512</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20d37a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1228.000000 93.500000) translate(0,12)">2510</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20d39e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1330.000000 91.500000) translate(0,12)">2511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20d3c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1197.000000 135.500000) translate(0,12)">4×LBGLJ-240</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20d3e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1211.000000 174.500000) translate(0,12)">4YDQ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20d40a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1313.000000 198.500000) translate(0,12)">4K</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20d42e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1241.000000 218.500000) translate(0,12)">14F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20de220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1500.000000 -107.500000) translate(0,12)">10TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20de850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1491.000000 -60.500000) translate(0,12)">231</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e1110" transform="matrix(0.538462 -0.000000 -0.000000 0.538462 1490.769231 94.961538) translate(0,12)">M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ee79b0" transform="matrix(0.538462 -0.000000 -0.000000 0.538462 1524.769231 22.961538) translate(0,12)">M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eed6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1424.500000 286.500000) translate(0,12)">大理上行（BC）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eee2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1522.000000 39.500000) translate(0,12)">2312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eee640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1406.000000 93.500000) translate(0,12)">2310</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eee880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1508.000000 91.500000) translate(0,12)">2311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eeeac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1375.000000 135.500000) translate(0,12)">2×LBGLJ-240</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eeed10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1389.000000 174.500000) translate(0,12)">2YDQ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eeef40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1491.000000 198.500000) translate(0,12)">2K</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eef180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1419.000000 218.500000) translate(0,12)">12F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1efbd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 656.000000 -733.000000) translate(0,12)">111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e74e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 654.000000 -380.000000) translate(0,12)">201B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e750c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 726.000000 -380.000000) translate(0,12)">201A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e75300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 654.000000 -781.000000) translate(0,12)">2616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e75540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 587.000000 -834.000000) translate(0,12)">26167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e75780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 566.000000 -900.000000) translate(0,12)">2619</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e759c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 527.000000 -946.000000) translate(0,12)">26197</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e75c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1366.000000 -740.000000) translate(0,12)">112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e75e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1364.000000 -788.000000) translate(0,12)">2626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e76080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1377.000000 -841.000000) translate(0,12)">26267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e762c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1405.000000 -905.000000) translate(0,12)">2629</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e76500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1493.000000 -953.000000) translate(0,12)">26297</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e76740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1365.000000 -383.000000) translate(0,12)">202B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e76980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1437.000000 -383.000000) translate(0,12)">202A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1e76bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 699.000000 -643.000000) translate(0,17)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1e78160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1401.000000 -643.000000) translate(0,17)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25a2a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -211.000000 38.500000) translate(0,12)">0871-66124214</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2017320" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 549.000000 -802.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f4b080" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1430.000000 -809.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2071ae0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 572.000000 -914.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2043170" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1546.000000 -921.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fa4060" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 423.000000 65.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1eb2bd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 413.000000 210.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1eabb10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 601.000000 65.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ed91b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 591.000000 210.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f73320" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1268.000000 64.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e932c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1258.000000 209.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ee68d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1446.000000 64.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1eeaea0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1436.000000 209.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_NHQ"/>
</svg>