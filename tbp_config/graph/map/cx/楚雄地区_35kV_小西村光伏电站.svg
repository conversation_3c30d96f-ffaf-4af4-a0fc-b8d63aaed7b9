<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-137" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="-330 -957 2022 1248">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="16" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.7" x1="19" x2="10" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="25" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="16" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="20" x2="11" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="11" x2="2" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="11" x2="11" y1="9" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="66"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="16" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="25" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="16" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="11" x2="11" y1="9" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="11" x2="2" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="20" x2="11" y1="17" y2="8"/>
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="40"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="5" x2="5" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="11" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="26" x2="9" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="18" y2="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape177">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <text font-family="SimSun" font-size="15" graphid="g_33ac320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
    <polyline points="17,19 17,30 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape164">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="60" x2="60" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="57" x2="57" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="48" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="13" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="9" y1="60" y2="60"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="60" x2="21" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="15" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="6" x2="6" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="4" x2="4" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="6" y2="9"/>
    <rect height="13" stroke-width="0.424575" width="29" x="15" y="1"/>
   </symbol>
   <symbol id="lightningRod:shape14">
    <polyline points="9,9 3,12 1,13 1,14 1,14 3,16 6,17 10,19 11,20 11,20 11,21 10,22 6,23 3,25 2,25 2,26 2,27 3,28 6,29 10,31 11,31 11,32 11,33 10,33 6,35 3,36 2,37 2,38 2,39 3,39 6,41 10,42 11,43 11,44 11,44 10,45 6,47 3,48 1,50 1,50 1,51 3,52 9,55 " stroke-width="2.00006"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="10" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="54" y2="61"/>
   </symbol>
   <symbol id="lightningRod:shape115">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0319731" x1="9" x2="13" y1="31" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0178798" x1="13" x2="13" y1="35" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0319731" x1="18" x2="13" y1="31" y2="35"/>
    <ellipse cx="13" cy="34" rx="12.5" ry="11.5" stroke-width="0.118558"/>
    <circle cx="13" cy="17" r="12" stroke-width="0.120929"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0326126" x1="18" x2="13" y1="14" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0326126" x1="9" x2="13" y1="14" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0182374" x1="13" x2="13" y1="17" y2="21"/>
   </symbol>
   <symbol id="lightningRod:shape197">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="82" y2="87"/>
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="14" y1="51" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="14,14 20,27 7,27 14,14 14,15 14,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="42" y2="0"/>
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="55" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="56" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="80" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="81" y2="76"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="5" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="3" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="8" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="1" x2="12" y1="8" y2="8"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape36_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
   </symbol>
   <symbol id="switch2:shape36_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="5" y1="39" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-16" x2="-4" y1="31" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-4" x2="3" y1="18" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="3" y1="38" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="-16" y1="38" y2="31"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="25" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,49 16,27 28,27 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="29"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="27" y2="27"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="7"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,6 16,28 28,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="30" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape12_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="52"/>
   </symbol>
   <symbol id="transformer2:shape12_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="82" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="80" y2="76"/>
   </symbol>
   <symbol id="transformer2:shape1_0">
    <circle cx="25" cy="61" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="16" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="33" y1="59" y2="59"/>
   </symbol>
   <symbol id="transformer2:shape1_1">
    <ellipse cx="25" cy="29" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
   </symbol>
   <symbol id="voltageTransformer:shape71">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="53" y1="12" y2="12"/>
    <ellipse cx="39" cy="19" fillStyle="0" rx="15" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.36031" x1="30" x2="34" y1="55" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.39762" x1="25" x2="21" y1="55" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.77579" x1="21" x2="35" y1="62" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="33" x2="41" y1="16" y2="20"/>
    <ellipse cx="16" cy="20" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="40" x2="41" y1="20" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="47" x2="41" y1="16" y2="20"/>
    <ellipse cx="28" cy="56" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="53" y1="32" y2="32"/>
    <ellipse cx="40" cy="39" fillStyle="0" rx="15" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="32" y2="32"/>
    <ellipse cx="15" cy="40" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="15" x2="16" y1="20" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="22" x2="16" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="16" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="33" x2="41" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="40" x2="41" y1="40" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="47" x2="41" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="15" x2="16" y1="40" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="21" x2="15" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="16" y1="36" y2="40"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_23be810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23eb170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23ebb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_236fe60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2370eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2371990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23723b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2439880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_236e7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_236e7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_243ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_243ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24360c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24360c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_24370e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2438d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_250ff40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2510cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2511440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24171b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25132e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2513ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24125d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2413390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2413d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2414800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24151c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24783b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2415c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2440690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24412b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2689790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2442080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_23904c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2391aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1258" width="2032" x="-335" y="-962"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1249,66 1232,66 1241,76 1249,67 1249,66 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1248,104 1234,104 1241,93 1248,103 1248,104 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="888,68 871,68 880,78 888,69 888,68 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="621,71 604,71 613,81 621,72 621,71 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="356,70 339,70 348,80 356,71 356,70 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="497,-544 483,-544 490,-555 497,-545 497,-544 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1261,-632 1244,-632 1253,-622 1261,-631 1261,-632 " stroke="rgb(30,144,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1260,-518 1243,-518 1252,-508 1260,-517 1260,-518 " stroke="rgb(30,144,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1259,-480 1245,-480 1252,-491 1259,-481 1259,-480 " stroke="rgb(30,144,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1503,-550 1486,-550 1495,-540 1503,-549 1503,-550 " stroke="rgb(30,144,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1502,-512 1488,-512 1495,-523 1502,-513 1502,-512 " stroke="rgb(30,144,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1502,-714 1485,-714 1494,-704 1502,-713 1502,-714 " stroke="rgb(30,144,255)"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-87825">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 429.000000 17.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19034" ObjectName="SW-CX_XXC.CX_XXC_36167SW"/>
     <cge:Meas_Ref ObjectId="87825"/>
    <cge:TPSR_Ref TObjectID="19034"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 417.000000 -663.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 953.000000 -604.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 818.000000 -604.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1554.500000 -568.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87858">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 913.000000 -209.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19041" ObjectName="SW-CX_XXC.CX_XXC_3901XC"/>
     <cge:Meas_Ref ObjectId="87858"/>
    <cge:TPSR_Ref TObjectID="19041"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87833">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 695.000000 17.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19036" ObjectName="SW-CX_XXC.CX_XXC_36267SW"/>
     <cge:Meas_Ref ObjectId="87833"/>
    <cge:TPSR_Ref TObjectID="19036"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87846">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 961.000000 16.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19038" ObjectName="SW-CX_XXC.CX_XXC_36367SW"/>
     <cge:Meas_Ref ObjectId="87846"/>
    <cge:TPSR_Ref TObjectID="19038"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87857">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 1322.000000 17.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19040" ObjectName="SW-CX_XXC.CX_XXC_36467SW"/>
     <cge:Meas_Ref ObjectId="87857"/>
    <cge:TPSR_Ref TObjectID="19040"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1248.000000 -636.000000)" xlink:href="#switch2:shape36_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 318.000000 -751.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 318.000000 -656.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87863">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 819.000000 206.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19042" ObjectName="SW-CX_XXC.CX_XXC_3010SW"/>
     <cge:Meas_Ref ObjectId="87863"/>
    <cge:TPSR_Ref TObjectID="19042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 739.500000 -510.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_XXC.P1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 343.000000 138.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43341" ObjectName="SM-CX_XXC.P1"/>
    <cge:TPSR_Ref TObjectID="43341"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_XXC.P2">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 609.000000 138.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43342" ObjectName="SM-CX_XXC.P2"/>
    <cge:TPSR_Ref TObjectID="43342"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_XXC" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_shaonengXXC" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="490,-589 490,-562 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="42322" ObjectName="AC-35kV.LN_shaonengXXC"/>
    <cge:TPSR_Ref TObjectID="42322_SS-137"/></metadata>
   <polyline fill="none" opacity="0" points="490,-589 490,-562 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_23841f0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 451.000000 40.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36213a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 824.000000 291.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3570400" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 439.000000 -640.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4019bc0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 1.000000 0.000000 1530.500000 -548.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35a7ab0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 717.000000 40.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26024d0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 983.000000 39.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ea3ca0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1344.000000 40.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_368d430" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 1.000000 0.000000 715.500000 -490.500000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_43acd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="425,32 407,32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_23841f0@0" ObjectIDZND0="19034@1" Pin0InfoVect0LinkObjId="SW-87825_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23841f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="425,32 407,32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_414b020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="371,32 348,32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="generator" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="19034@0" ObjectIDZND0="43341@x" ObjectIDZND1="g_3f97e40@0" ObjectIDZND2="19033@x" Pin0InfoVect0LinkObjId="SM-CX_XXC.P1_0" Pin0InfoVect1LinkObjId="g_3f97e40_0" Pin0InfoVect2LinkObjId="SW-87824_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87825_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="371,32 348,32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4012ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="923,-335 923,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_3cfe710@1" ObjectIDZND0="g_363e7c0@0" Pin0InfoVect0LinkObjId="g_363e7c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3cfe710_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="923,-335 923,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_435dc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="413,-649 395,-649 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3570400@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3570400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="413,-649 395,-649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2384890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="359,-648 333,-648 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="359,-648 333,-648 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_411f380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="796,-589 831,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="796,-589 831,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3fec2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="858,-589 895,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="858,-589 895,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ebec10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="931,-589 1009,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="931,-589 1009,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35fb680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1425,-300 1450,-300 1450,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1425,-300 1450,-300 1450,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_42c97f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1334,-300 1305,-300 1305,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1334,-300 1305,-300 1305,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_40f4990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1494,-404 1494,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1494,-404 1494,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_339e8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1494,-495 1494,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1494,-495 1494,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34357f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1495,-659 1495,-730 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_4289a90@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4289a90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1495,-659 1495,-730 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3cb7ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1539,-574 1539,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_4019bc0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4019bc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1539,-574 1539,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3df9cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1539,-626 1539,-647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_363b030@0" Pin0InfoVect0LinkObjId="g_363b030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1539,-626 1539,-647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_250d5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1539,-647 1495,-647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_363b030@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_363b030_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1539,-647 1495,-647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3fb6ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1539,-647 1585,-647 1585,-610 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDZND0="g_363b030@0" Pin0InfoVect0LinkObjId="g_363b030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1539,-647 1585,-647 1585,-610 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25d0560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="521,-491 490,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="powerLine" ObjectIDND0="g_25e6300@0" ObjectIDZND0="19032@x" ObjectIDZND1="42322@1" Pin0InfoVect0LinkObjId="SW-87817_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25e6300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="521,-491 490,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3de1920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="490,-563 490,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="42322@1" ObjectIDZND0="g_25e6300@0" ObjectIDZND1="19032@x" Pin0InfoVect0LinkObjId="g_25e6300_0" Pin0InfoVect1LinkObjId="SW-87817_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="490,-563 490,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34ca120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="923,-172 923,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19030@0" ObjectIDZND0="19041@0" Pin0InfoVect0LinkObjId="SW-87858_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="923,-172 923,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a76cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="968,-303 968,-283 923,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3fc7470@0" ObjectIDZND0="g_3cfe710@0" ObjectIDZND1="19041@x" Pin0InfoVect0LinkObjId="g_3cfe710_0" Pin0InfoVect1LinkObjId="SW-87858_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3fc7470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="968,-303 968,-283 923,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aa5060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="923,-304 923,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3cfe710@0" ObjectIDZND0="g_3fc7470@0" ObjectIDZND1="19041@x" Pin0InfoVect0LinkObjId="g_3fc7470_0" Pin0InfoVect1LinkObjId="SW-87858_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3cfe710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="923,-304 923,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3617060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="923,-283 923,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_3fc7470@0" ObjectIDND1="g_3cfe710@0" ObjectIDZND0="19041@1" Pin0InfoVect0LinkObjId="SW-87858_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3fc7470_0" Pin1InfoVect1LinkObjId="g_3cfe710_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="923,-283 923,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a66b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="315,57 348,57 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_3f97e40@0" ObjectIDZND0="43341@x" ObjectIDZND1="19034@x" ObjectIDZND2="19033@x" Pin0InfoVect0LinkObjId="SM-CX_XXC.P1_0" Pin0InfoVect1LinkObjId="SW-87825_0" Pin0InfoVect2LinkObjId="SW-87824_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f97e40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="315,57 348,57 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d44f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="348,117 348,56 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="43341@0" ObjectIDZND0="19034@x" ObjectIDZND1="19033@x" ObjectIDZND2="g_3f97e40@0" Pin0InfoVect0LinkObjId="SW-87825_0" Pin0InfoVect1LinkObjId="SW-87824_0" Pin0InfoVect2LinkObjId="g_3f97e40_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_XXC.P1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="348,117 348,56 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_469bf20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="348,56 348,32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="43341@x" ObjectIDND1="g_3f97e40@0" ObjectIDZND0="19034@x" ObjectIDZND1="19033@x" Pin0InfoVect0LinkObjId="SW-87825_0" Pin0InfoVect1LinkObjId="SW-87824_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SM-CX_XXC.P1_0" Pin1InfoVect1LinkObjId="g_3f97e40_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="348,56 348,32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ab1740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="691,32 673,32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_35a7ab0@0" ObjectIDZND0="19036@1" Pin0InfoVect0LinkObjId="SW-87833_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35a7ab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="691,32 673,32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a160c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="637,32 614,32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="breaker" ObjectIDND0="19036@0" ObjectIDZND0="g_2ab1e80@0" ObjectIDZND1="43342@x" ObjectIDZND2="19035@x" Pin0InfoVect0LinkObjId="g_2ab1e80_0" Pin0InfoVect1LinkObjId="SM-CX_XXC.P2_0" Pin0InfoVect2LinkObjId="SW-87832_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87833_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="637,32 614,32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fd1640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="581,57 614,57 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="generator" ObjectIDND0="g_2ab1e80@0" ObjectIDZND0="19036@x" ObjectIDZND1="19035@x" ObjectIDZND2="43342@x" Pin0InfoVect0LinkObjId="SW-87833_0" Pin0InfoVect1LinkObjId="SW-87832_0" Pin0InfoVect2LinkObjId="SM-CX_XXC.P2_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ab1e80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="581,57 614,57 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_356e890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="614,117 614,56 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="43342@0" ObjectIDZND0="19036@x" ObjectIDZND1="19035@x" ObjectIDZND2="g_2ab1e80@0" Pin0InfoVect0LinkObjId="SW-87833_0" Pin0InfoVect1LinkObjId="SW-87832_0" Pin0InfoVect2LinkObjId="g_2ab1e80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_XXC.P2_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="614,117 614,56 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_469d360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="614,56 614,32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_2ab1e80@0" ObjectIDND1="43342@x" ObjectIDZND0="19036@x" ObjectIDZND1="19035@x" Pin0InfoVect0LinkObjId="SW-87833_0" Pin0InfoVect1LinkObjId="SW-87832_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ab1e80_0" Pin1InfoVect1LinkObjId="SM-CX_XXC.P2_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="614,56 614,32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_263f8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="957,31 939,31 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_26024d0@0" ObjectIDZND0="19038@1" Pin0InfoVect0LinkObjId="SW-87846_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26024d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="957,31 939,31 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36219a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="903,31 880,31 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="19038@0" ObjectIDZND0="0@x" ObjectIDZND1="g_4074a00@0" ObjectIDZND2="19037@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_4074a00_0" Pin0InfoVect2LinkObjId="SW-87845_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87846_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="903,31 880,31 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_343f350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="847,56 880,56 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="transformer2" ObjectIDND0="g_4074a00@0" ObjectIDZND0="19038@x" ObjectIDZND1="19037@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-87846_0" Pin0InfoVect1LinkObjId="SW-87845_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4074a00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="847,56 880,56 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34dfaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="880,116 880,55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="19038@x" ObjectIDZND1="19037@x" ObjectIDZND2="g_4074a00@0" Pin0InfoVect0LinkObjId="SW-87846_0" Pin0InfoVect1LinkObjId="SW-87845_0" Pin0InfoVect2LinkObjId="g_4074a00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="880,116 880,55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_417f8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="880,55 880,31 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@x" ObjectIDND1="g_4074a00@0" ObjectIDZND0="19038@x" ObjectIDZND1="19037@x" Pin0InfoVect0LinkObjId="SW-87846_0" Pin0InfoVect1LinkObjId="SW-87845_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_4074a00_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="880,55 880,31 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d6c780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="834,253 833,265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_326e8b0@0" ObjectIDZND0="g_36213a0@0" Pin0InfoVect0LinkObjId="g_36213a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_326e8b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="834,253 833,265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a34a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1318,32 1300,32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3ea3ca0@0" ObjectIDZND0="19040@1" Pin0InfoVect0LinkObjId="SW-87857_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ea3ca0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1318,32 1300,32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34e05c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1264,32 1241,32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="19040@0" ObjectIDZND0="g_34e07b0@0" ObjectIDZND1="g_3cbc1d0@0" ObjectIDZND2="19039@x" Pin0InfoVect0LinkObjId="g_34e07b0_0" Pin0InfoVect1LinkObjId="g_3cbc1d0_0" Pin0InfoVect2LinkObjId="SW-87856_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87857_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1264,32 1241,32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d479c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1241,178 1241,161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2b0cdb0@0" ObjectIDZND0="g_3cbc1d0@0" Pin0InfoVect0LinkObjId="g_3cbc1d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b0cdb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1241,178 1241,161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2adce30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1172,-741 1253,-741 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="lightningRod" ObjectIDZND0="0@x" ObjectIDZND1="g_261ef40@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_261ef40_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1172,-741 1253,-741 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2add020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1253,-741 1315,-741 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_261ef40@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_261ef40_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1253,-741 1315,-741 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4370480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1237,-703 1253,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_261ef40@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_261ef40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1237,-703 1253,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a25fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1253,-741 1253,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="lightningRod" ObjectIDZND0="0@x" ObjectIDZND1="g_261ef40@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_261ef40_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1253,-741 1253,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25d17d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1253,-703 1253,-686 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_261ef40@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_261ef40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1253,-703 1253,-686 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3542090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1253,-641 1253,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1253,-641 1253,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b6e050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1252,-539 1252,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1252,-539 1252,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a4b820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1252,-367 1252,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1252,-367 1252,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fe03b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1208,57 1241,57 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="g_34e07b0@0" ObjectIDZND0="19040@x" ObjectIDZND1="19039@x" ObjectIDZND2="g_3cbc1d0@0" Pin0InfoVect0LinkObjId="SW-87857_0" Pin0InfoVect1LinkObjId="SW-87856_0" Pin0InfoVect2LinkObjId="g_3cbc1d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34e07b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1208,57 1241,57 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fb6d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1241,32 1241,56 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="19040@x" ObjectIDND1="19039@x" ObjectIDZND0="g_34e07b0@0" ObjectIDZND1="g_3cbc1d0@0" Pin0InfoVect0LinkObjId="g_34e07b0_0" Pin0InfoVect1LinkObjId="g_3cbc1d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-87857_0" Pin1InfoVect1LinkObjId="SW-87856_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1241,32 1241,56 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_359b0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1241,56 1241,118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="lightningRod" ObjectIDND0="g_34e07b0@0" ObjectIDND1="19040@x" ObjectIDND2="19039@x" ObjectIDZND0="g_3cbc1d0@1" Pin0InfoVect0LinkObjId="g_3cbc1d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_34e07b0_0" Pin1InfoVect1LinkObjId="SW-87857_0" Pin1InfoVect2LinkObjId="SW-87856_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1241,56 1241,118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_43719d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="333,-821 333,-809 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="333,-821 333,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25d0810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="333,-773 333,-758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="333,-773 333,-758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4110090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="333,-731 333,-714 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="333,-731 333,-714 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3caaea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="333,-678 333,-648 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="333,-678 333,-648 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40d78f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="834,198 834,184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_326e8b0@1" ObjectIDZND0="19042@0" Pin0InfoVect0LinkObjId="SW-87863_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_326e8b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="834,198 834,184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_359b930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="834,148 834,130 881,130 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="19042@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87863_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="834,148 834,130 881,130 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a77c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="724,-516 724,-532 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_368d430@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_368d430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="724,-516 724,-532 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_401b4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="724,-568 724,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="724,-568 724,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_435c670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="760,-589 724,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="760,-589 724,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35deaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="724,-589 333,-589 333,-648 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="724,-589 333,-589 333,-648 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e18210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="490,-172 490,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="19030@0" ObjectIDZND0="19032@1" Pin0InfoVect0LinkObjId="SW-87817_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="490,-172 490,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fc2a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="490,-348 490,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="19032@0" ObjectIDZND0="g_25e6300@0" ObjectIDZND1="42322@1" Pin0InfoVect0LinkObjId="g_25e6300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87817_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="490,-348 490,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25d1c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="348,-171 348,-109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="19030@0" ObjectIDZND0="19033@0" Pin0InfoVect0LinkObjId="SW-87824_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="348,-171 348,-109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25d1e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="348,-17 348,32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="generator" EndDevType2="lightningRod" ObjectIDND0="19033@1" ObjectIDZND0="19034@x" ObjectIDZND1="43341@x" ObjectIDZND2="g_3f97e40@0" Pin0InfoVect0LinkObjId="SW-87825_0" Pin0InfoVect1LinkObjId="SM-CX_XXC.P1_0" Pin0InfoVect2LinkObjId="g_3f97e40_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87824_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="348,-17 348,32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dbb790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="614,-172 614,-109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="19030@0" ObjectIDZND0="19035@0" Pin0InfoVect0LinkObjId="SW-87832_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="614,-172 614,-109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dbb9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="614,-17 614,32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="19035@1" ObjectIDZND0="g_2ab1e80@0" ObjectIDZND1="43342@x" ObjectIDZND2="19036@x" Pin0InfoVect0LinkObjId="g_2ab1e80_0" Pin0InfoVect1LinkObjId="SM-CX_XXC.P2_0" Pin0InfoVect2LinkObjId="SW-87833_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87832_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="614,-17 614,32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dbbc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="880,-172 880,-109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="19030@0" ObjectIDZND0="19037@0" Pin0InfoVect0LinkObjId="SW-87845_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="880,-172 880,-109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_435d270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="880,-17 880,31 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="19037@1" ObjectIDZND0="0@x" ObjectIDZND1="g_4074a00@0" ObjectIDZND2="19038@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_4074a00_0" Pin0InfoVect2LinkObjId="SW-87846_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87845_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="880,-17 880,31 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_435d4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1241,-172 1241,-109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="19030@0" ObjectIDZND0="19039@0" Pin0InfoVect0LinkObjId="SW-87856_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1241,-172 1241,-109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_435d730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1241,-17 1241,32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="19039@1" ObjectIDZND0="19040@x" ObjectIDZND1="g_34e07b0@0" ObjectIDZND2="g_3cbc1d0@0" Pin0InfoVect0LinkObjId="SW-87857_0" Pin0InfoVect1LinkObjId="g_34e07b0_0" Pin0InfoVect2LinkObjId="g_3cbc1d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87856_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1241,-17 1241,32 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1009" cy="-589" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1450" cy="-351" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1494" cy="-351" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1305" cy="-351" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1252" cy="-351" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="333" cy="-821" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-87757" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -47.000000 -849.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19023" ObjectName="DYN-CX_XXC"/>
     <cge:Meas_Ref ObjectId="87757"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35ae590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1178.000000 211.000000) translate(0,15)">1号动态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cde720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 843.000000 -439.000000) translate(0,12)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2485b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 307.000000 -69.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ef980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 941.000000 -233.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b0d170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 191.000000 -195.000000) translate(0,15)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a91cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 376.000000 41.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3616e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 308.000000 150.000000) translate(0,15)">1号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b12900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b12900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b12900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b12900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b12900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b12900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b12900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b12900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b12900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b12900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b12900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b12900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b12900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b12900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b12900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b12900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b12900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b12900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3558610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3558610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3558610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3558610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3558610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3558610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3558610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_3db6ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -200.000000 -929.500000) translate(0,16)">小西村光伏电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29c1ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 299.000000 168.000000) translate(0,15)">1-10号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40133c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 322.500000 186.000000) translate(0,15)">10.5MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35a7fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1221.500000 230.000000) translate(0,15)">±4MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a19b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 839.500000 213.000000) translate(0,15)">1号站用变及消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_26664f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 926.000000 106.000000) translate(0,13)">1号接地站变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_26664f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 926.000000 106.000000) translate(0,29)">DKSC-630/35-125/0.4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_26664f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 926.000000 106.000000) translate(0,45)">38.5±2x2.5%/0.4kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_26664f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 926.000000 106.000000) translate(0,61)">Ud=2.18% Zn,yn11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3559460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 263.500000 -851.000000) translate(0,15)">110kV哨房变35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_412b360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1017.000000 -656.000000) translate(0,15)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_412b360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1017.000000 -656.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_412b360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1017.000000 -656.000000) translate(0,51)">能</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_412b360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1017.000000 -656.000000) translate(0,69)">禹</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_412b360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1017.000000 -656.000000) translate(0,87)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34cbc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 451.000000 -308.500000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d8e800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 455.000000 -497.000000) translate(0,15)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d8e800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 455.000000 -497.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d8e800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 455.000000 -497.000000) translate(0,51)">小</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d8e800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 455.000000 -497.000000) translate(0,69)">西</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d8e800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 455.000000 -497.000000) translate(0,87)">村</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d8e800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 455.000000 -497.000000) translate(0,105)">T</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d8e800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 455.000000 -497.000000) translate(0,123)">接</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d8e800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 455.000000 -497.000000) translate(0,141)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_263e710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 471.000000 -614.000000) translate(0,15)">T接点</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a90f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 595.000000 -610.000000) translate(0,15)">35kV哨能线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_43ac260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 460.000000 -587.000000) translate(0,15)">N7</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ce2d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 355.000000 -640.500000) translate(0,12)">36367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33c68f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 294.000000 -752.500000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3faa260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 285.000000 -702.500000) translate(0,12)">3636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_326e530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 831.000000 -616.500000) translate(0,12)">332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41284c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 895.000000 -616.500000) translate(0,12)">3321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2305490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 760.000000 -616.500000) translate(0,12)">3326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4268dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1280.500000 -272.000000) translate(0,15)">变电站站用电接线示意图</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2318540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1185.500000 -337.000000) translate(0,15)">0.4kV备用段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34355b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1461.500000 -340.000000) translate(0,15)">0.4kV Ⅰ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_42a2b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.500000 -584.000000) translate(0,15)">施工变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_469cd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1174.000000 -791.000000) translate(0,15)">10kV站用变架空进线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_469cd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1174.000000 -791.000000) translate(0,33)">10kV马道地支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3352390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1444.000000 -752.000000) translate(0,15)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29e9780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1513.500000 -669.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a3c3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 291.000000 -796.500000) translate(0,12)">3631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42a2060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 675.000000 -554.500000) translate(0,12)">33267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4372d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 573.000000 -69.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3675d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 642.000000 41.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2600a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 574.000000 150.000000) translate(0,15)">2号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ce4f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 565.000000 168.000000) translate(0,15)">11-19号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3bdb490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 588.500000 186.000000) translate(0,15)">9.45MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_329d8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 839.000000 -69.000000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ca0af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 908.000000 40.000000) translate(0,12)">36367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41c5470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1200.000000 -69.000000) translate(0,12)">364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a25bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1269.000000 41.000000) translate(0,12)">36467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d47bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1260.000000 137.000000) translate(0,12)">4000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26447c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 794.000000 162.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3d05c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -206.000000 6.000000) translate(0,17)">4719</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3d05c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -206.000000 6.000000) translate(0,38)">8215826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_32c3950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -72.000000 -781.000000) translate(0,16)">AGC/AVC</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 324.000000 -722.642292)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 866.357708 -580.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1433.000000 -290.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1262.000000 -466.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1504.000000 -503.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87817">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 480.000000 -249.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19032" ObjectName="SW-CX_XXC.CX_XXC_351BK"/>
     <cge:Meas_Ref ObjectId="87817"/>
    <cge:TPSR_Ref TObjectID="19032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87824">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 338.000000 -10.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19033" ObjectName="SW-CX_XXC.CX_XXC_361BK"/>
     <cge:Meas_Ref ObjectId="87824"/>
    <cge:TPSR_Ref TObjectID="19033"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87832">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 604.000000 -10.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19035" ObjectName="SW-CX_XXC.CX_XXC_362BK"/>
     <cge:Meas_Ref ObjectId="87832"/>
    <cge:TPSR_Ref TObjectID="19035"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87845">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 870.000000 -10.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19037" ObjectName="SW-CX_XXC.CX_XXC_363BK"/>
     <cge:Meas_Ref ObjectId="87845"/>
    <cge:TPSR_Ref TObjectID="19037"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87856">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1231.000000 -10.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19039" ObjectName="SW-CX_XXC.CX_XXC_364BK"/>
     <cge:Meas_Ref ObjectId="87856"/>
    <cge:TPSR_Ref TObjectID="19039"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_XXC.CX_XXC_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="187,-172 1692,-172 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="19030" ObjectName="BS-CX_XXC.CX_XXC_3IM"/>
    <cge:TPSR_Ref TObjectID="19030"/></metadata>
   <polyline fill="none" opacity="0" points="187,-172 1692,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="196,-821 492,-821 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="196,-821 492,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1009,-513 1009,-674 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1009,-513 1009,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1209,-351 1341,-351 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1209,-351 1341,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1420,-351 1552,-351 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1420,-351 1552,-351 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3cfe710">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 914.000000 -299.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b0cdb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1224.000000 208.000000)" xlink:href="#lightningRod:shape177"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25e6300">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 517.000000 -483.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fc7470">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 962.000000 -299.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f97e40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 256.000000 65.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ab1e80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 522.000000 65.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4074a00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 788.000000 64.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_326e8b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 825.000000 258.000000)" xlink:href="#lightningRod:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34e07b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1149.000000 65.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3cbc1d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1228.000000 115.000000)" xlink:href="#lightningRod:shape115"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_261ef40">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1178.000000 -695.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4289a90">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1480.000000 -566.000000)" xlink:href="#lightningRod:shape197"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_363b030">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1578.000000 -552.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-87788" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 254.000000 -289.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87788" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19030"/>
     <cge:Term_Ref ObjectID="26453"/>
    <cge:TPSR_Ref TObjectID="19030"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-87789" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 254.000000 -289.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87789" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19030"/>
     <cge:Term_Ref ObjectID="26453"/>
    <cge:TPSR_Ref TObjectID="19030"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-87790" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 254.000000 -289.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87790" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19030"/>
     <cge:Term_Ref ObjectID="26453"/>
    <cge:TPSR_Ref TObjectID="19030"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-87791" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 254.000000 -289.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87791" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19030"/>
     <cge:Term_Ref ObjectID="26453"/>
    <cge:TPSR_Ref TObjectID="19030"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-87785" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 254.000000 -289.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87785" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19030"/>
     <cge:Term_Ref ObjectID="26453"/>
    <cge:TPSR_Ref TObjectID="19030"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-87784" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 254.000000 -289.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87784" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19030"/>
     <cge:Term_Ref ObjectID="26453"/>
    <cge:TPSR_Ref TObjectID="19030"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-87766" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 593.000000 -322.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87766" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19032"/>
     <cge:Term_Ref ObjectID="26456"/>
    <cge:TPSR_Ref TObjectID="19032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-87767" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 593.000000 -322.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87767" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19032"/>
     <cge:Term_Ref ObjectID="26456"/>
    <cge:TPSR_Ref TObjectID="19032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-87765" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 593.000000 -322.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87765" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19032"/>
     <cge:Term_Ref ObjectID="26456"/>
    <cge:TPSR_Ref TObjectID="19032"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-87770" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 347.000000 209.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87770" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19033"/>
     <cge:Term_Ref ObjectID="26458"/>
    <cge:TPSR_Ref TObjectID="19033"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-87771" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 347.000000 209.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87771" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19033"/>
     <cge:Term_Ref ObjectID="26458"/>
    <cge:TPSR_Ref TObjectID="19033"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-87769" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 347.000000 209.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87769" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19033"/>
     <cge:Term_Ref ObjectID="26458"/>
    <cge:TPSR_Ref TObjectID="19033"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-87774" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 613.000000 209.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87774" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19035"/>
     <cge:Term_Ref ObjectID="26462"/>
    <cge:TPSR_Ref TObjectID="19035"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-87775" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 613.000000 209.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87775" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19035"/>
     <cge:Term_Ref ObjectID="26462"/>
    <cge:TPSR_Ref TObjectID="19035"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-87773" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 613.000000 209.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87773" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19035"/>
     <cge:Term_Ref ObjectID="26462"/>
    <cge:TPSR_Ref TObjectID="19035"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-87778" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 942.000000 242.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87778" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19037"/>
     <cge:Term_Ref ObjectID="26466"/>
    <cge:TPSR_Ref TObjectID="19037"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-87779" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 942.000000 242.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87779" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19037"/>
     <cge:Term_Ref ObjectID="26466"/>
    <cge:TPSR_Ref TObjectID="19037"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-87777" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 942.000000 242.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87777" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19037"/>
     <cge:Term_Ref ObjectID="26466"/>
    <cge:TPSR_Ref TObjectID="19037"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-87782" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1238.000000 261.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87782" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19039"/>
     <cge:Term_Ref ObjectID="26470"/>
    <cge:TPSR_Ref TObjectID="19039"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-87781" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1238.000000 261.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="87781" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19039"/>
     <cge:Term_Ref ObjectID="26470"/>
    <cge:TPSR_Ref TObjectID="19039"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="172" x="-227" y="-940"/></g>
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-276" y="-957"/></g>
   <g href="AVC小西村1.svg" style="fill-opacity:0"><rect height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="-98" y="-795"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4075d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 291.000000 -208.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3df6150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 280.000000 -223.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2600ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 305.000000 -238.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fcc9e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 886.000000 -241.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b09870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 875.000000 -256.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2625db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 900.000000 -271.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42c9110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 535.000000 323.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42c8aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 524.000000 308.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29b25c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 549.000000 293.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3646590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 557.000000 -208.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35fb890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 546.000000 -223.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_426e7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 571.000000 -238.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25d3420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1171.000000 -261.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36615e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1196.000000 -276.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fe3e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 185.000000 231.400000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fe40c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 195.000000 289.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4371f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 195.000000 260.866667) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_43721d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 195.000000 274.933333) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4372410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 200.000000 217.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40f4d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 191.000000 246.466667) translate(0,12)">3U0(V):</text>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 865.000000 209.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 865.000000 209.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.940000 -0.000000 0.000000 -0.911111 1229.000000 -535.000000)" xlink:href="#transformer2:shape1_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.940000 -0.000000 0.000000 -0.911111 1229.000000 -535.000000)" xlink:href="#transformer2:shape1_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="172" x="-227" y="-940"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="172" x="-227" y="-940"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-276" y="-957"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-276" y="-957"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="-98,-795 -101,-798 -101,-744 -98,-747 -98,-795" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="-98,-795 -101,-798 48,-798 45,-795 -98,-795" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(112,119,119)" points="-98,-747 -101,-744 48,-744 45,-747 -98,-747" stroke="rgb(112,119,119)"/>
     <polygon fill="rgb(112,119,119)" points="45,-795 48,-798 48,-744 45,-747 45,-795" stroke="rgb(112,119,119)"/>
     <rect fill="rgb(224,238,238)" height="48" stroke="rgb(224,238,238)" width="143" x="-98" y="-795"/>
     <rect fill="none" height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="-98" y="-795"/>
    </a>
   <metadata/></g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_363e7c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 882.000000 -351.000000)" xlink:href="#voltageTransformer:shape71"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -239.000000 -881.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116492" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -216.000000 -744.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116492" ObjectName="CX_XXC:CX_XXC_ZJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116493" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -217.000000 -706.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116493" ObjectName="CX_XXC:CX_XXC_ZJ_sumQ"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_XXC"/>
</svg>