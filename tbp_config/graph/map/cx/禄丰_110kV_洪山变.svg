<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-24" aopId="270" id="thSvg" product="E8000V2" version="1.0" viewBox="3013 -1263 2189 1261">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape18">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="52" x2="52" y1="45" y2="16"/>
    <polyline arcFlag="1" points="25,101 23,101 21,100 20,100 18,99 17,98 15,97 14,95 13,93 13,92 12,90 12,88 12,86 13,84 13,83 14,81 15,80 17,78 18,77 20,76 21,76 23,75 25,75 27,75 29,76 30,76 32,77 33,78 35,80 36,81 37,83 37,84 38,86 38,88 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="38" x2="26" y1="88" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="25" x2="25" y1="100" y2="108"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="15" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="54" y2="47"/>
    <polyline arcFlag="1" points="43,15 44,15 45,15 45,15 46,15 46,16 47,16 47,17 48,17 48,18 48,18 48,19 49,20 49,20 49,21 48,22 48,22 48,23 48,23 47,24 47,24 46,25 46,25 45,25 45,26 44,26 43,26 " stroke-width="1"/>
    <polyline arcFlag="1" points="43,25 44,25 45,25 45,25 46,26 46,26 47,26 47,27 48,27 48,28 48,29 48,29 49,30 49,31 49,31 48,32 48,33 48,33 48,34 47,34 47,35 46,35 46,35 45,36 45,36 44,36 43,36 " stroke-width="1"/>
    <polyline arcFlag="1" points="43,36 44,36 45,36 45,37 46,37 46,37 47,38 47,38 48,39 48,39 48,40 48,40 49,41 49,42 49,42 48,43 48,44 48,44 48,45 47,45 47,46 46,46 46,47 45,47 45,47 44,47 43,47 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="26" x2="26" y1="88" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="9" x2="42" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="26" x2="41" y1="8" y2="8"/>
    <rect height="24" stroke-width="0.398039" width="12" x="1" y="23"/>
    <rect height="23" stroke-width="0.398039" width="12" x="20" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="8" x2="42" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="7" x2="7" y1="54" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="8" x2="8" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="43" x2="43" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="26" x2="26" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="16" y2="24"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="13" x2="4" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="13" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="20" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="58" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="8" y2="37"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape59">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="72" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="23" y2="23"/>
    <circle cx="9" cy="9" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="20" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="13" y1="63" y2="63"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,61 9,39 9,30 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="11" y="48"/>
   </symbol>
   <symbol id="lightningRod:shape103">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.786486" x1="23" x2="22" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.84362" x1="1" x2="22" y1="46" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.786486" x1="22" x2="15" y1="7" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.565049" x1="10" x2="10" y1="35" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.675211" x1="11" x2="11" y1="5" y2="16"/>
    <polyline arcFlag="1" points="11,28 11,28 11,28 11,28 12,28 12,28 12,27 12,27 13,27 13,27 13,26 13,26 13,26 13,25 13,25 13,24 13,24 13,24 13,23 12,23 12,23 12,23 12,22 11,22 11,22 11,22 " stroke-width="0.0340106"/>
    <polyline arcFlag="1" points="10,34 11,34 11,34 11,34 11,34 12,34 12,34 12,33 12,33 12,33 12,32 13,32 13,32 13,31 13,31 13,31 12,30 12,30 12,30 12,29 12,29 12,29 11,29 11,28 11,28 11,28 " stroke-width="0.0340106"/>
    <polyline arcFlag="1" points="11,22 11,22 11,22 11,22 12,22 12,21 12,21 12,21 13,21 13,20 13,20 13,20 13,19 13,19 13,19 13,18 13,18 13,17 13,17 12,17 12,17 12,16 12,16 11,16 11,16 11,16 " stroke-width="0.0340106"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.532624" x1="9" x2="12" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.565049" x1="8" x2="13" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.565049" x1="4" x2="17" y1="46" y2="46"/>
   </symbol>
   <symbol id="lightningRod:shape106">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="40" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="54" x2="43" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="42" x2="42" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="35" x2="35" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.676705" x1="34" x2="16" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="16" x2="16" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="9" x2="9" y1="48" y2="31"/>
    <circle cx="30" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="26" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="20" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="0" x2="9" y1="40" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape148">
    <circle cx="28" cy="40" fillStyle="0" r="6.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="8" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="46" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="46" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="28" y1="29" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="31" y1="29" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="28" y1="32" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="38" y1="40" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="41" y1="40" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="38" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="40" y1="31" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="40" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="35" y1="31" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="28" y1="40" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="31" y1="40" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="28" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="21" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="2" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="4" x2="11" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="58" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="25" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="1" x2="14" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="1" x2="14" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="6" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="1" x2="14" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="1" x2="14" y1="25" y2="25"/>
    <circle cx="37" cy="29" fillStyle="0" r="6.5" stroke-width="1"/>
    <circle cx="37" cy="40" fillStyle="0" r="6.5" stroke-width="1"/>
    <circle cx="27" cy="29" fillStyle="0" r="6.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape149">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="72" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183673" x1="6" x2="13" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183673" x1="10" x2="13" y1="26" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183673" x1="10" x2="6" y1="26" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183673" x1="7" x2="10" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183673" x1="10" x2="12" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183673" x1="10" x2="10" y1="4" y2="7"/>
    <rect height="4" stroke-width="1" width="19" x="11" y="48"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,61 9,39 9,30 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="13" y1="63" y2="63"/>
    <circle cx="9" cy="20" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="9" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape164">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="60" x2="60" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="57" x2="57" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="48" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="48" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="57" x2="57" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="60" x2="60" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="5" y2="8"/>
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
   </symbol>
   <symbol id="lightningRod:shape33">
    <ellipse cx="7" cy="23" fillStyle="0" rx="6.5" ry="6" stroke-width="0.45993"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="8" x2="8" y1="29" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="29" x2="28" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="30" x2="29" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="29" x2="29" y1="34" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="29" x2="8" y1="34" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.258981" x1="29" x2="29" y1="16" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.258981" x1="34" x2="24" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.258981" x1="31" x2="27" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.212121" x1="30" x2="28" y1="2" y2="2"/>
    <rect height="14" stroke-width="0.571429" width="6" x="26" y="16"/>
    <circle cx="14" cy="17" fillStyle="0" r="6.5" stroke-width="0.45993"/>
    <circle cx="7" cy="13" fillStyle="0" r="6.5" stroke-width="0.45993"/>
   </symbol>
   <symbol id="lightningRod:shape25">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0182374" x1="13" x2="13" y1="32" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0326126" x1="8" x2="13" y1="29" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0326126" x1="17" x2="13" y1="29" y2="32"/>
    <ellipse cx="12" cy="32" rx="12.5" ry="12" stroke-width="0.120929"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0433242" x1="16" x2="9" y1="12" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0433242" x1="9" x2="9" y1="17" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0433242" x1="16" x2="9" y1="12" y2="7"/>
    <circle cx="12" cy="12" r="12.5" stroke-width="0.120929"/>
   </symbol>
   <symbol id="load:shape12">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="6" x2="6" y1="25" y2="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,8 6,2 11,8 " stroke-width="1.14286"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="transformer:shape4_0">
    <circle cx="68" cy="45" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="60" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="58" y1="90" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="55" x2="60" y1="90" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="60" y1="56" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="74" x2="67" y1="46" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="82" x2="74" y1="53" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="74" x2="74" y1="37" y2="46"/>
   </symbol>
   <symbol id="transformer:shape4_1">
    <circle cx="38" cy="61" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="37" x2="30" y1="66" y2="73"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="37" y1="73" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="37" x2="37" y1="57" y2="66"/>
   </symbol>
   <symbol id="transformer:shape4-2">
    <circle cx="38" cy="29" fillStyle="0" r="24.5" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="47" x2="32" y1="23" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="47" x2="32" y1="23" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="32" x2="32" y1="32" y2="15"/>
   </symbol>
   <symbol id="voltageTransformer:shape15">
    <ellipse cx="23" cy="24" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="2" y1="20" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="2" y1="26" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="14" y2="6"/>
    <rect height="13" stroke-width="1" width="7" x="4" y="14"/>
    <ellipse cx="34" cy="24" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <ellipse cx="23" cy="35" rx="7.5" ry="7" stroke-width="0.66594"/>
    <ellipse cx="34" cy="35" rx="7.5" ry="7" stroke-width="0.66594"/>
    <polyline points="24,36 8,36 8,26 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="3" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="21" x2="23" y1="38" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="24" x2="24" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="26" x2="23" y1="38" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="35" x2="35" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="32" x2="34" y1="38" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="37" x2="34" y1="38" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.246311" x1="34" x2="32" y1="27" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.245503" x1="35" x2="37" y1="27" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.238574" x1="37" x2="32" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="23" x2="23" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="20" x2="23" y1="26" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="26" x2="23" y1="26" y2="24"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2542150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2577db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ded9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24f5120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30a8e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30a6680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30e4700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_30e51a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_30e6820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_30e6820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31bb5e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31bb5e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31bd050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31bd050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_31be070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31bfce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_31c0950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2e3f100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2e3f850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e410a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e41ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e423b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2e42b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e43bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e44570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e45060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2e45a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_31c95a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_31ca520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_31cb620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31cc2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31da660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31d2a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_31cd3e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_31ce760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1271" width="2199" x="3008" y="-1268"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4534,-191 4556,-191 4545,-208 4534,-191 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3618,-210 3640,-210 3629,-227 3618,-210 " stroke="rgb(255,255,255)"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-24066">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4098.000000 -1001.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3797" ObjectName="SW-CX_HS.CX_HS_172BK"/>
     <cge:Meas_Ref ObjectId="24066"/>
    <cge:TPSR_Ref TObjectID="3797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23875">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3712.000000 -994.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3781" ObjectName="SW-CX_HS.CX_HS_171BK"/>
     <cge:Meas_Ref ObjectId="23875"/>
    <cge:TPSR_Ref TObjectID="3781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24174">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3843.000000 -841.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3813" ObjectName="SW-CX_HS.CX_HS_112BK"/>
     <cge:Meas_Ref ObjectId="24174"/>
    <cge:TPSR_Ref TObjectID="3813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24975">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3539.000000 -431.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3872" ObjectName="SW-CX_HS.CX_HS_073BK"/>
     <cge:Meas_Ref ObjectId="24975"/>
    <cge:TPSR_Ref TObjectID="3872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24887">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3604.000000 -430.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3860" ObjectName="SW-CX_HS.CX_HS_074BK"/>
     <cge:Meas_Ref ObjectId="24887"/>
    <cge:TPSR_Ref TObjectID="3860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23914">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3590.000000 -597.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3794" ObjectName="SW-CX_HS.CX_HS_001BK"/>
     <cge:Meas_Ref ObjectId="23914"/>
    <cge:TPSR_Ref TObjectID="3794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24283">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3771.000000 -432.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3821" ObjectName="SW-CX_HS.CX_HS_075BK"/>
     <cge:Meas_Ref ObjectId="24283"/>
    <cge:TPSR_Ref TObjectID="3821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24382">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3943.000000 -432.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3831" ObjectName="SW-CX_HS.CX_HS_077BK"/>
     <cge:Meas_Ref ObjectId="24382"/>
    <cge:TPSR_Ref TObjectID="3831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24245">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3994.000000 -602.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3818" ObjectName="SW-CX_HS.CX_HS_012BK"/>
     <cge:Meas_Ref ObjectId="24245"/>
    <cge:TPSR_Ref TObjectID="3818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24104">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4215.000000 -597.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3810" ObjectName="SW-CX_HS.CX_HS_002BK"/>
     <cge:Meas_Ref ObjectId="24104"/>
    <cge:TPSR_Ref TObjectID="3810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24320">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4483.000000 -433.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3824" ObjectName="SW-CX_HS.CX_HS_084BK"/>
     <cge:Meas_Ref ObjectId="24320"/>
    <cge:TPSR_Ref TObjectID="3824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24906">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4556.000000 -434.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3863" ObjectName="SW-CX_HS.CX_HS_085BK"/>
     <cge:Meas_Ref ObjectId="24906"/>
    <cge:TPSR_Ref TObjectID="3863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24441">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4268.000000 -432.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3839" ObjectName="SW-CX_HS.CX_HS_083BK"/>
     <cge:Meas_Ref ObjectId="24441"/>
    <cge:TPSR_Ref TObjectID="3839"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24412">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.000000 -432.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3835" ObjectName="SW-CX_HS.CX_HS_082BK"/>
     <cge:Meas_Ref ObjectId="24412"/>
    <cge:TPSR_Ref TObjectID="3835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24352">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3839.000000 -432.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3827" ObjectName="SW-CX_HS.CX_HS_076BK"/>
     <cge:Meas_Ref ObjectId="24352"/>
    <cge:TPSR_Ref TObjectID="3827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4860.000000 -165.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3763.000000 -78.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4096.000000 -78.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4235.000000 -78.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4788.000000 -165.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5007.000000 -166.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4832.000000 -1133.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4832.000000 -1089.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24954">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4831.000000 -1041.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3869" ObjectName="SW-CX_HS.CX_HS_373BK"/>
     <cge:Meas_Ref ObjectId="24954"/>
    <cge:TPSR_Ref TObjectID="3869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24486">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4830.000000 -909.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3846" ObjectName="SW-CX_HS.CX_HS_374BK"/>
     <cge:Meas_Ref ObjectId="24486"/>
    <cge:TPSR_Ref TObjectID="3846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24579">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4830.000000 -856.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3849" ObjectName="SW-CX_HS.CX_HS_375BK"/>
     <cge:Meas_Ref ObjectId="24579"/>
    <cge:TPSR_Ref TObjectID="3849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24613">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4830.000000 -777.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3852" ObjectName="SW-CX_HS.CX_HS_376BK"/>
     <cge:Meas_Ref ObjectId="24613"/>
    <cge:TPSR_Ref TObjectID="3852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24647">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4828.000000 -687.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3855" ObjectName="SW-CX_HS.CX_HS_381BK"/>
     <cge:Meas_Ref ObjectId="24647"/>
    <cge:TPSR_Ref TObjectID="3855"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24929">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4830.000000 -567.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3866" ObjectName="SW-CX_HS.CX_HS_382BK"/>
     <cge:Meas_Ref ObjectId="24929"/>
    <cge:TPSR_Ref TObjectID="3866"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4830.000000 -516.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4830.000000 -472.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4832.000000 -429.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23900">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.918919 -0.000000 0.000000 -1.000000 4640.000000 -941.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3790" ObjectName="SW-CX_HS.CX_HS_301BK"/>
     <cge:Meas_Ref ObjectId="23900"/>
    <cge:TPSR_Ref TObjectID="3790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24469">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4659.000000 -723.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3843" ObjectName="SW-CX_HS.CX_HS_312BK"/>
     <cge:Meas_Ref ObjectId="24469"/>
    <cge:TPSR_Ref TObjectID="3843"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24091">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4630.000000 -613.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3806" ObjectName="SW-CX_HS.CX_HS_302BK"/>
     <cge:Meas_Ref ObjectId="24091"/>
    <cge:TPSR_Ref TObjectID="3806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24994">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4612.000000 -443.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3873" ObjectName="SW-CX_HS.CX_HS_086BK"/>
     <cge:Meas_Ref ObjectId="24994"/>
    <cge:TPSR_Ref TObjectID="3873"/></metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_HS.CX_HS_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="5537"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3562.000000 -713.000000)" xlink:href="#transformer:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="5539"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3562.000000 -713.000000)" xlink:href="#transformer:shape4_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="5541"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3562.000000 -713.000000)" xlink:href="#transformer:shape4-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="3874" ObjectName="TF-CX_HS.CX_HS_1T"/>
    <cge:TPSR_Ref TObjectID="3874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_HS.CX_HS_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="5544"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4187.000000 -713.000000)" xlink:href="#transformer:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="5546"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4187.000000 -713.000000)" xlink:href="#transformer:shape4_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="5548"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4187.000000 -713.000000)" xlink:href="#transformer:shape4-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="3875" ObjectName="TF-CX_HS.CX_HS_2T"/>
    <cge:TPSR_Ref TObjectID="3875"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_HS.CX_HS_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3517,-539 3992,-539 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="3767" ObjectName="BS-CX_HS.CX_HS_9IM"/>
    <cge:TPSR_Ref TObjectID="3767"/></metadata>
   <polyline fill="none" opacity="0" points="3517,-539 3992,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_HS.CX_HS_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4051,-539 4654,-539 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="3768" ObjectName="BS-CX_HS.CX_HS_9IIM"/>
    <cge:TPSR_Ref TObjectID="3768"/></metadata>
   <polyline fill="none" opacity="0" points="4051,-539 4654,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4769,-122 4887,-122 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4769,-122 4887,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4915,-122 5033,-122 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4915,-122 5033,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_HS.CX_HS_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4743,-1165 4743,-763 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="3765" ObjectName="BS-CX_HS.CX_HS_3IM"/>
    <cge:TPSR_Ref TObjectID="3765"/></metadata>
   <polyline fill="none" opacity="0" points="4743,-1165 4743,-763 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_HS.CX_HS_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4743,-725 4743,-412 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="3766" ObjectName="BS-CX_HS.CX_HS_3IIM"/>
    <cge:TPSR_Ref TObjectID="3766"/></metadata>
   <polyline fill="none" opacity="0" points="4743,-725 4743,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_HS.CX_HS_1IM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3515,-922 3826,-922 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="3763" ObjectName="BS-CX_HS.CX_HS_1IM"/>
    <cge:TPSR_Ref TObjectID="3763"/></metadata>
   <polyline fill="none" opacity="0" points="3515,-922 3826,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_HS.CX_HS_1IIM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3898,-925 4259,-925 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="3764" ObjectName="BS-CX_HS.CX_HS_1IIM"/>
    <cge:TPSR_Ref TObjectID="3764"/></metadata>
   <polyline fill="none" opacity="0" points="3898,-925 4259,-925 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_HS.CX_HS_1C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3824.000000 -169.000000)" xlink:href="#capacitor:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11722" ObjectName="CB-CX_HS.CX_HS_1C"/>
    <cge:TPSR_Ref TObjectID="11722"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_HS.CX_HS_2C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3928.000000 -167.000000)" xlink:href="#capacitor:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11723" ObjectName="CB-CX_HS.CX_HS_2C"/>
    <cge:TPSR_Ref TObjectID="11723"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_HS.CX_HS_3C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4137.000000 -168.000000)" xlink:href="#capacitor:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11724" ObjectName="CB-CX_HS.CX_HS_3C"/>
    <cge:TPSR_Ref TObjectID="11724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_HS.CX_HS_4C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4253.000000 -180.000000)" xlink:href="#capacitor:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11725" ObjectName="CB-CX_HS.CX_HS_4C"/>
    <cge:TPSR_Ref TObjectID="11725"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_33618c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3665.000000 -819.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3315480" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3625.000000 -947.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3317ae0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3624.000000 -1025.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_332cde0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3781.000000 -982.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33dfa80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3781.000000 -1035.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_331d270" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3781.000000 -1095.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33c7230" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4290.000000 -841.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_336c520" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4167.000000 -1105.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3370330" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4167.000000 -1045.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33707c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4167.000000 -992.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3521650" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4017.000000 -954.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33e8100" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4015.000000 -1032.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33c7630" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3800.000000 -768.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3492470" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3922.000000 -771.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33ebb80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3512.000000 -840.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33439a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4017.000000 -310.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3415800" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4343.000000 -310.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33bf580" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4227.000000 -310.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34625f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3913.000000 -310.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_346f710" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4774.000000 -295.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34a1a30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4994.000000 -302.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33d8670" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4358.000000 -839.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_31edff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3600,-798 3600,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="3874@1" ObjectIDZND0="3788@x" ObjectIDZND1="3787@x" Pin0InfoVect0LinkObjId="SW-23888_0" Pin0InfoVect1LinkObjId="SW-23887_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_353bb50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3600,-798 3600,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3299c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3600,-825 3617,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="3874@x" ObjectIDND1="3787@x" ObjectIDZND0="3788@0" Pin0InfoVect0LinkObjId="SW-23888_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_353bb50_0" Pin1InfoVect1LinkObjId="SW-23887_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3600,-825 3617,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_32a5460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3653,-825 3670,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3788@1" ObjectIDZND0="g_33618c0@0" Pin0InfoVect0LinkObjId="g_33618c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23888_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3653,-825 3670,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_328b960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3600,-825 3600,-844 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer" EndDevType0="switch" ObjectIDND0="3788@x" ObjectIDND1="3874@x" ObjectIDZND0="3787@0" Pin0InfoVect0LinkObjId="SW-23887_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-23888_0" Pin1InfoVect1LinkObjId="g_353bb50_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3600,-825 3600,-844 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e41710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3613,-953 3630,-953 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3772@1" ObjectIDZND0="g_3315480@0" Pin0InfoVect0LinkObjId="g_3315480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23713_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3613,-953 3630,-953 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_328ecd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3612,-1031 3629,-1031 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3771@1" ObjectIDZND0="g_3317ae0@0" Pin0InfoVect0LinkObjId="g_3317ae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23712_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3612,-1031 3629,-1031 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_329a0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3560,-1031 3560,-1047 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="3771@x" ObjectIDND1="3770@x" ObjectIDZND0="g_3599490@0" Pin0InfoVect0LinkObjId="g_3599490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-23712_0" Pin1InfoVect1LinkObjId="SW-23711_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3560,-1031 3560,-1047 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3319590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3576,-1031 3560,-1031 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3771@0" ObjectIDZND0="g_3599490@0" ObjectIDZND1="3770@x" Pin0InfoVect0LinkObjId="g_3599490_0" Pin0InfoVect1LinkObjId="SW-23711_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23712_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3576,-1031 3560,-1031 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_32a51b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3560,-1031 3560,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="3771@x" ObjectIDND1="g_3599490@0" ObjectIDZND0="3770@1" Pin0InfoVect0LinkObjId="SW-23711_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-23712_0" Pin1InfoVect1LinkObjId="g_3599490_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3560,-1031 3560,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_32fad90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-988 3733,-988 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="3785@x" ObjectIDND1="3781@x" ObjectIDZND0="3786@0" Pin0InfoVect0LinkObjId="SW-23886_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-23885_0" Pin1InfoVect1LinkObjId="SW-23875_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-988 3733,-988 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3613d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3769,-988 3786,-988 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3786@1" ObjectIDZND0="g_332cde0@0" Pin0InfoVect0LinkObjId="g_332cde0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23886_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3769,-988 3786,-988 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3675be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-988 3721,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="3786@x" ObjectIDND1="3781@x" ObjectIDZND0="3785@1" Pin0InfoVect0LinkObjId="SW-23885_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-23886_0" Pin1InfoVect1LinkObjId="SW-23875_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-988 3721,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_367b5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-1002 3721,-988 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="3781@0" ObjectIDZND0="3785@x" ObjectIDZND1="3786@x" Pin0InfoVect0LinkObjId="SW-23885_0" Pin0InfoVect1LinkObjId="SW-23886_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23875_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-1002 3721,-988 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3319280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-1041 3733,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="3781@x" ObjectIDND1="3782@x" ObjectIDZND0="3784@0" Pin0InfoVect0LinkObjId="SW-23884_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-23875_0" Pin1InfoVect1LinkObjId="SW-23882_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-1041 3733,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_330a590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3769,-1041 3786,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3784@1" ObjectIDZND0="g_33dfa80@0" Pin0InfoVect0LinkObjId="g_33dfa80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23884_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3769,-1041 3786,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_332af50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-1041 3721,-1029 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="3784@x" ObjectIDND1="3782@x" ObjectIDZND0="3781@1" Pin0InfoVect0LinkObjId="SW-23875_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-23884_0" Pin1InfoVect1LinkObjId="SW-23882_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-1041 3721,-1029 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3326f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-1055 3721,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="3782@0" ObjectIDZND0="3781@x" ObjectIDZND1="3784@x" Pin0InfoVect0LinkObjId="SW-23875_0" Pin0InfoVect1LinkObjId="SW-23884_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23882_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-1055 3721,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_355c230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-1101 3733,-1101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="3782@x" ObjectIDND1="g_3545da0@0" ObjectIDND2="g_34aa950@0" ObjectIDZND0="3783@0" Pin0InfoVect0LinkObjId="SW-23883_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-23882_0" Pin1InfoVect1LinkObjId="g_3545da0_0" Pin1InfoVect2LinkObjId="g_34aa950_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-1101 3733,-1101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34a6280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3769,-1101 3786,-1101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3783@1" ObjectIDZND0="g_331d270@0" Pin0InfoVect0LinkObjId="g_331d270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23883_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3769,-1101 3786,-1101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3310ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-1101 3721,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="3783@x" ObjectIDND1="g_3545da0@0" ObjectIDND2="g_34aa950@0" ObjectIDZND0="3782@1" Pin0InfoVect0LinkObjId="SW-23882_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-23883_0" Pin1InfoVect1LinkObjId="g_3545da0_0" Pin1InfoVect2LinkObjId="g_34aa950_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-1101 3721,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_351e400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-1123 3735,-1123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="3782@x" ObjectIDND1="3783@x" ObjectIDND2="g_34aa950@0" ObjectIDZND0="g_3545da0@0" Pin0InfoVect0LinkObjId="g_3545da0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-23882_0" Pin1InfoVect1LinkObjId="SW-23883_0" Pin1InfoVect2LinkObjId="g_34aa950_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-1123 3735,-1123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_367e2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-1123 3721,-1101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3545da0@0" ObjectIDND1="g_34aa950@0" ObjectIDND2="9182@1" ObjectIDZND0="3782@x" ObjectIDZND1="3783@x" Pin0InfoVect0LinkObjId="SW-23882_0" Pin0InfoVect1LinkObjId="SW-23883_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3545da0_0" Pin1InfoVect1LinkObjId="g_34aa950_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-1123 3721,-1101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3674920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-1143 3721,-1123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="9182@1" ObjectIDZND0="3782@x" ObjectIDZND1="3783@x" ObjectIDZND2="g_3545da0@0" Pin0InfoVect0LinkObjId="SW-23882_0" Pin0InfoVect1LinkObjId="SW-23883_0" Pin0InfoVect2LinkObjId="g_3545da0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-1143 3721,-1123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_32fc010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-847 4242,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="3875@x" ObjectIDND1="3803@x" ObjectIDZND0="3804@0" Pin0InfoVect0LinkObjId="SW-24079_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_353c270_0" Pin1InfoVect1LinkObjId="SW-24078_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4225,-847 4242,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33e0670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4278,-847 4295,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3804@1" ObjectIDZND0="g_33c7230@0" Pin0InfoVect0LinkObjId="g_33c7230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24079_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4278,-847 4295,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33133b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4125,-1138 4107,-1138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3546820@0" ObjectIDZND0="3798@x" ObjectIDZND1="3799@x" ObjectIDZND2="g_34abca0@0" Pin0InfoVect0LinkObjId="SW-24073_0" Pin0InfoVect1LinkObjId="SW-24074_0" Pin0InfoVect2LinkObjId="g_34abca0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3546820_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4125,-1138 4107,-1138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_32fbbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-1138 4107,-1150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_3546820@0" ObjectIDND1="3798@x" ObjectIDND2="3799@x" ObjectIDZND0="19327@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3546820_0" Pin1InfoVect1LinkObjId="SW-24073_0" Pin1InfoVect2LinkObjId="SW-24074_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-1138 4107,-1150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_335f120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4108,-1138 4091,-1138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3546820@0" ObjectIDND1="3798@x" ObjectIDND2="3799@x" ObjectIDZND0="g_34abca0@0" Pin0InfoVect0LinkObjId="g_34abca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3546820_0" Pin1InfoVect1LinkObjId="SW-24073_0" Pin1InfoVect2LinkObjId="SW-24074_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4108,-1138 4091,-1138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3364d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-1111 4119,-1111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="3798@x" ObjectIDND1="g_3546820@0" ObjectIDND2="g_34abca0@0" ObjectIDZND0="3799@0" Pin0InfoVect0LinkObjId="SW-24074_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-24073_0" Pin1InfoVect1LinkObjId="g_3546820_0" Pin1InfoVect2LinkObjId="g_34abca0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-1111 4119,-1111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_336c2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4155,-1111 4172,-1111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3799@1" ObjectIDZND0="g_336c520@0" Pin0InfoVect0LinkObjId="g_336c520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24074_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4155,-1111 4172,-1111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33f6bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-1051 4119,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="3797@x" ObjectIDND1="3798@x" ObjectIDZND0="3800@0" Pin0InfoVect0LinkObjId="SW-24075_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24066_0" Pin1InfoVect1LinkObjId="SW-24073_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-1051 4119,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33700d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4155,-1051 4172,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3800@1" ObjectIDZND0="g_3370330@0" Pin0InfoVect0LinkObjId="g_3370330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24075_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4155,-1051 4172,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3416bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-998 4119,-998 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="3801@x" ObjectIDND1="3797@x" ObjectIDZND0="3802@0" Pin0InfoVect0LinkObjId="SW-24077_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24076_0" Pin1InfoVect1LinkObjId="SW-24066_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-998 4119,-998 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3416e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4155,-998 4172,-998 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3802@1" ObjectIDZND0="g_33707c0@0" Pin0InfoVect0LinkObjId="g_33707c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24077_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4155,-998 4172,-998 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3560820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-1098 4107,-1111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="3798@1" ObjectIDZND0="3799@x" ObjectIDZND1="g_3546820@0" ObjectIDZND2="g_34abca0@0" Pin0InfoVect0LinkObjId="SW-24074_0" Pin0InfoVect1LinkObjId="g_3546820_0" Pin0InfoVect2LinkObjId="g_34abca0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24073_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-1098 4107,-1111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33183c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-1111 4107,-1138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="3798@x" ObjectIDND1="3799@x" ObjectIDZND0="g_3546820@0" ObjectIDZND1="g_34abca0@0" ObjectIDZND2="19327@1" Pin0InfoVect0LinkObjId="g_3546820_0" Pin0InfoVect1LinkObjId="g_34abca0_0" Pin0InfoVect2LinkObjId="g_32fbbb0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24073_0" Pin1InfoVect1LinkObjId="SW-24074_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-1111 4107,-1138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3318620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-1036 4107,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="3797@1" ObjectIDZND0="3800@x" ObjectIDZND1="3798@x" Pin0InfoVect0LinkObjId="SW-24075_0" Pin0InfoVect1LinkObjId="SW-24073_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24066_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-1036 4107,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_331d630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-1051 4107,-1062 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="3797@x" ObjectIDND1="3800@x" ObjectIDZND0="3798@0" Pin0InfoVect0LinkObjId="SW-24073_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24066_0" Pin1InfoVect1LinkObjId="SW-24075_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-1051 4107,-1062 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_331d890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-985 4107,-998 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="3801@1" ObjectIDZND0="3802@x" ObjectIDZND1="3797@x" Pin0InfoVect0LinkObjId="SW-24077_0" Pin0InfoVect1LinkObjId="SW-24066_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24076_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-985 4107,-998 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33e9dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-998 4107,-1009 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="3801@x" ObjectIDND1="3802@x" ObjectIDZND0="3797@0" Pin0InfoVect0LinkObjId="SW-24066_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24076_0" Pin1InfoVect1LinkObjId="SW-24077_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-998 4107,-1009 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3521410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4005,-960 4022,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3775@1" ObjectIDZND0="g_3521650@0" Pin0InfoVect0LinkObjId="g_3521650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23718_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4005,-960 4022,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33e7ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4003,-1038 4020,-1038 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3774@1" ObjectIDZND0="g_33e8100@0" Pin0InfoVect0LinkObjId="g_33e8100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23717_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4003,-1038 4020,-1038 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34a4d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-1038 3951,-1052 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="3774@x" ObjectIDND1="3773@x" ObjectIDZND0="g_359bec0@0" Pin0InfoVect0LinkObjId="g_359bec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-23717_0" Pin1InfoVect1LinkObjId="SW-23716_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-1038 3951,-1052 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34a5060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-1038 3951,-1038 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3774@0" ObjectIDZND0="g_359bec0@0" ObjectIDZND1="3773@x" Pin0InfoVect0LinkObjId="g_359bec0_0" Pin0InfoVect1LinkObjId="SW-23716_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23717_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-1038 3951,-1038 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34a52c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-1038 3951,-1019 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="3774@x" ObjectIDND1="g_359bec0@0" ObjectIDZND0="3773@1" Pin0InfoVect0LinkObjId="SW-23716_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-23717_0" Pin1InfoVect1LinkObjId="g_359bec0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-1038 3951,-1019 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_355b100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3805,-851 3852,-851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="3814@x" ObjectIDND1="3815@x" ObjectIDZND0="3813@1" Pin0InfoVect0LinkObjId="SW-24174_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24180_0" Pin1InfoVect1LinkObjId="SW-24181_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3805,-851 3852,-851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3564790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3806,-800 3806,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3815@0" ObjectIDZND0="g_33c7630@0" Pin0InfoVect0LinkObjId="g_33c7630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24181_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3806,-800 3806,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35649f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3927,-802 3928,-788 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3817@0" ObjectIDZND0="g_3492470@0" Pin0InfoVect0LinkObjId="g_3492470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24183_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3927,-802 3928,-788 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33d4000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3551,-779 3551,-792 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="3874@x" ObjectIDND1="g_33e7140@0" ObjectIDND2="3789@x" ObjectIDZND0="g_335da30@0" Pin0InfoVect0LinkObjId="g_335da30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_353bb50_0" Pin1InfoVect1LinkObjId="g_33e7140_0" Pin1InfoVect2LinkObjId="SW-23897_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3551,-779 3551,-792 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33d4260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3600,-779 3551,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="3874@x" ObjectIDZND0="g_335da30@0" ObjectIDZND1="g_33e7140@0" ObjectIDZND2="3789@x" Pin0InfoVect0LinkObjId="g_335da30_0" Pin0InfoVect1LinkObjId="g_33e7140_0" Pin0InfoVect2LinkObjId="SW-23897_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_353bb50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3600,-779 3551,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33d44c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3551,-779 3518,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3874@x" ObjectIDND1="g_335da30@0" ObjectIDZND0="g_33e7140@0" ObjectIDZND1="3789@x" Pin0InfoVect0LinkObjId="g_33e7140_0" Pin0InfoVect1LinkObjId="SW-23897_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_353bb50_0" Pin1InfoVect1LinkObjId="g_335da30_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3551,-779 3518,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33d4720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3518,-779 3491,-779 3491,-794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="3874@x" ObjectIDND1="g_335da30@0" ObjectIDND2="3789@x" ObjectIDZND0="g_33e7140@0" Pin0InfoVect0LinkObjId="g_33e7140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_353bb50_0" Pin1InfoVect1LinkObjId="g_335da30_0" Pin1InfoVect2LinkObjId="SW-23897_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3518,-779 3491,-779 3491,-794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33eb6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3518,-779 3518,-794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_33e7140@0" ObjectIDND1="3874@x" ObjectIDND2="g_335da30@0" ObjectIDZND0="3789@0" Pin0InfoVect0LinkObjId="SW-23897_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_33e7140_0" Pin1InfoVect1LinkObjId="g_353bb50_0" Pin1InfoVect2LinkObjId="g_335da30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3518,-779 3518,-794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33eb920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3518,-830 3518,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3789@1" ObjectIDZND0="g_33ebb80@0" Pin0InfoVect0LinkObjId="g_33ebb80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23897_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3518,-830 3518,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33ed2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3548,-539 3548,-522 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3767@0" ObjectIDZND0="3822@1" Pin0InfoVect0LinkObjId="SW-24977_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33d14f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3548,-539 3548,-522 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34736d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3548,-486 3548,-466 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3822@0" ObjectIDZND0="3872@1" Pin0InfoVect0LinkObjId="SW-24975_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24977_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3548,-486 3548,-466 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3481340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3613,-539 3613,-521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3767@0" ObjectIDZND0="3861@1" Pin0InfoVect0LinkObjId="SW-24889_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33d14f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3613,-539 3613,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3371950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3613,-485 3613,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3861@0" ObjectIDZND0="3860@1" Pin0InfoVect0LinkObjId="SW-24887_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24889_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3613,-485 3613,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34365f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-449 3663,-449 3663,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3437510@0" ObjectIDND1="3778@x" ObjectIDZND0="g_3436820@0" Pin0InfoVect0LinkObjId="g_3436820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3437510_0" Pin1InfoVect1LinkObjId="SW-23734_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3696,-449 3663,-449 3663,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34c4230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-449 3696,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3436820@0" ObjectIDND1="3778@x" ObjectIDZND0="g_3437510@0" Pin0InfoVect0LinkObjId="g_3437510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3436820_0" Pin1InfoVect1LinkObjId="SW-23734_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3696,-449 3696,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34c4490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-392 3696,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_3437510@1" ObjectIDZND0="g_359ffd0@0" Pin0InfoVect0LinkObjId="g_359ffd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3437510_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3696,-392 3696,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34c46f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-539 3696,-520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3767@0" ObjectIDZND0="3778@1" Pin0InfoVect0LinkObjId="SW-23734_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33d14f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3696,-539 3696,-520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34c4950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-484 3696,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="3778@0" ObjectIDZND0="g_3437510@0" ObjectIDZND1="g_3436820@0" Pin0InfoVect0LinkObjId="g_3437510_0" Pin0InfoVect1LinkObjId="g_3436820_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23734_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3696,-484 3696,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33d14f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3599,-552 3599,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3795@0" ObjectIDZND0="3767@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23916_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3599,-552 3599,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33d1750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3599,-588 3599,-605 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3795@1" ObjectIDZND0="3794@0" Pin0InfoVect0LinkObjId="SW-23914_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23916_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3599,-588 3599,-605 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33d19b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3599,-632 3599,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3794@1" ObjectIDZND0="3796@0" Pin0InfoVect0LinkObjId="SW-23917_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23914_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3599,-632 3599,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_330d690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3780,-539 3780,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3767@0" ObjectIDZND0="10453@1" Pin0InfoVect0LinkObjId="SW-24285_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33d14f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3780,-539 3780,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3365ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3780,-487 3780,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="10453@0" ObjectIDZND0="3821@1" Pin0InfoVect0LinkObjId="SW-24283_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24285_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3780,-487 3780,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33c6fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3952,-539 3952,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3767@0" ObjectIDZND0="3832@1" Pin0InfoVect0LinkObjId="SW-24384_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33d14f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3952,-539 3952,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3422a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3952,-487 3952,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3832@0" ObjectIDZND0="3831@1" Pin0InfoVect0LinkObjId="SW-24382_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24384_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3952,-487 3952,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33434e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3952,-316 3969,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="3833@x" ObjectIDND1="g_353d700@0" ObjectIDND2="11723@x" ObjectIDZND0="3834@0" Pin0InfoVect0LinkObjId="SW-24386_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-24385_0" Pin1InfoVect1LinkObjId="g_353d700_0" Pin1InfoVect2LinkObjId="CB-CX_HS.CX_HS_2C_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3952,-316 3969,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3343740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4005,-316 4022,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3834@1" ObjectIDZND0="g_33439a0@0" Pin0InfoVect0LinkObjId="g_33439a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24386_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4005,-316 4022,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3362cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3952,-329 3952,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="capacitor" ObjectIDND0="3833@0" ObjectIDZND0="3834@x" ObjectIDZND1="g_353d700@0" ObjectIDZND2="11723@x" Pin0InfoVect0LinkObjId="SW-24386_0" Pin0InfoVect1LinkObjId="g_353d700_0" Pin0InfoVect2LinkObjId="CB-CX_HS.CX_HS_2C_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24385_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3952,-329 3952,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3427b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4084,-539 4084,-521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3768@0" ObjectIDZND0="3780@1" Pin0InfoVect0LinkObjId="SW-23744_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_367c7e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4084,-539 4084,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33d2080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4084,-485 4084,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3780@0" ObjectIDZND0="g_3427df0@1" Pin0InfoVect0LinkObjId="g_3427df0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23744_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4084,-485 4084,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33d22e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4084,-437 4084,-408 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3427df0@0" ObjectIDZND0="g_35b5b40@0" Pin0InfoVect0LinkObjId="g_35b5b40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3427df0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4084,-437 4084,-408 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3482b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3970,-539 3970,-561 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3767@0" ObjectIDZND0="3820@0" Pin0InfoVect0LinkObjId="SW-24247_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33d14f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3970,-539 3970,-561 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_367c7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4069,-562 4069,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3819@0" ObjectIDZND0="3768@0" Pin0InfoVect0LinkObjId="g_34755c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24246_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4069,-562 4069,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3457ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4069,-598 4069,-612 4030,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3819@1" ObjectIDZND0="3818@0" Pin0InfoVect0LinkObjId="SW-24245_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24246_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4069,-598 4069,-612 4030,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3457f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4003,-612 3970,-612 3970,-597 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3818@1" ObjectIDZND0="3820@1" Pin0InfoVect0LinkObjId="SW-24247_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24245_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4003,-612 3970,-612 3970,-597 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3475360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4224,-605 4224,-588 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3810@0" ObjectIDZND0="3811@1" Pin0InfoVect0LinkObjId="SW-24106_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24104_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4224,-605 4224,-588 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34755c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4224,-552 4224,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3811@0" ObjectIDZND0="3768@0" Pin0InfoVect0LinkObjId="g_367c7e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24106_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4224,-552 4224,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3475820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4224,-645 4224,-632 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3812@0" ObjectIDZND0="3810@1" Pin0InfoVect0LinkObjId="SW-24104_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24107_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4224,-645 4224,-632 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3475a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4418,-448 4385,-448 4385,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_343bc10@0" ObjectIDND1="3779@x" ObjectIDZND0="g_3475ce0@0" Pin0InfoVect0LinkObjId="g_3475ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_343bc10_0" Pin1InfoVect1LinkObjId="SW-23737_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4418,-448 4385,-448 4385,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_343c3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4418,-448 4418,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_3475ce0@0" ObjectIDND1="3779@x" ObjectIDZND0="g_343bc10@0" Pin0InfoVect0LinkObjId="g_343bc10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3475ce0_0" Pin1InfoVect1LinkObjId="SW-23737_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4418,-448 4418,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_343c640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4418,-391 4418,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_343bc10@1" ObjectIDZND0="g_35a2a40@0" Pin0InfoVect0LinkObjId="g_35a2a40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_343bc10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4418,-391 4418,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_343eed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4492,-539 4492,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3768@0" ObjectIDZND0="3825@1" Pin0InfoVect0LinkObjId="SW-24322_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_367c7e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4492,-539 4492,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3322c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4492,-488 4492,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3825@0" ObjectIDZND0="3824@1" Pin0InfoVect0LinkObjId="SW-24320_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24322_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4492,-488 4492,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_347bdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-539 4565,-525 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3768@0" ObjectIDZND0="3864@1" Pin0InfoVect0LinkObjId="SW-24908_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_367c7e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-539 4565,-525 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3490040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-489 4565,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3864@0" ObjectIDZND0="3863@1" Pin0InfoVect0LinkObjId="SW-24906_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24908_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-489 4565,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3476b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4277,-539 4277,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3768@0" ObjectIDZND0="3840@1" Pin0InfoVect0LinkObjId="SW-24443_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_367c7e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4277,-539 4277,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3354b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4277,-487 4277,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3840@0" ObjectIDZND0="3839@1" Pin0InfoVect0LinkObjId="SW-24441_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24443_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4277,-487 4277,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3415340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4277,-316 4294,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="3841@x" ObjectIDND1="g_353f1e0@0" ObjectIDND2="11725@x" ObjectIDZND0="3842@0" Pin0InfoVect0LinkObjId="SW-24445_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-24444_0" Pin1InfoVect1LinkObjId="g_353f1e0_0" Pin1InfoVect2LinkObjId="CB-CX_HS.CX_HS_4C_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4277,-316 4294,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34155a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4331,-316 4348,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3842@1" ObjectIDZND0="g_3415800@0" Pin0InfoVect0LinkObjId="g_3415800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24445_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4331,-316 4348,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3416290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4277,-329 4277,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="capacitor" ObjectIDND0="3841@0" ObjectIDZND0="3842@x" ObjectIDZND1="g_353f1e0@0" ObjectIDZND2="11725@x" Pin0InfoVect0LinkObjId="SW-24445_0" Pin0InfoVect1LinkObjId="g_353f1e0_0" Pin0InfoVect2LinkObjId="CB-CX_HS.CX_HS_4C_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24444_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4277,-329 4277,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34164f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-539 4161,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3768@0" ObjectIDZND0="3836@1" Pin0InfoVect0LinkObjId="SW-24414_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_367c7e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-539 4161,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3457290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-487 4161,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3836@0" ObjectIDZND0="3835@1" Pin0InfoVect0LinkObjId="SW-24412_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24414_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-487 4161,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33bf0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-316 4178,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="3837@x" ObjectIDND1="g_353e470@0" ObjectIDND2="11724@x" ObjectIDZND0="3838@0" Pin0InfoVect0LinkObjId="SW-24416_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-24415_0" Pin1InfoVect1LinkObjId="g_353e470_0" Pin1InfoVect2LinkObjId="CB-CX_HS.CX_HS_3C_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-316 4178,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33bf320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4215,-316 4232,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3838@1" ObjectIDZND0="g_33bf580@0" Pin0InfoVect0LinkObjId="g_33bf580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24416_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4215,-316 4232,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33f7e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-329 4161,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="capacitor" ObjectIDND0="3837@0" ObjectIDZND0="3838@x" ObjectIDZND1="g_353e470@0" ObjectIDZND2="11724@x" Pin0InfoVect0LinkObjId="SW-24416_0" Pin0InfoVect1LinkObjId="g_353e470_0" Pin0InfoVect2LinkObjId="CB-CX_HS.CX_HS_3C_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24415_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-329 4161,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33f80b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3848,-539 3848,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3767@0" ObjectIDZND0="3828@1" Pin0InfoVect0LinkObjId="SW-24354_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33d14f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3848,-539 3848,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_336dbf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3848,-487 3848,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3828@0" ObjectIDZND0="3827@1" Pin0InfoVect0LinkObjId="SW-24352_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24354_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3848,-487 3848,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3462130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3848,-316 3865,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="3829@x" ObjectIDND1="g_353c990@0" ObjectIDND2="11722@x" ObjectIDZND0="3830@0" Pin0InfoVect0LinkObjId="SW-24356_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-24355_0" Pin1InfoVect1LinkObjId="g_353c990_0" Pin1InfoVect2LinkObjId="CB-CX_HS.CX_HS_1C_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3848,-316 3865,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3462390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3901,-316 3918,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3830@1" ObjectIDZND0="g_34625f0@0" Pin0InfoVect0LinkObjId="g_34625f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24356_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3901,-316 3918,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3463000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3848,-329 3848,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="capacitor" ObjectIDND0="3829@0" ObjectIDZND0="3830@x" ObjectIDZND1="g_353c990@0" ObjectIDZND2="11722@x" Pin0InfoVect0LinkObjId="SW-24356_0" Pin0InfoVect1LinkObjId="g_353c990_0" Pin0InfoVect2LinkObjId="CB-CX_HS.CX_HS_1C_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24355_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3848,-329 3848,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3471a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4869,-200 4869,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4869,-200 4869,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3471ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4869,-122 4869,-142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4869,-122 4869,-142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3471f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4869,-160 4869,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4869,-160 4869,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3428760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3747,-88 3772,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3747,-88 3772,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34289c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3676,-88 3676,-60 3691,-60 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_34ba320@0" ObjectIDND2="10452@x" ObjectIDZND0="g_354d3a0@0" Pin0InfoVect0LinkObjId="g_354d3a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_34ba320_0" Pin1InfoVect2LinkObjId="SW-24978_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3676,-88 3676,-60 3691,-60 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3428c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3676,-88 3711,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_354d3a0@0" ObjectIDND1="g_34ba320@0" ObjectIDND2="10452@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_354d3a0_0" Pin1InfoVect1LinkObjId="g_34ba320_0" Pin1InfoVect2LinkObjId="SW-24978_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3676,-88 3711,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3428e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-88 3943,-61 3958,-61 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_354e110@0" Pin0InfoVect0LinkObjId="g_354e110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-88 3943,-61 3958,-61 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3434d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3799,-88 3943,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_354e110@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_354e110_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3799,-88 3943,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3434fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4450,-88 4450,-57 4435,-57 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="10455@x" ObjectIDND2="g_3538700@0" ObjectIDZND0="g_3435200@0" Pin0InfoVect0LinkObjId="g_3435200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-24997_0" Pin1InfoVect2LinkObjId="g_3538700_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4450,-88 4450,-57 4435,-57 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3449d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4797,-173 4797,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4797,-173 4797,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3449f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4797,-142 4797,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4797,-142 4797,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_344a1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4797,-214 4797,-200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4797,-214 4797,-200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_346f4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4780,-290 4780,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_346f710@0" Pin0InfoVect0LinkObjId="g_346f710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4780,-290 4780,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34701a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4534,-307 4534,-320 4565,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3537ca0@0" ObjectIDZND0="3865@x" ObjectIDZND1="g_34383e0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-24909_0" Pin0InfoVect1LinkObjId="g_34383e0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3537ca0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4534,-307 4534,-320 4565,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3470400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-320 4565,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3537ca0@0" ObjectIDND1="g_34383e0@0" ObjectIDND2="0@x" ObjectIDZND0="3865@0" Pin0InfoVect0LinkObjId="SW-24909_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3537ca0_0" Pin1InfoVect1LinkObjId="g_34383e0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-320 4565,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3437800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4797,-231 4797,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="3865@x" ObjectIDZND2="g_3537ca0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-24909_0" Pin0InfoVect2LinkObjId="g_3537ca0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4797,-231 4797,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3437a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4797,-245 4780,-245 4780,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="3865@x" ObjectIDND2="g_3537ca0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-24909_0" Pin1InfoVect2LinkObjId="g_3537ca0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4797,-245 4780,-245 4780,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3437cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4798,-260 4810,-260 4810,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="3865@x" ObjectIDND1="g_3537ca0@0" ObjectIDND2="0@x" ObjectIDZND0="g_34383e0@0" Pin0InfoVect0LinkObjId="g_34383e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-24909_0" Pin1InfoVect1LinkObjId="g_3537ca0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4798,-260 4810,-260 4810,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3437f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-320 4565,-198 4719,-198 4719,-340 4798,-340 4798,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="3865@x" ObjectIDND1="g_3537ca0@0" ObjectIDZND0="g_34383e0@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_34383e0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24909_0" Pin1InfoVect1LinkObjId="g_3537ca0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-320 4565,-198 4719,-198 4719,-340 4798,-340 4798,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3438180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4798,-260 4798,-245 4797,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="3865@x" ObjectIDND1="g_3537ca0@0" ObjectIDND2="g_34383e0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-24909_0" Pin1InfoVect1LinkObjId="g_3537ca0_0" Pin1InfoVect2LinkObjId="g_34383e0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4798,-260 4798,-245 4797,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3494f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5016,-122 5016,-143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5016,-122 5016,-143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34951e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5016,-161 5016,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5016,-161 5016,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3495440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5016,-201 5016,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5016,-201 5016,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34a17d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5000,-297 5000,-307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_34a1a30@0" Pin0InfoVect0LinkObjId="g_34a1a30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5000,-297 5000,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34a24c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4319,-778 4319,-791 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="3875@x" ObjectIDND1="g_33d9e70@0" ObjectIDND2="3805@x" ObjectIDZND0="g_33d9100@0" Pin0InfoVect0LinkObjId="g_33d9100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_353c270_0" Pin1InfoVect1LinkObjId="g_33d9e70_0" Pin1InfoVect2LinkObjId="SW-24088_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4319,-778 4319,-791 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33d8410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-829 4352,-844 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3805@1" ObjectIDZND0="g_33d8670@0" Pin0InfoVect0LinkObjId="g_33d8670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24088_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-829 4352,-844 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33daa40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-798 4225,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="3875@1" ObjectIDZND0="3804@x" ObjectIDZND1="3803@x" Pin0InfoVect0LinkObjId="SW-24079_0" Pin0InfoVect1LinkObjId="SW-24078_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_353c270_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4225,-798 4225,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33daca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-847 4225,-862 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="3875@x" ObjectIDND1="3804@x" ObjectIDZND0="3803@0" Pin0InfoVect0LinkObjId="SW-24078_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_353c270_0" Pin1InfoVect1LinkObjId="SW-24079_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4225,-847 4225,-862 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33daf00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-793 4352,-778 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer" EndDevType2="lightningRod" ObjectIDND0="3805@0" ObjectIDZND0="g_33d9e70@0" ObjectIDZND1="3875@x" ObjectIDZND2="g_33d9100@0" Pin0InfoVect0LinkObjId="g_33d9e70_0" Pin0InfoVect1LinkObjId="g_353c270_0" Pin0InfoVect2LinkObjId="g_33d9100_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24088_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-793 4352,-778 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33db160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-778 4379,-778 4379,-793 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="3805@x" ObjectIDND1="3875@x" ObjectIDND2="g_33d9100@0" ObjectIDZND0="g_33d9e70@0" Pin0InfoVect0LinkObjId="g_33d9e70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-24088_0" Pin1InfoVect1LinkObjId="g_353c270_0" Pin1InfoVect2LinkObjId="g_33d9100_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-778 4379,-778 4379,-793 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33db3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4224,-778 4319,-778 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="3875@x" ObjectIDZND0="g_33d9100@0" ObjectIDZND1="g_33d9e70@0" ObjectIDZND2="3805@x" Pin0InfoVect0LinkObjId="g_33d9100_0" Pin0InfoVect1LinkObjId="g_33d9e70_0" Pin0InfoVect2LinkObjId="SW-24088_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_353c270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4224,-778 4319,-778 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33db620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4319,-778 4352,-778 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3875@x" ObjectIDND1="g_33d9100@0" ObjectIDZND0="g_33d9e70@0" ObjectIDZND1="3805@x" Pin0InfoVect0LinkObjId="g_33d9e70_0" Pin0InfoVect1LinkObjId="SW-24088_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_353c270_0" Pin1InfoVect1LinkObjId="g_33d9100_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4319,-778 4352,-778 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3611090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3613,-363 3613,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3862@1" ObjectIDZND0="g_33dc5d0@1" Pin0InfoVect0LinkObjId="g_33dc5d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24890_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3613,-363 3613,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36112f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3613,-426 3613,-438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_33dc5d0@0" ObjectIDZND0="3860@0" Pin0InfoVect0LinkObjId="SW-24887_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33dc5d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3613,-426 3613,-438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3611550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3548,-429 3548,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_33db880@0" ObjectIDZND0="3872@0" Pin0InfoVect0LinkObjId="SW-24975_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33db880_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3548,-429 3548,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36117b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3952,-365 3952,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3833@1" ObjectIDZND0="g_34b4a10@1" Pin0InfoVect0LinkObjId="g_34b4a10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24385_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3952,-365 3952,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3611a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3952,-429 3952,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_34b4a10@0" ObjectIDZND0="3831@0" Pin0InfoVect0LinkObjId="SW-24382_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34b4a10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3952,-429 3952,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3611c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3848,-365 3848,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3829@1" ObjectIDZND0="g_34b3cc0@1" Pin0InfoVect0LinkObjId="g_34b3cc0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24355_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3848,-365 3848,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3611ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3848,-429 3848,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_34b3cc0@0" ObjectIDZND0="3827@0" Pin0InfoVect0LinkObjId="SW-24352_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34b3cc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3848,-429 3848,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3612130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3780,-365 3780,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3823@1" ObjectIDZND0="g_34b2f70@1" Pin0InfoVect0LinkObjId="g_34b2f70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24286_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3780,-365 3780,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3612390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3780,-430 3780,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_34b2f70@0" ObjectIDZND0="3821@0" Pin0InfoVect0LinkObjId="SW-24283_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34b2f70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3780,-430 3780,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36125f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-365 4161,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3837@1" ObjectIDZND0="g_34b5760@1" Pin0InfoVect0LinkObjId="g_34b5760_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24415_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-365 4161,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3612850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-428 4161,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_34b5760@0" ObjectIDZND0="3835@0" Pin0InfoVect0LinkObjId="SW-24412_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34b5760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-428 4161,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3612ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4277,-365 4277,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3841@1" ObjectIDZND0="g_34b64b0@1" Pin0InfoVect0LinkObjId="g_34b64b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24444_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4277,-365 4277,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3612d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4277,-429 4277,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_34b64b0@0" ObjectIDZND0="3839@0" Pin0InfoVect0LinkObjId="SW-24441_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34b64b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4277,-429 4277,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3612f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4492,-366 4492,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3826@1" ObjectIDZND0="g_3610340@1" Pin0InfoVect0LinkObjId="g_3610340_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24323_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4492,-366 4492,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36131d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4492,-429 4492,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_3610340@0" ObjectIDZND0="3824@0" Pin0InfoVect0LinkObjId="SW-24320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3610340_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4492,-429 4492,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3613430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-367 4565,-378 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3865@1" ObjectIDZND0="g_360f5f0@1" Pin0InfoVect0LinkObjId="g_360f5f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24909_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-367 4565,-378 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3613690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-431 4565,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_360f5f0@0" ObjectIDZND0="3863@0" Pin0InfoVect0LinkObjId="SW-24906_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_360f5f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-431 4565,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3446ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4743,-1143 4787,-1143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3765@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_344c5b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4743,-1143 4787,-1143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_351a230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4823,-1143 4841,-1143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4823,-1143 4841,-1143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3467460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4823,-1099 4841,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4823,-1099 4841,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34676c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4743,-1099 4787,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3765@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_344c5b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4743,-1099 4787,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_340fa00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4884,-1051 4867,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3871@0" ObjectIDZND0="3869@0" Pin0InfoVect0LinkObjId="SW-24954_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24957_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4884,-1051 4867,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3413fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-919 4866,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3848@0" ObjectIDZND0="3846@0" Pin0InfoVect0LinkObjId="SW-24486_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24489_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-919 4866,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_349f270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5006,-866 5006,-848 5017,-848 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="g_3548300@0" ObjectIDND1="g_3527340@0" ObjectIDND2="18075@1" ObjectIDZND0="g_349e190@0" Pin0InfoVect0LinkObjId="g_349e190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3548300_0" Pin1InfoVect1LinkObjId="g_3527340_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5006,-866 5006,-848 5017,-848 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_349f4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5080,-866 5006,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="18075@1" ObjectIDZND0="g_349e190@0" ObjectIDZND1="g_3548300@0" ObjectIDZND2="g_3527340@0" Pin0InfoVect0LinkObjId="g_349e190_0" Pin0InfoVect1LinkObjId="g_3548300_0" Pin0InfoVect2LinkObjId="g_3527340_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5080,-866 5006,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3557e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-787 4866,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3854@0" ObjectIDZND0="3852@0" Pin0InfoVect0LinkObjId="SW-24613_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24616_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-787 4866,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3559160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5006,-787 5006,-770 5017,-770 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="g_3549070@0" ObjectIDND1="g_352ab80@0" ObjectIDND2="18066@1" ObjectIDZND0="g_3558080@0" Pin0InfoVect0LinkObjId="g_3558080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3549070_0" Pin1InfoVect1LinkObjId="g_352ab80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5006,-787 5006,-770 5017,-770 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_344c5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4722,-951 4743,-951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3791@1" ObjectIDZND0="3765@0" Pin0InfoVect0LinkObjId="g_3576d90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23902_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4722,-951 4743,-951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3450db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4878,-697 4864,-697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3857@0" ObjectIDZND0="3855@0" Pin0InfoVect0LinkObjId="SW-24647_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4878,-697 4864,-697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33efd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5004,-697 5004,-680 5015,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="g_3549de0@0" ObjectIDND1="g_3529ca0@0" ObjectIDND2="48926@1" ObjectIDZND0="g_3451010@0" Pin0InfoVect0LinkObjId="g_3451010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3549de0_0" Pin1InfoVect1LinkObjId="g_3529ca0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5004,-697 5004,-680 5015,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33f0820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4837,-645 4863,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_354b8c0@0" ObjectIDND1="3777@x" ObjectIDZND0="g_33effa0@0" Pin0InfoVect0LinkObjId="g_33effa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_354b8c0_0" Pin1InfoVect1LinkObjId="SW-23724_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4837,-645 4863,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33f0a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-645 4949,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_33effa0@1" ObjectIDZND0="g_35abec0@0" Pin0InfoVect0LinkObjId="g_35abec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33effa0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-645 4949,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33f5020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-577 4866,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3868@0" ObjectIDZND0="3866@0" Pin0InfoVect0LinkObjId="SW-24929_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24932_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-577 4866,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33f5280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5014,-577 5014,-559 5026,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="g_3527970@0" ObjectIDND1="12151@x" ObjectIDZND0="g_354ab50@0" Pin0InfoVect0LinkObjId="g_354ab50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3527970_0" Pin1InfoVect1LinkObjId="EC-CX_HS.CX_HS_382Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5014,-577 5014,-559 5026,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33fe6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-526 4866,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-526 4866,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_345ce80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-482 4866,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-482 4866,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3461360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4882,-439 4868,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4882,-439 4868,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_342d180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4743,-798 4723,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3765@0" ObjectIDZND0="3845@1" Pin0InfoVect0LinkObjId="SW-24471_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_344c5b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4743,-798 4723,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_342f910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4723,-692 4743,-692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3844@1" ObjectIDZND0="3766@0" Pin0InfoVect0LinkObjId="g_34320a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24470_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4723,-692 4743,-692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34320a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4716,-623 4743,-623 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3807@1" ObjectIDZND0="3766@0" Pin0InfoVect0LinkObjId="g_342f910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24093_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4716,-623 4743,-623 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3486e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4672,-951 4686,-951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3790@0" ObjectIDZND0="3791@0" Pin0InfoVect0LinkObjId="SW-23902_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4672,-951 4686,-951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3417920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4666,-623 4680,-623 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3806@0" ObjectIDZND0="3807@0" Pin0InfoVect0LinkObjId="SW-24093_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24091_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4666,-623 4680,-623 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3417b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-623 4639,-623 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3808@1" ObjectIDZND0="3806@1" Pin0InfoVect0LinkObjId="SW-24091_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24094_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-623 4639,-623 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_341d5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4497,-623 4497,-637 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer" EndDevType0="lightningRod" ObjectIDND0="3808@x" ObjectIDND1="3875@x" ObjectIDZND0="g_33d1c10@0" Pin0InfoVect0LinkObjId="g_33d1c10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24094_0" Pin1InfoVect1LinkObjId="g_353c270_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4497,-623 4497,-637 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_341d810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4497,-623 4585,-623 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" ObjectIDND0="g_33d1c10@0" ObjectIDND1="3875@x" ObjectIDZND0="3808@0" Pin0InfoVect0LinkObjId="SW-24094_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33d1c10_0" Pin1InfoVect1LinkObjId="g_353c270_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4497,-623 4585,-623 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_341da70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4280,-758 4407,-758 4407,-623 4499,-623 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3875@0" ObjectIDZND0="3808@x" ObjectIDZND1="g_33d1c10@0" Pin0InfoVect0LinkObjId="SW-24094_0" Pin0InfoVect1LinkObjId="g_33d1c10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_353c270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4280,-758 4407,-758 4407,-623 4499,-623 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_341dce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4648,-951 4626,-951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3790@1" ObjectIDZND0="3792@1" Pin0InfoVect0LinkObjId="SW-23903_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23900_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4648,-951 4626,-951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_341eac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4868,-1143 4888,-1143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4868,-1143 4888,-1143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_341ecb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4868,-1099 4889,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4868,-1099 4889,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34a8990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4687,-692 4668,-692 4668,-731 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3844@0" ObjectIDZND0="3843@0" Pin0InfoVect0LinkObjId="SW-24469_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4687,-692 4668,-692 4668,-731 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34a8b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4668,-758 4668,-798 4687,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3843@1" ObjectIDZND0="3845@0" Pin0InfoVect0LinkObjId="SW-24471_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24469_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4668,-758 4668,-798 4687,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34aa760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3701,-1123 3721,-1123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_34aa950@0" ObjectIDZND0="3782@x" ObjectIDZND1="3783@x" ObjectIDZND2="g_3545da0@0" Pin0InfoVect0LinkObjId="SW-23882_0" Pin0InfoVect1LinkObjId="SW-23883_0" Pin0InfoVect2LinkObjId="g_3545da0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34aa950_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3701,-1123 3721,-1123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34c5be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-88 4322,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-88 4322,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34c5dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4358,-88 4450,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3435200@0" ObjectIDZND1="10455@x" ObjectIDZND2="g_3538700@0" Pin0InfoVect0LinkObjId="g_3435200_0" Pin0InfoVect1LinkObjId="SW-24997_0" Pin0InfoVect2LinkObjId="g_3538700_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4358,-88 4450,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34c5fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-88 4019,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_354e110@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_354e110_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-88 4019,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34c6210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4244,-88 4132,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4244,-88 4132,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34c6440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-88 4055,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-88 4055,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34d1d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4575,-970 4575,-951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="g_34870b0@0" ObjectIDZND0="3792@x" ObjectIDZND1="3874@x" Pin0InfoVect0LinkObjId="SW-23903_0" Pin0InfoVect1LinkObjId="g_353bb50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34870b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4575,-970 4575,-951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34d1f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4575,-951 4590,-951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" ObjectIDND0="g_34870b0@0" ObjectIDND1="3874@x" ObjectIDZND0="3792@0" Pin0InfoVect0LinkObjId="SW-23903_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_34870b0_0" Pin1InfoVect1LinkObjId="g_353bb50_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4575,-951 4590,-951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34d2140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-377 4621,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_34b7200@1" ObjectIDZND0="10455@1" Pin0InfoVect0LinkObjId="SW-24997_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34b7200_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-377 4621,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34d26b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3676,-88 3548,-88 3548,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_354d3a0@0" ObjectIDZND0="g_34ba320@0" ObjectIDZND1="10452@x" Pin0InfoVect0LinkObjId="g_34ba320_0" Pin0InfoVect1LinkObjId="SW-24978_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_354d3a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3676,-88 3548,-88 3548,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34d28a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3548,-364 3548,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="10452@1" ObjectIDZND0="g_33db880@1" Pin0InfoVect0LinkObjId="g_33db880_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24978_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3548,-364 3548,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3596620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4869,-231 4869,-250 4933,-250 4933,-229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4869,-231 4869,-250 4933,-250 4933,-229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3596890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4933,-212 4933,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4933,-212 4933,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3596af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4933,-141 4933,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4933,-141 4933,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35a71d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4587,-299 4587,-319 4621,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3538700@0" ObjectIDZND0="10455@x" ObjectIDZND1="g_35a5d10@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-24997_0" Pin0InfoVect1LinkObjId="g_35a5d10_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3538700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4587,-299 4587,-319 4621,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35a7430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-319 4621,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3538700@0" ObjectIDND1="g_35a5d10@0" ObjectIDND2="0@x" ObjectIDZND0="10455@0" Pin0InfoVect0LinkObjId="SW-24997_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3538700_0" Pin1InfoVect1LinkObjId="g_35a5d10_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-319 4621,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35a7690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4491,-74 4491,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_35a5d10@0" ObjectIDZND0="10455@x" ObjectIDZND1="g_3538700@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-24997_0" Pin0InfoVect1LinkObjId="g_3538700_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35a5d10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4491,-74 4491,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35a78f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-319 4621,-88 4491,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="10455@x" ObjectIDND1="g_3538700@0" ObjectIDZND0="g_35a5d10@0" ObjectIDZND1="0@x" ObjectIDZND2="g_3435200@0" Pin0InfoVect0LinkObjId="g_35a5d10_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_3435200_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24997_0" Pin1InfoVect1LinkObjId="g_3538700_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-319 4621,-88 4491,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35a7b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4491,-88 4450,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="10455@x" ObjectIDND1="g_3538700@0" ObjectIDND2="g_35a5d10@0" ObjectIDZND0="0@x" ObjectIDZND1="g_3435200@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_3435200_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-24997_0" Pin1InfoVect1LinkObjId="g_3538700_0" Pin1InfoVect2LinkObjId="g_35a5d10_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4491,-88 4450,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35a7db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5000,-261 5000,-252 5016,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_359f520@0" ObjectIDZND2="3862@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_359f520_0" Pin0InfoVect2LinkObjId="SW-24890_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5000,-261 5000,-252 5016,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35a8010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5016,-252 5016,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_359f520@0" ObjectIDND2="3862@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_359f520_0" Pin1InfoVect2LinkObjId="SW-24890_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5016,-252 5016,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35ab7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4850,-978 4839,-978 4839,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_354c630@0" ObjectIDZND0="g_35aaf20@0" ObjectIDZND1="3776@x" Pin0InfoVect0LinkObjId="g_35aaf20_0" Pin0InfoVect1LinkObjId="SW-23721_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_354c630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4850,-978 4839,-978 4839,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35aba00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4839,-1002 4860,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_354c630@0" ObjectIDND1="3776@x" ObjectIDZND0="g_35aaf20@0" Pin0InfoVect0LinkObjId="g_35aaf20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_354c630_0" Pin1InfoVect1LinkObjId="SW-23721_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4839,-1002 4860,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35abc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4891,-1002 4923,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_35aaf20@1" ObjectIDZND0="g_35a8760@0" Pin0InfoVect0LinkObjId="g_35a8760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35aaf20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4891,-1002 4923,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3571730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4418,-448 4418,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_343bc10@0" ObjectIDND1="g_3475ce0@0" ObjectIDZND0="3779@0" Pin0InfoVect0LinkObjId="SW-23737_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_343bc10_0" Pin1InfoVect1LinkObjId="g_3475ce0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4418,-448 4418,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3571990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4418,-522 4418,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3779@1" ObjectIDZND0="3768@0" Pin0InfoVect0LinkObjId="g_367c7e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23737_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4418,-522 4418,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3576b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4840,-1051 4822,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3869@1" ObjectIDZND0="3870@1" Pin0InfoVect0LinkObjId="SW-24956_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24954_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4840,-1051 4822,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3576d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4786,-1051 4743,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3870@0" ObjectIDZND0="3765@0" Pin0InfoVect0LinkObjId="g_344c5b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24956_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4786,-1051 4743,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3579520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4839,-1002 4821,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_35aaf20@0" ObjectIDND1="g_354c630@0" ObjectIDZND0="3776@1" Pin0InfoVect0LinkObjId="SW-23721_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_35aaf20_0" Pin1InfoVect1LinkObjId="g_354c630_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4839,-1002 4821,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3579780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4785,-1002 4743,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3776@0" ObjectIDZND0="3765@0" Pin0InfoVect0LinkObjId="g_344c5b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23721_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4785,-1002 4743,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_357bf10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4839,-919 4823,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3846@1" ObjectIDZND0="3847@1" Pin0InfoVect0LinkObjId="SW-24488_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24486_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4839,-919 4823,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_357c170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4787,-919 4743,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3847@0" ObjectIDZND0="3765@0" Pin0InfoVect0LinkObjId="g_344c5b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24488_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4787,-919 4743,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_357c3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-866 4866,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3851@0" ObjectIDZND0="3849@0" Pin0InfoVect0LinkObjId="SW-24579_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24582_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-866 4866,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_357eb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4839,-866 4823,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3849@1" ObjectIDZND0="3850@1" Pin0InfoVect0LinkObjId="SW-24581_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24579_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4839,-866 4823,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_357edc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4787,-866 4743,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3850@0" ObjectIDZND0="3765@0" Pin0InfoVect0LinkObjId="g_344c5b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24581_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4787,-866 4743,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3581550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4839,-787 4823,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3852@1" ObjectIDZND0="3853@1" Pin0InfoVect0LinkObjId="SW-24615_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24613_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4839,-787 4823,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35817b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4787,-787 4743,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3853@0" ObjectIDZND0="3765@0" Pin0InfoVect0LinkObjId="g_344c5b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24615_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4787,-787 4743,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3583f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4837,-697 4821,-697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3855@1" ObjectIDZND0="3856@1" Pin0InfoVect0LinkObjId="SW-24649_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24647_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4837,-697 4821,-697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35841a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4785,-697 4743,-697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3856@0" ObjectIDZND0="3766@0" Pin0InfoVect0LinkObjId="g_342f910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24649_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4785,-697 4743,-697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3586930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4785,-645 4743,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3777@0" ObjectIDZND0="3766@0" Pin0InfoVect0LinkObjId="g_342f910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23724_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4785,-645 4743,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3589060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4841,-439 4821,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4841,-439 4821,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35892c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4785,-439 4743,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="3766@0" Pin0InfoVect0LinkObjId="g_342f910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4785,-439 4743,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_358b9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4839,-482 4822,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4839,-482 4822,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_358bc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4786,-482 4743,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="3766@0" Pin0InfoVect0LinkObjId="g_342f910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4786,-482 4743,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_358e380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4839,-526 4822,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4839,-526 4822,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_358e5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4786,-526 4743,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="3766@0" Pin0InfoVect0LinkObjId="g_342f910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4786,-526 4743,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3590d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4839,-577 4824,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3866@1" ObjectIDZND0="3867@1" Pin0InfoVect0LinkObjId="SW-24931_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24929_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4839,-577 4824,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3590fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4788,-577 4743,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3867@0" ObjectIDZND0="3766@0" Pin0InfoVect0LinkObjId="g_342f910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24931_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4788,-577 4743,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35289a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5083,-697 5004,-697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="48926@1" ObjectIDZND0="g_3451010@0" ObjectIDZND1="g_3549de0@0" ObjectIDZND2="g_3529ca0@0" Pin0InfoVect0LinkObjId="g_3451010_0" Pin0InfoVect1LinkObjId="g_3549de0_0" Pin0InfoVect2LinkObjId="g_3529ca0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5083,-697 5004,-697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3528c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5080,-787 5006,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="18066@1" ObjectIDZND0="g_3558080@0" ObjectIDZND1="g_3549070@0" ObjectIDZND2="g_352ab80@0" Pin0InfoVect0LinkObjId="g_3558080_0" Pin0InfoVect1LinkObjId="g_3549070_0" Pin0InfoVect2LinkObjId="g_352ab80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5080,-787 5006,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3528e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5004,-697 4993,-697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_3451010@0" ObjectIDND1="48926@1" ObjectIDZND0="g_3549de0@0" ObjectIDZND1="g_3529ca0@0" Pin0InfoVect0LinkObjId="g_3549de0_0" Pin0InfoVect1LinkObjId="g_3529ca0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3451010_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5004,-697 4993,-697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35290c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4993,-697 4993,-717 5006,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3451010@0" ObjectIDND1="48926@1" ObjectIDND2="g_3529ca0@0" ObjectIDZND0="g_3549de0@0" Pin0InfoVect0LinkObjId="g_3549de0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3451010_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="g_3529ca0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4993,-697 4993,-717 5006,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3529320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5006,-787 4995,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_3558080@0" ObjectIDND1="18066@1" ObjectIDZND0="g_3549070@0" ObjectIDZND1="g_352ab80@0" Pin0InfoVect0LinkObjId="g_3549070_0" Pin0InfoVect1LinkObjId="g_352ab80_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3558080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5006,-787 4995,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3529580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4995,-787 4995,-807 5008,-807 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3558080@0" ObjectIDND1="18066@1" ObjectIDND2="g_352ab80@0" ObjectIDZND0="g_3549070@0" Pin0InfoVect0LinkObjId="g_3549070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3558080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="g_352ab80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4995,-787 4995,-807 5008,-807 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35297e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5010,-1071 4997,-1071 4997,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="g_3547590@0" ObjectIDZND0="12147@x" ObjectIDZND1="g_352c940@0" Pin0InfoVect0LinkObjId="EC-CX_HS.CX_HS_373Ld_0" Pin0InfoVect1LinkObjId="g_352c940_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3547590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5010,-1071 4997,-1071 4997,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3529a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4997,-1051 5082,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="g_3547590@0" ObjectIDND1="g_352c940@0" ObjectIDZND0="12147@0" Pin0InfoVect0LinkObjId="EC-CX_HS.CX_HS_373Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3547590_0" Pin1InfoVect1LinkObjId="g_352c940_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4997,-1051 5082,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352a6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4914,-697 4925,-697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3857@1" ObjectIDZND0="g_3529ca0@1" Pin0InfoVect0LinkObjId="g_3529ca0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24650_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4914,-697 4925,-697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352a920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4978,-697 4993,-697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="g_3529ca0@0" ObjectIDZND0="g_3549de0@0" ObjectIDZND1="g_3451010@0" ObjectIDZND2="48926@1" Pin0InfoVect0LinkObjId="g_3549de0_0" Pin0InfoVect1LinkObjId="g_3451010_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3529ca0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4978,-697 4993,-697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352b5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4916,-787 4931,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3854@1" ObjectIDZND0="g_352ab80@1" Pin0InfoVect0LinkObjId="g_352ab80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24616_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4916,-787 4931,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352b800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4984,-787 4996,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="g_352ab80@0" ObjectIDZND0="g_3549070@0" ObjectIDZND1="g_3558080@0" ObjectIDZND2="18066@1" Pin0InfoVect0LinkObjId="g_3549070_0" Pin0InfoVect1LinkObjId="g_3558080_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_352ab80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4984,-787 4996,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352c480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4916,-919 4930,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3848@1" ObjectIDZND0="g_352ba60@1" Pin0InfoVect0LinkObjId="g_352ba60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24489_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4916,-919 4930,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352c6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4983,-919 4999,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_352ba60@0" ObjectIDZND0="g_35b73e0@0" Pin0InfoVect0LinkObjId="g_35b73e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_352ba60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4983,-919 4999,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352d360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4918,-1051 4934,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3871@1" ObjectIDZND0="g_352c940@1" Pin0InfoVect0LinkObjId="g_352c940_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24957_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4918,-1051 4934,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352d5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4987,-1051 4997,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="g_352c940@0" ObjectIDZND0="12147@x" ObjectIDZND1="g_3547590@0" Pin0InfoVect0LinkObjId="EC-CX_HS.CX_HS_373Ld_0" Pin0InfoVect1LinkObjId="g_3547590_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_352c940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4987,-1051 4997,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352d820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5006,-866 4995,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_349e190@0" ObjectIDND1="18075@1" ObjectIDZND0="g_3548300@0" ObjectIDZND1="g_3527340@0" Pin0InfoVect0LinkObjId="g_3548300_0" Pin0InfoVect1LinkObjId="g_3527340_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_349e190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5006,-866 4995,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352da80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4995,-866 4995,-885 5008,-885 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_349e190@0" ObjectIDND1="18075@1" ObjectIDND2="g_3527340@0" ObjectIDZND0="g_3548300@0" Pin0InfoVect0LinkObjId="g_3548300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_349e190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="g_3527340_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4995,-866 4995,-885 5008,-885 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352dce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4916,-866 4931,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3851@1" ObjectIDZND0="g_3527340@1" Pin0InfoVect0LinkObjId="g_3527340_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24582_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4916,-866 4931,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352df40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4984,-866 4996,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="g_3527340@0" ObjectIDZND0="g_3548300@0" ObjectIDZND1="g_349e190@0" ObjectIDZND2="18075@1" Pin0InfoVect0LinkObjId="g_3548300_0" Pin0InfoVect1LinkObjId="g_349e190_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3527340_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4984,-866 4996,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352e900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4916,-577 4942,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3868@1" ObjectIDZND0="g_3527970@1" Pin0InfoVect0LinkObjId="g_3527970_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24932_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4916,-577 4942,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352eb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4997,-577 5014,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_3527970@0" ObjectIDZND0="g_354ab50@0" ObjectIDZND1="12151@x" Pin0InfoVect0LinkObjId="g_354ab50_0" Pin0InfoVect1LinkObjId="EC-CX_HS.CX_HS_382Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3527970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4997,-577 5014,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_352edc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5014,-577 5059,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="g_3527970@0" ObjectIDND1="g_354ab50@0" ObjectIDZND0="12151@0" Pin0InfoVect0LinkObjId="EC-CX_HS.CX_HS_382Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3527970_0" Pin1InfoVect1LinkObjId="g_354ab50_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5014,-577 5059,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_352fa40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5030,-439 4996,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_352f020@0" Pin0InfoVect0LinkObjId="g_352f020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5030,-439 4996,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_352fca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4943,-439 4918,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_352f020@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_352f020_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4943,-439 4918,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3530920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5041,-482 4996,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_352ff00@0" Pin0InfoVect0LinkObjId="g_352ff00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5041,-482 4996,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3530b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4943,-482 4916,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_352ff00@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_352ff00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4943,-482 4916,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3531800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5025,-526 4994,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_3530de0@0" Pin0InfoVect0LinkObjId="g_3530de0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5025,-526 4994,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3531a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4941,-526 4916,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3530de0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3530de0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4941,-526 4916,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35326e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4925,-1099 4951,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3531cc0@1" Pin0InfoVect0LinkObjId="g_3531cc0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4925,-1099 4951,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3532940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5004,-1099 5030,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_3531cc0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3531cc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5004,-1099 5030,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35335c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4924,-1143 4946,-1143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3532ba0@1" Pin0InfoVect0LinkObjId="g_3532ba0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4924,-1143 4946,-1143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3533820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4999,-1143 5030,-1143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_3532ba0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3532ba0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4999,-1143 5030,-1143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3533a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5031,-276 5031,-260 5016,-260 5016,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_34956a0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_359f520@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_359f520_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34956a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5031,-276 5031,-260 5016,-260 5016,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_353bb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3609,-694 3599,-694 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" ObjectIDND0="g_353a070@0" ObjectIDZND0="3874@x" ObjectIDZND1="3796@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="SW-23917_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_353a070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3609,-694 3599,-694 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_353bdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3599,-718 3599,-694 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3874@2" ObjectIDZND0="g_353a070@0" ObjectIDZND1="3796@x" Pin0InfoVect0LinkObjId="g_353a070_0" Pin0InfoVect1LinkObjId="SW-23917_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_353bb50_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3599,-718 3599,-694 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_353c010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3599,-694 3599,-681 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="3874@x" ObjectIDND1="g_353a070@0" ObjectIDZND0="3796@1" Pin0InfoVect0LinkObjId="SW-23917_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_353bb50_0" Pin1InfoVect1LinkObjId="g_353a070_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3599,-694 3599,-681 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_353c270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4231,-695 4224,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" ObjectIDND0="g_353ade0@0" ObjectIDZND0="3875@x" ObjectIDZND1="3812@x" Pin0InfoVect0LinkObjId="g_35bd3b0_0" Pin0InfoVect1LinkObjId="SW-24107_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_353ade0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4231,-695 4224,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_353c4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4224,-718 4224,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3875@2" ObjectIDZND0="g_353ade0@0" ObjectIDZND1="3812@x" Pin0InfoVect0LinkObjId="g_353ade0_0" Pin0InfoVect1LinkObjId="SW-24107_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_353c270_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4224,-718 4224,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_353c730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4224,-695 4224,-681 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="3875@x" ObjectIDND1="g_353ade0@0" ObjectIDZND0="3812@1" Pin0InfoVect0LinkObjId="SW-24107_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_353c270_0" Pin1InfoVect1LinkObjId="g_353ade0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4224,-695 4224,-681 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_353ff50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-300 3848,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_353c990@0" ObjectIDZND0="3829@x" ObjectIDZND1="3830@x" ObjectIDZND2="11722@x" Pin0InfoVect0LinkObjId="SW-24355_0" Pin0InfoVect1LinkObjId="SW-24356_0" Pin0InfoVect2LinkObjId="CB-CX_HS.CX_HS_1C_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_353c990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-300 3848,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35401b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3848,-313 3848,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="capacitor" ObjectIDND0="3829@x" ObjectIDND1="3830@x" ObjectIDZND0="g_353c990@0" ObjectIDZND1="11722@x" Pin0InfoVect0LinkObjId="g_353c990_0" Pin0InfoVect1LinkObjId="CB-CX_HS.CX_HS_1C_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24355_0" Pin1InfoVect1LinkObjId="SW-24356_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3848,-313 3848,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3540410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3848,-300 3848,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="capacitor" ObjectIDND0="3829@x" ObjectIDND1="3830@x" ObjectIDND2="g_353c990@0" ObjectIDZND0="11722@0" Pin0InfoVect0LinkObjId="CB-CX_HS.CX_HS_1C_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-24355_0" Pin1InfoVect1LinkObjId="SW-24356_0" Pin1InfoVect2LinkObjId="g_353c990_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3848,-300 3848,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3540670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3961,-300 3952,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_353d700@0" ObjectIDZND0="3833@x" ObjectIDZND1="3834@x" ObjectIDZND2="11723@x" Pin0InfoVect0LinkObjId="SW-24385_0" Pin0InfoVect1LinkObjId="SW-24386_0" Pin0InfoVect2LinkObjId="CB-CX_HS.CX_HS_2C_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_353d700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3961,-300 3952,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35408d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3952,-313 3952,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="capacitor" ObjectIDND0="3833@x" ObjectIDND1="3834@x" ObjectIDZND0="g_353d700@0" ObjectIDZND1="11723@x" Pin0InfoVect0LinkObjId="g_353d700_0" Pin0InfoVect1LinkObjId="CB-CX_HS.CX_HS_2C_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24385_0" Pin1InfoVect1LinkObjId="SW-24386_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3952,-313 3952,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3540b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3952,-300 3952,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="capacitor" ObjectIDND0="3833@x" ObjectIDND1="3834@x" ObjectIDND2="g_353d700@0" ObjectIDZND0="11723@0" Pin0InfoVect0LinkObjId="CB-CX_HS.CX_HS_2C_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-24385_0" Pin1InfoVect1LinkObjId="SW-24386_0" Pin1InfoVect2LinkObjId="g_353d700_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3952,-300 3952,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3540d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4169,-301 4161,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_353e470@0" ObjectIDZND0="3837@x" ObjectIDZND1="3838@x" ObjectIDZND2="11724@x" Pin0InfoVect0LinkObjId="SW-24415_0" Pin0InfoVect1LinkObjId="SW-24416_0" Pin0InfoVect2LinkObjId="CB-CX_HS.CX_HS_3C_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_353e470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4169,-301 4161,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3540ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-313 4161,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="capacitor" ObjectIDND0="3837@x" ObjectIDND1="3838@x" ObjectIDZND0="g_353e470@0" ObjectIDZND1="11724@x" Pin0InfoVect0LinkObjId="g_353e470_0" Pin0InfoVect1LinkObjId="CB-CX_HS.CX_HS_3C_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24415_0" Pin1InfoVect1LinkObjId="SW-24416_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-313 4161,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3541250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-301 4161,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="capacitor" ObjectIDND0="3837@x" ObjectIDND1="3838@x" ObjectIDND2="g_353e470@0" ObjectIDZND0="11724@0" Pin0InfoVect0LinkObjId="CB-CX_HS.CX_HS_3C_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-24415_0" Pin1InfoVect1LinkObjId="SW-24416_0" Pin1InfoVect2LinkObjId="g_353e470_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-301 4161,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35414b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4286,-302 4277,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_353f1e0@0" ObjectIDZND0="3841@x" ObjectIDZND1="3842@x" ObjectIDZND2="11725@x" Pin0InfoVect0LinkObjId="SW-24444_0" Pin0InfoVect1LinkObjId="SW-24445_0" Pin0InfoVect2LinkObjId="CB-CX_HS.CX_HS_4C_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_353f1e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4286,-302 4277,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3541710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4277,-313 4277,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="capacitor" ObjectIDND0="3841@x" ObjectIDND1="3842@x" ObjectIDZND0="g_353f1e0@0" ObjectIDZND1="11725@x" Pin0InfoVect0LinkObjId="g_353f1e0_0" Pin0InfoVect1LinkObjId="CB-CX_HS.CX_HS_4C_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24444_0" Pin1InfoVect1LinkObjId="SW-24445_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4277,-313 4277,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3541970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4277,-302 4277,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="capacitor" ObjectIDND0="3841@x" ObjectIDND1="3842@x" ObjectIDND2="g_353f1e0@0" ObjectIDZND0="11725@0" Pin0InfoVect0LinkObjId="CB-CX_HS.CX_HS_4C_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-24444_0" Pin1InfoVect1LinkObjId="SW-24445_0" Pin1InfoVect2LinkObjId="g_353f1e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4277,-302 4277,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35437c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4492,-284 4492,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="12152@0" ObjectIDZND0="3826@x" ObjectIDZND1="g_3539300@0" Pin0InfoVect0LinkObjId="SW-24323_0" Pin0InfoVect1LinkObjId="g_3539300_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_HS.084Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4492,-284 4492,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3543a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4492,-330 4492,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="3826@0" ObjectIDZND0="12152@x" ObjectIDZND1="g_3539300@0" Pin0InfoVect0LinkObjId="EC-CX_HS.084Ld_0" Pin0InfoVect1LinkObjId="g_3539300_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24323_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4492,-330 4492,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3543c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4492,-320 4459,-320 4459,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="3826@x" ObjectIDND1="12152@x" ObjectIDZND0="g_3539300@0" Pin0InfoVect0LinkObjId="g_3539300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24323_0" Pin1InfoVect1LinkObjId="EC-CX_HS.084Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4492,-320 4459,-320 4459,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35445c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3780,-318 3780,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_33c62c0@0" ObjectIDND1="3823@x" ObjectIDZND0="12153@0" Pin0InfoVect0LinkObjId="EC-CX_HS.075Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33c62c0_0" Pin1InfoVect1LinkObjId="SW-24286_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3780,-318 3780,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3544820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3746,-302 3746,-318 3780,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_33c62c0@0" ObjectIDZND0="12153@x" ObjectIDZND1="3823@x" Pin0InfoVect0LinkObjId="EC-CX_HS.075Ld_0" Pin0InfoVect1LinkObjId="SW-24286_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33c62c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3746,-302 3746,-318 3780,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3544a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3780,-318 3780,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_33c62c0@0" ObjectIDND1="12153@x" ObjectIDZND0="3823@0" Pin0InfoVect0LinkObjId="SW-24286_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33c62c0_0" Pin1InfoVect1LinkObjId="EC-CX_HS.075Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3780,-318 3780,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_354f3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4853,-612 4837,-612 4837,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_354b8c0@0" ObjectIDZND0="g_33effa0@0" ObjectIDZND1="3777@x" Pin0InfoVect0LinkObjId="g_33effa0_0" Pin0InfoVect1LinkObjId="SW-23724_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_354b8c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4853,-612 4837,-612 4837,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_354f620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4837,-645 4821,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_354b8c0@0" ObjectIDND1="g_33effa0@0" ObjectIDZND0="3777@1" Pin0InfoVect0LinkObjId="SW-23724_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_354b8c0_0" Pin1InfoVect1LinkObjId="g_33effa0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4837,-645 4821,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_354faa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3600,-880 3600,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3787@1" ObjectIDZND0="3763@0" Pin0InfoVect0LinkObjId="g_3550d60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23887_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3600,-880 3600,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_354ff20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-949 4107,-925 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3801@0" ObjectIDZND0="3764@0" Pin0InfoVect0LinkObjId="g_3550180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24076_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-949 4107,-925 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3550180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-898 4225,-925 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3803@1" ObjectIDZND0="3764@0" Pin0InfoVect0LinkObjId="g_354ff20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24078_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4225,-898 4225,-925 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35503e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3927,-904 3927,-925 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3816@1" ObjectIDZND0="3764@0" Pin0InfoVect0LinkObjId="g_354ff20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24182_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3927,-904 3927,-925 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3550640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-960 3951,-925 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="3773@x" ObjectIDND1="3775@x" ObjectIDZND0="3764@0" Pin0InfoVect0LinkObjId="g_354ff20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-23716_0" Pin1InfoVect1LinkObjId="SW-23718_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-960 3951,-925 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35508a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-983 3951,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="3773@0" ObjectIDZND0="3764@0" ObjectIDZND1="3775@x" Pin0InfoVect0LinkObjId="g_354ff20_0" Pin0InfoVect1LinkObjId="SW-23718_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23716_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-983 3951,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3550b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-960 3969,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="3773@x" ObjectIDND1="3764@0" ObjectIDZND0="3775@0" Pin0InfoVect0LinkObjId="SW-23718_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-23716_0" Pin1InfoVect1LinkObjId="g_354ff20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-960 3969,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3550d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-942 3721,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3785@0" ObjectIDZND0="3763@0" Pin0InfoVect0LinkObjId="g_354faa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23885_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-942 3721,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3550fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3560,-953 3560,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="3770@x" ObjectIDND1="3772@x" ObjectIDZND0="3763@0" Pin0InfoVect0LinkObjId="g_354faa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-23711_0" Pin1InfoVect1LinkObjId="SW-23713_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3560,-953 3560,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3551220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3560,-976 3560,-953 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="3770@0" ObjectIDZND0="3763@0" ObjectIDZND1="3772@x" Pin0InfoVect0LinkObjId="g_354faa0_0" Pin0InfoVect1LinkObjId="SW-23713_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23711_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3560,-976 3560,-953 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3551480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3560,-953 3577,-953 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="3770@x" ObjectIDND1="3763@0" ObjectIDZND0="3772@0" Pin0InfoVect0LinkObjId="SW-23713_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-23711_0" Pin1InfoVect1LinkObjId="g_354faa0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3560,-953 3577,-953 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35516e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3806,-902 3806,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3814@1" ObjectIDZND0="3763@0" Pin0InfoVect0LinkObjId="g_354faa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24180_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3806,-902 3806,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3551940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3806,-866 3806,-851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="3814@0" ObjectIDZND0="3813@x" ObjectIDZND1="3815@x" Pin0InfoVect0LinkObjId="SW-24174_0" Pin0InfoVect1LinkObjId="SW-24181_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3806,-866 3806,-851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3551ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3806,-836 3806,-851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="3815@1" ObjectIDZND0="3814@x" ObjectIDZND1="3813@x" Pin0InfoVect0LinkObjId="SW-24180_0" Pin0InfoVect1LinkObjId="SW-24174_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24181_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3806,-836 3806,-851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3551e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3879,-851 3927,-851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="3813@0" ObjectIDZND0="3816@x" ObjectIDZND1="3817@x" Pin0InfoVect0LinkObjId="SW-24182_0" Pin0InfoVect1LinkObjId="SW-24183_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24174_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3879,-851 3927,-851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3552060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3927,-868 3927,-851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="3816@0" ObjectIDZND0="3813@x" ObjectIDZND1="3817@x" Pin0InfoVect0LinkObjId="SW-24174_0" Pin0InfoVect1LinkObjId="SW-24183_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24182_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3927,-868 3927,-851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35522c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3927,-851 3927,-838 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="3816@x" ObjectIDND1="3813@x" ObjectIDZND0="3817@1" Pin0InfoVect0LinkObjId="SW-24183_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24182_0" Pin1InfoVect1LinkObjId="SW-24174_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3927,-851 3927,-838 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34d6290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3517,-297 3517,-312 3548,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_34ba320@0" ObjectIDZND0="0@x" ObjectIDZND1="g_354d3a0@0" ObjectIDZND2="10452@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_354d3a0_0" Pin0InfoVect2LinkObjId="SW-24978_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34ba320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3517,-297 3517,-312 3548,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34d6480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3548,-312 3548,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_34ba320@0" ObjectIDND1="0@x" ObjectIDND2="g_354d3a0@0" ObjectIDZND0="10452@0" Pin0InfoVect0LinkObjId="SW-24978_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_34ba320_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_354d3a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3548,-312 3548,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34d90d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-539 4621,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3768@0" ObjectIDZND0="10454@1" Pin0InfoVect0LinkObjId="SW-24996_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_367c7e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-539 4621,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34db520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-495 4621,-478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="10454@0" ObjectIDZND0="3873@1" Pin0InfoVect0LinkObjId="SW-24994_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24996_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-495 4621,-478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34db780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-451 4621,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="3873@0" ObjectIDZND0="g_34b7200@0" Pin0InfoVect0LinkObjId="g_34b7200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24994_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-451 4621,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34e8800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3613,-314 3613,-123 4725,-123 4725,-75 5083,-75 5083,-356 5016,-356 5016,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_359f520@0" ObjectIDND1="3862@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_34956a0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_34956a0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_359f520_0" Pin1InfoVect1LinkObjId="SW-24890_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3613,-314 3613,-123 4725,-123 4725,-75 5083,-75 5083,-356 5016,-356 5016,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34e8a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3584,-299 3584,-314 3613,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_359f520@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_34956a0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_34956a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_359f520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3584,-299 3584,-314 3613,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34e8c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3613,-314 3613,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_359f520@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="3862@0" Pin0InfoVect0LinkObjId="SW-24890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_359f520_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3613,-314 3613,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35b84b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3654,-759 3684,-759 3684,-688 4479,-688 4479,-951 4575,-951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3874@0" ObjectIDZND0="3792@x" ObjectIDZND1="g_34870b0@0" Pin0InfoVect0LinkObjId="SW-23903_0" Pin0InfoVect1LinkObjId="g_34870b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_353bb50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3654,-759 3684,-759 3684,-688 4479,-688 4479,-951 4575,-951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35bac20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3632,-763 3632,-712 3785,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="3874@x" ObjectIDZND0="3793@0" Pin0InfoVect0LinkObjId="SW-23910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_353bb50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3632,-763 3632,-712 3785,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35bd3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3944,-712 4262,-712 4262,-758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="3809@1" ObjectIDZND0="3875@x" Pin0InfoVect0LinkObjId="g_353c270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24101_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3944,-712 4262,-712 4262,-758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35bd610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-732 3844,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_341be80@0" ObjectIDZND0="3793@x" ObjectIDZND1="g_33ee530@0" ObjectIDZND2="3809@x" Pin0InfoVect0LinkObjId="SW-23910_0" Pin0InfoVect1LinkObjId="g_33ee530_0" Pin0InfoVect2LinkObjId="SW-24101_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_341be80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-732 3844,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35be100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3821,-712 3844,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="3793@1" ObjectIDZND0="g_341be80@0" ObjectIDZND1="g_33ee530@0" ObjectIDZND2="3809@x" Pin0InfoVect0LinkObjId="g_341be80_0" Pin0InfoVect1LinkObjId="g_33ee530_0" Pin0InfoVect2LinkObjId="SW-24101_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23910_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3821,-712 3844,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35be360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3879,-733 3879,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_33ee530@0" ObjectIDZND0="g_341be80@0" ObjectIDZND1="3793@x" ObjectIDZND2="3809@x" Pin0InfoVect0LinkObjId="g_341be80_0" Pin0InfoVect1LinkObjId="SW-23910_0" Pin0InfoVect2LinkObjId="SW-24101_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33ee530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3879,-733 3879,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35bee50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-712 3879,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_341be80@0" ObjectIDND1="3793@x" ObjectIDZND0="g_33ee530@0" ObjectIDZND1="3809@x" Pin0InfoVect0LinkObjId="g_33ee530_0" Pin0InfoVect1LinkObjId="SW-24101_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_341be80_0" Pin1InfoVect1LinkObjId="SW-23910_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-712 3879,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35bf0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3879,-712 3908,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_33ee530@0" ObjectIDND1="g_341be80@0" ObjectIDND2="3793@x" ObjectIDZND0="3809@0" Pin0InfoVect0LinkObjId="SW-24101_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_33ee530_0" Pin1InfoVect1LinkObjId="g_341be80_0" Pin1InfoVect2LinkObjId="SW-23910_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3879,-712 3908,-712 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="3767" cx="3548" cy="-539" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3767" cx="3613" cy="-539" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3767" cx="3696" cy="-539" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3767" cx="3599" cy="-539" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3767" cx="3780" cy="-539" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3767" cx="3952" cy="-539" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3768" cx="4084" cy="-539" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3767" cx="3970" cy="-539" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3768" cx="4069" cy="-539" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3768" cx="4224" cy="-539" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3768" cx="4492" cy="-539" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3768" cx="4565" cy="-539" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3768" cx="4277" cy="-539" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3768" cx="4161" cy="-539" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3767" cx="3848" cy="-539" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4869" cy="-122" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4797" cy="-122" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5016" cy="-122" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3765" cx="4743" cy="-1143" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3765" cx="4743" cy="-1099" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3765" cx="4743" cy="-951" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3765" cx="4743" cy="-798" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3766" cx="4743" cy="-692" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3766" cx="4743" cy="-623" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4933" cy="-122" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3768" cx="4418" cy="-539" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3765" cx="4743" cy="-1051" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3765" cx="4743" cy="-1002" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3765" cx="4743" cy="-919" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3765" cx="4743" cy="-866" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3765" cx="4743" cy="-787" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3766" cx="4743" cy="-697" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3766" cx="4743" cy="-645" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3766" cx="4743" cy="-439" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3766" cx="4743" cy="-482" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3766" cx="4743" cy="-526" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3766" cx="4743" cy="-577" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3763" cx="3600" cy="-922" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3764" cx="4107" cy="-925" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3764" cx="4225" cy="-925" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3764" cx="3927" cy="-925" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3764" cx="3951" cy="-925" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3763" cx="3721" cy="-922" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3763" cx="3560" cy="-922" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3763" cx="3806" cy="-922" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3768" cx="4621" cy="-539" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-24" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3311.000000 -1094.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24" ObjectName="DYN-CX_HS"/>
     <cge:Meas_Ref ObjectId="24"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3432300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4842.000000 -1075.000000) translate(0,12)">373</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_341df40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5055.000000 -1047.000000) translate(0,15)">光明化工线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_341eea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4964.000000 -1011.000000) translate(0,15)">35kVI段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_341f780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4837.000000 -948.000000) translate(0,12)">374</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_341fa40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4878.000000 -949.000000) translate(0,12)">3746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_341fc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4970.000000 -950.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_341fec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5087.500000 -890.000000) translate(0,15)">洪清线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3420720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5092.500000 -808.000000) translate(0,15)">洪土线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3420c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5083.000000 -718.000000) translate(0,15)">金洪T线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34211e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5056.500000 -599.000000) translate(0,15)">江达磷化线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34a7090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4986.000000 -651.000000) translate(0,15)">35kVII段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34a7320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5042.000000 -532.000000) translate(0,15)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34a79f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5042.000000 -486.000000) translate(0,15)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34a7e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5042.000000 -441.000000) translate(0,15)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34a8090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5045.000000 -1152.000000) translate(0,15)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34a82d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5045.000000 -1108.000000) translate(0,15)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34a8510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4839.000000 -892.000000) translate(0,12)">375</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34a8750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4881.000000 -894.000000) translate(0,12)">3756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34a8d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4839.000000 -813.000000) translate(0,12)">376</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34a9090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4880.000000 -815.000000) translate(0,12)">3766</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34a92d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4837.000000 -723.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34a9510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4883.000000 -723.000000) translate(0,12)">3816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34a9750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4839.000000 -603.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34a9990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4880.000000 -603.000000) translate(0,12)">3826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34a9bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4701.000000 -1183.000000) translate(0,15)">35kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34a9e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4700.000000 -412.000000) translate(0,15)">35kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34aa060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3439.000000 -534.000000) translate(0,15)">10kVIM段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34aa520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4624.000000 -563.000000) translate(0,15)">10kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34ad240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4645.000000 -977.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34ad730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4687.000000 -979.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34ad970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4591.000000 -979.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34adbb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4639.000000 -649.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34addf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4680.000000 -651.000000) translate(0,12)">3022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34ae030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4585.000000 -651.000000) translate(0,12)">3026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34ae270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4678.000000 -753.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34ae4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4688.000000 -826.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34ae6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4688.000000 -720.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34ae930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3787.000000 -735.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34aeb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3919.000000 -734.000000) translate(0,12)">3020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34aedb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3608.000000 -627.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34aeff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3606.000000 -578.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34af230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3606.000000 -671.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34af470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4003.000000 -638.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34af6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3977.000000 -587.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34af8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4076.000000 -588.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34afb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4233.000000 -627.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34afd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4231.000000 -578.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34affb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4231.000000 -671.000000) translate(0,12)">0026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34b01f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3677.000000 -1188.000000) translate(0,15)">禄</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34b01f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3677.000000 -1188.000000) translate(0,33)">洪</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34b01f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3677.000000 -1188.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34b0730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4064.000000 -1203.000000) translate(0,15)">腰</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34b0730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4064.000000 -1203.000000) translate(0,33)">洪</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34b0730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4064.000000 -1203.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b0c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3730.000000 -1024.000000) translate(0,12)">171</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b0f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3728.000000 -968.000000) translate(0,12)">1711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b1150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3734.000000 -1008.000000) translate(0,12)">17117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b1390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3731.000000 -1065.000000) translate(0,12)">17160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b15d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3731.000000 -1098.000000) translate(0,12)">17167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b1810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3731.000000 -1081.000000) translate(0,12)">1716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b1a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3607.000000 -869.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b1c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3616.000000 -850.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b1ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3502.000000 -878.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b2110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3574.000000 -1057.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b7620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3580.000000 -976.000000) translate(0,12)">19010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b7860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3567.000000 -1001.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b7aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3854.000000 -875.000000) translate(0,12)">112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b7ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3808.000000 -891.000000) translate(0,12)">1121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b7f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3808.000000 -825.000000) translate(0,12)">11217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b8160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3934.000000 -893.000000) translate(0,12)">1122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b83a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3934.000000 -827.000000) translate(0,12)">11227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b85e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3958.000000 -1008.000000) translate(0,12)">1902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b8820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3967.000000 -986.000000) translate(0,12)">19020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b8a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3965.000000 -1064.000000) translate(0,12)">19027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b8ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4117.000000 -1034.000000) translate(0,12)">172</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b8ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4118.000000 -974.000000) translate(0,12)">1722</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b9120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4118.000000 -1090.000000) translate(0,12)">1726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b9360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4121.000000 -1018.000000) translate(0,12)">17227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b95a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4117.000000 -1075.000000) translate(0,12)">17260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b97e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4117.000000 -1106.000000) translate(0,12)">17267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b9a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4236.000000 -887.000000) translate(0,12)">1022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b9c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4243.000000 -870.000000) translate(0,12)">10227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b9ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4339.000000 -876.000000) translate(0,12)">1020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34ba0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3554.000000 -460.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bade0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3619.000000 -460.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34bb2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3558.000000 -357.000000) translate(0,15)">工</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34bb2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3558.000000 -357.000000) translate(0,33)">业</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34bb2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3558.000000 -357.000000) translate(0,51)">园</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34bb2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3558.000000 -357.000000) translate(0,69)">区</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34bb2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3558.000000 -357.000000) translate(0,87)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34bb2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3558.000000 -357.000000) translate(0,105)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34bb2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3558.000000 -357.000000) translate(0,123)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34bbd90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3623.000000 -322.000000) translate(0,15)">钛</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34bbd90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3623.000000 -322.000000) translate(0,33)">材</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34bbd90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3623.000000 -322.000000) translate(0,51)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34bbd90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3623.000000 -322.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34bbd90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3623.000000 -322.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bc600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3617.000000 -511.000000) translate(0,12)">0741</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bc870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3617.000000 -353.000000) translate(0,12)">0746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bcab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3707.000000 -510.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bccf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3789.000000 -461.000000) translate(0,12)">075</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bcf30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3787.000000 -354.000000) translate(0,12)">0756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34bd170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3661.000000 -291.000000) translate(0,15)">10kVI段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34bd170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3661.000000 -291.000000) translate(0,33)">母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34bd3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3734.000000 -245.000000) translate(0,15)">土官线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bd8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3857.000000 -461.000000) translate(0,12)">076</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bdb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3855.000000 -512.000000) translate(0,12)">0761</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bdd90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3855.000000 -354.000000) translate(0,12)">0766</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bdfd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3873.000000 -341.000000) translate(0,12)">07667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34be210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3961.000000 -461.000000) translate(0,12)">077</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34be450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3959.000000 -512.000000) translate(0,12)">0771</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34be690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3959.000000 -354.000000) translate(0,12)">0776</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34be8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3978.000000 -340.000000) translate(0,12)">07767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34beb10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4091.000000 -510.000000) translate(0,12)">0812</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bed50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4170.000000 -461.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bef90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4168.000000 -512.000000) translate(0,12)">0822</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bf1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4168.000000 -354.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bf410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4186.000000 -340.000000) translate(0,12)">08267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bf650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4286.000000 -461.000000) translate(0,12)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bf890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4284.000000 -512.000000) translate(0,12)">0832</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bfad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4284.000000 -354.000000) translate(0,12)">0836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bfd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4301.000000 -341.000000) translate(0,12)">08367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34bff50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4501.000000 -462.000000) translate(0,12)">084</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34c0190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4499.000000 -513.000000) translate(0,12)">0842</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34c03d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4499.000000 -355.000000) translate(0,12)">0846</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34c0610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4574.000000 -463.000000) translate(0,12)">085</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34c0850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4572.000000 -514.000000) translate(0,12)">0852</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34c0a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4631.000000 -463.000000) translate(0,12)">086</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34c0cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4379.000000 -319.000000) translate(0,15)">10kVII段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34c0cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4379.000000 -319.000000) translate(0,33)">母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34c0f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4440.000000 -243.000000) translate(0,15)">指挥营线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34c1a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4545.000000 -301.000000) translate(0,15)">钛</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34c1a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4545.000000 -301.000000) translate(0,33)">材</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34c1a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4545.000000 -301.000000) translate(0,51)">II</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34c1a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4545.000000 -301.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34c1a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4545.000000 -301.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34c1d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4633.000000 -326.000000) translate(0,15)">工</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34c1d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4633.000000 -326.000000) translate(0,33)">业</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34c1d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4633.000000 -326.000000) translate(0,51)">园</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34c1d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4633.000000 -326.000000) translate(0,69)">区</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34c1d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4633.000000 -326.000000) translate(0,87)">II</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34c1d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4633.000000 -326.000000) translate(0,105)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34c1d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4633.000000 -326.000000) translate(0,123)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34c1f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4779.000000 -102.000000) translate(0,15)">10kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34c2180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4923.000000 -102.000000) translate(0,15)">10kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="34" graphid="g_34c23b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4798.000000 -64.000000) translate(0,28)">10kV云钛1号配电室</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34c7530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3522.000000 -768.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34c7a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4254.000000 -799.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34d1b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4045.000000 -441.000000) translate(0,15)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34d1b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4045.000000 -441.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34d1b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4045.000000 -441.000000) translate(0,51)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34d1b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4045.000000 -441.000000) translate(0,69)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34d1b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4045.000000 -441.000000) translate(0,87)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34d2350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4572.000000 -356.000000) translate(0,12)">0856</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3597090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3033.000000 -1034.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3597090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3033.000000 -1034.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3597090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3033.000000 -1034.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3597090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3033.000000 -1034.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3597090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3033.000000 -1034.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3597090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3033.000000 -1034.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3597090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3033.000000 -1034.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3597730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3034.000000 -596.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3597730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3034.000000 -596.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3597730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3034.000000 -596.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3597730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3034.000000 -596.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3597730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3034.000000 -596.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3597730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3034.000000 -596.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3597730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3034.000000 -596.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3597730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3034.000000 -596.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3597730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3034.000000 -596.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3597730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3034.000000 -596.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3597730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3034.000000 -596.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3597730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3034.000000 -596.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3597730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3034.000000 -596.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3597730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3034.000000 -596.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3597730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3034.000000 -596.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3597730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3034.000000 -596.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3597730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3034.000000 -596.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3597730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3034.000000 -596.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_35981c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3176.500000 -1174.500000) translate(0,16)">洪山变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3598dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3501.000000 -355.000000) translate(0,12)">0736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3599250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4629.000000 -356.000000) translate(0,12)">0866</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_359eee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3490.000000 -1122.000000) translate(0,15)">110kVI段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35a54b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4504.000000 -29.000000) translate(0,15)">工业园区1号公用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a8270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4885.000000 -1073.000000) translate(0,12)">3736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35af6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3974.000000 -659.000000) translate(0,12)">Ia(A):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35af940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4583.000000 -754.000000) translate(0,12)">Ia(A):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3565800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3885.000000 -1129.000000) translate(0,15)">110kVII段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35659f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3787.000000 -512.000000) translate(0,12)">0751</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3565c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3548.000000 -511.000000) translate(0,12)">0731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3565e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4632.000000 -514.000000) translate(0,12)">0862</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3571bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4425.000000 -511.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3591230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4788.000000 -1077.000000) translate(0,12)">3731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3591860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4789.000000 -945.000000) translate(0,12)">3741</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3591aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4787.000000 -1028.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3521b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4789.000000 -892.000000) translate(0,12)">3751</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3521da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4789.000000 -813.000000) translate(0,12)">3761</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3521fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4787.000000 -723.000000) translate(0,12)">3812</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3522220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4786.000000 -671.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3522460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4789.000000 -603.000000) translate(0,12)">3822</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35226a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3287.000000 -771.000000) translate(0,15)">1号、2号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35226a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3287.000000 -771.000000) translate(0,33)">SFSZ11-50000/110GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35226a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3287.000000 -771.000000) translate(0,51)">50MVA,100/100/100</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35226a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3287.000000 -771.000000) translate(0,69)">YN,yn0,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35226a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3287.000000 -771.000000) translate(0,87)">110±8×1.25%/38.5±2×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35226a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3287.000000 -771.000000) translate(0,105)">Ud1-2=10.5%,Ud1-3=17.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35226a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3287.000000 -771.000000) translate(0,123)">Ud2-3=6.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3541bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3977.000000 -282.000000) translate(0,12)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3541bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3977.000000 -282.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3541bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3977.000000 -282.000000) translate(0,42)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3541bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3977.000000 -282.000000) translate(0,57)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3541bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3977.000000 -282.000000) translate(0,72)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3541bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3977.000000 -282.000000) translate(0,87)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3541bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3977.000000 -282.000000) translate(0,102)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3541bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3977.000000 -282.000000) translate(0,117)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35429e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4185.000000 -278.000000) translate(0,12)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35429e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4185.000000 -278.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35429e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4185.000000 -278.000000) translate(0,42)">3</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35429e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4185.000000 -278.000000) translate(0,57)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35429e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4185.000000 -278.000000) translate(0,72)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35429e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4185.000000 -278.000000) translate(0,87)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35429e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4185.000000 -278.000000) translate(0,102)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35429e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4185.000000 -278.000000) translate(0,117)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3542f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4311.000000 -273.000000) translate(0,12)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3542f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4311.000000 -273.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3542f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4311.000000 -273.000000) translate(0,42)">4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3542f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4311.000000 -273.000000) translate(0,57)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3542f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4311.000000 -273.000000) translate(0,72)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3542f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4311.000000 -273.000000) translate(0,87)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3542f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4311.000000 -273.000000) translate(0,102)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3542f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4311.000000 -273.000000) translate(0,117)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35431a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3869.000000 -284.000000) translate(0,12)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35431a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3869.000000 -284.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35431a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3869.000000 -284.000000) translate(0,42)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35431a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3869.000000 -284.000000) translate(0,57)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35431a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3869.000000 -284.000000) translate(0,72)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35431a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3869.000000 -284.000000) translate(0,87)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35431a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3869.000000 -284.000000) translate(0,102)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35431a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3869.000000 -284.000000) translate(0,117)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34d5c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4629.000000 -356.000000) translate(0,12)">0866</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_34e78a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3310.000000 -1175.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e8ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4728.000000 -280.000000) translate(0,12)">06267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e9400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4944.000000 -286.000000) translate(0,12)">06167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e9640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4807.000000 -194.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e9880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4881.000000 -193.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e9ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4938.000000 -194.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34e9d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5029.000000 -192.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_35bf310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3826.000000 -786.000000) translate(0,8)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_35bf310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3826.000000 -786.000000) translate(0,18)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_35bf310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3826.000000 -786.000000) translate(0,28)">消</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_35bf310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3826.000000 -786.000000) translate(0,38)">弧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_35bf310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3826.000000 -786.000000) translate(0,48)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_35bf310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3826.000000 -786.000000) translate(0,58)">圈</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3019" y="-1194"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3014" y="-606"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_359ffd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3673.000000 -296.000000)" xlink:href="#voltageTransformer:shape15"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35a2a40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4395.000000 -320.000000)" xlink:href="#voltageTransformer:shape15"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35a8760">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4965.500000 -979.500000)" xlink:href="#voltageTransformer:shape15"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35abec0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4991.500000 -622.500000)" xlink:href="#voltageTransformer:shape15"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_HS.CX_HS_382Ld">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5084.000000 -571.000000)" xlink:href="#load:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12151" ObjectName="EC-CX_HS.CX_HS_382Ld"/>
    <cge:TPSR_Ref TObjectID="12151"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_HS.CX_HS_373Ld">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5107.000000 -1045.000000)" xlink:href="#load:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12147" ObjectName="EC-CX_HS.CX_HS_373Ld"/>
    <cge:TPSR_Ref TObjectID="12147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_HS.084Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4486.000000 -259.000000)" xlink:href="#load:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12152" ObjectName="EC-CX_HS.084Ld"/>
    <cge:TPSR_Ref TObjectID="12152"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_HS.075Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3774.000000 -257.000000)" xlink:href="#load:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12153" ObjectName="EC-CX_HS.075Ld"/>
    <cge:TPSR_Ref TObjectID="12153"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-23490" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4148.000000 -754.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23490" ObjectName="CX_HS:CX_HS_2T_Tmp1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-23491" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4148.000000 -740.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23491" ObjectName="CX_HS:CX_HS_2T_Tmp2"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3124.500000 -1126.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-23452" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3750.000000 -783.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23452" ObjectName="CX_HS:CX_HS_1T_Tmp2"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-23451" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3750.000000 -797.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23451" ObjectName="CX_HS:CX_HS_1T_Tmp1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-62624" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3164.538462 -992.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62624" ObjectName="CX_HS:CX_HS_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-79724" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3164.538462 -950.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79724" ObjectName="CX_HS:CX_HS_sumQ"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="3136" y="-1185"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="3136" y="-1185"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3088" y="-1202"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3088" y="-1202"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="3298,-1194 3295,-1197 3295,-1134 3298,-1137 3298,-1194" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="3298,-1194 3295,-1197 3358,-1197 3355,-1194 3298,-1194" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="3298,-1137 3295,-1134 3358,-1134 3355,-1137 3298,-1137" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="3355,-1194 3358,-1197 3358,-1134 3355,-1137 3355,-1194" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="57" stroke="rgb(255,255,255)" width="57" x="3298" y="-1194"/>
     <rect fill="none" height="57" qtmmishow="hidden" stroke="rgb(0,0,0)" width="57" x="3298" y="-1194"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="19" qtmmishow="hidden" width="24" x="3617" y="-228"/>
    </a>
   <metadata/><rect fill="white" height="19" opacity="0" stroke="white" transform="" width="24" x="3617" y="-228"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="19" qtmmishow="hidden" width="24" x="4533" y="-209"/>
    </a>
   <metadata/><rect fill="white" height="19" opacity="0" stroke="white" transform="" width="24" x="4533" y="-209"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34c8f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3756.000000 1196.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34ca360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3745.000000 1181.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34cb0b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3770.000000 1166.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34cbb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4173.000000 1196.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34cbe10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4162.000000 1181.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34cc050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4187.000000 1166.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34cc470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3655.000000 641.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34cc730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3644.000000 626.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34cc970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3669.000000 611.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34ccd90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4284.000000 641.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34cd050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4273.000000 626.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34cd290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4298.000000 611.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34cd6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5152.000000 1117.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34cd970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5141.000000 1102.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34cdbb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5166.000000 1087.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34cdfd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4606.000000 939.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34ce290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4595.000000 924.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34ce4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4620.000000 909.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34ce8f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4606.000000 612.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34cebb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4595.000000 597.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34cedf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4620.000000 582.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34cf210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3488.000000 180.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34cf4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3477.000000 165.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34cf710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3502.000000 150.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34cfb30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3702.000000 228.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34cfdf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3691.000000 213.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34d0030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3716.000000 198.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34d0450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4430.000000 187.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34d0710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4419.000000 172.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34d0950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4444.000000 157.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34d0d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3796.000000 167.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34d1070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3821.000000 152.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3566cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3653.000000 813.000000) translate(0,12)">档位（档）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3567750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3653.000000 798.000000) translate(0,12)">油温1（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35684b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3653.000000 783.000000) translate(0,12)">油温2（℃）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35687f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4047.000000 770.000000) translate(0,12)">档位（档）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3568a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4047.000000 755.000000) translate(0,12)">油温1（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3568c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4047.000000 740.000000) translate(0,12)">油温2（℃）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3533e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3463.000000 604.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3534b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3463.000000 590.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3535080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3469.500000 575.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3535300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3455.000000 559.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3535540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3463.000000 619.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3535870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4435.000000 604.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3535ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4435.000000 590.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3535d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4441.500000 575.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3535f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4427.000000 559.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35361a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4435.000000 619.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35364d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4813.000000 397.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3536740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4813.000000 383.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3536980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4819.500000 368.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3536bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4805.000000 352.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3536e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4813.000000 412.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3537130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4699.000000 1247.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35373a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4699.000000 1233.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35375e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4705.500000 1218.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3537820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4691.000000 1202.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3537a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4699.000000 1262.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_LF" endPointId="0" endStationName="CX_HS" flowDrawDirect="1" flowShape="0" id="AC-110kV.luhong_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3721,-1143 3721,-1190 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9182" ObjectName="AC-110kV.luhong_line"/>
    <cge:TPSR_Ref TObjectID="9182_SS-24"/></metadata>
   <polyline fill="none" opacity="0" points="3721,-1143 3721,-1190 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_HS" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-110kV.yaohongzhiThs_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4107,-1151 4107,-1205 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="19327" ObjectName="AC-110kV.yaohongzhiThs_line"/>
    <cge:TPSR_Ref TObjectID="19327_SS-24"/></metadata>
   <polyline fill="none" opacity="0" points="4107,-1151 4107,-1205 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_HS" endPointId="0" endStationName="PAS_XN" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_HongQing" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5080,-866 5135,-866 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18075" ObjectName="AC-35kV.LN_HongQing"/>
    <cge:TPSR_Ref TObjectID="18075_SS-24"/></metadata>
   <polyline fill="none" opacity="0" points="5080,-866 5135,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_HS" endPointId="0" endStationName="LF_TG" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_HongTu" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5080,-788 5135,-788 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18066" ObjectName="AC-35kV.LN_HongTu"/>
    <cge:TPSR_Ref TObjectID="18066_SS-24"/></metadata>
   <polyline fill="none" opacity="0" points="5080,-788 5135,-788 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_HS" endPointId="0" endStationName="PAS_XN" flowDrawDirect="1" flowShape="0" id="AC-35kV.JingHongT" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5082,-697 5137,-697 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48926" ObjectName="AC-35kV.JingHongT"/>
    <cge:TPSR_Ref TObjectID="48926_SS-24"/></metadata>
   <polyline fill="none" opacity="0" points="5082,-697 5137,-697 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23495" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3850.000000 -895.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23495" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3813"/>
     <cge:Term_Ref ObjectID="5414"/>
    <cge:TPSR_Ref TObjectID="3813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23522" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4013.000000 -658.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23522" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3818"/>
     <cge:Term_Ref ObjectID="5424"/>
    <cge:TPSR_Ref TObjectID="3818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23534" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4483.000000 -187.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23534" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3824"/>
     <cge:Term_Ref ObjectID="5436"/>
    <cge:TPSR_Ref TObjectID="3824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23535" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4483.000000 -187.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23535" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3824"/>
     <cge:Term_Ref ObjectID="5436"/>
    <cge:TPSR_Ref TObjectID="3824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23531" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4483.000000 -187.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23531" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3824"/>
     <cge:Term_Ref ObjectID="5436"/>
    <cge:TPSR_Ref TObjectID="3824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-23478" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4152.000000 -768.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23478" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3875"/>
     <cge:Term_Ref ObjectID="5543"/>
    <cge:TPSR_Ref TObjectID="3875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-23398" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3520.000000 -619.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23398" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3767"/>
     <cge:Term_Ref ObjectID="5324"/>
    <cge:TPSR_Ref TObjectID="3767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-23399" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3520.000000 -619.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23399" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3767"/>
     <cge:Term_Ref ObjectID="5324"/>
    <cge:TPSR_Ref TObjectID="3767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-23400" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3520.000000 -619.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23400" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3767"/>
     <cge:Term_Ref ObjectID="5324"/>
    <cge:TPSR_Ref TObjectID="3767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-23404" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3520.000000 -619.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23404" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3767"/>
     <cge:Term_Ref ObjectID="5324"/>
    <cge:TPSR_Ref TObjectID="3767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-23406" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3520.000000 -619.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23406" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3767"/>
     <cge:Term_Ref ObjectID="5324"/>
    <cge:TPSR_Ref TObjectID="3767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-23384" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4761.000000 -1261.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23384" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3765"/>
     <cge:Term_Ref ObjectID="5322"/>
    <cge:TPSR_Ref TObjectID="3765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-23385" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4761.000000 -1261.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23385" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3765"/>
     <cge:Term_Ref ObjectID="5322"/>
    <cge:TPSR_Ref TObjectID="3765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-23386" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4761.000000 -1261.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23386" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3765"/>
     <cge:Term_Ref ObjectID="5322"/>
    <cge:TPSR_Ref TObjectID="3765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-23390" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4761.000000 -1261.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23390" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3765"/>
     <cge:Term_Ref ObjectID="5322"/>
    <cge:TPSR_Ref TObjectID="3765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-23392" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4761.000000 -1261.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23392" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3765"/>
     <cge:Term_Ref ObjectID="5322"/>
    <cge:TPSR_Ref TObjectID="3765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23561" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4625.000000 -753.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23561" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3843"/>
     <cge:Term_Ref ObjectID="5474"/>
    <cge:TPSR_Ref TObjectID="3843"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-23401" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4496.000000 -619.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23401" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3768"/>
     <cge:Term_Ref ObjectID="5325"/>
    <cge:TPSR_Ref TObjectID="3768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-23402" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4496.000000 -619.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23402" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3768"/>
     <cge:Term_Ref ObjectID="5325"/>
    <cge:TPSR_Ref TObjectID="3768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-23403" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4496.000000 -619.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23403" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3768"/>
     <cge:Term_Ref ObjectID="5325"/>
    <cge:TPSR_Ref TObjectID="3768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-23405" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4496.000000 -619.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23405" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3768"/>
     <cge:Term_Ref ObjectID="5325"/>
    <cge:TPSR_Ref TObjectID="3768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-23408" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4496.000000 -619.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23408" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3768"/>
     <cge:Term_Ref ObjectID="5325"/>
    <cge:TPSR_Ref TObjectID="3768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-23387" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4875.000000 -411.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23387" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3766"/>
     <cge:Term_Ref ObjectID="5323"/>
    <cge:TPSR_Ref TObjectID="3766"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-23388" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4875.000000 -411.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23388" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3766"/>
     <cge:Term_Ref ObjectID="5323"/>
    <cge:TPSR_Ref TObjectID="3766"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-23389" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4875.000000 -411.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23389" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3766"/>
     <cge:Term_Ref ObjectID="5323"/>
    <cge:TPSR_Ref TObjectID="3766"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-23391" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4875.000000 -411.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23391" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3766"/>
     <cge:Term_Ref ObjectID="5323"/>
    <cge:TPSR_Ref TObjectID="3766"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-23394" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4875.000000 -411.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23394" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3766"/>
     <cge:Term_Ref ObjectID="5323"/>
    <cge:TPSR_Ref TObjectID="3766"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23519" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3807.000000 -1196.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23519" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3781"/>
     <cge:Term_Ref ObjectID="5350"/>
    <cge:TPSR_Ref TObjectID="3781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23520" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3807.000000 -1196.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23520" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3781"/>
     <cge:Term_Ref ObjectID="5350"/>
    <cge:TPSR_Ref TObjectID="3781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23513" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3807.000000 -1196.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23513" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3781"/>
     <cge:Term_Ref ObjectID="5350"/>
    <cge:TPSR_Ref TObjectID="3781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23507" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4224.000000 -1196.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23507" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3797"/>
     <cge:Term_Ref ObjectID="5382"/>
    <cge:TPSR_Ref TObjectID="3797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23508" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4224.000000 -1196.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23508" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3797"/>
     <cge:Term_Ref ObjectID="5382"/>
    <cge:TPSR_Ref TObjectID="3797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23501" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4224.000000 -1196.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23501" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3797"/>
     <cge:Term_Ref ObjectID="5382"/>
    <cge:TPSR_Ref TObjectID="3797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23447" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3710.000000 -641.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23447" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3794"/>
     <cge:Term_Ref ObjectID="5376"/>
    <cge:TPSR_Ref TObjectID="3794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23448" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3710.000000 -641.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23448" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3794"/>
     <cge:Term_Ref ObjectID="5376"/>
    <cge:TPSR_Ref TObjectID="3794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23443" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3710.000000 -641.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23443" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3794"/>
     <cge:Term_Ref ObjectID="5376"/>
    <cge:TPSR_Ref TObjectID="3794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23486" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4338.000000 -641.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23486" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3810"/>
     <cge:Term_Ref ObjectID="5408"/>
    <cge:TPSR_Ref TObjectID="3810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23487" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4338.000000 -641.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23487" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3810"/>
     <cge:Term_Ref ObjectID="5408"/>
    <cge:TPSR_Ref TObjectID="3810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23482" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4338.000000 -641.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23482" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3810"/>
     <cge:Term_Ref ObjectID="5408"/>
    <cge:TPSR_Ref TObjectID="3810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23651" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3545.000000 -178.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23651" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3872"/>
     <cge:Term_Ref ObjectID="5532"/>
    <cge:TPSR_Ref TObjectID="3872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23652" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3545.000000 -178.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23652" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3872"/>
     <cge:Term_Ref ObjectID="5532"/>
    <cge:TPSR_Ref TObjectID="3872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23648" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3545.000000 -178.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23648" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3872"/>
     <cge:Term_Ref ObjectID="5532"/>
    <cge:TPSR_Ref TObjectID="3872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23627" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3618.000000 -178.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23627" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3860"/>
     <cge:Term_Ref ObjectID="5508"/>
    <cge:TPSR_Ref TObjectID="3860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23628" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3618.000000 -178.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23628" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3860"/>
     <cge:Term_Ref ObjectID="5508"/>
    <cge:TPSR_Ref TObjectID="3860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23624" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3618.000000 -178.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23624" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3860"/>
     <cge:Term_Ref ObjectID="5508"/>
    <cge:TPSR_Ref TObjectID="3860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23528" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3755.000000 -228.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23528" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3821"/>
     <cge:Term_Ref ObjectID="5430"/>
    <cge:TPSR_Ref TObjectID="3821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23529" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3755.000000 -228.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23529" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3821"/>
     <cge:Term_Ref ObjectID="5430"/>
    <cge:TPSR_Ref TObjectID="3821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23525" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3755.000000 -228.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23525" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3821"/>
     <cge:Term_Ref ObjectID="5430"/>
    <cge:TPSR_Ref TObjectID="3821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23541" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3855.000000 -167.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23541" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3827"/>
     <cge:Term_Ref ObjectID="5442"/>
    <cge:TPSR_Ref TObjectID="3827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23537" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3855.000000 -167.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23537" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3827"/>
     <cge:Term_Ref ObjectID="5442"/>
    <cge:TPSR_Ref TObjectID="3827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23547" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3934.000000 -167.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23547" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3831"/>
     <cge:Term_Ref ObjectID="5450"/>
    <cge:TPSR_Ref TObjectID="3831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23543" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3934.000000 -167.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23543" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3831"/>
     <cge:Term_Ref ObjectID="5450"/>
    <cge:TPSR_Ref TObjectID="3831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23553" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4133.000000 -168.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23553" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3835"/>
     <cge:Term_Ref ObjectID="5458"/>
    <cge:TPSR_Ref TObjectID="3835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23549" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4133.000000 -168.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23549" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3835"/>
     <cge:Term_Ref ObjectID="5458"/>
    <cge:TPSR_Ref TObjectID="3835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23559" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4252.000000 -166.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23559" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3839"/>
     <cge:Term_Ref ObjectID="5466"/>
    <cge:TPSR_Ref TObjectID="3839"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23555" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4252.000000 -166.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23555" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3839"/>
     <cge:Term_Ref ObjectID="5466"/>
    <cge:TPSR_Ref TObjectID="3839"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23633" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4549.000000 -187.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23633" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3863"/>
     <cge:Term_Ref ObjectID="5514"/>
    <cge:TPSR_Ref TObjectID="3863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23634" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4549.000000 -187.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23634" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3863"/>
     <cge:Term_Ref ObjectID="5514"/>
    <cge:TPSR_Ref TObjectID="3863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23630" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4549.000000 -187.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23630" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3863"/>
     <cge:Term_Ref ObjectID="5514"/>
    <cge:TPSR_Ref TObjectID="3863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23645" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5155.000000 -1063.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23645" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3869"/>
     <cge:Term_Ref ObjectID="5526"/>
    <cge:TPSR_Ref TObjectID="3869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23646" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5155.000000 -1063.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23646" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3869"/>
     <cge:Term_Ref ObjectID="5526"/>
    <cge:TPSR_Ref TObjectID="3869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23642" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5155.000000 -1063.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23642" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3869"/>
     <cge:Term_Ref ObjectID="5526"/>
    <cge:TPSR_Ref TObjectID="3869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23566" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5057.000000 -954.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23566" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3846"/>
     <cge:Term_Ref ObjectID="5480"/>
    <cge:TPSR_Ref TObjectID="3846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23567" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5057.000000 -954.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23567" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3846"/>
     <cge:Term_Ref ObjectID="5480"/>
    <cge:TPSR_Ref TObjectID="3846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23564" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5057.000000 -954.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23564" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3846"/>
     <cge:Term_Ref ObjectID="5480"/>
    <cge:TPSR_Ref TObjectID="3846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23572" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5155.000000 -900.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23572" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3849"/>
     <cge:Term_Ref ObjectID="5486"/>
    <cge:TPSR_Ref TObjectID="3849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23573" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5155.000000 -900.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23573" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3849"/>
     <cge:Term_Ref ObjectID="5486"/>
    <cge:TPSR_Ref TObjectID="3849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23569" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5155.000000 -900.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23569" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3849"/>
     <cge:Term_Ref ObjectID="5486"/>
    <cge:TPSR_Ref TObjectID="3849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23578" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5155.000000 -814.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23578" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3852"/>
     <cge:Term_Ref ObjectID="5492"/>
    <cge:TPSR_Ref TObjectID="3852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23579" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5155.000000 -814.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23579" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3852"/>
     <cge:Term_Ref ObjectID="5492"/>
    <cge:TPSR_Ref TObjectID="3852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23575" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5155.000000 -814.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23575" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3852"/>
     <cge:Term_Ref ObjectID="5492"/>
    <cge:TPSR_Ref TObjectID="3852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23584" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5155.000000 -723.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23584" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3855"/>
     <cge:Term_Ref ObjectID="5498"/>
    <cge:TPSR_Ref TObjectID="3855"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23585" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5155.000000 -723.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23585" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3855"/>
     <cge:Term_Ref ObjectID="5498"/>
    <cge:TPSR_Ref TObjectID="3855"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23581" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5155.000000 -723.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23581" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3855"/>
     <cge:Term_Ref ObjectID="5498"/>
    <cge:TPSR_Ref TObjectID="3855"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23639" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5155.000000 -609.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23639" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3866"/>
     <cge:Term_Ref ObjectID="5520"/>
    <cge:TPSR_Ref TObjectID="3866"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23640" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5155.000000 -609.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23640" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3866"/>
     <cge:Term_Ref ObjectID="5520"/>
    <cge:TPSR_Ref TObjectID="3866"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23636" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5155.000000 -609.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23636" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3866"/>
     <cge:Term_Ref ObjectID="5520"/>
    <cge:TPSR_Ref TObjectID="3866"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23435" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4660.000000 -939.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23435" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3790"/>
     <cge:Term_Ref ObjectID="5368"/>
    <cge:TPSR_Ref TObjectID="3790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23436" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4660.000000 -939.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23436" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3790"/>
     <cge:Term_Ref ObjectID="5368"/>
    <cge:TPSR_Ref TObjectID="3790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23431" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4660.000000 -939.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23431" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3790"/>
     <cge:Term_Ref ObjectID="5368"/>
    <cge:TPSR_Ref TObjectID="3790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23474" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4662.000000 -610.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23474" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3806"/>
     <cge:Term_Ref ObjectID="5400"/>
    <cge:TPSR_Ref TObjectID="3806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23475" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4662.000000 -610.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23475" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3806"/>
     <cge:Term_Ref ObjectID="5400"/>
    <cge:TPSR_Ref TObjectID="3806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23470" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4662.000000 -610.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23470" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3806"/>
     <cge:Term_Ref ObjectID="5400"/>
    <cge:TPSR_Ref TObjectID="3806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-23439" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3754.000000 -811.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23439" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3874"/>
     <cge:Term_Ref ObjectID="5538"/>
    <cge:TPSR_Ref TObjectID="3874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23657" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4627.000000 -187.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23657" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3873"/>
     <cge:Term_Ref ObjectID="5534"/>
    <cge:TPSR_Ref TObjectID="3873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23658" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4627.000000 -187.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23658" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3873"/>
     <cge:Term_Ref ObjectID="5534"/>
    <cge:TPSR_Ref TObjectID="3873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23654" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4627.000000 -187.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23654" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3873"/>
     <cge:Term_Ref ObjectID="5534"/>
    <cge:TPSR_Ref TObjectID="3873"/></metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-24078">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4216.000000 -857.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3803" ObjectName="SW-CX_HS.CX_HS_1022SW"/>
     <cge:Meas_Ref ObjectId="24078"/>
    <cge:TPSR_Ref TObjectID="3803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24076">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4098.000000 -944.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3801" ObjectName="SW-CX_HS.CX_HS_1722SW"/>
     <cge:Meas_Ref ObjectId="24076"/>
    <cge:TPSR_Ref TObjectID="3801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24073">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4098.000000 -1057.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3798" ObjectName="SW-CX_HS.CX_HS_1726SW"/>
     <cge:Meas_Ref ObjectId="24073"/>
    <cge:TPSR_Ref TObjectID="3798"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23888">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3612.000000 -820.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3788" ObjectName="SW-CX_HS.CX_HS_10117SW"/>
     <cge:Meas_Ref ObjectId="23888"/>
    <cge:TPSR_Ref TObjectID="3788"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23887">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3591.000000 -839.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3787" ObjectName="SW-CX_HS.CX_HS_1011SW"/>
     <cge:Meas_Ref ObjectId="23887"/>
    <cge:TPSR_Ref TObjectID="3787"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23713">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3572.000000 -948.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3772" ObjectName="SW-CX_HS.CX_HS_19010SW"/>
     <cge:Meas_Ref ObjectId="23713"/>
    <cge:TPSR_Ref TObjectID="3772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23711">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3551.000000 -971.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3770" ObjectName="SW-CX_HS.CX_HS_1901SW"/>
     <cge:Meas_Ref ObjectId="23711"/>
    <cge:TPSR_Ref TObjectID="3770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23712">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3571.000000 -1026.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3771" ObjectName="SW-CX_HS.CX_HS_19017SW"/>
     <cge:Meas_Ref ObjectId="23712"/>
    <cge:TPSR_Ref TObjectID="3771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23885">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3712.000000 -937.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3785" ObjectName="SW-CX_HS.CX_HS_1711SW"/>
     <cge:Meas_Ref ObjectId="23885"/>
    <cge:TPSR_Ref TObjectID="3785"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23886">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3728.000000 -983.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3786" ObjectName="SW-CX_HS.CX_HS_17117SW"/>
     <cge:Meas_Ref ObjectId="23886"/>
    <cge:TPSR_Ref TObjectID="3786"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23884">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3728.000000 -1036.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3784" ObjectName="SW-CX_HS.CX_HS_17160SW"/>
     <cge:Meas_Ref ObjectId="23884"/>
    <cge:TPSR_Ref TObjectID="3784"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23882">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3712.000000 -1050.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3782" ObjectName="SW-CX_HS.CX_HS_1716SW"/>
     <cge:Meas_Ref ObjectId="23882"/>
    <cge:TPSR_Ref TObjectID="3782"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23883">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3728.000000 -1096.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3783" ObjectName="SW-CX_HS.CX_HS_17167SW"/>
     <cge:Meas_Ref ObjectId="23883"/>
    <cge:TPSR_Ref TObjectID="3783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24079">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4237.000000 -842.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3804" ObjectName="SW-CX_HS.CX_HS_10227SW"/>
     <cge:Meas_Ref ObjectId="24079"/>
    <cge:TPSR_Ref TObjectID="3804"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24074">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4114.000000 -1106.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3799" ObjectName="SW-CX_HS.CX_HS_17267SW"/>
     <cge:Meas_Ref ObjectId="24074"/>
    <cge:TPSR_Ref TObjectID="3799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24075">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4114.000000 -1046.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3800" ObjectName="SW-CX_HS.CX_HS_17260SW"/>
     <cge:Meas_Ref ObjectId="24075"/>
    <cge:TPSR_Ref TObjectID="3800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24077">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4114.000000 -993.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3802" ObjectName="SW-CX_HS.CX_HS_17227SW"/>
     <cge:Meas_Ref ObjectId="24077"/>
    <cge:TPSR_Ref TObjectID="3802"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23718">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3964.000000 -955.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3775" ObjectName="SW-CX_HS.CX_HS_19020SW"/>
     <cge:Meas_Ref ObjectId="23718"/>
    <cge:TPSR_Ref TObjectID="3775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23716">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3942.000000 -978.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3773" ObjectName="SW-CX_HS.CX_HS_1902SW"/>
     <cge:Meas_Ref ObjectId="23716"/>
    <cge:TPSR_Ref TObjectID="3773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23717">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3962.000000 -1033.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3774" ObjectName="SW-CX_HS.CX_HS_19027SW"/>
     <cge:Meas_Ref ObjectId="23717"/>
    <cge:TPSR_Ref TObjectID="3774"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24180">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3797.000000 -861.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3814" ObjectName="SW-CX_HS.CX_HS_1121SW"/>
     <cge:Meas_Ref ObjectId="24180"/>
    <cge:TPSR_Ref TObjectID="3814"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24182">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3918.000000 -863.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3816" ObjectName="SW-CX_HS.CX_HS_1122SW"/>
     <cge:Meas_Ref ObjectId="24182"/>
    <cge:TPSR_Ref TObjectID="3816"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24183">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3918.000000 -797.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3817" ObjectName="SW-CX_HS.CX_HS_11227SW"/>
     <cge:Meas_Ref ObjectId="24183"/>
    <cge:TPSR_Ref TObjectID="3817"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24181">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3797.000000 -795.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3815" ObjectName="SW-CX_HS.CX_HS_11217SW"/>
     <cge:Meas_Ref ObjectId="24181"/>
    <cge:TPSR_Ref TObjectID="3815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23897">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3509.000000 -789.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3789" ObjectName="SW-CX_HS.CX_HS_1010SW"/>
     <cge:Meas_Ref ObjectId="23897"/>
    <cge:TPSR_Ref TObjectID="3789"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24977">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3539.000000 -481.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3822" ObjectName="SW-CX_HS.CX_HS_0731SW"/>
     <cge:Meas_Ref ObjectId="24977"/>
    <cge:TPSR_Ref TObjectID="3822"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24978">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3539.000000 -323.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10452" ObjectName="SW-CX_HS.CX_HS_0736SW"/>
     <cge:Meas_Ref ObjectId="24978"/>
    <cge:TPSR_Ref TObjectID="10452"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24889">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3604.000000 -480.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3861" ObjectName="SW-CX_HS.CX_HS_0741SW"/>
     <cge:Meas_Ref ObjectId="24889"/>
    <cge:TPSR_Ref TObjectID="3861"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24890">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3604.000000 -322.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3862" ObjectName="SW-CX_HS.CX_HS_0746SW"/>
     <cge:Meas_Ref ObjectId="24890"/>
    <cge:TPSR_Ref TObjectID="3862"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23917">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3590.000000 -640.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3796" ObjectName="SW-CX_HS.CX_HS_0016SW"/>
     <cge:Meas_Ref ObjectId="23917"/>
    <cge:TPSR_Ref TObjectID="3796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23916">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3590.000000 -547.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3795" ObjectName="SW-CX_HS.CX_HS_0011SW"/>
     <cge:Meas_Ref ObjectId="23916"/>
    <cge:TPSR_Ref TObjectID="3795"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24285">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3771.000000 -482.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10453" ObjectName="SW-CX_HS.CX_HS_0751SW"/>
     <cge:Meas_Ref ObjectId="24285"/>
    <cge:TPSR_Ref TObjectID="10453"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24286">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3771.000000 -324.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3823" ObjectName="SW-CX_HS.CX_HS_0756SW"/>
     <cge:Meas_Ref ObjectId="24286"/>
    <cge:TPSR_Ref TObjectID="3823"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24385">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3943.000000 -324.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3833" ObjectName="SW-CX_HS.CX_HS_0776SW"/>
     <cge:Meas_Ref ObjectId="24385"/>
    <cge:TPSR_Ref TObjectID="3833"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24384">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3943.000000 -482.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3832" ObjectName="SW-CX_HS.CX_HS_0771SW"/>
     <cge:Meas_Ref ObjectId="24384"/>
    <cge:TPSR_Ref TObjectID="3832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24386">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3964.000000 -311.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3834" ObjectName="SW-CX_HS.CX_HS_07767SW"/>
     <cge:Meas_Ref ObjectId="24386"/>
    <cge:TPSR_Ref TObjectID="3834"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23744">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4075.000000 -480.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3780" ObjectName="SW-CX_HS.CX_HS_0812SW"/>
     <cge:Meas_Ref ObjectId="23744"/>
    <cge:TPSR_Ref TObjectID="3780"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24247">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3961.000000 -556.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3820" ObjectName="SW-CX_HS.CX_HS_0121SW"/>
     <cge:Meas_Ref ObjectId="24247"/>
    <cge:TPSR_Ref TObjectID="3820"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24246">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4060.000000 -557.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3819" ObjectName="SW-CX_HS.CX_HS_0122SW"/>
     <cge:Meas_Ref ObjectId="24246"/>
    <cge:TPSR_Ref TObjectID="3819"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24107">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4215.000000 -640.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3812" ObjectName="SW-CX_HS.CX_HS_0026SW"/>
     <cge:Meas_Ref ObjectId="24107"/>
    <cge:TPSR_Ref TObjectID="3812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24106">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4215.000000 -547.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3811" ObjectName="SW-CX_HS.CX_HS_0022SW"/>
     <cge:Meas_Ref ObjectId="24106"/>
    <cge:TPSR_Ref TObjectID="3811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24322">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4483.000000 -483.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3825" ObjectName="SW-CX_HS.CX_HS_0842SW"/>
     <cge:Meas_Ref ObjectId="24322"/>
    <cge:TPSR_Ref TObjectID="3825"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24323">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4483.000000 -325.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3826" ObjectName="SW-CX_HS.CX_HS_0846SW"/>
     <cge:Meas_Ref ObjectId="24323"/>
    <cge:TPSR_Ref TObjectID="3826"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24908">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4556.000000 -484.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3864" ObjectName="SW-CX_HS.CX_HS_0852SW"/>
     <cge:Meas_Ref ObjectId="24908"/>
    <cge:TPSR_Ref TObjectID="3864"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24909">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4556.000000 -326.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3865" ObjectName="SW-CX_HS.CX_HS_0856SW"/>
     <cge:Meas_Ref ObjectId="24909"/>
    <cge:TPSR_Ref TObjectID="3865"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24444">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4268.000000 -324.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3841" ObjectName="SW-CX_HS.CX_HS_0836SW"/>
     <cge:Meas_Ref ObjectId="24444"/>
    <cge:TPSR_Ref TObjectID="3841"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24443">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4268.000000 -482.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3840" ObjectName="SW-CX_HS.CX_HS_0832SW"/>
     <cge:Meas_Ref ObjectId="24443"/>
    <cge:TPSR_Ref TObjectID="3840"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24445">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4290.000000 -311.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3842" ObjectName="SW-CX_HS.CX_HS_08367SW"/>
     <cge:Meas_Ref ObjectId="24445"/>
    <cge:TPSR_Ref TObjectID="3842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24415">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.000000 -324.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3837" ObjectName="SW-CX_HS.CX_HS_0826SW"/>
     <cge:Meas_Ref ObjectId="24415"/>
    <cge:TPSR_Ref TObjectID="3837"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24414">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.000000 -482.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3836" ObjectName="SW-CX_HS.CX_HS_0822SW"/>
     <cge:Meas_Ref ObjectId="24414"/>
    <cge:TPSR_Ref TObjectID="3836"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24416">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4174.000000 -311.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3838" ObjectName="SW-CX_HS.CX_HS_08267SW"/>
     <cge:Meas_Ref ObjectId="24416"/>
    <cge:TPSR_Ref TObjectID="3838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24355">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3839.000000 -324.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3829" ObjectName="SW-CX_HS.CX_HS_0766SW"/>
     <cge:Meas_Ref ObjectId="24355"/>
    <cge:TPSR_Ref TObjectID="3829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24354">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3839.000000 -482.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3828" ObjectName="SW-CX_HS.CX_HS_0761SW"/>
     <cge:Meas_Ref ObjectId="24354"/>
    <cge:TPSR_Ref TObjectID="3828"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24356">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3860.000000 -311.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3830" ObjectName="SW-CX_HS.CX_HS_07667SW"/>
     <cge:Meas_Ref ObjectId="24356"/>
    <cge:TPSR_Ref TObjectID="3830"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4859.000000 -136.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4859.000000 -207.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3706.000000 -83.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4014.000000 -83.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4317.000000 -83.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4787.000000 -207.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4787.000000 -136.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4771.000000 -249.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5006.000000 -208.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5006.000000 -137.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4991.000000 -256.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24088">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4343.000000 -788.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3805" ObjectName="SW-CX_HS.CX_HS_1020SW"/>
     <cge:Meas_Ref ObjectId="24088"/>
    <cge:TPSR_Ref TObjectID="3805"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4883.000000 -1138.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4782.000000 -1138.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4884.000000 -1094.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4782.000000 -1094.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24957">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4877.000000 -1046.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3871" ObjectName="SW-CX_HS.CX_HS_3736SW"/>
     <cge:Meas_Ref ObjectId="24957"/>
    <cge:TPSR_Ref TObjectID="3871"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24489">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4875.000000 -914.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3848" ObjectName="SW-CX_HS.CX_HS_3746SW"/>
     <cge:Meas_Ref ObjectId="24489"/>
    <cge:TPSR_Ref TObjectID="3848"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24582">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4875.000000 -861.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3851" ObjectName="SW-CX_HS.CX_HS_3756SW"/>
     <cge:Meas_Ref ObjectId="24582"/>
    <cge:TPSR_Ref TObjectID="3851"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24616">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4875.000000 -782.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3854" ObjectName="SW-CX_HS.CX_HS_3766SW"/>
     <cge:Meas_Ref ObjectId="24616"/>
    <cge:TPSR_Ref TObjectID="3854"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23902">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4681.000000 -946.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3791" ObjectName="SW-CX_HS.CX_HS_3011SW"/>
     <cge:Meas_Ref ObjectId="23902"/>
    <cge:TPSR_Ref TObjectID="3791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24650">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4873.000000 -692.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3857" ObjectName="SW-CX_HS.CX_HS_3816SW"/>
     <cge:Meas_Ref ObjectId="24650"/>
    <cge:TPSR_Ref TObjectID="3857"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24932">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4875.000000 -572.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3868" ObjectName="SW-CX_HS.CX_HS_3826SW"/>
     <cge:Meas_Ref ObjectId="24932"/>
    <cge:TPSR_Ref TObjectID="3868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4875.000000 -521.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4875.000000 -477.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4877.000000 -434.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24471">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4682.000000 -793.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3845" ObjectName="SW-CX_HS.CX_HS_3121SW"/>
     <cge:Meas_Ref ObjectId="24471"/>
    <cge:TPSR_Ref TObjectID="3845"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24470">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4682.000000 -687.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3844" ObjectName="SW-CX_HS.CX_HS_3122SW"/>
     <cge:Meas_Ref ObjectId="24470"/>
    <cge:TPSR_Ref TObjectID="3844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24093">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4675.000000 -618.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3807" ObjectName="SW-CX_HS.CX_HS_3022SW"/>
     <cge:Meas_Ref ObjectId="24093"/>
    <cge:TPSR_Ref TObjectID="3807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23903">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4585.000000 -946.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3792" ObjectName="SW-CX_HS.CX_HS_3016SW"/>
     <cge:Meas_Ref ObjectId="23903"/>
    <cge:TPSR_Ref TObjectID="3792"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24094">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4580.000000 -618.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3808" ObjectName="SW-CX_HS.CX_HS_3026SW"/>
     <cge:Meas_Ref ObjectId="24094"/>
    <cge:TPSR_Ref TObjectID="3808"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4923.000000 -205.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4923.000000 -135.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23737">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4409.000000 -481.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3779" ObjectName="SW-CX_HS.CX_HS_0902SW"/>
     <cge:Meas_Ref ObjectId="23737"/>
    <cge:TPSR_Ref TObjectID="3779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23734">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3687.000000 -479.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3778" ObjectName="SW-CX_HS.CX_HS_0901SW"/>
     <cge:Meas_Ref ObjectId="23734"/>
    <cge:TPSR_Ref TObjectID="3778"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24956">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4781.000000 -1046.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3870" ObjectName="SW-CX_HS.CX_HS_3731SW"/>
     <cge:Meas_Ref ObjectId="24956"/>
    <cge:TPSR_Ref TObjectID="3870"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23721">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4780.000000 -997.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3776" ObjectName="SW-CX_HS.CX_HS_3901SW"/>
     <cge:Meas_Ref ObjectId="23721"/>
    <cge:TPSR_Ref TObjectID="3776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24488">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4782.000000 -914.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3847" ObjectName="SW-CX_HS.CX_HS_3741SW"/>
     <cge:Meas_Ref ObjectId="24488"/>
    <cge:TPSR_Ref TObjectID="3847"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24581">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4782.000000 -861.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3850" ObjectName="SW-CX_HS.CX_HS_3751SW"/>
     <cge:Meas_Ref ObjectId="24581"/>
    <cge:TPSR_Ref TObjectID="3850"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24615">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4782.000000 -782.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3853" ObjectName="SW-CX_HS.CX_HS_3761SW"/>
     <cge:Meas_Ref ObjectId="24615"/>
    <cge:TPSR_Ref TObjectID="3853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24649">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4780.000000 -692.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3856" ObjectName="SW-CX_HS.CX_HS_3812SW"/>
     <cge:Meas_Ref ObjectId="24649"/>
    <cge:TPSR_Ref TObjectID="3856"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23724">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4780.000000 -640.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3777" ObjectName="SW-CX_HS.CX_HS_3902SW"/>
     <cge:Meas_Ref ObjectId="23724"/>
    <cge:TPSR_Ref TObjectID="3777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4780.000000 -434.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4781.000000 -477.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4781.000000 -521.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24931">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4783.000000 -572.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3867" ObjectName="SW-CX_HS.CX_HS_3822SW"/>
     <cge:Meas_Ref ObjectId="24931"/>
    <cge:TPSR_Ref TObjectID="3867"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24997">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.978261 4612.000000 -326.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10455" ObjectName="SW-CX_HS.CX_HS_0866SW"/>
     <cge:Meas_Ref ObjectId="24997"/>
    <cge:TPSR_Ref TObjectID="10455"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24996">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -0.869565 4613.000000 -491.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10454" ObjectName="SW-CX_HS.CX_HS_0862SW"/>
     <cge:Meas_Ref ObjectId="24996"/>
    <cge:TPSR_Ref TObjectID="10454"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23910">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3780.000000 -707.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3793" ObjectName="SW-CX_HS.CX_HS_3010SW"/>
     <cge:Meas_Ref ObjectId="23910"/>
    <cge:TPSR_Ref TObjectID="3793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24101">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3903.000000 -707.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3809" ObjectName="SW-CX_HS.CX_HS_3020SW"/>
     <cge:Meas_Ref ObjectId="24101"/>
    <cge:TPSR_Ref TObjectID="3809"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="lf_索引_接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="3136" y="-1185"/></g>
   <g href="cx_索引_接线图_局属变110.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3088" y="-1202"/></g>
   <g href="AVC洪山站.svg" style="fill-opacity:0"><rect height="57" qtmmishow="hidden" stroke="rgb(0,0,0)" width="57" x="3298" y="-1194"/></g>
   <g href="cx_地调_重要用电用户表.svg" style="fill-opacity:0"><rect height="19" qtmmishow="hidden" width="24" x="3617" y="-228"/></g>
   <g href="cx_地调_重要用电用户表.svg" style="fill-opacity:0"><rect height="19" qtmmishow="hidden" width="24" x="4533" y="-209"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_335da30">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3558.000000 -846.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33e7140">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3497.000000 -851.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3436820">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 3670.000000 -379.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3437510">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3687.000000 -428.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33d1c10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4490.000000 -691.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33ee530">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3872.000000 -787.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33c62c0">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 3753.000000 -248.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3427df0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4075.000000 -432.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3475ce0">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 4392.000000 -378.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_343bc10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4409.000000 -427.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3435200">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4381.500000 -64.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34383e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4803.000000 -323.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34956a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5024.000000 -330.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33d9100">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4326.000000 -845.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33d9e70">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4373.000000 -850.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33db880">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3543.000000 -371.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33dc5d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3608.000000 -368.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34b2f70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3775.000000 -372.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34b3cc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3843.000000 -371.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34b4a10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3947.000000 -371.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34b5760">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4156.000000 -370.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34b64b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4272.000000 -371.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34b7200">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4616.000000 -372.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_360f5f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4560.000000 -373.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3610340">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4487.000000 -371.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_349e190">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5089.000000 -858.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3558080">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5089.000000 -780.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3451010">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5087.000000 -690.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33effa0">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4858.500000 -636.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34870b0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4582.000000 -1024.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_341be80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3833.000000 -727.000000)" xlink:href="#lightningRod:shape103"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34aa950">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3647.000000 -1083.000000)" xlink:href="#lightningRod:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34abca0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4037.000000 -1098.000000)" xlink:href="#lightningRod:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34ba320">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 3524.000000 -243.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3599490">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3553.000000 -1042.000000)" xlink:href="#lightningRod:shape148"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_359bec0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3944.000000 -1047.000000)" xlink:href="#lightningRod:shape148"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_359f520">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 3591.000000 -245.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35a5d10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4481.000000 -2.000000)" xlink:href="#lightningRod:shape149"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35aaf20">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4855.500000 -993.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3527340">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4989.500000 -861.500000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3527970">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5002.500000 -572.500000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3529ca0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4983.500000 -692.500000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_352ab80">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4989.500000 -782.500000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_352ba60">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4988.500000 -914.500000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_352c940">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4992.500000 -1046.500000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_352f020">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5001.500000 -434.500000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_352ff00">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5001.500000 -477.500000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3530de0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4999.500000 -521.500000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3531cc0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5009.500000 -1094.500000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3532ba0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5004.500000 -1138.500000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3537ca0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4527.000000 -249.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3538700">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4580.000000 -241.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3539300">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4452.000000 -243.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_353a070">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3605.000000 -686.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_353ade0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4227.000000 -687.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_353c990">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3854.000000 -292.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_353d700">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3957.000000 -292.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_353e470">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4165.000000 -293.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_353f1e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4282.000000 -294.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3545da0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3731.000000 -1115.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3546820">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4121.000000 -1130.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3547590">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5006.000000 -1063.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3548300">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5004.000000 -877.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3549070">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5004.000000 -799.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3549de0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5002.000000 -709.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_354ab50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5022.000000 -551.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_354b8c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4849.000000 -604.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_354c630">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4846.000000 -970.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_354d3a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3687.000000 -52.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_354e110">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3954.000000 -53.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35b5b40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4076.000000 -366.000000)" xlink:href="#lightningRod:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35b73e0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5043.500000 -906.500000)" xlink:href="#lightningRod:shape25"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_HS"/>
</svg>