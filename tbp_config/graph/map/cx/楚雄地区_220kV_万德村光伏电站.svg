<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-300" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="-358 -1215 3398 1941">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape16_0">
    <circle cx="31" cy="5" fillStyle="0" r="4" stroke-width="0.888889"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="15" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="33" y1="5" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="9" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="35" y1="5" y2="5"/>
   </symbol>
   <symbol id="breaker2:shape16_1">
    <circle cx="31" cy="5" fillStyle="0" r="4" stroke-width="0.888889"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="15" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="35" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="9" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="35" y1="5" y2="5"/>
   </symbol>
   <symbol id="breaker2:shape16-UnNor1">
    <circle cx="31" cy="5" fillStyle="0" r="4" stroke-width="0.888889"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="15" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="33" y1="5" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="9" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="35" y1="5" y2="5"/>
   </symbol>
   <symbol id="breaker2:shape16-UnNor2">
    <circle cx="31" cy="5" fillStyle="0" r="4" stroke-width="0.888889"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="15" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="35" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="9" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="35" y1="5" y2="5"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape14">
    <polyline points="9,9 3,12 1,13 1,14 1,14 3,16 6,17 10,19 11,20 11,20 11,21 10,22 6,23 3,25 2,25 2,26 2,27 3,28 6,29 10,31 11,31 11,32 11,33 10,33 6,35 3,36 2,37 2,38 2,39 3,39 6,41 10,42 11,43 11,44 11,44 10,45 6,47 3,48 1,50 1,50 1,51 3,52 9,55 " stroke-width="2.00006"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="10" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="54" y2="61"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape37">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,63 0,73 10,73 5,63 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,28 0,18 10,18 5,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="86" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape146">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
    <text font-family="SimSun" font-size="15" graphid="g_2a07b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
   </symbol>
   <symbol id="lightningRod:shape126">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="5" y1="6" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="5" y1="46" y2="29"/>
   </symbol>
   <symbol id="lightningRod:shape174">
    <rect height="18" stroke-width="1.1697" width="11" x="1" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="14" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="39" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.447552" x1="7" x2="7" y1="7" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape194">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="10" y1="8" y2="8"/>
    <polyline DF8003:Layer="PUBLIC" points="22,1 22,16 10,8 22,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="36" x2="22" y1="9" y2="9"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="load:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="6" y2="15"/>
    <polyline DF8003:Layer="PUBLIC" points="1,15 10,15 5,25 0,15 1,15 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="15" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="15" y2="25"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="reactance:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="13" x2="13" y1="39" y2="47"/>
    <polyline points="13,39 15,39 17,38 18,38 20,37 21,36 23,35 24,33 25,31 25,30 26,28 26,26 26,24 25,22 25,21 24,19 23,18 21,16 20,15 18,14 17,14 15,13 13,13 11,13 9,14 8,14 6,15 5,16 3,18 2,19 1,21 1,22 0,24 0,26 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="0" x2="12" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44" x1="13" x2="13" y1="5" y2="26"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape7_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="18" x2="43" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="18" x2="9" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="5" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="9" x2="9" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape7_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape5_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape43_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.8" x1="27" x2="24" y1="19" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
   </symbol>
   <symbol id="switch2:shape43_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.8" x1="27" x2="27" y1="13" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape43-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.8" x1="27" x2="27" y1="13" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape43-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.8" x1="27" x2="27" y1="13" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape44_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.8" x1="29" x2="32" y1="19" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="18" x2="43" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="18" x2="9" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="5" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape44_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="43" x2="43" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="52" x2="10" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="10" x2="10" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="6" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="3" x2="3" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.8" x1="30" x2="30" y1="13" y2="6"/>
   </symbol>
   <symbol id="switch2:shape44-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="43" x2="43" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="52" x2="10" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="10" x2="10" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="6" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="3" x2="3" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.8" x1="30" x2="30" y1="13" y2="6"/>
   </symbol>
   <symbol id="switch2:shape44-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="43" x2="43" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="52" x2="10" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="10" x2="10" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="6" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="3" x2="3" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.8" x1="30" x2="30" y1="13" y2="6"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="transformer:shape31_0">
    <circle cx="29" cy="42" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="102" y1="54" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="98" x2="103" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="103" x2="101" y1="87" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="23" y1="79" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="24" x2="42" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="33" x2="42" y1="54" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="33" x2="24" y1="54" y2="38"/>
   </symbol>
   <symbol id="transformer:shape31_1">
    <circle cx="70" cy="29" fillStyle="0" r="24.5" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="69" x2="69" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="76" x2="69" y1="31" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="69" x2="62" y1="24" y2="31"/>
   </symbol>
   <symbol id="transformer:shape31-2">
    <circle cx="70" cy="64" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="69" x2="69" y1="78" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="76" x2="69" y1="62" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="69" x2="62" y1="69" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape77_0">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,61 6,61 6,32 " stroke-width="1"/>
    <circle cx="31" cy="64" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="19" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="49" y2="31"/>
    <polyline DF8003:Layer="PUBLIC" points="31,18 25,31 37,31 31,18 31,19 31,18 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="31" y1="56" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="61" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="61" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="61" y2="56"/>
   </symbol>
   <symbol id="transformer2:shape77_1">
    <circle cx="31" cy="86" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="31" y1="90" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="31" y1="90" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="26" y1="90" y2="90"/>
   </symbol>
   <symbol id="voltageTransformer:shape40">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="64" x2="64" y1="10" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="61" x2="61" y1="11" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="57" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="46" x2="46" y1="0" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="39" x2="39" y1="0" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="9" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="45" y1="9" y2="9"/>
    <circle cx="31" cy="46" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="38" cy="40" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="31" cy="36" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="23" x2="23" y1="1" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="16" x2="16" y1="1" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.503497" x1="39" x2="22" y1="10" y2="10"/>
   </symbol>
   <symbol id="voltageTransformer:shape71">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="53" y1="12" y2="12"/>
    <ellipse cx="39" cy="19" fillStyle="0" rx="15" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.36031" x1="30" x2="34" y1="55" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.39762" x1="25" x2="21" y1="55" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.77579" x1="21" x2="35" y1="62" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="33" x2="41" y1="16" y2="20"/>
    <ellipse cx="16" cy="20" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="40" x2="41" y1="20" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="47" x2="41" y1="16" y2="20"/>
    <ellipse cx="28" cy="56" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="53" y1="32" y2="32"/>
    <ellipse cx="40" cy="39" fillStyle="0" rx="15" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="32" y2="32"/>
    <ellipse cx="15" cy="40" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="15" x2="16" y1="20" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="22" x2="16" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="16" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="33" x2="41" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="40" x2="41" y1="40" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="47" x2="41" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="15" x2="16" y1="40" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="21" x2="15" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="16" y1="36" y2="40"/>
   </symbol>
   <symbol id="voltageTransformer:shape146">
    <circle cx="23" cy="32" r="7.5" stroke-width="0.804311"/>
    <rect height="13" stroke-width="1" width="5" x="3" y="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="18" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="17" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="34" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="25" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="3" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="4" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="47" y2="37"/>
    <circle cx="34" cy="27" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="23" x2="20" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="24" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="18" y2="21"/>
    <circle cx="33" cy="14" r="7.5" stroke-width="0.804311"/>
    <circle cx="44" cy="20" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="34" x2="32" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="32" x2="29" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="34" x2="32" y1="12" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="35" x2="32" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="37" x2="35" y1="30" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="37" x2="35" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="47" x2="43" y1="22" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="47" x2="43" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="43" x2="43" y1="18" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="23" x2="21" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="35" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="29" y2="32"/>
    <circle cx="23" cy="21" r="7.5" stroke-width="0.804311"/>
   </symbol>
   <symbol id="voltageTransformer:shape35">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="11" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="11" x2="9" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="11" y1="28" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="4" x2="2" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="17" x2="15" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="15" x2="13" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="17" x2="15" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="11" y1="4" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="11" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="11" y1="9" y2="7"/>
    <circle cx="11" cy="24" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="6" cy="15" fillStyle="0" rx="6" ry="6.5" stroke-width="0.431185"/>
    <circle cx="11" cy="7" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="14" cy="16" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="6" x2="4" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="6" x2="4" y1="13" y2="16"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_34d78a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34d8a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34d9440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34da350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34db640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34dc2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34dce80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_34dd7a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_30af990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_30af990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34e0170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34e0170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34e1b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34e1b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_34e27d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34e44c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_34e50b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_34e5d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34e6650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34e7d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34e8830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34e8fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34e9770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34ea850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34eb1d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34ebcc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_34ec680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_34edb20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_34ee690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_34ef6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34f0300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34fead0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34f1bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_34f2c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_34f41a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1951" width="3408" x="-363" y="-1220"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_293efe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1531.000000 -281.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ffbf10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1520.000000 -296.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b705f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1545.000000 -311.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6e6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2929.000000 73.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6e910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2946.000000 58.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6ea80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2937.000000 87.000000) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6ebf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2938.000000 103.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6ed60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2937.000000 118.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6eed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2937.000000 133.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a76590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 764.000000 723.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a76810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 781.000000 708.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a76a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 772.000000 737.000000) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a76c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 773.000000 753.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a76ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 772.000000 768.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a77110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 772.000000 783.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab2490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2133.000000 833.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab26c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2122.000000 818.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab28d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2147.000000 803.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab2c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2062.000000 546.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab2e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2051.000000 531.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab30a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2076.000000 516.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab33d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2072.000000 159.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab3630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2061.000000 144.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab3870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2086.000000 129.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29af330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 96.000000 -302.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29af980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 85.000000 -317.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29afbc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 110.000000 -332.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ce02c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1325.000000 541.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ce0910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1314.000000 526.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ce0b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1339.000000 511.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ce0e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1308.000000 160.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ce10e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1297.000000 145.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ce1320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1322.000000 130.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d45460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -3.000000 84.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d456f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 14.000000 69.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d45900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 98.000000) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d45b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 114.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d45d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 129.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d45fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 144.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3078a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1449.000000 -127.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3078c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1438.000000 -142.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3078e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1463.000000 -157.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="343,629 346,637 340,637 343,629 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="294,640 298,646 292,646 294,640 " stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-263199">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1909.000000 63.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42890" ObjectName="SW-CX_WDGF.CX_WDGF_368BK"/>
     <cge:Meas_Ref ObjectId="263199"/>
    <cge:TPSR_Ref TObjectID="42890"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-263187">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1984.000000 -139.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48121" ObjectName="SW-CX_WDGF.CX_WDGF_301BK"/>
     <cge:Meas_Ref ObjectId="263187"/>
    <cge:TPSR_Ref TObjectID="48121"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-263180">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1984.000000 -514.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48120" ObjectName="SW-CX_WDGF.CX_WDGF_201BK"/>
     <cge:Meas_Ref ObjectId="263180"/>
    <cge:TPSR_Ref TObjectID="48120"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309288">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2050.000000 -777.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48084" ObjectName="SW-CX_WDGF.CX_WDGF_231BK"/>
     <cge:Meas_Ref ObjectId="309288"/>
    <cge:TPSR_Ref TObjectID="48084"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309312">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2055.000000 60.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48104" ObjectName="SW-CX_WDGF.CX_WDGF_367BK"/>
     <cge:Meas_Ref ObjectId="309312"/>
    <cge:TPSR_Ref TObjectID="48104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309302">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2340.000000 65.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48096" ObjectName="SW-CX_WDGF.CX_WDGF_365BK"/>
     <cge:Meas_Ref ObjectId="309302"/>
    <cge:TPSR_Ref TObjectID="48096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309322">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1630.000000 64.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48112" ObjectName="SW-CX_WDGF.CX_WDGF_370BK"/>
     <cge:Meas_Ref ObjectId="309322"/>
    <cge:TPSR_Ref TObjectID="48112"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309317">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1770.000000 61.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48108" ObjectName="SW-CX_WDGF.CX_WDGF_369BK"/>
     <cge:Meas_Ref ObjectId="309317"/>
    <cge:TPSR_Ref TObjectID="48108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309307">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2201.000000 67.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48100" ObjectName="SW-CX_WDGF.CX_WDGF_366BK"/>
     <cge:Meas_Ref ObjectId="309307"/>
    <cge:TPSR_Ref TObjectID="48100"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309297">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2477.000000 70.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48092" ObjectName="SW-CX_WDGF.CX_WDGF_364BK"/>
     <cge:Meas_Ref ObjectId="309297"/>
    <cge:TPSR_Ref TObjectID="48092"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309292">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2621.000000 68.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48088" ObjectName="SW-CX_WDGF.CX_WDGF_363BK"/>
     <cge:Meas_Ref ObjectId="309292"/>
    <cge:TPSR_Ref TObjectID="48088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-263194">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2766.000000 67.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42886" ObjectName="SW-CX_WDGF.CX_WDGF_362BK"/>
     <cge:Meas_Ref ObjectId="263194"/>
    <cge:TPSR_Ref TObjectID="42886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-263189">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2904.000000 66.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42882" ObjectName="SW-CX_WDGF.CX_WDGF_361BK"/>
     <cge:Meas_Ref ObjectId="263189"/>
    <cge:TPSR_Ref TObjectID="42882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316874">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 538.000000 60.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48868" ObjectName="SW-CX_WDGF.CX_WDGF_388BK"/>
     <cge:Meas_Ref ObjectId="316874"/>
    <cge:TPSR_Ref TObjectID="48868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316879">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 644.000000 57.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48872" ObjectName="SW-CX_WDGF.CX_WDGF_387BK"/>
     <cge:Meas_Ref ObjectId="316879"/>
    <cge:TPSR_Ref TObjectID="48872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316889">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 866.000000 62.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48880" ObjectName="SW-CX_WDGF.CX_WDGF_385BK"/>
     <cge:Meas_Ref ObjectId="316889"/>
    <cge:TPSR_Ref TObjectID="48880"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 60.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316884">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 749.000000 64.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48876" ObjectName="SW-CX_WDGF.CX_WDGF_386BK"/>
     <cge:Meas_Ref ObjectId="316884"/>
    <cge:TPSR_Ref TObjectID="48876"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316894">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 989.000000 65.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48884" ObjectName="SW-CX_WDGF.CX_WDGF_384BK"/>
     <cge:Meas_Ref ObjectId="316894"/>
    <cge:TPSR_Ref TObjectID="48884"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316899">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1110.000000 62.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48888" ObjectName="SW-CX_WDGF.CX_WDGF_383BK"/>
     <cge:Meas_Ref ObjectId="316899"/>
    <cge:TPSR_Ref TObjectID="48888"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316920">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1210.000000 63.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48907" ObjectName="SW-CX_WDGF.CX_WDGF_382BK"/>
     <cge:Meas_Ref ObjectId="316920"/>
    <cge:TPSR_Ref TObjectID="48907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316904">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1327.000000 62.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48892" ObjectName="SW-CX_WDGF.CX_WDGF_381BK"/>
     <cge:Meas_Ref ObjectId="316904"/>
    <cge:TPSR_Ref TObjectID="48892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1045.000000 -786.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316866">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1167.000000 -135.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48860" ObjectName="SW-CX_WDGF.CX_WDGF_302BK"/>
     <cge:Meas_Ref ObjectId="316866"/>
    <cge:TPSR_Ref TObjectID="48860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316859">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1167.000000 -510.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48853" ObjectName="SW-CX_WDGF.CX_WDGF_202BK"/>
     <cge:Meas_Ref ObjectId="316859"/>
    <cge:TPSR_Ref TObjectID="48853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316909">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1423.000000 63.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48896" ObjectName="SW-CX_WDGF.CX_WDGF_312BK"/>
     <cge:Meas_Ref ObjectId="316909"/>
    <cge:TPSR_Ref TObjectID="48896"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316869">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 421.000000 62.376471)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48864" ObjectName="SW-CX_WDGF.CX_WDGF_389BK"/>
     <cge:Meas_Ref ObjectId="316869"/>
    <cge:TPSR_Ref TObjectID="48864"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316914">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 305.867233 71.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48901" ObjectName="SW-CX_WDGF.CX_WDGF_390BK"/>
     <cge:Meas_Ref ObjectId="316914"/>
    <cge:TPSR_Ref TObjectID="48901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 321.500000 477.500000)" xlink:href="#breaker2:shape16_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_WDGF.CX_WDGF_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="47024"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1923.000000 -288.000000)" xlink:href="#transformer:shape31_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="47026"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1923.000000 -288.000000)" xlink:href="#transformer:shape31_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="47028"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1923.000000 -288.000000)" xlink:href="#transformer:shape31-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="48122" ObjectName="TF-CX_WDGF.CX_WDGF_1T"/>
    <cge:TPSR_Ref TObjectID="48122"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_WDGF.CX_WDGF_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="48245"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1106.000000 -284.000000)" xlink:href="#transformer:shape31_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="48247"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1106.000000 -284.000000)" xlink:href="#transformer:shape31_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="48249"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1106.000000 -284.000000)" xlink:href="#transformer:shape31-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="48912" ObjectName="TF-CX_WDGF.CX_WDGF_2T"/>
    <cge:TPSR_Ref TObjectID="48912"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_WDGF.CX_WDGF_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1516,-23 2987,-23 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="42880" ObjectName="BS-CX_WDGF.CX_WDGF_3IM"/>
    <cge:TPSR_Ref TObjectID="42880"/></metadata>
   <polyline fill="none" opacity="0" points="1516,-23 2987,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_WDGF.CX_WDGF_2IM">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="850,-685 2488,-685 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48119" ObjectName="BS-CX_WDGF.CX_WDGF_2IM"/>
    <cge:TPSR_Ref TObjectID="48119"/></metadata>
   <polyline fill="none" opacity="0" points="850,-685 2488,-685 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_WDGF.CX_WDGF_3ⅡM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-44,-26 1462,-26 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48911" ObjectName="BS-CX_WDGF.CX_WDGF_3ⅡM"/>
    <cge:TPSR_Ref TObjectID="48911"/></metadata>
   <polyline fill="none" opacity="0" points="-44,-26 1462,-26 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2054.000000 -1129.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2345.000000 239.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1913.000000 237.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2060.000000 234.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2206.000000 241.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2482.000000 244.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2626.000000 242.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2771.000000 241.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2909.000000 240.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 871.000000 236.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 542.000000 234.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 649.000000 231.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 754.000000 238.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 994.000000 239.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1115.000000 236.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1215.000000 237.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1049.000000 -1138.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 425.000000 236.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2a7a210" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2055.500000 -192.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a7ba50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2114.000000 -419.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a7c4e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2141.000000 -417.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cd8e60" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1238.500000 -188.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cda6a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1297.000000 -415.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cdb130" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1324.000000 -413.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d193b0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 385.740650 188.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2a89450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1918,55 1918,83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42890@0" ObjectIDZND0="42892@1" Pin0InfoVect0LinkObjId="SW-263200_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263199_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1918,55 1918,83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a5d220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1993,-193 1993,-175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42895@1" ObjectIDZND0="48121@1" Pin0InfoVect0LinkObjId="SW-263187_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263188_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1993,-193 1993,-175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a5d410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1993,-148 1993,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48121@0" ObjectIDZND0="48147@1" Pin0InfoVect0LinkObjId="SW-263188_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263187_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1993,-148 1993,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ae0440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1959,-234 1993,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" ObjectIDND0="g_2b1dee0@0" ObjectIDZND0="48122@x" ObjectIDZND1="42895@x" Pin0InfoVect0LinkObjId="g_2ab0e50_0" Pin0InfoVect1LinkObjId="SW-263188_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b1dee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1959,-234 1993,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ae0c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1993,-293 1993,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="48122@1" ObjectIDZND0="g_2b1dee0@0" ObjectIDZND1="42895@x" Pin0InfoVect0LinkObjId="g_2b1dee0_0" Pin0InfoVect1LinkObjId="SW-263188_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ae0440_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1993,-293 1993,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ae0df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1993,-234 1993,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" ObjectIDND0="g_2b1dee0@0" ObjectIDND1="48122@x" ObjectIDZND0="42895@0" Pin0InfoVect0LinkObjId="SW-263188_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b1dee0_0" Pin1InfoVect1LinkObjId="g_2ae0440_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1993,-234 1993,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ab0e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1971,-404 1993,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="48145@0" ObjectIDZND0="48122@x" ObjectIDZND1="48144@x" ObjectIDZND2="48142@x" Pin0InfoVect0LinkObjId="g_2ae0440_0" Pin0InfoVect1LinkObjId="SW-263184_0" Pin0InfoVect2LinkObjId="SW-263182_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263185_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1971,-404 1993,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b32070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1993,-377 1993,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="48122@2" ObjectIDZND0="48145@x" ObjectIDZND1="48144@x" ObjectIDZND2="48142@x" Pin0InfoVect0LinkObjId="SW-263185_0" Pin0InfoVect1LinkObjId="SW-263184_0" Pin0InfoVect2LinkObjId="SW-263182_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ae0440_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1993,-377 1993,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29f8ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2022,-429 1993,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer" EndDevType2="switch" ObjectIDND0="48144@0" ObjectIDZND0="48145@x" ObjectIDZND1="48122@x" ObjectIDZND2="48142@x" Pin0InfoVect0LinkObjId="SW-263185_0" Pin0InfoVect1LinkObjId="g_2ae0440_0" Pin0InfoVect2LinkObjId="SW-263182_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263184_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2022,-429 1993,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29f9710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1993,-404 1993,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="48145@x" ObjectIDND1="48122@x" ObjectIDZND0="48144@x" ObjectIDZND1="48142@x" Pin0InfoVect0LinkObjId="SW-263184_0" Pin0InfoVect1LinkObjId="SW-263182_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-263185_0" Pin1InfoVect1LinkObjId="g_2ae0440_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1993,-404 1993,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29f9900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1993,-429 1993,-464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="transformer" EndDevType0="switch" ObjectIDND0="48144@x" ObjectIDND1="48145@x" ObjectIDND2="48122@x" ObjectIDZND0="48142@0" Pin0InfoVect0LinkObjId="SW-263182_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-263184_0" Pin1InfoVect1LinkObjId="SW-263185_0" Pin1InfoVect2LinkObjId="g_2ae0440_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1993,-429 1993,-464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29f9af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1993,-500 1993,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48142@1" ObjectIDZND0="48120@0" Pin0InfoVect0LinkObjId="SW-263180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263182_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1993,-500 1993,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b18860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2019,-577 1993,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="48143@0" ObjectIDZND0="48120@x" ObjectIDZND1="48141@x" Pin0InfoVect0LinkObjId="SW-263180_0" Pin0InfoVect1LinkObjId="SW-263181_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263183_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2019,-577 1993,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b190b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1993,-550 1993,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="48120@1" ObjectIDZND0="48143@x" ObjectIDZND1="48141@x" Pin0InfoVect0LinkObjId="SW-263183_0" Pin0InfoVect1LinkObjId="SW-263181_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263180_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1993,-550 1993,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b192a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1993,-577 1993,-610 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="48143@x" ObjectIDND1="48120@x" ObjectIDZND0="48141@0" Pin0InfoVect0LinkObjId="SW-263181_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-263183_0" Pin1InfoVect1LinkObjId="SW-263180_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1993,-577 1993,-610 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b194d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1993,-646 1993,-685 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48141@1" ObjectIDZND0="48119@0" Pin0InfoVect0LinkObjId="g_2aed1d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263181_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1993,-646 1993,-685 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b1afc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2059,-685 2059,-706 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="48119@0" ObjectIDZND0="48137@0" Pin0InfoVect0LinkObjId="SW-263176_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b194d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2059,-685 2059,-706 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2aa1d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2080,-763 2059,-763 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="48148@0" ObjectIDZND0="48137@x" ObjectIDZND1="48084@x" Pin0InfoVect0LinkObjId="SW-263176_0" Pin0InfoVect1LinkObjId="SW-309288_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309289_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2080,-763 2059,-763 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b2adf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2059,-742 2059,-763 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="48137@1" ObjectIDZND0="48148@x" ObjectIDZND1="48084@x" Pin0InfoVect0LinkObjId="SW-309289_0" Pin0InfoVect1LinkObjId="SW-309288_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263176_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2059,-742 2059,-763 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b2cd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2059,-763 2059,-786 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="48148@x" ObjectIDND1="48137@x" ObjectIDZND0="48084@0" Pin0InfoVect0LinkObjId="SW-309288_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-309289_0" Pin1InfoVect1LinkObjId="SW-263176_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2059,-763 2059,-786 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a67040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2059,-813 2059,-838 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48084@1" ObjectIDZND0="48086@0" Pin0InfoVect0LinkObjId="SW-263175_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309288_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2059,-813 2059,-838 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b1e870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2081,-948 2059,-948 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="48087@0" ObjectIDZND0="48085@x" ObjectIDZND1="48086@x" ObjectIDZND2="g_2b20080@0" Pin0InfoVect0LinkObjId="SW-263174_0" Pin0InfoVect1LinkObjId="SW-263175_0" Pin0InfoVect2LinkObjId="g_2b20080_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2081,-948 2059,-948 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b1f1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2081,-896 2059,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="48085@0" ObjectIDZND0="48086@x" ObjectIDZND1="48087@x" ObjectIDZND2="g_2b20080@0" Pin0InfoVect0LinkObjId="SW-263175_0" Pin0InfoVect1LinkObjId="SW-309290_0" Pin0InfoVect2LinkObjId="g_2b20080_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263174_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2081,-896 2059,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b1fc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2059,-874 2059,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="48086@1" ObjectIDZND0="48085@x" ObjectIDZND1="48087@x" ObjectIDZND2="g_2b20080@0" Pin0InfoVect0LinkObjId="SW-263174_0" Pin0InfoVect1LinkObjId="SW-309290_0" Pin0InfoVect2LinkObjId="g_2b20080_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263175_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2059,-874 2059,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b1fe60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2059,-896 2059,-948 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="48085@x" ObjectIDND1="48086@x" ObjectIDZND0="48087@x" ObjectIDZND1="g_2b20080@0" Pin0InfoVect0LinkObjId="SW-309290_0" Pin0InfoVect1LinkObjId="g_2b20080_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-263174_0" Pin1InfoVect1LinkObjId="SW-263175_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2059,-896 2059,-948 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b20540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2059,-948 2059,-989 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="48087@x" ObjectIDND1="48085@x" ObjectIDND2="48086@x" ObjectIDZND0="g_2b20080@0" Pin0InfoVect0LinkObjId="g_2b20080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-309290_0" Pin1InfoVect1LinkObjId="SW-263174_0" Pin1InfoVect2LinkObjId="SW-263175_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2059,-948 2059,-989 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2acf8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2034,-1070 2059,-1070 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="g_2b20760@0" ObjectIDZND0="g_2b20080@0" ObjectIDZND1="g_2ad04f0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2b20080_0" Pin0InfoVect1LinkObjId="g_2ad04f0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b20760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2034,-1070 2059,-1070 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ad02d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2059,-1046 2059,-1070 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="g_2b20080@1" ObjectIDZND0="g_2b20760@0" ObjectIDZND1="g_2ad04f0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2b20760_0" Pin0InfoVect1LinkObjId="g_2ad04f0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b20080_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2059,-1046 2059,-1070 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ad10a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2085,-1095 2059,-1095 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="g_2ad04f0@0" ObjectIDZND0="g_2b20760@0" ObjectIDZND1="g_2b20080@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2b20760_0" Pin0InfoVect1LinkObjId="g_2b20080_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ad04f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2085,-1095 2059,-1095 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ad1b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2059,-1070 2059,-1095 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_2b20760@0" ObjectIDND1="g_2b20080@0" ObjectIDZND0="g_2ad04f0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2ad04f0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b20760_0" Pin1InfoVect1LinkObjId="g_2b20080_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2059,-1070 2059,-1095 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ad1d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2059,-1095 2059,-1134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="g_2ad04f0@0" ObjectIDND1="g_2b20760@0" ObjectIDND2="g_2b20080@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2ad04f0_0" Pin1InfoVect1LinkObjId="g_2b20760_0" Pin1InfoVect2LinkObjId="g_2b20080_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2059,-1095 2059,-1134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2aed1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1641,-727 1667,-727 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="48140@0" ObjectIDZND0="48119@0" ObjectIDZND1="48138@x" Pin0InfoVect0LinkObjId="g_2b194d0_0" Pin0InfoVect1LinkObjId="SW-263177_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263179_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1641,-727 1667,-727 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b677a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,-685 1667,-727 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="48119@0" ObjectIDZND0="48140@x" ObjectIDZND1="48138@x" Pin0InfoVect0LinkObjId="SW-263179_0" Pin0InfoVect1LinkObjId="SW-263177_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b194d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1667,-685 1667,-727 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a98b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,-727 1667,-751 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="48140@x" ObjectIDND1="48119@0" ObjectIDZND0="48138@0" Pin0InfoVect0LinkObjId="SW-263177_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-263179_0" Pin1InfoVect1LinkObjId="g_2b194d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1667,-727 1667,-751 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a98d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1687,-809 1667,-809 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="48139@0" ObjectIDZND0="48138@x" ObjectIDZND1="g_2a99a60@0" ObjectIDZND2="g_2a9ada0@0" Pin0InfoVect0LinkObjId="SW-263177_0" Pin0InfoVect1LinkObjId="g_2a99a60_0" Pin0InfoVect2LinkObjId="g_2a9ada0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263178_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1687,-809 1667,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a99800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,-787 1667,-809 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="48138@1" ObjectIDZND0="48139@x" ObjectIDZND1="g_2a99a60@0" ObjectIDZND2="g_2a9ada0@0" Pin0InfoVect0LinkObjId="SW-263178_0" Pin0InfoVect1LinkObjId="g_2a99a60_0" Pin0InfoVect2LinkObjId="g_2a9ada0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263177_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1667,-787 1667,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a99e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1694,-864 1667,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_2a99a60@0" ObjectIDZND0="48139@x" ObjectIDZND1="48138@x" ObjectIDZND2="g_2a9ada0@0" Pin0InfoVect0LinkObjId="SW-263178_0" Pin0InfoVect1LinkObjId="SW-263177_0" Pin0InfoVect2LinkObjId="g_2a9ada0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a99a60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1694,-864 1667,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a9a8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,-809 1667,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="48139@x" ObjectIDND1="48138@x" ObjectIDZND0="g_2a99a60@0" ObjectIDZND1="g_2a9ada0@0" Pin0InfoVect0LinkObjId="g_2a99a60_0" Pin0InfoVect1LinkObjId="g_2a9ada0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-263178_0" Pin1InfoVect1LinkObjId="SW-263177_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1667,-809 1667,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a9ab40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,-864 1667,-898 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_2a99a60@0" ObjectIDND1="48139@x" ObjectIDND2="48138@x" ObjectIDZND0="g_2a9ada0@0" Pin0InfoVect0LinkObjId="g_2a9ada0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a99a60_0" Pin1InfoVect1LinkObjId="SW-263178_0" Pin1InfoVect2LinkObjId="SW-263177_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1667,-864 1667,-898 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a9b210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1741,-241 1741,-200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2b0aa20@0" ObjectIDZND0="g_2787b40@0" Pin0InfoVect0LinkObjId="g_2787b40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b0aa20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1741,-241 1741,-200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a9b890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1698,-142 1741,-142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2b70760@0" ObjectIDZND0="g_2787b40@0" ObjectIDZND1="42894@x" Pin0InfoVect0LinkObjId="g_2787b40_0" Pin0InfoVect1LinkObjId="SW-263202_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b70760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1698,-142 1741,-142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0a560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1741,-169 1741,-142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2787b40@1" ObjectIDZND0="g_2b70760@0" ObjectIDZND1="42894@x" Pin0InfoVect0LinkObjId="g_2b70760_0" Pin0InfoVect1LinkObjId="SW-263202_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2787b40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1741,-169 1741,-142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0a7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1741,-142 1741,-112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b70760@0" ObjectIDND1="g_2787b40@0" ObjectIDZND0="42894@1" Pin0InfoVect0LinkObjId="SW-263202_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b70760_0" Pin1InfoVect1LinkObjId="g_2787b40_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1741,-142 1741,-112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0cc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1918,100 1918,121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="42892@0" ObjectIDZND0="g_27ff990@0" ObjectIDZND1="42893@x" ObjectIDZND2="g_2a2c1c0@0" Pin0InfoVect0LinkObjId="g_27ff990_0" Pin0InfoVect1LinkObjId="SW-263201_0" Pin0InfoVect2LinkObjId="g_2a2c1c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1918,100 1918,121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a2baa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1961,142 1961,121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_27ff990@0" ObjectIDZND0="42893@x" ObjectIDZND1="42892@x" ObjectIDZND2="g_2a2c1c0@0" Pin0InfoVect0LinkObjId="SW-263201_0" Pin0InfoVect1LinkObjId="SW-263200_0" Pin0InfoVect2LinkObjId="g_2a2c1c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27ff990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1961,142 1961,121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a2bd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1997,136 1997,121 1961,121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="42893@0" ObjectIDZND0="g_27ff990@0" ObjectIDZND1="42892@x" ObjectIDZND2="g_2a2c1c0@0" Pin0InfoVect0LinkObjId="g_27ff990_0" Pin0InfoVect1LinkObjId="SW-263200_0" Pin0InfoVect2LinkObjId="g_2a2c1c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263201_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1997,136 1997,121 1961,121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a2bf60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1961,121 1918,121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_27ff990@0" ObjectIDND1="42893@x" ObjectIDZND0="42892@x" ObjectIDZND1="g_2a2c1c0@0" Pin0InfoVect0LinkObjId="SW-263200_0" Pin0InfoVect1LinkObjId="g_2a2c1c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_27ff990_0" Pin1InfoVect1LinkObjId="SW-263201_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1961,121 1918,121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a2ca80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1918,121 1918,147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="42892@x" ObjectIDND1="g_27ff990@0" ObjectIDND2="42893@x" ObjectIDZND0="g_2a2c1c0@1" Pin0InfoVect0LinkObjId="g_2a2c1c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-263200_0" Pin1InfoVect1LinkObjId="g_27ff990_0" Pin1InfoVect2LinkObjId="SW-263201_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1918,121 1918,147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a2cce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1918,185 1918,216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2a2c1c0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a2c1c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1918,185 1918,216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a2cf40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1918,-6 1918,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42891@0" ObjectIDZND0="42880@0" Pin0InfoVect0LinkObjId="g_2b827d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1918,-6 1918,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2adbe10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2064,52 2064,80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48104@0" ObjectIDZND0="48106@1" Pin0InfoVect0LinkObjId="SW-309313_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309312_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2064,52 2064,80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2adc680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2064,8 2064,25 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48105@1" ObjectIDZND0="48104@1" Pin0InfoVect0LinkObjId="SW-309312_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309313_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2064,8 2064,25 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b16e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2065,94 2065,115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="48106@0" ObjectIDZND0="g_2a2d1a0@0" ObjectIDZND1="48107@x" ObjectIDZND2="g_2b177b0@0" Pin0InfoVect0LinkObjId="g_2a2d1a0_0" Pin0InfoVect1LinkObjId="SW-309314_0" Pin0InfoVect2LinkObjId="g_2b177b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309313_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2065,94 2065,115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b17090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2107,139 2107,114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2a2d1a0@0" ObjectIDZND0="48107@x" ObjectIDZND1="48106@x" ObjectIDZND2="g_2b177b0@0" Pin0InfoVect0LinkObjId="SW-309314_0" Pin0InfoVect1LinkObjId="SW-309313_0" Pin0InfoVect2LinkObjId="g_2b177b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a2d1a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2107,139 2107,114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b172f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2143,130 2143,115 2107,115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="48107@0" ObjectIDZND0="g_2a2d1a0@0" ObjectIDZND1="48106@x" ObjectIDZND2="g_2b177b0@0" Pin0InfoVect0LinkObjId="g_2a2d1a0_0" Pin0InfoVect1LinkObjId="SW-309313_0" Pin0InfoVect2LinkObjId="g_2b177b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309314_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2143,130 2143,115 2107,115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b17550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2107,115 2065,115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2a2d1a0@0" ObjectIDND1="48107@x" ObjectIDZND0="48106@x" ObjectIDZND1="g_2b177b0@0" Pin0InfoVect0LinkObjId="SW-309313_0" Pin0InfoVect1LinkObjId="g_2b177b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a2d1a0_0" Pin1InfoVect1LinkObjId="SW-309314_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2107,115 2065,115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b82310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2065,115 2065,144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="48106@x" ObjectIDND1="g_2a2d1a0@0" ObjectIDND2="48107@x" ObjectIDZND0="g_2b177b0@1" Pin0InfoVect0LinkObjId="g_2b177b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-309313_0" Pin1InfoVect1LinkObjId="g_2a2d1a0_0" Pin1InfoVect2LinkObjId="SW-309314_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2065,115 2065,144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b82570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2065,182 2065,213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2b177b0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b177b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2065,182 2065,213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b827d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2064,-9 2064,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48105@0" ObjectIDZND0="42880@0" Pin0InfoVect0LinkObjId="g_2a2cf40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309313_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2064,-9 2064,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aace20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2349,57 2349,85 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48096@0" ObjectIDZND0="48098@1" Pin0InfoVect0LinkObjId="SW-309303_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309302_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2349,57 2349,85 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aad850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2349,13 2349,30 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48097@1" ObjectIDZND0="48096@1" Pin0InfoVect0LinkObjId="SW-309302_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309303_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2349,13 2349,30 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2afff20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2350,99 2350,120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="48098@0" ObjectIDZND0="g_2b82a30@0" ObjectIDZND1="48099@x" ObjectIDZND2="g_2b008a0@0" Pin0InfoVect0LinkObjId="g_2b82a30_0" Pin0InfoVect1LinkObjId="SW-309304_0" Pin0InfoVect2LinkObjId="g_2b008a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309303_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2350,99 2350,120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b00180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2392,144 2392,119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2b82a30@0" ObjectIDZND0="48099@x" ObjectIDZND1="48098@x" ObjectIDZND2="g_2b008a0@0" Pin0InfoVect0LinkObjId="SW-309304_0" Pin0InfoVect1LinkObjId="SW-309303_0" Pin0InfoVect2LinkObjId="g_2b008a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b82a30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2392,144 2392,119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b003e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2428,135 2428,120 2392,120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="48099@0" ObjectIDZND0="g_2b82a30@0" ObjectIDZND1="48098@x" ObjectIDZND2="g_2b008a0@0" Pin0InfoVect0LinkObjId="g_2b82a30_0" Pin0InfoVect1LinkObjId="SW-309303_0" Pin0InfoVect2LinkObjId="g_2b008a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309304_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2428,135 2428,120 2392,120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b00640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2392,120 2350,120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2b82a30@0" ObjectIDND1="48099@x" ObjectIDZND0="48098@x" ObjectIDZND1="g_2b008a0@0" Pin0InfoVect0LinkObjId="SW-309303_0" Pin0InfoVect1LinkObjId="g_2b008a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b82a30_0" Pin1InfoVect1LinkObjId="SW-309304_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2392,120 2350,120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b012c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2350,120 2350,149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="48098@x" ObjectIDND1="g_2b82a30@0" ObjectIDND2="48099@x" ObjectIDZND0="g_2b008a0@1" Pin0InfoVect0LinkObjId="g_2b008a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-309303_0" Pin1InfoVect1LinkObjId="g_2b82a30_0" Pin1InfoVect2LinkObjId="SW-309304_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2350,120 2350,149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b01520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2350,187 2350,218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2b008a0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b008a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2350,187 2350,218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b01780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2349,-4 2349,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48097@0" ObjectIDZND0="42880@0" Pin0InfoVect0LinkObjId="g_2a2cf40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309303_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2349,-4 2349,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a94b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1639,56 1639,84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48112@0" ObjectIDZND0="48114@1" Pin0InfoVect0LinkObjId="SW-309323_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309322_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1639,56 1639,84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a953d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1639,12 1639,29 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48113@1" ObjectIDZND0="48112@1" Pin0InfoVect0LinkObjId="SW-309322_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309323_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1639,12 1639,29 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a4f8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1640,98 1640,119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="48114@0" ObjectIDZND0="g_2b025f0@0" ObjectIDZND1="48115@x" ObjectIDZND2="g_2a50250@0" Pin0InfoVect0LinkObjId="g_2b025f0_0" Pin0InfoVect1LinkObjId="SW-309324_0" Pin0InfoVect2LinkObjId="g_2a50250_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309323_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1640,98 1640,119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a4fb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1682,143 1682,118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2b025f0@0" ObjectIDZND0="48115@x" ObjectIDZND1="48114@x" ObjectIDZND2="g_2a50250@0" Pin0InfoVect0LinkObjId="SW-309324_0" Pin0InfoVect1LinkObjId="SW-309323_0" Pin0InfoVect2LinkObjId="g_2a50250_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b025f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1682,143 1682,118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a4fd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1718,134 1718,119 1682,119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="48115@0" ObjectIDZND0="g_2b025f0@0" ObjectIDZND1="48114@x" ObjectIDZND2="g_2a50250@0" Pin0InfoVect0LinkObjId="g_2b025f0_0" Pin0InfoVect1LinkObjId="SW-309323_0" Pin0InfoVect2LinkObjId="g_2a50250_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309324_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1718,134 1718,119 1682,119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a4fff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1682,119 1640,119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2b025f0@0" ObjectIDND1="48115@x" ObjectIDZND0="48114@x" ObjectIDZND1="g_2a50250@0" Pin0InfoVect0LinkObjId="SW-309323_0" Pin0InfoVect1LinkObjId="g_2a50250_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b025f0_0" Pin1InfoVect1LinkObjId="SW-309324_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1682,119 1640,119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a50c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1640,119 1640,148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="48114@x" ObjectIDND1="g_2b025f0@0" ObjectIDND2="48115@x" ObjectIDZND0="g_2a50250@1" Pin0InfoVect0LinkObjId="g_2a50250_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-309323_0" Pin1InfoVect1LinkObjId="g_2b025f0_0" Pin1InfoVect2LinkObjId="SW-309324_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1640,119 1640,148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a50ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1640,186 1640,217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2a50250@0" ObjectIDZND0="g_2a51130@0" Pin0InfoVect0LinkObjId="g_2a51130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a50250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1640,186 1640,217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a08570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1639,-5 1639,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48113@0" ObjectIDZND0="42880@0" Pin0InfoVect0LinkObjId="g_2a2cf40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309323_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1639,-5 1639,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a32ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1779,53 1779,81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48108@0" ObjectIDZND0="48110@1" Pin0InfoVect0LinkObjId="SW-309318_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309317_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1779,53 1779,81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a33340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1779,9 1779,26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48109@1" ObjectIDZND0="48108@1" Pin0InfoVect0LinkObjId="SW-309317_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309318_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1779,9 1779,26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a395a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1780,95 1780,116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="48110@0" ObjectIDZND0="g_2a08760@0" ObjectIDZND1="48111@x" ObjectIDZND2="g_2a39cc0@0" Pin0InfoVect0LinkObjId="g_2a08760_0" Pin0InfoVect1LinkObjId="SW-309319_0" Pin0InfoVect2LinkObjId="g_2a39cc0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309318_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1780,95 1780,116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a39800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1822,140 1822,115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2a08760@0" ObjectIDZND0="48110@x" ObjectIDZND1="g_2a39cc0@0" ObjectIDZND2="48111@x" Pin0InfoVect0LinkObjId="SW-309318_0" Pin0InfoVect1LinkObjId="g_2a39cc0_0" Pin0InfoVect2LinkObjId="SW-309319_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a08760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1822,140 1822,115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a39a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1822,116 1780,116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2a08760@0" ObjectIDND1="48111@x" ObjectIDZND0="48110@x" ObjectIDZND1="g_2a39cc0@0" Pin0InfoVect0LinkObjId="SW-309318_0" Pin0InfoVect1LinkObjId="g_2a39cc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a08760_0" Pin1InfoVect1LinkObjId="SW-309319_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1822,116 1780,116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a67c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1780,116 1780,145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="48110@x" ObjectIDND1="g_2a08760@0" ObjectIDND2="48111@x" ObjectIDZND0="g_2a39cc0@1" Pin0InfoVect0LinkObjId="g_2a39cc0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-309318_0" Pin1InfoVect1LinkObjId="g_2a08760_0" Pin1InfoVect2LinkObjId="SW-309319_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1780,116 1780,145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a67ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1780,183 1780,214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2a39cc0@0" ObjectIDZND0="g_2a68130@0" Pin0InfoVect0LinkObjId="g_2a68130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a39cc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1780,183 1780,214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a68f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1779,-8 1779,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48109@0" ObjectIDZND0="42880@0" Pin0InfoVect0LinkObjId="g_2a2cf40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309318_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1779,-8 1779,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a702c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2210,59 2210,87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48100@0" ObjectIDZND0="48102@1" Pin0InfoVect0LinkObjId="SW-309308_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309307_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2210,59 2210,87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a70ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2210,15 2210,32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48101@1" ObjectIDZND0="48100@1" Pin0InfoVect0LinkObjId="SW-309307_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309308_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2210,15 2210,32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29ef080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2211,101 2211,122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="48102@0" ObjectIDZND0="g_2a6a6e0@0" ObjectIDZND1="48103@x" ObjectIDZND2="g_29efa00@0" Pin0InfoVect0LinkObjId="g_2a6a6e0_0" Pin0InfoVect1LinkObjId="SW-309309_0" Pin0InfoVect2LinkObjId="g_29efa00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309308_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2211,101 2211,122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29ef2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2253,146 2253,121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2a6a6e0@0" ObjectIDZND0="48103@x" ObjectIDZND1="48102@x" ObjectIDZND2="g_29efa00@0" Pin0InfoVect0LinkObjId="SW-309309_0" Pin0InfoVect1LinkObjId="SW-309308_0" Pin0InfoVect2LinkObjId="g_29efa00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a6a6e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2253,146 2253,121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29ef540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2289,137 2289,122 2253,122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="48103@0" ObjectIDZND0="g_2a6a6e0@0" ObjectIDZND1="48102@x" ObjectIDZND2="g_29efa00@0" Pin0InfoVect0LinkObjId="g_2a6a6e0_0" Pin0InfoVect1LinkObjId="SW-309308_0" Pin0InfoVect2LinkObjId="g_29efa00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309309_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2289,137 2289,122 2253,122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29ef7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2253,122 2211,122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2a6a6e0@0" ObjectIDND1="48103@x" ObjectIDZND0="48102@x" ObjectIDZND1="g_29efa00@0" Pin0InfoVect0LinkObjId="SW-309308_0" Pin0InfoVect1LinkObjId="g_29efa00_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a6a6e0_0" Pin1InfoVect1LinkObjId="SW-309309_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2253,122 2211,122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f0420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2211,122 2211,151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="48102@x" ObjectIDND1="g_2a6a6e0@0" ObjectIDND2="48103@x" ObjectIDZND0="g_29efa00@1" Pin0InfoVect0LinkObjId="g_29efa00_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-309308_0" Pin1InfoVect1LinkObjId="g_2a6a6e0_0" Pin1InfoVect2LinkObjId="SW-309309_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2211,122 2211,151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f0680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2211,189 2211,220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_29efa00@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29efa00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2211,189 2211,220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f08e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2210,-2 2210,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48101@0" ObjectIDZND0="42880@0" Pin0InfoVect0LinkObjId="g_2a2cf40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309308_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2210,-2 2210,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2af9810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2486,62 2486,90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48092@0" ObjectIDZND0="48094@1" Pin0InfoVect0LinkObjId="SW-309298_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309297_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2486,62 2486,90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2afa0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2486,18 2486,35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48093@1" ObjectIDZND0="48092@1" Pin0InfoVect0LinkObjId="SW-309297_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309298_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2486,18 2486,35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a635d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2487,104 2487,125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="48094@0" ObjectIDZND0="g_29f1750@0" ObjectIDZND1="48095@x" ObjectIDZND2="g_2a63f50@0" Pin0InfoVect0LinkObjId="g_29f1750_0" Pin0InfoVect1LinkObjId="SW-309299_0" Pin0InfoVect2LinkObjId="g_2a63f50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309298_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2487,104 2487,125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a63830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2529,149 2529,124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_29f1750@0" ObjectIDZND0="48095@x" ObjectIDZND1="48094@x" ObjectIDZND2="g_2a63f50@0" Pin0InfoVect0LinkObjId="SW-309299_0" Pin0InfoVect1LinkObjId="SW-309298_0" Pin0InfoVect2LinkObjId="g_2a63f50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29f1750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2529,149 2529,124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a63a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2565,140 2565,125 2529,125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="48095@0" ObjectIDZND0="g_29f1750@0" ObjectIDZND1="48094@x" ObjectIDZND2="g_2a63f50@0" Pin0InfoVect0LinkObjId="g_29f1750_0" Pin0InfoVect1LinkObjId="SW-309298_0" Pin0InfoVect2LinkObjId="g_2a63f50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309299_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2565,140 2565,125 2529,125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a63cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2529,125 2487,125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_29f1750@0" ObjectIDND1="48095@x" ObjectIDZND0="48094@x" ObjectIDZND1="g_2a63f50@0" Pin0InfoVect0LinkObjId="SW-309298_0" Pin0InfoVect1LinkObjId="g_2a63f50_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29f1750_0" Pin1InfoVect1LinkObjId="SW-309299_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2529,125 2487,125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ad5040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2487,125 2487,154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="48094@x" ObjectIDND1="g_29f1750@0" ObjectIDND2="48095@x" ObjectIDZND0="g_2a63f50@1" Pin0InfoVect0LinkObjId="g_2a63f50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-309298_0" Pin1InfoVect1LinkObjId="g_29f1750_0" Pin1InfoVect2LinkObjId="SW-309299_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2487,125 2487,154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ad52a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2487,192 2487,223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2a63f50@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a63f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2487,192 2487,223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ad5500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2486,1 2486,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48093@0" ObjectIDZND0="42880@0" Pin0InfoVect0LinkObjId="g_2a2cf40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309298_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2486,1 2486,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aa2cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2630,60 2630,88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48088@0" ObjectIDZND0="48090@1" Pin0InfoVect0LinkObjId="SW-309293_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309292_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2630,60 2630,88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aa3560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2630,16 2630,33 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48089@1" ObjectIDZND0="48088@1" Pin0InfoVect0LinkObjId="SW-309292_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309293_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2630,16 2630,33 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aa9780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2631,102 2631,123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="48090@0" ObjectIDZND0="g_2ad6370@0" ObjectIDZND1="48091@x" ObjectIDZND2="g_29f2390@0" Pin0InfoVect0LinkObjId="g_2ad6370_0" Pin0InfoVect1LinkObjId="SW-309294_0" Pin0InfoVect2LinkObjId="g_29f2390_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309293_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2631,102 2631,123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f1c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2673,147 2673,122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2ad6370@0" ObjectIDZND0="48091@x" ObjectIDZND1="48090@x" ObjectIDZND2="g_29f2390@0" Pin0InfoVect0LinkObjId="SW-309294_0" Pin0InfoVect1LinkObjId="SW-309293_0" Pin0InfoVect2LinkObjId="g_29f2390_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ad6370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2673,147 2673,122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f1ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2709,138 2709,123 2673,123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="48091@0" ObjectIDZND0="g_2ad6370@0" ObjectIDZND1="48090@x" ObjectIDZND2="g_29f2390@0" Pin0InfoVect0LinkObjId="g_2ad6370_0" Pin0InfoVect1LinkObjId="SW-309293_0" Pin0InfoVect2LinkObjId="g_29f2390_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309294_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2709,138 2709,123 2673,123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f2130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2673,123 2631,123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2ad6370@0" ObjectIDND1="48091@x" ObjectIDZND0="48090@x" ObjectIDZND1="g_29f2390@0" Pin0InfoVect0LinkObjId="SW-309293_0" Pin0InfoVect1LinkObjId="g_29f2390_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ad6370_0" Pin1InfoVect1LinkObjId="SW-309294_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2673,123 2631,123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f2db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2631,123 2631,152 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="48090@x" ObjectIDND1="g_2ad6370@0" ObjectIDND2="48091@x" ObjectIDZND0="g_29f2390@1" Pin0InfoVect0LinkObjId="g_29f2390_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-309293_0" Pin1InfoVect1LinkObjId="g_2ad6370_0" Pin1InfoVect2LinkObjId="SW-309294_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2631,123 2631,152 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f3010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2631,190 2631,221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_29f2390@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29f2390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2631,190 2631,221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f3270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2630,-1 2630,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48089@0" ObjectIDZND0="42880@0" Pin0InfoVect0LinkObjId="g_2a2cf40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309293_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2630,-1 2630,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a8dd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2775,59 2775,87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42886@0" ObjectIDZND0="42888@1" Pin0InfoVect0LinkObjId="SW-263195_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263194_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2775,59 2775,87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a8e720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2775,15 2775,32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42887@1" ObjectIDZND0="42886@1" Pin0InfoVect0LinkObjId="SW-263194_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263195_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2775,15 2775,32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29fbe70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2776,101 2776,122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="42888@0" ObjectIDZND0="g_29f40e0@0" ObjectIDZND1="42889@x" ObjectIDZND2="g_29fc7f0@0" Pin0InfoVect0LinkObjId="g_29f40e0_0" Pin0InfoVect1LinkObjId="SW-263196_0" Pin0InfoVect2LinkObjId="g_29fc7f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263195_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2776,101 2776,122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29fc0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2818,146 2818,121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_29f40e0@0" ObjectIDZND0="42889@x" ObjectIDZND1="42888@x" ObjectIDZND2="g_29fc7f0@0" Pin0InfoVect0LinkObjId="SW-263196_0" Pin0InfoVect1LinkObjId="SW-263195_0" Pin0InfoVect2LinkObjId="g_29fc7f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29f40e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2818,146 2818,121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29fc330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2854,137 2854,122 2818,122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="42889@0" ObjectIDZND0="g_29f40e0@0" ObjectIDZND1="42888@x" ObjectIDZND2="g_29fc7f0@0" Pin0InfoVect0LinkObjId="g_29f40e0_0" Pin0InfoVect1LinkObjId="SW-263195_0" Pin0InfoVect2LinkObjId="g_29fc7f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263196_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2854,137 2854,122 2818,122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29fc590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2818,122 2776,122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_29f40e0@0" ObjectIDND1="42889@x" ObjectIDZND0="42888@x" ObjectIDZND1="g_29fc7f0@0" Pin0InfoVect0LinkObjId="SW-263195_0" Pin0InfoVect1LinkObjId="g_29fc7f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29f40e0_0" Pin1InfoVect1LinkObjId="SW-263196_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2818,122 2776,122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29fd210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2776,122 2776,151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="42888@x" ObjectIDND1="g_29f40e0@0" ObjectIDND2="42889@x" ObjectIDZND0="g_29fc7f0@1" Pin0InfoVect0LinkObjId="g_29fc7f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-263195_0" Pin1InfoVect1LinkObjId="g_29f40e0_0" Pin1InfoVect2LinkObjId="SW-263196_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2776,122 2776,151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29fd470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2776,189 2776,220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_29fc7f0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29fc7f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2776,189 2776,220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29fd6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2775,-2 2775,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42887@0" ObjectIDZND0="42880@0" Pin0InfoVect0LinkObjId="g_2a2cf40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263195_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2775,-2 2775,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a3bed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,58 2913,86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42882@0" ObjectIDZND0="42884@1" Pin0InfoVect0LinkObjId="SW-263190_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263189_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,58 2913,86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a3c760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,14 2913,31 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="42883@1" ObjectIDZND0="42882@1" Pin0InfoVect0LinkObjId="SW-263189_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263190_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,14 2913,31 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a42980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2914,100 2914,121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="42884@0" ObjectIDZND0="g_29fe540@0" ObjectIDZND1="42885@x" ObjectIDZND2="g_2a43300@0" Pin0InfoVect0LinkObjId="g_29fe540_0" Pin0InfoVect1LinkObjId="SW-263191_0" Pin0InfoVect2LinkObjId="g_2a43300_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2914,100 2914,121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a42be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2956,145 2956,120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_29fe540@0" ObjectIDZND0="42885@x" ObjectIDZND1="42884@x" ObjectIDZND2="g_2a43300@0" Pin0InfoVect0LinkObjId="SW-263191_0" Pin0InfoVect1LinkObjId="SW-263190_0" Pin0InfoVect2LinkObjId="g_2a43300_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29fe540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2956,145 2956,120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a42e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2992,136 2992,121 2956,121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="42885@0" ObjectIDZND0="g_29fe540@0" ObjectIDZND1="42884@x" ObjectIDZND2="g_2a43300@0" Pin0InfoVect0LinkObjId="g_29fe540_0" Pin0InfoVect1LinkObjId="SW-263190_0" Pin0InfoVect2LinkObjId="g_2a43300_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263191_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2992,136 2992,121 2956,121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a430a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2956,121 2914,121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_29fe540@0" ObjectIDND1="42885@x" ObjectIDZND0="42884@x" ObjectIDZND1="g_2a43300@0" Pin0InfoVect0LinkObjId="SW-263190_0" Pin0InfoVect1LinkObjId="g_2a43300_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29fe540_0" Pin1InfoVect1LinkObjId="SW-263191_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2956,121 2914,121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a43d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2914,121 2914,150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="42884@x" ObjectIDND1="g_29fe540@0" ObjectIDND2="42885@x" ObjectIDZND0="g_2a43300@1" Pin0InfoVect0LinkObjId="g_2a43300_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-263190_0" Pin1InfoVect1LinkObjId="g_29fe540_0" Pin1InfoVect2LinkObjId="SW-263191_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2914,121 2914,150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a43f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2914,188 2914,219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2a43300@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a43300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2914,188 2914,219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a441e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2913,-3 2913,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42883@0" ObjectIDZND0="42880@0" Pin0InfoVect0LinkObjId="g_2a2cf40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2913,-3 2913,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a7cf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2061,-188 2061,-198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_2a7a210@0" ObjectIDZND0="g_2a79660@1" Pin0InfoVect0LinkObjId="g_2a79660_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a7a210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2061,-188 2061,-198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2983310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2061,-239 2061,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="48149@0" ObjectIDZND0="g_2a79660@0" Pin0InfoVect0LinkObjId="g_2a79660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-309739_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2061,-239 2061,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2983570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2147,-413 2147,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_2a79010@1" ObjectIDZND0="g_2a7c4e0@0" Pin0InfoVect0LinkObjId="g_2a7c4e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a79010_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2147,-413 2147,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2984b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2072,-293 2061,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" ObjectIDND0="g_2984040@0" ObjectIDZND0="48122@x" ObjectIDZND1="48149@x" Pin0InfoVect0LinkObjId="g_2ae0440_0" Pin0InfoVect1LinkObjId="SW-309739_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2984040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2072,-293 2061,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29855f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1994,-312 2061,-312 2061,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="48122@x" ObjectIDZND0="g_2984040@0" ObjectIDZND1="48149@x" Pin0InfoVect0LinkObjId="g_2984040_0" Pin0InfoVect1LinkObjId="SW-309739_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ae0440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1994,-312 2061,-312 2061,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2985850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2061,-293 2061,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" ObjectIDND0="g_2984040@0" ObjectIDND1="48122@x" ObjectIDZND0="48149@1" Pin0InfoVect0LinkObjId="SW-309739_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2984040_0" Pin1InfoVect1LinkObjId="g_2ae0440_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2061,-293 2061,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29867e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1928,-330 1892,-330 1892,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" ObjectIDND0="48122@0" ObjectIDZND0="g_2985ab0@0" Pin0InfoVect0LinkObjId="g_2985ab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ae0440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1928,-330 1892,-330 1892,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2986a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2147,-373 2147,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="transformer" ObjectIDND0="g_2a79010@0" ObjectIDZND0="g_2a7aca0@0" ObjectIDZND1="48146@x" ObjectIDZND2="48122@x" Pin0InfoVect0LinkObjId="g_2a7aca0_0" Pin0InfoVect1LinkObjId="SW-263186_0" Pin0InfoVect2LinkObjId="g_2ae0440_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a79010_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2147,-373 2147,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2987530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2147,-356 2178,-356 2178,-372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="transformer" EndDevType0="lightningRod" ObjectIDND0="g_2a79010@0" ObjectIDND1="48146@x" ObjectIDND2="48122@x" ObjectIDZND0="g_2a7aca0@0" Pin0InfoVect0LinkObjId="g_2a7aca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a79010_0" Pin1InfoVect1LinkObjId="SW-263186_0" Pin1InfoVect2LinkObjId="g_2ae0440_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2147,-356 2178,-356 2178,-372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2987790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2120,-376 2120,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="48146@0" ObjectIDZND0="48122@x" ObjectIDZND1="g_2a79010@0" ObjectIDZND2="g_2a7aca0@0" Pin0InfoVect0LinkObjId="g_2ae0440_0" Pin0InfoVect1LinkObjId="g_2a79010_0" Pin0InfoVect2LinkObjId="g_2a7aca0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263186_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2120,-376 2120,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2988280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1993,-356 2120,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="48122@x" ObjectIDZND0="48146@x" ObjectIDZND1="g_2a79010@0" ObjectIDZND2="g_2a7aca0@0" Pin0InfoVect0LinkObjId="SW-263186_0" Pin0InfoVect1LinkObjId="g_2a79010_0" Pin0InfoVect2LinkObjId="g_2a7aca0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ae0440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1993,-356 2120,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29884e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2120,-356 2147,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="48146@x" ObjectIDND1="48122@x" ObjectIDZND0="g_2a79010@0" ObjectIDZND1="g_2a7aca0@0" Pin0InfoVect0LinkObjId="g_2a79010_0" Pin0InfoVect1LinkObjId="g_2a7aca0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-263186_0" Pin1InfoVect1LinkObjId="g_2ae0440_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2120,-356 2147,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2988740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2120,-424 2120,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2a7ba50@0" ObjectIDZND0="48146@1" Pin0InfoVect0LinkObjId="SW-263186_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a7ba50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2120,-424 2120,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ab9cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1918,28 1918,11 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="42890@1" ObjectIDZND0="42891@1" Pin0InfoVect0LinkObjId="SW-263200_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263199_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1918,28 1918,11 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a91f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1822,116 1858,116 1858,131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2a08760@0" ObjectIDND1="48110@x" ObjectIDND2="g_2a39cc0@0" ObjectIDZND0="48111@0" Pin0InfoVect0LinkObjId="SW-309319_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a08760_0" Pin1InfoVect1LinkObjId="SW-309318_0" Pin1InfoVect2LinkObjId="g_2a39cc0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1822,116 1858,116 1858,131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29aefe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="547,52 547,80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48868@0" ObjectIDZND0="48870@1" Pin0InfoVect0LinkObjId="SW-316875_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316874_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="547,52 547,80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29b5dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="547,97 547,118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="48870@0" ObjectIDZND0="g_29b6020@0" ObjectIDZND1="g_29a9450@0" ObjectIDZND2="48871@x" Pin0InfoVect0LinkObjId="g_29b6020_0" Pin0InfoVect1LinkObjId="g_29a9450_0" Pin0InfoVect2LinkObjId="SW-316876_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316875_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="547,97 547,118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a149c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="547,118 547,144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="48870@x" ObjectIDND1="g_29a9450@0" ObjectIDND2="48871@x" ObjectIDZND0="g_29b6020@1" Pin0InfoVect0LinkObjId="g_29b6020_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-316875_0" Pin1InfoVect1LinkObjId="g_29a9450_0" Pin1InfoVect2LinkObjId="SW-316876_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="547,118 547,144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a14c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="547,-9 547,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48869@0" ObjectIDZND0="48911@0" Pin0InfoVect0LinkObjId="g_2a22470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316875_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="547,-9 547,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a1aac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="653,49 653,77 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48872@0" ObjectIDZND0="48874@1" Pin0InfoVect0LinkObjId="SW-316880_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316879_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="653,49 653,77 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a1ad20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="653,5 653,22 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48873@1" ObjectIDZND0="48872@1" Pin0InfoVect0LinkObjId="SW-316879_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316880_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="653,5 653,22 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a21330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="654,91 654,112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="48874@0" ObjectIDZND0="g_2a21590@0" ObjectIDZND1="g_2a14e80@0" ObjectIDZND2="48875@x" Pin0InfoVect0LinkObjId="g_2a21590_0" Pin0InfoVect1LinkObjId="g_2a14e80_0" Pin0InfoVect2LinkObjId="SW-316881_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316880_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="654,91 654,112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a21fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="654,112 654,141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="48874@x" ObjectIDND1="g_2a14e80@0" ObjectIDND2="48875@x" ObjectIDZND0="g_2a21590@1" Pin0InfoVect0LinkObjId="g_2a21590_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-316880_0" Pin1InfoVect1LinkObjId="g_2a14e80_0" Pin1InfoVect2LinkObjId="SW-316881_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="654,112 654,141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a22210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="654,179 654,210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2a21590@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a21590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="654,179 654,210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a22470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="653,-12 653,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48873@0" ObjectIDZND0="48911@0" Pin0InfoVect0LinkObjId="g_2a14c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316880_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="653,-12 653,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a28310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="875,54 875,82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48880@0" ObjectIDZND0="48882@1" Pin0InfoVect0LinkObjId="SW-316890_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316889_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="875,54 875,82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a28570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="875,10 875,27 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48881@1" ObjectIDZND0="48880@1" Pin0InfoVect0LinkObjId="SW-316889_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316890_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="875,10 875,27 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bc3ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="876,96 876,117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="48882@0" ObjectIDZND0="g_2bc4140@0" ObjectIDZND1="g_2a226d0@0" ObjectIDZND2="48883@x" Pin0InfoVect0LinkObjId="g_2bc4140_0" Pin0InfoVect1LinkObjId="g_2a226d0_0" Pin0InfoVect2LinkObjId="SW-316891_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="876,96 876,117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bc4b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="876,117 876,146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="48882@x" ObjectIDND1="g_2a226d0@0" ObjectIDND2="48883@x" ObjectIDZND0="g_2bc4140@1" Pin0InfoVect0LinkObjId="g_2bc4140_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-316890_0" Pin1InfoVect1LinkObjId="g_2a226d0_0" Pin1InfoVect2LinkObjId="SW-316891_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="876,117 876,146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bc4dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="875,-7 875,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48881@0" ObjectIDZND0="48911@0" Pin0InfoVect0LinkObjId="g_2a14c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="875,-7 875,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2bcb7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="22,52 22,80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="22,52 22,80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2bcba50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="22,8 22,25 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="22,8 22,25 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2bd2020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="23,94 23,115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2bc5c30@0" ObjectIDZND2="g_2bd29a0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2bc5c30_0" Pin0InfoVect2LinkObjId="g_2bd29a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="23,94 23,115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2bd2280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="65,139 65,114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2bc5c30@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_2bd29a0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2bd29a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bc5c30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="65,139 65,114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2bd24e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="101,130 101,115 65,115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2bc5c30@0" ObjectIDZND1="0@x" ObjectIDZND2="g_2bd29a0@0" Pin0InfoVect0LinkObjId="g_2bc5c30_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2bd29a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="101,130 101,115 65,115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2bd2740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="65,115 23,115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_2bc5c30@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2bd29a0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2bd29a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2bc5c30_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="65,115 23,115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2bd33c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="23,115 23,144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_2bc5c30@0" ObjectIDND2="0@x" ObjectIDZND0="g_2bd29a0@1" Pin0InfoVect0LinkObjId="g_2bd29a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2bc5c30_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="23,115 23,144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2bd3620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="23,182 23,213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_2bd29a0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bd29a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="23,182 23,213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bd3880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="22,-9 22,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="48911@0" Pin0InfoVect0LinkObjId="g_2a14c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="22,-9 22,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2991db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="758,56 758,84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48876@0" ObjectIDZND0="48878@1" Pin0InfoVect0LinkObjId="SW-316885_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316884_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="758,56 758,84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2992010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="758,12 758,29 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48877@1" ObjectIDZND0="48876@1" Pin0InfoVect0LinkObjId="SW-316884_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316885_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="758,12 758,29 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2998620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="759,98 759,119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="48878@0" ObjectIDZND0="g_2998880@0" ObjectIDZND1="g_2bd5300@0" ObjectIDZND2="48879@x" Pin0InfoVect0LinkObjId="g_2998880_0" Pin0InfoVect1LinkObjId="g_2bd5300_0" Pin0InfoVect2LinkObjId="SW-316886_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316885_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="759,98 759,119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29992a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="759,119 759,148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="48878@x" ObjectIDND1="g_2bd5300@0" ObjectIDND2="48879@x" ObjectIDZND0="g_2998880@1" Pin0InfoVect0LinkObjId="g_2998880_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-316885_0" Pin1InfoVect1LinkObjId="g_2bd5300_0" Pin1InfoVect2LinkObjId="SW-316886_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="759,119 759,148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2999500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="758,-5 758,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48877@0" ObjectIDZND0="48911@0" Pin0InfoVect0LinkObjId="g_2a14c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316885_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="758,-5 758,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_299ffb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="998,57 998,85 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48884@0" ObjectIDZND0="48886@1" Pin0InfoVect0LinkObjId="SW-316895_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316894_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="998,57 998,85 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a0210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="998,13 998,30 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48885@1" ObjectIDZND0="48884@1" Pin0InfoVect0LinkObjId="SW-316894_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316895_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="998,13 998,30 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_295a860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="999,99 999,120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="48886@0" ObjectIDZND0="g_295aac0@0" ObjectIDZND1="g_299a370@0" ObjectIDZND2="48887@x" Pin0InfoVect0LinkObjId="g_295aac0_0" Pin0InfoVect1LinkObjId="g_299a370_0" Pin0InfoVect2LinkObjId="SW-316896_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316895_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="999,99 999,120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_295b4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="999,120 999,149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="48886@x" ObjectIDND1="g_299a370@0" ObjectIDND2="48887@x" ObjectIDZND0="g_295aac0@1" Pin0InfoVect0LinkObjId="g_295aac0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-316895_0" Pin1InfoVect1LinkObjId="g_299a370_0" Pin1InfoVect2LinkObjId="SW-316896_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="999,120 999,149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_295b740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="998,-4 998,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48885@0" ObjectIDZND0="48911@0" Pin0InfoVect0LinkObjId="g_2a14c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316895_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="998,-4 998,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29621f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1119,54 1119,82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48888@0" ObjectIDZND0="48890@1" Pin0InfoVect0LinkObjId="SW-316900_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316899_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1119,54 1119,82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2962450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1119,10 1119,27 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48889@1" ObjectIDZND0="48888@1" Pin0InfoVect0LinkObjId="SW-316899_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316900_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1119,10 1119,27 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2968a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1120,96 1120,117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="48890@0" ObjectIDZND0="g_2968cc0@0" ObjectIDZND1="g_295c5b0@0" ObjectIDZND2="48891@x" Pin0InfoVect0LinkObjId="g_2968cc0_0" Pin0InfoVect1LinkObjId="g_295c5b0_0" Pin0InfoVect2LinkObjId="SW-316901_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1120,96 1120,117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29696e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1120,117 1120,146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="48890@x" ObjectIDND1="g_295c5b0@0" ObjectIDND2="48891@x" ObjectIDZND0="g_2968cc0@1" Pin0InfoVect0LinkObjId="g_2968cc0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-316900_0" Pin1InfoVect1LinkObjId="g_295c5b0_0" Pin1InfoVect2LinkObjId="SW-316901_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1120,117 1120,146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2969940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1119,-7 1119,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48889@0" ObjectIDZND0="48911@0" Pin0InfoVect0LinkObjId="g_2a14c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1119,-7 1119,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29703f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1219,55 1219,83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48907@0" ObjectIDZND0="48909@1" Pin0InfoVect0LinkObjId="SW-316921_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1219,55 1219,83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2970650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1219,11 1219,28 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48908@1" ObjectIDZND0="48907@1" Pin0InfoVect0LinkObjId="SW-316920_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316921_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1219,11 1219,28 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2976c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1220,97 1220,118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="48909@0" ObjectIDZND0="g_2976ec0@0" ObjectIDZND1="g_296a7b0@0" ObjectIDZND2="48910@x" Pin0InfoVect0LinkObjId="g_2976ec0_0" Pin0InfoVect1LinkObjId="g_296a7b0_0" Pin0InfoVect2LinkObjId="SW-316922_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316921_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1220,97 1220,118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29778e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1220,118 1220,147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="48909@x" ObjectIDND1="g_296a7b0@0" ObjectIDND2="48910@x" ObjectIDZND0="g_2976ec0@1" Pin0InfoVect0LinkObjId="g_2976ec0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-316921_0" Pin1InfoVect1LinkObjId="g_296a7b0_0" Pin1InfoVect2LinkObjId="SW-316922_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1220,118 1220,147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2977b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1219,-6 1219,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48908@0" ObjectIDZND0="48911@0" Pin0InfoVect0LinkObjId="g_2a14c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316921_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1219,-6 1219,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c8e300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1336,54 1336,82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48892@0" ObjectIDZND0="48894@1" Pin0InfoVect0LinkObjId="SW-316905_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316904_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1336,54 1336,82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c8e560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1336,10 1336,27 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48893@1" ObjectIDZND0="48892@1" Pin0InfoVect0LinkObjId="SW-316904_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316905_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1336,10 1336,27 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c94b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1337,96 1337,117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="48894@0" ObjectIDZND0="g_2c94dd0@0" ObjectIDZND1="g_29789b0@0" ObjectIDZND2="48895@x" Pin0InfoVect0LinkObjId="g_2c94dd0_0" Pin0InfoVect1LinkObjId="g_29789b0_0" Pin0InfoVect2LinkObjId="SW-316906_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316905_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1337,96 1337,117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c957f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1337,117 1337,146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="48894@x" ObjectIDND1="g_29789b0@0" ObjectIDND2="48895@x" ObjectIDZND0="g_2c94dd0@1" Pin0InfoVect0LinkObjId="g_2c94dd0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-316905_0" Pin1InfoVect1LinkObjId="g_29789b0_0" Pin1InfoVect2LinkObjId="SW-316906_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1337,117 1337,146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c95a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1336,-7 1336,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48893@0" ObjectIDZND0="48911@0" Pin0InfoVect0LinkObjId="g_2a14c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316905_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1336,-7 1336,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c980b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="547,25 547,8 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48868@1" ObjectIDZND0="48869@1" Pin0InfoVect0LinkObjId="SW-316875_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316874_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="547,25 547,8 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ca1090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1741,-76 1741,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="42894@0" ObjectIDZND0="42880@0" Pin0InfoVect0LinkObjId="g_2a2cf40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263202_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1741,-76 1741,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ca18c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1993,-104 1993,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48147@0" ObjectIDZND0="42880@0" Pin0InfoVect0LinkObjId="g_2a2cf40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-263188_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1993,-104 1993,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ca84f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1075,-772 1054,-772 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1075,-772 1054,-772 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ca8750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1054,-751 1054,-772 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1054,-751 1054,-772 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2caaa60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1054,-772 1054,-795 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1054,-772 1054,-795 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2caff50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1054,-822 1054,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1054,-822 1054,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2cb01b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-957 1054,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_2cb0b30@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2cb0b30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1076,-957 1054,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2cb0410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-905 1054,-905 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_2cb0b30@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2cb0b30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1076,-905 1054,-905 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2cb0670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1054,-883 1054,-905 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_2cb0b30@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2cb0b30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1054,-883 1054,-905 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2cb08d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1054,-905 1054,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="g_2cb0b30@0" ObjectIDZND2="g_2cb2590@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2cb0b30_0" Pin0InfoVect2LinkObjId="g_2cb2590_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1054,-905 1054,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2cb7170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1080,-1104 1054,-1104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="g_2cb2590@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2cb0b30@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2cb0b30_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cb2590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1080,-1104 1054,-1104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2cb7c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1054,-1104 1054,-1140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_2cb2590@0" ObjectIDND1="g_2cb0b30@0" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2cb2590_0" Pin1InfoVect1LinkObjId="g_2cb0b30_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1054,-1104 1054,-1140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2cb7e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1029,-1079 1054,-1079 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2cb0b30@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cb0b30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1029,-1079 1054,-1079 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2cb8930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1054,-957 1054,-1079 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_2cb0b30@0" ObjectIDZND1="g_2cb2590@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2cb0b30_0" Pin0InfoVect1LinkObjId="g_2cb2590_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1054,-957 1054,-1079 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2cb8b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1054,-1079 1054,-1104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_2cb0b30@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_2cb2590@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2cb2590_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2cb0b30_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1054,-1079 1054,-1104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2cb8dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1054,-715 1054,-685 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="48119@0" Pin0InfoVect0LinkObjId="g_2b194d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1054,-715 1054,-685 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cc4800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-189 1176,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48862@1" ObjectIDZND0="48860@1" Pin0InfoVect0LinkObjId="SW-316866_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316867_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-189 1176,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cc4a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-144 1176,-117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48860@0" ObjectIDZND0="48861@1" Pin0InfoVect0LinkObjId="SW-316867_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316866_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-144 1176,-117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2cd72a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1202,-573 1176,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="48856@0" ObjectIDZND0="48853@x" ObjectIDZND1="48854@x" Pin0InfoVect0LinkObjId="SW-316859_0" Pin0InfoVect1LinkObjId="SW-316860_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316862_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1202,-573 1176,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2cd7500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-546 1176,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="48853@1" ObjectIDZND0="48856@x" ObjectIDZND1="48854@x" Pin0InfoVect0LinkObjId="SW-316862_0" Pin0InfoVect1LinkObjId="SW-316860_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316859_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-546 1176,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2cd7760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-573 1176,-606 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="48853@x" ObjectIDND1="48856@x" ObjectIDZND0="48854@0" Pin0InfoVect0LinkObjId="SW-316860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-316859_0" Pin1InfoVect1LinkObjId="SW-316862_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-573 1176,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cdbbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1244,-184 1244,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_2cd8e60@0" ObjectIDZND0="g_2cd8230@1" Pin0InfoVect0LinkObjId="g_2cd8230_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cd8e60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1244,-184 1244,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cde620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1244,-235 1244,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="48863@0" ObjectIDZND0="g_2cd8230@0" Pin0InfoVect0LinkObjId="g_2cd8230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316868_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1244,-235 1244,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2cde880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1330,-409 1330,-418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_2cd79c0@1" ObjectIDZND0="g_2cdb130@0" Pin0InfoVect0LinkObjId="g_2cdb130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cd79c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1330,-409 1330,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cdf850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1255,-289 1244,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" ObjectIDND0="g_2cdeae0@0" ObjectIDZND0="48912@x" ObjectIDZND1="48863@x" Pin0InfoVect0LinkObjId="g_2ce7cc0_0" Pin0InfoVect1LinkObjId="SW-316868_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cdeae0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1255,-289 1244,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cdfab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1177,-308 1244,-308 1244,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="48912@x" ObjectIDZND0="g_2cdeae0@0" ObjectIDZND1="48863@x" Pin0InfoVect0LinkObjId="g_2cdeae0_0" Pin0InfoVect1LinkObjId="SW-316868_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cdf850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1177,-308 1244,-308 1244,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cdfd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1244,-289 1244,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="48912@x" ObjectIDND1="g_2cdeae0@0" ObjectIDZND0="48863@1" Pin0InfoVect0LinkObjId="SW-316868_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2cdf850_0" Pin1InfoVect1LinkObjId="g_2cdeae0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1244,-289 1244,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2cdff70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1303,-420 1303,-408 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2cda6a0@0" ObjectIDZND0="48859@1" Pin0InfoVect0LinkObjId="SW-316865_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cda6a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1303,-420 1303,-408 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ce47c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-642 1176,-685 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48854@1" ObjectIDZND0="48119@0" Pin0InfoVect0LinkObjId="g_2b194d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316860_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-642 1176,-685 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ce4ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-100 1176,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48861@0" ObjectIDZND0="48911@0" Pin0InfoVect0LinkObjId="g_2a14c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316867_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-100 1176,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ce5250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1208,-499 1176,-499 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="48857@0" ObjectIDZND0="48855@x" ObjectIDZND1="48853@x" Pin0InfoVect0LinkObjId="SW-316861_0" Pin0InfoVect1LinkObjId="SW-316859_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316863_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1208,-499 1176,-499 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ce5d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-480 1176,-499 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="48855@1" ObjectIDZND0="48857@x" ObjectIDZND1="48853@x" Pin0InfoVect0LinkObjId="SW-316863_0" Pin0InfoVect1LinkObjId="SW-316859_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316861_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-480 1176,-499 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ce5fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-499 1176,-519 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="48857@x" ObjectIDND1="48855@x" ObjectIDZND0="48853@0" Pin0InfoVect0LinkObjId="SW-316859_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-316863_0" Pin1InfoVect1LinkObjId="SW-316861_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-499 1176,-519 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ce6200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1156,-422 1176,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer" ObjectIDND0="48858@0" ObjectIDZND0="48855@x" ObjectIDZND1="g_2ce6f50@0" ObjectIDZND2="48912@x" Pin0InfoVect0LinkObjId="SW-316861_0" Pin0InfoVect1LinkObjId="g_2ce6f50_0" Pin0InfoVect2LinkObjId="g_2cdf850_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316864_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1156,-422 1176,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ce6cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-422 1176,-444 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer" EndDevType0="switch" ObjectIDND0="48858@x" ObjectIDND1="g_2ce6f50@0" ObjectIDND2="48912@x" ObjectIDZND0="48855@0" Pin0InfoVect0LinkObjId="SW-316861_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-316864_0" Pin1InfoVect1LinkObjId="g_2ce6f50_0" Pin1InfoVect2LinkObjId="g_2cdf850_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-422 1176,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ce7cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1196,-402 1176,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2ce6f50@0" ObjectIDZND0="48912@x" ObjectIDZND1="48858@x" ObjectIDZND2="48855@x" Pin0InfoVect0LinkObjId="g_2cdf850_0" Pin0InfoVect1LinkObjId="SW-316864_0" Pin0InfoVect2LinkObjId="SW-316861_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ce6f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1196,-402 1176,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ce87b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-373 1176,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="48912@2" ObjectIDZND0="g_2ce6f50@0" ObjectIDZND1="48858@x" ObjectIDZND2="48855@x" Pin0InfoVect0LinkObjId="g_2ce6f50_0" Pin0InfoVect1LinkObjId="SW-316864_0" Pin0InfoVect2LinkObjId="SW-316861_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cdf850_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-373 1176,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ce8a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-402 1176,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2ce6f50@0" ObjectIDND1="48912@x" ObjectIDZND0="48858@x" ObjectIDZND1="48855@x" Pin0InfoVect0LinkObjId="SW-316864_0" Pin0InfoVect1LinkObjId="SW-316861_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ce6f50_0" Pin1InfoVect1LinkObjId="g_2cdf850_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-402 1176,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cedb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1432,55 1432,83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48896@0" ObjectIDZND0="48898@1" Pin0InfoVect0LinkObjId="SW-316910_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316909_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1432,55 1432,83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cedde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1432,11 1432,28 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48897@1" ObjectIDZND0="48896@1" Pin0InfoVect0LinkObjId="SW-316909_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316910_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1432,11 1432,28 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cf1100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1432,-6 1432,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48897@0" ObjectIDZND0="48911@0" Pin0InfoVect0LinkObjId="g_2a14c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1432,-6 1432,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cf8f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1551,-1 1551,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48899@0" ObjectIDZND0="42880@0" Pin0InfoVect0LinkObjId="g_2a2cf40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316911_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1551,-1 1551,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cf97a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1432,100 1432,110 1552,110 1552,104 1551,103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="48898@0" ObjectIDZND0="48900@0" Pin0InfoVect0LinkObjId="SW-316911_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1432,100 1432,110 1552,110 1552,104 1551,103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d04370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="430,54 430,82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48864@0" ObjectIDZND0="48866@1" Pin0InfoVect0LinkObjId="SW-316870_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316869_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="430,54 430,82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d0a980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="430,99 430,120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="48866@0" ObjectIDZND0="g_2d0abe0@0" ObjectIDZND1="g_2cfeb10@0" ObjectIDZND2="48867@x" Pin0InfoVect0LinkObjId="g_2d0abe0_0" Pin0InfoVect1LinkObjId="g_2cfeb10_0" Pin0InfoVect2LinkObjId="SW-316871_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="430,99 430,120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d0b600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="430,120 430,146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="48866@x" ObjectIDND1="g_2cfeb10@0" ObjectIDND2="48867@x" ObjectIDZND0="g_2d0abe0@1" Pin0InfoVect0LinkObjId="g_2d0abe0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-316870_0" Pin1InfoVect1LinkObjId="g_2cfeb10_0" Pin1InfoVect2LinkObjId="SW-316871_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="430,120 430,146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d0b860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="430,-7 430,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48865@0" ObjectIDZND0="48911@0" Pin0InfoVect0LinkObjId="g_2a14c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="430,-7 430,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d0c6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="430,27 430,10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48864@1" ObjectIDZND0="48865@1" Pin0InfoVect0LinkObjId="SW-316870_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316869_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="430,27 430,10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d10aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="315,25 315,35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="48902@1" ObjectIDZND0="48901@1" Pin0InfoVect0LinkObjId="SW-316914_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316915_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="315,25 315,35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d10d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="315,62 315,74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="48901@0" ObjectIDZND0="48903@1" Pin0InfoVect0LinkObjId="SW-316915_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316914_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="315,62 315,74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d15e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="315,8 315,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48902@0" ObjectIDZND0="48911@0" Pin0InfoVect0LinkObjId="g_2a14c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316915_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="315,8 315,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d19e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="392,192 392,180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2d193b0@0" ObjectIDZND0="48904@0" Pin0InfoVect0LinkObjId="SW-316916_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d193b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="392,192 392,180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d1a0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="315,91 315,128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="48903@0" ObjectIDZND0="g_2d1a300@0" ObjectIDZND1="g_2d160d0@0" ObjectIDZND2="48904@x" Pin0InfoVect0LinkObjId="g_2d1a300_0" Pin0InfoVect1LinkObjId="g_2d160d0_0" Pin0InfoVect2LinkObjId="SW-316916_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316915_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="315,91 315,128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d1ad20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="315,158 315,128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2d1a300@1" ObjectIDZND0="48903@x" ObjectIDZND1="g_2d160d0@0" ObjectIDZND2="48904@x" Pin0InfoVect0LinkObjId="SW-316915_0" Pin0InfoVect1LinkObjId="g_2d160d0_0" Pin0InfoVect2LinkObjId="SW-316916_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d1a300_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="315,158 315,128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d1ded0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="316,316 316,270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2d1af80@1" Pin0InfoVect0LinkObjId="g_2d1af80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="316,316 316,270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d1ee40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="316,569 316,552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="reactance" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="316,569 316,552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d1f0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="316,510 316,488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="316,510 316,488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d1f300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="273,468 273,488 316,488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="reactance" EndDevType1="breaker" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="273,468 273,488 316,488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d1f560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="316,488 316,472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="316,488 316,472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d2b1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="310,332 269,332 269,356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="310,332 269,332 269,356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d2be10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="202,6 202,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48905@0" ObjectIDZND0="48911@0" Pin0InfoVect0LinkObjId="g_2a14c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316919_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="202,6 202,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d2ce20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="157,97 202,97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2d2c070@0" ObjectIDZND0="g_2d2b440@0" ObjectIDZND1="48906@x" Pin0InfoVect0LinkObjId="g_2d2b440_0" Pin0InfoVect1LinkObjId="SW-316919_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d2c070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="157,97 202,97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d2d910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="202,112 202,97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2d2b440@0" ObjectIDZND0="g_2d2c070@0" ObjectIDZND1="48906@x" Pin0InfoVect0LinkObjId="g_2d2c070_0" Pin0InfoVect1LinkObjId="SW-316919_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d2b440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="202,112 202,97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d2db70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="202,97 202,76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2d2c070@0" ObjectIDND1="g_2d2b440@0" ObjectIDZND0="48906@0" Pin0InfoVect0LinkObjId="SW-316919_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2d2c070_0" Pin1InfoVect1LinkObjId="g_2d2b440_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="202,97 202,76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d2fb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="202,148 202,177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2d2b440@1" ObjectIDZND0="g_2d2ddd0@0" Pin0InfoVect0LinkObjId="g_2d2ddd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d2b440_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="202,148 202,177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d31c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="282,363 316,363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2d224f0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2d224f0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="282,363 316,363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d31e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="316,363 316,352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_2d224f0@0" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2d224f0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="316,363 316,352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d32800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="316,436 316,421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_2d224f0@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2d224f0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="316,436 316,421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d32a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="316,421 273,421 273,443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="g_2d224f0@0" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2d224f0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="316,421 273,421 273,443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d32cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="297,385 316,385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_2d224f0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d224f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="297,385 316,385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d33790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="316,363 316,385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_2d224f0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2d224f0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="316,363 316,385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d339d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="316,385 316,421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="g_2d224f0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d224f0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="316,385 316,421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d33c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1262,142 1262,118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_296a7b0@0" ObjectIDZND0="48910@x" ObjectIDZND1="48909@x" ObjectIDZND2="g_2976ec0@0" Pin0InfoVect0LinkObjId="SW-316922_0" Pin0InfoVect1LinkObjId="SW-316921_0" Pin0InfoVect2LinkObjId="g_2976ec0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_296a7b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1262,142 1262,118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d346d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1298,133 1298,118 1262,118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="48910@0" ObjectIDZND0="g_296a7b0@0" ObjectIDZND1="48909@x" ObjectIDZND2="g_2976ec0@0" Pin0InfoVect0LinkObjId="g_296a7b0_0" Pin0InfoVect1LinkObjId="SW-316921_0" Pin0InfoVect2LinkObjId="g_2976ec0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316922_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1298,133 1298,118 1262,118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d34930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1262,118 1220,118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_296a7b0@0" ObjectIDND1="48910@x" ObjectIDZND0="48909@x" ObjectIDZND1="g_2976ec0@0" Pin0InfoVect0LinkObjId="SW-316921_0" Pin0InfoVect1LinkObjId="g_2976ec0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_296a7b0_0" Pin1InfoVect1LinkObjId="SW-316922_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1262,118 1220,118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d34b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1162,141 1162,117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_295c5b0@0" ObjectIDZND0="48891@x" ObjectIDZND1="48890@x" ObjectIDZND2="g_2968cc0@0" Pin0InfoVect0LinkObjId="SW-316901_0" Pin0InfoVect1LinkObjId="SW-316900_0" Pin0InfoVect2LinkObjId="g_2968cc0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_295c5b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1162,141 1162,117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d35660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1198,132 1198,117 1162,117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="48891@0" ObjectIDZND0="g_295c5b0@0" ObjectIDZND1="48890@x" ObjectIDZND2="g_2968cc0@0" Pin0InfoVect0LinkObjId="g_295c5b0_0" Pin0InfoVect1LinkObjId="SW-316900_0" Pin0InfoVect2LinkObjId="g_2968cc0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316901_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1198,132 1198,117 1162,117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d358c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1162,117 1120,117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_295c5b0@0" ObjectIDND1="48891@x" ObjectIDZND0="48890@x" ObjectIDZND1="g_2968cc0@0" Pin0InfoVect0LinkObjId="SW-316900_0" Pin0InfoVect1LinkObjId="g_2968cc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_295c5b0_0" Pin1InfoVect1LinkObjId="SW-316901_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1162,117 1120,117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d35b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1041,144 1041,120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_299a370@0" ObjectIDZND0="48887@x" ObjectIDZND1="48886@x" ObjectIDZND2="g_295aac0@0" Pin0InfoVect0LinkObjId="SW-316896_0" Pin0InfoVect1LinkObjId="SW-316895_0" Pin0InfoVect2LinkObjId="g_295aac0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_299a370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1041,144 1041,120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d365f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1077,135 1077,120 1041,120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="48887@0" ObjectIDZND0="g_299a370@0" ObjectIDZND1="48886@x" ObjectIDZND2="g_295aac0@0" Pin0InfoVect0LinkObjId="g_299a370_0" Pin0InfoVect1LinkObjId="SW-316895_0" Pin0InfoVect2LinkObjId="g_295aac0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316896_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1077,135 1077,120 1041,120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d36830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1041,120 999,120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_299a370@0" ObjectIDND1="48887@x" ObjectIDZND0="48886@x" ObjectIDZND1="g_295aac0@0" Pin0InfoVect0LinkObjId="SW-316895_0" Pin0InfoVect1LinkObjId="g_295aac0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_299a370_0" Pin1InfoVect1LinkObjId="SW-316896_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1041,120 999,120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d36a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,141 918,117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2a226d0@0" ObjectIDZND0="48883@x" ObjectIDZND1="48882@x" ObjectIDZND2="g_2bc4140@0" Pin0InfoVect0LinkObjId="SW-316891_0" Pin0InfoVect1LinkObjId="SW-316890_0" Pin0InfoVect2LinkObjId="g_2bc4140_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a226d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="918,141 918,117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d37560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="954,132 954,117 918,117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="48883@0" ObjectIDZND0="g_2a226d0@0" ObjectIDZND1="48882@x" ObjectIDZND2="g_2bc4140@0" Pin0InfoVect0LinkObjId="g_2a226d0_0" Pin0InfoVect1LinkObjId="SW-316890_0" Pin0InfoVect2LinkObjId="g_2bc4140_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316891_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="954,132 954,117 918,117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d377c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,117 876,117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2a226d0@0" ObjectIDND1="48883@x" ObjectIDZND0="48882@x" ObjectIDZND1="g_2bc4140@0" Pin0InfoVect0LinkObjId="SW-316890_0" Pin0InfoVect1LinkObjId="g_2bc4140_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a226d0_0" Pin1InfoVect1LinkObjId="SW-316891_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="918,117 876,117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d37a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,143 801,119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2bd5300@0" ObjectIDZND0="48879@x" ObjectIDZND1="48878@1" ObjectIDZND2="g_2998880@0" Pin0InfoVect0LinkObjId="SW-316886_0" Pin0InfoVect1LinkObjId="SW-316885_1" Pin0InfoVect2LinkObjId="g_2998880_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bd5300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="801,143 801,119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d384f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="837,134 837,119 801,119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="48879@0" ObjectIDZND0="g_2bd5300@0" ObjectIDZND1="48878@1" ObjectIDZND2="g_2998880@0" Pin0InfoVect0LinkObjId="g_2bd5300_0" Pin0InfoVect1LinkObjId="SW-316885_1" Pin0InfoVect2LinkObjId="g_2998880_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316886_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="837,134 837,119 801,119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d38730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="801,119 759,119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2bd5300@0" ObjectIDND1="48879@x" ObjectIDZND0="48878@x" ObjectIDZND1="g_2998880@0" Pin0InfoVect0LinkObjId="SW-316885_0" Pin0InfoVect1LinkObjId="g_2998880_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2bd5300_0" Pin1InfoVect1LinkObjId="SW-316886_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="801,119 759,119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d38990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="696,136 696,112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2a14e80@0" ObjectIDZND0="48875@x" ObjectIDZND1="48874@x" ObjectIDZND2="g_2a21590@0" Pin0InfoVect0LinkObjId="SW-316881_0" Pin0InfoVect1LinkObjId="SW-316880_0" Pin0InfoVect2LinkObjId="g_2a21590_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a14e80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="696,136 696,112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d39460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="732,127 732,112 696,112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="48875@0" ObjectIDZND0="g_2a14e80@0" ObjectIDZND1="48874@x" ObjectIDZND2="g_2a21590@0" Pin0InfoVect0LinkObjId="g_2a14e80_0" Pin0InfoVect1LinkObjId="SW-316880_0" Pin0InfoVect2LinkObjId="g_2a21590_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316881_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="732,127 732,112 696,112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d396c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="696,112 654,112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2a14e80@0" ObjectIDND1="48875@x" ObjectIDZND0="48874@x" ObjectIDZND1="g_2a21590@0" Pin0InfoVect0LinkObjId="SW-316880_0" Pin0InfoVect1LinkObjId="g_2a21590_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a14e80_0" Pin1InfoVect1LinkObjId="SW-316881_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="696,112 654,112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d39920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="590,139 590,118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_29a9450@0" ObjectIDZND0="48871@x" ObjectIDZND1="48870@x" ObjectIDZND2="g_29b6020@0" Pin0InfoVect0LinkObjId="SW-316876_0" Pin0InfoVect1LinkObjId="SW-316875_0" Pin0InfoVect2LinkObjId="g_29b6020_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29a9450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="590,139 590,118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3a3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="626,133 626,118 590,118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="48871@0" ObjectIDZND0="g_29a9450@0" ObjectIDZND1="48870@x" ObjectIDZND2="g_29b6020@0" Pin0InfoVect0LinkObjId="g_29a9450_0" Pin0InfoVect1LinkObjId="SW-316875_0" Pin0InfoVect2LinkObjId="g_29b6020_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316876_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="626,133 626,118 590,118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3a650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="590,118 547,118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_29a9450@0" ObjectIDND1="48871@x" ObjectIDZND0="48870@x" ObjectIDZND1="g_29b6020@0" Pin0InfoVect0LinkObjId="SW-316875_0" Pin0InfoVect1LinkObjId="g_29b6020_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29a9450_0" Pin1InfoVect1LinkObjId="SW-316876_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="590,118 547,118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3a8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="473,141 473,120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2cfeb10@0" ObjectIDZND0="48867@x" ObjectIDZND1="48866@x" ObjectIDZND2="g_2d0abe0@0" Pin0InfoVect0LinkObjId="SW-316871_0" Pin0InfoVect1LinkObjId="SW-316870_0" Pin0InfoVect2LinkObjId="g_2d0abe0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cfeb10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="473,141 473,120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3b380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="509,135 509,120 473,120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="48867@0" ObjectIDZND0="g_2cfeb10@0" ObjectIDZND1="48866@x" ObjectIDZND2="g_2d0abe0@0" Pin0InfoVect0LinkObjId="g_2cfeb10_0" Pin0InfoVect1LinkObjId="SW-316870_0" Pin0InfoVect2LinkObjId="g_2d0abe0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316871_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="509,135 509,120 473,120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3b5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="473,120 430,120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2cfeb10@0" ObjectIDND1="48867@x" ObjectIDZND0="48866@x" ObjectIDZND1="g_2d0abe0@0" Pin0InfoVect0LinkObjId="SW-316870_0" Pin0InfoVect1LinkObjId="g_2d0abe0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2cfeb10_0" Pin1InfoVect1LinkObjId="SW-316871_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="473,120 430,120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3b840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="357,148 357,128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2d160d0@0" ObjectIDZND0="48903@x" ObjectIDZND1="g_2d1a300@0" ObjectIDZND2="48904@x" Pin0InfoVect0LinkObjId="SW-316915_0" Pin0InfoVect1LinkObjId="g_2d1a300_0" Pin0InfoVect2LinkObjId="SW-316916_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d160d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="357,148 357,128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3c310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="315,128 357,128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="48903@x" ObjectIDND1="g_2d1a300@0" ObjectIDZND0="g_2d160d0@0" ObjectIDZND1="48904@x" Pin0InfoVect0LinkObjId="g_2d160d0_0" Pin0InfoVect1LinkObjId="SW-316916_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-316915_0" Pin1InfoVect1LinkObjId="g_2d1a300_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="315,128 357,128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3c570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="357,128 392,128 392,144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2d160d0@0" ObjectIDND1="48903@x" ObjectIDND2="g_2d1a300@0" ObjectIDZND0="48904@1" Pin0InfoVect0LinkObjId="SW-316916_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d160d0_0" Pin1InfoVect1LinkObjId="SW-316915_0" Pin1InfoVect2LinkObjId="g_2d1a300_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="357,128 392,128 392,144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3c7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="315,241 315,207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2d1af80@0" ObjectIDZND0="g_2d1a300@0" Pin0InfoVect0LinkObjId="g_2d1a300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d1af80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="315,241 315,207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3ca30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="430,215 430,184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2d0abe0@0" Pin0InfoVect0LinkObjId="g_2d0abe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="430,215 430,184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3cc90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="547,213 547,182 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_29b6020@0" Pin0InfoVect0LinkObjId="g_29b6020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="547,213 547,182 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3cef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="759,217 759,186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2998880@0" Pin0InfoVect0LinkObjId="g_2998880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="759,217 759,186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3d150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="876,215 876,184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2bc4140@0" Pin0InfoVect0LinkObjId="g_2bc4140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="876,215 876,184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3d3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="999,218 999,187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_295aac0@0" Pin0InfoVect0LinkObjId="g_295aac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="999,218 999,187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3d610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1120,215 1120,184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2968cc0@0" Pin0InfoVect0LinkObjId="g_2968cc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1120,215 1120,184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3d870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1220,216 1220,185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2976ec0@0" Pin0InfoVect0LinkObjId="g_2976ec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1220,216 1220,185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3dad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1337,215 1337,184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2c94dd0@0" Pin0InfoVect0LinkObjId="g_2c94dd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1337,215 1337,184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3dd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1379,141 1379,117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_29789b0@0" ObjectIDZND0="48894@x" ObjectIDZND1="g_2c94dd0@0" ObjectIDZND2="48895@x" Pin0InfoVect0LinkObjId="SW-316905_0" Pin0InfoVect1LinkObjId="g_2c94dd0_0" Pin0InfoVect2LinkObjId="SW-316906_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29789b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1379,141 1379,117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3e800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1337,117 1379,117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="48894@x" ObjectIDND1="g_2c94dd0@0" ObjectIDZND0="g_29789b0@0" ObjectIDZND1="48895@x" Pin0InfoVect0LinkObjId="g_29789b0_0" Pin0InfoVect1LinkObjId="SW-316906_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-316905_0" Pin1InfoVect1LinkObjId="g_2c94dd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1337,117 1379,117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3ea60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1379,117 1415,117 1415,132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_29789b0@0" ObjectIDND1="48894@x" ObjectIDND2="g_2c94dd0@0" ObjectIDZND0="48895@0" Pin0InfoVect0LinkObjId="SW-316906_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29789b0_0" Pin1InfoVect1LinkObjId="SW-316905_0" Pin1InfoVect2LinkObjId="g_2c94dd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1379,117 1415,117 1415,132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2d3f530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-352 1303,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="48912@x" ObjectIDZND0="48859@x" ObjectIDZND1="g_2cd79c0@0" ObjectIDZND2="g_2cd98f0@0" Pin0InfoVect0LinkObjId="SW-316865_0" Pin0InfoVect1LinkObjId="g_2cd79c0_0" Pin0InfoVect2LinkObjId="g_2cd98f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cdf850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-352 1303,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2d3f770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1303,-352 1303,-372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="48912@x" ObjectIDND1="g_2cd79c0@0" ObjectIDND2="g_2cd98f0@0" ObjectIDZND0="48859@0" Pin0InfoVect0LinkObjId="SW-316865_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2cdf850_0" Pin1InfoVect1LinkObjId="g_2cd79c0_0" Pin1InfoVect2LinkObjId="g_2cd98f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1303,-352 1303,-372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2d3f9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1330,-369 1330,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2cd79c0@0" ObjectIDZND0="48912@x" ObjectIDZND1="48859@x" ObjectIDZND2="g_2cd98f0@0" Pin0InfoVect0LinkObjId="g_2cdf850_0" Pin0InfoVect1LinkObjId="SW-316865_0" Pin0InfoVect2LinkObjId="g_2cd98f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cd79c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1330,-369 1330,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2d404a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1303,-352 1330,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="48912@x" ObjectIDND1="48859@x" ObjectIDZND0="g_2cd79c0@0" ObjectIDZND1="g_2cd98f0@0" Pin0InfoVect0LinkObjId="g_2cd79c0_0" Pin0InfoVect1LinkObjId="g_2cd98f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2cdf850_0" Pin1InfoVect1LinkObjId="SW-316865_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1303,-352 1330,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2d40700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1330,-352 1361,-352 1361,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2cd79c0@0" ObjectIDND1="48912@x" ObjectIDND2="48859@x" ObjectIDZND0="g_2cd98f0@0" Pin0InfoVect0LinkObjId="g_2cd98f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2cd79c0_0" Pin1InfoVect1LinkObjId="g_2cdf850_0" Pin1InfoVect2LinkObjId="SW-316865_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1330,-352 1361,-352 1361,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30693e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="316,554 316,569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="reactance" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="316,554 316,569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3069640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="316,569 316,608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" ObjectIDND0="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="316,569 316,608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_306b1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1551,14 1551,86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="48899@1" ObjectIDZND0="48900@1" Pin0InfoVect0LinkObjId="SW-316911_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316911_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1551,14 1551,86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3074290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="202,23 202,59 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="48905@1" ObjectIDZND0="48906@1" Pin0InfoVect0LinkObjId="SW-316919_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316919_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="202,23 202,59 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3079c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1111,-328 1075,-328 1075,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" ObjectIDND0="48912@0" ObjectIDZND0="g_3079e70@0" Pin0InfoVect0LinkObjId="g_3079e70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cdf850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1111,-328 1075,-328 1075,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_307a900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1142,-230 1176,-230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" ObjectIDND0="g_2cc6e20@0" ObjectIDZND0="48912@x" ObjectIDZND1="48862@x" Pin0InfoVect0LinkObjId="g_2cdf850_0" Pin0InfoVect1LinkObjId="SW-316867_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cc6e20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1142,-230 1176,-230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_307b3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-289 1176,-230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="48912@1" ObjectIDZND0="g_2cc6e20@0" ObjectIDZND1="48862@x" Pin0InfoVect0LinkObjId="g_2cc6e20_0" Pin0InfoVect1LinkObjId="SW-316867_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cdf850_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-289 1176,-230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_307b610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1176,-230 1176,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" ObjectIDND0="g_2cc6e20@0" ObjectIDND1="48912@x" ObjectIDZND0="48862@0" Pin0InfoVect0LinkObjId="SW-316867_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2cc6e20_0" Pin1InfoVect1LinkObjId="g_2cdf850_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1176,-230 1176,-206 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="42880" cx="1918" cy="-23" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42880" cx="2064" cy="-23" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42880" cx="2349" cy="-23" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42880" cx="1639" cy="-23" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42880" cx="1779" cy="-23" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42880" cx="2210" cy="-23" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42880" cx="2486" cy="-23" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42880" cx="2630" cy="-23" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42880" cx="2775" cy="-23" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42880" cx="2913" cy="-23" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42880" cx="1741" cy="-23" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42880" cx="1993" cy="-23" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48119" cx="1993" cy="-685" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48119" cx="2059" cy="-685" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48119" cx="1667" cy="-685" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48119" cx="1054" cy="-685" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48119" cx="1176" cy="-685" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48911" cx="22" cy="-26" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48911" cx="1219" cy="-26" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48911" cx="1336" cy="-26" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48911" cx="1176" cy="-26" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48911" cx="1432" cy="-26" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="42880" cx="1551" cy="-23" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48911" cx="1119" cy="-26" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48911" cx="998" cy="-26" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48911" cx="875" cy="-26" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48911" cx="758" cy="-26" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48911" cx="653" cy="-26" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48911" cx="547" cy="-26" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48911" cx="430" cy="-26" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48911" cx="315" cy="-26" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48911" cx="202" cy="-26" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-263125" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -54.000000 -976.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42874" ObjectName="DYN-CX_WDGF"/>
     <cge:Meas_Ref ObjectId="263125"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b91010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b91010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b91010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b91010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b91010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b91010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b91010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b91010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b91010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b91010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b91010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b91010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b91010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b91010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b91010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b91010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b91010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b91010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2949440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2949440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2949440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2949440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2949440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2949440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2949440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2b770e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -1084.500000) translate(0,16)">万德村光伏电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2aef290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1694.500000 -328.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2aef290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1694.500000 -328.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a890c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1598.000000 259.000000) translate(0,15)">35kV2号SVG</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2aee080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -202.500000 -13.000000) translate(0,15)">0878 6018400</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2aee080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -202.500000 -13.000000) translate(0,33)">0878 6034800</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2aee080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -202.500000 -13.000000) translate(0,51)">18787859302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aee1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1884.000000 37.000000) translate(0,12)">368</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b1c3a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2002.000000 -169.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2ad1f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2004.000000 -1206.000000) translate(0,17)">220kV万华线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2b342e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2028.000000 -1178.000000) translate(0,17)">C  B  A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2adc050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2030.000000 34.000000) translate(0,12)">367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aad080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2315.000000 39.000000) translate(0,12)">365</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a94da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1605.000000 38.000000) translate(0,12)">370</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a32d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1745.000000 35.000000) translate(0,12)">369</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a70520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2176.000000 41.000000) translate(0,12)">366</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2af9a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2452.000000 44.000000) translate(0,12)">364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aa2f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2596.000000 42.000000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a8dff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2741.000000 41.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a3c130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2879.000000 40.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a45050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1730.000000 259.000000) translate(0,15)">35kV1号SVG</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a45690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1878.000000 260.000000) translate(0,15)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a73eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2006.000000 260.000000) translate(0,15)">35kV备用(储能)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a75040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2162.000000 262.000000) translate(0,15)">35kV6号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a75750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2293.000000 262.000000) translate(0,15)">35kV5号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a75970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2433.000000 262.000000) translate(0,15)">35kV4号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a75bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2587.000000 262.000000) translate(0,15)">35kV3号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a75df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2732.000000 262.000000) translate(0,15)">35kV2号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a76030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2869.000000 262.000000) translate(0,15)">35kV1号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a76270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1620.500000 -1018.000000) translate(0,15)">220kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a76270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1620.500000 -1018.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a77350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2084.000000 -978.000000) translate(0,12)">23167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a77590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1727.000000 149.000000) translate(0,12)">37067</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a777d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1867.000000 146.000000) translate(0,12)">36967</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a77a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2006.000000 148.000000) translate(0,12)">36867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a77c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2152.000000 145.000000) translate(0,12)">36767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a77e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2298.000000 152.000000) translate(0,12)">36667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a780d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2437.000000 150.000000) translate(0,12)">36567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a78310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2574.000000 155.000000) translate(0,12)">36467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a78550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2718.000000 153.000000) translate(0,12)">36367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a78790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2863.000000 152.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a789d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3001.000000 151.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a78c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1454.000000 -677.000000) translate(0,12)">220kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29837d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2075.000000 -398.000000) translate(0,12)">2010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2983e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2073.000000 -257.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2ab3ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2093.000000 -928.000000) translate(0,17)">23160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2ab4670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2070.000000 -866.000000) translate(0,17)">2316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2ab48f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2070.000000 -734.000000) translate(0,17)">2311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2ab4b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2076.000000 -795.000000) translate(0,17)">23117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2ab4f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2014.000000 -807.000000) translate(0,17)">231</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2ab5390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1678.000000 -779.000000) translate(0,17)">2901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2ab57f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1684.000000 -841.000000) translate(0,17)">29017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2ab5a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1592.000000 -763.000000) translate(0,17)">29010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2ab7f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2002.000000 -547.000000) translate(0,17)">201</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab8420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1752.000000 -101.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab8660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2004.000000 -635.000000) translate(0,12)">2011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab88a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2034.000000 -603.000000) translate(0,12)">20117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab8ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2004.000000 -489.000000) translate(0,12)">2016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab8d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2025.000000 -455.000000) translate(0,12)">20160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab8f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1928.000000 -430.000000) translate(0,12)">20167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c95cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1091.000000 255.000000) translate(0,15)">光伏进线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c95cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1091.000000 255.000000) translate(0,33)">  13UL</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c97120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1208.000000 261.000000) translate(0,15)">#2-1储能</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c97e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1299.000000 326.000000) translate(0,15)">#2站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2cb3300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 999.000000 -1215.000000) translate(0,17)">备用二间隔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2cb5160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1065.000000 -743.000000) translate(0,17)">2321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2cb5790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1071.000000 -804.000000) translate(0,17)">23217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2cfc4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 969.000000 253.000000) translate(0,15)">光伏进线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2cfc4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 969.000000 253.000000) translate(0,33)">  12UL</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2cfcfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 845.000000 254.000000) translate(0,15)">光伏进线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2cfcfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 845.000000 254.000000) translate(0,33)">  11UL</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2cfd7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 728.000000 257.000000) translate(0,15)">光伏进线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2cfd7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 728.000000 257.000000) translate(0,33)">  10UL</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2cfdfe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 616.000000 256.000000) translate(0,15)">光伏进线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2cfdfe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 616.000000 256.000000) translate(0,33)">  9UL</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2cfe7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 516.000000 256.000000) translate(0,15)">光伏进线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2cfe7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 516.000000 256.000000) translate(0,33)">  8UL</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d0c930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 399.000000 258.000000) translate(0,15)">光伏进线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d0c930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 399.000000 258.000000) translate(0,33)">  7UL</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2d232a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 250.000000 684.000000) translate(0,17)">3号动态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2d232a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 250.000000 684.000000) translate(0,38)">    ±46Mvar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d2fdc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -11.000000 234.000000) translate(0,15)">谐波预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d31060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 150.000000 235.000000) translate(0,15)">#2电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d40960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 916.000000 -471.000000) translate(0,12)">2号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d40960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 916.000000 -471.000000) translate(0,27)">SFZB18-150000/230</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d40960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 916.000000 -471.000000) translate(0,42)">230±8×1.25%/37kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d40960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 916.000000 -471.000000) translate(0,57)">YN，yn0+d11 Ud%=14%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d450b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2172.000000 -284.000000) translate(0,12)">1号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d450b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2172.000000 -284.000000) translate(0,27)">SFZB18-150000/220</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d450b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2172.000000 -284.000000) translate(0,42)">230±8×1.25%/37kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d450b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2172.000000 -284.000000) translate(0,57)">YN，yn0+d11 Ud%=14%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30698a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1191.000000 -631.000000) translate(0,12)">2021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3069d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1205.000000 -599.000000) translate(0,12)">20217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3069fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1192.000000 -540.000000) translate(0,12)">202</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306a210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1229.000000 -490.000000) translate(0,12)">20260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306a450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1112.000000 -448.000000) translate(0,12)">20267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306a690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1187.000000 -469.000000) translate(0,12)">2026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306a8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1262.000000 -395.000000) translate(0,12)">2020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306ab10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1251.000000 -260.000000) translate(0,12)">3020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306ad50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1190.000000 -163.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306af90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1447.000000 37.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306b3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1514.000000 41.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306b670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1345.000000 33.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306b8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1396.000000 189.000000) translate(0,12)">38167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306baf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1228.000000 34.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306bd30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1276.000000 192.000000) translate(0,12)">38267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306bf70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1128.000000 33.000000) translate(0,12)">383</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306c1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1174.000000 195.000000) translate(0,12)">38367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306c3f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1007.000000 36.000000) translate(0,12)">384</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306c630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1057.000000 194.000000) translate(0,12)">38467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306c870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 884.000000 33.000000) translate(0,12)">385</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306cab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 930.000000 195.000000) translate(0,12)">38567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306ccf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 767.000000 35.000000) translate(0,12)">386</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306cf30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 814.000000 195.000000) translate(0,12)">38667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306d170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 662.000000 28.000000) translate(0,12)">387</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306d3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 705.000000 194.000000) translate(0,12)">38767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306d5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 556.000000 31.000000) translate(0,12)">388</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306d830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 607.000000 191.000000) translate(0,12)">38867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306da70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 439.000000 33.000000) translate(0,12)">389</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306dcb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 485.000000 195.000000) translate(0,12)">38967</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306def0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 324.000000 41.000000) translate(0,12)">390</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_306e130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 370.000000 215.000000) translate(0,12)">39067</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30744f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 213.000000 34.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3074b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -75.000000 -58.000000) translate(0,12)">35kVⅡ母</text>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="1618" x2="1754" y1="377" y2="377"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="362" x2="362" y1="609" y2="662"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="264" x2="264" y1="609" y2="662"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="264" x2="362" y1="662" y2="662"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="300" x2="343" y1="618" y2="618"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="343" x2="343" y1="618" y2="653"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="343" x2="301" y1="653" y2="653"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="346" x2="340" y1="630" y2="630"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="300" x2="300" y1="658" y2="647"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="300" x2="300" y1="613" y2="624"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="287" x2="287" y1="627" y2="646"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="280" x2="280" y1="632" y2="643"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="280" x2="272" y1="637" y2="637"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="300" x2="287" y1="647" y2="639"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="300" x2="287" y1="624" y2="631"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="264" x2="263" y1="636" y2="636"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="387" x2="362" y1="658" y2="658"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="362" x2="387" y1="614" y2="614"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="387" x2="387" y1="614" y2="629"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="387" x2="387" y1="642" y2="658"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="373" x2="411" y1="642" y2="642"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="264" x2="362" y1="609" y2="609"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-263200">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1908.000000 18.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42891" ObjectName="SW-CX_WDGF.CX_WDGF_368XC"/>
     <cge:Meas_Ref ObjectId="263200"/>
    <cge:TPSR_Ref TObjectID="42891"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-263200">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1907.772152 107.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42892" ObjectName="SW-CX_WDGF.CX_WDGF_368XC1"/>
     <cge:Meas_Ref ObjectId="263200"/>
    <cge:TPSR_Ref TObjectID="42892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-263188">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 2003.227848 -217.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42895" ObjectName="SW-CX_WDGF.CX_WDGF_301XC1"/>
     <cge:Meas_Ref ObjectId="263188"/>
    <cge:TPSR_Ref TObjectID="42895"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-263188">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 2003.000000 -127.811765)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48147" ObjectName="SW-CX_WDGF.CX_WDGF_301XC"/>
     <cge:Meas_Ref ObjectId="263188"/>
    <cge:TPSR_Ref TObjectID="48147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-263182">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1988.000000 -459.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48142" ObjectName="SW-CX_WDGF.CX_WDGF_2016SW"/>
     <cge:Meas_Ref ObjectId="263182"/>
    <cge:TPSR_Ref TObjectID="48142"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-263181">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1988.000000 -605.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48141" ObjectName="SW-CX_WDGF.CX_WDGF_2011SW"/>
     <cge:Meas_Ref ObjectId="263181"/>
    <cge:TPSR_Ref TObjectID="48141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-263185">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1920.000000 -397.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48145" ObjectName="SW-CX_WDGF.CX_WDGF_20167SW"/>
     <cge:Meas_Ref ObjectId="263185"/>
    <cge:TPSR_Ref TObjectID="48145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-263184">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2017.000000 -422.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48144" ObjectName="SW-CX_WDGF.CX_WDGF_20160SW"/>
     <cge:Meas_Ref ObjectId="263184"/>
    <cge:TPSR_Ref TObjectID="48144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-263183">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2014.000000 -570.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48143" ObjectName="SW-CX_WDGF.CX_WDGF_20117SW"/>
     <cge:Meas_Ref ObjectId="263183"/>
    <cge:TPSR_Ref TObjectID="48143"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-263176">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 2054.000000 -701.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48137" ObjectName="SW-CX_WDGF.CX_WDGF_2311SW"/>
     <cge:Meas_Ref ObjectId="263176"/>
    <cge:TPSR_Ref TObjectID="48137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309289">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2075.000000 -756.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48148" ObjectName="SW-CX_WDGF.CX_WDGF_23117SW"/>
     <cge:Meas_Ref ObjectId="309289"/>
    <cge:TPSR_Ref TObjectID="48148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-263175">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 2054.000000 -833.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48086" ObjectName="SW-CX_WDGF.CX_WDGF_2316SW"/>
     <cge:Meas_Ref ObjectId="263175"/>
    <cge:TPSR_Ref TObjectID="48086"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-263174">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2076.000000 -889.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48085" ObjectName="SW-CX_WDGF.CX_WDGF_23160SW"/>
     <cge:Meas_Ref ObjectId="263174"/>
    <cge:TPSR_Ref TObjectID="48085"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309290">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2076.000000 -941.000000)" xlink:href="#switch2:shape43_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48087" ObjectName="SW-CX_WDGF.CX_WDGF_23167SW"/>
     <cge:Meas_Ref ObjectId="309290"/>
    <cge:TPSR_Ref TObjectID="48087"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-263179">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1590.000000 -720.000000)" xlink:href="#switch2:shape44_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48140" ObjectName="SW-CX_WDGF.CX_WDGF_29010SW"/>
     <cge:Meas_Ref ObjectId="263179"/>
    <cge:TPSR_Ref TObjectID="48140"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-263177">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1662.000000 -746.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48138" ObjectName="SW-CX_WDGF.CX_WDGF_2901BK"/>
     <cge:Meas_Ref ObjectId="263177"/>
    <cge:TPSR_Ref TObjectID="48138"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-263178">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1682.000000 -802.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48139" ObjectName="SW-CX_WDGF.CX_WDGF_29017SW"/>
     <cge:Meas_Ref ObjectId="263178"/>
    <cge:TPSR_Ref TObjectID="48139"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-263202">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1736.000000 -71.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42894" ObjectName="SW-CX_WDGF.CX_WDGF_3901SW"/>
     <cge:Meas_Ref ObjectId="263202"/>
    <cge:TPSR_Ref TObjectID="42894"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-263201">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1988.000000 187.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42893" ObjectName="SW-CX_WDGF.CX_WDGF_36867SW"/>
     <cge:Meas_Ref ObjectId="263201"/>
    <cge:TPSR_Ref TObjectID="42893"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309313">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2054.000000 15.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48105" ObjectName="SW-CX_WDGF.CX_WDGF_367XC"/>
     <cge:Meas_Ref ObjectId="309313"/>
    <cge:TPSR_Ref TObjectID="48105"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309313">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2054.772152 101.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48106" ObjectName="SW-CX_WDGF.CX_WDGF_367XC1"/>
     <cge:Meas_Ref ObjectId="309313"/>
    <cge:TPSR_Ref TObjectID="48106"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309314">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2134.000000 181.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48107" ObjectName="SW-CX_WDGF.CX_WDGF_36767SW"/>
     <cge:Meas_Ref ObjectId="309314"/>
    <cge:TPSR_Ref TObjectID="48107"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309303">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2339.000000 20.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48097" ObjectName="SW-CX_WDGF.CX_WDGF_365XC"/>
     <cge:Meas_Ref ObjectId="309303"/>
    <cge:TPSR_Ref TObjectID="48097"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309303">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2339.772152 106.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48098" ObjectName="SW-CX_WDGF.CX_WDGF_365XC1"/>
     <cge:Meas_Ref ObjectId="309303"/>
    <cge:TPSR_Ref TObjectID="48098"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309304">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2419.000000 186.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48099" ObjectName="SW-CX_WDGF.CX_WDGF_36567SW"/>
     <cge:Meas_Ref ObjectId="309304"/>
    <cge:TPSR_Ref TObjectID="48099"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309323">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1629.000000 19.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48113" ObjectName="SW-CX_WDGF.CX_WDGF_370XC"/>
     <cge:Meas_Ref ObjectId="309323"/>
    <cge:TPSR_Ref TObjectID="48113"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309323">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1629.772152 105.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48114" ObjectName="SW-CX_WDGF.CX_WDGF_370XC1"/>
     <cge:Meas_Ref ObjectId="309323"/>
    <cge:TPSR_Ref TObjectID="48114"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309324">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1709.000000 185.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48115" ObjectName="SW-CX_WDGF.CX_WDGF_37067SW"/>
     <cge:Meas_Ref ObjectId="309324"/>
    <cge:TPSR_Ref TObjectID="48115"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309318">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1769.000000 16.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48109" ObjectName="SW-CX_WDGF.CX_WDGF_369XC"/>
     <cge:Meas_Ref ObjectId="309318"/>
    <cge:TPSR_Ref TObjectID="48109"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309318">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1769.772152 102.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48110" ObjectName="SW-CX_WDGF.CX_WDGF_369XC1"/>
     <cge:Meas_Ref ObjectId="309318"/>
    <cge:TPSR_Ref TObjectID="48110"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309319">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1849.000000 182.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48111" ObjectName="SW-CX_WDGF.CX_WDGF_36967SW"/>
     <cge:Meas_Ref ObjectId="309319"/>
    <cge:TPSR_Ref TObjectID="48111"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309308">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2200.000000 22.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48101" ObjectName="SW-CX_WDGF.CX_WDGF_366XC"/>
     <cge:Meas_Ref ObjectId="309308"/>
    <cge:TPSR_Ref TObjectID="48101"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309308">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2200.772152 108.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48102" ObjectName="SW-CX_WDGF.CX_WDGF_366XC1"/>
     <cge:Meas_Ref ObjectId="309308"/>
    <cge:TPSR_Ref TObjectID="48102"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309309">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2280.000000 188.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48103" ObjectName="SW-CX_WDGF.CX_WDGF_36667SW"/>
     <cge:Meas_Ref ObjectId="309309"/>
    <cge:TPSR_Ref TObjectID="48103"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309298">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2476.000000 25.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48093" ObjectName="SW-CX_WDGF.CX_WDGF_364XC"/>
     <cge:Meas_Ref ObjectId="309298"/>
    <cge:TPSR_Ref TObjectID="48093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309298">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2476.772152 111.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48094" ObjectName="SW-CX_WDGF.CX_WDGF_364XC1"/>
     <cge:Meas_Ref ObjectId="309298"/>
    <cge:TPSR_Ref TObjectID="48094"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309299">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2556.000000 191.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48095" ObjectName="SW-CX_WDGF.CX_WDGF_36467SW"/>
     <cge:Meas_Ref ObjectId="309299"/>
    <cge:TPSR_Ref TObjectID="48095"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309293">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2620.000000 23.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48089" ObjectName="SW-CX_WDGF.CX_WDGF_363XC"/>
     <cge:Meas_Ref ObjectId="309293"/>
    <cge:TPSR_Ref TObjectID="48089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309293">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2620.772152 109.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48090" ObjectName="SW-CX_WDGF.CX_WDGF_363XC1"/>
     <cge:Meas_Ref ObjectId="309293"/>
    <cge:TPSR_Ref TObjectID="48090"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309294">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2700.000000 189.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48091" ObjectName="SW-CX_WDGF.CX_WDGF_36367SW"/>
     <cge:Meas_Ref ObjectId="309294"/>
    <cge:TPSR_Ref TObjectID="48091"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-263195">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2765.000000 22.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42887" ObjectName="SW-CX_WDGF.CX_WDGF_362XC"/>
     <cge:Meas_Ref ObjectId="263195"/>
    <cge:TPSR_Ref TObjectID="42887"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-263195">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2765.772152 108.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42888" ObjectName="SW-CX_WDGF.CX_WDGF_362XC1"/>
     <cge:Meas_Ref ObjectId="263195"/>
    <cge:TPSR_Ref TObjectID="42888"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-263196">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2845.000000 188.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42889" ObjectName="SW-CX_WDGF.CX_WDGF_36267SW"/>
     <cge:Meas_Ref ObjectId="263196"/>
    <cge:TPSR_Ref TObjectID="42889"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-263190">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2903.000000 21.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42883" ObjectName="SW-CX_WDGF.CX_WDGF_361XC"/>
     <cge:Meas_Ref ObjectId="263190"/>
    <cge:TPSR_Ref TObjectID="42883"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-263190">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2903.772152 107.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42884" ObjectName="SW-CX_WDGF.CX_WDGF_361XC1"/>
     <cge:Meas_Ref ObjectId="263190"/>
    <cge:TPSR_Ref TObjectID="42884"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-263191">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2983.000000 187.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42885" ObjectName="SW-CX_WDGF.CX_WDGF_36167SW"/>
     <cge:Meas_Ref ObjectId="263191"/>
    <cge:TPSR_Ref TObjectID="42885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-309739">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 2052.000000 -234.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48149" ObjectName="SW-CX_WDGF.CX_WDGF_3010SW"/>
     <cge:Meas_Ref ObjectId="309739"/>
    <cge:TPSR_Ref TObjectID="48149"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-263186">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 2115.000000 -371.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48146" ObjectName="SW-CX_WDGF.CX_WDGF_2010SW"/>
     <cge:Meas_Ref ObjectId="263186"/>
    <cge:TPSR_Ref TObjectID="48146"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316875">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 537.000000 15.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48869" ObjectName="SW-CX_WDGF.CX_WDGF_388XC"/>
     <cge:Meas_Ref ObjectId="316875"/>
    <cge:TPSR_Ref TObjectID="48869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316875">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 536.772152 104.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48870" ObjectName="SW-CX_WDGF.CX_WDGF_388XC1"/>
     <cge:Meas_Ref ObjectId="316875"/>
    <cge:TPSR_Ref TObjectID="48870"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316876">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 617.000000 184.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48871" ObjectName="SW-CX_WDGF.CX_WDGF_38867SW"/>
     <cge:Meas_Ref ObjectId="316876"/>
    <cge:TPSR_Ref TObjectID="48871"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316880">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 643.000000 12.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48873" ObjectName="SW-CX_WDGF.CX_WDGF_387XC"/>
     <cge:Meas_Ref ObjectId="316880"/>
    <cge:TPSR_Ref TObjectID="48873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316880">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 643.772152 98.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48874" ObjectName="SW-CX_WDGF.CX_WDGF_387XC1"/>
     <cge:Meas_Ref ObjectId="316880"/>
    <cge:TPSR_Ref TObjectID="48874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316881">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 723.000000 178.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48875" ObjectName="SW-CX_WDGF.CX_WDGF_38767SW"/>
     <cge:Meas_Ref ObjectId="316881"/>
    <cge:TPSR_Ref TObjectID="48875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316890">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 865.000000 17.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48881" ObjectName="SW-CX_WDGF.CX_WDGF_385XC"/>
     <cge:Meas_Ref ObjectId="316890"/>
    <cge:TPSR_Ref TObjectID="48881"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316890">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 865.772152 103.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48882" ObjectName="SW-CX_WDGF.CX_WDGF_385XC1"/>
     <cge:Meas_Ref ObjectId="316890"/>
    <cge:TPSR_Ref TObjectID="48882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316891">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 945.000000 183.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48883" ObjectName="SW-CX_WDGF.CX_WDGF_38567SW"/>
     <cge:Meas_Ref ObjectId="316891"/>
    <cge:TPSR_Ref TObjectID="48883"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 12.000000 15.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 12.772152 101.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 92.000000 181.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316885">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 748.000000 19.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48877" ObjectName="SW-CX_WDGF.CX_WDGF_386XC"/>
     <cge:Meas_Ref ObjectId="316885"/>
    <cge:TPSR_Ref TObjectID="48877"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316885">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 748.772152 105.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48878" ObjectName="SW-CX_WDGF.CX_WDGF_386XC1"/>
     <cge:Meas_Ref ObjectId="316885"/>
    <cge:TPSR_Ref TObjectID="48878"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316886">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 828.000000 185.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48879" ObjectName="SW-CX_WDGF.CX_WDGF_38667SW"/>
     <cge:Meas_Ref ObjectId="316886"/>
    <cge:TPSR_Ref TObjectID="48879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316895">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 988.000000 20.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48885" ObjectName="SW-CX_WDGF.CX_WDGF_384XC"/>
     <cge:Meas_Ref ObjectId="316895"/>
    <cge:TPSR_Ref TObjectID="48885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316895">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 988.772152 106.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48886" ObjectName="SW-CX_WDGF.CX_WDGF_384XC1"/>
     <cge:Meas_Ref ObjectId="316895"/>
    <cge:TPSR_Ref TObjectID="48886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316896">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1068.000000 186.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48887" ObjectName="SW-CX_WDGF.CX_WDGF_38467SW"/>
     <cge:Meas_Ref ObjectId="316896"/>
    <cge:TPSR_Ref TObjectID="48887"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316900">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1109.000000 17.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48889" ObjectName="SW-CX_WDGF.CX_WDGF_383XC"/>
     <cge:Meas_Ref ObjectId="316900"/>
    <cge:TPSR_Ref TObjectID="48889"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316900">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1109.772152 103.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48890" ObjectName="SW-CX_WDGF.CX_WDGF_383XC1"/>
     <cge:Meas_Ref ObjectId="316900"/>
    <cge:TPSR_Ref TObjectID="48890"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316901">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1189.000000 183.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48891" ObjectName="SW-CX_WDGF.CX_WDGF_38367SW"/>
     <cge:Meas_Ref ObjectId="316901"/>
    <cge:TPSR_Ref TObjectID="48891"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316921">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1209.000000 18.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48908" ObjectName="SW-CX_WDGF.CX_WDGF_382XC"/>
     <cge:Meas_Ref ObjectId="316921"/>
    <cge:TPSR_Ref TObjectID="48908"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316921">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1209.772152 104.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48909" ObjectName="SW-CX_WDGF.CX_WDGF_382XC1"/>
     <cge:Meas_Ref ObjectId="316921"/>
    <cge:TPSR_Ref TObjectID="48909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316922">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1289.000000 184.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48910" ObjectName="SW-CX_WDGF.CX_WDGF_38267SW"/>
     <cge:Meas_Ref ObjectId="316922"/>
    <cge:TPSR_Ref TObjectID="48910"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316905">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1326.000000 17.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48893" ObjectName="SW-CX_WDGF.CX_WDGF_381XC"/>
     <cge:Meas_Ref ObjectId="316905"/>
    <cge:TPSR_Ref TObjectID="48893"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316905">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1326.772152 103.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48894" ObjectName="SW-CX_WDGF.CX_WDGF_381XC1"/>
     <cge:Meas_Ref ObjectId="316905"/>
    <cge:TPSR_Ref TObjectID="48894"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316906">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1406.000000 183.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48895" ObjectName="SW-CX_WDGF.CX_WDGF_38167SW"/>
     <cge:Meas_Ref ObjectId="316906"/>
    <cge:TPSR_Ref TObjectID="48895"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1049.000000 -710.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1070.000000 -765.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1049.000000 -842.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1071.000000 -898.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1071.000000 -950.000000)" xlink:href="#switch2:shape43_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316867">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1186.227848 -213.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48862" ObjectName="SW-CX_WDGF.CX_WDGF_302XC1"/>
     <cge:Meas_Ref ObjectId="316867"/>
    <cge:TPSR_Ref TObjectID="48862"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316867">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1186.000000 -123.811765)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48861" ObjectName="SW-CX_WDGF.CX_WDGF_302XC"/>
     <cge:Meas_Ref ObjectId="316867"/>
    <cge:TPSR_Ref TObjectID="48861"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316861">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1171.000000 -439.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48855" ObjectName="SW-CX_WDGF.CX_WDGF_2026SW"/>
     <cge:Meas_Ref ObjectId="316861"/>
    <cge:TPSR_Ref TObjectID="48855"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316860">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1171.000000 -601.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48854" ObjectName="SW-CX_WDGF.CX_WDGF_2021SW"/>
     <cge:Meas_Ref ObjectId="316860"/>
    <cge:TPSR_Ref TObjectID="48854"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316864">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1105.000000 -415.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48858" ObjectName="SW-CX_WDGF.CX_WDGF_20267SW"/>
     <cge:Meas_Ref ObjectId="316864"/>
    <cge:TPSR_Ref TObjectID="48858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316863">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1203.000000 -492.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48857" ObjectName="SW-CX_WDGF.CX_WDGF_20260SW"/>
     <cge:Meas_Ref ObjectId="316863"/>
    <cge:TPSR_Ref TObjectID="48857"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316862">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1197.000000 -566.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48856" ObjectName="SW-CX_WDGF.CX_WDGF_20217SW"/>
     <cge:Meas_Ref ObjectId="316862"/>
    <cge:TPSR_Ref TObjectID="48856"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316868">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1235.000000 -230.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48863" ObjectName="SW-CX_WDGF.CX_WDGF_3020SW"/>
     <cge:Meas_Ref ObjectId="316868"/>
    <cge:TPSR_Ref TObjectID="48863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316865">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1298.000000 -367.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48859" ObjectName="SW-CX_WDGF.CX_WDGF_2020SW"/>
     <cge:Meas_Ref ObjectId="316865"/>
    <cge:TPSR_Ref TObjectID="48859"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316910">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1422.000000 18.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48897" ObjectName="SW-CX_WDGF.CX_WDGF_312XC"/>
     <cge:Meas_Ref ObjectId="316910"/>
    <cge:TPSR_Ref TObjectID="48897"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316910">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1421.772152 107.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48898" ObjectName="SW-CX_WDGF.CX_WDGF_312XC1"/>
     <cge:Meas_Ref ObjectId="316910"/>
    <cge:TPSR_Ref TObjectID="48898"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316911">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1541.000000 21.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48899" ObjectName="SW-CX_WDGF.CX_WDGF_3121XC"/>
     <cge:Meas_Ref ObjectId="316911"/>
    <cge:TPSR_Ref TObjectID="48899"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316911">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1540.772152 110.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48900" ObjectName="SW-CX_WDGF.CX_WDGF_3121XC1"/>
     <cge:Meas_Ref ObjectId="316911"/>
    <cge:TPSR_Ref TObjectID="48900"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316870">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 420.000000 17.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48865" ObjectName="SW-CX_WDGF.CX_WDGF_389XC"/>
     <cge:Meas_Ref ObjectId="316870"/>
    <cge:TPSR_Ref TObjectID="48865"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316870">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 419.772152 106.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48866" ObjectName="SW-CX_WDGF.CX_WDGF_389XC1"/>
     <cge:Meas_Ref ObjectId="316870"/>
    <cge:TPSR_Ref TObjectID="48866"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316871">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 500.000000 186.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48867" ObjectName="SW-CX_WDGF.CX_WDGF_38967SW"/>
     <cge:Meas_Ref ObjectId="316871"/>
    <cge:TPSR_Ref TObjectID="48867"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316915">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 304.867233 98.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48903" ObjectName="SW-CX_WDGF.CX_WDGF_390XC1"/>
     <cge:Meas_Ref ObjectId="316915"/>
    <cge:TPSR_Ref TObjectID="48903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316915">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 304.867233 32.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48902" ObjectName="SW-CX_WDGF.CX_WDGF_390XC"/>
     <cge:Meas_Ref ObjectId="316915"/>
    <cge:TPSR_Ref TObjectID="48902"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316916">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 396.740650 185.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48904" ObjectName="SW-CX_WDGF.CX_WDGF_39067SW"/>
     <cge:Meas_Ref ObjectId="316916"/>
    <cge:TPSR_Ref TObjectID="48904"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 320.740650 357.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 231.000000 370.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316919">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 192.000000 30.058824)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48905" ObjectName="SW-CX_WDGF.CX_WDGF_3902XC"/>
     <cge:Meas_Ref ObjectId="316919"/>
    <cge:TPSR_Ref TObjectID="48905"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316919">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 191.772152 83.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48906" ObjectName="SW-CX_WDGF.CX_WDGF_3902XC1"/>
     <cge:Meas_Ref ObjectId="316919"/>
    <cge:TPSR_Ref TObjectID="48906"/></metadata>
   </g>
  </g><g id="Reactance_Layer">
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 302.740650 557.000000)" xlink:href="#reactance:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2787b40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1732.000000 -164.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27ff990">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1954.000000 196.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b70760">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1705.000000 -196.188235)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b1dee0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1952.000000 -180.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b20080">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2050.000000 -985.000000)" xlink:href="#lightningRod:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ad04f0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2081.000000 -1087.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a99a60">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1690.000000 -856.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a2c1c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.484900 1913.000000 187.820513)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a2d1a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2100.000000 193.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b177b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.484900 2060.000000 184.820513)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b82a30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2385.000000 198.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b008a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.484900 2345.000000 189.820513)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b025f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1675.000000 197.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a50250">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.484900 1635.000000 188.820513)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a51130">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1623.000000 247.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a08760">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1815.000000 194.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a39cc0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.484900 1775.000000 185.820513)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a68130">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1763.000000 244.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a6a6e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2246.000000 200.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29efa00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.484900 2206.000000 191.820513)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29f1750">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2522.000000 203.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a63f50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.484900 2482.000000 194.820513)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ad6370">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2666.000000 201.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29f2390">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.484900 2626.000000 192.820513)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29f40e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2811.000000 200.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29fc7f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.484900 2771.000000 191.820513)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29fe540">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2949.000000 199.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a43300">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.484900 2909.000000 190.820513)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a79010">
    <use class="BV-220KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 2142.000000 -368.000000)" xlink:href="#lightningRod:shape126"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a79660">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2054.000000 -193.000000)" xlink:href="#lightningRod:shape174"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a7aca0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2171.000000 -368.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2984040">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2068.000000 -285.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2985ab0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1885.000000 -247.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29a9450">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 583.000000 193.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29b6020">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.484900 542.000000 184.820513)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a14e80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 689.000000 190.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a21590">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.484900 649.000000 181.820513)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a226d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 911.000000 195.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bc4140">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.484900 871.000000 186.820513)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bc5c30">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 58.000000 193.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bd29a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.484900 18.000000 184.820513)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bd5300">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 794.000000 197.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2998880">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.484900 754.000000 188.820513)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_299a370">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1034.000000 198.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_295aac0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.484900 994.000000 189.820513)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_295c5b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1155.000000 195.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2968cc0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.484900 1115.000000 186.820513)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_296a7b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1255.000000 196.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2976ec0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.484900 1215.000000 187.820513)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29789b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1372.000000 195.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c94dd0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.484900 1332.000000 186.820513)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cb2590">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1076.000000 -1096.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cc6e20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1135.000000 -176.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cd79c0">
    <use class="BV-220KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1325.000000 -364.000000)" xlink:href="#lightningRod:shape126"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cd8230">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1237.000000 -189.000000)" xlink:href="#lightningRod:shape174"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cd98f0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1354.000000 -364.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cdeae0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1251.000000 -281.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ce6f50">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1192.000000 -394.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cfeb10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 466.000000 195.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d0abe0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.484900 425.000000 186.820513)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d160d0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 363.740650 206.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d1a300">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.622222 309.740650 211.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d1af80">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 323.740650 275.000000)" xlink:href="#lightningRod:shape194"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d224f0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 239.740650 392.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d2b440">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 193.000000 153.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d2c070">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 163.740650 155.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3079e70">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1068.000000 -245.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-309353" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 826.000000 -780.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309353" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48119"/>
     <cge:Term_Ref ObjectID="47018"/>
    <cge:TPSR_Ref TObjectID="48119"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-309354" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 826.000000 -780.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309354" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48119"/>
     <cge:Term_Ref ObjectID="47018"/>
    <cge:TPSR_Ref TObjectID="48119"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-309355" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 826.000000 -780.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309355" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48119"/>
     <cge:Term_Ref ObjectID="47018"/>
    <cge:TPSR_Ref TObjectID="48119"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-309356" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 826.000000 -780.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309356" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48119"/>
     <cge:Term_Ref ObjectID="47018"/>
    <cge:TPSR_Ref TObjectID="48119"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-309350" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 826.000000 -780.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309350" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48119"/>
     <cge:Term_Ref ObjectID="47018"/>
    <cge:TPSR_Ref TObjectID="48119"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-309349" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 826.000000 -780.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309349" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48119"/>
     <cge:Term_Ref ObjectID="47018"/>
    <cge:TPSR_Ref TObjectID="48119"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-263160" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3001.000000 -129.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="263160" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42880"/>
     <cge:Term_Ref ObjectID="18774"/>
    <cge:TPSR_Ref TObjectID="42880"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-263161" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3001.000000 -129.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="263161" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42880"/>
     <cge:Term_Ref ObjectID="18774"/>
    <cge:TPSR_Ref TObjectID="42880"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-263162" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3001.000000 -129.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="263162" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42880"/>
     <cge:Term_Ref ObjectID="18774"/>
    <cge:TPSR_Ref TObjectID="42880"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-263163" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3001.000000 -129.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="263163" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42880"/>
     <cge:Term_Ref ObjectID="18774"/>
    <cge:TPSR_Ref TObjectID="42880"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-263157" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3001.000000 -129.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="263157" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42880"/>
     <cge:Term_Ref ObjectID="18774"/>
    <cge:TPSR_Ref TObjectID="42880"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-263156" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3001.000000 -129.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="263156" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42880"/>
     <cge:Term_Ref ObjectID="18774"/>
    <cge:TPSR_Ref TObjectID="42880"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-309454" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1604.000000 282.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309454" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48112"/>
     <cge:Term_Ref ObjectID="47010"/>
    <cge:TPSR_Ref TObjectID="48112"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-309455" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1604.000000 282.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309455" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48112"/>
     <cge:Term_Ref ObjectID="47010"/>
    <cge:TPSR_Ref TObjectID="48112"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-309451" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1604.000000 282.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309451" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48112"/>
     <cge:Term_Ref ObjectID="47010"/>
    <cge:TPSR_Ref TObjectID="48112"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-309448" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1755.000000 282.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309448" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48108"/>
     <cge:Term_Ref ObjectID="47002"/>
    <cge:TPSR_Ref TObjectID="48108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-309449" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1755.000000 282.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309449" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48108"/>
     <cge:Term_Ref ObjectID="47002"/>
    <cge:TPSR_Ref TObjectID="48108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-309445" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1755.000000 282.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309445" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48108"/>
     <cge:Term_Ref ObjectID="47002"/>
    <cge:TPSR_Ref TObjectID="48108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-263153" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1898.000000 283.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="263153" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42890"/>
     <cge:Term_Ref ObjectID="18793"/>
    <cge:TPSR_Ref TObjectID="42890"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-263154" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1898.000000 283.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="263154" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42890"/>
     <cge:Term_Ref ObjectID="18793"/>
    <cge:TPSR_Ref TObjectID="42890"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-263150" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1898.000000 283.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="263150" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42890"/>
     <cge:Term_Ref ObjectID="18793"/>
    <cge:TPSR_Ref TObjectID="42890"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-309442" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2044.000000 283.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309442" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48104"/>
     <cge:Term_Ref ObjectID="46994"/>
    <cge:TPSR_Ref TObjectID="48104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-309443" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2044.000000 283.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309443" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48104"/>
     <cge:Term_Ref ObjectID="46994"/>
    <cge:TPSR_Ref TObjectID="48104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-309433" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2044.000000 283.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309433" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48104"/>
     <cge:Term_Ref ObjectID="46994"/>
    <cge:TPSR_Ref TObjectID="48104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-309430" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2190.000000 285.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309430" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48100"/>
     <cge:Term_Ref ObjectID="46986"/>
    <cge:TPSR_Ref TObjectID="48100"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-309431" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2190.000000 285.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309431" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48100"/>
     <cge:Term_Ref ObjectID="46986"/>
    <cge:TPSR_Ref TObjectID="48100"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-309421" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2190.000000 285.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309421" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48100"/>
     <cge:Term_Ref ObjectID="46986"/>
    <cge:TPSR_Ref TObjectID="48100"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-309418" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2332.000000 285.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309418" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48096"/>
     <cge:Term_Ref ObjectID="46978"/>
    <cge:TPSR_Ref TObjectID="48096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-309419" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2332.000000 285.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309419" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48096"/>
     <cge:Term_Ref ObjectID="46978"/>
    <cge:TPSR_Ref TObjectID="48096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-309409" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2332.000000 285.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309409" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48096"/>
     <cge:Term_Ref ObjectID="46978"/>
    <cge:TPSR_Ref TObjectID="48096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-309406" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2466.000000 285.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309406" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48092"/>
     <cge:Term_Ref ObjectID="46970"/>
    <cge:TPSR_Ref TObjectID="48092"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-309407" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2466.000000 285.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309407" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48092"/>
     <cge:Term_Ref ObjectID="46970"/>
    <cge:TPSR_Ref TObjectID="48092"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-309397" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2466.000000 285.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309397" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48092"/>
     <cge:Term_Ref ObjectID="46970"/>
    <cge:TPSR_Ref TObjectID="48092"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-309394" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2609.000000 285.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309394" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48088"/>
     <cge:Term_Ref ObjectID="46962"/>
    <cge:TPSR_Ref TObjectID="48088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-309395" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2609.000000 285.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309395" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48088"/>
     <cge:Term_Ref ObjectID="46962"/>
    <cge:TPSR_Ref TObjectID="48088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-309385" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2609.000000 285.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309385" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48088"/>
     <cge:Term_Ref ObjectID="46962"/>
    <cge:TPSR_Ref TObjectID="48088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-263147" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2764.000000 285.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="263147" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42886"/>
     <cge:Term_Ref ObjectID="18785"/>
    <cge:TPSR_Ref TObjectID="42886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-263148" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2764.000000 285.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="263148" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42886"/>
     <cge:Term_Ref ObjectID="18785"/>
    <cge:TPSR_Ref TObjectID="42886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-263144" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2764.000000 285.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="263144" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42886"/>
     <cge:Term_Ref ObjectID="18785"/>
    <cge:TPSR_Ref TObjectID="42886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-263141" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2895.000000 285.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="263141" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42882"/>
     <cge:Term_Ref ObjectID="18777"/>
    <cge:TPSR_Ref TObjectID="42882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-263142" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2895.000000 285.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="263142" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42882"/>
     <cge:Term_Ref ObjectID="18777"/>
    <cge:TPSR_Ref TObjectID="42882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-263132" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2895.000000 285.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="263132" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="42882"/>
     <cge:Term_Ref ObjectID="18777"/>
    <cge:TPSR_Ref TObjectID="42882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-309346" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2193.000000 -832.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309346" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48084"/>
     <cge:Term_Ref ObjectID="46954"/>
    <cge:TPSR_Ref TObjectID="48084"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-309347" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2193.000000 -832.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309347" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48084"/>
     <cge:Term_Ref ObjectID="46954"/>
    <cge:TPSR_Ref TObjectID="48084"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-309283" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2193.000000 -832.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309283" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48084"/>
     <cge:Term_Ref ObjectID="46954"/>
    <cge:TPSR_Ref TObjectID="48084"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-309366" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2116.000000 -543.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309366" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48120"/>
     <cge:Term_Ref ObjectID="47019"/>
    <cge:TPSR_Ref TObjectID="48120"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-309367" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2116.000000 -543.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309367" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48120"/>
     <cge:Term_Ref ObjectID="47019"/>
    <cge:TPSR_Ref TObjectID="48120"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-309357" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2116.000000 -543.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309357" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48120"/>
     <cge:Term_Ref ObjectID="47019"/>
    <cge:TPSR_Ref TObjectID="48120"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-309378" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2129.000000 -160.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309378" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48121"/>
     <cge:Term_Ref ObjectID="47021"/>
    <cge:TPSR_Ref TObjectID="48121"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-309379" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2129.000000 -160.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309379" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48121"/>
     <cge:Term_Ref ObjectID="47021"/>
    <cge:TPSR_Ref TObjectID="48121"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-309369" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2129.000000 -160.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="309369" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48121"/>
     <cge:Term_Ref ObjectID="47021"/>
    <cge:TPSR_Ref TObjectID="48121"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-316958" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 403.000000 301.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316958" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48864"/>
     <cge:Term_Ref ObjectID="48149"/>
    <cge:TPSR_Ref TObjectID="48864"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-316959" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 403.000000 301.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316959" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48864"/>
     <cge:Term_Ref ObjectID="48149"/>
    <cge:TPSR_Ref TObjectID="48864"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-316949" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 403.000000 301.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316949" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48864"/>
     <cge:Term_Ref ObjectID="48149"/>
    <cge:TPSR_Ref TObjectID="48864"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-316970" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 527.000000 302.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316970" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48868"/>
     <cge:Term_Ref ObjectID="48157"/>
    <cge:TPSR_Ref TObjectID="48868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-316971" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 527.000000 302.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316971" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48868"/>
     <cge:Term_Ref ObjectID="48157"/>
    <cge:TPSR_Ref TObjectID="48868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-316961" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 527.000000 302.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316961" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48868"/>
     <cge:Term_Ref ObjectID="48157"/>
    <cge:TPSR_Ref TObjectID="48868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-316982" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 633.000000 300.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316982" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48872"/>
     <cge:Term_Ref ObjectID="48165"/>
    <cge:TPSR_Ref TObjectID="48872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-316983" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 633.000000 300.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316983" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48872"/>
     <cge:Term_Ref ObjectID="48165"/>
    <cge:TPSR_Ref TObjectID="48872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-316973" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 633.000000 300.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316973" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48872"/>
     <cge:Term_Ref ObjectID="48165"/>
    <cge:TPSR_Ref TObjectID="48872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-316994" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 739.000000 296.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316994" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48876"/>
     <cge:Term_Ref ObjectID="48173"/>
    <cge:TPSR_Ref TObjectID="48876"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-316995" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 739.000000 296.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316995" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48876"/>
     <cge:Term_Ref ObjectID="48173"/>
    <cge:TPSR_Ref TObjectID="48876"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-316985" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 739.000000 296.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316985" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48876"/>
     <cge:Term_Ref ObjectID="48173"/>
    <cge:TPSR_Ref TObjectID="48876"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-317006" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 851.000000 299.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317006" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48880"/>
     <cge:Term_Ref ObjectID="48181"/>
    <cge:TPSR_Ref TObjectID="48880"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-317007" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 851.000000 299.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317007" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48880"/>
     <cge:Term_Ref ObjectID="48181"/>
    <cge:TPSR_Ref TObjectID="48880"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-316997" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 851.000000 299.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316997" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48880"/>
     <cge:Term_Ref ObjectID="48181"/>
    <cge:TPSR_Ref TObjectID="48880"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-317018" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 977.000000 302.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317018" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48884"/>
     <cge:Term_Ref ObjectID="48189"/>
    <cge:TPSR_Ref TObjectID="48884"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-317019" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 977.000000 302.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317019" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48884"/>
     <cge:Term_Ref ObjectID="48189"/>
    <cge:TPSR_Ref TObjectID="48884"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-317009" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 977.000000 302.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317009" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48884"/>
     <cge:Term_Ref ObjectID="48189"/>
    <cge:TPSR_Ref TObjectID="48884"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-317030" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1098.000000 302.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317030" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48888"/>
     <cge:Term_Ref ObjectID="48197"/>
    <cge:TPSR_Ref TObjectID="48888"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-317031" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1098.000000 302.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317031" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48888"/>
     <cge:Term_Ref ObjectID="48197"/>
    <cge:TPSR_Ref TObjectID="48888"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-317021" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1098.000000 302.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317021" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48888"/>
     <cge:Term_Ref ObjectID="48197"/>
    <cge:TPSR_Ref TObjectID="48888"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-317086" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1206.000000 300.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317086" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48907"/>
     <cge:Term_Ref ObjectID="48235"/>
    <cge:TPSR_Ref TObjectID="48907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-317087" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1206.000000 300.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317087" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48907"/>
     <cge:Term_Ref ObjectID="48235"/>
    <cge:TPSR_Ref TObjectID="48907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-317077" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1206.000000 300.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317077" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48907"/>
     <cge:Term_Ref ObjectID="48235"/>
    <cge:TPSR_Ref TObjectID="48907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-317042" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1373.000000 288.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317042" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48892"/>
     <cge:Term_Ref ObjectID="48205"/>
    <cge:TPSR_Ref TObjectID="48892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-317043" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1373.000000 288.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317043" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48892"/>
     <cge:Term_Ref ObjectID="48205"/>
    <cge:TPSR_Ref TObjectID="48892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-317033" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1373.000000 288.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317033" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48892"/>
     <cge:Term_Ref ObjectID="48205"/>
    <cge:TPSR_Ref TObjectID="48892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-317054" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1513.000000 129.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317054" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48896"/>
     <cge:Term_Ref ObjectID="48213"/>
    <cge:TPSR_Ref TObjectID="48896"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-317055" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1513.000000 129.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317055" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48896"/>
     <cge:Term_Ref ObjectID="48213"/>
    <cge:TPSR_Ref TObjectID="48896"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-317045" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1513.000000 129.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317045" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48896"/>
     <cge:Term_Ref ObjectID="48213"/>
    <cge:TPSR_Ref TObjectID="48896"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-316933" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1373.000000 -159.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316933" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48860"/>
     <cge:Term_Ref ObjectID="48141"/>
    <cge:TPSR_Ref TObjectID="48860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-316934" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1373.000000 -159.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316934" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48860"/>
     <cge:Term_Ref ObjectID="48141"/>
    <cge:TPSR_Ref TObjectID="48860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-316936" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1373.000000 -159.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="316936" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48860"/>
     <cge:Term_Ref ObjectID="48141"/>
    <cge:TPSR_Ref TObjectID="48860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-317066" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 194.000000 301.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317066" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48901"/>
     <cge:Term_Ref ObjectID="48223"/>
    <cge:TPSR_Ref TObjectID="48901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-317067" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 194.000000 301.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317067" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48901"/>
     <cge:Term_Ref ObjectID="48223"/>
    <cge:TPSR_Ref TObjectID="48901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-317057" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 194.000000 301.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317057" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48901"/>
     <cge:Term_Ref ObjectID="48223"/>
    <cge:TPSR_Ref TObjectID="48901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-317089" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1381.000000 -540.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317089" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48853"/>
     <cge:Term_Ref ObjectID="48093"/>
    <cge:TPSR_Ref TObjectID="48853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-317090" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1381.000000 -540.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317090" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48853"/>
     <cge:Term_Ref ObjectID="48093"/>
    <cge:TPSR_Ref TObjectID="48853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-317092" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1381.000000 -540.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317092" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="48853"/>
     <cge:Term_Ref ObjectID="48093"/>
    <cge:TPSR_Ref TObjectID="48853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-317069" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 62.000000 -142.000000) translate(0,12)">317069.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317069" ObjectName="CX_WDGF.CX_WDGF_3ⅡM:F"/>
     <cge:PSR_Ref ObjectID="48911"/>
     <cge:Term_Ref ObjectID="48243"/>
    <cge:TPSR_Ref TObjectID="48911"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-317070" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 62.000000 -142.000000) translate(0,27)">317070.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317070" ObjectName="CX_WDGF.CX_WDGF_3ⅡM:F"/>
     <cge:PSR_Ref ObjectID="48911"/>
     <cge:Term_Ref ObjectID="48243"/>
    <cge:TPSR_Ref TObjectID="48911"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-317071" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 62.000000 -142.000000) translate(0,42)">317071.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317071" ObjectName="CX_WDGF.CX_WDGF_3ⅡM:F"/>
     <cge:PSR_Ref ObjectID="48911"/>
     <cge:Term_Ref ObjectID="48243"/>
    <cge:TPSR_Ref TObjectID="48911"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="1" id="ME-317075" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 62.000000 -142.000000) translate(0,57)">317075.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317075" ObjectName="CX_WDGF.CX_WDGF_3ⅡM:F"/>
     <cge:PSR_Ref ObjectID="48911"/>
     <cge:Term_Ref ObjectID="48243"/>
    <cge:TPSR_Ref TObjectID="48911"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-317072" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 62.000000 -142.000000) translate(0,72)">317072.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317072" ObjectName="CX_WDGF.CX_WDGF_3ⅡM:F"/>
     <cge:PSR_Ref ObjectID="48911"/>
     <cge:Term_Ref ObjectID="48243"/>
    <cge:TPSR_Ref TObjectID="48911"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-317076" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 62.000000 -142.000000) translate(0,87)">317076.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="317076" ObjectName="CX_WDGF.CX_WDGF_3ⅡM:F"/>
     <cge:PSR_Ref ObjectID="48911"/>
     <cge:Term_Ref ObjectID="48243"/>
    <cge:TPSR_Ref TObjectID="48911"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_省调直调电厂_光伏.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="175" x="-255" y="-1095"/></g>
   <g href="cx_索引_接线图_省调直调电厂_光伏.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-304" y="-1112"/></g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="300" cy="653" fill="rgb(60,120,255)" fillStyle="1" r="3" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="300" cy="619" fill="rgb(60,120,255)" fillStyle="1" r="3" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2b20760">
    <use class="BV-220KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 2039.000000 -1061.000000)" xlink:href="#voltageTransformer:shape40"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a9ada0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1626.000000 -891.000000)" xlink:href="#voltageTransformer:shape71"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b0aa20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1708.000000 -236.000000)" xlink:href="#voltageTransformer:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cb0b30">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 1034.000000 -1070.000000)" xlink:href="#voltageTransformer:shape40"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d2ddd0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 191.000000 208.000000)" xlink:href="#voltageTransformer:shape35"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -267.000000 -1036.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-263141" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 -234.461538 -903.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="263141" ObjectName="CX_WDGF:CX_WDGF_361BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-263142" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 -234.461538 -862.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="263142" ObjectName="CX_WDGF:CX_WDGF_361BK_Q"/>
    </metadata>
   </g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="26" stroke="rgb(255,255,0)" stroke-width="1" width="12" x="267" y="443"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="6" stroke="rgb(60,120,255)" stroke-width="1" width="39" x="372" y="629"/>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="175" x="-255" y="-1095"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="175" x="-255" y="-1095"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-304" y="-1112"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-304" y="-1112"/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1305.000000 315.000000)" xlink:href="#transformer2:shape77_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1305.000000 315.000000)" xlink:href="#transformer2:shape77_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_WDGF"/>
</svg>