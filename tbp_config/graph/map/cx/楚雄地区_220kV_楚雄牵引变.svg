<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-251" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="-348 -984 2388 1286">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape10_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="0" y1="13" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="34" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="28" y2="34"/>
   </symbol>
   <symbol id="breaker2:shape10_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="13" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="34" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="28" y2="34"/>
   </symbol>
   <symbol id="breaker2:shape10-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="0" y1="13" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="28" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="34" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="13" y2="5"/>
   </symbol>
   <symbol id="breaker2:shape10-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="13" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="34" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="28" y2="34"/>
   </symbol>
   <symbol id="capacitor:shape39">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="5" x2="15" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="55" x2="55" y1="20" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="55" x2="65" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="15" x2="15" y1="20" y2="0"/>
   </symbol>
   <symbol id="capacitor:shape42">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0375704" x1="8" x2="12" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0375704" x1="2" x2="6" y1="14" y2="14"/>
    <circle cx="10" cy="14" fillStyle="0" r="4" stroke-width="0.25"/>
    <circle cx="4" cy="14" fillStyle="0" r="4" stroke-width="0.25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0375704" x1="8" x2="12" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0375704" x1="2" x2="6" y1="26" y2="26"/>
    <circle cx="10" cy="26" fillStyle="0" r="4" stroke-width="0.25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0166979" x1="10" x2="10" y1="10" y2="8"/>
    <circle cx="4" cy="26" fillStyle="0" r="4" stroke-width="0.25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0939259" x1="10" x2="20" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0632133" x1="20" x2="20" y1="37" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0166979" x1="10" x2="10" y1="30" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0166979" x1="10" x2="10" y1="18" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0166979" x1="20" x2="10" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0751407" x1="10" x2="20" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0375704" x1="18" x2="22" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0375704" x1="18" x2="22" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0632133" x1="20" x2="20" y1="25" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0375704" x1="18" x2="22" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0375704" x1="18" x2="22" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0632133" x1="20" x2="20" y1="13" y2="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="5" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="3" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="8" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="1" x2="12" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="load:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="5" y2="29"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,19 9,31 17,19 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape81_0">
    <ellipse cx="34" cy="92" fillStyle="0" rx="33.5" ry="33" stroke-width="0.693878"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.693878" x1="35" x2="25" y1="85" y2="106"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.693878" x1="35" x2="45" y1="85" y2="106"/>
   </symbol>
   <symbol id="transformer2:shape81_1">
    <ellipse cx="34" cy="44" fillStyle="0" rx="33.5" ry="33" stroke-width="0.693878"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.693878" x1="35" x2="25" y1="32" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.693878" x1="35" x2="45" y1="32" y2="53"/>
   </symbol>
   <symbol id="voltageTransformer:shape52">
    <circle cx="27" cy="15" r="7.5" stroke-width="0.804311"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,28 13,28 13,1 40,1 40,8 " stroke-width="0.964286"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="24" x2="27" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="27" x2="27" y1="15" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="30" x2="27" y1="17" y2="15"/>
    <circle cx="27" cy="27" r="7.5" stroke-width="0.804311"/>
    <circle cx="39" cy="27" r="7.5" stroke-width="0.804311"/>
    <polyline points="27,15 6,15 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="4" x2="4" y1="12" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07362" x1="2" x2="2" y1="13" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="6" x2="6" y1="10" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="24" x2="27" y1="30" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="27" x2="27" y1="28" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="30" x2="27" y1="30" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="40" x2="40" y1="15" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="37" x2="40" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="43" x2="40" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="37" y1="30" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="41" x2="43" y1="30" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="43" x2="37" y1="26" y2="26"/>
    <circle cx="39" cy="15" r="7.5" stroke-width="0.804311"/>
   </symbol>
   <symbol id="voltageTransformer:shape124">
    <ellipse cx="33" cy="63" rx="13" ry="12.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="48" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="4" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="2" y1="45" y2="45"/>
    <ellipse cx="33" cy="80" rx="13" ry="12.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="79" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="57" y2="66"/>
    <rect height="19" stroke-width="0.311623" width="12" x="27" y="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="6" y2="51"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31b04d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31b0ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31b1860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31b2a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31b3d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31b4a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31b55a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31b5fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31b6810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31b71f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31b7ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_31b8320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31b9cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31ba8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31bb380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31bbce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31bd700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31be170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31bea30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31bf420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31c0600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31c0f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31c1a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31c6d90" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31c79e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31c3800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31c4c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31c5a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_31c8e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_31ca260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1296" width="2398" x="-353" y="-989"/>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="101,183 103,183 105,182 107,182 109,181 110,180 112,179 113,177 114,175 115,174 115,172 115,170 115,168 115,166 114,165 113,163 112,162 110,160 109,159 107,158 105,158 103,157 101,157 100,157 98,158 96,158 94,159 93,160 91,162 90,163 89,165 88,166 88,168 88,170 " stroke="rgb(60,120,255)" stroke-width="0.0972"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="225,195 226,195 226,195 227,195 228,195 228,194 229,194 230,193 230,193 230,192 231,191 231,191 231,190 231,189 231,188 231,188 230,187 230,186 230,186 229,185 228,185 228,184 227,184 226,184 226,184 225,184 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="225,206 226,206 226,206 227,206 228,206 228,205 229,205 230,204 230,204 230,203 231,202 231,202 231,201 231,200 231,199 231,199 230,198 230,197 230,197 229,196 228,196 228,195 227,195 226,195 226,195 225,195 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="225,217 226,217 226,217 227,217 228,217 228,216 229,216 230,215 230,215 230,214 231,213 231,213 231,212 231,211 231,210 231,210 230,209 230,208 230,208 229,207 228,207 228,206 227,206 226,206 226,206 225,206 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="225,228 226,228 226,228 227,228 228,228 228,227 229,227 230,226 230,226 230,225 231,224 231,224 231,223 231,222 231,221 231,221 230,220 230,219 230,219 229,218 228,218 228,217 227,217 226,217 226,217 225,217 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="361,192 362,192 362,192 363,192 364,192 364,191 365,191 366,190 366,190 366,189 367,188 367,188 367,187 367,186 367,185 367,185 366,184 366,183 366,183 365,182 364,182 364,181 363,181 362,181 362,181 361,181 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="361,203 362,203 362,203 363,203 364,203 364,202 365,202 366,201 366,201 366,200 367,199 367,199 367,198 367,197 367,196 367,196 366,195 366,194 366,194 365,193 364,193 364,192 363,192 362,192 362,192 361,192 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="361,214 362,214 362,214 363,214 364,214 364,213 365,213 366,212 366,212 366,211 367,210 367,210 367,209 367,208 367,207 367,207 366,206 366,205 366,205 365,204 364,204 364,203 363,203 362,203 362,203 361,203 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="361,225 362,225 362,225 363,225 364,225 364,224 365,224 366,223 366,223 366,222 367,221 367,221 367,220 367,219 367,218 367,218 366,217 366,216 366,216 365,215 364,215 364,214 363,214 362,214 362,214 361,214 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="574,193 575,193 575,193 576,193 577,193 577,192 578,192 579,191 579,191 579,190 580,189 580,189 580,188 580,187 580,186 580,186 579,185 579,184 579,184 578,183 577,183 577,182 576,182 575,182 575,182 574,182 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="574,204 575,204 575,204 576,204 577,204 577,203 578,203 579,202 579,202 579,201 580,200 580,200 580,199 580,198 580,197 580,197 579,196 579,195 579,195 578,194 577,194 577,193 576,193 575,193 575,193 574,193 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="574,215 575,215 575,215 576,215 577,215 577,214 578,214 579,213 579,213 579,212 580,211 580,211 580,210 580,209 580,208 580,208 579,207 579,206 579,206 578,205 577,205 577,204 576,204 575,204 575,204 574,204 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="574,226 575,226 575,226 576,226 577,226 577,225 578,225 579,224 579,224 579,223 580,222 580,222 580,221 580,220 580,219 580,219 579,218 579,217 579,217 578,216 577,216 577,215 576,215 575,215 575,215 574,215 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="752,193 753,193 753,193 754,193 755,193 755,192 756,192 757,191 757,191 757,190 758,189 758,189 758,188 758,187 758,186 758,186 757,185 757,184 757,184 756,183 755,183 755,182 754,182 753,182 753,182 752,182 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="752,204 753,204 753,204 754,204 755,204 755,203 756,203 757,202 757,202 757,201 758,200 758,200 758,199 758,198 758,197 758,197 757,196 757,195 757,195 756,194 755,194 755,193 754,193 753,193 753,193 752,193 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="752,215 753,215 753,215 754,215 755,215 755,214 756,214 757,213 757,213 757,212 758,211 758,211 758,210 758,209 758,208 758,208 757,207 757,206 757,206 756,205 755,205 755,204 754,204 753,204 753,204 752,204 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="752,226 753,226 753,226 754,226 755,226 755,225 756,225 757,224 757,224 757,223 758,222 758,222 758,221 758,220 758,219 758,219 757,218 757,217 757,217 756,216 755,216 755,215 754,215 753,215 753,215 752,215 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1384,193 1385,193 1385,193 1386,193 1387,193 1387,192 1388,192 1389,191 1389,191 1389,190 1390,189 1390,189 1390,188 1390,187 1390,186 1390,186 1389,185 1389,184 1389,184 1388,183 1387,183 1387,182 1386,182 1385,182 1385,182 1384,182 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1384,204 1385,204 1385,204 1386,204 1387,204 1387,203 1388,203 1389,202 1389,202 1389,201 1390,200 1390,200 1390,199 1390,198 1390,197 1390,197 1389,196 1389,195 1389,195 1388,194 1387,194 1387,193 1386,193 1385,193 1385,193 1384,193 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1384,215 1385,215 1385,215 1386,215 1387,215 1387,214 1388,214 1389,213 1389,213 1389,212 1390,211 1390,211 1390,210 1390,209 1390,208 1390,208 1389,207 1389,206 1389,206 1388,205 1387,205 1387,204 1386,204 1385,204 1385,204 1384,204 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1384,226 1385,226 1385,226 1386,226 1387,226 1387,225 1388,225 1389,224 1389,224 1389,223 1390,222 1390,222 1390,221 1390,220 1390,219 1390,219 1389,218 1389,217 1389,217 1388,216 1387,216 1387,215 1386,215 1385,215 1385,215 1384,215 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1562,193 1563,193 1563,193 1564,193 1565,193 1565,192 1566,192 1567,191 1567,191 1567,190 1568,189 1568,189 1568,188 1568,187 1568,186 1568,186 1567,185 1567,184 1567,184 1566,183 1565,183 1565,182 1564,182 1563,182 1563,182 1562,182 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1562,204 1563,204 1563,204 1564,204 1565,204 1565,203 1566,203 1567,202 1567,202 1567,201 1568,200 1568,200 1568,199 1568,198 1568,197 1568,197 1567,196 1567,195 1567,195 1566,194 1565,194 1565,193 1564,193 1563,193 1563,193 1562,193 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1562,215 1563,215 1563,215 1564,215 1565,215 1565,214 1566,214 1567,213 1567,213 1567,212 1568,211 1568,211 1568,210 1568,209 1568,208 1568,208 1567,207 1567,206 1567,206 1566,205 1565,205 1565,204 1564,204 1563,204 1563,204 1562,204 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1562,226 1563,226 1563,226 1564,226 1565,226 1565,225 1566,225 1567,224 1567,224 1567,223 1568,222 1568,222 1568,221 1568,220 1568,219 1568,219 1567,218 1567,217 1567,217 1566,216 1565,216 1565,215 1564,215 1563,215 1563,215 1562,215 " stroke="rgb(60,120,255)" stroke-width="0.171589"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1888,183 1890,183 1892,182 1894,182 1896,181 1897,180 1899,179 1900,177 1901,175 1902,174 1902,172 1902,170 1902,168 1902,166 1901,165 1900,163 1899,162 1897,160 1896,159 1894,158 1892,158 1890,157 1888,157 1887,157 1885,158 1883,158 1881,159 1880,160 1878,162 1877,163 1876,165 1875,166 1875,168 1875,170 " stroke="rgb(60,120,255)" stroke-width="0.0972"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1805,185 1807,185 1809,184 1811,184 1813,183 1814,182 1816,181 1817,179 1818,177 1819,176 1819,174 1819,172 1819,170 1819,168 1818,167 1817,165 1816,164 1814,162 1813,161 1811,160 1809,160 1807,159 1805,159 1804,159 1802,160 1800,160 1798,161 1797,162 1795,164 1794,165 1793,167 1792,168 1792,170 1792,172 " stroke="rgb(60,120,255)" stroke-width="0.0972"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="599" x2="583" y1="-491" y2="-507"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="583" x2="599" y1="-491" y2="-507"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="766" x2="782" y1="-491" y2="-507"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="782" x2="766" y1="-491" y2="-507"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="654" x2="654" y1="-339" y2="-339"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="654" x2="663" y1="-340" y2="-340"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="659" x2="659" y1="-337" y2="-343"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="656" x2="656" y1="-337" y2="-343"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="654" x2="654" y1="-320" y2="-320"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="654" x2="663" y1="-321" y2="-321"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="659" x2="659" y1="-318" y2="-324"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="656" x2="656" y1="-318" y2="-324"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="654" x2="654" y1="-301" y2="-301"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="654" x2="663" y1="-302" y2="-302"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="659" x2="659" y1="-299" y2="-305"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="656" x2="656" y1="-299" y2="-305"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="726" x2="726" y1="-339" y2="-339"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="726" x2="735" y1="-340" y2="-340"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="731" x2="731" y1="-337" y2="-343"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="728" x2="728" y1="-337" y2="-343"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="726" x2="726" y1="-320" y2="-320"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="726" x2="735" y1="-321" y2="-321"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="731" x2="731" y1="-318" y2="-324"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="728" x2="728" y1="-318" y2="-324"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="726" x2="726" y1="-301" y2="-301"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="726" x2="735" y1="-302" y2="-302"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="731" x2="731" y1="-299" y2="-305"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="728" x2="728" y1="-299" y2="-305"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="959" x2="981" y1="-381" y2="-381"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="959" x2="970" y1="-381" y2="-363"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="981" x2="970" y1="-381" y2="-363"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="959" x2="981" y1="-265" y2="-265"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="981" x2="970" y1="-265" y2="-283"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="959" x2="970" y1="-265" y2="-283"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1090" x2="1112" y1="-383" y2="-383"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1090" x2="1101" y1="-383" y2="-365"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1112" x2="1101" y1="-383" y2="-365"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1090" x2="1112" y1="-267" y2="-267"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1112" x2="1101" y1="-267" y2="-285"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1090" x2="1101" y1="-267" y2="-285"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1096" x2="949" y1="-420" y2="-420"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="998" x2="968" y1="-422" y2="-422"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="998" x2="998" y1="-470" y2="-422"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="977" x2="998" y1="-470" y2="-470"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1128" x2="1098" y1="-422" y2="-422"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1128" x2="1128" y1="-470" y2="-422"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1107" x2="1128" y1="-470" y2="-470"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1097" x2="966" y1="-514" y2="-514"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="962" x2="950" y1="-523" y2="-523"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="958" x2="954" y1="-529" y2="-529"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="960" x2="952" y1="-526" y2="-526"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="956" x2="956" y1="-523" y2="-517"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1097" x2="1085" y1="-522" y2="-522"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1093" x2="1089" y1="-528" y2="-528"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1095" x2="1087" y1="-525" y2="-525"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1091" x2="1091" y1="-522" y2="-516"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="1026" x2="1043" y1="-745" y2="-745"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="1035" x2="1043" y1="-761" y2="-745"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="1035" x2="1026" y1="-761" y2="-745"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="1027" x2="1035" y1="-787" y2="-795"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="1035" x2="1043" y1="-795" y2="-787"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="1035" x2="1035" y1="-803" y2="-795"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="1027" x2="1035" y1="-795" y2="-795"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="984" x2="1011" y1="-794" y2="-794"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="984" x2="984" y1="-801" y2="-788"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="978" x2="978" y1="-796" y2="-792"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="981" x2="981" y1="-798" y2="-791"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="1035" x2="1035" y1="-819" y2="-852"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="1026" x2="1043" y1="-852" y2="-852"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="1035" x2="1043" y1="-868" y2="-852"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="1035" x2="1026" y1="-868" y2="-852"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="998" x2="998" y1="-794" y2="-828"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="998" x2="1035" y1="-828" y2="-852"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(60,120,255)" stroke-dasharray="10 5 " stroke-width="0.510204" x1="1035" x2="1035" y1="-868" y2="-876"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="1035" x2="1035" y1="-704" y2="-731"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1029" x2="1041" y1="-704" y2="-704"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1033" x2="1037" y1="-698" y2="-698"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1031" x2="1039" y1="-701" y2="-701"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="969" x2="1035" y1="-678" y2="-731"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.510204" x1="1101" x2="1035" y1="-678" y2="-731"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1310" x2="1294" y1="-494" y2="-510"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1294" x2="1310" y1="-494" y2="-510"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1477" x2="1493" y1="-494" y2="-510"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1493" x2="1477" y1="-494" y2="-510"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1365" x2="1365" y1="-342" y2="-342"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1365" x2="1374" y1="-343" y2="-343"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1370" x2="1370" y1="-340" y2="-346"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1367" x2="1367" y1="-340" y2="-346"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1365" x2="1365" y1="-323" y2="-323"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1365" x2="1374" y1="-324" y2="-324"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1370" x2="1370" y1="-321" y2="-327"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1367" x2="1367" y1="-321" y2="-327"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1365" x2="1365" y1="-304" y2="-304"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1365" x2="1374" y1="-305" y2="-305"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1370" x2="1370" y1="-302" y2="-308"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1367" x2="1367" y1="-302" y2="-308"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1437" x2="1437" y1="-342" y2="-342"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1437" x2="1446" y1="-343" y2="-343"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1442" x2="1442" y1="-340" y2="-346"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1439" x2="1439" y1="-340" y2="-346"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1437" x2="1437" y1="-323" y2="-323"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1437" x2="1446" y1="-324" y2="-324"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1442" x2="1442" y1="-321" y2="-327"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1439" x2="1439" y1="-321" y2="-327"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1437" x2="1437" y1="-304" y2="-304"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1437" x2="1446" y1="-305" y2="-305"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1442" x2="1442" y1="-302" y2="-308"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="1439" x2="1439" y1="-302" y2="-308"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="108" x2="108" y1="-105" y2="-105"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="108" x2="117" y1="-106" y2="-106"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="113" x2="113" y1="-103" y2="-109"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="110" x2="110" y1="-103" y2="-109"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="108" x2="108" y1="-86" y2="-86"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="108" x2="117" y1="-87" y2="-87"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="113" x2="113" y1="-84" y2="-90"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="110" x2="110" y1="-84" y2="-90"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="109" x2="109" y1="73" y2="73"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="109" x2="118" y1="73" y2="73"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="114" x2="114" y1="76" y2="70"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="111" x2="111" y1="76" y2="70"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.491429" x1="101" x2="101" y1="146" y2="170"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.48" x1="88" x2="101" y1="170" y2="170"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.504" x1="101" x2="101" y1="183" y2="209"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="109" x2="109" y1="197" y2="197"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="109" x2="118" y1="197" y2="197"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="114" x2="114" y1="200" y2="194"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="111" x2="111" y1="200" y2="194"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="107" x2="95" y1="209" y2="209"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="103" x2="99" y1="215" y2="215"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="105" x2="97" y1="212" y2="212"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="232" x2="232" y1="-105" y2="-105"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="232" x2="241" y1="-106" y2="-106"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="237" x2="237" y1="-103" y2="-109"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="234" x2="234" y1="-103" y2="-109"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="232" x2="232" y1="-86" y2="-86"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="232" x2="241" y1="-87" y2="-87"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="237" x2="237" y1="-84" y2="-90"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="234" x2="234" y1="-84" y2="-90"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(60,120,255)" stroke-dasharray="10 5 " stroke-width="1" x1="251" x2="265" y1="26" y2="26"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(60,120,255)" stroke-dasharray="10 5 " stroke-width="1" x1="221" x2="235" y1="100" y2="100"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(60,120,255)" stroke-dasharray="10 5 " stroke-width="1" x1="357" x2="371" y1="98" y2="98"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="368" x2="368" y1="-107" y2="-107"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="368" x2="377" y1="-108" y2="-108"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="373" x2="373" y1="-105" y2="-111"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="370" x2="370" y1="-105" y2="-111"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="368" x2="368" y1="-88" y2="-88"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="368" x2="377" y1="-89" y2="-89"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="373" x2="373" y1="-86" y2="-92"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="370" x2="370" y1="-86" y2="-92"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(60,120,255)" stroke-dasharray="10 5 " stroke-width="1" x1="391" x2="405" y1="26" y2="26"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.388889" x1="193" x2="186" y1="181" y2="174"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.388889" x1="186" x2="193" y1="181" y2="174"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.715488" x1="225" x2="225" y1="175" y2="184"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="225" x2="225" y1="228" y2="281"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="231" x2="225" y1="268" y2="281"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.388889" x1="329" x2="322" y1="178" y2="171"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.388889" x1="322" x2="329" y1="178" y2="171"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.715488" x1="361" x2="361" y1="172" y2="181"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="361" x2="361" y1="225" y2="278"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="367" x2="361" y1="265" y2="278"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(60,120,255)" stroke-dasharray="10 5 " stroke-width="1" x1="570" x2="584" y1="99" y2="99"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(60,120,255)" stroke-dasharray="10 5 " stroke-width="1" x1="604" x2="618" y1="27" y2="27"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.388889" x1="542" x2="535" y1="179" y2="172"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.388889" x1="535" x2="542" y1="179" y2="172"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.715488" x1="574" x2="574" y1="173" y2="182"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="574" x2="574" y1="226" y2="279"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="580" x2="574" y1="266" y2="279"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="581" x2="581" y1="-106" y2="-106"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="581" x2="590" y1="-107" y2="-107"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="586" x2="586" y1="-104" y2="-110"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="583" x2="583" y1="-104" y2="-110"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="581" x2="581" y1="-87" y2="-87"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="581" x2="590" y1="-88" y2="-88"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="586" x2="586" y1="-85" y2="-91"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="583" x2="583" y1="-85" y2="-91"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="470" x2="470" y1="-107" y2="-107"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="470" x2="479" y1="-108" y2="-108"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="475" x2="475" y1="-105" y2="-111"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="472" x2="472" y1="-105" y2="-111"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="470" x2="470" y1="-88" y2="-88"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="470" x2="479" y1="-89" y2="-89"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="475" x2="475" y1="-86" y2="-92"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="472" x2="472" y1="-86" y2="-92"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="759" x2="759" y1="-107" y2="-107"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="759" x2="768" y1="-108" y2="-108"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="764" x2="764" y1="-105" y2="-111"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="761" x2="761" y1="-105" y2="-111"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="759" x2="759" y1="-88" y2="-88"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="759" x2="768" y1="-89" y2="-89"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="764" x2="764" y1="-86" y2="-92"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="761" x2="761" y1="-86" y2="-92"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(60,120,255)" stroke-dasharray="10 5 " stroke-width="1" x1="748" x2="762" y1="99" y2="99"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(60,120,255)" stroke-dasharray="10 5 " stroke-width="1" x1="782" x2="796" y1="27" y2="27"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.388889" x1="720" x2="713" y1="179" y2="172"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.388889" x1="713" x2="720" y1="179" y2="172"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.715488" x1="752" x2="752" y1="173" y2="182"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="752" x2="752" y1="226" y2="279"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="758" x2="752" y1="266" y2="279"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(60,120,255)" stroke-dasharray="10 5 " stroke-width="1" x1="1380" x2="1394" y1="99" y2="99"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(60,120,255)" stroke-dasharray="10 5 " stroke-width="1" x1="1414" x2="1428" y1="27" y2="27"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.388889" x1="1352" x2="1345" y1="179" y2="172"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.388889" x1="1345" x2="1352" y1="179" y2="172"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.715488" x1="1384" x2="1384" y1="173" y2="182"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1384" x2="1384" y1="226" y2="279"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1390" x2="1384" y1="266" y2="279"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1391" x2="1391" y1="-106" y2="-106"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1391" x2="1400" y1="-107" y2="-107"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1396" x2="1396" y1="-104" y2="-110"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1393" x2="1393" y1="-104" y2="-110"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1391" x2="1391" y1="-87" y2="-87"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1391" x2="1400" y1="-88" y2="-88"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1396" x2="1396" y1="-85" y2="-91"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1393" x2="1393" y1="-85" y2="-91"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1280" x2="1280" y1="-107" y2="-107"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1280" x2="1289" y1="-108" y2="-108"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1285" x2="1285" y1="-105" y2="-111"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1282" x2="1282" y1="-105" y2="-111"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1280" x2="1280" y1="-88" y2="-88"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1280" x2="1289" y1="-89" y2="-89"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1285" x2="1285" y1="-86" y2="-92"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1282" x2="1282" y1="-86" y2="-92"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1569" x2="1569" y1="-107" y2="-107"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1569" x2="1578" y1="-108" y2="-108"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1574" x2="1574" y1="-105" y2="-111"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1571" x2="1571" y1="-105" y2="-111"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1569" x2="1569" y1="-88" y2="-88"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1569" x2="1578" y1="-89" y2="-89"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1574" x2="1574" y1="-86" y2="-92"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1571" x2="1571" y1="-86" y2="-92"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(60,120,255)" stroke-dasharray="10 5 " stroke-width="1" x1="1558" x2="1572" y1="99" y2="99"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" lineStyle="1" stroke="rgb(60,120,255)" stroke-dasharray="10 5 " stroke-width="1" x1="1592" x2="1606" y1="27" y2="27"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.388889" x1="1530" x2="1523" y1="179" y2="172"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.388889" x1="1523" x2="1530" y1="179" y2="172"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.715488" x1="1562" x2="1562" y1="173" y2="182"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1562" x2="1562" y1="226" y2="279"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1568" x2="1562" y1="266" y2="279"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1895" x2="1895" y1="-105" y2="-105"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1895" x2="1904" y1="-106" y2="-106"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1900" x2="1900" y1="-103" y2="-109"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1897" x2="1897" y1="-103" y2="-109"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1895" x2="1895" y1="-86" y2="-86"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1895" x2="1904" y1="-87" y2="-87"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1900" x2="1900" y1="-84" y2="-90"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1897" x2="1897" y1="-84" y2="-90"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1896" x2="1896" y1="73" y2="73"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1896" x2="1905" y1="73" y2="73"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1901" x2="1901" y1="76" y2="70"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1898" x2="1898" y1="76" y2="70"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.491429" x1="1888" x2="1888" y1="146" y2="170"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.48" x1="1875" x2="1888" y1="170" y2="170"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.504" x1="1888" x2="1888" y1="183" y2="209"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1896" x2="1896" y1="197" y2="197"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1896" x2="1905" y1="197" y2="197"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1901" x2="1901" y1="200" y2="194"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1898" x2="1898" y1="200" y2="194"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1894" x2="1882" y1="209" y2="209"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1890" x2="1886" y1="215" y2="215"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1892" x2="1884" y1="212" y2="212"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1813" x2="1813" y1="75" y2="75"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1813" x2="1822" y1="75" y2="75"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1818" x2="1818" y1="78" y2="72"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1815" x2="1815" y1="78" y2="72"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.491429" x1="1805" x2="1805" y1="148" y2="172"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.48" x1="1792" x2="1805" y1="172" y2="172"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.504" x1="1805" x2="1805" y1="185" y2="211"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1813" x2="1813" y1="199" y2="199"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1813" x2="1822" y1="199" y2="199"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1818" x2="1818" y1="202" y2="196"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1815" x2="1815" y1="202" y2="196"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1811" x2="1799" y1="211" y2="211"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1809" x2="1801" y1="214" y2="214"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-194673">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 638.000000 -704.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29553" ObjectName="SW-CX_CXBQ.CX_CXBQ_111BK"/>
     <cge:Meas_Ref ObjectId="194673"/>
    <cge:TPSR_Ref TObjectID="29553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194721">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1348.000000 -711.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29563" ObjectName="SW-CX_CXBQ.CX_CXBQ_112BK"/>
     <cge:Meas_Ref ObjectId="194721"/>
    <cge:TPSR_Ref TObjectID="29563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194684">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 638.000000 -350.000000)" xlink:href="#breaker2:shape10_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29574" ObjectName="SW-CX_CXBQ.CX_CXBQ_201BBK"/>
     <cge:Meas_Ref ObjectId="194684"/>
    <cge:TPSR_Ref TObjectID="29574"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194680">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 710.000000 -350.000000)" xlink:href="#breaker2:shape10_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29554" ObjectName="SW-CX_CXBQ.CX_CXBQ_201ABK"/>
     <cge:Meas_Ref ObjectId="194680"/>
    <cge:TPSR_Ref TObjectID="29554"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194732">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1349.000000 -353.000000)" xlink:href="#breaker2:shape10_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29567" ObjectName="SW-CX_CXBQ.CX_CXBQ_202BBK"/>
     <cge:Meas_Ref ObjectId="194732"/>
    <cge:TPSR_Ref TObjectID="29567"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194728">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1421.000000 -353.000000)" xlink:href="#breaker2:shape10_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29564" ObjectName="SW-CX_CXBQ.CX_CXBQ_202ABK"/>
     <cge:Meas_Ref ObjectId="194728"/>
    <cge:TPSR_Ref TObjectID="29564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 92.000000 -31.000000)" xlink:href="#breaker2:shape10_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 216.000000 -31.000000)" xlink:href="#breaker2:shape10_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 352.000000 -33.000000)" xlink:href="#breaker2:shape10_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 565.000000 -32.000000)" xlink:href="#breaker2:shape10_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 454.000000 -33.000000)" xlink:href="#breaker2:shape10_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 743.000000 -33.000000)" xlink:href="#breaker2:shape10_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1375.000000 -32.000000)" xlink:href="#breaker2:shape10_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1264.000000 -33.000000)" xlink:href="#breaker2:shape10_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1553.000000 -33.000000)" xlink:href="#breaker2:shape10_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1879.000000 -31.000000)" xlink:href="#breaker2:shape10_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2dffe00">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 444.000000 -768.000000)" xlink:href="#voltageTransformer:shape52"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_321ff80">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1496.000000 -774.000000)" xlink:href="#voltageTransformer:shape52"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_323cd00">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 381.000000 -363.000000)" xlink:href="#voltageTransformer:shape124"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_323df40">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 236.000000 -361.000000)" xlink:href="#voltageTransformer:shape124"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30c8a70">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1779.000000 -363.000000)" xlink:href="#voltageTransformer:shape124"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30c9cb0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1634.000000 -361.000000)" xlink:href="#voltageTransformer:shape124"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1001.000000 -149.000000)" xlink:href="#capacitor:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 81.000000 131.000000)" xlink:href="#capacitor:shape42"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1868.000000 131.000000)" xlink:href="#capacitor:shape42"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1785.000000 133.000000)" xlink:href="#capacitor:shape42"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_CXBQ.CX_CXBQ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="42165"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 613.000000 -556.000000)" xlink:href="#transformer2:shape81_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 613.000000 -556.000000)" xlink:href="#transformer2:shape81_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="29576" ObjectName="TF-CX_CXBQ.CX_CXBQ_1T"/>
    <cge:TPSR_Ref TObjectID="29576"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_CXBQ.CX_CXBQ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="42158"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1324.000000 -559.000000)" xlink:href="#transformer2:shape81_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1324.000000 -559.000000)" xlink:href="#transformer2:shape81_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="29575" ObjectName="TF-CX_CXBQ.CX_CXBQ_2T"/>
    <cge:TPSR_Ref TObjectID="29575"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2e02470">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 432.000000 -808.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3222850">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1548.000000 -796.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3226ec0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 752.000000 -451.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3227c00">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 547.000000 -452.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30a53a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 293.000000 -364.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30a63b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 437.000000 -363.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3245700">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1463.000000 -454.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3246380">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1258.000000 -455.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3261b20">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1691.000000 -364.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3262960">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1835.000000 -363.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30db2d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 12.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_310e6a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 144.000000 252.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3111e10">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 280.000000 249.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_311f150">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 493.000000 250.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_314d0b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 671.000000 250.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3166680">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1303.000000 250.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3193b00">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1481.000000 250.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_328c8d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1906.000000 14.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -241.000000 -849.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-194621" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 580.000000 -739.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194621" ObjectName="CX_CXBQ:CX_CXBQ_111BK_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-194622" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 580.000000 -725.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194622" ObjectName="CX_CXBQ:CX_CXBQ_111BK_Ub"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-194623" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 580.000000 -711.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194623" ObjectName="CX_CXBQ:CX_CXBQ_111BK_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-194624" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 580.000000 -697.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194624" ObjectName="CX_CXBQ:CX_CXBQ_111BK_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-194630" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 580.000000 -683.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194630" ObjectName="CX_CXBQ:CX_CXBQ_111BK_Hz"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-194646" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1285.000000 -742.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194646" ObjectName="CX_CXBQ:CX_CXBQ_112BK_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-194647" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1285.000000 -728.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194647" ObjectName="CX_CXBQ:CX_CXBQ_112BK_Ub"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-194648" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1285.000000 -714.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194648" ObjectName="CX_CXBQ:CX_CXBQ_112BK_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-194649" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1285.000000 -700.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194649" ObjectName="CX_CXBQ:CX_CXBQ_112BK_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-194655" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1285.000000 -686.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194655" ObjectName="CX_CXBQ:CX_CXBQ_112BK_Hz"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-215375" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -202.000000 -719.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215375" ObjectName="CX_CXQ:CX_CXQ_sumP"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-194627" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 757.000000 -749.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194627" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29553"/>
     <cge:Term_Ref ObjectID="42112"/>
    <cge:TPSR_Ref TObjectID="29553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-194628" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 757.000000 -749.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194628" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29553"/>
     <cge:Term_Ref ObjectID="42112"/>
    <cge:TPSR_Ref TObjectID="29553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-194618" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 757.000000 -749.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194618" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29553"/>
     <cge:Term_Ref ObjectID="42112"/>
    <cge:TPSR_Ref TObjectID="29553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-194652" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1477.000000 -753.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194652" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29563"/>
     <cge:Term_Ref ObjectID="42132"/>
    <cge:TPSR_Ref TObjectID="29563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-194653" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1477.000000 -753.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194653" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29563"/>
     <cge:Term_Ref ObjectID="42132"/>
    <cge:TPSR_Ref TObjectID="29563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-194643" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1477.000000 -753.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194643" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29563"/>
     <cge:Term_Ref ObjectID="42132"/>
    <cge:TPSR_Ref TObjectID="29563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-194633" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 772.000000 -396.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194633" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29554"/>
     <cge:Term_Ref ObjectID="42114"/>
    <cge:TPSR_Ref TObjectID="29554"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-194634" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 772.000000 -396.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194634" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29554"/>
     <cge:Term_Ref ObjectID="42114"/>
    <cge:TPSR_Ref TObjectID="29554"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="I" PreSymbol="0" appendix="" decimal="2" id="ME-194631" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 772.000000 -396.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194631" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29554"/>
     <cge:Term_Ref ObjectID="42114"/>
    <cge:TPSR_Ref TObjectID="29554"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-194639" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 597.000000 -396.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194639" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29574"/>
     <cge:Term_Ref ObjectID="42154"/>
    <cge:TPSR_Ref TObjectID="29574"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-194640" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 597.000000 -396.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194640" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29574"/>
     <cge:Term_Ref ObjectID="42154"/>
    <cge:TPSR_Ref TObjectID="29574"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="I" PreSymbol="0" appendix="" decimal="2" id="ME-194637" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 597.000000 -396.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194637" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29574"/>
     <cge:Term_Ref ObjectID="42154"/>
    <cge:TPSR_Ref TObjectID="29574"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-194664" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1311.000000 -399.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194664" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29567"/>
     <cge:Term_Ref ObjectID="42140"/>
    <cge:TPSR_Ref TObjectID="29567"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-194665" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1311.000000 -399.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194665" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29567"/>
     <cge:Term_Ref ObjectID="42140"/>
    <cge:TPSR_Ref TObjectID="29567"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="I" PreSymbol="0" appendix="" decimal="2" id="ME-194662" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1311.000000 -399.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194662" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29567"/>
     <cge:Term_Ref ObjectID="42140"/>
    <cge:TPSR_Ref TObjectID="29567"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-194658" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1482.000000 -400.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194658" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29564"/>
     <cge:Term_Ref ObjectID="42134"/>
    <cge:TPSR_Ref TObjectID="29564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-194659" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1482.000000 -400.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194659" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29564"/>
     <cge:Term_Ref ObjectID="42134"/>
    <cge:TPSR_Ref TObjectID="29564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="I" PreSymbol="0" appendix="" decimal="2" id="ME-194656" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1482.000000 -400.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="194656" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29564"/>
     <cge:Term_Ref ObjectID="42134"/>
    <cge:TPSR_Ref TObjectID="29564"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-229" y="-908"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-229" y="-908"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-278" y="-922"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-278" y="-922"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-229" y="-908"/></g>
   <g href="cx_索引_接线图_客户变.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-278" y="-922"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_329cb20" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 508.000000 694.033333) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_329d150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 516.000000 739.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_329d390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 516.000000 724.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_329d5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 516.000000 709.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_329d810" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 524.000000 680.033333) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_329f030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 699.000000 749.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_329f290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 713.000000 719.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_329f4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 688.000000 734.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32a03a0" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 1219.000000 697.033333) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32a0610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1227.000000 742.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32a0850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1227.000000 727.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32a0a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1227.000000 712.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32a0cd0" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 1235.000000 683.033333) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32a3b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1417.000000 754.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32a3dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1431.000000 724.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32a4010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1406.000000 739.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-194688">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 638.000000 -751.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29559" ObjectName="SW-CX_CXBQ.CX_CXBQ_2516SW"/>
     <cge:Meas_Ref ObjectId="194688"/>
    <cge:TPSR_Ref TObjectID="29559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194736">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1348.000000 -758.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29570" ObjectName="SW-CX_CXBQ.CX_CXBQ_2526SW"/>
     <cge:Meas_Ref ObjectId="194736"/>
    <cge:TPSR_Ref TObjectID="29570"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194692">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 584.000000 -803.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29560" ObjectName="SW-CX_CXBQ.CX_CXBQ_25167SW"/>
     <cge:Meas_Ref ObjectId="194692"/>
    <cge:TPSR_Ref TObjectID="29560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194740">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1375.000000 -810.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29571" ObjectName="SW-CX_CXBQ.CX_CXBQ_25267SW"/>
     <cge:Meas_Ref ObjectId="194740"/>
    <cge:TPSR_Ref TObjectID="29571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194693">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 559.000000 -869.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29561" ObjectName="SW-CX_CXBQ.CX_CXBQ_2519SW"/>
     <cge:Meas_Ref ObjectId="194693"/>
    <cge:TPSR_Ref TObjectID="29561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194741">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1399.000000 -874.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29572" ObjectName="SW-CX_CXBQ.CX_CXBQ_2529SW"/>
     <cge:Meas_Ref ObjectId="194741"/>
    <cge:TPSR_Ref TObjectID="29572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194694">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 524.000000 -915.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29562" ObjectName="SW-CX_CXBQ.CX_CXBQ_25197SW"/>
     <cge:Meas_Ref ObjectId="194694"/>
    <cge:TPSR_Ref TObjectID="29562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194742">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1491.000000 -922.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29573" ObjectName="SW-CX_CXBQ.CX_CXBQ_25297SW"/>
     <cge:Meas_Ref ObjectId="194742"/>
    <cge:TPSR_Ref TObjectID="29573"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194685">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 637.000000 -392.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29557" ObjectName="SW-CX_CXBQ.CX_CXBQ_201BXC"/>
     <cge:Meas_Ref ObjectId="194685"/>
    <cge:TPSR_Ref TObjectID="29557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194685">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 637.000000 -261.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29558" ObjectName="SW-CX_CXBQ.CX_CXBQ_201BXC1"/>
     <cge:Meas_Ref ObjectId="194685"/>
    <cge:TPSR_Ref TObjectID="29558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194681">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 709.000000 -392.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29555" ObjectName="SW-CX_CXBQ.CX_CXBQ_201AXC"/>
     <cge:Meas_Ref ObjectId="194681"/>
    <cge:TPSR_Ref TObjectID="29555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194681">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 709.000000 -261.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29556" ObjectName="SW-CX_CXBQ.CX_CXBQ_201AXC1"/>
     <cge:Meas_Ref ObjectId="194681"/>
    <cge:TPSR_Ref TObjectID="29556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 260.000000 -283.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 405.000000 -286.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 874.000000 -235.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1149.000000 -235.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 874.000000 -203.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1149.000000 -203.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 961.000000 -396.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1092.000000 -398.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 961.000000 -491.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1092.000000 -490.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194733">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1348.000000 -395.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29568" ObjectName="SW-CX_CXBQ.CX_CXBQ_202BXC"/>
     <cge:Meas_Ref ObjectId="194733"/>
    <cge:TPSR_Ref TObjectID="29568"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194733">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1348.000000 -264.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29569" ObjectName="SW-CX_CXBQ.CX_CXBQ_202BXC1"/>
     <cge:Meas_Ref ObjectId="194733"/>
    <cge:TPSR_Ref TObjectID="29569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194729">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1420.000000 -395.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29565" ObjectName="SW-CX_CXBQ.CX_CXBQ_202AXC"/>
     <cge:Meas_Ref ObjectId="194729"/>
    <cge:TPSR_Ref TObjectID="29565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-194729">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1420.000000 -264.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29566" ObjectName="SW-CX_CXBQ.CX_CXBQ_202AXC1"/>
     <cge:Meas_Ref ObjectId="194729"/>
    <cge:TPSR_Ref TObjectID="29566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1658.000000 -283.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1803.000000 -286.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 91.000000 -114.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 91.000000 -2.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 92.000000 57.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 215.000000 -114.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 215.000000 -2.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 216.000000 124.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 191.000000 124.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 352.000000 122.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 327.000000 122.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 351.000000 -116.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 351.000000 -4.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 246.000000 49.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 386.000000 49.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 565.000000 123.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 540.000000 123.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 599.000000 50.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 564.000000 -115.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 564.000000 -3.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 453.000000 -116.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 453.000000 -4.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 742.000000 -116.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 742.000000 -3.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 743.000000 123.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 718.000000 123.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 777.000000 50.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1375.000000 123.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1350.000000 123.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1409.000000 50.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1374.000000 -115.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1374.000000 -3.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1263.000000 -116.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1263.000000 -4.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1552.000000 -116.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1552.000000 -3.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1553.000000 123.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1528.000000 123.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1587.000000 50.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1878.000000 -114.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1878.000000 -2.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1879.000000 57.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1796.000000 59.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 638.000000 -929.000000)" xlink:href="#load:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1348.000000 -936.000000)" xlink:href="#load:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-220KV" id="g_2e031e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-680 647,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="29576@0" ObjectIDZND0="29553@0" Pin0InfoVect0LinkObjId="SW-194673_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3226830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="647,-680 647,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e03440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-739 647,-756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29553@1" ObjectIDZND0="29559@0" Pin0InfoVect0LinkObjId="SW-194688_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194673_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="647,-739 647,-756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e036a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-874 647,-934 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="29561@x" ObjectIDND1="29560@x" ObjectIDND2="29559@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-194693_0" Pin1InfoVect1LinkObjId="SW-194692_0" Pin1InfoVect2LinkObjId="SW-194688_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="647,-874 647,-934 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e03900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-874 600,-874 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="29560@x" ObjectIDND2="29559@x" ObjectIDZND0="29561@1" Pin0InfoVect0LinkObjId="SW-194693_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-194692_0" Pin1InfoVect2LinkObjId="SW-194688_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="647,-874 600,-874 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e03b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="565,-920 576,-920 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29562@1" ObjectIDZND0="g_3063230@0" Pin0InfoVect0LinkObjId="g_3063230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194694_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="565,-920 576,-920 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e03dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-792 647,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="29559@1" ObjectIDZND0="29560@x" ObjectIDZND1="29561@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-194692_0" Pin0InfoVect1LinkObjId="SW-194693_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194688_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="647,-792 647,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e04020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-808 647,-874 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="load" ObjectIDND0="29560@x" ObjectIDND1="29559@x" ObjectIDZND0="29561@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-194693_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-194692_0" Pin1InfoVect1LinkObjId="SW-194688_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="647,-808 647,-874 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e04280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="567,-808 589,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2939590@0" ObjectIDZND0="29560@0" Pin0InfoVect0LinkObjId="SW-194692_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2939590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="567,-808 589,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e044e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="625,-808 647,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" EndDevType2="switch" ObjectIDND0="29560@1" ObjectIDZND0="29561@x" ObjectIDZND1="0@x" ObjectIDZND2="29559@x" Pin0InfoVect0LinkObjId="SW-194693_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-194688_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194692_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="625,-808 647,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_321b580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="471,-803 471,-874 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2dffe00@0" ObjectIDZND0="g_2e02470@0" ObjectIDZND1="29562@x" ObjectIDZND2="29561@x" Pin0InfoVect0LinkObjId="g_2e02470_0" Pin0InfoVect1LinkObjId="SW-194694_0" Pin0InfoVect2LinkObjId="SW-194693_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dffe00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="471,-803 471,-874 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_321b770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="471,-874 439,-874 439,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2dffe00@0" ObjectIDND1="29562@x" ObjectIDND2="29561@x" ObjectIDZND0="g_2e02470@0" Pin0InfoVect0LinkObjId="g_2e02470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2dffe00_0" Pin1InfoVect1LinkObjId="SW-194694_0" Pin1InfoVect2LinkObjId="SW-194693_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="471,-874 439,-874 439,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_321b960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="518,-874 518,-920 529,-920 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2e02470@0" ObjectIDND1="g_2dffe00@0" ObjectIDND2="29561@x" ObjectIDZND0="29562@0" Pin0InfoVect0LinkObjId="SW-194694_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e02470_0" Pin1InfoVect1LinkObjId="g_2dffe00_0" Pin1InfoVect2LinkObjId="SW-194693_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="518,-874 518,-920 529,-920 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_321bb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="471,-874 518,-874 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2e02470@0" ObjectIDND1="g_2dffe00@0" ObjectIDZND0="29562@x" ObjectIDZND1="29561@x" Pin0InfoVect0LinkObjId="SW-194694_0" Pin0InfoVect1LinkObjId="SW-194693_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e02470_0" Pin1InfoVect1LinkObjId="g_2dffe00_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="471,-874 518,-874 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_321bda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="518,-874 564,-874 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2e02470@0" ObjectIDND1="g_2dffe00@0" ObjectIDND2="29562@x" ObjectIDZND0="29561@0" Pin0InfoVect0LinkObjId="SW-194693_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e02470_0" Pin1InfoVect1LinkObjId="g_2dffe00_0" Pin1InfoVect2LinkObjId="SW-194694_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="518,-874 564,-874 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_321db60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1357,-684 1357,-719 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="29575@0" ObjectIDZND0="29563@0" Pin0InfoVect0LinkObjId="SW-194721_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3245070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1357,-684 1357,-719 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_321ddc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1357,-746 1357,-763 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29563@1" ObjectIDZND0="29570@0" Pin0InfoVect0LinkObjId="SW-194736_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194721_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1357,-746 1357,-763 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_321e020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1357,-816 1380,-816 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="29572@x" ObjectIDND1="0@x" ObjectIDND2="29570@x" ObjectIDZND0="29571@0" Pin0InfoVect0LinkObjId="SW-194740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-194741_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-194736_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1357,-816 1380,-816 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_321eb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1357,-799 1357,-817 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="29570@1" ObjectIDZND0="29571@x" ObjectIDZND1="29572@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-194740_0" Pin0InfoVect1LinkObjId="SW-194741_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194736_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1357,-799 1357,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_321ed70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1416,-815 1434,-815 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29571@1" ObjectIDZND0="g_2939fc0@0" Pin0InfoVect0LinkObjId="g_2939fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194740_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1416,-815 1434,-815 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_321efd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1357,-878 1405,-878 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="29571@x" ObjectIDND1="29570@x" ObjectIDND2="0@x" ObjectIDZND0="29572@0" Pin0InfoVect0LinkObjId="SW-194741_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-194740_0" Pin1InfoVect1LinkObjId="SW-194736_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1357,-878 1405,-878 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_321fac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1357,-878 1357,-817 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29572@x" ObjectIDND1="0@x" ObjectIDZND0="29571@x" ObjectIDZND1="29570@x" Pin0InfoVect0LinkObjId="SW-194740_0" Pin0InfoVect1LinkObjId="SW-194736_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-194741_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1357,-878 1357,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_321fd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1357,-941 1357,-878 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="29572@x" ObjectIDZND1="29571@x" ObjectIDZND2="29570@x" Pin0InfoVect0LinkObjId="SW-194741_0" Pin0InfoVect1LinkObjId="SW-194740_0" Pin0InfoVect2LinkObjId="SW-194736_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1357,-941 1357,-878 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_32225f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1523,-809 1523,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_321ff80@0" ObjectIDZND0="g_3222850@0" ObjectIDZND1="29572@x" ObjectIDZND2="29573@x" Pin0InfoVect0LinkObjId="g_3222850_0" Pin0InfoVect1LinkObjId="SW-194741_0" Pin0InfoVect2LinkObjId="SW-194742_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_321ff80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1523,-809 1523,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3223e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1555,-854 1555,-879 1523,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3222850@0" ObjectIDZND0="g_321ff80@0" ObjectIDZND1="29572@x" ObjectIDZND2="29573@x" Pin0InfoVect0LinkObjId="g_321ff80_0" Pin0InfoVect1LinkObjId="SW-194741_0" Pin0InfoVect2LinkObjId="SW-194742_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3222850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1555,-854 1555,-879 1523,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3224940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1440,-879 1481,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="29572@1" ObjectIDZND0="g_321ff80@0" ObjectIDZND1="g_3222850@0" ObjectIDZND2="29573@x" Pin0InfoVect0LinkObjId="g_321ff80_0" Pin0InfoVect1LinkObjId="g_3222850_0" Pin0InfoVect2LinkObjId="SW-194742_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194741_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1440,-879 1481,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3224ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1481,-879 1523,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="29572@x" ObjectIDND1="29573@x" ObjectIDZND0="g_321ff80@0" ObjectIDZND1="g_3222850@0" Pin0InfoVect0LinkObjId="g_321ff80_0" Pin0InfoVect1LinkObjId="g_3222850_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-194741_0" Pin1InfoVect1LinkObjId="SW-194742_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1481,-879 1523,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3224e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1496,-927 1481,-927 1481,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="29573@0" ObjectIDZND0="29572@x" ObjectIDZND1="g_321ff80@0" ObjectIDZND2="g_3222850@0" Pin0InfoVect0LinkObjId="SW-194741_0" Pin0InfoVect1LinkObjId="g_321ff80_0" Pin0InfoVect2LinkObjId="g_3222850_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194742_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1496,-927 1481,-927 1481,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3225060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1532,-927 1550,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29573@1" ObjectIDZND0="g_2dff370@0" Pin0InfoVect0LinkObjId="g_2dff370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194742_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1532,-927 1550,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_32252c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-537 591,-537 591,-515 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="29576@x" ObjectIDND1="g_3227c00@0" ObjectIDND2="29557@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3226830_0" Pin1InfoVect1LinkObjId="g_3227c00_0" Pin1InfoVect2LinkObjId="SW-194685_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="647,-537 591,-537 591,-515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3225520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-567 647,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="29576@1" ObjectIDZND0="g_3227c00@0" ObjectIDZND1="29557@x" Pin0InfoVect0LinkObjId="g_3227c00_0" Pin0InfoVect1LinkObjId="SW-194685_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3226830_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="647,-567 647,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_32263e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-537 776,-537 776,-515 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer2" ObjectIDND0="29555@x" ObjectIDND1="g_3226ec0@0" ObjectIDND2="29576@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-194681_0" Pin1InfoVect1LinkObjId="g_3226ec0_0" Pin1InfoVect2LinkObjId="g_3226830_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="719,-537 776,-537 776,-515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3226830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-537 719,-550 672,-578 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="29555@x" ObjectIDND1="g_3226ec0@0" ObjectIDZND0="29576@x" Pin0InfoVect0LinkObjId="g_3226c90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-194681_0" Pin1InfoVect1LinkObjId="g_3226ec0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-537 719,-550 672,-578 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3226a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-459 604,-459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="29576@x" ObjectIDND1="29557@x" ObjectIDZND0="g_3227c00@0" Pin0InfoVect0LinkObjId="g_3227c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3226830_0" Pin1InfoVect1LinkObjId="SW-194685_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="647,-459 604,-459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3226c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-459 647,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_3227c00@0" ObjectIDND1="29557@x" ObjectIDZND0="29576@x" Pin0InfoVect0LinkObjId="g_3226830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3227c00_0" Pin1InfoVect1LinkObjId="SW-194685_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="647,-459 647,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3097340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-416 647,-459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="29557@0" ObjectIDZND0="g_3227c00@0" ObjectIDZND1="29576@x" Pin0InfoVect0LinkObjId="g_3227c00_0" Pin0InfoVect1LinkObjId="g_3226830_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194685_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="647,-416 647,-459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_30975a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-391 647,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29574@0" ObjectIDZND0="29557@1" Pin0InfoVect0LinkObjId="SW-194685_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194684_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="647,-391 647,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3098eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-268 647,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29558@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194685_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="647,-268 647,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_30a1f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-416 719,-459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="29555@0" ObjectIDZND0="29576@x" ObjectIDZND1="g_3226ec0@0" Pin0InfoVect0LinkObjId="g_3226830_0" Pin0InfoVect1LinkObjId="g_3226ec0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194681_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="719,-416 719,-459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_30a2160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-391 719,-399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29554@0" ObjectIDZND0="29555@1" Pin0InfoVect0LinkObjId="SW-194681_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-391 719,-399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_30a3a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-268 719,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29556@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194681_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-268 719,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_30a3cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-537 719,-459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="29576@x" ObjectIDZND0="29555@x" ObjectIDZND1="g_3226ec0@0" Pin0InfoVect0LinkObjId="SW-194681_0" Pin0InfoVect1LinkObjId="g_3226ec0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3226830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="719,-537 719,-459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_30a3f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-459 756,-459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="29576@x" ObjectIDND1="29555@x" ObjectIDZND0="g_3226ec0@0" Pin0InfoVect0LinkObjId="g_3226ec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3226830_0" Pin1InfoVect1LinkObjId="SW-194681_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-459 756,-459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_30a4190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-355 647,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29574@1" ObjectIDZND0="29558@1" Pin0InfoVect0LinkObjId="SW-194685_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194684_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="647,-355 647,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_30a43f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="719,-355 719,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29554@1" ObjectIDZND0="29556@1" Pin0InfoVect0LinkObjId="SW-194681_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194680_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="719,-355 719,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30a4650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="269,-344 300,-344 300,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_323df40@0" ObjectIDND1="0@x" ObjectIDZND0="g_30a53a0@0" Pin0InfoVect0LinkObjId="g_30a53a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_323df40_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="269,-344 300,-344 300,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30a5140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="269,-344 269,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_30a53a0@0" ObjectIDND1="0@x" ObjectIDZND0="g_323df40@0" Pin0InfoVect0LinkObjId="g_323df40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_30a53a0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="269,-344 269,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30a6150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="413,-343 444,-343 444,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_323cd00@0" ObjectIDND1="0@x" ObjectIDZND0="g_30a63b0@0" Pin0InfoVect0LinkObjId="g_30a63b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_323cd00_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="413,-343 444,-343 444,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30a7160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="414,-343 414,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_30a63b0@0" ObjectIDND1="0@x" ObjectIDZND0="g_323cd00@0" Pin0InfoVect0LinkObjId="g_323cd00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_30a63b0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="414,-343 414,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3196f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="269,-208 269,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="269,-208 269,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3197160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="269,-324 269,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="g_30a53a0@0" ObjectIDZND1="g_323df40@0" Pin0InfoVect0LinkObjId="g_30a53a0_0" Pin0InfoVect1LinkObjId="g_323df40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="269,-324 269,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3199b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="414,-240 414,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="414,-240 414,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3199dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="414,-327 414,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="g_30a63b0@0" ObjectIDZND1="g_323cd00@0" Pin0InfoVect0LinkObjId="g_30a63b0_0" Pin0InfoVect1LinkObjId="g_323cd00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="414,-327 414,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31a1a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="879,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="879,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31a1ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="915,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="915,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31a4be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1190,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1190,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31a4e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1148,-240 1154,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1148,-240 1154,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31a7f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="879,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="879,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31a81a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="915,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="915,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31aaad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1190,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1190,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31aad30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1148,-208 1154,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1148,-208 1154,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31ad4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1006,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="capacitor" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1006,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31ad730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1066,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1066,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3232bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="970,-159 970,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="970,-159 970,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32355c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1101,-159 1101,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1101,-159 1101,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3239420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="970,-437 970,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="970,-437 970,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3239680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="970,-532 970,-678 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="970,-532 970,-678 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_323c080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1101,-439 1101,-495 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1101,-439 1101,-495 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_323c2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1101,-531 1101,-678 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1101,-531 1101,-678 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3243d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1358,-540 1302,-540 1302,-518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="29575@x" ObjectIDND1="g_3246380@0" ObjectIDND2="29568@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3245070_0" Pin1InfoVect1LinkObjId="g_3246380_0" Pin1InfoVect2LinkObjId="SW-194733_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1358,-540 1302,-540 1302,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3243fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1358,-570 1358,-540 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="29575@1" ObjectIDZND0="g_3246380@0" ObjectIDZND1="29568@x" Pin0InfoVect0LinkObjId="g_3246380_0" Pin0InfoVect1LinkObjId="SW-194733_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3245070_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1358,-570 1358,-540 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3244c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1430,-540 1487,-540 1487,-518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="29575@x" ObjectIDND1="29565@x" ObjectIDND2="g_3245700@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3245070_0" Pin1InfoVect1LinkObjId="SW-194729_0" Pin1InfoVect2LinkObjId="g_3245700_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1430,-540 1487,-540 1487,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3245070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1430,-540 1430,-553 1383,-581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="29565@x" ObjectIDND1="g_3245700@0" ObjectIDZND0="29575@x" Pin0InfoVect0LinkObjId="g_32454d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-194729_0" Pin1InfoVect1LinkObjId="g_3245700_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1430,-540 1430,-553 1383,-581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_32452a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1358,-462 1315,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="29575@x" ObjectIDND1="29568@x" ObjectIDZND0="g_3246380@0" Pin0InfoVect0LinkObjId="g_3246380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3245070_0" Pin1InfoVect1LinkObjId="SW-194733_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1358,-462 1315,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_32454d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1358,-462 1358,-540 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_3246380@0" ObjectIDND1="29568@x" ObjectIDZND0="29575@x" Pin0InfoVect0LinkObjId="g_3245070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3246380_0" Pin1InfoVect1LinkObjId="SW-194733_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1358,-462 1358,-540 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3250c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1358,-419 1358,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="29568@0" ObjectIDZND0="29575@x" ObjectIDZND1="g_3246380@0" Pin0InfoVect0LinkObjId="g_3245070_0" Pin0InfoVect1LinkObjId="g_3246380_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194733_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1358,-419 1358,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3250e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1358,-394 1358,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29567@0" ObjectIDZND0="29568@1" Pin0InfoVect0LinkObjId="SW-194733_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194732_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1358,-394 1358,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_3252770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1358,-271 1358,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29569@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194733_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1358,-271 1358,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_325b7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1430,-419 1430,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="29565@0" ObjectIDZND0="29575@x" ObjectIDZND1="g_3245700@0" Pin0InfoVect0LinkObjId="g_3245070_0" Pin0InfoVect1LinkObjId="g_3245700_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194729_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1430,-419 1430,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_325ba20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1430,-394 1430,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29564@0" ObjectIDZND0="29565@1" Pin0InfoVect0LinkObjId="SW-194729_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194728_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1430,-394 1430,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_325d330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1430,-271 1430,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29566@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194729_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1430,-271 1430,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_325d590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1430,-540 1430,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="29575@x" ObjectIDZND0="29565@x" ObjectIDZND1="g_3245700@0" Pin0InfoVect0LinkObjId="SW-194729_0" Pin0InfoVect1LinkObjId="g_3245700_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3245070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1430,-540 1430,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_325d7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1430,-462 1467,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="29575@x" ObjectIDND1="29565@x" ObjectIDZND0="g_3245700@0" Pin0InfoVect0LinkObjId="g_3245700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3245070_0" Pin1InfoVect1LinkObjId="SW-194729_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1430,-462 1467,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_325da50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1358,-358 1358,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29567@1" ObjectIDZND0="29569@1" Pin0InfoVect0LinkObjId="SW-194733_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194732_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1358,-358 1358,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_325dcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1430,-358 1430,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29564@1" ObjectIDZND0="29566@1" Pin0InfoVect0LinkObjId="SW-194729_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-194728_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1430,-358 1430,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3261740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,-344 1698,-344 1698,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_30c9cb0@0" ObjectIDND1="0@x" ObjectIDZND0="g_3261b20@0" Pin0InfoVect0LinkObjId="g_3261b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_30c9cb0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1667,-344 1698,-344 1698,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3261930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,-344 1667,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_3261b20@0" ObjectIDND1="0@x" ObjectIDZND0="g_30c9cb0@0" Pin0InfoVect0LinkObjId="g_30c9cb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3261b20_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1667,-344 1667,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3262700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1811,-343 1842,-343 1842,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_30c8a70@0" ObjectIDND1="0@x" ObjectIDZND0="g_3262960@0" Pin0InfoVect0LinkObjId="g_3262960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_30c8a70_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1811,-343 1842,-343 1842,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3263710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1812,-343 1812,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_3262960@0" ObjectIDND1="0@x" ObjectIDZND0="g_30c8a70@0" Pin0InfoVect0LinkObjId="g_30c8a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3262960_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1812,-343 1812,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3266e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,-208 1667,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1667,-208 1667,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32670f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1667,-324 1667,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="g_3261b20@0" ObjectIDZND1="g_30c9cb0@0" Pin0InfoVect0LinkObjId="g_3261b20_0" Pin0InfoVect1LinkObjId="g_30c9cb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1667,-324 1667,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30c7e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1812,-240 1812,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1812,-240 1812,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30c80e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1812,-327 1812,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_30c8a70@0" ObjectIDZND1="g_3262960@0" Pin0InfoVect0LinkObjId="g_30c8a70_0" Pin0InfoVect1LinkObjId="g_3262960_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1812,-327 1812,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30d38a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="101,-159 101,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="101,-159 101,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30d78b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="101,-121 101,-72 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="101,-121 101,-72 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30dabb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="101,-36 101,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="101,-36 101,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30dae10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="101,5 77,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_30db2d0@0" Pin0InfoVect0LinkObjId="g_30db2d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="101,5 77,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30db070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="101,-9 101,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_30db2d0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_30db2d0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="101,-9 101,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30de820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="101,5 101,16 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_30db2d0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_30db2d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="101,5 101,16 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30e3810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="101,52 101,94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="101,52 101,94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30e3a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="101,128 101,147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="101,128 101,147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30e6d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="225,-159 225,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="225,-159 225,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30ead10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="225,-121 225,-72 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="225,-121 225,-72 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30ee010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="225,-36 225,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="225,-36 225,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30ee270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="225,-9 225,58 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="225,-9 225,58 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30ee4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="225,55 225,83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="225,55 225,83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30ee730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="225,55 255,55 255,44 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="225,55 255,55 255,44 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30f43b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="200,83 200,60 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_30f4610@0" Pin0InfoVect0LinkObjId="g_30f4610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="200,83 200,60 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30f50a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="225,132 200,132 200,119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="225,132 200,132 200,119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30f5300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="225,119 225,132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="225,119 225,132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30faa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="336,81 336,58 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_30fb130@0" Pin0InfoVect0LinkObjId="g_30fb130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="336,81 336,58 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30fac70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="361,130 336,130 336,117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="361,130 336,130 336,117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30faed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="361,117 361,130 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="361,117 361,130 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_30fe9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="361,-159 361,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="361,-159 361,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31029b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="361,-123 361,-74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="361,-123 361,-74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3105cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="361,-38 361,-28 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="361,-38 361,-28 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31086b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="255,8 255,0 395,0 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="255,8 255,0 395,0 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3108910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="361,81 361,54 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="361,81 361,54 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3108b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="361,54 361,-11 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="361,54 361,-11 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_310bae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="395,0 395,8 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="395,0 395,8 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_310bd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="395,44 395,54 361,54 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="395,44 395,54 361,54 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_310bfa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="225,156 190,156 190,170 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="225,156 190,156 190,170 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_310c520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="225,176 225,156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="switch" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="225,176 225,156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_310c780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="225,156 225,132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="switch" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="225,156 225,132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_310cb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="190,185 190,193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_310cdd0@0" Pin0InfoVect0LinkObjId="g_310cdd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="190,185 190,193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_310e440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="225,245 201,245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_310e6a0@0" Pin0InfoVect0LinkObjId="g_310e6a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="225,245 201,245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31104c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="326,182 326,190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_31106f0@0" Pin0InfoVect0LinkObjId="g_31106f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="326,182 326,190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3111bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="361,242 337,242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_3111e10@0" Pin0InfoVect0LinkObjId="g_3111e10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="361,242 337,242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3113380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="361,153 361,174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="361,153 361,174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3113570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="361,130 361,153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="361,130 361,153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3113760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="361,153 326,153 326,167 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="361,153 326,153 326,167 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3118b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="549,82 549,59 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_31192b0@0" Pin0InfoVect0LinkObjId="g_31192b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="549,82 549,59 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3118df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="574,131 549,131 549,118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="574,131 549,131 549,118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3119050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="574,118 574,131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="574,118 574,131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3119d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="574,82 574,55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="574,82 574,55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3119fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="574,55 574,-10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="574,55 574,-10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_311cf10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="608,45 608,55 574,55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="608,45 608,55 574,55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_311d620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="539,183 539,191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_311d880@0" Pin0InfoVect0LinkObjId="g_311d880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="539,183 539,191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_311eef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="574,243 550,243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_311f150@0" Pin0InfoVect0LinkObjId="g_311f150_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="574,243 550,243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31206c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="574,154 574,175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="574,154 574,175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31208b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="574,131 574,154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="574,131 574,154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3120aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="574,154 539,154 539,168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="574,154 539,154 539,168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3123810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="574,-159 574,-139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="574,-159 574,-139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3127820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="574,-122 574,-73 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="574,-122 574,-73 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_312ab20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="574,-37 574,-27 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="574,-37 574,-27 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_312db60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="463,-159 463,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="463,-159 463,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3131b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="463,-123 463,-74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="463,-123 463,-74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3134e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="463,-38 463,-28 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="463,-38 463,-28 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31350d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="463,0 463,-11 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="463,0 463,-11 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3135330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="395,0 463,0 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="395,0 463,0 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3139c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="752,-159 752,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="752,-159 752,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_313dc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="752,-123 752,-74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="752,-123 752,-74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3140f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="752,-38 752,-28 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="752,-38 752,-28 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3146af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="727,82 727,59 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_3147210@0" Pin0InfoVect0LinkObjId="g_3147210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="727,82 727,59 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3146d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="752,131 727,131 727,118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="752,131 727,131 727,118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3146fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="752,118 752,131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="752,118 752,131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3147ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="752,82 752,55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="752,82 752,55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3147f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="752,55 752,-10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="752,55 752,-10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_314ae70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="786,45 786,55 752,55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="786,45 786,55 752,55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_314b580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="717,183 717,191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_314b7e0@0" Pin0InfoVect0LinkObjId="g_314b7e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="717,183 717,191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_314ce50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="752,243 728,243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_314d0b0@0" Pin0InfoVect0LinkObjId="g_314d0b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="752,243 728,243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_314e620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="752,154 752,175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="752,154 752,175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_314e810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="752,131 752,154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="752,131 752,154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_314ea00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="752,154 717,154 717,168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="752,154 717,154 717,168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_314fd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="608,0 786,0 786,9 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="608,0 786,0 786,9 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_314fef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="463,0 608,0 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="463,0 608,0 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31500e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="608,0 608,9 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="608,0 608,9 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31600c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1359,82 1359,59 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_31607e0@0" Pin0InfoVect0LinkObjId="g_31607e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1359,82 1359,59 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3160320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1384,131 1359,131 1359,118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1384,131 1359,131 1359,118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3160580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1384,118 1384,131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1384,118 1384,131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3161270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1384,82 1384,55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1384,82 1384,55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31614d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1384,55 1384,-10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1384,55 1384,-10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3164440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1418,45 1418,55 1384,55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1418,45 1418,55 1384,55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3164b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1349,183 1349,191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_3164db0@0" Pin0InfoVect0LinkObjId="g_3164db0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1349,183 1349,191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3166420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1384,243 1360,243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_3166680@0" Pin0InfoVect0LinkObjId="g_3166680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1384,243 1360,243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3167c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1384,154 1384,175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1384,154 1384,175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3167df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1384,131 1384,154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1384,131 1384,154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3167fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1384,154 1349,154 1349,168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1384,154 1349,154 1349,168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_316eae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1384,-122 1384,-73 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1384,-122 1384,-73 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3171de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1384,-37 1384,-27 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1384,-37 1384,-27 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3178bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1273,-123 1273,-74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1273,-123 1273,-74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_317bed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1273,-38 1273,-28 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1273,-38 1273,-28 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3184590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1562,-123 1562,-74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1562,-123 1562,-74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3187890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1562,-38 1562,-28 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1562,-38 1562,-28 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_318d550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1537,82 1537,59 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_318dc70@0" Pin0InfoVect0LinkObjId="g_318dc70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1537,82 1537,59 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_318d7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1562,131 1537,131 1537,118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1562,131 1537,131 1537,118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_318da10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1562,118 1562,131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1562,118 1562,131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_318e700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1562,82 1562,55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1562,82 1562,55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_318e960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1562,55 1562,-10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1562,55 1562,-10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31918c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1596,45 1596,55 1562,55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1596,45 1596,55 1562,55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3191fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1527,183 1527,191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_3192230@0" Pin0InfoVect0LinkObjId="g_3192230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1527,183 1527,191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_31938a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1562,243 1538,243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_3193b00@0" Pin0InfoVect0LinkObjId="g_3193b00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1562,243 1538,243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32725d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1562,154 1562,175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1562,154 1562,175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32727c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1562,131 1562,154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1562,131 1562,154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32729b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1562,154 1527,154 1527,168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1562,154 1527,154 1527,168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3273c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1418,0 1596,0 1596,9 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1418,0 1596,0 1596,9 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3273e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1418,0 1418,9 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1418,0 1418,9 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32779f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1273,-159 1273,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1273,-159 1273,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3277c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1418,0 1273,0 1273,-11 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1418,0 1273,0 1273,-11 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3277eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1384,-159 1384,-139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1384,-159 1384,-139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3278110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1562,-159 1562,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1562,-159 1562,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_327c210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1888,-159 1888,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1888,-159 1888,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3280220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1888,-121 1888,-72 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1888,-121 1888,-72 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3283520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1888,-36 1888,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1888,-36 1888,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3283780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1888,5 1910,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_328c8d0@0" Pin0InfoVect0LinkObjId="g_328c8d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1888,5 1910,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3286180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1888,5 1888,16 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_328c8d0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_328c8d0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1888,5 1888,16 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_328b170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1888,52 1888,94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1888,52 1888,94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_328b3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1888,128 1888,147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1888,128 1888,147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3294130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1805,54 1805,96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1805,54 1805,96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3294390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1805,130 1805,149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1805,130 1805,149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32953a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1888,-9 1888,-3 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_328c8d0@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_328c8d0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1888,-9 1888,-3 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3295590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1888,-3 1888,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_328c8d0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_328c8d0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1888,-3 1888,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3295780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1888,-3 1805,-3 1805,18 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_328c8d0@0" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_328c8d0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1888,-3 1805,-3 1805,18 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-194435" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -43.000000 -817.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29519" ObjectName="DYN-CX_CXBQ"/>
     <cge:Meas_Ref ObjectId="194435"/>
    </metadata>
   </g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="591" cy="-499" fill="none" fillStyle="0" r="16" stroke="rgb(255,255,255)" stroke-width="1.0625"/>
   <circle DF8003:Layer="PUBLIC" cx="774" cy="-499" fill="none" fillStyle="0" r="16" stroke="rgb(255,255,255)" stroke-width="1.0625"/>
   <circle DF8003:Layer="PUBLIC" cx="646" cy="-320" fill="none" fillStyle="0" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="646" cy="-301" fill="none" fillStyle="0" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="646" cy="-339" fill="none" fillStyle="0" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="718" cy="-320" fill="none" fillStyle="0" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="718" cy="-301" fill="none" fillStyle="0" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="718" cy="-339" fill="none" fillStyle="0" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="937" cy="-420" fill="none" fillStyle="0" r="12" stroke="rgb(60,120,255)" stroke-width="0.8125"/>
   <ellipse DF8003:Layer="PUBLIC" cx="1035" cy="-794" fill="none" fillStyle="0" rx="24" ry="24.5" stroke="rgb(60,120,255)" stroke-width="0.510204"/>
   <circle DF8003:Layer="PUBLIC" cx="1035" cy="-755" fill="none" fillStyle="0" r="24" stroke="rgb(60,120,255)" stroke-width="0.510204"/>
   <circle DF8003:Layer="PUBLIC" cx="1302" cy="-502" fill="none" fillStyle="0" r="16" stroke="rgb(255,255,255)" stroke-width="1.0625"/>
   <circle DF8003:Layer="PUBLIC" cx="1485" cy="-502" fill="none" fillStyle="0" r="16" stroke="rgb(255,255,255)" stroke-width="1.0625"/>
   <circle DF8003:Layer="PUBLIC" cx="1357" cy="-323" fill="none" fillStyle="0" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1357" cy="-304" fill="none" fillStyle="0" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1357" cy="-342" fill="none" fillStyle="0" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1429" cy="-323" fill="none" fillStyle="0" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1429" cy="-304" fill="none" fillStyle="0" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1429" cy="-342" fill="none" fillStyle="0" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="100" cy="-105" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="100" cy="-86" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <ellipse DF8003:Layer="PUBLIC" cx="101" cy="73" fill="none" fillStyle="0" rx="7.5" ry="8" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <ellipse DF8003:Layer="PUBLIC" cx="101" cy="197" fill="none" fillStyle="0" rx="7.5" ry="8" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="224" cy="-105" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="224" cy="-86" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="272" cy="26" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="0.4375"/>
   <circle DF8003:Layer="PUBLIC" cx="242" cy="100" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="0.4375"/>
   <circle DF8003:Layer="PUBLIC" cx="378" cy="98" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="0.4375"/>
   <circle DF8003:Layer="PUBLIC" cx="360" cy="-107" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="360" cy="-88" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="412" cy="26" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="0.4375"/>
   <circle DF8003:Layer="PUBLIC" cx="189" cy="177" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="325" cy="174" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="591" cy="99" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="0.4375"/>
   <circle DF8003:Layer="PUBLIC" cx="625" cy="27" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="0.4375"/>
   <circle DF8003:Layer="PUBLIC" cx="538" cy="175" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="573" cy="-106" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="573" cy="-87" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="462" cy="-107" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="462" cy="-88" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="751" cy="-107" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="751" cy="-88" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="769" cy="99" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="0.4375"/>
   <circle DF8003:Layer="PUBLIC" cx="803" cy="27" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="0.4375"/>
   <circle DF8003:Layer="PUBLIC" cx="716" cy="175" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1401" cy="99" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="0.4375"/>
   <circle DF8003:Layer="PUBLIC" cx="1435" cy="27" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="0.4375"/>
   <circle DF8003:Layer="PUBLIC" cx="1348" cy="175" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1383" cy="-106" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1383" cy="-87" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1272" cy="-107" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1272" cy="-88" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1561" cy="-107" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1561" cy="-88" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1579" cy="99" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="0.4375"/>
   <circle DF8003:Layer="PUBLIC" cx="1613" cy="27" fill="none" fillStyle="0" r="7" stroke="rgb(60,120,255)" stroke-width="0.4375"/>
   <circle DF8003:Layer="PUBLIC" cx="1526" cy="175" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1887" cy="-105" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <circle DF8003:Layer="PUBLIC" cx="1887" cy="-86" fill="none" fillStyle="0" r="7.5" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <ellipse DF8003:Layer="PUBLIC" cx="1888" cy="73" fill="none" fillStyle="0" rx="7.5" ry="8" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <ellipse DF8003:Layer="PUBLIC" cx="1888" cy="197" fill="none" fillStyle="0" rx="7.5" ry="8" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <ellipse DF8003:Layer="PUBLIC" cx="1805" cy="75" fill="none" fillStyle="0" rx="7.5" ry="8" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
   <ellipse DF8003:Layer="PUBLIC" cx="1805" cy="199" fill="none" fillStyle="0" rx="7.5" ry="8" stroke="rgb(60,120,255)" stroke-width="0.53125"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="0,-240 882,-240 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="0,-240 882,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="915,-240 1152,-240 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="915,-240 1152,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1190,-240 2005,-240 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1190,-240 2005,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="0,-208 882,-208 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="0,-208 882,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="915,-208 1152,-208 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="915,-208 1152,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1190,-208 2005,-208 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1190,-208 2005,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2,-159 1008,-159 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="2,-159 1008,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1065,-159 2005,-159 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1065,-159 2005,-159 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="915" cy="-240" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1148" cy="-240" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="915" cy="-208" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1148" cy="-208" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="647" cy="-208" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="879" cy="-208" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="719" cy="-240" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="879" cy="-240" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1006" cy="-159" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1190" cy="-240" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1430" cy="-240" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1190" cy="-208" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1358" cy="-208" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1066" cy="-159" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="101" cy="-159" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="225" cy="-159" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="361" cy="-159" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="574" cy="-159" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="463" cy="-159" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="752" cy="-159" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1273" cy="-159" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1384" cy="-159" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1562" cy="-159" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1888" cy="-159" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="269" cy="-208" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="414" cy="-240" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="970" cy="-159" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1101" cy="-159" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1667" cy="-208" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1812" cy="-240" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="-347" y="-809"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="-346" y="-929"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(60,120,255)" stroke-width="1" width="14" x="963" y="-482"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(60,120,255)" stroke-width="1" width="14" x="1093" y="-482"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29b0920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -337.000000 -763.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29b0920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -337.000000 -763.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29b0920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -337.000000 -763.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29b0920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -337.000000 -763.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29b0920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -337.000000 -763.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29b0920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -337.000000 -763.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29b0920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -337.000000 -763.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32705f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32705f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32705f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32705f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32705f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32705f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32705f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32705f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32705f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32705f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32705f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32705f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32705f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32705f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32705f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32705f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32705f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_32705f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -336.000000 -320.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="18" graphid="g_326c4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -199.000000 -896.500000) translate(0,15)">楚雄牵引变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2e04740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1293.000000 -984.000000) translate(0,17)">220kV谢家河变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2e05720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 663.000000 -912.000000) translate(0,17)">紫</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2e05720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 663.000000 -912.000000) translate(0,38)">楚</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2e05720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 663.000000 -912.000000) translate(0,59)">北</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2e05720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 663.000000 -912.000000) translate(0,80)">牵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2e05720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 663.000000 -912.000000) translate(0,101)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_321b0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 591.000000 -984.000000) translate(0,17)">220kV紫溪变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_321b360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1330.000000 -919.000000) translate(0,17)">谢</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_321b360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1330.000000 -919.000000) translate(0,38)">楚</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_321b360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1330.000000 -919.000000) translate(0,59)">北</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_321b360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1330.000000 -919.000000) translate(0,80)">牵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_321b360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1330.000000 -919.000000) translate(0,101)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3225aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 543.000000 -506.500000) translate(0,12)">YD7</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32260d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 795.000000 -506.500000) translate(0,12)">YD8</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32289b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 568.000000 -482.500000) translate(0,12)">5F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3228ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 782.000000 -480.500000) translate(0,12)">3F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32290e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 619.000000 -435.500000) translate(0,12)">AC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3229320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 727.000000 -435.500000) translate(0,12)">AB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3229560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 543.000000 -434.500000) translate(0,12)">2×TJ-240</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32297a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.000000 -435.500000) translate(0,12)">4×TJ-240</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30a73c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 312.000000 -395.500000) translate(0,12)">9F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30a79f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 457.000000 -395.500000) translate(0,12)">7F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30a7c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 227.000000 -395.500000) translate(0,12)">2FU</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30a7e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 374.000000 -395.500000) translate(0,12)">1FU</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30a80b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 220.000000 -457.500000) translate(0,12)">5TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30a82f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 365.000000 -457.500000) translate(0,12)">3TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_319a020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 222.000000 -313.500000) translate(0,12)">2605</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_319a650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 368.000000 -313.500000) translate(0,12)">2603</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31abb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 880.000000 -265.500000) translate(0,12)">2101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ac160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1155.000000 -265.500000) translate(0,12)">2102</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ac3a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 879.500000 -198.500000) translate(0,12)">2103</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ac5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1154.500000 -198.500000) translate(0,12)">2104</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3235b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 931.000000 -427.500000) translate(0,12)">M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3236a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 885.000000 -427.500000) translate(0,12)">2631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_323c6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 908.000000 -517.500000) translate(0,12)">26310</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_32429a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 998.000000 -905.000000) translate(0,17)">至交流屏</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3244520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1254.000000 -509.500000) translate(0,12)">YD5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32470c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1279.000000 -485.500000) translate(0,12)">4F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32476f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1493.000000 -483.500000) translate(0,12)">2F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3247930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1330.000000 -438.500000) translate(0,12)">AC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3247b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1438.000000 -438.500000) translate(0,12)">AB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3247db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1254.000000 -437.500000) translate(0,12)">2×TJ-240</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3247ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1470.000000 -438.500000) translate(0,12)">4×TJ-240</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3260150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 599.000000 -327.500000) translate(0,12)">5TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3260780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 749.000000 -327.500000) translate(0,12)">3TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32609c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 927.000000 -477.500000) translate(0,12)">5FU</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3260c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1059.000000 -477.500000) translate(0,12)">6FU</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3260e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1068.000000 -779.500000) translate(0,12)">3T</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3261080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1504.000000 -510.500000) translate(0,12)">YD6</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32612c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1312.000000 -327.500000) translate(0,12)">4TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3261500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1458.000000 -327.500000) translate(0,12)">2TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3263970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1710.000000 -395.500000) translate(0,12)">10F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3263e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1855.000000 -395.500000) translate(0,12)">8F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32640a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1625.000000 -395.500000) translate(0,12)">4FU</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32642e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1772.000000 -395.500000) translate(0,12)">3FU</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3264520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1618.000000 -457.500000) translate(0,12)">6TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3264760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1763.000000 -457.500000) translate(0,12)">4TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30c8340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1620.000000 -313.500000) translate(0,12)">2606</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30c8830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1766.000000 -313.500000) translate(0,12)">2604</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30cc310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -27.000000 -247.500000) translate(0,12)">AB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30cc940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -27.500000 -215.500000) translate(0,12)">AC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ccb80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -27.500000 -166.500000) translate(0,12)">AC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ce880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2019.000000 -247.500000) translate(0,12)">AB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ced10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2018.500000 -215.500000) translate(0,12)">AC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30cef50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2019.000000 -166.500000) translate(0,12)">AB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30e0580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 51.000000 66.500000) translate(0,12)">15TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30e0bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 55.000000 -57.500000) translate(0,12)">235B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30e0df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 37.000000 -18.500000) translate(0,12)">15F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30e1030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 48.000000 32.500000) translate(0,12)">2351B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30e1270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 53.000000 -101.500000) translate(0,12)">13TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30e3cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 66.000000 164.500000) translate(0,12)">3L</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30eeb20" transform="matrix(0.538462 -0.000000 -0.000000 0.538462 268.769231 21.961538) translate(0,12)">M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30f1830" transform="matrix(0.538462 -0.000000 -0.000000 0.538462 238.769231 95.961538) translate(0,12)">M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30f7e90" transform="matrix(0.538462 -0.000000 -0.000000 0.538462 374.769231 93.961538) translate(0,12)">M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3108f60" transform="matrix(0.538462 -0.000000 -0.000000 0.538462 408.769231 21.961538) translate(0,12)">M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_310f5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 211.000000 286.500000) translate(0,12)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3112d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 347.000000 286.500000) translate(0,12)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3116010" transform="matrix(0.538462 -0.000000 -0.000000 0.538462 587.769231 94.961538) translate(0,12)">M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_311a390" transform="matrix(0.538462 -0.000000 -0.000000 0.538462 621.769231 22.961538) translate(0,12)">M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3120090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 521.500000 286.500000) translate(0,12)">大理下行（AC）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3135590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 475.000000 -59.500000) translate(0,12)">241</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3135bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 485.000000 -106.500000) translate(0,12)">11TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3135e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 597.000000 -106.500000) translate(0,12)">9TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3136040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 588.000000 -59.500000) translate(0,12)">251</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3136280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 619.000000 39.500000) translate(0,12)">2512</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31364c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 503.000000 93.500000) translate(0,12)">2510</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3136700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 605.000000 91.500000) translate(0,12)">2511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3136940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 472.000000 135.500000) translate(0,12)">2×LBGLJ-240</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3136b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 486.000000 174.500000) translate(0,12)">3YDQ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3136dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 588.000000 198.500000) translate(0,12)">3K</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3137000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 516.000000 218.500000) translate(0,12)">13F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31411b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 775.000000 -107.500000) translate(0,12)">7TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31417e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 766.000000 -60.500000) translate(0,12)">231</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3143f70" transform="matrix(0.538462 -0.000000 -0.000000 0.538462 765.769231 94.961538) translate(0,12)">M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31482f0" transform="matrix(0.538462 -0.000000 -0.000000 0.538462 799.769231 22.961538) translate(0,12)">M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314dff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 699.500000 286.500000) translate(0,12)">大理上行（AC）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314ebf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 797.000000 39.500000) translate(0,12)">2312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314ef80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 681.000000 93.500000) translate(0,12)">2310</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314f1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 783.000000 91.500000) translate(0,12)">2311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314f400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 650.000000 135.500000) translate(0,12)">2×LBGLJ-240</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314f650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 664.000000 174.500000) translate(0,12)">1YDQ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314f880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 766.000000 198.500000) translate(0,12)">1K</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314fac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 694.000000 218.500000) translate(0,12)">11F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_315d540" transform="matrix(0.538462 -0.000000 -0.000000 0.538462 1397.769231 94.961538) translate(0,12)">M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31618c0" transform="matrix(0.538462 -0.000000 -0.000000 0.538462 1431.769231 22.961538) translate(0,12)">M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31675c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1331.500000 286.500000) translate(0,12)">广通北下行（AB）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_317c130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1285.000000 -59.500000) translate(0,12)">242</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_317c760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1295.000000 -106.500000) translate(0,12)">12TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_317c9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1407.000000 -106.500000) translate(0,12)">10TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_317cbe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1398.000000 -59.500000) translate(0,12)">252</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_317ce20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1429.000000 39.500000) translate(0,12)">2522</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_317d060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1313.000000 93.500000) translate(0,12)">2520</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_317d2a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1415.000000 91.500000) translate(0,12)">2521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_317d4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.000000 135.500000) translate(0,12)">2×LBGLJ-240</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_317d730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1296.000000 174.500000) translate(0,12)">4YDQ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_317d960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1398.000000 198.500000) translate(0,12)">4K</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_317dba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1326.000000 218.500000) translate(0,12)">13F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3187af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1585.000000 -107.500000) translate(0,12)">8TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3188120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1576.000000 -60.500000) translate(0,12)">232</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_318a9e0" transform="matrix(0.538462 -0.000000 -0.000000 0.538462 1575.769231 94.961538) translate(0,12)">M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_318ed50" transform="matrix(0.538462 -0.000000 -0.000000 0.538462 1609.769231 22.961538) translate(0,12)">M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3194a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1509.500000 286.500000) translate(0,12)">广通北上行（AB）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3272ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1607.000000 39.500000) translate(0,12)">2322</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3272f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1491.000000 93.500000) translate(0,12)">2320</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3273140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1593.000000 91.500000) translate(0,12)">2321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3273380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1460.000000 135.500000) translate(0,12)">2×LBGLJ-240</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32735d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1474.000000 174.500000) translate(0,12)">2YDQ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3273800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1576.000000 198.500000) translate(0,12)">2K</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3273a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1504.000000 218.500000) translate(0,12)">11F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3287ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1838.000000 66.500000) translate(0,12)">16TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3288510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1842.000000 -57.500000) translate(0,12)">235A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3288750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1928.000000 -16.500000) translate(0,12)">16F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3288990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1835.000000 32.500000) translate(0,12)">2351A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3288bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1840.000000 -101.500000) translate(0,12)">14TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_328b630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1844.000000 103.500000) translate(0,12)">1C</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32915c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1755.000000 68.500000) translate(0,12)">20TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3291ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1752.000000 34.500000) translate(0,12)">2352A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32945f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1761.000000 105.500000) translate(0,12)">2C</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3295990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 58.000000 103.500000) translate(0,12)">3C</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3295e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 50.000000 188.500000) translate(0,12)">17TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3296050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1767.000000 167.500000) translate(0,12)">2L</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3296290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1853.000000 167.500000) translate(0,12)">1L</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32964d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1757.000000 206.500000) translate(0,12)">22TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3296710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1840.000000 206.500000) translate(0,12)">18TA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_32989c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 684.000000 -631.000000) translate(0,17)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3298e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1395.000000 -634.000000) translate(0,17)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32990b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 656.000000 -733.000000) translate(0,12)">111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32992f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 654.000000 -781.000000) translate(0,12)">2516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3299530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 587.000000 -834.000000) translate(0,12)">25167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3299770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 566.000000 -900.000000) translate(0,12)">2519</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32999b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 527.000000 -946.000000) translate(0,12)">25197</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3299bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 726.000000 -380.000000) translate(0,12)">201A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3299e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 654.000000 -380.000000) translate(0,12)">201B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_329a070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1366.000000 -740.000000) translate(0,12)">112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_329a2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1364.000000 -788.000000) translate(0,12)">2526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_329a4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1378.000000 -841.000000) translate(0,12)">25267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_329a730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1406.000000 -905.000000) translate(0,12)">2529</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_329a970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1494.000000 -953.000000) translate(0,12)">25297</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_329abb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1437.000000 -383.000000) translate(0,12)">202A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_329adf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1365.000000 -383.000000) translate(0,12)">202B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32a4250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -222.000000 37.500000) translate(0,12)">0871-66124214</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2939590" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 549.000000 -802.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2939fc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1430.000000 -809.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3063230" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 572.000000 -914.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2dff370" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1546.000000 -921.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30f4610" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 194.000000 65.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30fb130" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 330.000000 63.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_310cdd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 184.000000 211.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31106f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 320.000000 208.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31192b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 543.000000 64.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_311d880" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 533.000000 209.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3147210" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 721.000000 64.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_314b7e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 711.000000 209.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31607e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1353.000000 64.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3164db0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1343.000000 209.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_318dc70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1531.000000 64.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3192230" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1521.000000 209.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_CXBQ"/>
</svg>