<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-85" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3115 -1194 2069 1071">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape137">
    <ellipse cx="18" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="25" x2="28" y1="15" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="31" x2="28" y1="15" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="28" x2="28" y1="17" y2="19"/>
    <ellipse cx="28" cy="16" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="14" x2="20" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="16" x2="14" y1="19" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="17" x2="20" y1="19" y2="22"/>
    <ellipse cx="17" cy="21" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="5" x2="8" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="11" x2="8" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="8" x2="8" y1="13" y2="16"/>
    <ellipse cx="8" cy="14" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="15" x2="18" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="18" x2="18" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="21" x2="18" y1="6" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="55" x2="55" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="54" x2="46" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="59" x2="59" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="62" x2="62" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="19" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="39" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape25_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
   </symbol>
   <symbol id="switch2:shape25_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape66_0">
    <circle cx="31" cy="80" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="31" y1="50" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <polyline DF8003:Layer="PUBLIC" points="31,12 25,25 37,25 31,12 31,13 31,12 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="43" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="79" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="79" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="79" y2="74"/>
   </symbol>
   <symbol id="transformer2:shape66_1">
    <circle cx="31" cy="58" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,55 6,55 6,26 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="55" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="55" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="55" y2="60"/>
   </symbol>
   <symbol id="transformer2:shape43_0">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="60" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="68" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="76" y2="68"/>
   </symbol>
   <symbol id="transformer2:shape43_1">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape34_0">
    <ellipse cx="13" cy="43" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="13" y1="7" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="9" y1="0" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="31" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="42" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="37" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="39" x2="35" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="46" x2="28" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="40" x2="33" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="38" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="42" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="46" y2="42"/>
   </symbol>
   <symbol id="transformer2:shape34_1">
    <circle cx="13" cy="62" fillStyle="0" r="13" stroke-width="0.265306"/>
    <polyline DF8003:Layer="PUBLIC" points="12,70 8,61 18,61 12,70 "/>
   </symbol>
   <symbol id="voltageTransformer:shape116">
    <ellipse cx="8" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.238574" x1="10" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.245503" x1="9" x2="10" y1="18" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.246311" x1="6" x2="5" y1="18" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="11" x2="8" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="8" x2="8" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="5" x2="8" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="33" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="36" y1="44" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="35" y1="42" y2="42"/>
    <polyline points="20,8 37,8 37,19 " stroke-width="1"/>
    <ellipse cx="19" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <ellipse cx="8" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <ellipse cx="19" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <rect height="13" stroke-width="1" width="8" x="33" y="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="32" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="31" y1="21" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="27" y2="30"/>
   </symbol>
   <symbol id="voltageTransformer:shape106">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="6" y2="16"/>
    <rect height="13" stroke-width="1" width="5" x="3" y="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="35" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="6" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="1" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="1" y1="28" y2="19"/>
    <ellipse cx="25" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="34" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="25" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="25" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="37" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="37" y1="24" y2="22"/>
    <ellipse cx="25" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="34" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="40" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="34" y1="33" y2="33"/>
    <ellipse cx="36" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="36" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="25" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="25" y1="24" y2="22"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1abcbd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1abdd40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1abe6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1abf3c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_196dba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1ac0450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ac0eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1ac1970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_17b7cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_17b7cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ac4c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ac4c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ac6590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ac6590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1ac75a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ac8f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1ac9b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1acaa30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1acb310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1accad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1acd7d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ace090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1ace850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1acf930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ad02b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ad0da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1ad1760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1ad2be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1ad3780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1ad47b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1ad53f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1ae3bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ad6a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1ad7730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1ad8c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1081" width="2079" x="3110" y="-1199"/>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3486,-291 3497,-291 3491,-283 3486,-291 " stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-239349">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4675.215686 -340.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11471" ObjectName="SW-LF_ZC.LF_ZC_465BK"/>
     <cge:Meas_Ref ObjectId="239349"/>
    <cge:TPSR_Ref TObjectID="11471"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59817">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4641.000000 -690.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17392" ObjectName="SW-LF_ZC.LF_ZC_302BK"/>
     <cge:Meas_Ref ObjectId="59817"/>
    <cge:TPSR_Ref TObjectID="17392"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59820">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4641.000000 -517.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17393" ObjectName="SW-LF_ZC.LF_ZC_002BK"/>
     <cge:Meas_Ref ObjectId="59820"/>
    <cge:TPSR_Ref TObjectID="17393"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236346">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3592.000000 -338.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11459" ObjectName="SW-LF_ZC.LF_ZC_461BK"/>
     <cge:Meas_Ref ObjectId="236346"/>
    <cge:TPSR_Ref TObjectID="11459"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3785.333333 -338.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236376">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3983.333333 -339.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11465" ObjectName="SW-LF_ZC.LF_ZC_463BK"/>
     <cge:Meas_Ref ObjectId="236376"/>
    <cge:TPSR_Ref TObjectID="11465"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236410">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4177.333333 -336.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11468" ObjectName="SW-LF_ZC.LF_ZC_464BK"/>
     <cge:Meas_Ref ObjectId="236410"/>
    <cge:TPSR_Ref TObjectID="11468"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59749">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3952.000000 -694.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11450" ObjectName="SW-LF_ZC.LF_ZC_301BK"/>
     <cge:Meas_Ref ObjectId="59749"/>
    <cge:TPSR_Ref TObjectID="11450"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59746">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3952.000000 -517.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11456" ObjectName="SW-LF_ZC.LF_ZC_001BK"/>
     <cge:Meas_Ref ObjectId="59746"/>
    <cge:TPSR_Ref TObjectID="11456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236442">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4283.000000 -512.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11474" ObjectName="SW-LF_ZC.LF_ZC_012BK"/>
     <cge:Meas_Ref ObjectId="236442"/>
    <cge:TPSR_Ref TObjectID="11474"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_22aabe0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4129.000000 -604.000000)" xlink:href="#voltageTransformer:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_234e840">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4939.000000 -204.000000)" xlink:href="#voltageTransformer:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_SY" endPointId="0" endStationName="LF_ZC" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_ZhongCun" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4238,-1109 4238,-1139 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18096" ObjectName="AC-35kV.LN_ZhongCun"/>
    <cge:TPSR_Ref TObjectID="18096_SS-85"/></metadata>
   <polyline fill="none" opacity="0" points="4238,-1109 4238,-1139 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4147.000000 -887.000000)" xlink:href="#transformer2:shape66_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4147.000000 -887.000000)" xlink:href="#transformer2:shape66_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-LF_ZC.LF_ZC_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="16026"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3936.000000 -585.000000)" xlink:href="#transformer2:shape43_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3936.000000 -585.000000)" xlink:href="#transformer2:shape43_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="11437" ObjectName="TF-LF_ZC.LF_ZC_1T"/>
    <cge:TPSR_Ref TObjectID="11437"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-LF_ZC.LF_ZC_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="24342"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4611.000000 -575.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4611.000000 -575.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="11438" ObjectName="TF-LF_ZC.LF_ZC_2T"/>
    <cge:TPSR_Ref TObjectID="11438"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3478.000000 -272.000000)" xlink:href="#transformer2:shape34_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3478.000000 -272.000000)" xlink:href="#transformer2:shape34_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_246bab0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4275.000000 -995.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2323af0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4584.000000 -907.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23eeed0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4575.000000 -953.000000)" xlink:href="#lightningRod:shape137"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23ba0c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3609.000000 -277.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24bf920">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3998.000000 -278.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_230e690">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4192.000000 -250.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_232adf0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4167.000000 -523.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2320b00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3800.000000 -278.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23da820">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4690.000000 -279.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_234e3e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4921.000000 -293.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2251780">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4620.000000 -900.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23268a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4409.000000 -843.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3228.000000 -1088.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-81000" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3257.000000 -944.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81000" ObjectName="LF_ZC:LF_ZC_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-81001" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3257.000000 -901.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81001" ObjectName="LF_ZC:LF_ZC_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-81000" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3257.000000 -1023.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81000" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-81000" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3257.000000 -983.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81000" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-78923" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4547.000000 -631.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78923" ObjectName="LF_ZC:LF_ZC_2T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-78870" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4547.000000 -651.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78870" ObjectName="LF_ZC:LF_ZC_2T_Tp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-78846" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3870.000000 -632.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78846" ObjectName="LF_ZC:LF_ZC_1T_Tmp"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-78874" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3605.000000 -169.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78874" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11459"/>
     <cge:Term_Ref ObjectID="15980"/>
    <cge:TPSR_Ref TObjectID="11459"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-78875" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3605.000000 -169.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78875" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11459"/>
     <cge:Term_Ref ObjectID="15980"/>
    <cge:TPSR_Ref TObjectID="11459"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-78871" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3605.000000 -169.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78871" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11459"/>
     <cge:Term_Ref ObjectID="15980"/>
    <cge:TPSR_Ref TObjectID="11459"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-78879" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3997.000000 -169.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78879" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11465"/>
     <cge:Term_Ref ObjectID="15992"/>
    <cge:TPSR_Ref TObjectID="11465"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-78880" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3997.000000 -169.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78880" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11465"/>
     <cge:Term_Ref ObjectID="15992"/>
    <cge:TPSR_Ref TObjectID="11465"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-78876" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3997.000000 -169.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78876" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11465"/>
     <cge:Term_Ref ObjectID="15992"/>
    <cge:TPSR_Ref TObjectID="11465"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-236542" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4196.000000 -169.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236542" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11468"/>
     <cge:Term_Ref ObjectID="15998"/>
    <cge:TPSR_Ref TObjectID="11468"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-236543" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4196.000000 -169.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236543" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11468"/>
     <cge:Term_Ref ObjectID="15998"/>
    <cge:TPSR_Ref TObjectID="11468"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-78882" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4196.000000 -169.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78882" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11468"/>
     <cge:Term_Ref ObjectID="15998"/>
    <cge:TPSR_Ref TObjectID="11468"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-78843" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3870.000000 -561.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78843" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11456"/>
     <cge:Term_Ref ObjectID="15974"/>
    <cge:TPSR_Ref TObjectID="11456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-78844" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3870.000000 -561.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78844" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11456"/>
     <cge:Term_Ref ObjectID="15974"/>
    <cge:TPSR_Ref TObjectID="11456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-78840" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3870.000000 -561.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78840" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11456"/>
     <cge:Term_Ref ObjectID="15974"/>
    <cge:TPSR_Ref TObjectID="11456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-78831" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3842.000000 -758.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78831" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11450"/>
     <cge:Term_Ref ObjectID="15962"/>
    <cge:TPSR_Ref TObjectID="11450"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-78832" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3842.000000 -758.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78832" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11450"/>
     <cge:Term_Ref ObjectID="15962"/>
    <cge:TPSR_Ref TObjectID="11450"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-78828" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3842.000000 -758.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78828" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11450"/>
     <cge:Term_Ref ObjectID="15962"/>
    <cge:TPSR_Ref TObjectID="11450"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-236548" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4341.000000 -629.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236548" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11474"/>
     <cge:Term_Ref ObjectID="16010"/>
    <cge:TPSR_Ref TObjectID="11474"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-236549" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4341.000000 -629.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236549" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11474"/>
     <cge:Term_Ref ObjectID="16010"/>
    <cge:TPSR_Ref TObjectID="11474"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-236545" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4341.000000 -629.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236545" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11474"/>
     <cge:Term_Ref ObjectID="16010"/>
    <cge:TPSR_Ref TObjectID="11474"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-78815" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3960.000000 -927.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78815" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17394"/>
     <cge:Term_Ref ObjectID="16018"/>
    <cge:TPSR_Ref TObjectID="17394"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-78816" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3960.000000 -927.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78816" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17394"/>
     <cge:Term_Ref ObjectID="16018"/>
    <cge:TPSR_Ref TObjectID="17394"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-78817" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3960.000000 -927.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78817" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17394"/>
     <cge:Term_Ref ObjectID="16018"/>
    <cge:TPSR_Ref TObjectID="17394"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-78821" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3960.000000 -927.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78821" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17394"/>
     <cge:Term_Ref ObjectID="16018"/>
    <cge:TPSR_Ref TObjectID="17394"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-78818" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3960.000000 -927.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78818" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17394"/>
     <cge:Term_Ref ObjectID="16018"/>
    <cge:TPSR_Ref TObjectID="17394"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-236551" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3547.000000 -564.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236551" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17395"/>
     <cge:Term_Ref ObjectID="16019"/>
    <cge:TPSR_Ref TObjectID="17395"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-236552" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3547.000000 -564.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236552" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17395"/>
     <cge:Term_Ref ObjectID="16019"/>
    <cge:TPSR_Ref TObjectID="17395"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-236553" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3547.000000 -564.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236553" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17395"/>
     <cge:Term_Ref ObjectID="16019"/>
    <cge:TPSR_Ref TObjectID="17395"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-236558" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3547.000000 -564.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236558" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17395"/>
     <cge:Term_Ref ObjectID="16019"/>
    <cge:TPSR_Ref TObjectID="17395"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-236555" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3547.000000 -564.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236555" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17395"/>
     <cge:Term_Ref ObjectID="16019"/>
    <cge:TPSR_Ref TObjectID="17395"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-236557" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3547.000000 -564.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236557" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17395"/>
     <cge:Term_Ref ObjectID="16019"/>
    <cge:TPSR_Ref TObjectID="17395"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-78866" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4777.000000 -546.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78866" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17393"/>
     <cge:Term_Ref ObjectID="16022"/>
    <cge:TPSR_Ref TObjectID="17393"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-78867" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4777.000000 -546.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78867" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17393"/>
     <cge:Term_Ref ObjectID="16022"/>
    <cge:TPSR_Ref TObjectID="17393"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-78922" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4777.000000 -546.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78922" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17393"/>
     <cge:Term_Ref ObjectID="16022"/>
    <cge:TPSR_Ref TObjectID="17393"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-78891" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4879.000000 -738.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78891" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17392"/>
     <cge:Term_Ref ObjectID="16020"/>
    <cge:TPSR_Ref TObjectID="17392"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-78892" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4879.000000 -738.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78892" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17392"/>
     <cge:Term_Ref ObjectID="16020"/>
    <cge:TPSR_Ref TObjectID="17392"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-78852" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4879.000000 -738.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78852" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17392"/>
     <cge:Term_Ref ObjectID="16020"/>
    <cge:TPSR_Ref TObjectID="17392"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-78898" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4974.000000 -584.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78898" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17396"/>
     <cge:Term_Ref ObjectID="16028"/>
    <cge:TPSR_Ref TObjectID="17396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-78899" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4974.000000 -584.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78899" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17396"/>
     <cge:Term_Ref ObjectID="16028"/>
    <cge:TPSR_Ref TObjectID="17396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-78900" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4974.000000 -584.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78900" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17396"/>
     <cge:Term_Ref ObjectID="16028"/>
    <cge:TPSR_Ref TObjectID="17396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-236562" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4974.000000 -584.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236562" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17396"/>
     <cge:Term_Ref ObjectID="16028"/>
    <cge:TPSR_Ref TObjectID="17396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-236560" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4974.000000 -584.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236560" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17396"/>
     <cge:Term_Ref ObjectID="16028"/>
    <cge:TPSR_Ref TObjectID="17396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-236563" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4974.000000 -584.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236563" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17396"/>
     <cge:Term_Ref ObjectID="16028"/>
    <cge:TPSR_Ref TObjectID="17396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-239328" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4699.000000 -171.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="239328" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11471"/>
     <cge:Term_Ref ObjectID="16004"/>
    <cge:TPSR_Ref TObjectID="11471"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-239329" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4699.000000 -171.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="239329" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11471"/>
     <cge:Term_Ref ObjectID="16004"/>
    <cge:TPSR_Ref TObjectID="11471"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-239325" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4699.000000 -171.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="239325" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11471"/>
     <cge:Term_Ref ObjectID="16004"/>
    <cge:TPSR_Ref TObjectID="11471"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3248" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3199" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="31" x="3613" y="-368"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="31" x="3613" y="-368"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="31" x="4002" y="-368"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="31" x="4002" y="-368"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="32" x="4196" y="-365"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="32" x="4196" y="-365"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="26" qtmmishow="hidden" width="77" x="3993" y="-646"/>
    </a>
   <metadata/><rect fill="white" height="26" opacity="0" stroke="white" transform="" width="77" x="3993" y="-646"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="22" qtmmishow="hidden" width="77" x="4689" y="-651"/>
    </a>
   <metadata/><rect fill="white" height="22" opacity="0" stroke="white" transform="" width="77" x="4689" y="-651"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="4301" y="-541"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="4301" y="-541"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3412" y="-1144"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3412" y="-1144"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3412" y="-1179"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3412" y="-1179"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="72" x="3152" y="-801"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="72" x="3152" y="-801"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="3403" y="-1051"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="3403" y="-1051"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="31" x="4694" y="-369"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="31" x="4694" y="-369"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/></g>
   <g href="35kV中村变中村变中科线461断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="31" x="3613" y="-368"/></g>
   <g href="35kV中村变中村变10kV中叽线463断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="31" x="4002" y="-368"/></g>
   <g href="35kV中村变中村变10kV中鲁线464断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="32" x="4196" y="-365"/></g>
   <g href="35kV中村变中村变＃1主变主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="26" qtmmishow="hidden" width="77" x="3993" y="-646"/></g>
   <g href="35kV中村变中村变＃2主变主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="22" qtmmishow="hidden" width="77" x="4689" y="-651"/></g>
   <g href="35kV中村变中村变母联012断路器A相电流Ia间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="4301" y="-541"/></g>
   <g href="cx_配调_配网接线图35_禄丰.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3412" y="-1144"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3412" y="-1179"/></g>
   <g href="35kV中村变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="72" x="3152" y="-801"/></g>
   <g href="AVC中村站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="3403" y="-1051"/></g>
   <g href="35kV中村变中村变10kV滇中引水中村石料厂专线465断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="31" x="4694" y="-369"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="3404" y="-1050"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-LF_ZC.LF_ZC_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3452,-446 4324,-446 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17395" ObjectName="BS-LF_ZC.LF_ZC_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="17395"/></metadata>
   <polyline fill="none" opacity="0" points="3452,-446 4324,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_ZC.LF_ZC_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3871,-814 4721,-814 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17394" ObjectName="BS-LF_ZC.LF_ZC_3ⅠM"/>
    <cge:TPSR_Ref TObjectID="17394"/></metadata>
   <polyline fill="none" opacity="0" points="3871,-814 4721,-814 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_ZC.LF_ZC_9ⅡM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4363,-446 5184,-446 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17396" ObjectName="BS-LF_ZC.LF_ZC_9ⅡM"/>
    <cge:TPSR_Ref TObjectID="17396"/></metadata>
   <polyline fill="none" opacity="0" points="4363,-446 5184,-446 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_24733e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4274.000000 -829.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_234e100" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4539.000000 -943.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20f1340" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4760.000000 -731.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="17394" cx="4238" cy="-814" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17396" cx="4684" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17394" cx="4650" cy="-814" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17394" cx="4416" cy="-814" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17394" cx="3961" cy="-814" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17396" cx="4394" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17395" cx="3601" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17395" cx="3794" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17395" cx="3992" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17395" cx="4186" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17395" cx="4137" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17395" cx="3961" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17395" cx="4292" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17395" cx="3492" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17396" cx="4650" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17396" cx="4963" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17394" cx="4593" cy="-814" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1fe7920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1fe7920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1fe7920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1fe7920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1fe7920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1fe7920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1fe7920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1fe7920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1fe7920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ff9860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ff9860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ff9860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ff9860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ff9860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ff9860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ff9860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ff9860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ff9860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ff9860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ff9860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ff9860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ff9860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ff9860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ff9860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ff9860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ff9860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1ff9860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1e8bb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3283.000000 -1166.500000) translate(0,16)">中村变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_23df760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4207.000000 -1170.000000) translate(0,18)">35kV中村线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2203750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4154.333333 -204.000000) translate(0,18)">中鲁线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2679f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4509.000000 -1011.000000) translate(0,18)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2243d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3569.333333 -204.000000) translate(0,18)">中科线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_20bd5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3765.333333 -182.000000) translate(0,18)">备用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2120040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3961.333333 -204.000000) translate(0,18)">中叽线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_23f5770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4573.333333 -203.000000) translate(0,18)">滇中引水中村石料厂专线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2471e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4087.000000 -670.000000) translate(0,15)">10kV I 母电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2315390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4099.000000 -877.000000) translate(0,18)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23c27f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4659.000000 -771.000000) translate(0,15)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2474fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4662.000000 -495.000000) translate(0,15)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23dff00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4193.000000 -414.000000) translate(0,15)">4641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2412f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4194.000000 -319.000000) translate(0,15)">4642</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2317490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3976.000000 -497.000000) translate(0,15)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2194380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4691.000000 -322.000000) translate(0,15)">4652</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2298930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4691.000000 -418.000000) translate(0,15)">4651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2298610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3984.000000 -548.000000) translate(0,15)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22982f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3979.000000 -724.000000) translate(0,15)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20d5970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3608.000000 -416.000000) translate(0,15)">4611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24c22f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3613.000000 -368.000000) translate(0,15)">461</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22acda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3609.000000 -320.000000) translate(0,15)">4612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24141f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3801.000000 -416.000000) translate(0,15)">4621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23a4780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3804.000000 -367.000000) translate(0,15)">462</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20e08c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3801.000000 -320.000000) translate(0,15)">4622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2298c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4002.000000 -368.000000) translate(0,15)">463</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_240c910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3999.000000 -417.000000) translate(0,15)">4631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2340e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3999.000000 -321.000000) translate(0,15)">4632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24118a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4196.000000 -365.000000) translate(0,15)">464</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2413c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4694.000000 -369.000000) translate(0,15)">465</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_241bd30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4301.000000 -541.000000) translate(0,15)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22f9b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3968.000000 -770.000000) translate(0,15)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23107d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4659.000000 -546.000000) translate(0,15)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23b6e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4659.000000 -719.000000) translate(0,15)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24b4cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4545.000000 -856.000000) translate(0,15)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2473f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4480.000000 -927.000000) translate(0,15)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_231cd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4287.000000 -886.000000) translate(0,15)">35117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_23d2dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3268.000000 -241.000000) translate(0,17)">19987817320</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2474c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3611.000000 -346.000000) translate(0,15)">200/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2411f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4000.000000 -347.000000) translate(0,15)">200/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20d42c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4196.000000 -341.000000) translate(0,15)">200/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2349e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3991.000000 -608.000000) translate(0,15)">S9-3150kVA/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fd8910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4685.000000 -602.000000) translate(0,15)">SZ11-5000kVA/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_22ab910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4880.000000 -195.000000) translate(0,18)">10kV II 母电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2191ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3444.000000 -259.000000) translate(0,18)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24bff80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3366.000000 -469.000000) translate(0,15)">10kV I 段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22fd110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5049.000000 -474.000000) translate(0,15)">10kV II段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_231a6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3791.000000 -831.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23a45b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4706.000000 -762.000000) translate(0,15)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_23157f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3423.000000 -1136.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_24becf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3423.000000 -1171.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_230b130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3998.000000 -639.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_246e5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4692.000000 -649.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23cdb40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4245.000000 -954.000000) translate(0,12)">3511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2340700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4149.000000 -490.000000) translate(0,15)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23d2bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4403.000000 -505.000000) translate(0,15)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24026b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3154.000000 -799.000000) translate(0,15)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fe6fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4970.000000 -398.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_21865e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3418.000000 -1038.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_24b2580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3268.000000 -212.000000) translate(0,17)">18887829215</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_247dfb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3115.000000 -187.000000) translate(0,17)">禄丰巡维中心</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_247dfb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3115.000000 -187.000000) translate(0,38)">禄丰变值班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2323ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3268.000000 -176.500000) translate(0,17)">13908784381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_24b78a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4449.000000 -653.000000) translate(0,16)">档位(档)：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_246f400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4449.000000 -633.000000) translate(0,16)">油温(℃)：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_21a9f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3772.000000 -632.000000) translate(0,16)">油温(℃)：</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2356730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3824.000000 528.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e8cd40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3811.000000 566.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2325d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3801.000000 547.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2415af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3771.000000 743.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2415d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3791.000000 726.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2415f30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3778.000000 761.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_230f3c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4271.000000 614.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_230f5b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4291.000000 597.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_230f7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4278.000000 632.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24bc2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3890.000000 897.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23c6730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3888.000000 882.000000) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23c69b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3880.000000 866.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2372270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3889.000000 929.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23724f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3889.000000 915.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23b69a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4810.000000 723.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23b6c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4830.000000 706.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24132b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4817.000000 741.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20f4ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4707.000000 532.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20f50c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4727.000000 515.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23d0b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4714.000000 550.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2419130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3552.000000 170.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2419530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3541.000000 154.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2419770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3566.000000 138.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24a9270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3942.000000 170.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24a94d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3931.000000 154.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24a9710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3956.000000 138.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24a9a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4138.000000 170.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20d1800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4127.000000 154.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20d19d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.000000 138.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_247d750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4644.000000 172.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_247d940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4633.000000 156.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_247db50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4658.000000 140.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21aa6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3490.000000 534.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e7680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3496.000000 520.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e78a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3482.000000 505.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e7ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3482.000000 490.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e7d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3490.000000 565.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e7f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3490.000000 550.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2354910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4920.000000 551.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2354b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4926.000000 537.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2354db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4912.000000 522.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2354ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4912.000000 507.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2330990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4920.000000 582.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2330bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4920.000000 567.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-LF_ZC.LD_ZC_461">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3592.000000 -215.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18097" ObjectName="EC-LF_ZC.LD_ZC_461"/>
    <cge:TPSR_Ref TObjectID="18097"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_ZC.LF_ZJ_463">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3983.000000 -213.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18098" ObjectName="EC-LF_ZC.LF_ZJ_463"/>
    <cge:TPSR_Ref TObjectID="18098"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_ZC.LD_ZC_464">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4177.000000 -219.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18099" ObjectName="EC-LF_ZC.LD_ZC_464"/>
    <cge:TPSR_Ref TObjectID="18099"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_ZC.LD_ZC_465">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4675.000000 -224.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40329" ObjectName="EC-LF_ZC.LD_ZC_465"/>
    <cge:TPSR_Ref TObjectID="40329"/></metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-52534" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3430.000000 -1067.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9244" ObjectName="DYN-LF_ZC"/>
     <cge:Meas_Ref ObjectId="52534"/>
    </metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-239331">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4676.000000 -388.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11472" ObjectName="SW-LF_ZC.LF_ZC_4651SW"/>
     <cge:Meas_Ref ObjectId="239331"/>
    <cge:TPSR_Ref TObjectID="11472"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-239332">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4676.000000 -292.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11473" ObjectName="SW-LF_ZC.LF_ZC_4652SW"/>
     <cge:Meas_Ref ObjectId="239332"/>
    <cge:TPSR_Ref TObjectID="11473"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59821">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4641.000000 -468.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23055" ObjectName="SW-LF_ZC.LF_ZC_0022SW"/>
     <cge:Meas_Ref ObjectId="59821"/>
    <cge:TPSR_Ref TObjectID="23055"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236348">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3592.000000 -386.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11460" ObjectName="SW-LF_ZC.LF_ZC_4611SW"/>
     <cge:Meas_Ref ObjectId="236348"/>
    <cge:TPSR_Ref TObjectID="11460"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236349">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3592.000000 -290.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11461" ObjectName="SW-LF_ZC.LF_ZC_4612SW"/>
     <cge:Meas_Ref ObjectId="236349"/>
    <cge:TPSR_Ref TObjectID="11461"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236472">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4128.000000 -462.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11458" ObjectName="SW-LF_ZC.LF_ZC_0901SW"/>
     <cge:Meas_Ref ObjectId="236472"/>
    <cge:TPSR_Ref TObjectID="11458"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59742">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3952.000000 -740.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11451" ObjectName="SW-LF_ZC.LF_ZC_3011SW"/>
     <cge:Meas_Ref ObjectId="59742"/>
    <cge:TPSR_Ref TObjectID="11451"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59747">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3952.000000 -468.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11457" ObjectName="SW-LF_ZC.LF_ZC_0011SW"/>
     <cge:Meas_Ref ObjectId="59747"/>
    <cge:TPSR_Ref TObjectID="11457"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3785.000000 -386.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3785.000000 -290.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236380">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3983.000000 -387.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11466" ObjectName="SW-LF_ZC.LF_ZC_4631SW"/>
     <cge:Meas_Ref ObjectId="236380"/>
    <cge:TPSR_Ref TObjectID="11466"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236381">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3983.000000 -291.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11467" ObjectName="SW-LF_ZC.LF_ZC_4632SW"/>
     <cge:Meas_Ref ObjectId="236381"/>
    <cge:TPSR_Ref TObjectID="11467"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236412">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4177.000000 -384.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11469" ObjectName="SW-LF_ZC.LF_ZC_4641SW"/>
     <cge:Meas_Ref ObjectId="236412"/>
    <cge:TPSR_Ref TObjectID="11469"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236413">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4177.000000 -288.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11470" ObjectName="SW-LF_ZC.LF_ZC_4642SW"/>
     <cge:Meas_Ref ObjectId="236413"/>
    <cge:TPSR_Ref TObjectID="11470"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236474">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4954.000000 -368.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17389" ObjectName="SW-LF_ZC.LF_ZC_0902SW"/>
     <cge:Meas_Ref ObjectId="236474"/>
    <cge:TPSR_Ref TObjectID="17389"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59741">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4229.000000 -924.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11452" ObjectName="SW-LF_ZC.LF_ZC_3511SW"/>
     <cge:Meas_Ref ObjectId="59741"/>
    <cge:TPSR_Ref TObjectID="11452"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236444">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4385.000000 -475.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11475" ObjectName="SW-LF_ZC.LF_ZC_0122SW"/>
     <cge:Meas_Ref ObjectId="236444"/>
    <cge:TPSR_Ref TObjectID="11475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59818">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4641.000000 -740.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23054" ObjectName="SW-LF_ZC.LF_ZC_3021SW"/>
     <cge:Meas_Ref ObjectId="59818"/>
    <cge:TPSR_Ref TObjectID="23054"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4174.000000 -994.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59819">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4703.000000 -732.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11455" ObjectName="SW-LF_ZC.LF_ZC_30217SW"/>
     <cge:Meas_Ref ObjectId="59819"/>
    <cge:TPSR_Ref TObjectID="11455"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3485.000000 -359.000000)" xlink:href="#switch2:shape25_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4959.000000 -299.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4132.000000 -524.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59745">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4271.000000 -856.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32005" ObjectName="SW-LF_ZC.LF_ZC_35117SW"/>
     <cge:Meas_Ref ObjectId="59745"/>
    <cge:TPSR_Ref TObjectID="32005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59744">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4536.000000 -893.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11454" ObjectName="SW-LF_ZC.LF_ZC_39017SW"/>
     <cge:Meas_Ref ObjectId="59744"/>
    <cge:TPSR_Ref TObjectID="11454"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-59743">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4584.000000 -830.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11453" ObjectName="SW-LF_ZC.LF_ZC_3901SW"/>
     <cge:Meas_Ref ObjectId="59743"/>
    <cge:TPSR_Ref TObjectID="11453"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_2414600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4684,-446 4684,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17396@0" ObjectIDZND0="11472@1" Pin0InfoVect0LinkObjId="SW-239331_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_231ee70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4684,-446 4684,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f2be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4684,-393 4684,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11472@0" ObjectIDZND0="11471@1" Pin0InfoVect0LinkObjId="SW-239349_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239331_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4684,-393 4684,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22e7c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4684,-348 4684,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="11471@0" ObjectIDZND0="11473@1" Pin0InfoVect0LinkObjId="SW-239332_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239349_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4684,-348 4684,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23f5ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4684,-286 4694,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="11473@x" ObjectIDND1="40329@x" ObjectIDZND0="g_23da820@0" Pin0InfoVect0LinkObjId="g_23da820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-239332_0" Pin1InfoVect1LinkObjId="EC-LF_ZC.LD_ZC_465_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4684,-286 4694,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23261d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4684,-297 4684,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="11473@0" ObjectIDZND0="g_23da820@0" ObjectIDZND1="40329@x" Pin0InfoVect0LinkObjId="g_23da820_0" Pin0InfoVect1LinkObjId="EC-LF_ZC.LD_ZC_465_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-239332_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4684,-297 4684,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20f0bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4684,-285 4684,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="11473@x" ObjectIDND1="g_23da820@0" ObjectIDZND0="40329@0" Pin0InfoVect0LinkObjId="EC-LF_ZC.LD_ZC_465_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-239332_0" Pin1InfoVect1LinkObjId="g_23da820_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4684,-285 4684,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_234c740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4650,-814 4650,-781 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17394@0" ObjectIDZND0="23054@1" Pin0InfoVect0LinkObjId="SW-59818_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21884f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4650,-814 4650,-781 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2322c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4650,-525 4650,-509 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17393@0" ObjectIDZND0="23055@1" Pin0InfoVect0LinkObjId="SW-59821_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59820_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4650,-525 4650,-509 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22e5450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4238,-814 4238,-912 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="17394@0" ObjectIDZND0="11452@x" ObjectIDZND1="32005@x" Pin0InfoVect0LinkObjId="SW-59741_0" Pin0InfoVect1LinkObjId="SW-59745_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21884f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4238,-814 4238,-912 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2320840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4238,-912 4280,-912 4280,-897 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="17394@0" ObjectIDND1="11452@x" ObjectIDZND0="32005@1" Pin0InfoVect0LinkObjId="SW-59745_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21884f0_0" Pin1InfoVect1LinkObjId="SW-59741_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4238,-912 4280,-912 4280,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21805d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4280,-861 4280,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32005@0" ObjectIDZND0="g_24733e0@0" Pin0InfoVect0LinkObjId="g_24733e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59745_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4280,-861 4280,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2310af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4238,-912 4238,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="17394@0" ObjectIDND1="32005@x" ObjectIDZND0="11452@0" Pin0InfoVect0LinkObjId="SW-59741_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_21884f0_0" Pin1InfoVect1LinkObjId="SW-59745_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4238,-912 4238,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_232a020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4238,-1070 4282,-1070 4282,-1053 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="11452@x" ObjectIDND2="18096@1" ObjectIDZND0="g_246bab0@0" Pin0InfoVect0LinkObjId="g_246bab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-59741_0" Pin1InfoVect2LinkObjId="g_2317900_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4238,-1070 4282,-1070 4282,-1053 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2317900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4238,-1070 4238,-1110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_246bab0@0" ObjectIDND1="0@x" ObjectIDND2="11452@x" ObjectIDZND0="18096@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_246bab0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-59741_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4238,-1070 4238,-1110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23da4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4593,-814 4593,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17394@0" ObjectIDZND0="11453@0" Pin0InfoVect0LinkObjId="SW-59743_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21884f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4593,-814 4593,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23c51a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4593,-871 4593,-889 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="11453@1" ObjectIDZND0="g_2251780@0" ObjectIDZND1="g_2323af0@0" ObjectIDZND2="11454@x" Pin0InfoVect0LinkObjId="g_2251780_0" Pin0InfoVect1LinkObjId="g_2323af0_0" Pin0InfoVect2LinkObjId="SW-59744_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59743_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4593,-871 4593,-889 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2251ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4593,-889 4545,-889 4545,-898 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2251780@0" ObjectIDND1="g_2323af0@0" ObjectIDND2="11453@x" ObjectIDZND0="11454@0" Pin0InfoVect0LinkObjId="SW-59744_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2251780_0" Pin1InfoVect1LinkObjId="g_2323af0_0" Pin1InfoVect2LinkObjId="SW-59743_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4593,-889 4545,-889 4545,-898 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2308290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-934 4545,-948 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11454@1" ObjectIDZND0="g_234e100@0" Pin0InfoVect0LinkObjId="g_234e100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59744_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-934 4545,-948 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_222f070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4593,-943 4593,-955 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2323af0@1" ObjectIDZND0="g_23eeed0@0" Pin0InfoVect0LinkObjId="g_23eeed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2323af0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4593,-943 4593,-955 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2419b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4650,-552 4650,-581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="17393@1" ObjectIDZND0="11438@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59820_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4650,-552 4650,-581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20cefe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4650,-672 4650,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="11438@1" ObjectIDZND0="17392@0" Pin0InfoVect0LinkObjId="SW-59817_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2419b40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4650,-672 4650,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20f6350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4964,-360 4928,-360 4928,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="17389@x" ObjectIDND1="0@x" ObjectIDZND0="g_234e3e0@0" Pin0InfoVect0LinkObjId="g_234e3e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-236474_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4964,-360 4928,-360 4928,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e85a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3601,-446 3601,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17395@0" ObjectIDZND0="11460@1" Pin0InfoVect0LinkObjId="SW-236348_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24681a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3601,-446 3601,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e84df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3601,-391 3601,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11460@0" ObjectIDZND0="11459@1" Pin0InfoVect0LinkObjId="SW-236346_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236348_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3601,-391 3601,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_246bf40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3601,-346 3601,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="11459@0" ObjectIDZND0="11461@1" Pin0InfoVect0LinkObjId="SW-236349_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236346_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3601,-346 3601,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21871f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3601,-283 3613,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="11461@x" ObjectIDND1="18097@x" ObjectIDZND0="g_23ba0c0@0" Pin0InfoVect0LinkObjId="g_23ba0c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-236349_0" Pin1InfoVect1LinkObjId="EC-LF_ZC.LD_ZC_461_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3601,-283 3613,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2300f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3601,-295 3601,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="11461@0" ObjectIDZND0="g_23ba0c0@0" ObjectIDZND1="18097@x" Pin0InfoVect0LinkObjId="g_23ba0c0_0" Pin0InfoVect1LinkObjId="EC-LF_ZC.LD_ZC_461_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236349_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3601,-295 3601,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22d3190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3601,-282 3601,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_23ba0c0@0" ObjectIDND1="11461@x" ObjectIDZND0="18097@0" Pin0InfoVect0LinkObjId="EC-LF_ZC.LD_ZC_461_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_23ba0c0_0" Pin1InfoVect1LinkObjId="SW-236349_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3601,-282 3601,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2417ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3794,-446 3794,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17395@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24681a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3794,-446 3794,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20d7a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3794,-391 3794,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3794,-391 3794,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20f00e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3794,-346 3794,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3794,-346 3794,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21459a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3794,-284 3804,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDZND0="g_2320b00@0" Pin0InfoVect0LinkObjId="g_2320b00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3794,-284 3804,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_246c7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3794,-295 3794,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2320b00@0" Pin0InfoVect0LinkObjId="g_2320b00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3794,-295 3794,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_246ab60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3794,-282 3794,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" ObjectIDND0="g_2320b00@0" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2320b00_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3794,-282 3794,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2293bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-446 3992,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17395@0" ObjectIDZND0="11466@1" Pin0InfoVect0LinkObjId="SW-236380_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24681a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-446 3992,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22faf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-392 3992,-374 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11466@0" ObjectIDZND0="11465@1" Pin0InfoVect0LinkObjId="SW-236376_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-392 3992,-374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24badd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-347 3992,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="11465@0" ObjectIDZND0="11467@1" Pin0InfoVect0LinkObjId="SW-236381_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236376_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-347 3992,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_234fe40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-284 4002,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="11467@x" ObjectIDND1="18098@x" ObjectIDZND0="g_24bf920@0" Pin0InfoVect0LinkObjId="g_24bf920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-236381_0" Pin1InfoVect1LinkObjId="EC-LF_ZC.LF_ZJ_463_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-284 4002,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22fd920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-296 3992,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="11467@0" ObjectIDZND0="g_24bf920@0" ObjectIDZND1="18098@x" Pin0InfoVect0LinkObjId="g_24bf920_0" Pin0InfoVect1LinkObjId="EC-LF_ZC.LF_ZJ_463_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236381_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-296 3992,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22fad50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3992,-283 3992,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_24bf920@0" ObjectIDND1="11467@x" ObjectIDZND0="18098@0" Pin0InfoVect0LinkObjId="EC-LF_ZC.LF_ZJ_463_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24bf920_0" Pin1InfoVect1LinkObjId="SW-236381_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3992,-283 3992,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2311180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-446 4186,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17395@0" ObjectIDZND0="11469@1" Pin0InfoVect0LinkObjId="SW-236412_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24681a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-446 4186,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2327600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-389 4186,-371 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11469@0" ObjectIDZND0="11468@1" Pin0InfoVect0LinkObjId="SW-236410_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236412_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-389 4186,-371 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_234dda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-344 4186,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="11468@0" ObjectIDZND0="11470@1" Pin0InfoVect0LinkObjId="SW-236413_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-344 4186,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2189ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-256 4196,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="18099@x" ObjectIDND1="11470@x" ObjectIDZND0="g_230e690@0" Pin0InfoVect0LinkObjId="g_230e690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-LF_ZC.LD_ZC_464_0" Pin1InfoVect1LinkObjId="SW-236413_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-256 4196,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23ce340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4137,-446 4137,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17395@0" ObjectIDZND0="11458@0" Pin0InfoVect0LinkObjId="SW-236472_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24681a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4137,-446 4137,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24115e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4137,-516 4174,-516 4174,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="11458@x" ObjectIDND1="0@x" ObjectIDZND0="g_232adf0@0" Pin0InfoVect0LinkObjId="g_232adf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-236472_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4137,-516 4174,-516 4174,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2195570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3961,-814 3961,-781 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17394@0" ObjectIDZND0="11451@1" Pin0InfoVect0LinkObjId="SW-59742_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21884f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3961,-814 3961,-781 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23d99f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3961,-525 3961,-509 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="11456@0" ObjectIDZND0="11457@1" Pin0InfoVect0LinkObjId="SW-59747_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59746_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3961,-525 3961,-509 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2195eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3961,-446 3961,-473 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17395@0" ObjectIDZND0="11457@0" Pin0InfoVect0LinkObjId="SW-59747_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24681a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3961,-446 3961,-473 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2413f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3961,-729 3961,-745 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="11450@1" ObjectIDZND0="11451@0" Pin0InfoVect0LinkObjId="SW-59742_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59749_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3961,-729 3961,-745 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21ab7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3961,-552 3961,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="11456@1" ObjectIDZND0="11437@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59746_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3961,-552 3961,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20cfa90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3961,-670 3961,-702 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="11437@0" ObjectIDZND0="11450@0" Pin0InfoVect0LinkObjId="SW-59749_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21ab7b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3961,-670 3961,-702 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23e9d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4292,-446 4292,-520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="17395@0" ObjectIDZND0="11474@0" Pin0InfoVect0LinkObjId="SW-236442_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24681a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4292,-446 4292,-520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_23f0150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4179,-999 4179,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4179,-999 4179,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23da130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4292,-547 4292,-570 4394,-570 4394,-516 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="11474@1" ObjectIDZND0="11475@1" Pin0InfoVect0LinkObjId="SW-236444_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236442_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4292,-547 4292,-570 4394,-570 4394,-516 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_231ee70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4394,-480 4394,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11475@0" ObjectIDZND0="17396@0" Pin0InfoVect0LinkObjId="g_217b0c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236444_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4394,-480 4394,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2315590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4650,-725 4650,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="17392@1" ObjectIDZND0="23054@x" ObjectIDZND1="11455@x" Pin0InfoVect0LinkObjId="SW-59818_0" Pin0InfoVect1LinkObjId="SW-59819_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59817_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4650,-725 4650,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23cb8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4650,-737 4650,-745 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="17392@x" ObjectIDND1="11455@x" ObjectIDZND0="23054@0" Pin0InfoVect0LinkObjId="SW-59818_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-59817_0" Pin1InfoVect1LinkObjId="SW-59819_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4650,-737 4650,-745 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24bea90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4650,-737 4708,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="17392@x" ObjectIDND1="23054@x" ObjectIDZND0="11455@0" Pin0InfoVect0LinkObjId="SW-59819_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-59817_0" Pin1InfoVect1LinkObjId="SW-59818_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4650,-737 4708,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23a4350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4744,-737 4764,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11455@1" ObjectIDZND0="g_20f1340@0" Pin0InfoVect0LinkObjId="g_20f1340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59819_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4744,-737 4764,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2425080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4179,-1044 4179,-1053 4238,-1053 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_246bab0@0" ObjectIDZND1="18096@1" ObjectIDZND2="11452@x" Pin0InfoVect0LinkObjId="g_246bab0_0" Pin0InfoVect1LinkObjId="g_2317900_1" Pin0InfoVect2LinkObjId="SW-59741_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4179,-1044 4179,-1053 4238,-1053 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21862d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4238,-1070 4238,-1053 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_246bab0@0" ObjectIDND1="18096@1" ObjectIDZND0="0@x" ObjectIDZND1="11452@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-59741_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_246bab0_0" Pin1InfoVect1LinkObjId="g_2317900_1" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4238,-1070 4238,-1053 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2251520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4238,-1053 4238,-965 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_246bab0@0" ObjectIDND2="18096@1" ObjectIDZND0="11452@1" Pin0InfoVect0LinkObjId="SW-59741_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_246bab0_0" Pin1InfoVect2LinkObjId="g_2317900_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4238,-1053 4238,-965 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2469250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4627,-904 4627,-896 4593,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2251780@0" ObjectIDZND0="11453@x" ObjectIDZND1="11454@x" ObjectIDZND2="g_2323af0@0" Pin0InfoVect0LinkObjId="SW-59743_0" Pin0InfoVect1LinkObjId="SW-59744_0" Pin0InfoVect2LinkObjId="g_2323af0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2251780_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4627,-904 4627,-896 4593,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2313160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4593,-889 4593,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="11453@x" ObjectIDND1="11454@x" ObjectIDZND0="g_2251780@0" ObjectIDZND1="g_2323af0@0" Pin0InfoVect0LinkObjId="g_2251780_0" Pin0InfoVect1LinkObjId="g_2323af0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-59743_0" Pin1InfoVect1LinkObjId="SW-59744_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4593,-889 4593,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2326640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4593,-896 4593,-912 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2251780@0" ObjectIDND1="11453@x" ObjectIDND2="11454@x" ObjectIDZND0="g_2323af0@0" Pin0InfoVect0LinkObjId="g_2323af0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2251780_0" Pin1InfoVect1LinkObjId="SW-59743_0" Pin1InfoVect2LinkObjId="SW-59744_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4593,-896 4593,-912 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21884f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4416,-847 4416,-814 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_23268a0@0" ObjectIDZND0="17394@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23268a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4416,-847 4416,-814 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22aa9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4137,-571 4137,-606 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="g_22aabe0@0" Pin0InfoVect0LinkObjId="g_22aabe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4137,-571 4137,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20d89e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4137,-503 4137,-516 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="11458@1" ObjectIDZND0="g_232adf0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_232adf0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236472_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4137,-503 4137,-516 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20d8bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4137,-516 4137,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="11458@x" ObjectIDND1="g_232adf0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-236472_0" Pin1InfoVect1LinkObjId="g_232adf0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4137,-516 4137,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2467f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3492,-347 3492,-364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3492,-347 3492,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24681a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3492,-416 3492,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="17395@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3492,-416 3492,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20d9590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4964,-373 4964,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="17389@0" ObjectIDZND0="0@x" ObjectIDZND1="g_234e3e0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_234e3e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236474_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4964,-373 4964,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24bb470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4964,-360 4964,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="17389@x" ObjectIDND1="g_234e3e0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-236474_0" Pin1InfoVect1LinkObjId="g_234e3e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4964,-360 4964,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24bb6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4964,-304 4964,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_234e840@0" Pin0InfoVect0LinkObjId="g_234e840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4964,-304 4964,-246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23c5d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-256 4186,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_230e690@0" ObjectIDND1="11470@x" ObjectIDZND0="18099@0" Pin0InfoVect0LinkObjId="EC-LF_ZC.LD_ZC_464_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_230e690_0" Pin1InfoVect1LinkObjId="SW-236413_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-256 4186,-246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23c5f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-293 4186,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="11470@0" ObjectIDZND0="g_230e690@0" ObjectIDZND1="18099@x" Pin0InfoVect0LinkObjId="g_230e690_0" Pin0InfoVect1LinkObjId="EC-LF_ZC.LD_ZC_464_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236413_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-293 4186,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_217b0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4650,-473 4650,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23055@0" ObjectIDZND0="17396@0" Pin0InfoVect0LinkObjId="g_231ee70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-59821_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4650,-473 4650,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23f0a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4963,-409 4963,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="17389@1" ObjectIDZND0="17396@0" Pin0InfoVect0LinkObjId="g_231ee70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236474_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4963,-409 4963,-446 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="LF_ZC"/>
</svg>