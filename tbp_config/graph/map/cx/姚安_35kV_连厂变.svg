<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-214" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-196 -1108 2068 1200">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape189">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="13" y1="21" y2="11"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="13,64 38,64 38,35 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="13" y1="58" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="32" x2="44" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="41" x2="35" y1="31" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="39" x2="37" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="13" y1="21" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="18" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="53" y2="0"/>
    <circle cx="13" cy="66" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="80" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="84" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="88" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="60" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="64" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="68" y2="64"/>
    <ellipse cx="13" cy="82" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
   </symbol>
   <symbol id="lightningRod:shape205">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="16,27 38,5 50,5 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="25" y="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="13" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="2" y2="8"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape25_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
   </symbol>
   <symbol id="switch2:shape25_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape42_0">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,56 6,56 6,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="32" y1="53" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.396825" x1="31" x2="31" y1="73" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="31" x2="35" y1="79" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="31" y1="81" y2="79"/>
   </symbol>
   <symbol id="transformer2:shape42_1">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.396825" x1="31" x2="31" y1="49" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="31" x2="35" y1="55" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="31" y1="57" y2="55"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1.24528"/>
    <polyline points="58,100 64,100 " stroke-width="1.24528"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1.24528"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape64">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="29" y1="17" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="6" y1="69" y2="69"/>
    <ellipse cx="8" cy="59" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="67" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="38" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="8" y1="16" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="17" y1="24" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="37" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="10" y1="17" y2="30"/>
   </symbol>
   <symbol id="voltageTransformer:shape116">
    <ellipse cx="8" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.238574" x1="10" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.245503" x1="9" x2="10" y1="18" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.246311" x1="6" x2="5" y1="18" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="11" x2="8" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="8" x2="8" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="5" x2="8" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="33" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="36" y1="44" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="35" y1="42" y2="42"/>
    <polyline points="20,8 37,8 37,19 " stroke-width="1"/>
    <ellipse cx="19" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <ellipse cx="8" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <ellipse cx="19" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <rect height="13" stroke-width="1" width="8" x="33" y="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="32" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="31" y1="21" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="27" y2="30"/>
   </symbol>
   <symbol id="voltageTransformer:shape133">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="18" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="20" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="21" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="41" y1="44" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="38" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="39" y1="46" y2="46"/>
    <circle cx="7" cy="37" r="7.5" stroke-width="1"/>
    <circle cx="7" cy="25" r="7.5" stroke-width="1"/>
    <rect height="14" stroke-width="1" width="8" x="32" y="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="35" y2="44"/>
    <polyline points="42,23 30,32 30,36 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="35" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="21" y1="29" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="21" y1="35" y2="33"/>
    <circle cx="17" cy="31" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="35" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="35" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="37" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="22" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="22" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="36" y1="11" y2="11"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2071060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_145dae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_145e300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2072c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_20739d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_20745f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2075050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2075930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2071a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2071a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2078160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2078160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2079550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2079550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_207a1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_207bea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_207caf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_207d9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_207e2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_207fa70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2080770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2081030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_20817f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_20828d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2083250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2083d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2084700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2085b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2086720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2087750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2088390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2096b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2089c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_208b270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_208c7a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1210" width="2078" x="-201" y="-1113"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="160" y="-938"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-144549">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 807.000000 -812.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25447" ObjectName="SW-YA_LC.YA_LC_3311SW"/>
     <cge:Meas_Ref ObjectId="144549"/>
    <cge:TPSR_Ref TObjectID="25447"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144679">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 697.000000 -708.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25462" ObjectName="SW-YA_LC.YA_LC_3011SW"/>
     <cge:Meas_Ref ObjectId="144679"/>
    <cge:TPSR_Ref TObjectID="25462"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144819">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 697.000000 -395.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25468" ObjectName="SW-YA_LC.YA_LC_0011SW"/>
     <cge:Meas_Ref ObjectId="144819"/>
    <cge:TPSR_Ref TObjectID="25468"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144654">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1267.000000 -813.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25458" ObjectName="SW-YA_LC.YA_LC_3901SW"/>
     <cge:Meas_Ref ObjectId="144654"/>
    <cge:TPSR_Ref TObjectID="25458"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144608">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1496.000000 -787.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25455" ObjectName="SW-YA_LC.YA_LC_3321SW"/>
     <cge:Meas_Ref ObjectId="144608"/>
    <cge:TPSR_Ref TObjectID="25455"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144609">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1496.000000 -893.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25456" ObjectName="SW-YA_LC.YA_LC_3326SW"/>
     <cge:Meas_Ref ObjectId="144609"/>
    <cge:TPSR_Ref TObjectID="25456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144749">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1390.000000 -708.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25465" ObjectName="SW-YA_LC.YA_LC_3021SW"/>
     <cge:Meas_Ref ObjectId="144749"/>
    <cge:TPSR_Ref TObjectID="25465"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144859">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1390.000000 -395.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25469" ObjectName="SW-YA_LC.YA_LC_0021SW"/>
     <cge:Meas_Ref ObjectId="144859"/>
    <cge:TPSR_Ref TObjectID="25469"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144952">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 920.600000 -91.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25477" ObjectName="SW-YA_LC.YA_LC_0736SW"/>
     <cge:Meas_Ref ObjectId="144952"/>
    <cge:TPSR_Ref TObjectID="25477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144951">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 920.600000 -291.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25476" ObjectName="SW-YA_LC.YA_LC_0731SW"/>
     <cge:Meas_Ref ObjectId="144951"/>
    <cge:TPSR_Ref TObjectID="25476"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144997">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1484.200000 -291.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25479" ObjectName="SW-YA_LC.YA_LC_0751SW"/>
     <cge:Meas_Ref ObjectId="144997"/>
    <cge:TPSR_Ref TObjectID="25479"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144998">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1484.200000 -91.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25480" ObjectName="SW-YA_LC.YA_LC_0756SW"/>
     <cge:Meas_Ref ObjectId="144998"/>
    <cge:TPSR_Ref TObjectID="25480"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144906">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 373.000000 -91.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25474" ObjectName="SW-YA_LC.YA_LC_0716SW"/>
     <cge:Meas_Ref ObjectId="144906"/>
    <cge:TPSR_Ref TObjectID="25474"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144905">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 373.000000 -291.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25473" ObjectName="SW-YA_LC.YA_LC_0711SW"/>
     <cge:Meas_Ref ObjectId="144905"/>
    <cge:TPSR_Ref TObjectID="25473"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 650.800000 -289.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 650.800000 -89.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144551">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 807.000000 -914.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25449" ObjectName="SW-YA_LC.YA_LC_3316SW"/>
     <cge:Meas_Ref ObjectId="144551"/>
    <cge:TPSR_Ref TObjectID="25449"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144656">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1296.000000 -796.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25460" ObjectName="SW-YA_LC.YA_LC_39010SW"/>
     <cge:Meas_Ref ObjectId="144656"/>
    <cge:TPSR_Ref TObjectID="25460"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144550">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 837.000000 -795.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25448" ObjectName="SW-YA_LC.YA_LC_33110SW"/>
     <cge:Meas_Ref ObjectId="144550"/>
    <cge:TPSR_Ref TObjectID="25448"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144552">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 837.000000 -904.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25450" ObjectName="SW-YA_LC.YA_LC_33160SW"/>
     <cge:Meas_Ref ObjectId="144552"/>
    <cge:TPSR_Ref TObjectID="25450"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144553">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 752.000000 -913.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25451" ObjectName="SW-YA_LC.YA_LC_33167SW"/>
     <cge:Meas_Ref ObjectId="144553"/>
    <cge:TPSR_Ref TObjectID="25451"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144607">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1440.000000 -894.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25454" ObjectName="SW-YA_LC.YA_LC_33267SW"/>
     <cge:Meas_Ref ObjectId="144607"/>
    <cge:TPSR_Ref TObjectID="25454"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144655">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1214.000000 -886.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25459" ObjectName="SW-YA_LC.YA_LC_39017SW"/>
     <cge:Meas_Ref ObjectId="144655"/>
    <cge:TPSR_Ref TObjectID="25459"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144606">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1536.000000 -884.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25453" ObjectName="SW-YA_LC.YA_LC_33260SW"/>
     <cge:Meas_Ref ObjectId="144606"/>
    <cge:TPSR_Ref TObjectID="25453"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144610">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1536.000000 -834.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25457" ObjectName="SW-YA_LC.YA_LC_33217SW"/>
     <cge:Meas_Ref ObjectId="144610"/>
    <cge:TPSR_Ref TObjectID="25457"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144750">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1338.000000 -692.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25466" ObjectName="SW-YA_LC.YA_LC_30217SW"/>
     <cge:Meas_Ref ObjectId="144750"/>
    <cge:TPSR_Ref TObjectID="25466"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144901">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1041.000000 -380.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25471" ObjectName="SW-YA_LC.YA_LC_0901SW"/>
     <cge:Meas_Ref ObjectId="144901"/>
    <cge:TPSR_Ref TObjectID="25471"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195577">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1211.600000 -91.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29744" ObjectName="SW-YA_LC.YA_LC_0746SW"/>
     <cge:Meas_Ref ObjectId="195577"/>
    <cge:TPSR_Ref TObjectID="29744"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195576">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1211.600000 -291.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29743" ObjectName="SW-YA_LC.YA_LC_0741SW"/>
     <cge:Meas_Ref ObjectId="195576"/>
    <cge:TPSR_Ref TObjectID="29743"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144680">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 644.000000 -693.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25463" ObjectName="SW-YA_LC.YA_LC_30117SW"/>
     <cge:Meas_Ref ObjectId="144680"/>
    <cge:TPSR_Ref TObjectID="25463"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1043.000000 -440.000000)" xlink:href="#switch2:shape25_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285034">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 509.000000 -788.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44725" ObjectName="SW-YA_LC.YA_LC_3331SW"/>
     <cge:Meas_Ref ObjectId="285034"/>
    <cge:TPSR_Ref TObjectID="44725"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285036">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 509.000000 -894.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44726" ObjectName="SW-YA_LC.YA_LC_3336SW"/>
     <cge:Meas_Ref ObjectId="285036"/>
    <cge:TPSR_Ref TObjectID="44726"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285038">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 453.000000 -895.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44729" ObjectName="SW-YA_LC.YA_LC_33367SW"/>
     <cge:Meas_Ref ObjectId="285038"/>
    <cge:TPSR_Ref TObjectID="44729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285037">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 549.000000 -885.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44728" ObjectName="SW-YA_LC.YA_LC_33360SW"/>
     <cge:Meas_Ref ObjectId="285037"/>
    <cge:TPSR_Ref TObjectID="44728"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285035">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 549.000000 -835.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44727" ObjectName="SW-YA_LC.YA_LC_33317SW"/>
     <cge:Meas_Ref ObjectId="285035"/>
    <cge:TPSR_Ref TObjectID="44727"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-YA_LC.YA_LC_3M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="352,-779 1792,-779 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25443" ObjectName="BS-YA_LC.YA_LC_3M"/>
    <cge:TPSR_Ref TObjectID="25443"/></metadata>
   <polyline fill="none" opacity="0" points="352,-779 1792,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YA_LC.YA_LC_9M">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="276,-363 1872,-363 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25444" ObjectName="BS-YA_LC.YA_LC_9M"/>
    <cge:TPSR_Ref TObjectID="25444"/></metadata>
   <polyline fill="none" opacity="0" points="276,-363 1872,-363 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-YA_LC.073Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 924.600000 -28.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34267" ObjectName="EC-YA_LC.073Ld"/>
    <cge:TPSR_Ref TObjectID="34267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_LC.075Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1488.200000 -28.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34269" ObjectName="EC-YA_LC.075Ld"/>
    <cge:TPSR_Ref TObjectID="34269"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_LC.071Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 377.000000 -28.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34266" ObjectName="EC-YA_LC.071Ld"/>
    <cge:TPSR_Ref TObjectID="34266"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 654.800000 -26.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_LC.074Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1215.600000 -28.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34268" ObjectName="EC-YA_LC.074Ld"/>
    <cge:TPSR_Ref TObjectID="34268"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2d26d30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1349.000000 -795.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2085680" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 890.000000 -794.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2082cf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 890.000000 -903.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1363a00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 755.000000 -883.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_138a000" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1443.000000 -864.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1368ed0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1248.000000 -885.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13e5230" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1592.000000 -883.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1353690" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1592.000000 -833.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12b6890" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1311.000000 -691.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30ff890" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 617.000000 -692.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c8be10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 456.000000 -865.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_206ba50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 605.000000 -884.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_136f300" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 605.000000 -834.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_3ab5140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1505,-779 1505,-792 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25443@0" ObjectIDZND0="25455@0" Pin0InfoVect0LinkObjId="SW-144608_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3baa860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1505,-779 1505,-792 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_136b400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="706,-779 706,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25443@0" ObjectIDZND0="25462@1" Pin0InfoVect0LinkObjId="SW-144679_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3baa860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="706,-779 706,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b60360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1505,-828 1505,-839 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="25455@1" ObjectIDZND0="25452@x" ObjectIDZND1="25457@x" Pin0InfoVect0LinkObjId="SW-144604_0" Pin0InfoVect1LinkObjId="SW-144610_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144608_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1505,-828 1505,-839 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_141d000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1505,-839 1505,-851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25455@x" ObjectIDND1="25457@x" ObjectIDZND0="25452@0" Pin0InfoVect0LinkObjId="SW-144604_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-144608_0" Pin1InfoVect1LinkObjId="SW-144610_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1505,-839 1505,-851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b63370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1505,-878 1505,-889 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="25452@1" ObjectIDZND0="25456@x" ObjectIDZND1="25453@x" Pin0InfoVect0LinkObjId="SW-144609_0" Pin0InfoVect1LinkObjId="SW-144606_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144604_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1505,-878 1505,-889 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e0c820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1505,-889 1505,-898 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="25452@x" ObjectIDND1="25453@x" ObjectIDZND0="25456@0" Pin0InfoVect0LinkObjId="SW-144609_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-144604_0" Pin1InfoVect1LinkObjId="SW-144606_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1505,-889 1505,-898 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_135b290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1465,-967 1505,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_13f1da0@0" ObjectIDZND0="g_13a63a0@0" ObjectIDZND1="25454@x" ObjectIDZND2="25456@x" Pin0InfoVect0LinkObjId="g_13a63a0_0" Pin0InfoVect1LinkObjId="SW-144607_0" Pin0InfoVect2LinkObjId="SW-144609_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13f1da0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1465,-967 1505,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3981a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1505,-967 1505,-1013 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_13f1da0@0" ObjectIDND1="g_13a63a0@0" ObjectIDND2="25454@x" ObjectIDZND0="34318@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13f1da0_0" Pin1InfoVect1LinkObjId="g_13a63a0_0" Pin1InfoVect2LinkObjId="SW-144607_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1505,-967 1505,-1013 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bde660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1530,-956 1505,-956 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="g_13a63a0@0" ObjectIDZND0="g_13f1da0@0" ObjectIDZND1="34318@1" ObjectIDZND2="25454@x" Pin0InfoVect0LinkObjId="g_13f1da0_0" Pin0InfoVect1LinkObjId="g_3981a80_1" Pin0InfoVect2LinkObjId="SW-144607_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13a63a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1530,-956 1505,-956 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3be8360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1505,-956 1505,-967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_13a63a0@0" ObjectIDND1="25454@x" ObjectIDND2="25456@x" ObjectIDZND0="g_13f1da0@0" ObjectIDZND1="34318@1" Pin0InfoVect0LinkObjId="g_13f1da0_0" Pin0InfoVect1LinkObjId="g_3981a80_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13a63a0_0" Pin1InfoVect1LinkObjId="SW-144607_0" Pin1InfoVect2LinkObjId="SW-144609_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1505,-956 1505,-967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b33870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1399,-470 1399,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25470@0" ObjectIDZND0="25469@1" Pin0InfoVect0LinkObjId="SW-144859_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1399,-470 1399,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3958820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1399,-713 1399,-697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="25465@0" ObjectIDZND0="25464@x" ObjectIDZND1="25466@x" Pin0InfoVect0LinkObjId="SW-144747_0" Pin0InfoVect1LinkObjId="SW-144750_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144749_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1399,-713 1399,-697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39989d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1399,-697 1399,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25465@x" ObjectIDND1="25466@x" ObjectIDZND0="25464@1" Pin0InfoVect0LinkObjId="SW-144747_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-144749_0" Pin1InfoVect1LinkObjId="SW-144750_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1399,-697 1399,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3980fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1399,-779 1399,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25443@0" ObjectIDZND0="25465@1" Pin0InfoVect0LinkObjId="SW-144749_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3baa860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1399,-779 1399,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_206cc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1399,-400 1399,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25469@0" ObjectIDZND0="25444@0" Pin0InfoVect0LinkObjId="g_3cd1930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144859_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1399,-400 1399,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_135dc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1399,-648 1399,-611 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="25464@0" ObjectIDZND0="25482@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144747_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1399,-648 1399,-611 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c5c550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1399,-532 1399,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="25482@0" ObjectIDZND0="25470@1" Pin0InfoVect0LinkObjId="SW-144860_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_135dc40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1399,-532 1399,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f15670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="930,-269 930,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25475@1" ObjectIDZND0="25476@0" Pin0InfoVect0LinkObjId="SW-144951_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144949_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="930,-269 930,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13f85a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="930,-134 930,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25477@1" ObjectIDZND0="25475@0" Pin0InfoVect0LinkObjId="SW-144949_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144952_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="930,-134 930,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_336c380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="974,-83 930,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_3b6f300@0" ObjectIDZND0="34267@x" ObjectIDZND1="25477@x" Pin0InfoVect0LinkObjId="EC-YA_LC.073Ld_0" Pin0InfoVect1LinkObjId="SW-144952_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b6f300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="974,-83 930,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c90b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="930,-49 930,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34267@0" ObjectIDZND0="g_3b6f300@0" ObjectIDZND1="25477@x" Pin0InfoVect0LinkObjId="g_3b6f300_0" Pin0InfoVect1LinkObjId="SW-144952_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_LC.073Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="930,-49 930,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ce3f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="930,-83 930,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34267@x" ObjectIDND1="g_3b6f300@0" ObjectIDZND0="25477@0" Pin0InfoVect0LinkObjId="SW-144952_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YA_LC.073Ld_0" Pin1InfoVect1LinkObjId="g_3b6f300_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="930,-83 930,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cd1930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="930,-332 930,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25476@1" ObjectIDZND0="25444@0" Pin0InfoVect0LinkObjId="g_206cc60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144951_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="930,-332 930,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c6d5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1493,-269 1493,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25478@1" ObjectIDZND0="25479@0" Pin0InfoVect0LinkObjId="SW-144997_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144995_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1493,-269 1493,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ca9ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1493,-134 1493,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25480@1" ObjectIDZND0="25478@0" Pin0InfoVect0LinkObjId="SW-144995_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144998_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1493,-134 1493,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35b1240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1537,-83 1493,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_12f8ce0@0" ObjectIDZND0="34269@x" ObjectIDZND1="25480@x" Pin0InfoVect0LinkObjId="EC-YA_LC.075Ld_0" Pin0InfoVect1LinkObjId="SW-144998_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12f8ce0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1537,-83 1493,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_133dd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1493,-49 1493,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34269@0" ObjectIDZND0="g_12f8ce0@0" ObjectIDZND1="25480@x" Pin0InfoVect0LinkObjId="g_12f8ce0_0" Pin0InfoVect1LinkObjId="SW-144998_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_LC.075Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1493,-49 1493,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3caabf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1493,-83 1493,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34269@x" ObjectIDND1="g_12f8ce0@0" ObjectIDZND0="25480@0" Pin0InfoVect0LinkObjId="SW-144998_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YA_LC.075Ld_0" Pin1InfoVect1LinkObjId="g_12f8ce0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1493,-83 1493,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cb7a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1493,-332 1493,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25479@1" ObjectIDZND0="25444@0" Pin0InfoVect0LinkObjId="g_206cc60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144997_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1493,-332 1493,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cb65b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1776,-182 1775,-155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3a69ab0@1" ObjectIDZND0="25484@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a69ab0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1776,-182 1775,-155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3cfd470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1776,-227 1776,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_3a69ab0@0" ObjectIDZND0="25444@0" Pin0InfoVect0LinkObjId="g_206cc60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a69ab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1776,-227 1776,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3346dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="382,-269 382,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25472@1" ObjectIDZND0="25473@0" Pin0InfoVect0LinkObjId="SW-144905_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144903_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="382,-269 382,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20a5700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="382,-134 382,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25474@1" ObjectIDZND0="25472@0" Pin0InfoVect0LinkObjId="SW-144903_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144906_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="382,-134 382,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13d63f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="426,-83 382,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2ea3490@0" ObjectIDZND0="34266@x" ObjectIDZND1="25474@x" Pin0InfoVect0LinkObjId="EC-YA_LC.071Ld_0" Pin0InfoVect1LinkObjId="SW-144906_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ea3490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="426,-83 382,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1333a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="382,-49 382,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34266@0" ObjectIDZND0="g_2ea3490@0" ObjectIDZND1="25474@x" Pin0InfoVect0LinkObjId="g_2ea3490_0" Pin0InfoVect1LinkObjId="SW-144906_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_LC.071Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="382,-49 382,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b60d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="382,-83 382,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34266@x" ObjectIDND1="g_2ea3490@0" ObjectIDZND0="25474@0" Pin0InfoVect0LinkObjId="SW-144906_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YA_LC.071Ld_0" Pin1InfoVect1LinkObjId="g_2ea3490_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="382,-83 382,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ffe8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="382,-332 382,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25473@1" ObjectIDZND0="25444@0" Pin0InfoVect0LinkObjId="g_206cc60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144905_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="382,-332 382,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1390500">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="660,-267 660,-294 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="660,-267 660,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13452d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="660,-132 660,-240 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="660,-132 660,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1491380">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="704,-81 660,-81 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_3c3c7d0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c3c7d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="704,-81 660,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1443870">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="660,-47 660,-81 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_3c3c7d0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_3c3c7d0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="660,-47 660,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b62370">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="660,-81 660,-94 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_3c3c7d0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3c3c7d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="660,-81 660,-94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bfca50">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="660,-330 660,-363 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="25444@0" Pin0InfoVect0LinkObjId="g_206cc60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="660,-330 660,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_396e090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-867 816,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25446@0" ObjectIDZND0="25447@1" Pin0InfoVect0LinkObjId="SW-144549_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144547_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="816,-867 816,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_342fe20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-818 1276,-801 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="25458@0" ObjectIDZND0="25460@x" ObjectIDZND1="25443@0" Pin0InfoVect0LinkObjId="SW-144656_0" Pin0InfoVect1LinkObjId="g_3baa860_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144654_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-818 1276,-801 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b8d0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-801 1301,-801 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="25458@x" ObjectIDND1="25443@0" ObjectIDZND0="25460@0" Pin0InfoVect0LinkObjId="SW-144656_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-144654_0" Pin1InfoVect1LinkObjId="g_3baa860_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-801 1301,-801 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c35cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1337,-801 1353,-801 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="25460@1" ObjectIDZND0="g_2d26d30@0" Pin0InfoVect0LinkObjId="g_2d26d30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144656_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1337,-801 1353,-801 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3baa860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-801 1276,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="25458@x" ObjectIDND1="25460@x" ObjectIDZND0="25443@0" Pin0InfoVect0LinkObjId="g_3cbcef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-144654_0" Pin1InfoVect1LinkObjId="SW-144656_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-801 1276,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_382ec70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="817,-800 842,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="25447@x" ObjectIDND1="25443@0" ObjectIDZND0="25448@0" Pin0InfoVect0LinkObjId="SW-144550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-144549_0" Pin1InfoVect1LinkObjId="g_3baa860_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="817,-800 842,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20f6320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="878,-800 894,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="25448@1" ObjectIDZND0="g_2085680@0" Pin0InfoVect0LinkObjId="g_2085680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144550_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="878,-800 894,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c73cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-817 816,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="25447@0" ObjectIDZND0="25448@x" ObjectIDZND1="25443@0" Pin0InfoVect0LinkObjId="SW-144550_0" Pin0InfoVect1LinkObjId="g_3baa860_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144549_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="816,-817 816,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cbcef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-800 816,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="25448@x" ObjectIDND1="25447@x" ObjectIDZND0="25443@0" Pin0InfoVect0LinkObjId="g_3baa860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-144550_0" Pin1InfoVect1LinkObjId="SW-144549_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="816,-800 816,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c72970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="817,-909 842,-909 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="25446@x" ObjectIDND1="25449@x" ObjectIDZND0="25450@0" Pin0InfoVect0LinkObjId="SW-144552_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-144547_0" Pin1InfoVect1LinkObjId="SW-144551_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="817,-909 842,-909 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1371a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="878,-909 894,-909 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="25450@1" ObjectIDZND0="g_2082cf0@0" Pin0InfoVect0LinkObjId="g_2082cf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144552_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="878,-909 894,-909 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2034840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-894 816,-909 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="25446@1" ObjectIDZND0="25450@x" ObjectIDZND1="25449@x" Pin0InfoVect0LinkObjId="SW-144552_0" Pin0InfoVect1LinkObjId="SW-144551_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144547_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="816,-894 816,-909 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b782c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-909 816,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25450@x" ObjectIDND1="25446@x" ObjectIDZND0="25449@0" Pin0InfoVect0LinkObjId="SW-144551_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-144552_0" Pin1InfoVect1LinkObjId="SW-144547_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="816,-909 816,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b5c5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-971 872,-971 872,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_3cd2d50@0" ObjectIDND1="25449@x" ObjectIDND2="25451@x" ObjectIDZND0="g_3c83d40@0" Pin0InfoVect0LinkObjId="g_3c83d40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3cd2d50_0" Pin1InfoVect1LinkObjId="SW-144551_0" Pin1InfoVect2LinkObjId="SW-144553_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="816,-971 872,-971 872,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b41c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-971 816,-994 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_3c83d40@0" ObjectIDND1="g_3cd2d50@0" ObjectIDND2="25449@x" ObjectIDZND0="37782@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c83d40_0" Pin1InfoVect1LinkObjId="g_3cd2d50_0" Pin1InfoVect2LinkObjId="SW-144551_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="816,-971 816,-994 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_364c8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-971 761,-971 761,-977 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3c83d40@0" ObjectIDND1="25449@x" ObjectIDND2="25451@x" ObjectIDZND0="g_3cd2d50@0" Pin0InfoVect0LinkObjId="g_3cd2d50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3c83d40_0" Pin1InfoVect1LinkObjId="SW-144551_0" Pin1InfoVect2LinkObjId="SW-144553_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="816,-971 761,-971 761,-977 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_147ddb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-955 816,-964 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="25449@1" ObjectIDZND0="g_3c83d40@0" ObjectIDZND1="g_3cd2d50@0" ObjectIDZND2="37782@1" Pin0InfoVect0LinkObjId="g_3c83d40_0" Pin0InfoVect1LinkObjId="g_3cd2d50_0" Pin0InfoVect2LinkObjId="g_3b41c20_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144551_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="816,-955 816,-964 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20d5820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-964 816,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="25449@x" ObjectIDND1="25451@x" ObjectIDZND0="g_3c83d40@0" ObjectIDZND1="g_3cd2d50@0" ObjectIDZND2="37782@1" Pin0InfoVect0LinkObjId="g_3c83d40_0" Pin0InfoVect1LinkObjId="g_3cd2d50_0" Pin0InfoVect2LinkObjId="g_3b41c20_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-144551_0" Pin1InfoVect1LinkObjId="SW-144553_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="816,-964 816,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f9ab70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-964 761,-964 761,-954 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="25449@x" ObjectIDND1="g_3c83d40@0" ObjectIDND2="g_3cd2d50@0" ObjectIDZND0="25451@1" Pin0InfoVect0LinkObjId="SW-144553_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-144551_0" Pin1InfoVect1LinkObjId="g_3c83d40_0" Pin1InfoVect2LinkObjId="g_3cd2d50_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="816,-964 761,-964 761,-954 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b7e920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="761,-918 761,-901 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="25451@0" ObjectIDZND0="g_1363a00@0" Pin0InfoVect0LinkObjId="g_1363a00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144553_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="761,-918 761,-901 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b5110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1449,-899 1449,-882 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="25454@0" ObjectIDZND0="g_138a000@0" Pin0InfoVect0LinkObjId="g_138a000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144607_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1449,-899 1449,-882 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3beed30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1504,-945 1449,-945 1449,-935 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="25456@x" ObjectIDND1="g_13a63a0@0" ObjectIDND2="g_13f1da0@0" ObjectIDZND0="25454@1" Pin0InfoVect0LinkObjId="SW-144607_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-144609_0" Pin1InfoVect1LinkObjId="g_13a63a0_0" Pin1InfoVect2LinkObjId="g_13f1da0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1504,-945 1449,-945 1449,-935 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_348c4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1505,-934 1505,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="25456@1" ObjectIDZND0="25454@x" ObjectIDZND1="g_13a63a0@0" ObjectIDZND2="g_13f1da0@0" Pin0InfoVect0LinkObjId="SW-144607_0" Pin0InfoVect1LinkObjId="g_13a63a0_0" Pin0InfoVect2LinkObjId="g_13f1da0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144609_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1505,-934 1505,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ad010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1505,-945 1505,-956 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="25454@x" ObjectIDND1="25456@x" ObjectIDZND0="g_13a63a0@0" ObjectIDZND1="g_13f1da0@0" ObjectIDZND2="34318@1" Pin0InfoVect0LinkObjId="g_13a63a0_0" Pin0InfoVect1LinkObjId="g_13f1da0_0" Pin0InfoVect2LinkObjId="g_3981a80_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-144607_0" Pin1InfoVect1LinkObjId="SW-144609_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1505,-945 1505,-956 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20f16b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-891 1255,-891 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="25458@x" ObjectIDND1="g_3c8df80@0" ObjectIDND2="g_135a9c0@0" ObjectIDZND0="25459@1" Pin0InfoVect0LinkObjId="SW-144655_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-144654_0" Pin1InfoVect1LinkObjId="g_3c8df80_0" Pin1InfoVect2LinkObjId="g_135a9c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-891 1255,-891 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b1d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1219,-891 1204,-891 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="25459@0" ObjectIDZND0="g_1368ed0@0" Pin0InfoVect0LinkObjId="g_1368ed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144655_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1219,-891 1204,-891 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c9e7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-891 1276,-854 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="25459@x" ObjectIDND1="g_3c8df80@0" ObjectIDND2="g_135a9c0@0" ObjectIDZND0="25458@1" Pin0InfoVect0LinkObjId="SW-144654_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-144655_0" Pin1InfoVect1LinkObjId="g_3c8df80_0" Pin1InfoVect2LinkObjId="g_135a9c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-891 1276,-854 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1444af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1596,-889 1577,-889 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_13e5230@0" ObjectIDZND0="25453@1" Pin0InfoVect0LinkObjId="SW-144606_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13e5230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1596,-889 1577,-889 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13fa030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1541,-889 1505,-889 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="25453@0" ObjectIDZND0="25452@x" ObjectIDZND1="25456@x" Pin0InfoVect0LinkObjId="SW-144604_0" Pin0InfoVect1LinkObjId="SW-144609_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144606_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1541,-889 1505,-889 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_134cc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1596,-839 1577,-839 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1353690@0" ObjectIDZND0="25457@1" Pin0InfoVect0LinkObjId="SW-144610_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1353690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1596,-839 1577,-839 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b72b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1541,-839 1505,-839 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25457@0" ObjectIDZND0="25455@x" ObjectIDZND1="25452@x" Pin0InfoVect0LinkObjId="SW-144608_0" Pin0InfoVect1LinkObjId="SW-144604_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1541,-839 1505,-839 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c62e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1329,-697 1343,-697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_12b6890@0" ObjectIDZND0="25466@0" Pin0InfoVect0LinkObjId="SW-144750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12b6890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1329,-697 1343,-697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c6e980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1379,-697 1399,-697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25466@1" ObjectIDZND0="25465@x" ObjectIDZND1="25464@x" Pin0InfoVect0LinkObjId="SW-144749_0" Pin0InfoVect1LinkObjId="SW-144747_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144750_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1379,-697 1399,-697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c702f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1050,-385 1050,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25471@0" ObjectIDZND0="25444@0" Pin0InfoVect0LinkObjId="g_206cc60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144901_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1050,-385 1050,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c6b360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1221,-269 1221,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29745@1" ObjectIDZND0="29743@0" Pin0InfoVect0LinkObjId="SW-195576_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195624_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1221,-269 1221,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20b3ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1221,-134 1221,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29744@1" ObjectIDZND0="29745@0" Pin0InfoVect0LinkObjId="SW-195624_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195577_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1221,-134 1221,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c4df20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1265,-83 1221,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_205d400@0" ObjectIDZND0="34268@x" ObjectIDZND1="29744@x" Pin0InfoVect0LinkObjId="EC-YA_LC.074Ld_0" Pin0InfoVect1LinkObjId="SW-195577_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_205d400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1265,-83 1221,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bf11e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1221,-49 1221,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34268@0" ObjectIDZND0="g_205d400@0" ObjectIDZND1="29744@x" Pin0InfoVect0LinkObjId="g_205d400_0" Pin0InfoVect1LinkObjId="SW-195577_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_LC.074Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1221,-49 1221,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3befe90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1221,-83 1221,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34268@x" ObjectIDND1="g_205d400@0" ObjectIDZND0="29744@0" Pin0InfoVect0LinkObjId="SW-195577_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YA_LC.074Ld_0" Pin1InfoVect1LinkObjId="g_205d400_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1221,-83 1221,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13dbbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1221,-332 1221,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29743@1" ObjectIDZND0="25444@0" Pin0InfoVect0LinkObjId="g_206cc60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195576_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1221,-332 1221,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20698f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1235,-929 1275,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3c8df80@0" ObjectIDZND0="25459@x" ObjectIDZND1="25458@x" ObjectIDZND2="g_135a9c0@0" Pin0InfoVect0LinkObjId="SW-144655_0" Pin0InfoVect1LinkObjId="SW-144654_0" Pin0InfoVect2LinkObjId="g_135a9c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c8df80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1235,-929 1275,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cb94e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-929 1276,-891 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3c8df80@0" ObjectIDND1="g_135a9c0@0" ObjectIDZND0="25459@x" ObjectIDZND1="25458@x" Pin0InfoVect0LinkObjId="SW-144655_0" Pin0InfoVect1LinkObjId="SW-144654_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3c8df80_0" Pin1InfoVect1LinkObjId="g_135a9c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-929 1276,-891 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c8c830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-1007 1276,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_13e6080@0" ObjectIDZND0="g_135a9c0@0" Pin0InfoVect0LinkObjId="g_135a9c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13e6080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-1007 1276,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cbd600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1276,-945 1276,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_135a9c0@1" ObjectIDZND0="g_3c8df80@0" ObjectIDZND1="25459@x" ObjectIDZND2="25458@x" Pin0InfoVect0LinkObjId="g_3c8df80_0" Pin0InfoVect1LinkObjId="SW-144655_0" Pin0InfoVect2LinkObjId="SW-144654_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_135a9c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1276,-945 1276,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d0ab50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="635,-698 649,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_30ff890@0" ObjectIDZND0="25463@0" Pin0InfoVect0LinkObjId="SW-144680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30ff890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="635,-698 649,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13deaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="685,-698 705,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25463@1" ObjectIDZND0="25462@x" ObjectIDZND1="25461@x" Pin0InfoVect0LinkObjId="SW-144679_0" Pin0InfoVect1LinkObjId="SW-144677_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144680_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="685,-698 705,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1369530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="706,-713 706,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25462@0" ObjectIDZND0="25463@x" ObjectIDZND1="25461@x" Pin0InfoVect0LinkObjId="SW-144680_0" Pin0InfoVect1LinkObjId="SW-144677_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144679_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="706,-713 706,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12abb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="706,-698 706,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25463@x" ObjectIDND1="25462@x" ObjectIDZND0="25461@1" Pin0InfoVect0LinkObjId="SW-144677_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-144680_0" Pin1InfoVect1LinkObjId="SW-144679_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="706,-698 706,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13151d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1009,-431 1049,-431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3cf4420@0" ObjectIDZND0="0@x" ObjectIDZND1="25471@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-144901_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3cf4420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1009,-431 1049,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b61bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1050,-445 1050,-431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="g_3cf4420@0" ObjectIDZND1="25471@x" Pin0InfoVect0LinkObjId="g_3cf4420_0" Pin0InfoVect1LinkObjId="SW-144901_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1050,-445 1050,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13e6b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1050,-431 1050,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3cf4420@0" ObjectIDND1="0@x" ObjectIDZND0="25471@1" Pin0InfoVect0LinkObjId="SW-144901_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3cf4420_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1050,-431 1050,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3251450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1050,-497 1050,-514 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_3cb54b0@0" Pin0InfoVect0LinkObjId="g_3cb54b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1050,-497 1050,-514 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13a1d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="706,-400 706,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25468@0" ObjectIDZND0="25444@0" Pin0InfoVect0LinkObjId="g_206cc60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144819_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="706,-400 706,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20a5290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="706,-470 706,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25467@0" ObjectIDZND0="25468@1" Pin0InfoVect0LinkObjId="SW-144819_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144817_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="706,-470 706,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1311920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="706,-648 706,-613 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="25461@0" ObjectIDZND0="25481@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144677_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="706,-648 706,-613 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_141be60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="706,-532 706,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="25481@0" ObjectIDZND0="25467@1" Pin0InfoVect0LinkObjId="SW-144817_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1311920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="706,-532 706,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_381fc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="518,-779 518,-793 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25443@0" ObjectIDZND0="44725@0" Pin0InfoVect0LinkObjId="SW-285034_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3baa860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="518,-779 518,-793 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c2c3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="518,-829 518,-840 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="44725@1" ObjectIDZND0="44724@x" ObjectIDZND1="44727@x" Pin0InfoVect0LinkObjId="SW-285033_0" Pin0InfoVect1LinkObjId="SW-285035_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285034_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="518,-829 518,-840 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13d10f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="518,-840 518,-852 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="44725@x" ObjectIDND1="44727@x" ObjectIDZND0="44724@0" Pin0InfoVect0LinkObjId="SW-285033_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-285034_0" Pin1InfoVect1LinkObjId="SW-285035_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="518,-840 518,-852 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_208b900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="518,-879 518,-890 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="44724@1" ObjectIDZND0="44726@x" ObjectIDZND1="44728@x" Pin0InfoVect0LinkObjId="SW-285036_0" Pin0InfoVect1LinkObjId="SW-285037_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285033_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="518,-879 518,-890 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c4d590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="518,-890 518,-899 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="44724@x" ObjectIDND1="44728@x" ObjectIDZND0="44726@0" Pin0InfoVect0LinkObjId="SW-285036_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-285033_0" Pin1InfoVect1LinkObjId="SW-285037_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="518,-890 518,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39aa780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="462,-900 462,-883 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="44729@0" ObjectIDZND0="g_3c8be10@0" Pin0InfoVect0LinkObjId="g_3c8be10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285038_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="462,-900 462,-883 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_398d340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="517,-946 462,-946 462,-936 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="44726@x" ObjectIDND1="g_1428590@0" ObjectIDND2="g_3be2150@0" ObjectIDZND0="44729@1" Pin0InfoVect0LinkObjId="SW-285038_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-285036_0" Pin1InfoVect1LinkObjId="g_1428590_0" Pin1InfoVect2LinkObjId="g_3be2150_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="517,-946 462,-946 462,-936 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3834370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="518,-935 518,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="44726@1" ObjectIDZND0="44729@x" ObjectIDZND1="g_1428590@0" ObjectIDZND2="g_3be2150@0" Pin0InfoVect0LinkObjId="SW-285038_0" Pin0InfoVect1LinkObjId="g_1428590_0" Pin0InfoVect2LinkObjId="g_3be2150_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285036_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="518,-935 518,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38202f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="609,-890 590,-890 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_206ba50@0" ObjectIDZND0="44728@1" Pin0InfoVect0LinkObjId="SW-285037_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_206ba50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="609,-890 590,-890 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_397d920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="554,-890 518,-890 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="44728@0" ObjectIDZND0="44724@x" ObjectIDZND1="44726@x" Pin0InfoVect0LinkObjId="SW-285033_0" Pin0InfoVect1LinkObjId="SW-285036_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285037_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="554,-890 518,-890 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b5b5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="609,-840 590,-840 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_136f300@0" ObjectIDZND0="44727@1" Pin0InfoVect0LinkObjId="SW-285035_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_136f300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="609,-840 590,-840 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ced020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="554,-840 518,-840 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="44727@0" ObjectIDZND0="44725@x" ObjectIDZND1="44724@x" Pin0InfoVect0LinkObjId="SW-285034_0" Pin0InfoVect1LinkObjId="SW-285033_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-285035_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="554,-840 518,-840 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cd0cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="518,-1021 518,-1052 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="powerLine" ObjectIDND0="g_2d52b40@1" ObjectIDZND0="47613@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d52b40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="518,-1021 518,-1052 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bd2e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1063,-945 1063,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3cdb600@0" ObjectIDZND0="g_208f490@1" Pin0InfoVect0LinkObjId="g_208f490_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3cdb600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1063,-945 1063,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c01560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1063,-873 1063,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_208f490@0" ObjectIDZND0="25443@0" Pin0InfoVect0LinkObjId="g_3baa860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_208f490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1063,-873 1063,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ddfdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="478,-957 518,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_1428590@0" ObjectIDZND0="44729@x" ObjectIDZND1="44726@x" ObjectIDZND2="g_3be2150@0" Pin0InfoVect0LinkObjId="SW-285038_0" Pin0InfoVect1LinkObjId="SW-285036_0" Pin0InfoVect2LinkObjId="g_3be2150_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1428590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="478,-957 518,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d03b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="518,-957 518,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1428590@0" ObjectIDND1="g_3be2150@0" ObjectIDND2="g_2d52b40@0" ObjectIDZND0="44729@x" ObjectIDZND1="44726@x" Pin0InfoVect0LinkObjId="SW-285038_0" Pin0InfoVect1LinkObjId="SW-285036_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1428590_0" Pin1InfoVect1LinkObjId="g_3be2150_0" Pin1InfoVect2LinkObjId="g_2d52b40_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="518,-957 518,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3de2640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="543,-968 518,-968 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3be2150@0" ObjectIDZND0="g_2d52b40@0" ObjectIDZND1="g_1428590@0" ObjectIDZND2="44729@x" Pin0InfoVect0LinkObjId="g_2d52b40_0" Pin0InfoVect1LinkObjId="g_1428590_0" Pin0InfoVect2LinkObjId="SW-285038_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3be2150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="543,-968 518,-968 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3de0890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="518,-982 518,-968 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2d52b40@0" ObjectIDZND0="g_3be2150@0" ObjectIDZND1="g_1428590@0" ObjectIDZND2="44729@x" Pin0InfoVect0LinkObjId="g_3be2150_0" Pin0InfoVect1LinkObjId="g_1428590_0" Pin0InfoVect2LinkObjId="SW-285038_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d52b40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="518,-982 518,-968 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3de2f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="518,-968 518,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3be2150@0" ObjectIDND1="g_2d52b40@0" ObjectIDZND0="g_1428590@0" ObjectIDZND1="44729@x" ObjectIDZND2="44726@x" Pin0InfoVect0LinkObjId="g_1428590_0" Pin0InfoVect1LinkObjId="SW-285038_0" Pin0InfoVect2LinkObjId="SW-285036_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3be2150_0" Pin1InfoVect1LinkObjId="g_2d52b40_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="518,-968 518,-957 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="25443" cx="705" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25443" cx="816" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25443" cx="1505" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25443" cx="1399" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25444" cx="1399" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25444" cx="930" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25444" cx="1493" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25444" cx="1776" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25444" cx="382" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25444" cx="660" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25444" cx="1050" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25444" cx="1221" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25444" cx="706" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25443" cx="518" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25443" cx="1063" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-144416" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 101.500000 -946.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25430" ObjectName="DYN-YA_LC"/>
     <cge:Meas_Ref ObjectId="144416"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1416c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -16.000000 -1033.500000) translate(0,16)">连厂变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_138d6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1243.000000 -1071.000000) translate(0,12)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13e7f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1455.000000 -1062.000000) translate(0,12)">35kV西大连线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_10faf60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 778.000000 -1048.000000) translate(0,12)">35kV连厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a5a910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 282.000000 -787.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39382a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1801.000000 -787.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6eb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1806.000000 -388.000000) translate(0,12)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b80570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 528.000000 -595.000000) translate(0,12)">SZ20-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b80570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 528.000000 -595.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b80570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 528.000000 -595.000000) translate(0,42)">5000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b80570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 528.000000 -595.000000) translate(0,57)">YNd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b80570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 528.000000 -595.000000) translate(0,72)">Ud=7.25%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3a54fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3a54fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3a54fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3a54fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3a54fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3a54fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3a54fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3a54fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3a54fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3a54fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3a54fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3a54fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3a54fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3a54fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3a54fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3a54fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3a54fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3a54fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b6e750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b6e750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b6e750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b6e750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b6e750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b6e750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b6e750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b6e750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3b6e750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b1ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1219.000000 -595.000000) translate(0,12)">SZ11-2500/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b1ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1219.000000 -595.000000) translate(0,27)">35±2×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b1ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1219.000000 -595.000000) translate(0,42)">2500kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b1ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1219.000000 -595.000000) translate(0,57)">y,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b1ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1219.000000 -595.000000) translate(0,72)">Ud=7.0%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_322f510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 907.600000 -13.400000) translate(0,12)">代家线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20bd600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1185.400000 -13.400000) translate(0,12)">观测站Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39ab2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1463.200000 -13.400000) translate(0,12)">王家坡线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3822440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1744.000000 -53.600000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3822440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1744.000000 -53.600000) translate(0,27)">SH15-M-50/10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20a8c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 352.000000 -13.400000) translate(0,12)">大河口线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3545a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 629.800000 -11.400000) translate(0,12)">预留间隔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b88000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1020.000000 -580.000000) translate(0,12)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c05890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 827.000000 -885.000000) translate(0,12)">331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3256b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 823.000000 -842.000000) translate(0,12)">3311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_136bcd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 766.000000 -942.000000) translate(0,12)">33167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c01d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 821.000000 -945.000000) translate(0,12)">3316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32cc1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 845.000000 -823.000000) translate(0,12)">33110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c31680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 844.000000 -930.000000) translate(0,12)">33160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c30cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1217.000000 -917.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c2a3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1283.000000 -843.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12c4ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1297.000000 -824.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bf67f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1516.000000 -874.000000) translate(0,12)">332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ca2570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1512.000000 -817.000000) translate(0,12)">3321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12f93e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1511.000000 -925.000000) translate(0,12)">3326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13d5e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1456.000000 -924.000000) translate(0,12)">33267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1312280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1538.000000 -911.000000) translate(0,12)">33260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1398e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1542.000000 -862.000000) translate(0,12)">33217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c722c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 721.000000 -668.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c76460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 716.000000 -735.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c62200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 720.000000 -489.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c30070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 712.000000 -425.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13cb830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1415.000000 -669.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c78100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1406.000000 -738.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_141ca10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1341.000000 -723.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c96350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1415.000000 -491.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20ff310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1406.000000 -425.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13e0e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1057.000000 -410.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cd8bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 397.000000 -262.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cee580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 389.000000 -321.000000) translate(0,12)">0711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1428210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 389.000000 -121.000000) translate(0,12)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1405a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 945.000000 -262.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c91be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 937.000000 -321.000000) translate(0,12)">0731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cb2350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 937.000000 -121.000000) translate(0,12)">0736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cfe020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1507.000000 -263.000000) translate(0,12)">075</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2cdfd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1500.000000 -321.000000) translate(0,12)">0751</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d044c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1500.000000 -121.000000) translate(0,12)">0756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d0c640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1793.000000 -118.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12965d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1236.000000 -262.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32ef0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1228.000000 -321.000000) translate(0,12)">0741</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_336aec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1228.000000 -121.000000) translate(0,12)">0746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_21050b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -142.000000 -605.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_20d4970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 168.000000 -1017.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3431950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 168.000000 -1054.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2d3ed70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -179.000000 -418.000000) translate(0,13)">10kV王家坡线出线负荷与主变侧不平衡,</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2d3ed70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -179.000000 -418.000000) translate(0,29)">主站系统×1.7为界面上数值，</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2d3ed70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -179.000000 -418.000000) translate(0,45)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2d3ed70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -179.000000 -418.000000) translate(0,61)">现场有自动化人员时进行核实</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b465d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 642.000000 -722.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2069740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -196.000000 -63.000000) translate(0,17)">姚安巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3caff90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -73.500000) translate(0,17)">18787878958</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3caff90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -73.500000) translate(0,38)">18787878954</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3cf4600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -103.000000) translate(0,17)">3811059</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31d5200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1221.000000 -617.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2083720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 291.000000 -751.000000) translate(0,12)">Ub（kV）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_138e580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 291.000000 -766.000000) translate(0,12)">Ua（kV）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13cc9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 292.000000 -721.000000) translate(0,12)">3U0（V）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1311000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 291.000000 -737.000000) translate(0,12)">Uc（kV）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13de0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 283.000000 -706.000000) translate(0,12)">Uab（kV）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_138db40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 176.500000 -927.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_350aa50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 604.000000 -623.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13cbd30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 529.000000 -875.000000) translate(0,12)">333</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13a5ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 525.000000 -818.000000) translate(0,12)">3331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1344b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 524.000000 -926.000000) translate(0,12)">3336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1316960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 469.000000 -925.000000) translate(0,12)">33367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14923f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -912.000000) translate(0,12)">33360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13a3090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 555.000000 -863.000000) translate(0,12)">33317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ce2a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1040.000000 -1057.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c5e240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 475.000000 -1108.000000) translate(0,12)">35kV连河线</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-144677">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 697.000000 -640.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25461" ObjectName="SW-YA_LC.YA_LC_301BK"/>
     <cge:Meas_Ref ObjectId="144677"/>
    <cge:TPSR_Ref TObjectID="25461"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144817">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 697.000000 -462.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25467" ObjectName="SW-YA_LC.YA_LC_001BK"/>
     <cge:Meas_Ref ObjectId="144817"/>
    <cge:TPSR_Ref TObjectID="25467"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144604">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1496.000000 -843.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25452" ObjectName="SW-YA_LC.YA_LC_332BK"/>
     <cge:Meas_Ref ObjectId="144604"/>
    <cge:TPSR_Ref TObjectID="25452"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144747">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1390.000000 -640.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25464" ObjectName="SW-YA_LC.YA_LC_302BK"/>
     <cge:Meas_Ref ObjectId="144747"/>
    <cge:TPSR_Ref TObjectID="25464"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144860">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1390.000000 -462.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25470" ObjectName="SW-YA_LC.YA_LC_002BK"/>
     <cge:Meas_Ref ObjectId="144860"/>
    <cge:TPSR_Ref TObjectID="25470"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144949">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 920.600000 -234.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25475" ObjectName="SW-YA_LC.YA_LC_073BK"/>
     <cge:Meas_Ref ObjectId="144949"/>
    <cge:TPSR_Ref TObjectID="25475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144995">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1484.200000 -234.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25478" ObjectName="SW-YA_LC.YA_LC_075BK"/>
     <cge:Meas_Ref ObjectId="144995"/>
    <cge:TPSR_Ref TObjectID="25478"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144903">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 373.000000 -234.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25472" ObjectName="SW-YA_LC.YA_LC_071BK"/>
     <cge:Meas_Ref ObjectId="144903"/>
    <cge:TPSR_Ref TObjectID="25472"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 650.800000 -232.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144547">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 807.000000 -859.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25446" ObjectName="SW-YA_LC.YA_LC_331BK"/>
     <cge:Meas_Ref ObjectId="144547"/>
    <cge:TPSR_Ref TObjectID="25446"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195624">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1211.600000 -234.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29745" ObjectName="SW-YA_LC.YA_LC_074BK"/>
     <cge:Meas_Ref ObjectId="195624"/>
    <cge:TPSR_Ref TObjectID="29745"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-285033">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 509.000000 -844.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44724" ObjectName="SW-YA_LC.YA_LC_333BK"/>
     <cge:Meas_Ref ObjectId="285033"/>
    <cge:TPSR_Ref TObjectID="44724"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="YA_LC" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_xidalianTlc" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1505,-1042 1505,-1013 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34318" ObjectName="AC-35kV.LN_xidalianTlc"/>
    <cge:TPSR_Ref TObjectID="34318_SS-214"/></metadata>
   <polyline fill="none" opacity="0" points="1505,-1042 1505,-1013 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="YA_LC" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_lianchangTlc" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="816,-1027 816,-994 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37782" ObjectName="AC-35kV.LN_lianchangTlc"/>
    <cge:TPSR_Ref TObjectID="37782_SS-214"/></metadata>
   <polyline fill="none" opacity="0" points="816,-1027 816,-994 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="YA_LC" endPointId="0" endStationName="YA_DHK" flowDrawDirect="1" flowShape="0" id="AC-35kV.lianhe_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="518,-1049 518,-1088 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="47613" ObjectName="AC-35kV.lianhe_line"/>
    <cge:TPSR_Ref TObjectID="47613_SS-214"/></metadata>
   <polyline fill="none" opacity="0" points="518,-1049 518,-1088 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_13f1da0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1458.000000 -963.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b6f300">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 966.600000 -29.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12f8ce0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1530.200000 -29.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a69ab0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1781.000000 -232.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ea3490">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 419.000000 -29.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c3c7d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 696.800000 -27.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3cd2d50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 754.000000 -971.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_205d400">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1257.600000 -29.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c8df80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1228.000000 -925.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_135a9c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1267.000000 -940.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3cf4420">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1002.000000 -427.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1428590">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 471.000000 -953.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d52b40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 508.000000 -977.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3cdb600">
    <use class="BV-35KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 1076.000000 -1040.000000)" xlink:href="#lightningRod:shape189"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_208f490">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1058.000000 -868.000000)" xlink:href="#lightningRod:shape205"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-144490" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 375.000000 45.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144490" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25472"/>
     <cge:Term_Ref ObjectID="35889"/>
    <cge:TPSR_Ref TObjectID="25472"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-144491" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 375.000000 45.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144491" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25472"/>
     <cge:Term_Ref ObjectID="35889"/>
    <cge:TPSR_Ref TObjectID="25472"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-144487" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 375.000000 45.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144487" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25472"/>
     <cge:Term_Ref ObjectID="35889"/>
    <cge:TPSR_Ref TObjectID="25472"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-144440" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 960.000000 -888.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144440" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25446"/>
     <cge:Term_Ref ObjectID="35837"/>
    <cge:TPSR_Ref TObjectID="25446"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-144441" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 960.000000 -888.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144441" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25446"/>
     <cge:Term_Ref ObjectID="35837"/>
    <cge:TPSR_Ref TObjectID="25446"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-144437" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 960.000000 -888.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144437" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25446"/>
     <cge:Term_Ref ObjectID="35837"/>
    <cge:TPSR_Ref TObjectID="25446"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-144434" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1700.000000 -886.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144434" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25452"/>
     <cge:Term_Ref ObjectID="35849"/>
    <cge:TPSR_Ref TObjectID="25452"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-144435" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1700.000000 -886.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144435" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25452"/>
     <cge:Term_Ref ObjectID="35849"/>
    <cge:TPSR_Ref TObjectID="25452"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-144431" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1700.000000 -886.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144431" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25452"/>
     <cge:Term_Ref ObjectID="35849"/>
    <cge:TPSR_Ref TObjectID="25452"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-144502" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1484.000000 46.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144502" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25478"/>
     <cge:Term_Ref ObjectID="35901"/>
    <cge:TPSR_Ref TObjectID="25478"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-144503" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1484.000000 46.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144503" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25478"/>
     <cge:Term_Ref ObjectID="35901"/>
    <cge:TPSR_Ref TObjectID="25478"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-144499" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1484.000000 46.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144499" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25478"/>
     <cge:Term_Ref ObjectID="35901"/>
    <cge:TPSR_Ref TObjectID="25478"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-144443" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 370.000000 -768.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144443" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25443"/>
     <cge:Term_Ref ObjectID="35833"/>
    <cge:TPSR_Ref TObjectID="25443"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-144444" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 370.000000 -768.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144444" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25443"/>
     <cge:Term_Ref ObjectID="35833"/>
    <cge:TPSR_Ref TObjectID="25443"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-144445" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 370.000000 -768.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144445" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25443"/>
     <cge:Term_Ref ObjectID="35833"/>
    <cge:TPSR_Ref TObjectID="25443"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-144449" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 370.000000 -768.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144449" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25443"/>
     <cge:Term_Ref ObjectID="35833"/>
    <cge:TPSR_Ref TObjectID="25443"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-144446" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 370.000000 -768.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144446" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25443"/>
     <cge:Term_Ref ObjectID="35833"/>
    <cge:TPSR_Ref TObjectID="25443"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-144472" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 895.000000 -590.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144472" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25481"/>
     <cge:Term_Ref ObjectID="35910"/>
    <cge:TPSR_Ref TObjectID="25481"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-144471" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 895.000000 -590.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144471" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25481"/>
     <cge:Term_Ref ObjectID="35910"/>
    <cge:TPSR_Ref TObjectID="25481"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-144486" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1589.000000 -592.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144486" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25482"/>
     <cge:Term_Ref ObjectID="35914"/>
    <cge:TPSR_Ref TObjectID="25482"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-144485" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1589.000000 -592.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144485" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25482"/>
     <cge:Term_Ref ObjectID="35914"/>
    <cge:TPSR_Ref TObjectID="25482"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-144462" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 831.000000 -688.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144462" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25461"/>
     <cge:Term_Ref ObjectID="35867"/>
    <cge:TPSR_Ref TObjectID="25461"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-144463" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 831.000000 -688.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144463" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25461"/>
     <cge:Term_Ref ObjectID="35867"/>
    <cge:TPSR_Ref TObjectID="25461"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-144459" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 831.000000 -688.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144459" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25461"/>
     <cge:Term_Ref ObjectID="35867"/>
    <cge:TPSR_Ref TObjectID="25461"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-144468" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 831.000000 -503.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144468" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25467"/>
     <cge:Term_Ref ObjectID="35879"/>
    <cge:TPSR_Ref TObjectID="25467"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-144469" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 831.000000 -503.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144469" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25467"/>
     <cge:Term_Ref ObjectID="35879"/>
    <cge:TPSR_Ref TObjectID="25467"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-144465" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 831.000000 -503.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144465" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25467"/>
     <cge:Term_Ref ObjectID="35879"/>
    <cge:TPSR_Ref TObjectID="25467"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-144476" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1522.000000 -681.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144476" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25464"/>
     <cge:Term_Ref ObjectID="35873"/>
    <cge:TPSR_Ref TObjectID="25464"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-144477" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1522.000000 -681.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144477" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25464"/>
     <cge:Term_Ref ObjectID="35873"/>
    <cge:TPSR_Ref TObjectID="25464"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-144473" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1522.000000 -681.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144473" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25464"/>
     <cge:Term_Ref ObjectID="35873"/>
    <cge:TPSR_Ref TObjectID="25464"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-144482" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1525.000000 -497.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144482" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25470"/>
     <cge:Term_Ref ObjectID="35885"/>
    <cge:TPSR_Ref TObjectID="25470"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-144483" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1525.000000 -497.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144483" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25470"/>
     <cge:Term_Ref ObjectID="35885"/>
    <cge:TPSR_Ref TObjectID="25470"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-144479" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1525.000000 -497.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144479" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25470"/>
     <cge:Term_Ref ObjectID="35885"/>
    <cge:TPSR_Ref TObjectID="25470"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-144496" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 922.000000 45.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144496" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25475"/>
     <cge:Term_Ref ObjectID="35895"/>
    <cge:TPSR_Ref TObjectID="25475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-144498" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 922.000000 45.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144498" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25475"/>
     <cge:Term_Ref ObjectID="35895"/>
    <cge:TPSR_Ref TObjectID="25475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-144493" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 922.000000 45.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144493" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25475"/>
     <cge:Term_Ref ObjectID="35895"/>
    <cge:TPSR_Ref TObjectID="25475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-195572" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1214.000000 46.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195572" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29745"/>
     <cge:Term_Ref ObjectID="42413"/>
    <cge:TPSR_Ref TObjectID="29745"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-195573" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1214.000000 46.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195573" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29745"/>
     <cge:Term_Ref ObjectID="42413"/>
    <cge:TPSR_Ref TObjectID="29745"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-195568" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1214.000000 46.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195568" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29745"/>
     <cge:Term_Ref ObjectID="42413"/>
    <cge:TPSR_Ref TObjectID="29745"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-144451" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 364.000000 -483.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144451" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25444"/>
     <cge:Term_Ref ObjectID="35834"/>
    <cge:TPSR_Ref TObjectID="25444"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-144452" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 364.000000 -483.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144452" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25444"/>
     <cge:Term_Ref ObjectID="35834"/>
    <cge:TPSR_Ref TObjectID="25444"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-144453" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 364.000000 -483.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144453" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25444"/>
     <cge:Term_Ref ObjectID="35834"/>
    <cge:TPSR_Ref TObjectID="25444"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-144457" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 364.000000 -483.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144457" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25444"/>
     <cge:Term_Ref ObjectID="35834"/>
    <cge:TPSR_Ref TObjectID="25444"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-144454" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 364.000000 -483.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144454" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25444"/>
     <cge:Term_Ref ObjectID="35834"/>
    <cge:TPSR_Ref TObjectID="25444"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-144455" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 364.000000 -483.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144455" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25444"/>
     <cge:Term_Ref ObjectID="35834"/>
    <cge:TPSR_Ref TObjectID="25444"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-144456" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 364.000000 -483.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="144456" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25444"/>
     <cge:Term_Ref ObjectID="35834"/>
    <cge:TPSR_Ref TObjectID="25444"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-285029" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 709.000000 -900.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="285029" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44724"/>
     <cge:Term_Ref ObjectID="22262"/>
    <cge:TPSR_Ref TObjectID="44724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-285030" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 709.000000 -900.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="285030" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44724"/>
     <cge:Term_Ref ObjectID="22262"/>
    <cge:TPSR_Ref TObjectID="44724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-285026" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 709.000000 -900.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="285026" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="44724"/>
     <cge:Term_Ref ObjectID="22262"/>
    <cge:TPSR_Ref TObjectID="44724"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_配调_配网接线图35_姚安.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="157" y="-1024"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-54" y="-1044"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-102" y="-1064"/></g>
   <g href="35kV连厂变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="60" x="-142" y="-605"/></g>
   <g href="35kV连厂变35kV连厂线331间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="827" y="-885"/></g>
   <g href="35kV连厂变35kV西大连线332间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1516" y="-874"/></g>
   <g href="35kV连厂变10kV大河口线071间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="397" y="-262"/></g>
   <g href="35kV连厂变10kV代家线073间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="945" y="-262"/></g>
   <g href="35kV连厂变10kV观测站Ⅱ回线074间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1236" y="-262"/></g>
   <g href="35kV连厂变10kV王家坡线075间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1507" y="-263"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="157" y="-1063"/></g>
   <g href="35kV连厂变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="1221" y="-617"/></g>
   <g href="AVC连厂站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="159" y="-939"/></g>
   <g href="35kV连厂变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="604" y="-623"/></g>
   <g href="35kV连厂变35kV连河线333断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="529" y="-875"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b6f930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 308.000000 -45.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32ef280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 297.000000 -60.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_252ef10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 322.000000 -75.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133eb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 887.000000 889.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_207ee50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 876.000000 874.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12f72f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 901.000000 859.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34330b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 801.000000 575.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3346c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 801.000000 590.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ffec00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1494.000000 576.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_336b390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1494.000000 591.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d1c020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 772.000000 689.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12c1960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 761.000000 674.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13f8a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 786.000000 659.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1454290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 773.000000 503.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19835f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 762.000000 488.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1983450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 787.000000 473.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20ad800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1643.000000 888.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21b0ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1632.000000 873.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b7cdd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1657.000000 858.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20e4ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1467.000000 683.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b5e710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1456.000000 668.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c09880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1481.000000 653.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_133fa60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1463.000000 498.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1312f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1452.000000 483.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20f8df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1477.000000 468.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c4c2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 863.000000 -45.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d71bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 852.000000 -60.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3be8e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 877.000000 -75.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c53f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1154.000000 -47.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_141b5e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1143.000000 -62.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34c2540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1168.000000 -77.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c51170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1430.000000 -44.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ca63b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1419.000000 -59.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_207fe50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1444.000000 -74.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a54430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 285.000000 471.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13a1bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 285.000000 485.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_387eaf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 284.000000 441.000000) translate(0,12)">3U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3902ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 285.000000 456.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b854c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 277.000000 426.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c0ba80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 277.000000 410.000000) translate(0,12)">Ubc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f9a6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 277.000000 395.000000) translate(0,12)">Uca（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1443ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 656.000000 900.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14231a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 645.000000 885.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14917a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 670.000000 870.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_202dba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 568.000000 1100.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="575" cy="1093" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-YA_LC.YA_LC_Zyb2">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="35921"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1744.000000 -62.000000)" xlink:href="#transformer2:shape42_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1744.000000 -62.000000)" xlink:href="#transformer2:shape42_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25484" ObjectName="TF-YA_LC.YA_LC_Zyb2"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YA_LC.YA_LC_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="35913"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.803030 -0.000000 0.000000 -0.882353 1368.000000 -528.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.803030 -0.000000 0.000000 -0.882353 1368.000000 -528.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25482" ObjectName="TF-YA_LC.YA_LC_2T"/>
    <cge:TPSR_Ref TObjectID="25482"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YA_LC.YA_LC_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="35909"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.803030 -0.000000 0.000000 -0.882353 675.000000 -528.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.803030 -0.000000 0.000000 -0.882353 675.000000 -528.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25481" ObjectName="TF-YA_LC.YA_LC_1T"/>
    <cge:TPSR_Ref TObjectID="25481"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="157" y="-1024"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="157" y="-1024"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-54" y="-1044"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-54" y="-1044"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-102" y="-1064"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-102" y="-1064"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="60" x="-142" y="-605"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="60" x="-142" y="-605"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="827" y="-885"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="827" y="-885"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1516" y="-874"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1516" y="-874"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="397" y="-262"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="397" y="-262"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="945" y="-262"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="945" y="-262"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1236" y="-262"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1236" y="-262"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1507" y="-263"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1507" y="-263"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="157" y="-1063"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="157" y="-1063"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="1221" y="-617"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="1221" y="-617"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="159" y="-939"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="159" y="-939"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="604" y="-623"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="604" y="-623"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="529" y="-875"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="529" y="-875"/></g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_13a63a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1522.000000 -951.000000)" xlink:href="#voltageTransformer:shape64"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c83d40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 864.000000 -973.000000)" xlink:href="#voltageTransformer:shape64"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13e6080">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1268.000000 -1005.000000)" xlink:href="#voltageTransformer:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3cb54b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1042.000000 -509.000000)" xlink:href="#voltageTransformer:shape133"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3be2150">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 535.000000 -963.000000)" xlink:href="#voltageTransformer:shape64"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -95.000000 -980.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217890" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -815.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217890" ObjectName="YA_LC:YA_LC_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-219739" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -44.000000 -774.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219739" ObjectName="YA_LC:YA_LC_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217890" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -46.000000 -895.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217890" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217890" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -45.000000 -853.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217890" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="YA_LC"/>
</svg>