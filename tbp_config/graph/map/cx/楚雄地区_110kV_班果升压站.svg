<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-258" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="430 -937 1977 1223">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="99" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape6">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="1" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="2" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="5" y1="6" y2="26"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape188">
    <polyline DF8003:Layer="PUBLIC" points="19,18 10,0 1,19 19,18 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape177">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <text font-family="SimSun" font-size="15" graphid="g_2ad88e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
    <polyline points="17,19 17,30 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape11">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.11074" x1="14" x2="21" y1="19" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.11075" x1="21" x2="21" y1="25" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.11074" x1="29" x2="22" y1="19" y2="25"/>
    <ellipse cx="21" cy="24" rx="20" ry="19.5" stroke-width="2.11074"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.00001" x1="28" x2="21" y1="62" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="28" x2="31" y1="61" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="14" y1="59" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.00001" x1="13" x2="20" y1="62" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="21" x2="21" y1="47" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="28" x2="20" y1="42" y2="48"/>
    <ellipse cx="21" cy="55" rx="20" ry="19.5" stroke-width="2.00007"/>
   </symbol>
   <symbol id="lightningRod:shape174">
    <rect height="18" stroke-width="1.1697" width="11" x="1" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="14" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="39" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.447552" x1="7" x2="7" y1="7" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape192">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="26" y1="9" y2="9"/>
    <polyline DF8003:Layer="PUBLIC" points="5,19 17,9 5,0 5,19 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="38,1 26,10 38,19 38,1 " stroke-width="1"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape13_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape14_0">
    <circle cx="37" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="84" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="70" x2="68" y1="84" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="45" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="28" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="28" x2="45" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape14_1">
    <ellipse cx="37" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="37" y1="75" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="67" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="37" y1="59" y2="67"/>
   </symbol>
   <symbol id="voltageTransformer:shape71">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="53" y1="12" y2="12"/>
    <ellipse cx="39" cy="19" fillStyle="0" rx="15" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.36031" x1="30" x2="34" y1="55" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.39762" x1="25" x2="21" y1="55" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.77579" x1="21" x2="35" y1="62" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="33" x2="41" y1="16" y2="20"/>
    <ellipse cx="16" cy="20" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="40" x2="41" y1="20" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="47" x2="41" y1="16" y2="20"/>
    <ellipse cx="28" cy="56" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="53" y1="32" y2="32"/>
    <ellipse cx="40" cy="39" fillStyle="0" rx="15" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="32" y2="32"/>
    <ellipse cx="15" cy="40" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="15" x2="16" y1="20" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="22" x2="16" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="16" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="33" x2="41" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="40" x2="41" y1="40" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="47" x2="41" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="15" x2="16" y1="40" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="21" x2="15" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="16" y1="36" y2="40"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_29a4850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29a54a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29a5d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29a6510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29a7000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29a7b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29a8330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_29a8d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2150a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2150a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29ab7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29ab7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29ad590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29ad590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_29ae5a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29aff10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_29b0b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_29b1a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29b2320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29b32b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29b3c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29b4540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29b4d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29b5de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29b6760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29b7250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_29b7c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_29b9300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_29b9df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_29bae40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29bba90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29c9e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29c1ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_29c31c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_29bd6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1233" width="1987" x="425" y="-942"/>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="1987" cy="-814" fill="none" fillStyle="0" r="15" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1988" cy="-840" fill="none" fillStyle="0" r="15" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="2204" cy="-818" fill="none" fillStyle="0" r="15" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="2204" cy="-840" fill="none" fillStyle="0" r="15" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a94460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1524.000000 346.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a95520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1513.000000 331.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a96330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1538.000000 316.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aacd80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1518.000000 686.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aad260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1507.000000 671.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aad4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1532.000000 656.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b312b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1015.000000 -167.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b31510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1004.000000 -182.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b31750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1029.000000 -197.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b31a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1290.000000 -172.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b31ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1279.000000 -187.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b31f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1304.000000 -202.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b32250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1563.000000 -164.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b324b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1552.000000 -179.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b326f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1577.000000 -194.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b35fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1811.000000 -213.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b361e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1836.000000 -228.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ff4f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2142.000000 -256.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ff5770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2167.000000 -271.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 -0.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b21740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 778.000000 335.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b21980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 777.000000 303.666667) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b21ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 777.000000 319.333333) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b205c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 783.000000 269.666667) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b21190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 768.000000 287.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1492,-799 1497,-792 1494,-793 " stroke="rgb(255,0,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1497,-792 1497,-795 " stroke="rgb(255,0,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2213,125 2246,125 2246,153 2213,167 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1988,-832 1985,-832 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1992,-841 1994,-839 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1987,-816 2010,-816 2010,-795 1988,-777 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1988,-518 1967,-494 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2015,-518 2035,-492 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1988,-699 1935,-653 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2177,-518 2156,-494 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2204,-518 2224,-492 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2204,-699 2255,-654 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2319,-596 2370,-551 " stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="26" stroke="rgb(170,85,127)" stroke-width="1" width="12" x="1408" y="-477"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="11" stroke="rgb(60,120,255)" stroke-width="1" width="6" x="1047" y="-454"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(60,120,255)" stroke-width="1" width="24" x="2010" y="-577"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(60,120,255)" stroke-width="1" width="24" x="2049" y="-576"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="20" stroke="rgb(60,120,255)" stroke-width="1" width="13" x="1981" y="-495"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="20" stroke="rgb(60,120,255)" stroke-width="1" width="13" x="2008" y="-495"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="20" stroke="rgb(60,120,255)" stroke-width="1" width="13" x="2170" y="-495"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="20" stroke="rgb(60,120,255)" stroke-width="1" width="13" x="2197" y="-495"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(60,120,255)" stroke-width="1" width="24" x="2158" y="-562"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(60,120,255)" stroke-width="1" width="24" x="2119" y="-561"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-196391">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1348.000000 -460.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29941" ObjectName="SW-CX_BG.CX_BG_1010SW"/>
     <cge:Meas_Ref ObjectId="196391"/>
    <cge:TPSR_Ref TObjectID="29941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196386">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1414.000000 -555.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29936" ObjectName="SW-CX_BG.CX_BG_15117SW"/>
     <cge:Meas_Ref ObjectId="196386"/>
    <cge:TPSR_Ref TObjectID="29936"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196387">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1414.000000 -698.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29937" ObjectName="SW-CX_BG.CX_BG_15160SW"/>
     <cge:Meas_Ref ObjectId="196387"/>
    <cge:TPSR_Ref TObjectID="29937"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196385">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1459.000000 -726.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29935" ObjectName="SW-CX_BG.CX_BG_1516SW"/>
     <cge:Meas_Ref ObjectId="196385"/>
    <cge:TPSR_Ref TObjectID="29935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196388">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1477.000000 -782.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29938" ObjectName="SW-CX_BG.CX_BG_1517SW"/>
     <cge:Meas_Ref ObjectId="196388"/>
    <cge:TPSR_Ref TObjectID="29938"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196384">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1459.000000 -575.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29934" ObjectName="SW-CX_BG.CX_BG_1511SW"/>
     <cge:Meas_Ref ObjectId="196384"/>
    <cge:TPSR_Ref TObjectID="29934"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196389">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.750000 -0.000000 0.000000 -0.928571 1418.000000 -815.714286)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29939" ObjectName="SW-CX_BG.CX_BG_1901SW"/>
     <cge:Meas_Ref ObjectId="196389"/>
    <cge:TPSR_Ref TObjectID="29939"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196390">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.750000 -0.000000 0.000000 -0.928571 1391.000000 -773.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29940" ObjectName="SW-CX_BG.CX_BG_19017SW"/>
     <cge:Meas_Ref ObjectId="196390"/>
    <cge:TPSR_Ref TObjectID="29940"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196409">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.950000 -0.000000 0.000000 -0.721519 1073.850000 -289.936709)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29965" ObjectName="SW-CX_BG.CX_BG_3901SW"/>
     <cge:Meas_Ref ObjectId="196409"/>
    <cge:TPSR_Ref TObjectID="29965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196402">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.500000 -0.000000 0.000000 -1.103988 1072.000000 -26.955479)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29956" ObjectName="SW-CX_BG.CX_BG_35367SW"/>
     <cge:Meas_Ref ObjectId="196402"/>
    <cge:TPSR_Ref TObjectID="29956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196393">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1456.000000 -354.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29944" ObjectName="SW-CX_BG.CX_BG_301XC1"/>
     <cge:Meas_Ref ObjectId="196393"/>
    <cge:TPSR_Ref TObjectID="29944"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196393">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1456.000000 -275.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29943" ObjectName="SW-CX_BG.CX_BG_301XC"/>
     <cge:Meas_Ref ObjectId="196393"/>
    <cge:TPSR_Ref TObjectID="29943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196401">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1114.000000 -189.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29954" ObjectName="SW-CX_BG.CX_BG_353XC"/>
     <cge:Meas_Ref ObjectId="196401"/>
    <cge:TPSR_Ref TObjectID="29954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196398">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1377.000000 -190.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29950" ObjectName="SW-CX_BG.CX_BG_352XC"/>
     <cge:Meas_Ref ObjectId="196398"/>
    <cge:TPSR_Ref TObjectID="29950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196395">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1647.000000 -186.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29946" ObjectName="SW-CX_BG.CX_BG_351XC"/>
     <cge:Meas_Ref ObjectId="196395"/>
    <cge:TPSR_Ref TObjectID="29946"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196404">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1896.000000 -190.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29958" ObjectName="SW-CX_BG.CX_BG_354XC"/>
     <cge:Meas_Ref ObjectId="196404"/>
    <cge:TPSR_Ref TObjectID="29958"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196401">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1114.000000 -118.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29955" ObjectName="SW-CX_BG.CX_BG_353XC1"/>
     <cge:Meas_Ref ObjectId="196401"/>
    <cge:TPSR_Ref TObjectID="29955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196398">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1377.000000 -118.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29951" ObjectName="SW-CX_BG.CX_BG_352XC1"/>
     <cge:Meas_Ref ObjectId="196398"/>
    <cge:TPSR_Ref TObjectID="29951"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196395">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1647.000000 -117.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29947" ObjectName="SW-CX_BG.CX_BG_351XC1"/>
     <cge:Meas_Ref ObjectId="196395"/>
    <cge:TPSR_Ref TObjectID="29947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196404">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1896.000000 -117.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29959" ObjectName="SW-CX_BG.CX_BG_354XC1"/>
     <cge:Meas_Ref ObjectId="196404"/>
    <cge:TPSR_Ref TObjectID="29959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196399">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.500000 -0.000000 0.000000 -1.103988 1334.000000 -27.955479)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29952" ObjectName="SW-CX_BG.CX_BG_35267SW"/>
     <cge:Meas_Ref ObjectId="196399"/>
    <cge:TPSR_Ref TObjectID="29952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196396">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.500000 -0.000000 0.000000 -1.103988 1601.000000 -29.955479)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29948" ObjectName="SW-CX_BG.CX_BG_35167SW"/>
     <cge:Meas_Ref ObjectId="196396"/>
    <cge:TPSR_Ref TObjectID="29948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196405">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.500000 -0.000000 0.000000 -1.103988 1854.000000 -36.955479)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29960" ObjectName="SW-CX_BG.CX_BG_35467SW"/>
     <cge:Meas_Ref ObjectId="196405"/>
    <cge:TPSR_Ref TObjectID="29960"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196407">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2203.000000 -187.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29962" ObjectName="SW-CX_BG.CX_BG_355XC"/>
     <cge:Meas_Ref ObjectId="196407"/>
    <cge:TPSR_Ref TObjectID="29962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196407">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2203.000000 -99.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29963" ObjectName="SW-CX_BG.CX_BG_355XC1"/>
     <cge:Meas_Ref ObjectId="196407"/>
    <cge:TPSR_Ref TObjectID="29963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196408">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.500000 -0.000000 0.000000 -1.103988 2165.000000 -26.955479)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29964" ObjectName="SW-CX_BG.CX_BG_35567SW"/>
     <cge:Meas_Ref ObjectId="196408"/>
    <cge:TPSR_Ref TObjectID="29964"/></metadata>
   </g>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_BG.P1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1652.000000 90.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43258" ObjectName="SM-CX_BG.P1"/>
    <cge:TPSR_Ref TObjectID="43258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_BG.P2">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1382.000000 92.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43259" ObjectName="SM-CX_BG.P2"/>
    <cge:TPSR_Ref TObjectID="43259"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_BG.P3">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1119.000000 94.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43260" ObjectName="SM-CX_BG.P3"/>
    <cge:TPSR_Ref TObjectID="43260"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_HGY" endPointId="0" endStationName="CX_BG" flowDrawDirect="1" flowShape="0" id="AC-110kV.banhuang_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1468,-848 1468,-880 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="31891" ObjectName="AC-110kV.banhuang_line"/>
    <cge:TPSR_Ref TObjectID="31891_SS-258"/></metadata>
   <polyline fill="none" opacity="0" points="1468,-848 1468,-880 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2aad6e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1531.000000 -781.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2aadec0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1380.000000 -697.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2aae890" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1380.000000 -554.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2adfaa0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2170.000000 196.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b3d1b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2021.000000 -767.000000)" xlink:href="#earth:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b506f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2231.000000 -457.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f75840" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2347.000000 -455.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2a99010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1466,-413 1487,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="29966@x" ObjectIDND1="29944@x" ObjectIDZND0="g_2a99200@0" Pin0InfoVect0LinkObjId="g_2a99200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ac16b0_0" Pin1InfoVect1LinkObjId="SW-196393_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1466,-413 1487,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a9b360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1372,-446 1357,-446 1357,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="29941@0" Pin0InfoVect0LinkObjId="SW-196391_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1372,-446 1357,-446 1357,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2aa0230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1419,-560 1398,-560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29936@0" ObjectIDZND0="g_2aae890@0" Pin0InfoVect0LinkObjId="g_2aae890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196386_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1419,-560 1398,-560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2aa0490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1468,-560 1455,-560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="29966@x" ObjectIDND1="29934@x" ObjectIDZND0="29936@1" Pin0InfoVect0LinkObjId="SW-196386_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ac16b0_0" Pin1InfoVect1LinkObjId="SW-196384_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1468,-560 1455,-560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2aa06f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1468,-544 1468,-560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29966@1" ObjectIDZND0="29936@x" ObjectIDZND1="29934@x" Pin0InfoVect0LinkObjId="SW-196386_0" Pin0InfoVect1LinkObjId="SW-196384_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ac16b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1468,-544 1468,-560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2aa2e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1419,-703 1398,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29937@0" ObjectIDZND0="g_2aadec0@0" Pin0InfoVect0LinkObjId="g_2aadec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196387_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1419,-703 1398,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2aa30e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1468,-703 1455,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="29933@x" ObjectIDND1="29935@x" ObjectIDZND0="29937@1" Pin0InfoVect0LinkObjId="SW-196387_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-196383_0" Pin1InfoVect1LinkObjId="SW-196385_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1468,-703 1455,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2aa5b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1468,-560 1468,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="29936@x" ObjectIDND1="29966@x" ObjectIDZND0="29934@0" Pin0InfoVect0LinkObjId="SW-196384_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-196386_0" Pin1InfoVect1LinkObjId="g_2ac16b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1468,-560 1468,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2aa5da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1468,-703 1468,-731 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="29933@x" ObjectIDND1="29937@x" ObjectIDZND0="29935@0" Pin0InfoVect0LinkObjId="SW-196385_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-196383_0" Pin1InfoVect1LinkObjId="SW-196387_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1468,-703 1468,-731 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2aa80d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1468,-677 1468,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29933@1" ObjectIDZND0="29935@x" ObjectIDZND1="29937@x" Pin0InfoVect0LinkObjId="SW-196385_0" Pin0InfoVect1LinkObjId="SW-196387_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196383_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1468,-677 1468,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2aa8330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1468,-767 1468,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="29935@1" ObjectIDZND0="29938@x" ObjectIDZND1="29939@x" ObjectIDZND2="g_2aab440@0" Pin0InfoVect0LinkObjId="SW-196388_0" Pin0InfoVect1LinkObjId="SW-196389_0" Pin0InfoVect2LinkObjId="g_2aab440_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196385_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1468,-767 1468,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2aaaac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1468,-787 1482,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="29935@x" ObjectIDND1="29939@x" ObjectIDND2="g_2aab440@0" ObjectIDZND0="29938@0" Pin0InfoVect0LinkObjId="SW-196388_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-196385_0" Pin1InfoVect1LinkObjId="SW-196389_0" Pin1InfoVect2LinkObjId="g_2aab440_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1468,-787 1482,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2aaad20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1518,-787 1535,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29938@1" ObjectIDZND0="g_2aad6e0@0" Pin0InfoVect0LinkObjId="g_2aad6e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196388_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1518,-787 1535,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2aaaf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1468,-820 1449,-820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="29938@x" ObjectIDND1="29935@x" ObjectIDND2="g_2aab440@0" ObjectIDZND0="29939@1" Pin0InfoVect0LinkObjId="SW-196389_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-196388_0" Pin1InfoVect1LinkObjId="SW-196385_0" Pin1InfoVect2LinkObjId="g_2aab440_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1468,-820 1449,-820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2aab1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1468,-787 1468,-820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="29938@x" ObjectIDND1="29935@x" ObjectIDZND0="29939@x" ObjectIDZND1="g_2aab440@0" ObjectIDZND2="31891@1" Pin0InfoVect0LinkObjId="SW-196389_0" Pin0InfoVect1LinkObjId="g_2aab440_0" Pin0InfoVect2LinkObjId="g_2b36a30_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-196388_0" Pin1InfoVect1LinkObjId="SW-196385_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1468,-787 1468,-820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ab90c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1422,-820 1379,-820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="29939@0" ObjectIDZND0="g_2abc610@0" Pin0InfoVect0LinkObjId="g_2abc610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196389_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1422,-820 1379,-820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ac0650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1468,-636 1468,-650 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="47736@0" ObjectIDZND0="29933@0" Pin0InfoVect0LinkObjId="SW-196383_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f7f6b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1468,-636 1468,-650 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ac1450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1389,-446 1389,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1389,-446 1389,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ac16b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1466,-413 1466,-463 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_2a99200@0" ObjectIDND1="29944@x" ObjectIDZND0="29966@0" Pin0InfoVect0LinkObjId="g_2b37e60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a99200_0" Pin1InfoVect1LinkObjId="SW-196393_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1466,-413 1466,-463 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ac8030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1080,-285 1080,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29965@1" ObjectIDZND0="30103@0" Pin0InfoVect0LinkObjId="g_2b03bc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196409_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1080,-285 1080,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ac8b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1081,-402 1081,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_2ac8290@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ac8290_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1081,-402 1081,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ac8d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1042,-351 1080,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2ac8fd0@0" ObjectIDZND0="29965@x" ObjectIDZND1="g_2ac8290@0" Pin0InfoVect0LinkObjId="SW-196409_0" Pin0InfoVect1LinkObjId="g_2ac8290_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ac8fd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1042,-351 1080,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aca610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1081,-334 1081,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="29965@0" ObjectIDZND0="g_2ac8fd0@0" ObjectIDZND1="g_2ac8290@0" Pin0InfoVect0LinkObjId="g_2ac8fd0_0" Pin0InfoVect1LinkObjId="g_2ac8290_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196409_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1081,-334 1081,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aca870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1081,-371 1081,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2ac8290@0" ObjectIDZND0="g_2ac8fd0@0" ObjectIDZND1="29965@x" Pin0InfoVect0LinkObjId="g_2ac8fd0_0" Pin0InfoVect1LinkObjId="SW-196409_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ac8290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1081,-371 1081,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2acd070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1081,-432 1050,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1081,-432 1050,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ade110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2213,139 2213,207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_2adc830@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2adc830_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2213,139 2213,207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ae04f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2176,178 2176,157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_2adfaa0@0" ObjectIDZND0="g_2adee70@1" Pin0InfoVect0LinkObjId="g_2adee70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2adfaa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2176,178 2176,157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ae8bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1466,-378 1466,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="29944@0" ObjectIDZND0="g_2a99200@0" ObjectIDZND1="29966@x" Pin0InfoVect0LinkObjId="g_2a99200_0" Pin0InfoVect1LinkObjId="g_2ac16b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196393_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1466,-378 1466,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aebf30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1466,-238 1466,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="30103@0" ObjectIDZND0="29943@0" Pin0InfoVect0LinkObjId="SW-196393_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ac8030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1466,-238 1466,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aec190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1466,-299 1466,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29943@1" ObjectIDZND0="29942@0" Pin0InfoVect0LinkObjId="SW-196392_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196393_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1466,-299 1466,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aec3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1466,-343 1466,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29942@1" ObjectIDZND0="29944@1" Pin0InfoVect0LinkObjId="SW-196393_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196392_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1466,-343 1466,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aede00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1165,-69 1165,-90 1085,-90 1085,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2acee50@0" ObjectIDZND0="29956@0" Pin0InfoVect0LinkObjId="SW-196402_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2acee50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1165,-69 1165,-90 1085,-90 1085,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2afa060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1124,-238 1124,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="30103@0" ObjectIDZND0="29954@0" Pin0InfoVect0LinkObjId="SW-196401_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ac8030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1124,-238 1124,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2afa2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1124,-196 1124,-182 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29954@1" ObjectIDZND0="29953@1" Pin0InfoVect0LinkObjId="SW-196400_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196401_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1124,-196 1124,-182 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2afd360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1387,-238 1387,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="30103@0" ObjectIDZND0="29950@0" Pin0InfoVect0LinkObjId="SW-196398_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ac8030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1387,-238 1387,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2afd5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1387,-197 1387,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29950@1" ObjectIDZND0="29949@1" Pin0InfoVect0LinkObjId="SW-196397_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196398_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1387,-197 1387,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b00660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1657,-238 1657,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="30103@0" ObjectIDZND0="29946@0" Pin0InfoVect0LinkObjId="SW-196395_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ac8030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1657,-238 1657,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b008c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1657,-193 1657,-184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29946@1" ObjectIDZND0="29945@1" Pin0InfoVect0LinkObjId="SW-196394_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196395_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1657,-193 1657,-184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b03960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1906,-182 1906,-197 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29957@1" ObjectIDZND0="29958@1" Pin0InfoVect0LinkObjId="SW-196404_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196403_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1906,-182 1906,-197 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b03bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1906,-214 1906,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29958@0" ObjectIDZND0="30103@0" Pin0InfoVect0LinkObjId="g_2ac8030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196404_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1906,-214 1906,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b06ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1124,-155 1124,-142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29953@0" ObjectIDZND0="29955@1" Pin0InfoVect0LinkObjId="SW-196401_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1124,-155 1124,-142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b07140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1124,-125 1124,73 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="generator" ObjectIDND0="29955@0" ObjectIDZND0="43260@0" Pin0InfoVect0LinkObjId="SM-CX_BG.P3_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196401_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1124,-125 1124,73 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0a460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1387,-155 1387,-142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29949@0" ObjectIDZND0="29951@1" Pin0InfoVect0LinkObjId="SW-196398_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196397_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1387,-155 1387,-142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0a6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1387,-125 1387,71 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="generator" ObjectIDND0="29951@0" ObjectIDZND0="43259@0" Pin0InfoVect0LinkObjId="SM-CX_BG.P2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196398_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1387,-125 1387,71 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0d9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1657,-156 1657,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29945@0" ObjectIDZND0="29947@1" Pin0InfoVect0LinkObjId="SW-196395_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196394_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1657,-156 1657,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0dc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1657,-124 1657,69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="generator" ObjectIDND0="29947@0" ObjectIDZND0="43258@0" Pin0InfoVect0LinkObjId="SM-CX_BG.P1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196395_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1657,-124 1657,69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b10f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1906,111 1906,-124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2ad8580@0" ObjectIDZND0="29959@0" Pin0InfoVect0LinkObjId="SW-196404_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ad8580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1906,111 1906,-124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b111c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1906,-141 1906,-155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29959@1" ObjectIDZND0="29957@0" Pin0InfoVect0LinkObjId="SW-196403_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196404_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1906,-141 1906,-155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b121d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1427,-70 1427,-91 1347,-91 1347,-84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b11420@0" ObjectIDZND0="29952@0" Pin0InfoVect0LinkObjId="SW-196399_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b11420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-70 1427,-91 1347,-91 1347,-84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b164e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1694,-72 1694,-93 1614,-93 1614,-86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b15730@0" ObjectIDZND0="29948@0" Pin0InfoVect0LinkObjId="SW-196396_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b15730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1694,-72 1694,-93 1614,-93 1614,-86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b1a7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1945,-74 1945,-95 1867,-95 1867,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b19a40@0" ObjectIDZND0="29960@0" Pin0InfoVect0LinkObjId="SW-196405_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b19a40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1945,-74 1945,-95 1867,-95 1867,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b2a5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2256,-62 2256,-83 2178,-83 2178,-76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b2a850@0" ObjectIDZND0="29964@0" Pin0InfoVect0LinkObjId="SW-196408_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b2a850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2256,-62 2256,-83 2178,-83 2178,-76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b2b600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2213,-174 2213,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29961@1" ObjectIDZND0="29962@1" Pin0InfoVect0LinkObjId="SW-196407_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196406_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2213,-174 2213,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b2b860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2213,-211 2213,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29962@0" ObjectIDZND0="30103@0" Pin0InfoVect0LinkObjId="g_2ac8030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196407_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2213,-211 2213,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b2bac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2213,90 2213,-106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2adc830@0" ObjectIDZND0="29963@0" Pin0InfoVect0LinkObjId="SW-196407_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2adc830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2213,90 2213,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b2bd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2213,-123 2213,-147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29963@1" ObjectIDZND0="29961@0" Pin0InfoVect0LinkObjId="SW-196406_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196407_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2213,-123 2213,-147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b36a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1468,-841 1468,-848 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2aab440@0" ObjectIDND1="29938@x" ObjectIDND2="29935@x" ObjectIDZND0="31891@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2aab440_0" Pin1InfoVect1LinkObjId="SW-196388_0" Pin1InfoVect2LinkObjId="SW-196385_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1468,-841 1468,-848 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b373f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1487,-841 1468,-841 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2aab440@0" ObjectIDZND0="29938@x" ObjectIDZND1="29935@x" ObjectIDZND2="29939@x" Pin0InfoVect0LinkObjId="SW-196388_0" Pin0InfoVect1LinkObjId="SW-196385_0" Pin0InfoVect2LinkObjId="SW-196389_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2aab440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1487,-841 1468,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b37640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1468,-841 1468,-820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2aab440@0" ObjectIDND1="31891@1" ObjectIDZND0="29938@x" ObjectIDZND1="29935@x" ObjectIDZND2="29939@x" Pin0InfoVect0LinkObjId="SW-196388_0" Pin0InfoVect1LinkObjId="SW-196385_0" Pin0InfoVect2LinkObjId="SW-196389_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2aab440_0" Pin1InfoVect1LinkObjId="g_2b36a30_1" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1468,-841 1468,-820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b37e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1357,-501 1357,-525 1469,-525 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="29941@1" ObjectIDZND0="29966@x" Pin0InfoVect0LinkObjId="g_2ac16b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196391_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1357,-501 1357,-525 1469,-525 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b38250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1988,-753 1988,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_2b3dca0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b3dca0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1988,-753 1988,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b446b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2204,-596 2204,-518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2aad6e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2204,-596 2204,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b44910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2204,-753 2204,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_2b46d60@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b46d60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2204,-753 2204,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b4dc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2002,-435 2002,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2aad6e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2002,-435 2002,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b501b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2191,-435 2191,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2aad6e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2191,-435 2191,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b51180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2237,-462 2237,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="busSection" ObjectIDND0="g_2b506f0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2aad6e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b506f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2237,-462 2237,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b513e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2319,-493 2319,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2aad6e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2319,-493 2319,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b51640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2319,-650 2319,-700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_2b518a0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b518a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2319,-650 2319,-700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2f755e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2319,-436 2319,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2aad6e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2319,-436 2319,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2f762d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2353,-460 2353,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="busSection" ObjectIDND0="g_2f75840@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2aad6e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f75840_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2353,-460 2353,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2f79010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1988,-596 1988,-518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2aad6e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1988,-596 1988,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f7f6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1468,-616 1468,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29934@1" ObjectIDZND0="47736@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196384_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1468,-616 1468,-636 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectPoint_Layer"/><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="1" id="ME-196419" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 845.000000 -333.000000) translate(0,12)">196419.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196419" ObjectName="CX_BG.CX_BG_3IM:F"/>
     <cge:PSR_Ref ObjectID="30103"/>
     <cge:Term_Ref ObjectID="42775"/>
    <cge:TPSR_Ref TObjectID="30103"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="1" id="ME-196420" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 845.000000 -333.000000) translate(0,27)">196420.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196420" ObjectName="CX_BG.CX_BG_3IM:F"/>
     <cge:PSR_Ref ObjectID="30103"/>
     <cge:Term_Ref ObjectID="42775"/>
    <cge:TPSR_Ref TObjectID="30103"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="1" id="ME-196421" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 845.000000 -333.000000) translate(0,42)">196421.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196421" ObjectName="CX_BG.CX_BG_3IM:F"/>
     <cge:PSR_Ref ObjectID="30103"/>
     <cge:Term_Ref ObjectID="42775"/>
    <cge:TPSR_Ref TObjectID="30103"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="1" id="ME-196422" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 845.000000 -333.000000) translate(0,57)">196422.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196422" ObjectName="CX_BG.CX_BG_3IM:F"/>
     <cge:PSR_Ref ObjectID="30103"/>
     <cge:Term_Ref ObjectID="42775"/>
    <cge:TPSR_Ref TObjectID="30103"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="1" id="ME-196425" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 845.000000 -333.000000) translate(0,72)">196425.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196425" ObjectName="CX_BG.CX_BG_3IM:F"/>
     <cge:PSR_Ref ObjectID="30103"/>
     <cge:Term_Ref ObjectID="42775"/>
    <cge:TPSR_Ref TObjectID="30103"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-196347" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1581.000000 -685.000000) translate(0,12)">196347.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196347" ObjectName="CX_BG.CX_BG_151BK:F"/>
     <cge:PSR_Ref ObjectID="29933"/>
     <cge:Term_Ref ObjectID="42435"/>
    <cge:TPSR_Ref TObjectID="29933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-196348" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1581.000000 -685.000000) translate(0,27)">196348.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196348" ObjectName="CX_BG.CX_BG_151BK:F"/>
     <cge:PSR_Ref ObjectID="29933"/>
     <cge:Term_Ref ObjectID="42435"/>
    <cge:TPSR_Ref TObjectID="29933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-196346" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1581.000000 -685.000000) translate(0,42)">196346.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196346" ObjectName="CX_BG.CX_BG_151BK:F"/>
     <cge:PSR_Ref ObjectID="29933"/>
     <cge:Term_Ref ObjectID="42435"/>
    <cge:TPSR_Ref TObjectID="29933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-196352" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1586.000000 -347.000000) translate(0,12)">196352.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196352" ObjectName="CX_BG.CX_BG_301BK:F"/>
     <cge:PSR_Ref ObjectID="29942"/>
     <cge:Term_Ref ObjectID="42453"/>
    <cge:TPSR_Ref TObjectID="29942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-196353" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1586.000000 -347.000000) translate(0,27)">196353.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196353" ObjectName="CX_BG.CX_BG_301BK:F"/>
     <cge:PSR_Ref ObjectID="29942"/>
     <cge:Term_Ref ObjectID="42453"/>
    <cge:TPSR_Ref TObjectID="29942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-196351" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1586.000000 -347.000000) translate(0,42)">196351.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196351" ObjectName="CX_BG.CX_BG_301BK:F"/>
     <cge:PSR_Ref ObjectID="29942"/>
     <cge:Term_Ref ObjectID="42453"/>
    <cge:TPSR_Ref TObjectID="29942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-196364" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1096.000000 162.000000) translate(0,12)">196364.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196364" ObjectName="CX_BG.CX_BG_353BK:F"/>
     <cge:PSR_Ref ObjectID="29953"/>
     <cge:Term_Ref ObjectID="42475"/>
    <cge:TPSR_Ref TObjectID="29953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-196365" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1096.000000 162.000000) translate(0,27)">196365.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196365" ObjectName="CX_BG.CX_BG_353BK:F"/>
     <cge:PSR_Ref ObjectID="29953"/>
     <cge:Term_Ref ObjectID="42475"/>
    <cge:TPSR_Ref TObjectID="29953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-196366" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1096.000000 162.000000) translate(0,42)">196366.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196366" ObjectName="CX_BG.CX_BG_353BK:F"/>
     <cge:PSR_Ref ObjectID="29953"/>
     <cge:Term_Ref ObjectID="42475"/>
    <cge:TPSR_Ref TObjectID="29953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-196360" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1354.000000 169.000000) translate(0,12)">196360.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196360" ObjectName="CX_BG.CX_BG_352BK:F"/>
     <cge:PSR_Ref ObjectID="29949"/>
     <cge:Term_Ref ObjectID="42467"/>
    <cge:TPSR_Ref TObjectID="29949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-196361" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1354.000000 169.000000) translate(0,27)">196361.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196361" ObjectName="CX_BG.CX_BG_352BK:F"/>
     <cge:PSR_Ref ObjectID="29949"/>
     <cge:Term_Ref ObjectID="42467"/>
    <cge:TPSR_Ref TObjectID="29949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-196362" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1354.000000 169.000000) translate(0,42)">196362.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196362" ObjectName="CX_BG.CX_BG_352BK:F"/>
     <cge:PSR_Ref ObjectID="29949"/>
     <cge:Term_Ref ObjectID="42467"/>
    <cge:TPSR_Ref TObjectID="29949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-196356" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1630.000000 160.000000) translate(0,12)">196356.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196356" ObjectName="CX_BG.CX_BG_351BK:F"/>
     <cge:PSR_Ref ObjectID="29945"/>
     <cge:Term_Ref ObjectID="42459"/>
    <cge:TPSR_Ref TObjectID="29945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-196357" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1630.000000 160.000000) translate(0,27)">196357.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196357" ObjectName="CX_BG.CX_BG_351BK:F"/>
     <cge:PSR_Ref ObjectID="29945"/>
     <cge:Term_Ref ObjectID="42459"/>
    <cge:TPSR_Ref TObjectID="29945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-196358" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1630.000000 160.000000) translate(0,42)">196358.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196358" ObjectName="CX_BG.CX_BG_351BK:F"/>
     <cge:PSR_Ref ObjectID="29945"/>
     <cge:Term_Ref ObjectID="42459"/>
    <cge:TPSR_Ref TObjectID="29945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-196372" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1889.000000 211.000000) translate(0,12)">196372.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196372" ObjectName="CX_BG.CX_BG_354BK:F"/>
     <cge:PSR_Ref ObjectID="29957"/>
     <cge:Term_Ref ObjectID="42483"/>
    <cge:TPSR_Ref TObjectID="29957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-196373" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1889.000000 211.000000) translate(0,27)">196373.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196373" ObjectName="CX_BG.CX_BG_354BK:F"/>
     <cge:PSR_Ref ObjectID="29957"/>
     <cge:Term_Ref ObjectID="42483"/>
    <cge:TPSR_Ref TObjectID="29957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-196369" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2211.000000 255.000000) translate(0,12)">196369.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196369" ObjectName="CX_BG.CX_BG_355BK:F"/>
     <cge:PSR_Ref ObjectID="29961"/>
     <cge:Term_Ref ObjectID="42491"/>
    <cge:TPSR_Ref TObjectID="29961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-196370" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2211.000000 255.000000) translate(0,27)">196370.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196370" ObjectName="CX_BG.CX_BG_355BK:F"/>
     <cge:PSR_Ref ObjectID="29961"/>
     <cge:Term_Ref ObjectID="42491"/>
    <cge:TPSR_Ref TObjectID="29961"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="155" x="533" y="-824"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="155" x="533" y="-824"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="484" y="-841"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="484" y="-841"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="707,-698 704,-701 704,-647 707,-650 707,-698" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="707,-698 704,-701 853,-701 850,-698 707,-698" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(112,119,119)" points="707,-650 704,-647 853,-647 850,-650 707,-650" stroke="rgb(112,119,119)"/>
     <polygon fill="rgb(112,119,119)" points="850,-698 853,-701 853,-647 850,-650 850,-698" stroke="rgb(112,119,119)"/>
     <rect fill="rgb(224,238,238)" height="48" stroke="rgb(224,238,238)" width="143" x="707" y="-698"/>
     <rect fill="none" height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="707" y="-698"/>
    </a>
   <metadata/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="155" x="533" y="-824"/></g>
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="484" y="-841"/></g>
   <g href="AVC班果.svg" style="fill-opacity:0"><rect height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="707" y="-698"/></g>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="2002,-454 2003,-454 2005,-453 2006,-453 2008,-453 2009,-452 2010,-451 2011,-450 2012,-449 2012,-448 2013,-447 2013,-446 2014,-445 2014,-444 2013,-443 2013,-442 2012,-441 2012,-440 2011,-439 2010,-438 2009,-437 2008,-436 2006,-436 2005,-436 2003,-435 2002,-435 " stroke="rgb(60,120,255)" stroke-width="0.404269"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="2172,-532 2172,-533 2172,-534 2172,-534 2173,-535 2173,-535 2173,-536 2174,-536 2174,-537 2175,-537 2175,-537 2176,-537 2176,-537 2177,-537 2177,-537 2178,-537 2178,-537 2179,-537 2179,-536 2180,-536 2180,-535 2180,-535 2181,-534 2181,-534 2181,-533 2181,-532 " stroke="rgb(60,120,255)" stroke-width="0.191496"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="2191,-454 2192,-454 2194,-453 2195,-453 2197,-453 2198,-452 2199,-451 2200,-450 2201,-449 2201,-448 2202,-447 2202,-446 2203,-445 2203,-444 2202,-443 2202,-442 2201,-441 2201,-440 2200,-439 2199,-438 2198,-437 2197,-436 2195,-436 2194,-436 2192,-435 2191,-435 " stroke="rgb(60,120,255)" stroke-width="0.404269"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="2319,-455 2320,-455 2322,-454 2323,-454 2325,-454 2326,-453 2327,-452 2328,-451 2329,-450 2329,-449 2330,-448 2330,-447 2331,-446 2331,-445 2330,-444 2330,-443 2329,-442 2329,-441 2328,-440 2327,-439 2326,-438 2325,-437 2323,-437 2322,-437 2320,-436 2319,-436 " stroke="rgb(60,120,255)" stroke-width="0.404269"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="rgb(139,102,139)" points="1389,-479 1384,-468 1395,-468 1389,-479 1389,-478 1389,-479 " stroke="rgb(170,85,127)"/>
   <polyline DF8003:Layer="PUBLIC" fill="rgb(139,102,139)" points="1389,-493 1384,-504 1395,-504 1389,-493 1389,-494 1389,-493 " stroke="rgb(170,85,127)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1988,-870 1982,-883 1995,-883 1988,-870 1988,-871 1988,-870 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="2204,-870 2198,-883 2211,-883 2204,-870 2204,-871 2204,-870 " stroke="rgb(60,120,255)"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2abc610">
    <use class="BV-110KV" transform="matrix(0.000000 -0.554113 -0.666667 -0.000000 1383.450980 -797.484848)" xlink:href="#voltageTransformer:shape71"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_BG.CX_BG_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="42503"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1430.000000 -458.000000)" xlink:href="#transformer2:shape14_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1430.000000 -458.000000)" xlink:href="#transformer2:shape14_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="29966" ObjectName="TF-CX_BG.CX_BG_1T"/>
    <cge:TPSR_Ref TObjectID="29966"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 521.000000 -765.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-196347" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 546.538462 -632.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196347" ObjectName="CX_BG:CX_BG_151BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-196348" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 545.538462 -592.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196348" ObjectName="CX_BG:CX_BG_151BK_Q"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-196187" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 713.000000 -733.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29872" ObjectName="DYN-CX_BG"/>
     <cge:Meas_Ref ObjectId="196187"/>
    </metadata>
   </g>
  </g><g id="Circle_Layer">
   <circle DF8003:Layer="PUBLIC" cx="1081" cy="-432" fill="none" r="7.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1081" cy="-444" fill="none" r="7.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1093" cy="-438" fill="none" r="7.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1070" cy="-437" fill="none" r="7.5" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-196383">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1459.000000 -642.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29933" ObjectName="SW-CX_BG.CX_BG_151BK"/>
     <cge:Meas_Ref ObjectId="196383"/>
    <cge:TPSR_Ref TObjectID="29933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196392">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1457.000000 -308.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29942" ObjectName="SW-CX_BG.CX_BG_301BK"/>
     <cge:Meas_Ref ObjectId="196392"/>
    <cge:TPSR_Ref TObjectID="29942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196400">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1115.000000 -147.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29953" ObjectName="SW-CX_BG.CX_BG_353BK"/>
     <cge:Meas_Ref ObjectId="196400"/>
    <cge:TPSR_Ref TObjectID="29953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196397">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1378.000000 -147.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29949" ObjectName="SW-CX_BG.CX_BG_352BK"/>
     <cge:Meas_Ref ObjectId="196397"/>
    <cge:TPSR_Ref TObjectID="29949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196394">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1648.000000 -147.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29945" ObjectName="SW-CX_BG.CX_BG_351BK"/>
     <cge:Meas_Ref ObjectId="196394"/>
    <cge:TPSR_Ref TObjectID="29945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196403">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1897.000000 -147.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29957" ObjectName="SW-CX_BG.CX_BG_354BK"/>
     <cge:Meas_Ref ObjectId="196403"/>
    <cge:TPSR_Ref TObjectID="29957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196406">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2204.000000 -139.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29961" ObjectName="SW-CX_BG.CX_BG_355BK"/>
     <cge:Meas_Ref ObjectId="196406"/>
    <cge:TPSR_Ref TObjectID="29961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2017.000000 -784.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.900000 -0.000000 0.000000 -0.882353 1979.000000 -590.235294)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.900000 -0.000000 0.000000 -0.882353 2195.000000 -590.235294)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.900000 -0.000000 0.000000 -0.882353 2310.000000 -487.235294)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2a99200">
    <use class="BV-35KV" transform="matrix(-0.935484 -0.000000 -0.000000 0.928571 1541.000000 -420.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2aab440">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1483.000000 -833.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ac8290">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1072.000000 -366.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ac8fd0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1035.000000 -347.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2acee50">
    <use class="BV-35KV" transform="matrix(0.000000 -0.935484 -0.928571 -0.000000 1172.000000 -15.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ad2c50">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1114.000000 28.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ad5c50">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1377.000000 26.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ad7300">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1647.000000 24.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ad7d40">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1896.000000 22.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ad8120">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1916.000000 44.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ad8580">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1889.000000 141.000000)" xlink:href="#lightningRod:shape177"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2adbe80">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2203.000000 6.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2adc320">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 2223.000000 28.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2adc830">
    <use class="BV-35KV" transform="matrix(0.714286 -0.000000 0.000000 -0.712500 2198.000000 143.000000)" xlink:href="#lightningRod:shape11"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ade370">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2203.000000 194.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2adee70">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2169.000000 162.000000)" xlink:href="#lightningRod:shape174"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b11420">
    <use class="BV-35KV" transform="matrix(0.000000 -0.935484 -0.928571 -0.000000 1434.000000 -16.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b15730">
    <use class="BV-35KV" transform="matrix(0.000000 -0.935484 -0.928571 -0.000000 1701.000000 -18.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b19a40">
    <use class="BV-35KV" transform="matrix(0.000000 -0.935484 -0.928571 -0.000000 1952.000000 -20.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b2a850">
    <use class="BV-35KV" transform="matrix(0.000000 -0.935484 -0.928571 -0.000000 2263.000000 -8.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b3dca0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1997.500000 -715.500000)" xlink:href="#lightningRod:shape192"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b46d60">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2213.500000 -715.500000)" xlink:href="#lightningRod:shape192"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b518a0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2328.500000 -612.500000)" xlink:href="#lightningRod:shape192"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28f61d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 857.000000 -220.000000) translate(0,15)">I段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bdbef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 430.000000 -234.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bdbef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 430.000000 -234.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bdbef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 430.000000 -234.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bdbef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 430.000000 -234.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bdbef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 430.000000 -234.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bdbef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 430.000000 -234.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bdbef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 430.000000 -234.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bdbef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 430.000000 -234.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bdbef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 430.000000 -234.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bdbef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 430.000000 -234.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bdbef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 430.000000 -234.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bdbef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 430.000000 -234.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bdbef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 430.000000 -234.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bdbef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 430.000000 -234.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bdbef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 430.000000 -234.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bdbef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 430.000000 -234.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bdbef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 430.000000 -234.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2bdbef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 430.000000 -234.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a13100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 430.000000 -672.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a13100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 430.000000 -672.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a13100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 430.000000 -672.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a13100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 430.000000 -672.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a13100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 430.000000 -672.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a13100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 430.000000 -672.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a13100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 430.000000 -672.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2a90a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 560.000000 -813.500000) translate(0,16)">班果光伏电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2a92f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 545.000000 113.000000) translate(0,17)">0878-8383520</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aac1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1491.000000 -813.000000) translate(0,12)">K1517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2aaf2c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1482.000000 -915.000000) translate(0,16)">班</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2aaf2c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1482.000000 -915.000000) translate(0,36)">黄</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2aaf2c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1482.000000 -915.000000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab0140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1572.000000 -546.000000) translate(0,12)">1号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab0140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1572.000000 -546.000000) translate(0,27)">SZ11-50000/110GYW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab0140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1572.000000 -546.000000) translate(0,42)">115±8x1.25%/35kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab0140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1572.000000 -546.000000) translate(0,57)">50MVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab0140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1572.000000 -546.000000) translate(0,72)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab0140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1572.000000 -546.000000) translate(0,87)">Ud%=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2abf060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1326.000000 -846.000000) translate(0,12)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2acd2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1042.000000 -498.000000) translate(0,12)"> 35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2acd2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1042.000000 -498.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ace190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1092.000000 -318.000000) translate(0,15)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ad3200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1084.000000 110.000000) translate(0,15)">3号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ad4310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1057.000000 134.000000) translate(0,15)">26、29-40号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ad5f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 108.000000) translate(0,15)">2号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ad6560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1345.000000 127.000000) translate(0,15)">  15-25、</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ad6560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1345.000000 127.000000) translate(0,33)">27-28号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ad7720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1617.000000 106.000000) translate(0,15)">1号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ad7b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1614.000000 129.000000) translate(0,15)">1-14号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ad91e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1866.000000 148.000000) translate(0,15)">1号动态无</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ad91e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1866.000000 148.000000) translate(0,33)">功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2adac90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1875.500000 189.000000) translate(0,15)">±13MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ae0750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2159.500000 210.000000) translate(0,15)">1号站用变及小</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ae0750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2159.500000 210.000000) translate(0,33)">电阻接地装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ae27c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2231.500000 104.000000) translate(0,12)">1000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ae29f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1416.000000 -840.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ae2c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1401.000000 -806.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ae2e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1475.000000 -756.000000) translate(0,12)">1516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ae31e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1418.000000 -729.000000) translate(0,12)">15160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ae3680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1477.000000 -671.000000) translate(0,12)">151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ae38c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1475.000000 -605.000000) translate(0,12)">1511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ae3b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1418.000000 -586.000000) translate(0,12)">15117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ae3d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1479.000000 -466.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ae3f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1316.000000 -488.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aec650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1475.000000 -337.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b1dd50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1036.000000 -67.000000) translate(0,12)">35367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b1e380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1400.000000 -174.000000) translate(0,12)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b1e5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1299.000000 -66.000000) translate(0,12)">35267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b1e800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1670.000000 -174.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b1ea40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1562.000000 -69.000000) translate(0,12)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b1ec80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1919.000000 -174.000000) translate(0,12)">354</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b1f010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1814.000000 -76.000000) translate(0,12)">35467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2bf80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2226.000000 -166.000000) translate(0,12)">355</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2f4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2127.000000 -63.000000) translate(0,12)">35567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2ffd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1137.000000 -174.000000) translate(0,12)">353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b302f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1136.000000 -212.000000) translate(0,12)">353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b38ea0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1953.000000 -919.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b44b70" transform="matrix(1.000000 0.000000 -0.000000 1.000000 2154.000000 -937.000000) translate(0,15)">10kV虎溪线T</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b44b70" transform="matrix(1.000000 0.000000 -0.000000 1.000000 2154.000000 -937.000000) translate(0,33)">金沙平村支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f76530" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1918.000000 -368.000000) translate(0,15)">0.4kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f77220" transform="matrix(1.000000 0.000000 -0.000000 1.000000 2198.000000 -372.000000) translate(0,15)">0.4kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f77780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1855.500000 -846.000000) translate(0,15)">1号站用变及小</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f77780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1855.500000 -846.000000) translate(0,33)">电阻接地装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f77780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1855.500000 -846.000000) translate(0,51)">   1000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f77b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2226.500000 -852.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f77b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2226.500000 -852.000000) translate(0,33)"> 250kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f77fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2287.500000 -742.000000) translate(0,15)">100kW柴</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f77fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2287.500000 -742.000000) translate(0,33)">油发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2f7df00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 734.000000 -683.000000) translate(0,16)">AGC/AVC</text>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="1389" x2="1389" y1="-525" y2="-504"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="1389" x2="1414" y1="-446" y2="-446"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1372,-446 1389,-446 1389,-468 " stroke="rgb(170,85,127)" stroke-width="1"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="0.151049" x1="1414" x2="1414" y1="-446" y2="-451"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="1414" x2="1414" y1="-492" y2="-457"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1395" x2="1383" y1="-426" y2="-426"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.228882" x1="1390" x2="1388" y1="-419" y2="-419"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.322998" x1="1392" x2="1386" y1="-422" y2="-422"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.251748" x1="1389" x2="1389" y1="-426" y2="-435"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1081" x2="1081" y1="-444" y2="-441"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1078" x2="1081" y1="-446" y2="-444"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1084" x2="1081" y1="-446" y2="-444"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1094" x2="1094" y1="-438" y2="-435"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1091" x2="1094" y1="-440" y2="-438"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1097" x2="1094" y1="-440" y2="-438"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1074" x2="1070" y1="-439" y2="-441"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1074" x2="1070" y1="-437" y2="-435"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1070" x2="1070" y1="-435" y2="-441"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1055,-443 1045,-450 1045,-453 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1050" x2="1050" y1="-454" y2="-461"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1048" x2="1052" y1="-462" y2="-462"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1049" x2="1051" y1="-464" y2="-464"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1047" x2="1054" y1="-461" y2="-461"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1081" x2="1081" y1="-432" y2="-429"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1078" x2="1081" y1="-434" y2="-432"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1084" x2="1081" y1="-434" y2="-432"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1050" x2="1050" y1="-443" y2="-432"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="2213" x2="2176" y1="103" y2="103"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="2177" x2="2177" y1="103" y2="103"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="2176" x2="2176" y1="103" y2="127"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(176,48,96)" stroke-width="1" x1="1414" x2="1414" y1="-525" y2="-491"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1988" x2="2010" y1="-571" y2="-571"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2034" x2="2066" y1="-569" y2="-569"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2073" x2="2093" y1="-569" y2="-569"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2093" x2="2093" y1="-574" y2="-565"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2095" x2="2095" y1="-573" y2="-567"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2097" x2="2097" y1="-572" y2="-567"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1987" x2="1992" y1="-816" y2="-821"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1987" x2="1987" y1="-816" y2="-811"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1987" x2="1982" y1="-816" y2="-821"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1988" x2="1988" y1="-837" y2="-832"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1988" x2="1992" y1="-837" y2="-841"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1988" x2="1984" y1="-837" y2="-841"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1988" x2="1988" y1="-832" y2="-832"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1984" x2="1986" y1="-841" y2="-843"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1988" x2="1988" y1="-837" y2="-837"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1988" x2="1988" y1="-897" y2="-855"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1988" x2="2026" y1="-837" y2="-837"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2026" x2="2026" y1="-837" y2="-824"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1988" x2="1988" y1="-720" y2="-678"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1989" x2="2177" y1="-541" y2="-541"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1988" x2="2015" y1="-518" y2="-518"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2015" x2="2015" y1="-475" y2="-462"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2015" x2="2015" y1="-507" y2="-495"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1988" x2="1988" y1="-475" y2="-462"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1988" x2="1988" y1="-495" y2="-507"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1988" x2="2015" y1="-462" y2="-462"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1935" x2="1935" y1="-653" y2="-447"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2204" x2="2204" y1="-897" y2="-855"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2204" x2="2204" y1="-720" y2="-678"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2177" x2="2204" y1="-518" y2="-518"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2204" x2="2204" y1="-475" y2="-462"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2204" x2="2204" y1="-507" y2="-495"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2177" x2="2177" y1="-475" y2="-462"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2177" x2="2177" y1="-495" y2="-507"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2177" x2="2204" y1="-462" y2="-462"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2015" x2="2015" y1="-518" y2="-532"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2015" x2="2172" y1="-532" y2="-532"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2177" x2="2177" y1="-518" y2="-541"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2181" x2="2204" y1="-532" y2="-532"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2002" x2="2002" y1="-454" y2="-462"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.335714" x1="2204" x2="2198" y1="-849" y2="-839"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2204" x2="2199" y1="-815" y2="-820"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2204" x2="2204" y1="-815" y2="-810"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2204" x2="2209" y1="-815" y2="-820"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.335714" x1="2198" x2="2209" y1="-839" y2="-839"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.335714" x1="2204" x2="2209" y1="-849" y2="-839"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2204" x2="2231" y1="-815" y2="-815"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2231" x2="2231" y1="-815" y2="-795"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2231" x2="2204" y1="-795" y2="-779"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2204" x2="2182" y1="-556" y2="-556"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2158" x2="2126" y1="-554" y2="-554"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2119" x2="2099" y1="-554" y2="-554"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2099" x2="2099" y1="-559" y2="-550"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2097" x2="2097" y1="-558" y2="-552"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2095" x2="2095" y1="-557" y2="-552"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2191" x2="2191" y1="-454" y2="-462"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2255" x2="2255" y1="-654" y2="-447"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2319" x2="2319" y1="-617" y2="-575"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2370" x2="2370" y1="-551" y2="-446"/>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="30103" cx="1124" cy="-238" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30103" cx="1906" cy="-238" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30103" cx="1387" cy="-238" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30103" cx="1657" cy="-238" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30103" cx="2213" cy="-238" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30103" cx="1080" cy="-238" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="2237" cy="-446" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="2353" cy="-446" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47736" cx="1468" cy="-636" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47736" cx="1468" cy="-636" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="2002" cy="-381" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="2191" cy="-382" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="2319" cy="-382" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_BG.CX_BG_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="871,-238 2376,-238 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="30103" ObjectName="BS-CX_BG.CX_BG_3IM"/>
    <cge:TPSR_Ref TObjectID="30103"/></metadata>
   <polyline fill="none" opacity="0" points="871,-238 2376,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1889,-446 2406,-446 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1889,-446 2406,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1889,-381 2079,-381 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1889,-381 2079,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2126,-382 2407,-382 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="2126,-382 2407,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_BG.XM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1454,-636 1484,-636 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="47736" ObjectName="BS-CX_BG.XM"/>
    <cge:TPSR_Ref TObjectID="47736"/></metadata>
   <polyline fill="none" opacity="0" points="1454,-636 1484,-636 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_BG"/>
</svg>