<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-23" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-246 -5241 2598 2031">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="16" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.7" x1="19" x2="10" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="25" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="16" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="20" x2="11" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="11" x2="2" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="11" x2="11" y1="9" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="66"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="16" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="25" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="16" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="11" x2="11" y1="9" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="11" x2="2" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="20" x2="11" y1="17" y2="8"/>
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="40"/>
   </symbol>
   <symbol id="breaker2:shape9_0">
    <rect height="14" stroke-width="0.833219" width="27" x="39" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="89" x2="80" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="88" x2="97" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="97" x2="88" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="80" x2="89" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="89" x2="66" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="13" x2="39" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="22" x2="13" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="13" x2="22" y1="10" y2="1"/>
   </symbol>
   <symbol id="breaker2:shape9_1">
    <rect height="14" stroke-width="0.833219" width="27" x="39" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="39" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="97" x2="66" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="97" x2="88" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="88" x2="97" y1="19" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape9-UnNor1">
    <rect height="14" stroke-width="0.833219" width="27" x="39" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="13" x2="22" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="22" x2="13" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="13" x2="39" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="89" x2="66" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="80" x2="89" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="97" x2="88" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="88" x2="97" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="89" x2="80" y1="10" y2="1"/>
   </symbol>
   <symbol id="breaker2:shape9-UnNor2">
    <rect height="14" stroke-width="0.833219" width="27" x="39" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="6" x2="39" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="97" x2="66" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="97" x2="88" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="88" x2="97" y1="19" y2="10"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="55" x2="55" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="54" x2="46" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="59" x2="59" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="62" x2="62" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="19" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="39" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape29">
    <ellipse cx="11" cy="15" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="28" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape116">
    <circle cx="8" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="14" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="18" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape81">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="40" y2="27"/>
    <ellipse cx="9" cy="18" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <circle cx="9" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="18" cy="12" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape123">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="2" y2="2"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="20" x2="20" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="5" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="10" x2="8" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="8" y1="4" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="11" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="16" x2="14" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="14" y1="15" y2="18"/>
    <ellipse cx="19" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <ellipse cx="14" cy="16" fillStyle="0" rx="9" ry="7.5" stroke-width="0.155709"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="59" x2="24" y1="7" y2="7"/>
    <rect height="12" stroke-width="1" width="26" x="18" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="5" x2="5" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="17" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="8" x2="8" y1="12" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape192">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="26" y1="9" y2="9"/>
    <polyline DF8003:Layer="0" points="5,19 17,9 5,0 5,19 " stroke-width="1"/>
    <polyline DF8003:Layer="0" points="38,1 26,10 38,19 38,1 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape210">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="74" y2="66"/>
    <circle cx="9" cy="23" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="10" cy="9" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="13" y1="65" y2="65"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,63 9,41 9,32 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="11" y="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183673" x1="9" x2="9" y1="21" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183673" x1="9" x2="11" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183673" x1="6" x2="9" y1="27" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183673" x1="10" x2="6" y1="13" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183673" x1="10" x2="13" y1="13" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183673" x1="6" x2="13" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape211">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="74" x2="66" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183673" x1="7" x2="7" y1="6" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183673" x1="13" x2="7" y1="10" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183673" x1="13" x2="7" y1="10" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183673" x1="27" x2="24" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183673" x1="24" x2="27" y1="9" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183673" x1="21" x2="24" y1="9" y2="9"/>
    <rect height="19" stroke-width="1" width="4" x="50" y="11"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="63,31 41,9 32,9 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="65" x2="65" y1="7" y2="13"/>
    <circle cx="9" cy="10" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="23" cy="9" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="-15" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-16,39 6,17 6,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="-15" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-16,39 6,17 6,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape26_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <rect height="10" stroke-width="0.416609" width="28" x="23" y="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="23" x2="49" y1="30" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <rect height="14" stroke-width="0.416609" width="26" x="19" y="-2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="12" x2="48" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="48" x2="18" y1="5" y2="34"/>
    <rect height="9" stroke-width="0.416609" width="29" x="21" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
   </symbol>
   <symbol id="switch2:shape26-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="12" x2="48" y1="5" y2="5"/>
    <rect height="14" stroke-width="0.416609" width="26" x="19" y="-2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape7_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="18" x2="43" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="18" x2="9" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="5" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="9" x2="9" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape7_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="transformer2:shape1_0">
    <circle cx="25" cy="61" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="16" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="33" y1="59" y2="59"/>
   </symbol>
   <symbol id="transformer2:shape1_1">
    <ellipse cx="25" cy="29" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape20_0">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <polyline DF8003:Layer="0" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="13" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="11" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="19" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape20_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape48_0">
    <ellipse cx="25" cy="29" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape48_1">
    <circle cx="25" cy="61" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="33" y1="59" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="16" y1="75" y2="59"/>
   </symbol>
   <symbol id="transformer2:shape25_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="0" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="58" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape25_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="0" points="12,76 19,76 16,83 12,76 "/>
   </symbol>
   <symbol id="transformer2:shape43_0">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="60" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="68" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="76" y2="68"/>
   </symbol>
   <symbol id="transformer2:shape43_1">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape2_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape2_1">
    <circle cx="13" cy="18" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="18" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="9" y1="20" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="18" y1="20" y2="12"/>
   </symbol>
   <symbol id="transformer2:shape32_0">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
   </symbol>
   <symbol id="transformer2:shape32_1">
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
   </symbol>
   <symbol id="voltageTransformer:shape56">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="1" y1="58" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="20" y1="58" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="20" y1="38" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="13" y1="51" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="22" y1="59" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="37" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="61" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="70" y2="61"/>
    <ellipse cx="21" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="21" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="18" y2="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape21">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="27" y1="11" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="11" y2="5"/>
    <circle cx="27" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="13" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="voltageTransformer:shape80">
    <ellipse cx="8" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <rect height="24" stroke-width="0.379884" width="14" x="1" y="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="67" y2="23"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
   </symbol>
   <symbol id="voltageTransformer:shape141">
    <ellipse cx="55" cy="8" fillStyle="0" rx="7" ry="7.5" stroke-width="1"/>
    <rect height="14" stroke-width="0.379884" width="24" x="17" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="48" y1="9" y2="9"/>
    <ellipse cx="63" cy="8" fillStyle="0" rx="7" ry="7.5" stroke-width="1"/>
   </symbol>
   <symbol id="voltageTransformer:shape140">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="1" y1="66" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="24" y1="9" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="18" y1="8" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="8" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="24" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="18" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="22" y2="18"/>
    <circle cx="22" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="22" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="78" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="69" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="45" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="22" y1="67" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="13" y1="59" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="20" y1="46" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="20" y1="66" y2="53"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4205ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4206900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_4207280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_4207f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_4208e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_4209aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_420a2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_420abb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_420b2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_420bc30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_420c730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_420cd10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_420e730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_420f2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_420fbd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_4210360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4211890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4212320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4212aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_4213490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4214670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4214ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4215ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_421ad60" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_421b760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_42175d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_42189d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_4219490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_4220810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_421d2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="2041" width="2608" x="-251" y="-5246"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.382353" x1="929" x2="927" y1="-4618" y2="-4618"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.382353" x1="930" x2="926" y1="-4616" y2="-4616"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.826087" x1="961" x2="936" y1="-4762" y2="-4761"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.382353" x1="936" x2="936" y1="-4773" y2="-4780"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.826087" x1="923" x2="898" y1="-4648" y2="-4647"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="942" x2="942" y1="-3934" y2="-3926"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="945" x2="939" y1="-3925" y2="-3925"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.611465" x1="877" x2="888" y1="-3877" y2="-3877"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.251748" x1="882" x2="882" y1="-3878" y2="-3886"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.39375" x1="886" x2="879" y1="-3874" y2="-3874"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.25" x1="884" x2="881" y1="-3871" y2="-3871"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="882" x2="882" y1="-3928" y2="-3893"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="963" x2="927" y1="-4565" y2="-4565"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="0" fill="none" points="715,-4854 726,-4847 726,-4860 715,-4854 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="698,-4854 687,-4860 687,-4847 698,-4854 " stroke="rgb(255,255,0)"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="0" id="SW-12063">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2042.063492 -4327.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1800" ObjectName="SW-CX_HGY.CX_HGY_381BK"/>
     <cge:Meas_Ref ObjectId="12063"/>
    <cge:TPSR_Ref TObjectID="1800"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-11387">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1694.000000 -4334.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1774" ObjectName="SW-CX_HGY.CX_HGY_312BK"/>
     <cge:Meas_Ref ObjectId="11387"/>
    <cge:TPSR_Ref TObjectID="1774"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-11977">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1459.000000 -4329.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1794" ObjectName="SW-CX_HGY.CX_HGY_372BK"/>
     <cge:Meas_Ref ObjectId="11977"/>
    <cge:TPSR_Ref TObjectID="1794"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1424.000000 -4592.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1491.000000 -4591.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-12020">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1270.000000 -4315.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1797" ObjectName="SW-CX_HGY.CX_HGY_371BK"/>
     <cge:Meas_Ref ObjectId="12020"/>
    <cge:TPSR_Ref TObjectID="1797"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-19286">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 551.000000 -5074.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2950" ObjectName="SW-CX_YR.CX_YR_362BK"/>
     <cge:Meas_Ref ObjectId="19286"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 990.000000 -4461.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-11932">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 902.000000 -4306.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1791" ObjectName="SW-CX_HGY.CX_HGY_373BK"/>
     <cge:Meas_Ref ObjectId="11932"/>
    <cge:TPSR_Ref TObjectID="1791"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-171109">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1061.000000 -4763.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27248" ObjectName="SW-YM_WM.YM_WM_371BK"/>
     <cge:Meas_Ref ObjectId="171109"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-12107">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1958.000000 -4152.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1803" ObjectName="SW-CX_HGY.CX_HGY_382BK"/>
     <cge:Meas_Ref ObjectId="12107"/>
    <cge:TPSR_Ref TObjectID="1803"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-188043">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1360.000000 -3536.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28639" ObjectName="SW-YM_NY.YM_NY_331BK"/>
     <cge:Meas_Ref ObjectId="188043"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-188057">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1211.000000 -3539.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28643" ObjectName="SW-YM_NY.YM_NY_332BK"/>
     <cge:Meas_Ref ObjectId="188057"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1505.500000 -3881.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1836.500000 -3644.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1493.500000 -3758.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1599.500000 -3758.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166724">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1075.000000 -4064.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27019" ObjectName="SW-YM_LC.YM_LC_351BK"/>
     <cge:Meas_Ref ObjectId="166724"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166743">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 888.000000 -4064.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27023" ObjectName="SW-YM_LC.YM_LC_352BK"/>
     <cge:Meas_Ref ObjectId="166743"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 656.000000 -4017.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 480.000000 -3913.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-215558">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 899.000000 -3582.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32077" ObjectName="SW-YM_XH.YM_XH_351BK"/>
     <cge:Meas_Ref ObjectId="215558"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-61876">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 715.000000 -4896.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11811" ObjectName="SW-CX_DLTY.CX_DLTY_381BK"/>
     <cge:Meas_Ref ObjectId="61876"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-77199">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2249.855346 -4159.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16784" ObjectName="SW-CX_HGY.CX_HGY_384BK"/>
     <cge:Meas_Ref ObjectId="77199"/>
    <cge:TPSR_Ref TObjectID="16784"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-77350">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2249.855346 -3834.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16799" ObjectName="SW-CX_TZS.CX_TZS_354BK"/>
     <cge:Meas_Ref ObjectId="77350"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-77230">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2243.774184 -4315.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16787" ObjectName="SW-CX_HGY.CX_HGY_383BK"/>
     <cge:Meas_Ref ObjectId="77230"/>
    <cge:TPSR_Ref TObjectID="16787"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-83653">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2242.210692 -4632.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18439" ObjectName="SW-CX_DLC.CX_DLC_391BK"/>
     <cge:Meas_Ref ObjectId="83653"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-87817">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 726.000000 -3386.000000)" xlink:href="#breaker2:shape9_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19032" ObjectName="SW-CX_XXC.CX_XXC_351BK"/>
     <cge:Meas_Ref ObjectId="87817"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166075">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 230.000000 -3637.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26905" ObjectName="SW-CX_SF.CX_SF_364BK"/>
     <cge:Meas_Ref ObjectId="166075"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166033">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 228.000000 -3293.822257)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26895" ObjectName="SW-CX_SF.CX_SF_362BK"/>
     <cge:Meas_Ref ObjectId="166033"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166054">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 231.000000 -3437.917827)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26900" ObjectName="SW-CX_SF.CX_SF_363BK"/>
     <cge:Meas_Ref ObjectId="166054"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166262">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 195.000000 -3506.424326)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26946" ObjectName="SW-CX_SF.CX_SF_312BK"/>
     <cge:Meas_Ref ObjectId="166262"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166096">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 228.000000 -3790.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26910" ObjectName="SW-CX_SF.CX_SF_365BK"/>
     <cge:Meas_Ref ObjectId="166096"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-196756">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1008.000000 -4649.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30131" ObjectName="SW-YM_WM.YM_WM_373BK"/>
     <cge:Meas_Ref ObjectId="196756"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-196899">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 551.000000 -4319.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30177" ObjectName="SW-CX_PTS.CX_PTS_351BK"/>
     <cge:Meas_Ref ObjectId="196899"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-171130">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1042.000000 -4571.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27254" ObjectName="SW-YM_WM.YM_WM_372BK"/>
     <cge:Meas_Ref ObjectId="171130"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-199022">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 649.000000 -3582.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30411" ObjectName="SW-YM_PT.YM_PT_332BK"/>
     <cge:Meas_Ref ObjectId="199022"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-170310">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1766.000000 -4885.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27187" ObjectName="SW-YM_JY.YM_JY_361BK"/>
     <cge:Meas_Ref ObjectId="170310"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-225670">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1270.000000 -5075.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37497" ObjectName="SW-YM_JB.YM_JB_341BK"/>
     <cge:Meas_Ref ObjectId="225670"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-225697">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1465.000000 -5073.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37498" ObjectName="SW-YM_JB.YM_JB_342BK"/>
     <cge:Meas_Ref ObjectId="225697"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="0" id="g_3b58e30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2207.710692 -4341.000000)" xlink:href="#voltageTransformer:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b5a420">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2060.000000 -4364.000000)" xlink:href="#voltageTransformer:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b7a630">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 448.000000 -3765.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b859a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 450.000000 -3599.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b8c7b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 535.000000 -3253.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3ba5e30">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 427.000000 -3398.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3ce21d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1305.000000 -4832.000000)" xlink:href="#voltageTransformer:shape80"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3ce8930">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1544.000000 -4952.000000)" xlink:href="#voltageTransformer:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3cea1c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1658.000000 -4800.000000)" xlink:href="#voltageTransformer:shape140"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1408.000000 -4644.000000)" xlink:href="#transformer2:shape1_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1408.000000 -4644.000000)" xlink:href="#transformer2:shape1_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1475.000000 -4644.000000)" xlink:href="#transformer2:shape1_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1475.000000 -4644.000000)" xlink:href="#transformer2:shape1_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 421.000000 -3453.000000)" xlink:href="#transformer2:shape20_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 421.000000 -3453.000000)" xlink:href="#transformer2:shape20_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.680000 -0.000000 0.000000 -0.688889 1022.000000 -4473.000000)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.680000 -0.000000 0.000000 -0.688889 1022.000000 -4473.000000)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1993.000000 -3882.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1993.000000 -3882.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 373.000000 -4032.000000)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 373.000000 -4032.000000)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 464.000000 -4028.000000)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 464.000000 -4028.000000)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1477.000000 -3583.000000)" xlink:href="#transformer2:shape43_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1477.000000 -3583.000000)" xlink:href="#transformer2:shape43_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1584.000000 -3581.000000)" xlink:href="#transformer2:shape43_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1584.000000 -3581.000000)" xlink:href="#transformer2:shape43_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1822.000000 -3542.000000)" xlink:href="#transformer2:shape43_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1822.000000 -3542.000000)" xlink:href="#transformer2:shape43_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-YM_XH.YM_XH_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="46378"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1051.000000 -3605.000000)" xlink:href="#transformer2:shape2_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1051.000000 -3605.000000)" xlink:href="#transformer2:shape2_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="32139" ObjectName="TF-YM_XH.YM_XH_1T"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1133.000000 -3377.000000)" xlink:href="#transformer2:shape32_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1133.000000 -3377.000000)" xlink:href="#transformer2:shape32_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="0" id="g_22905b0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2036.063492 -4431.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_2be2bf0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1455.000000 -4424.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_2b65a40">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1517.000000 -4395.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_38119e0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1263.000000 -4413.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_35b04c0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1329.000000 -4351.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_185e5d0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1335.000000 -3408.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_1861b60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1340.000000 -3394.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3a82140">
    <use class="BV-35KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 1247.000000 -3405.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3a9acb0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1433.000000 -3883.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3a9b9f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1428.000000 -3858.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3ab0cd0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1803.000000 -3631.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3ab1a80">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1780.000000 -3590.000000)" xlink:href="#lightningRod:shape81"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3ab29b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1780.000000 -3637.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3ad87f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1095.000000 -4000.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3add140">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1086.000000 -3935.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3ae5010">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 578.000000 -3717.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3aecf30">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 679.000000 -4136.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3af4880">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 625.000000 -3842.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3af5630">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 602.000000 -3850.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b07810">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 -0.000000 -1.000000 443.000000 -3697.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b0e8c0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 882.500000 -3608.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b0f3a0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 892.000000 -3629.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b0fae0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 846.000000 -3632.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b34110">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1948.000000 -4010.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b383e0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 540.000000 -4970.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b38920">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 519.000000 -4971.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b5ba10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2266.710692 -4410.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b66fd0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2237.774184 -4607.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b73330">
    <use class="BV-0KV" transform="matrix(-0.826087 -0.000000 -0.000000 0.829268 959.000000 -4330.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b73a30">
    <use class="BV-35KV" transform="matrix(0.000000 -0.826087 -0.731707 -0.000000 979.860310 -4542.021739)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b79900">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 327.000000 -3728.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b84c70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 322.000000 -3574.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b8ba80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 377.000000 -3235.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3ba5100">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 324.000000 -3372.917827)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3bd73d0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 730.500000 -4863.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3be4360">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 622.000000 -3847.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3bef6c0">
    <use class="BV-35KV" transform="matrix(0.000000 -0.826087 -0.731707 -0.000000 984.860310 -4737.021739)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3bf96a0">
    <use class="BV-35KV" transform="matrix(0.000000 -0.826087 -0.731707 -0.000000 946.860310 -4623.021739)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3bfeb20">
    <use class="BV-35KV" transform="matrix(0.000000 -0.953488 -0.850000 -0.000000 568.575000 -4476.476744)" xlink:href="#lightningRod:shape192"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3bff5a0">
    <use class="BV-0KV" transform="matrix(0.000000 -0.896739 -0.710801 -0.000000 593.644599 -4448.470109)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3bffe20">
    <use class="BV-0KV" transform="matrix(-0.000000 0.896739 0.710801 0.000000 596.000000 -4466.750000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3c00cb0">
    <use class="BV-0KV" transform="matrix(-0.000000 0.769231 0.734375 0.000000 500.367188 -4438.384615)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3c46050">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 896.000000 -4392.166667)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3cc6750">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1353.000000 -4895.000000)" xlink:href="#lightningRod:shape210"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3cdea20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1783.000000 -4833.000000)" xlink:href="#lightningRod:shape211"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="0" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -117.000000 -5164.013514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="0" busDevId="0" cx="1468" cy="-4524" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="1516" cy="-3840" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="1503" cy="-3840" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="1609" cy="-3840" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="489" cy="-3975" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="1433" cy="-4524" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="1500" cy="-4524" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="27015" cx="1084" cy="-4169" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="27015" cx="897" cy="-4169" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="489" cy="-3975" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="16797" cx="2259" cy="-3764" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="18426" cx="2253" cy="-4746" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="398" cy="-3975" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="30175" cx="560" cy="-4246" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="2891" cx="560" cy="-5176" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="11821" cx="817" cy="-4906" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="28262" cx="764" cy="-4854" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="0" cx="2051" cy="-4736" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="27244" cx="1180" cy="-4659" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="27244" cx="1180" cy="-4581" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="1727" cx="2051" cy="-4258" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="1727" cx="1764" cy="-4258" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="1727" cx="1967" cy="-4258" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="1727" cx="2259" cy="-4258" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="1727" cx="2253" cy="-4258" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="1726" cx="1675" cy="-4258" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="1726" cx="1468" cy="-4258" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="1726" cx="1279" cy="-4258" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="1726" cx="911" cy="-4258" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="28722" cx="1220" cy="-3642" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="19030" cx="837" cy="-3396" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="28722" cx="1369" cy="-3642" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="26859" cx="148" cy="-3800" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="26859" cx="148" cy="-3647" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="26859" cx="148" cy="-3552" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="26963" cx="148" cy="-3447" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="26963" cx="148" cy="-3502" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="26963" cx="148" cy="-3303" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="30408" cx="573" cy="-3647" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="27184" cx="1886" cy="-4895" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="27184" cx="1886" cy="-4843" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="27088" cx="1279" cy="-5177" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="27088" cx="1474" cy="-5177" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="27244" cx="1180" cy="-4773" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="30408" cx="573" cy="-3592" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1496.000000 -4518.000000) translate(0,17)">虎跳滩电站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1388.000000 -4623.000000) translate(0,17)">301</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1515.000000 -4625.000000) translate(0,17)">302</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1407.000000 -4550.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1519.000000 -4555.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1400.000000 -4758.000000) translate(0,17)">1号变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1480.000000 -4758.000000) translate(0,17)">2号变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 969.000000 -4441.000000) translate(0,13)">黄物T线虎跳滩二级支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1059.000000 -4514.000000) translate(0,11)">虎跳滩二级电站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1059.000000 -4514.000000) translate(0,24)">1号变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 996.000000 -4463.000000) translate(0,17)">321</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 946.000000 -4511.000000) translate(0,17)">32110</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 967.000000 -4468.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1148.000000 -4774.000000) translate(0,12)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 497.000000 -5204.000000) translate(0,17)">110kV永仁变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1280.000000 -4241.000000) translate(0,17)">110kV黄瓜园变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1663.000000 -4249.000000) translate(0,17)">Ⅰ母</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1738.000000 -4249.000000) translate(0,17)">Ⅱ母</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1700.000000 -4374.000000) translate(0,17)">312</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2033.000000 -4390.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2009.000000 -4361.000000) translate(0,17)">381</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2032.000000 -4312.000000) translate(0,17)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1996.000000 -4691.000000) translate(0,17)">3923</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2016.000000 -4778.000000) translate(0,17)">元谋水泥</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2016.000000 -4778.000000) translate(0,38)">二分厂</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 947.000000 -4198.000000) translate(0,16)">老城变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1134.000000 -4056.000000) translate(0,16)">凤老小线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1526.000000 -3912.000000) translate(0,15)">359</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1563.000000 -3867.000000) translate(0,16)">小河口电站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1512.000000 -3789.000000) translate(0,15)">301</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1619.000000 -3789.000000) translate(0,15)">302</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1545.000000 -3699.000000) translate(0,15)">3010</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1648.000000 -3702.000000) translate(0,15)">3020</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1589.000000 -3941.000000) translate(0,15)">3597</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1789.000000 -3873.000000) translate(0,15)">30167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1788.000000 -3782.000000) translate(0,15)">30160</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1800.000000 -3827.000000) translate(0,15)">3016</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1857.000000 -3676.000000) translate(0,16)">301</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1481.000000 -3582.000000) translate(0,16)">1号变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1589.000000 -3580.000000) translate(0,16)">2号变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1711.000000 -3675.000000) translate(0,15)">39017</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1794.000000 -3736.000000) translate(0,15)">3901</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1274.000000 -3666.000000) translate(0,14)">能禹变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 381.000000 -3667.000000) translate(0,14)">哨平线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 343.000000 -3824.000000) translate(0,14)">哨老麻丙线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 378.000000 -3965.000000) translate(0,14)">丙间电站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 565.000000 -3988.000000) translate(0,14)">麻柳电站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 683.000000 -3961.000000) translate(0,14)">3420</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -3889.000000) translate(0,14)">3425</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 560.000000 -3950.000000) translate(0,14)">3424</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 617.000000 -4007.000000) translate(0,14)">3421</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 624.000000 -4047.000000) translate(0,14)">342</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 613.000000 -4095.000000) translate(0,14)">3422</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 500.000000 -3942.000000) translate(0,14)">301</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 494.000000 -3887.000000) translate(0,14)">3011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 408.000000 -4040.000000) translate(0,14)">1号变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 500.000000 -4037.000000) translate(0,14)">2号变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 526.000000 -3466.000000) translate(0,14)">哨能线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1444.000000 -3324.000000) translate(0,14)">哨黄能线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 526.000000 -3668.000000) translate(0,10)">3436</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 912.000000 -3566.000000) translate(0,14)">平新线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1031.000000 -3625.000000) translate(0,15)">新华变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 850.000000 -3566.000000) translate(0,10)">35167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3153350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3153350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,38)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3153350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3153350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,80)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3153350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3153350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,122)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3153350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -5114.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3056e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3056e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,38)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3056e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,59)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3056e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,80)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3056e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,101)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3056e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,122)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3056e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,143)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3056e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,164)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3056e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,185)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3056e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,206)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3056e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,227)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3056e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,248)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3056e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,269)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3056e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,290)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3056e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,311)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3056e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,332)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3056e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,353)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3056e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -4174.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b203a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 855.000000 -4254.000000) translate(0,17)">IM段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b209d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1700.000000 -4374.000000) translate(0,17)">312</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b20c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1682.000000 -4312.000000) translate(0,17)">3121</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b20e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1766.000000 -4312.000000) translate(0,17)">3122</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b21090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1424.000000 -4361.000000) translate(0,17)">372</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b212d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1412.000000 -4405.000000) translate(0,17)">3726</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b21510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1412.000000 -4302.000000) translate(0,17)">3721</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b21750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1234.000000 -4348.000000) translate(0,17)">371</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b21990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1220.000000 -4396.000000) translate(0,17)">3716</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b21bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1218.000000 -4299.000000) translate(0,17)">3711</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b21e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 866.000000 -4339.000000) translate(0,17)">373</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b22050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 855.000000 -4379.000000) translate(0,17)">3736</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b22290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 851.000000 -4297.000000) translate(0,17)">3731</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b224d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 588.000000 -5168.000000) translate(0,17)">Ⅰ段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b22710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 514.000000 -5106.000000) translate(0,17)">362</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b22950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 502.000000 -5058.000000) translate(0,17)">3626</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b22b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 502.000000 -5156.000000) translate(0,17)">3621</text>
   <text DF8003:Layer="0" fill="rgb(38,38,38)" font-family="SimHei" font-size="20" graphid="g_3b26db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -86.000000 -5212.000000) translate(0,16)">元谋片区35kV电网图</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3b28160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 630.000000 -4947.000000) translate(0,13)">38167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b28840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 672.000000 -4932.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b28cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 720.000000 -4935.000000) translate(0,17)">381</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b30270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 825.000000 -4955.000000) translate(0,16)">多</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b30270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 825.000000 -4955.000000) translate(0,36)">凌</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b30270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 825.000000 -4955.000000) translate(0,56)">钛</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b30270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 825.000000 -4955.000000) translate(0,76)">业</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b30270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 825.000000 -4955.000000) translate(0,96)">变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3b30770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1976.000000 -4181.000000) translate(0,14)">382</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3b30c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1974.000000 -4227.000000) translate(0,14)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3b30ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1972.000000 -4128.000000) translate(0,14)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b37b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1983.000000 -3947.000000) translate(0,17)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b37b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1983.000000 -3947.000000) translate(0,38)">号</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b37b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1983.000000 -3947.000000) translate(0,59)">站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b37b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1983.000000 -3947.000000) translate(0,80)">用</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b37b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1983.000000 -3947.000000) translate(0,101)">变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3b381a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1972.000000 -4065.000000) translate(0,14)">哨</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3b381a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1972.000000 -4065.000000) translate(0,31)">黄</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3b381a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1972.000000 -4065.000000) translate(0,48)">能</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3b381a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1972.000000 -4065.000000) translate(0,65)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3b40e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2267.855346 -4183.000000) translate(0,14)">384</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3b41310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2265.855346 -4229.000000) translate(0,14)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3b41550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2263.855346 -4130.000000) translate(0,14)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3b48880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2267.855346 -3860.000000) translate(0,14)">354</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3b48eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2265.855346 -3906.000000) translate(0,14)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3b490f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2263.855346 -3807.000000) translate(0,14)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b4c810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2230.000000 -3757.000000) translate(0,17)">天子山</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b4c810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2230.000000 -3757.000000) translate(0,38)">开关站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3b4fed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2274.000000 -3954.000000) translate(0,14)">35467</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b510e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2232.000000 -4051.000000) translate(0,16)">天</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b510e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2232.000000 -4051.000000) translate(0,36)">子</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b510e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2232.000000 -4051.000000) translate(0,56)">山</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b510e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2232.000000 -4051.000000) translate(0,76)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3b6af30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2214.000000 -4772.000000) translate(0,17)">大罗岔变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b6b560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2264.710692 -4693.000000) translate(0,12)">391</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b6bb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2210.710692 -4593.000000) translate(0,12)">3916</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b6bde0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2267.710692 -4571.000000) translate(0,12)">39167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b6c020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2261.710692 -4344.000000) translate(0,12)">383</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b6c260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2259.710692 -4387.000000) translate(0,12)">3836</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b6c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2259.710692 -4301.000000) translate(0,12)">3832</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b6c8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2228.000000 -4529.000000) translate(0,16)">铁</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b6c8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2228.000000 -4529.000000) translate(0,36)">路</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b6c8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2228.000000 -4529.000000) translate(0,56)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b71760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 578.000000 -3674.000000) translate(0,16)">平</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b71760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 578.000000 -3674.000000) translate(0,36)">田</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b71760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 578.000000 -3674.000000) translate(0,56)">变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3b71cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 696.000000 -3436.000000) translate(0,11)">小</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3b71cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 696.000000 -3436.000000) translate(0,24)">西</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3b71cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 696.000000 -3436.000000) translate(0,37)">村</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3b71cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 696.000000 -3436.000000) translate(0,50)">T</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3b71cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 696.000000 -3436.000000) translate(0,63)">接线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b71f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 767.000000 -3419.000000) translate(0,12)">351</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_3b747e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 988.000000 -4557.000000) translate(0,10)">线路TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b74cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 568.000000 -5042.000000) translate(0,16)">永</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b74cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 568.000000 -5042.000000) translate(0,36)">物</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b74cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 568.000000 -5042.000000) translate(0,56)">T</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3b74cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 568.000000 -5042.000000) translate(0,76)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3b74f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 675.000000 -4895.000000) translate(0,11)">永物T线多凌钛业支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3bb2c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4.000000 -3645.000000) translate(0,17)">110kV哨房变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3bb3290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 60.000000 -3393.000000) translate(0,17)">35kVI母</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3bb34d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 104.000000 -3857.000000) translate(0,17)">35kVII母</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bc1bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 241.000000 -3823.000000) translate(0,12)">365</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bc20a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 188.000000 -3824.000000) translate(0,12)">3652</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bc22e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 297.000000 -3825.000000) translate(0,12)">3656</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bc2520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 229.000000 -3775.000000) translate(0,12)">36560</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bc2950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 240.000000 -3671.000000) translate(0,12)">364</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bc2de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 183.000000 -3671.000000) translate(0,12)">3642</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bc3240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 298.000000 -3670.000000) translate(0,12)">3646</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bc3480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 228.000000 -3614.000000) translate(0,12)">36460</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bc36c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 363.000000 -3602.000000) translate(0,12)">36467</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bc3900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 213.000000 -3534.000000) translate(0,12)">312</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bc3b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 159.000000 -3576.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bc3d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 158.000000 -3496.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bc3fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 240.000000 -3467.000000) translate(0,12)">363</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bc4200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 198.000000 -3472.000000) translate(0,12)">3631</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bc4440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 308.000000 -3472.000000) translate(0,12)">3636</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bc4680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 230.000000 -3411.000000) translate(0,12)">36360</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bc48c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 365.000000 -3396.000000) translate(0,12)">36367</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bc4b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 238.000000 -3327.000000) translate(0,12)">362</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bc4d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 307.000000 -3328.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bc4f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 195.000000 -3328.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bc51c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 287.000000 -3269.000000) translate(0,12)">36260</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bc5400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 429.000000 -3258.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bd6da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 605.000000 -4880.000000) translate(0,12)">3516</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3bd8410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 770.000000 -4849.000000) translate(0,16)">提灌变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3bd8900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 627.000000 -4844.000000) translate(0,11)">永物T线提灌支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3bd8b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 431.000000 -3435.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3bd8d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 416.000000 -3517.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3be1c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2270.710692 -4633.000000) translate(0,12)">39160</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bee570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1071.000000 -4797.000000) translate(0,12)">371</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3beeba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1005.000000 -4776.000000) translate(0,12)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_3bf0640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 995.000000 -4753.000000) translate(0,10)">线路TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_3bfa6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 957.000000 -4639.000000) translate(0,10)">线路TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_3c007c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 589.000000 -4477.000000) translate(0,10)">TV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c02040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 576.000000 -4433.000000) translate(0,12)">35167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3c07af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 518.000000 -4351.000000) translate(0,16)">351</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_3c08030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 514.000000 -4239.000000) translate(0,13)">坡头山开关站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3c08270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 822.000000 -4350.000000) translate(0,16)">坡</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3c08270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 822.000000 -4350.000000) translate(0,36)">物</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3c08270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 822.000000 -4350.000000) translate(0,56)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c08600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 522.000000 -4266.000000) translate(0,12)">IM段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c166a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 365.000000 -3747.000000) translate(0,12)">36567</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c16cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 511.000000 -3829.000000) translate(0,12)">#1塔</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c1de50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1124.000000 -4607.000000) translate(0,12)">3721</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c1e340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1052.000000 -4605.000000) translate(0,12)">372</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c1e580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 976.000000 -4607.000000) translate(0,12)">3726</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c21c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 943.000000 -4816.000000) translate(0,12)">37167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c25390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 902.000000 -4691.000000) translate(0,12)">37367</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c25880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1137.000000 -4685.000000) translate(0,12)">3731</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c25ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1084.000000 -4685.000000) translate(0,12)">3732</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c25d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1018.000000 -4683.000000) translate(0,12)">373</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c25f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 963.000000 -4685.000000) translate(0,12)">3736</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c26180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1168.000000 -4825.000000) translate(0,12)">Ⅰ段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c275c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 845.000000 -4189.000000) translate(0,12)">IM段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c27800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 904.000000 -4143.000000) translate(0,12)">3521</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c27a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 907.000000 -4093.000000) translate(0,12)">352</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c27c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 904.000000 -4043.000000) translate(0,12)">3526</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c27ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 840.000000 -3976.000000) translate(0,12)">35267</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c28100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1091.000000 -4143.000000) translate(0,12)">3511</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c28340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1094.000000 -4093.000000) translate(0,12)">351</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c28580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1091.000000 -4043.000000) translate(0,12)">3516</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c287c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1027.000000 -4032.000000) translate(0,12)">35167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c28a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1227.000000 -3618.000000) translate(0,12)">3321</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c28c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1227.000000 -3520.000000) translate(0,12)">3326</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c28e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1229.000000 -3568.000000) translate(0,12)">332</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c290c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1160.000000 -3490.000000) translate(0,12)">33267</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c29300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1376.000000 -3615.000000) translate(0,12)">3311</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c29540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1378.000000 -3565.000000) translate(0,12)">331</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c29780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1382.000000 -3474.000000) translate(0,12)">33167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c299c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1376.000000 -3517.000000) translate(0,12)">3316</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c29c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1192.000000 -3662.000000) translate(0,12)">Ⅰ段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" graphid="g_3c29e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 520.000000 -3636.000000) translate(0,11)">34367</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c2a8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 720.000000 -3443.000000) translate(0,12)">#7塔</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c3ed30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 659.000000 -3616.000000) translate(0,12)">332</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c3f360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 602.000000 -3613.000000) translate(0,12)">3321</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c3f5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 610.000000 -3571.000000) translate(0,12)">33217</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c3f7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 652.000000 -3652.000000) translate(0,12)">33260</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c3fa20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 717.000000 -3618.000000) translate(0,12)">3326</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c3fc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 701.000000 -3566.000000) translate(0,12)">33267</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_3c40280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 362.000000 -3327.000000) translate(0,14)">哨黄能线</text>
   <text DF8003:Layer="0" fill="rgb(0,0,0)" font-family="SimSun" font-size="25" graphid="g_3c444f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 152.000000 -5216.000000) translate(0,20)">配调返回</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c45420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 909.000000 -3585.000000) translate(0,12)">351</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c45750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 860.000000 -3588.000000) translate(0,12)">3516</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c45990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 952.000000 -3586.000000) translate(0,12)">3511</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c45bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1002.000000 -3587.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c45e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2317.000000 -4378.000000) translate(0,12)">3819</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3cb9b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1895.000000 -4897.000000) translate(0,16)">姜</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3cb9b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1895.000000 -4897.000000) translate(0,36)">驿</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3cb9b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1895.000000 -4897.000000) translate(0,56)">变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3cb9d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1352.000000 -5204.000000) translate(0,17)">江边变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cc5470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1830.000000 -4921.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cc56a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1776.000000 -4919.000000) translate(0,12)">361</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cc58c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1727.000000 -4921.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cc5ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1655.000000 -4931.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cc5d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1891.000000 -4932.000000) translate(0,12)">Ⅰ段</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cc7e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1345.000000 -4895.000000) translate(0,12)">1号站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cc7e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1345.000000 -4895.000000) translate(0,27)">用变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3cde040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1549.000000 -4892.000000) translate(0,17)">边驿线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_3ce00e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1800.000000 -4832.000000) translate(0,16)">站用变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ce0570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1290.000000 -5104.000000) translate(0,12)">341</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ce07a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1288.000000 -5152.000000) translate(0,12)">3411</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ce09c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1288.000000 -5059.000000) translate(0,12)">3416</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ce0be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1295.000000 -5028.000000) translate(0,12)">34167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ce0e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1485.000000 -5102.000000) translate(0,12)">342</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ce1020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1483.000000 -5150.000000) translate(0,12)">3421</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ce1240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1483.000000 -5057.000000) translate(0,12)">3426</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ce1460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1488.000000 -5029.000000) translate(0,12)">34267</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ce8350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1317.000000 -4943.000000) translate(0,12)">3419</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ce95f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1501.000000 -4986.000000) translate(0,12)">3429</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cebec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1321.000000 -4851.000000) translate(0,12)">电压互感器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cec350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1645.000000 -4798.000000) translate(0,12)">电压互感器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cec570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1555.000000 -4987.000000) translate(0,12)">电压互感器</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1283.000000 -4732.000000) translate(0,16)">黄</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1283.000000 -4732.000000) translate(0,36)">江</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1283.000000 -4732.000000) translate(0,56)">姜</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1283.000000 -4732.000000) translate(0,76)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1432.000000 -4513.000000) translate(0,10)">黄虎线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 890.000000 -4526.000000) translate(0,16)">黄</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 890.000000 -4526.000000) translate(0,36)">物</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 890.000000 -4526.000000) translate(0,56)">T</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 890.000000 -4526.000000) translate(0,76)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1184.000000 -4746.000000) translate(0,16)">物</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1184.000000 -4746.000000) translate(0,36)">茂</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1184.000000 -4746.000000) translate(0,56)">变</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2057.000000 -4689.000000) translate(0,13)">黄</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2057.000000 -4689.000000) translate(0,29)">水</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2057.000000 -4689.000000) translate(0,45)">线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1867.000000 -3780.000000) translate(0,16)">凤</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1867.000000 -3780.000000) translate(0,35)">凰</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1867.000000 -3780.000000) translate(0,54)">箐</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1867.000000 -3780.000000) translate(0,73)">电</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1867.000000 -3780.000000) translate(0,92)">站</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 843.000000 -3432.000000) translate(0,11)">小</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 843.000000 -3432.000000) translate(0,24)">西</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 843.000000 -3432.000000) translate(0,37)">村</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 843.000000 -3432.000000) translate(0,50)">光</text>
   <text DF8003:Layer="图层2" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 843.000000 -3432.000000) translate(0,63)">伏</text>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="921,-3923 943,-3901 943,-3889 " stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2042.063492 -4663.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-12067">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2042.063492 -4376.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1802" ObjectName="SW-CX_HGY.CX_HGY_3816SW"/>
     <cge:Meas_Ref ObjectId="12067"/>
    <cge:TPSR_Ref TObjectID="1802"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-12065">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2042.063492 -4281.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1801" ObjectName="SW-CX_HGY.CX_HGY_3812SW"/>
     <cge:Meas_Ref ObjectId="12065"/>
    <cge:TPSR_Ref TObjectID="1801"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-11389">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1666.000000 -4279.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1775" ObjectName="SW-CX_HGY.CX_HGY_3121SW"/>
     <cge:Meas_Ref ObjectId="11389"/>
    <cge:TPSR_Ref TObjectID="1775"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-11390">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1755.000000 -4279.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1776" ObjectName="SW-CX_HGY.CX_HGY_3122SW"/>
     <cge:Meas_Ref ObjectId="11390"/>
    <cge:TPSR_Ref TObjectID="1776"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1500.000000 -4401.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-11981">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1459.000000 -4372.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1796" ObjectName="SW-CX_HGY.CX_HGY_3726SW"/>
     <cge:Meas_Ref ObjectId="11981"/>
    <cge:TPSR_Ref TObjectID="1796"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-11979">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1459.000000 -4269.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1795" ObjectName="SW-CX_HGY.CX_HGY_3721SW"/>
     <cge:Meas_Ref ObjectId="11979"/>
    <cge:TPSR_Ref TObjectID="1795"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1424.000000 -4534.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1491.000000 -4537.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1311.000000 -4356.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-12023">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1270.000000 -4364.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1799" ObjectName="SW-CX_HGY.CX_HGY_3716SW"/>
     <cge:Meas_Ref ObjectId="12023"/>
    <cge:TPSR_Ref TObjectID="1799"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-12021">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1270.000000 -4266.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1798" ObjectName="SW-CX_HGY.CX_HGY_3711SW"/>
     <cge:Meas_Ref ObjectId="12021"/>
    <cge:TPSR_Ref TObjectID="1798"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-19284">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 551.000000 -5123.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2948" ObjectName="SW-CX_YR.CX_YR_3621SW"/>
     <cge:Meas_Ref ObjectId="19284"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-19285">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 551.000000 -5025.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2949" ObjectName="SW-CX_YR.CX_YR_3626SW"/>
     <cge:Meas_Ref ObjectId="19285"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-11936">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 902.000000 -4346.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1793" ObjectName="SW-CX_HGY.CX_HGY_3736SW"/>
     <cge:Meas_Ref ObjectId="11936"/>
    <cge:TPSR_Ref TObjectID="1793"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-11934">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 902.000000 -4264.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1792" ObjectName="SW-CX_HGY.CX_HGY_3731SW"/>
     <cge:Meas_Ref ObjectId="11934"/>
    <cge:TPSR_Ref TObjectID="1792"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 943.000000 -4336.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 949.000000 -4466.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 933.000000 -4479.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-171111">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 987.000000 -4768.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27250" ObjectName="SW-YM_WM.YM_WM_3716SW"/>
     <cge:Meas_Ref ObjectId="171111"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-171110">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1130.000000 -4768.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27249" ObjectName="SW-YM_WM.YM_WM_3711SW"/>
     <cge:Meas_Ref ObjectId="171110"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-12109">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1958.000000 -4197.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1804" ObjectName="SW-CX_HGY.CX_HGY_3822SW"/>
     <cge:Meas_Ref ObjectId="12109"/>
    <cge:TPSR_Ref TObjectID="1804"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-12111">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1958.000000 -4098.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="1805" ObjectName="SW-CX_HGY.CX_HGY_3826SW"/>
     <cge:Meas_Ref ObjectId="12111"/>
    <cge:TPSR_Ref TObjectID="1805"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166037">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 410.000000 -3261.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26899" ObjectName="SW-CX_SF.CX_SF_36267SW"/>
     <cge:Meas_Ref ObjectId="166037"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-188044">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1360.000000 -3585.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28640" ObjectName="SW-YM_NY.YM_NY_3311SW"/>
     <cge:Meas_Ref ObjectId="188044"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-188045">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1360.000000 -3487.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28641" ObjectName="SW-YM_NY.YM_NY_3316SW"/>
     <cge:Meas_Ref ObjectId="188045"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1317.000000 -3413.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-188059">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1211.000000 -3490.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28645" ObjectName="SW-YM_NY.YM_NY_3326SW"/>
     <cge:Meas_Ref ObjectId="188059"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-188058">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1211.000000 -3588.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28644" ObjectName="SW-YM_NY.YM_NY_3321SW"/>
     <cge:Meas_Ref ObjectId="188058"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-188060">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1158.000000 -3459.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28646" ObjectName="SW-YM_NY.YM_NY_33267SW"/>
     <cge:Meas_Ref ObjectId="188060"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1505.000000 -3928.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1505.000000 -3847.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1432.000000 -3927.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1574.000000 -3911.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1787.000000 -3837.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1837.000000 -3787.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1791.000000 -3779.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1780.000000 -3704.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1761.000000 -3648.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1493.000000 -3805.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1493.000000 -3724.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1531.000000 -3667.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1599.000000 -3805.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1599.000000 -3724.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1637.000000 -3667.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166726">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1075.000000 -4113.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27020" ObjectName="SW-YM_LC.YM_LC_3511SW"/>
     <cge:Meas_Ref ObjectId="166726"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166727">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1075.000000 -4013.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27021" ObjectName="SW-YM_LC.YM_LC_3516SW"/>
     <cge:Meas_Ref ObjectId="166727"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166728">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1024.000000 -4001.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27022" ObjectName="SW-YM_LC.YM_LC_35167SW"/>
     <cge:Meas_Ref ObjectId="166728"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1068.000000 -3940.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166745">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 888.000000 -4113.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27024" ObjectName="SW-YM_LC.YM_LC_3521SW"/>
     <cge:Meas_Ref ObjectId="166745"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166746">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 888.000000 -4013.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27025" ObjectName="SW-YM_LC.YM_LC_3526SW"/>
     <cge:Meas_Ref ObjectId="166746"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 656.000000 -4063.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 656.000000 -3972.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 680.000000 -3963.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 602.000000 -3919.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 583.000000 -3860.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 480.000000 -3982.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 480.000000 -3858.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 448.000000 -3853.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 520.000000 -3710.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-199067">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 504.000000 -3606.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30418" ObjectName="SW-YM_PT.YM_PT_34367SW"/>
     <cge:Meas_Ref ObjectId="199067"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-215562">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 836.000000 -3538.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32081" ObjectName="SW-YM_XH.YM_XH_35167SW"/>
     <cge:Meas_Ref ObjectId="215562"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-199066">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 518.000000 -3642.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30417" ObjectName="SW-YM_PT.YM_PT_3436SW"/>
     <cge:Meas_Ref ObjectId="199066"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-215560">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 944.000000 -3587.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32079" ObjectName="SW-YM_XH.YM_XH_3511SW"/>
     <cge:Meas_Ref ObjectId="215560"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-215559">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 853.000000 -3587.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32078" ObjectName="SW-YM_XH.YM_XH_3516SW"/>
     <cge:Meas_Ref ObjectId="215559"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-215572">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 994.000000 -3587.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32083" ObjectName="SW-YM_XH.YM_XH_3011SW"/>
     <cge:Meas_Ref ObjectId="215572"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-188046">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1397.000000 -3407.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28642" ObjectName="SW-YM_NY.YM_NY_33167SW"/>
     <cge:Meas_Ref ObjectId="188046"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-61724">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 617.000000 -4915.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11813" ObjectName="SW-CX_DLTY.CX_DLTY_38167SW"/>
     <cge:Meas_Ref ObjectId="61724"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-61722">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 652.000000 -4901.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11812" ObjectName="SW-CX_DLTY.CX_DLTY_3816SW"/>
     <cge:Meas_Ref ObjectId="61722"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1930.000000 -4015.000000)" xlink:href="#switch2:shape19_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2003.000000 -4014.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-77202">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2249.855346 -4102.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16786" ObjectName="SW-CX_HGY.CX_HGY_3846SW"/>
     <cge:Meas_Ref ObjectId="77202"/>
    <cge:TPSR_Ref TObjectID="16786"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-77201">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2249.855346 -4204.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16785" ObjectName="SW-CX_HGY.CX_HGY_3842SW"/>
     <cge:Meas_Ref ObjectId="77201"/>
    <cge:TPSR_Ref TObjectID="16785"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-77351">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2249.855346 -3777.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16800" ObjectName="SW-CX_TZS.CX_TZS_3541SW"/>
     <cge:Meas_Ref ObjectId="77351"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-77352">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2249.855346 -3879.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16801" ObjectName="SW-CX_TZS.CX_TZS_3546SW"/>
     <cge:Meas_Ref ObjectId="77352"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-77353">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2271.855346 -3926.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16802" ObjectName="SW-CX_TZS.CX_TZS_35467SW"/>
     <cge:Meas_Ref ObjectId="77353"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-77232">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2243.774184 -4271.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16788" ObjectName="SW-CX_HGY.CX_HGY_3832SW"/>
     <cge:Meas_Ref ObjectId="77232"/>
    <cge:TPSR_Ref TObjectID="16788"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-77233">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2243.774184 -4357.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16789" ObjectName="SW-CX_HGY.CX_HGY_3836SW"/>
     <cge:Meas_Ref ObjectId="77233"/>
    <cge:TPSR_Ref TObjectID="16789"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-83655">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2255.710692 -4547.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18441" ObjectName="SW-CX_DLC.CX_DLC_39167SW"/>
     <cge:Meas_Ref ObjectId="83655"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-83654">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2243.710692 -4563.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18440" ObjectName="SW-CX_DLC.CX_DLC_3916SW"/>
     <cge:Meas_Ref ObjectId="83654"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166097">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 172.000000 -3795.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26911" ObjectName="SW-CX_SF.CX_SF_3652SW"/>
     <cge:Meas_Ref ObjectId="166097"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166098">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 287.000000 -3795.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26912" ObjectName="SW-CX_SF.CX_SF_3656SW"/>
     <cge:Meas_Ref ObjectId="166098"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 375.000000 -3769.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166076">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 178.000000 -3642.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26906" ObjectName="SW-CX_SF.CX_SF_3642SW"/>
     <cge:Meas_Ref ObjectId="166076"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166078">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 272.000000 -3583.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26908" ObjectName="SW-CX_SF.CX_SF_36460SW"/>
     <cge:Meas_Ref ObjectId="166078"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 377.000000 -3603.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166079">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 348.000000 -3586.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26909" ObjectName="SW-CX_SF.CX_SF_36467SW"/>
     <cge:Meas_Ref ObjectId="166079"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 462.000000 -3258.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166034">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 176.000000 -3297.822257)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26896" ObjectName="SW-CX_SF.CX_SF_3621SW"/>
     <cge:Meas_Ref ObjectId="166034"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166036">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 271.000000 -3237.822257)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26898" ObjectName="SW-CX_SF.CX_SF_36260SW"/>
     <cge:Meas_Ref ObjectId="166036"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166035">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 289.000000 -3297.822257)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26897" ObjectName="SW-CX_SF.CX_SF_3626SW"/>
     <cge:Meas_Ref ObjectId="166035"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 364.000000 -3463.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166055">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 178.000000 -3441.917827)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26901" ObjectName="SW-CX_SF.CX_SF_3631SW"/>
     <cge:Meas_Ref ObjectId="166055"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166057">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 274.000000 -3381.917827)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26903" ObjectName="SW-CX_SF.CX_SF_36360SW"/>
     <cge:Meas_Ref ObjectId="166057"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 364.000000 -3402.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166058">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 349.000000 -3385.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26904" ObjectName="SW-CX_SF.CX_SF_36367SW"/>
     <cge:Meas_Ref ObjectId="166058"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166265">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 163.000000 -3546.790993)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26948" ObjectName="SW-CX_SF.CX_SF_3122SW"/>
     <cge:Meas_Ref ObjectId="166265"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166264">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 163.000000 -3497.424326)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26947" ObjectName="SW-CX_SF.CX_SF_3121SW"/>
     <cge:Meas_Ref ObjectId="166264"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166099">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 271.000000 -3748.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26913" ObjectName="SW-CX_SF.CX_SF_36560SW"/>
     <cge:Meas_Ref ObjectId="166099"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166100">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 346.000000 -3750.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26914" ObjectName="SW-CX_SF.CX_SF_36567SW"/>
     <cge:Meas_Ref ObjectId="166100"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166077">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 289.000000 -3642.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26907" ObjectName="SW-CX_SF.CX_SF_3646SW"/>
     <cge:Meas_Ref ObjectId="166077"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166056">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 290.000000 -3441.917827)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26902" ObjectName="SW-CX_SF.CX_SF_3636SW"/>
     <cge:Meas_Ref ObjectId="166056"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-186630">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 598.000000 -4849.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28265" ObjectName="SW-CX_TG.CX_TG_3516SW"/>
     <cge:Meas_Ref ObjectId="186630"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2266.710692 -4611.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 389.000000 -3981.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-196759">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 957.000000 -4654.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30134" ObjectName="SW-YM_WM.YM_WM_3736SW"/>
     <cge:Meas_Ref ObjectId="196759"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-196758">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1078.000000 -4654.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30133" ObjectName="SW-YM_WM.YM_WM_3732SW"/>
     <cge:Meas_Ref ObjectId="196758"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-196757">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1130.000000 -4654.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30132" ObjectName="SW-YM_WM.YM_WM_3731SW"/>
     <cge:Meas_Ref ObjectId="196757"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-166747">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 834.000000 -3943.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27026" ObjectName="SW-YM_LC.YM_LC_35267SW"/>
     <cge:Meas_Ref ObjectId="166747"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-196901">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 563.000000 -4400.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30180" ObjectName="SW-CX_PTS.CX_PTS_35167SW"/>
     <cge:Meas_Ref ObjectId="196901"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-196900">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 550.000000 -4360.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30178" ObjectName="SW-CX_PTS.CX_PTS_351XC"/>
     <cge:Meas_Ref ObjectId="196900"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-196900">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 550.000000 -4289.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30179" ObjectName="SW-CX_PTS.CX_PTS_351XC1"/>
     <cge:Meas_Ref ObjectId="196900"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-171132">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 970.000000 -4576.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27256" ObjectName="SW-YM_WM.YM_WM_3726SW"/>
     <cge:Meas_Ref ObjectId="171132"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-171131">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1117.000000 -4576.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27255" ObjectName="SW-YM_WM.YM_WM_3721SW"/>
     <cge:Meas_Ref ObjectId="171131"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-171113">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 927.000000 -4786.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27252" ObjectName="SW-YM_WM.YM_WM_37167SW"/>
     <cge:Meas_Ref ObjectId="171113"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-196762">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 889.000000 -4666.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30137" ObjectName="SW-YM_WM.YM_WM_37367SW"/>
     <cge:Meas_Ref ObjectId="196762"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-199025">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 701.000000 -3587.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30414" ObjectName="SW-YM_PT.YM_PT_3326SW"/>
     <cge:Meas_Ref ObjectId="199025"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-199023">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 583.000000 -3587.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30412" ObjectName="SW-YM_PT.YM_PT_3321SW"/>
     <cge:Meas_Ref ObjectId="199023"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-199024">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 600.000000 -3571.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30413" ObjectName="SW-YM_PT.YM_PT_33217SW"/>
     <cge:Meas_Ref ObjectId="199024"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-199026">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 645.000000 -3623.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30415" ObjectName="SW-YM_PT.YM_PT_33260SW"/>
     <cge:Meas_Ref ObjectId="199026"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-199027">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 692.000000 -3564.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30416" ObjectName="SW-YM_PT.YM_PT_33267SW"/>
     <cge:Meas_Ref ObjectId="199027"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1195.000000 -3386.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-170314">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1695.000000 -4901.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27190" ObjectName="SW-YM_JY.YM_JY_36167SW"/>
     <cge:Meas_Ref ObjectId="170314"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-170313">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1720.000000 -4890.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27189" ObjectName="SW-YM_JY.YM_JY_3616SW"/>
     <cge:Meas_Ref ObjectId="170313"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-170312">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1823.000000 -4890.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27188" ObjectName="SW-YM_JY.YM_JY_3611SW"/>
     <cge:Meas_Ref ObjectId="170312"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-225630">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1292.000000 -4997.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37490" ObjectName="SW-YM_JB.YM_JB_34167SW"/>
     <cge:Meas_Ref ObjectId="225630"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-225702">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1485.000000 -4998.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37496" ObjectName="SW-YM_JB.YM_JB_34267SW"/>
     <cge:Meas_Ref ObjectId="225702"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-169477">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1270.000000 -5122.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27105" ObjectName="SW-YM_JB.YM_JB_3411SW"/>
     <cge:Meas_Ref ObjectId="169477"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-169478">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1270.000000 -5029.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27106" ObjectName="SW-YM_JB.YM_JB_3416SW"/>
     <cge:Meas_Ref ObjectId="169478"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-225698">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1465.000000 -5120.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37492" ObjectName="SW-YM_JB.YM_JB_3421SW"/>
     <cge:Meas_Ref ObjectId="225698"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-225700">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1465.000000 -5023.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37491" ObjectName="SW-YM_JB.YM_JB_3426SW"/>
     <cge:Meas_Ref ObjectId="225700"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-225703">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1489.000000 -4956.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37493" ObjectName="SW-YM_JB.YM_JB_3429SW"/>
     <cge:Meas_Ref ObjectId="225703"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-225671">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1304.000000 -4912.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37487" ObjectName="SW-YM_JB.YM_JB_3419SW"/>
     <cge:Meas_Ref ObjectId="225671"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="0" id="g_3a006a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.347826 411.000000 -3225.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3a8d680" refnum="0">
    <use class="BV-0KV" transform="matrix(1.304348 -0.000000 0.000000 -1.583333 1123.000000 -3455.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3aa1250" refnum="0">
    <use class="BV-0KV" transform="matrix(1.304348 -0.000000 0.000000 -1.583333 1740.000000 -3833.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3abf9c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.347826 1531.000000 -3638.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3acb160" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.347826 1637.000000 -3638.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3ad7b00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.304348 -0.000000 0.000000 -1.583333 992.000000 -3997.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3af1190" refnum="0">
    <use class="BV-0KV" transform="matrix(1.428571 -0.000000 0.000000 -1.500000 729.000000 -3959.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b05a10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.347826 448.000000 -3826.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b0d9b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.347826 504.000000 -3578.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b13050" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.347826 836.000000 -3507.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b1f950" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.347826 1574.000000 -3874.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b22dd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.347826 583.000000 -3827.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b26100" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.347826 1397.000000 -3378.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b4f440" refnum="0">
    <use class="BV-0KV" transform="matrix(1.428571 -0.000000 0.000000 -1.500000 2320.000000 -3922.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3b67c90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.047619 -0.000000 0.000000 -1.000000 2303.000000 -4546.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3bb8920" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.347826 271.000000 -3718.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3bb9370" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.347826 272.000000 -3551.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3bb9dc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.347826 346.000000 -3720.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3bba810" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.347826 274.000000 -3355.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3bbb260" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.347826 349.000000 -3356.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3bbbcb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.347826 271.000000 -3209.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3bbc700" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.347826 348.000000 -3554.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3bd1b10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 938.000000 -4526.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3bd3550" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 608.000000 -4961.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3be0f20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.047619 -0.000000 0.000000 -1.000000 2314.000000 -4610.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3c096c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 612.000000 -4399.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3c1eba0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 918.000000 -4832.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3c22340" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 880.000000 -4712.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3c32c80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 579.000000 -3570.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3c39380" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 623.000000 -3622.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3c3d2f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 671.000000 -3563.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3cbcfb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.500000 -0.000000 0.000000 -1.454545 1695.000000 -4949.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3cca550" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1346.000000 -4996.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_3ccb8a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1537.000000 -4997.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectPoint_Layer"/><g id="MotifButton_Layer">
   <g href="cx_索引_接线图.svg" style="fill-opacity:0"><rect height="40" qtmmishow="hidden" width="226" x="-119" y="-5223"/></g>
   <g href="cx_索引_接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-166" y="-5240"/></g>
   <g href="cx_配调_配网接线图35_元谋.svg" style="fill-opacity:0"><rect height="39" qtmmishow="hidden" width="127" x="138" y="-5223"/></g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="0" cx="971" cy="-4565" fill="none" fillStyle="0" r="8.5" stroke="rgb(255,255,0)" stroke-width="0.501742"/>
   <ellipse DF8003:Layer="0" cx="981" cy="-4565" fill="none" fillStyle="0" rx="8" ry="8.5" stroke="rgb(255,255,0)" stroke-width="0.501742"/>
   <circle DF8003:Layer="0" cx="978" cy="-4761" fill="none" fillStyle="0" r="8.5" stroke="rgb(255,255,0)" stroke-width="0.501742"/>
   <ellipse DF8003:Layer="0" cx="988" cy="-4761" fill="none" fillStyle="0" rx="8" ry="8.5" stroke="rgb(255,255,0)" stroke-width="0.501742"/>
   <circle DF8003:Layer="0" cx="940" cy="-4647" fill="none" fillStyle="0" r="8.5" stroke="rgb(255,255,0)" stroke-width="0.501742"/>
   <ellipse DF8003:Layer="0" cx="950" cy="-4647" fill="none" fillStyle="0" rx="8" ry="8.5" stroke="rgb(255,255,0)" stroke-width="0.501742"/>
   <ellipse DF8003:Layer="0" cx="943" cy="-3863" fill="none" fillStyle="0" rx="10.5" ry="11.5" stroke="rgb(60,120,255)" stroke-width="0.64567"/>
   <ellipse DF8003:Layer="0" cx="943" cy="-3850" fill="none" fillStyle="0" rx="10.5" ry="11.5" stroke="rgb(60,120,255)" stroke-width="0.64567"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="0" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="0" fill="none" points="2011,-4736 2088,-4736 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="2011,-4736 2088,-4736 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_HGY.CX_HGY_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="2342,-4258 1742,-4258 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="1727" ObjectName="BS-CX_HGY.CX_HGY_3IIM"/>
    <cge:TPSR_Ref TObjectID="1727"/></metadata>
   <polyline fill="none" opacity="0" points="2342,-4258 1742,-4258 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_HGY.CX_HGY_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1699,-4258 855,-4258 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="1726" ObjectName="BS-CX_HGY.CX_HGY_3IM"/>
    <cge:TPSR_Ref TObjectID="1726"/></metadata>
   <polyline fill="none" opacity="0" points="1699,-4258 855,-4258 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="0" fill="none" points="1407,-4524 1533,-4524 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1407,-4524 1533,-4524 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_YR.CX_YR_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="469,-5176 628,-5176 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="2891" ObjectName="BS-CX_YR.CX_YR_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="469,-5176 628,-5176 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-YM_LC.YM_LC_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="845,-4169 1131,-4169 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27015" ObjectName="BS-YM_LC.YM_LC_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="845,-4169 1131,-4169 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-YM_NY.YM_NY_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1193,-3642 1408,-3642 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="28722" ObjectName="BS-YM_NY.YM_NY_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1193,-3642 1408,-3642 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="0" fill="none" points="1419,-3840 1660,-3840 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1419,-3840 1660,-3840 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="0" fill="none" points="374,-3975 523,-3975 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="374,-3975 523,-3975 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-YM_PT.YM_PT_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="573,-3685 573,-3572 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="30408" ObjectName="BS-YM_PT.YM_PT_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="573,-3685 573,-3572 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_DLTY.CX_DLTY_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="817,-4939 817,-4868 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11821" ObjectName="BS-CX_DLTY.CX_DLTY_3IIM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="817,-4939 817,-4868 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_TZS.CX_TZS_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="2238,-3764 2279,-3764 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="16797" ObjectName="BS-CX_TZS.CX_TZS_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="2238,-3764 2279,-3764 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_DLC.CX_DLC_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="2216,-4746 2293,-4746 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18426" ObjectName="BS-CX_DLC.CX_DLC_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="2216,-4746 2293,-4746 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_XXC.CX_XXC_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="837,-3430 837,-3365 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="19030" ObjectName="BS-CX_XXC.CX_XXC_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="837,-3430 837,-3365 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_SF.CX_SF_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="148,-3836 148,-3538 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26859" ObjectName="BS-CX_SF.CX_SF_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="148,-3836 148,-3538 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_SF.CX_SF_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="148,-3518 148,-3256 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26963" ObjectName="BS-CX_SF.CX_SF_3IIM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="148,-3518 148,-3256 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-YM_WM.YM_WM_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1180,-4804 1180,-4559 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27244" ObjectName="BS-YM_WM.YM_WM_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1180,-4804 1180,-4559 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_TG.CX_TG_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="764,-4867 764,-4837 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="28262" ObjectName="BS-CX_TG.CX_TG_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="764,-4867 764,-4837 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-CX_PTS.CX_PTS_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="522,-4246 610,-4246 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="30175" ObjectName="BS-CX_PTS.CX_PTS_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="522,-4246 610,-4246 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-YM_JB.YM_JB_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1244,-5177 1511,-5177 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27088" ObjectName="BS-YM_JB.YM_JB_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1244,-5177 1511,-5177 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="0" id="BS-YM_JY.YM_JY_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="0" fill="none" points="1886,-4932 1886,-4803 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="27184" ObjectName="BS-YM_JY.YM_JY_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1886,-4932 1886,-4803 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="0" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="40" qtmmishow="hidden" width="226" x="-119" y="-5223"/>
    </a>
   <metadata/><rect fill="white" height="40" opacity="0" stroke="white" transform="" width="226" x="-119" y="-5223"/></g>
   <g DF8003:Layer="0" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-166" y="-5240"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-166" y="-5240"/></g>
   <g DF8003:Layer="0" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="39" qtmmishow="hidden" width="127" x="138" y="-5223"/>
    </a>
   <metadata/><rect fill="white" height="39" opacity="0" stroke="white" transform="" width="127" x="138" y="-5223"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="0" fill="none" height="13" stroke="rgb(255,255,0)" stroke-width="0.371482" width="23" x="939" y="-4768"/>
   <rect DF8003:Layer="0" fill="none" height="13" stroke="rgb(255,255,0)" stroke-width="0.371482" width="23" x="901" y="-4654"/>
   <rect DF8003:Layer="0" fill="none" height="4" stroke="rgb(60,120,255)" stroke-width="1" width="19" x="922" y="-3914"/>
   <rect DF8003:Layer="0" fill="none" height="26" stroke="rgb(60,120,255)" stroke-width="1" width="12" x="876" y="-3913"/>
   <rect DF8003:Layer="0" fill="none" height="12" stroke="rgb(255,255,0)" stroke-width="0.388889" width="25" x="934" y="-4571"/>
  </g><g id="Link_Layer">
   <g class="BV-0KV" id="g_22944e0">
     <polyline DF8003:Layer="0" fill="none" points="2051,-4705 2051,-4736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2051,-4705 2051,-4736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2294710">
     <polyline DF8003:Layer="0" fill="none" points="2051,-4437 2032,-4437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3b5a420@0" ObjectIDND1="0@x" ObjectIDND2="1802@x" ObjectIDZND0="g_22905b0@0" Pin0InfoVect0LinkObjId="g_22905b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3b5a420_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-12067_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2051,-4437 2032,-4437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_227ac10">
     <polyline DF8003:Layer="0" fill="none" points="2051,-4437 2051,-4447 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="switch" ObjectIDND0="g_22905b0@0" ObjectIDND1="1802@x" ObjectIDZND0="g_3b5a420@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_3b5a420_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_22905b0_0" Pin1InfoVect1LinkObjId="SW-12067_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2051,-4437 2051,-4447 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22693c0">
     <polyline DF8003:Layer="0" fill="none" points="2051,-4447 2051,-4668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_22905b0@0" ObjectIDND1="1802@x" ObjectIDND2="g_3b5a420@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_22905b0_0" Pin1InfoVect1LinkObjId="SW-12067_0" Pin1InfoVect2LinkObjId="g_3b5a420_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2051,-4447 2051,-4668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2268af0">
     <polyline DF8003:Layer="0" fill="none" points="2082,-4434 2082,-4447 2051,-4447 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3b5a420@0" ObjectIDZND0="g_22905b0@0" ObjectIDZND1="1802@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_22905b0_0" Pin0InfoVect1LinkObjId="SW-12067_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b5a420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2082,-4434 2082,-4447 2051,-4447 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2218b60">
     <polyline DF8003:Layer="0" fill="none" points="2051,-4417 2051,-4437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="1802@1" ObjectIDZND0="g_22905b0@0" ObjectIDZND1="g_3b5a420@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_22905b0_0" Pin0InfoVect1LinkObjId="g_3b5a420_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-12067_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2051,-4417 2051,-4437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3698df0">
     <polyline DF8003:Layer="0" fill="none" points="2051,-4362 2051,-4381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="1800@1" ObjectIDZND0="1802@0" Pin0InfoVect0LinkObjId="SW-12067_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-12063_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2051,-4362 2051,-4381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_362b180">
     <polyline DF8003:Layer="0" fill="none" points="2051,-4258 2051,-4286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="1727@0" ObjectIDZND0="1801@0" Pin0InfoVect0LinkObjId="SW-12065_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2051,-4258 2051,-4286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3619c20">
     <polyline DF8003:Layer="0" fill="none" points="2051,-4322 2051,-4335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="1801@1" ObjectIDZND0="1800@0" Pin0InfoVect0LinkObjId="SW-12063_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-12065_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2051,-4322 2051,-4335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35d30e0">
     <polyline DF8003:Layer="0" fill="none" points="1675,-4284 1675,-4258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="1775@0" ObjectIDZND0="1726@0" Pin0InfoVect0LinkObjId="g_31551e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-11389_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1675,-4284 1675,-4258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c32f00">
     <polyline DF8003:Layer="0" fill="none" points="1764,-4258 1764,-4284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="1727@0" ObjectIDZND0="1776@0" Pin0InfoVect0LinkObjId="SW-11390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1764,-4258 1764,-4284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bed5e0">
     <polyline DF8003:Layer="0" fill="none" points="1764,-4320 1764,-4344 1730,-4344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="1776@1" ObjectIDZND0="1774@0" Pin0InfoVect0LinkObjId="SW-11387_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-11390_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1764,-4320 1764,-4344 1730,-4344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2be9670">
     <polyline DF8003:Layer="0" fill="none" points="1703,-4344 1675,-4344 1675,-4320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="1774@1" ObjectIDZND0="1775@1" Pin0InfoVect0LinkObjId="SW-11389_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-11387_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1703,-4344 1675,-4344 1675,-4320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bcc310">
     <polyline DF8003:Layer="0" fill="none" points="1470,-4430 1451,-4430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDND1="0@x" ObjectIDND2="1796@x" ObjectIDZND0="g_2be2bf0@0" Pin0InfoVect0LinkObjId="g_2be2bf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-11981_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1470,-4430 1451,-4430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b657e0">
     <polyline DF8003:Layer="0" fill="none" points="1505,-4405 1505,-4390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2b65a40@0" Pin0InfoVect0LinkObjId="g_2b65a40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1505,-4405 1505,-4390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b4de50">
     <polyline DF8003:Layer="0" fill="none" points="1505,-4451 1505,-4464 1468,-4464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="busSection" ObjectIDND0="0@0" ObjectIDZND0="g_2be2bf0@0" ObjectIDZND1="1796@x" ObjectIDZND2="0@0" Pin0InfoVect0LinkObjId="g_2be2bf0_0" Pin0InfoVect1LinkObjId="SW-11981_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1505,-4451 1505,-4464 1468,-4464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0cbb0">
     <polyline DF8003:Layer="0" fill="none" points="1468,-4430 1468,-4464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="g_2be2bf0@0" ObjectIDND1="1796@x" ObjectIDZND0="0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2be2bf0_0" Pin1InfoVect1LinkObjId="SW-11981_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1468,-4430 1468,-4464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b08520">
     <polyline DF8003:Layer="0" fill="none" points="1468,-4464 1468,-4524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="busSection" ObjectIDND0="g_2be2bf0@0" ObjectIDND1="1796@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2be2bf0_0" Pin1InfoVect1LinkObjId="SW-11981_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1468,-4464 1468,-4524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ab4590">
     <polyline DF8003:Layer="0" fill="none" points="1468,-4413 1468,-4430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="busSection" EndDevType2="switch" ObjectIDND0="1796@1" ObjectIDZND0="g_2be2bf0@0" ObjectIDZND1="0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2be2bf0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-11981_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1468,-4413 1468,-4430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29d73a0">
     <polyline DF8003:Layer="0" fill="none" points="1468,-4364 1468,-4377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="1794@1" ObjectIDZND0="1796@0" Pin0InfoVect0LinkObjId="SW-11981_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-11977_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1468,-4364 1468,-4377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29d75e0">
     <polyline DF8003:Layer="0" fill="none" points="1468,-4258 1468,-4274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="1726@0" ObjectIDZND0="1795@0" Pin0InfoVect0LinkObjId="SW-11979_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35d30e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1468,-4258 1468,-4274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29d0f60">
     <polyline DF8003:Layer="0" fill="none" points="1468,-4310 1468,-4337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="1795@1" ObjectIDZND0="1794@0" Pin0InfoVect0LinkObjId="SW-11977_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-11979_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1468,-4310 1468,-4337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_298d4a0">
     <polyline DF8003:Layer="0" fill="none" points="1433,-4524 1433,-4539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1433,-4524 1433,-4539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_28b9790">
     <polyline DF8003:Layer="0" fill="none" points="1500,-4524 1500,-4542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1500,-4524 1500,-4542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2742ac0">
     <polyline DF8003:Layer="0" fill="none" points="1500,-4578 1500,-4599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1500,-4578 1500,-4599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_273b1b0">
     <polyline DF8003:Layer="0" fill="none" points="1500,-4626 1500,-4649 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1500,-4626 1500,-4649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26f0cd0">
     <polyline DF8003:Layer="0" fill="none" points="1433,-4575 1433,-4600 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1433,-4575 1433,-4600 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26e9b40">
     <polyline DF8003:Layer="0" fill="none" points="1433,-4627 1433,-4649 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1433,-4627 1433,-4649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36f3680">
     <polyline DF8003:Layer="0" fill="none" points="1279,-4419 1259,-4419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="1799@x" ObjectIDZND0="g_38119e0@0" Pin0InfoVect0LinkObjId="g_38119e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-12023_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1279,-4419 1259,-4419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35f44d0">
     <polyline DF8003:Layer="0" fill="none" points="1317,-4361 1317,-4346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_35b04c0@0" Pin0InfoVect0LinkObjId="g_35b04c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1317,-4361 1317,-4346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35aede0">
     <polyline DF8003:Layer="0" fill="none" points="1316,-4406 1316,-4419 1279,-4419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_38119e0@0" ObjectIDZND1="1799@x" Pin0InfoVect0LinkObjId="g_38119e0_0" Pin0InfoVect1LinkObjId="SW-12023_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1316,-4406 1316,-4419 1279,-4419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33adff0">
     <polyline DF8003:Layer="0" fill="none" points="1279,-4419 1279,-4405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_38119e0@0" ObjectIDND1="0@x" ObjectIDZND0="1799@1" Pin0InfoVect0LinkObjId="SW-12023_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_38119e0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1279,-4419 1279,-4405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31fb220">
     <polyline DF8003:Layer="0" fill="none" points="1279,-4369 1279,-4350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="1799@0" ObjectIDZND0="1797@1" Pin0InfoVect0LinkObjId="SW-12020_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-12023_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1279,-4369 1279,-4350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3154f80">
     <polyline DF8003:Layer="0" fill="none" points="1279,-4323 1279,-4307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="1797@0" ObjectIDZND0="1798@1" Pin0InfoVect0LinkObjId="SW-12021_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-12020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1279,-4323 1279,-4307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31551e0">
     <polyline DF8003:Layer="0" fill="none" points="1279,-4271 1279,-4258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="1798@0" ObjectIDZND0="1726@0" Pin0InfoVect0LinkObjId="g_35d30e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-12021_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1279,-4271 1279,-4258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2582a40">
     <polyline DF8003:Layer="0" fill="none" points="560,-5164 560,-5176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2948@1" ObjectIDZND0="2891@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19284_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="560,-5164 560,-5176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_185a0f0">
     <polyline DF8003:Layer="0" fill="none" points="911,-4258 911,-4269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="1726@0" ObjectIDZND0="1792@0" Pin0InfoVect0LinkObjId="SW-11934_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35d30e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="911,-4258 911,-4269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_185a350">
     <polyline DF8003:Layer="0" fill="none" points="911,-4305 911,-4314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="1792@1" ObjectIDZND0="1791@0" Pin0InfoVect0LinkObjId="SW-11932_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-11934_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="911,-4305 911,-4314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_38782c0">
     <polyline DF8003:Layer="0" fill="none" points="990,-4471 999,-4471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="990,-4471 999,-4471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_186c6a0">
     <polyline DF8003:Layer="0" fill="none" points="1026,-4471 1039,-4471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1026,-4471 1039,-4471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_186c900">
     <polyline DF8003:Layer="0" fill="none" points="942,-4471 942,-4484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="1793@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-11936_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="942,-4471 942,-4484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_186cb60">
     <polyline DF8003:Layer="0" fill="none" points="949,-4341 949,-4326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3b73330@0" Pin0InfoVect0LinkObjId="g_3b73330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="949,-4341 949,-4326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39ea4a0">
     <polyline DF8003:Layer="0" fill="none" points="560,-5066 560,-5082 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2949@1" ObjectIDZND0="2950@0" Pin0InfoVect0LinkObjId="SW-19286_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19285_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="560,-5066 560,-5082 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39ea700">
     <polyline DF8003:Layer="0" fill="none" points="560,-5109 560,-5128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2950@1" ObjectIDZND0="2948@0" Pin0InfoVect0LinkObjId="SW-19284_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19286_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="560,-5109 560,-5128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3178d80">
     <polyline DF8003:Layer="0" fill="none" points="1097,-4773 1110,-4773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="27248@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171109_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1097,-4773 1110,-4773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3728bd0">
     <polyline DF8003:Layer="0" fill="none" points="1109,-4773 1135,-4773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="27249@0" Pin0InfoVect0LinkObjId="SW-171110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1109,-4773 1135,-4773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a0c2b0">
     <polyline DF8003:Layer="0" fill="none" points="1967,-4258 1967,-4238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="1727@0" ObjectIDZND0="1804@1" Pin0InfoVect0LinkObjId="SW-12109_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1967,-4258 1967,-4238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a00440">
     <polyline DF8003:Layer="0" fill="none" points="419,-3266 420,-3249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26899@0" ObjectIDZND0="g_3a006a0@0" Pin0InfoVect0LinkObjId="g_3a006a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166037_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="419,-3266 420,-3249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_185deb0">
     <polyline DF8003:Layer="0" fill="none" points="1369,-3626 1369,-3642 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28640@1" ObjectIDZND0="28722@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188044_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1369,-3626 1369,-3642 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_185e110">
     <polyline DF8003:Layer="0" fill="none" points="1369,-3528 1369,-3544 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28641@1" ObjectIDZND0="28639@0" Pin0InfoVect0LinkObjId="SW-188043_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188045_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1369,-3528 1369,-3544 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_185e370">
     <polyline DF8003:Layer="0" fill="none" points="1369,-3571 1369,-3590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28639@1" ObjectIDZND0="28640@0" Pin0InfoVect0LinkObjId="SW-188044_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188043_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1369,-3571 1369,-3590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_185ed10">
     <polyline DF8003:Layer="0" fill="none" points="1323,-3418 1323,-3403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_185e5d0@0" Pin0InfoVect0LinkObjId="g_185e5d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1323,-3418 1323,-3403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1861900">
     <polyline DF8003:Layer="0" fill="none" points="1369,-3479 1369,-3492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="28642@x" ObjectIDND1="0@x" ObjectIDND2="g_3b8ba80@0" ObjectIDZND0="28641@0" Pin0InfoVect0LinkObjId="SW-188045_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-188046_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_3b8ba80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1369,-3479 1369,-3492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a81190">
     <polyline DF8003:Layer="0" fill="none" points="1322,-3463 1322,-3479 1347,-3479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="28642@x" ObjectIDZND1="0@x" ObjectIDZND2="g_3b8ba80@0" Pin0InfoVect0LinkObjId="SW-188046_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_3b8ba80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1322,-3463 1322,-3479 1347,-3479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a813f0">
     <polyline DF8003:Layer="0" fill="none" points="1347,-3479 1369,-3479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_1861b60@0" ObjectIDZND0="28642@x" ObjectIDZND1="0@x" ObjectIDZND2="g_3b8ba80@0" Pin0InfoVect0LinkObjId="SW-188046_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_3b8ba80_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1861b60_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1347,-3479 1369,-3479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a81650">
     <polyline DF8003:Layer="0" fill="none" points="1239,-3479 1221,-3479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_3a82140@0" ObjectIDZND0="28645@x" ObjectIDZND1="19032@x" ObjectIDZND2="26904@x" Pin0InfoVect0LinkObjId="SW-188059_0" Pin0InfoVect1LinkObjId="SW-87817_0" Pin0InfoVect2LinkObjId="SW-166058_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a82140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1239,-3479 1221,-3479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a82ef0">
     <polyline DF8003:Layer="0" fill="none" points="1239,-3463 1239,-3479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_3a82140@0" ObjectIDZND0="28645@x" ObjectIDZND1="19032@x" ObjectIDZND2="26904@x" Pin0InfoVect0LinkObjId="SW-188059_0" Pin0InfoVect1LinkObjId="SW-87817_0" Pin0InfoVect2LinkObjId="SW-166058_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a82140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1239,-3463 1239,-3479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a8aa30">
     <polyline DF8003:Layer="0" fill="none" points="1220,-3593 1220,-3574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28644@0" ObjectIDZND0="28643@1" Pin0InfoVect0LinkObjId="SW-188057_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188058_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1220,-3593 1220,-3574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a8ac90">
     <polyline DF8003:Layer="0" fill="none" points="1220,-3547 1220,-3531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28643@0" ObjectIDZND0="28645@1" Pin0InfoVect0LinkObjId="SW-188059_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188057_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1220,-3547 1220,-3531 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a8aef0">
     <polyline DF8003:Layer="0" fill="none" points="1220,-3495 1220,-3479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="28645@0" ObjectIDZND0="g_3a82140@0" ObjectIDZND1="19032@x" ObjectIDZND2="26904@x" Pin0InfoVect0LinkObjId="g_3a82140_0" Pin0InfoVect1LinkObjId="SW-87817_0" Pin0InfoVect2LinkObjId="SW-166058_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188059_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1220,-3495 1220,-3479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a8e110">
     <polyline DF8003:Layer="0" fill="none" points="1163,-3464 1147,-3464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28646@0" ObjectIDZND0="g_3a8d680@0" Pin0InfoVect0LinkObjId="g_3a8d680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1163,-3464 1147,-3464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3a96630">
     <polyline DF8003:Layer="0" fill="none" points="1515,-3935 1515,-3919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1515,-3935 1515,-3919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3a96890">
     <polyline DF8003:Layer="0" fill="none" points="1515,-3885 1515,-3871 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1515,-3885 1515,-3871 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3a96af0">
     <polyline DF8003:Layer="0" fill="none" points="1515,-3854 1515,-3841 1516,-3840 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1515,-3854 1515,-3841 1516,-3840 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a97800">
     <polyline DF8003:Layer="0" fill="none" points="1515,-3981 1515,-3966 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_3ad87f0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_3ad87f0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1515,-3981 1515,-3966 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a97a60">
     <polyline DF8003:Layer="0" fill="none" points="1515,-3966 1515,-3952 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1515,-3966 1515,-3952 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a9aa50">
     <polyline DF8003:Layer="0" fill="none" points="1515,-3966 1442,-3966 1442,-3951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1515,-3966 1442,-3966 1442,-3951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3a9b530">
     <polyline DF8003:Layer="0" fill="none" points="1442,-3934 1442,-3919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3a9acb0@1" Pin0InfoVect0LinkObjId="g_3a9acb0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1442,-3934 1442,-3919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3a9b790">
     <polyline DF8003:Layer="0" fill="none" points="1442,-3888 1442,-3882 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3a9acb0@0" ObjectIDZND0="g_3a9b9f0@0" Pin0InfoVect0LinkObjId="g_3a9b9f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a9acb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1442,-3888 1442,-3882 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a9ea70">
     <polyline DF8003:Layer="0" fill="none" points="1515,-3966 1583,-3966 1583,-3952 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1515,-3966 1583,-3966 1583,-3952 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3a9ecd0">
     <polyline DF8003:Layer="0" fill="none" points="1583,-3916 1583,-3898 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_3b1f950@0" Pin0InfoVect0LinkObjId="g_3b1f950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1583,-3916 1583,-3898 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3aa4770">
     <polyline DF8003:Layer="0" fill="none" points="1797,-3842 1778,-3842 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="earth" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_3aa1250@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_3aa1250_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1797,-3842 1778,-3842 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3aa49d0">
     <polyline DF8003:Layer="0" fill="none" points="1778,-3842 1763,-3842 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_3aa1250@0" Pin0InfoVect0LinkObjId="g_3aa1250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1778,-3842 1763,-3842 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3aa54c0">
     <polyline DF8003:Layer="0" fill="none" points="1846,-3792 1846,-3784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1846,-3792 1846,-3784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3aa7bf0">
     <polyline DF8003:Layer="0" fill="none" points="1778,-3842 1778,-3784 1796,-3784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="earth" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_3aa1250@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3aa1250_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1778,-3842 1778,-3784 1796,-3784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3aa7e50">
     <polyline DF8003:Layer="0" fill="none" points="1832,-3784 1846,-3784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1832,-3784 1846,-3784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3aa8940">
     <polyline DF8003:Layer="0" fill="none" points="1846,-3784 1846,-3755 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1846,-3784 1846,-3755 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3aab340">
     <polyline DF8003:Layer="0" fill="none" points="1846,-3755 1789,-3755 1789,-3745 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1846,-3755 1789,-3755 1789,-3745 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3aac050">
     <polyline DF8003:Layer="0" fill="none" points="1789,-3709 1789,-3700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_3ab0cd0@0" ObjectIDZND1="0@x" ObjectIDZND2="g_3ab29b0@0" Pin0InfoVect0LinkObjId="g_3ab0cd0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_3ab29b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1789,-3709 1789,-3700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3aac2b0">
     <polyline DF8003:Layer="0" fill="none" points="1789,-3700 1789,-3701 1810,-3701 1810,-3689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_3ab29b0@0" ObjectIDZND0="g_3ab0cd0@0" Pin0InfoVect0LinkObjId="g_3ab0cd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_3ab29b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1789,-3700 1789,-3701 1810,-3701 1810,-3689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3aaecc0">
     <polyline DF8003:Layer="0" fill="none" points="1789,-3700 1770,-3700 1770,-3689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_3ab0cd0@0" ObjectIDND2="g_3ab29b0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3ab0cd0_0" Pin1InfoVect2LinkObjId="g_3ab29b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1789,-3700 1770,-3700 1770,-3689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ab24f0">
     <polyline DF8003:Layer="0" fill="none" points="1846,-3755 1846,-3682 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1846,-3755 1846,-3682 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ab2750">
     <polyline DF8003:Layer="0" fill="none" points="1846,-3648 1846,-3625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1846,-3648 1846,-3625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ab3230">
     <polyline DF8003:Layer="0" fill="none" points="1789,-3700 1789,-3673 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_3ab0cd0@0" ObjectIDND2="0@x" ObjectIDZND0="g_3ab29b0@1" Pin0InfoVect0LinkObjId="g_3ab29b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3ab0cd0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1789,-3700 1789,-3673 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ab3490">
     <polyline DF8003:Layer="0" fill="none" points="1789,-3642 1789,-3629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3ab29b0@0" ObjectIDZND0="g_3ab1a80@0" Pin0InfoVect0LinkObjId="g_3ab1a80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ab29b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1789,-3642 1789,-3629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3abb8f0">
     <polyline DF8003:Layer="0" fill="none" points="1503,-3762 1503,-3748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1503,-3762 1503,-3748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3abbb50">
     <polyline DF8003:Layer="0" fill="none" points="1503,-3840 1503,-3829 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1503,-3840 1503,-3829 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3abbdb0">
     <polyline DF8003:Layer="0" fill="none" points="1503,-3812 1503,-3796 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1503,-3812 1503,-3796 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3abc8a0">
     <polyline DF8003:Layer="0" fill="none" points="1503,-3731 1503,-3717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1503,-3731 1503,-3717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3abcb00">
     <polyline DF8003:Layer="0" fill="none" points="1503,-3717 1503,-3666 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1503,-3717 1503,-3666 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3abf500">
     <polyline DF8003:Layer="0" fill="none" points="1503,-3717 1540,-3717 1540,-3708 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1503,-3717 1540,-3717 1540,-3708 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3abf760">
     <polyline DF8003:Layer="0" fill="none" points="1540,-3672 1540,-3662 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_3abf9c0@0" Pin0InfoVect0LinkObjId="g_3abf9c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1540,-3672 1540,-3662 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ac8040">
     <polyline DF8003:Layer="0" fill="none" points="1609,-3762 1609,-3748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1609,-3762 1609,-3748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ac82a0">
     <polyline DF8003:Layer="0" fill="none" points="1609,-3840 1609,-3829 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1609,-3840 1609,-3829 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ac8500">
     <polyline DF8003:Layer="0" fill="none" points="1609,-3812 1609,-3796 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1609,-3812 1609,-3796 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3acaf00">
     <polyline DF8003:Layer="0" fill="none" points="1646,-3672 1646,-3662 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_3acb160@0" Pin0InfoVect0LinkObjId="g_3acb160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1646,-3672 1646,-3662 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3acc180">
     <polyline DF8003:Layer="0" fill="none" points="1609,-3716 1609,-3665 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1609,-3716 1609,-3665 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3accc70">
     <polyline DF8003:Layer="0" fill="none" points="1646,-3708 1646,-3717 1609,-3717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1646,-3708 1646,-3717 1609,-3717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3acced0">
     <polyline DF8003:Layer="0" fill="none" points="1609,-3717 1609,-3731 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1609,-3717 1609,-3731 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3acf930">
     <polyline DF8003:Layer="0" fill="none" points="1084,-4169 1084,-4154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27015@0" ObjectIDZND0="27020@1" Pin0InfoVect0LinkObjId="SW-166726_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1084,-4169 1084,-4154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ad41a0">
     <polyline DF8003:Layer="0" fill="none" points="1084,-4054 1084,-4068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27021@1" ObjectIDZND0="27019@1" Pin0InfoVect0LinkObjId="SW-166724_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166727_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1084,-4054 1084,-4068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ad4400">
     <polyline DF8003:Layer="0" fill="none" points="1084,-4102 1084,-4118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27019@0" ObjectIDZND0="27020@0" Pin0InfoVect0LinkObjId="SW-166726_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166724_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1084,-4102 1084,-4118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ad5110">
     <polyline DF8003:Layer="0" fill="none" points="1084,-4006 1084,-4018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3ad87f0@0" ObjectIDND1="0@x" ObjectIDND2="27022@x" ObjectIDZND0="27021@0" Pin0InfoVect0LinkObjId="SW-166727_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ad87f0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-166728_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1084,-4006 1084,-4018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ad5370">
     <polyline DF8003:Layer="0" fill="none" points="1084,-4006 1099,-4006 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="27022@x" ObjectIDND2="27021@x" ObjectIDZND0="g_3ad87f0@0" Pin0InfoVect0LinkObjId="g_3ad87f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-166728_0" Pin1InfoVect2LinkObjId="SW-166727_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1084,-4006 1099,-4006 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ad8590">
     <polyline DF8003:Layer="0" fill="none" points="1015,-4006 1029,-4006 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3ad7b00@0" ObjectIDZND0="27022@0" Pin0InfoVect0LinkObjId="SW-166728_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ad7b00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1015,-4006 1029,-4006 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ad95a0">
     <polyline DF8003:Layer="0" fill="none" points="1073,-4006 1073,-3990 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3ad87f0@0" ObjectIDND1="27021@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ad87f0_0" Pin1InfoVect1LinkObjId="SW-166727_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1073,-4006 1073,-3990 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ada090">
     <polyline DF8003:Layer="0" fill="none" points="1065,-4006 1073,-4006 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="27022@1" ObjectIDZND0="g_3ad87f0@0" ObjectIDZND1="27021@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_3ad87f0_0" Pin0InfoVect1LinkObjId="SW-166727_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166728_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1065,-4006 1073,-4006 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ada2f0">
     <polyline DF8003:Layer="0" fill="none" points="1073,-4006 1084,-4006 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="27022@x" ObjectIDZND0="g_3ad87f0@0" ObjectIDZND1="27021@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_3ad87f0_0" Pin0InfoVect1LinkObjId="SW-166727_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-166728_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1073,-4006 1084,-4006 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3adcee0">
     <polyline DF8003:Layer="0" fill="none" points="1074,-3945 1074,-3930 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3add140@0" Pin0InfoVect0LinkObjId="g_3add140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1074,-3945 1074,-3930 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ae4690">
     <polyline DF8003:Layer="0" fill="none" points="897,-4068 897,-4054 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27023@1" ObjectIDZND0="27025@1" Pin0InfoVect0LinkObjId="SW-166746_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166743_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="897,-4068 897,-4054 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ae48f0">
     <polyline DF8003:Layer="0" fill="none" points="897,-4169 897,-4154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27015@0" ObjectIDZND0="27024@1" Pin0InfoVect0LinkObjId="SW-166745_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="897,-4169 897,-4154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ae4b50">
     <polyline DF8003:Layer="0" fill="none" points="897,-4118 897,-4102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27024@0" ObjectIDZND0="27023@0" Pin0InfoVect0LinkObjId="SW-166743_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166745_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="897,-4118 897,-4102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ae4db0">
     <polyline DF8003:Layer="0" fill="none" points="897,-3951 947,-3951 947,-3929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="27025@x" ObjectIDND1="0@x" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-166746_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="897,-3951 947,-3951 947,-3929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ae5750">
     <polyline DF8003:Layer="0" fill="none" points="943,-3889 943,-3874 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="943,-3889 943,-3874 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3af0cd0">
     <polyline DF8003:Layer="0" fill="none" points="665,-3968 685,-3968 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="27025@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-166746_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="665,-3968 685,-3968 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3af0f30">
     <polyline DF8003:Layer="0" fill="none" points="721,-3968 735,-3968 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_3af1190@0" Pin0InfoVect0LinkObjId="g_3af1190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="721,-3968 735,-3968 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3af43c0">
     <polyline DF8003:Layer="0" fill="none" points="611,-3912 592,-3912 592,-3901 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3af5630@0" ObjectIDND1="g_3af4880@0" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3af5630_0" Pin1InfoVect1LinkObjId="g_3af4880_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="611,-3912 592,-3912 592,-3901 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3af4620">
     <polyline DF8003:Layer="0" fill="none" points="592,-3865 592,-3851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_3b22dd0@0" Pin0InfoVect0LinkObjId="g_3b22dd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="592,-3865 592,-3851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3af5eb0">
     <polyline DF8003:Layer="0" fill="none" points="611,-3912 611,-3886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3af4880@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_3af5630@1" Pin0InfoVect0LinkObjId="g_3af5630_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3af4880_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="611,-3912 611,-3886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3af6110">
     <polyline DF8003:Layer="0" fill="none" points="611,-3855 611,-3842 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_3af5630@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3af5630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="611,-3855 611,-3842 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3af95c0">
     <polyline DF8003:Layer="0" fill="none" points="665,-4104 665,-4112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3aecf30@0" Pin0InfoVect0LinkObjId="g_3aecf30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="665,-4104 665,-4112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3af9820">
     <polyline DF8003:Layer="0" fill="none" points="665,-4068 665,-4055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="665,-4068 665,-4055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3af9a80">
     <polyline DF8003:Layer="0" fill="none" points="665,-4013 665,-4021 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="665,-4013 665,-4021 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3afa790">
     <polyline DF8003:Layer="0" fill="none" points="665,-3968 665,-3977 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="27025@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-166746_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="665,-3968 665,-3977 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3afa9f0">
     <polyline DF8003:Layer="0" fill="none" points="665,-3968 665,-3800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="27025@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-166746_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="665,-3968 665,-3800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3afac50">
     <polyline DF8003:Layer="0" fill="none" points="611,-3912 632,-3912 632,-3900 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3af5630@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_3af4880@0" Pin0InfoVect0LinkObjId="g_3af4880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3af5630_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="611,-3912 632,-3912 632,-3900 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3afaeb0">
     <polyline DF8003:Layer="0" fill="none" points="665,-3968 611,-3968 611,-3960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="27025@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-166746_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="665,-3968 611,-3968 611,-3960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b01e00">
     <polyline DF8003:Layer="0" fill="none" points="489,-3800 489,-3863 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="489,-3800 489,-3863 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b028f0">
     <polyline DF8003:Layer="0" fill="none" points="489,-3899 489,-3909 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="489,-3899 489,-3909 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b02b50">
     <polyline DF8003:Layer="0" fill="none" points="489,-3909 489,-3917 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="489,-3909 489,-3917 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b05550">
     <polyline DF8003:Layer="0" fill="none" points="489,-3909 457,-3909 457,-3894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="489,-3909 457,-3909 457,-3894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b057b0">
     <polyline DF8003:Layer="0" fill="none" points="457,-3858 457,-3850 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_3b05a10@0" Pin0InfoVect0LinkObjId="g_3b05a10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="457,-3858 457,-3850 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b06460">
     <polyline DF8003:Layer="0" fill="none" points="665,-3800 489,-3800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="665,-3800 489,-3800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b068c0">
     <polyline DF8003:Layer="0" fill="none" points="489,-3951 489,-3975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="489,-3951 489,-3975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b070f0">
     <polyline DF8003:Layer="0" fill="none" points="489,-3975 489,-3987 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="489,-3975 489,-3987 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b07350">
     <polyline DF8003:Layer="0" fill="none" points="489,-4023 489,-4037 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="489,-4023 489,-4037 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b0af50">
     <polyline DF8003:Layer="0" fill="none" points="570,-3705 583,-3705 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_3ae5010@0" Pin0InfoVect0LinkObjId="g_3ae5010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="570,-3705 583,-3705 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b0e400">
     <polyline DF8003:Layer="0" fill="none" points="513,-3704 513,-3647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_3b07810@0" ObjectIDZND0="30418@1" Pin0InfoVect0LinkObjId="SW-199067_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3b07810_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="513,-3704 513,-3647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b0e660">
     <polyline DF8003:Layer="0" fill="none" points="513,-3611 513,-3602 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="30418@0" ObjectIDZND0="g_3b0d9b0@0" Pin0InfoVect0LinkObjId="g_3b0d9b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199067_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="513,-3611 513,-3602 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b0f140">
     <polyline DF8003:Layer="0" fill="none" points="877,-3617 897,-3617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3b0e8c0@0" ObjectIDZND0="g_3b0f3a0@0" Pin0InfoVect0LinkObjId="g_3b0f3a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b0e8c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="877,-3617 897,-3617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b13aa0">
     <polyline DF8003:Layer="0" fill="none" points="845,-3543 845,-3531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32081@0" ObjectIDZND0="g_3b13050@0" Pin0InfoVect0LinkObjId="g_3b13050_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215562_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="845,-3543 845,-3531 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b16230">
     <polyline DF8003:Layer="0" fill="none" points="559,-3647 573,-3647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="30417@1" ObjectIDZND0="30408@0" Pin0InfoVect0LinkObjId="g_3c3fea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199066_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="559,-3647 573,-3647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b1f230">
     <polyline DF8003:Layer="0" fill="none" points="894,-3592 908,-3592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32078@1" ObjectIDZND0="32077@1" Pin0InfoVect0LinkObjId="SW-215558_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215559_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="894,-3592 908,-3592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b1f490">
     <polyline DF8003:Layer="0" fill="none" points="935,-3592 949,-3592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="32077@0" ObjectIDZND0="32079@0" Pin0InfoVect0LinkObjId="SW-215560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215558_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="935,-3592 949,-3592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b1f6f0">
     <polyline DF8003:Layer="0" fill="none" points="985,-3592 998,-3592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="32079@1" ObjectIDZND0="32083@0" Pin0InfoVect0LinkObjId="SW-215572_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215560_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="985,-3592 998,-3592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b235a0">
     <polyline DF8003:Layer="0" fill="none" points="611,-3924 611,-3912 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_3af5630@0" ObjectIDZND1="g_3af4880@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_3af5630_0" Pin0InfoVect1LinkObjId="g_3af4880_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="611,-3924 611,-3912 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b25ea0">
     <polyline DF8003:Layer="0" fill="none" points="1406,-3412 1406,-3402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28642@0" ObjectIDZND0="g_3b26100@0" Pin0InfoVect0LinkObjId="g_3b26100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188046_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1406,-3412 1406,-3402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b26b50">
     <polyline DF8003:Layer="0" fill="none" points="1347,-3452 1347,-3479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1861b60@0" ObjectIDZND0="0@x" ObjectIDZND1="28642@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-188046_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1861b60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1347,-3452 1347,-3479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b2b410">
     <polyline DF8003:Layer="0" fill="none" points="626,-4906 626,-4920 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="11812@x" ObjectIDND1="g_3b38920@0" ObjectIDND2="2949@x" ObjectIDZND0="11813@0" Pin0InfoVect0LinkObjId="SW-61724_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-61722_0" Pin1InfoVect1LinkObjId="g_3b38920_0" Pin1InfoVect2LinkObjId="SW-19285_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="626,-4906 626,-4920 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b2b670">
     <polyline DF8003:Layer="0" fill="none" points="626,-4956 626,-4967 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11813@1" ObjectIDZND0="g_3bd3550@0" Pin0InfoVect0LinkObjId="g_3bd3550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-61724_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="626,-4956 626,-4967 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b2fdb0">
     <polyline DF8003:Layer="0" fill="none" points="751,-4906 817,-4906 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="11811@0" ObjectIDZND0="11821@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-61876_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="751,-4906 817,-4906 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b30010">
     <polyline DF8003:Layer="0" fill="none" points="693,-4906 724,-4906 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11812@1" ObjectIDZND0="11811@1" Pin0InfoVect0LinkObjId="SW-61876_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-61722_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="693,-4906 724,-4906 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b31230">
     <polyline DF8003:Layer="0" fill="none" points="1967,-4202 1967,-4187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="1804@0" ObjectIDZND0="1803@1" Pin0InfoVect0LinkObjId="SW-12107_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-12109_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1967,-4202 1967,-4187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b31420">
     <polyline DF8003:Layer="0" fill="none" points="1967,-4160 1967,-4136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="1803@0" ObjectIDZND0="1805@1" Pin0InfoVect0LinkObjId="SW-12111_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-12107_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1967,-4160 1967,-4136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b33eb0">
     <polyline DF8003:Layer="0" fill="none" points="1936,-4020 1936,-4005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3b34110@0" Pin0InfoVect0LinkObjId="g_3b34110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1936,-4020 1936,-4005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b34850">
     <polyline DF8003:Layer="0" fill="none" points="1966,-4082 1935,-4082 1935,-4065 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="1805@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-12111_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1966,-4082 1935,-4082 1935,-4065 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b34ab0">
     <polyline DF8003:Layer="0" fill="none" points="1967,-4082 1967,-4103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="1805@0" Pin0InfoVect0LinkObjId="SW-12111_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1967,-4082 1967,-4103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b376a0">
     <polyline DF8003:Layer="0" fill="none" points="2008,-4019 2008,-3975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2008,-4019 2008,-3975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b37900">
     <polyline DF8003:Layer="0" fill="none" points="2009,-4064 2009,-4082 1966,-4082 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="1805@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-12111_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2009,-4064 2009,-4082 1966,-4082 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b390e0">
     <polyline DF8003:Layer="0" fill="none" points="528,-4976 528,-4965 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3b38920@0" ObjectIDZND0="g_3b383e0@0" Pin0InfoVect0LinkObjId="g_3b383e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b38920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="528,-4976 528,-4965 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b39340">
     <polyline DF8003:Layer="0" fill="none" points="560,-5015 528,-5015 528,-5007 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="2949@x" ObjectIDND1="11813@x" ObjectIDND2="11812@x" ObjectIDZND0="g_3b38920@1" Pin0InfoVect0LinkObjId="g_3b38920_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-19285_0" Pin1InfoVect1LinkObjId="SW-61724_0" Pin1InfoVect2LinkObjId="SW-61722_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="560,-5015 528,-5015 528,-5007 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b3c110">
     <polyline DF8003:Layer="0" fill="none" points="626,-4906 657,-4906 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="11813@x" ObjectIDND1="g_3b38920@0" ObjectIDND2="2949@x" ObjectIDZND0="11812@0" Pin0InfoVect0LinkObjId="SW-61722_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-61724_0" Pin1InfoVect1LinkObjId="g_3b38920_0" Pin1InfoVect2LinkObjId="SW-19285_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="626,-4906 657,-4906 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b40bc0">
     <polyline DF8003:Layer="0" fill="none" points="2259,-4258 2259,-4245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="1727@0" ObjectIDZND0="16785@1" Pin0InfoVect0LinkObjId="SW-77201_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2259,-4258 2259,-4245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b41790">
     <polyline DF8003:Layer="0" fill="none" points="2259,-4209 2259,-4194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16785@0" ObjectIDZND0="16784@1" Pin0InfoVect0LinkObjId="SW-77199_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77201_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2259,-4209 2259,-4194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b41980">
     <polyline DF8003:Layer="0" fill="none" points="2259,-4167 2259,-4143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16784@0" ObjectIDZND0="16786@1" Pin0InfoVect0LinkObjId="SW-77202_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77199_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2259,-4167 2259,-4143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b49330">
     <polyline DF8003:Layer="0" fill="none" points="2259,-3884 2259,-3869 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16801@0" ObjectIDZND0="16799@1" Pin0InfoVect0LinkObjId="SW-77350_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77352_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2259,-3884 2259,-3869 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b49520">
     <polyline DF8003:Layer="0" fill="none" points="2259,-3842 2259,-3818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16799@0" ObjectIDZND0="16800@1" Pin0InfoVect0LinkObjId="SW-77351_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2259,-3842 2259,-3818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b49710">
     <polyline DF8003:Layer="0" fill="none" points="2259,-3764 2259,-3782 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="16797@0" ObjectIDZND0="16800@0" Pin0InfoVect0LinkObjId="SW-77351_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2259,-3764 2259,-3782 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b4ef80">
     <polyline DF8003:Layer="0" fill="none" points="2260,-3931 2277,-3931 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="16801@x" ObjectIDND1="16786@x" ObjectIDZND0="16802@0" Pin0InfoVect0LinkObjId="SW-77353_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-77352_0" Pin1InfoVect1LinkObjId="SW-77202_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2260,-3931 2277,-3931 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b4f1e0">
     <polyline DF8003:Layer="0" fill="none" points="2313,-3931 2326,-3931 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="16802@1" ObjectIDZND0="g_3b4f440@0" Pin0InfoVect0LinkObjId="g_3b4f440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77353_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2313,-3931 2326,-3931 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b50cb0">
     <polyline DF8003:Layer="0" fill="none" points="2259,-3921 2259,-3931 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="16801@1" ObjectIDZND0="16802@x" ObjectIDZND1="16786@x" Pin0InfoVect0LinkObjId="SW-77353_0" Pin0InfoVect1LinkObjId="SW-77202_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77352_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2259,-3921 2259,-3931 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b50ea0">
     <polyline DF8003:Layer="0" fill="none" points="2259,-4107 2259,-3931 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="16786@0" ObjectIDZND0="16802@x" ObjectIDZND1="16801@x" Pin0InfoVect0LinkObjId="SW-77353_0" Pin0InfoVect1LinkObjId="SW-77352_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77202_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2259,-4107 2259,-3931 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b584b0">
     <polyline DF8003:Layer="0" fill="none" points="2253,-4258 2253,-4276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="1727@0" ObjectIDZND0="16788@0" Pin0InfoVect0LinkObjId="SW-77232_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2253,-4258 2253,-4276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b58710">
     <polyline DF8003:Layer="0" fill="none" points="2253,-4312 2253,-4323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16788@1" ObjectIDZND0="16787@0" Pin0InfoVect0LinkObjId="SW-77230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77232_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2253,-4312 2253,-4323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b58970">
     <polyline DF8003:Layer="0" fill="none" points="2253,-4350 2253,-4362 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16787@1" ObjectIDZND0="16789@0" Pin0InfoVect0LinkObjId="SW-77233_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77230_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2253,-4350 2253,-4362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b5c7c0">
     <polyline DF8003:Layer="0" fill="none" points="2271,-4417 2253,-4417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="g_3b5ba10@0" ObjectIDZND0="16789@x" ObjectIDZND1="g_3b58e30@0" ObjectIDZND2="18440@x" Pin0InfoVect0LinkObjId="SW-77233_0" Pin0InfoVect1LinkObjId="g_3b58e30_0" Pin0InfoVect2LinkObjId="SW-83654_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b5ba10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2271,-4417 2253,-4417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b5d510">
     <polyline DF8003:Layer="0" fill="none" points="2253,-4398 2253,-4416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="16789@1" ObjectIDZND0="g_3b5ba10@0" ObjectIDZND1="g_3b58e30@0" ObjectIDZND2="18440@x" Pin0InfoVect0LinkObjId="g_3b5ba10_0" Pin0InfoVect1LinkObjId="g_3b58e30_0" Pin0InfoVect2LinkObjId="SW-83654_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77233_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2253,-4398 2253,-4416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b5d770">
     <polyline DF8003:Layer="0" fill="none" points="2230,-4411 2230,-4417 2253,-4417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3b58e30@0" ObjectIDZND0="16789@x" ObjectIDZND1="g_3b5ba10@0" ObjectIDZND2="18440@x" Pin0InfoVect0LinkObjId="SW-77233_0" Pin0InfoVect1LinkObjId="g_3b5ba10_0" Pin0InfoVect2LinkObjId="SW-83654_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b58e30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2230,-4411 2230,-4417 2253,-4417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b68720">
     <polyline DF8003:Layer="0" fill="none" points="2253,-4731 2253,-4746 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="18439@1" ObjectIDZND0="18426@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83653_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2253,-4731 2253,-4746 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b69a80">
     <polyline DF8003:Layer="0" fill="none" points="2253,-4604 2253,-4615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="18440@1" ObjectIDZND0="18439@x" ObjectIDZND1="0@x" ObjectIDZND2="g_3b66fd0@0" Pin0InfoVect0LinkObjId="SW-83653_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_3b66fd0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83654_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2253,-4604 2253,-4615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b6a5b0">
     <polyline DF8003:Layer="0" fill="none" points="2253,-4417 2253,-4551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="16789@x" ObjectIDND1="g_3b5ba10@0" ObjectIDND2="g_3b58e30@0" ObjectIDZND0="18440@x" ObjectIDZND1="18441@x" Pin0InfoVect0LinkObjId="SW-83654_0" Pin0InfoVect1LinkObjId="SW-83655_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-77233_0" Pin1InfoVect1LinkObjId="g_3b5ba10_0" Pin1InfoVect2LinkObjId="g_3b58e30_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2253,-4417 2253,-4551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b6a810">
     <polyline DF8003:Layer="0" fill="none" points="2253,-4552 2253,-4568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="18441@x" ObjectIDND1="16789@x" ObjectIDND2="g_3b5ba10@0" ObjectIDZND0="18440@0" Pin0InfoVect0LinkObjId="SW-83654_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-83655_0" Pin1InfoVect1LinkObjId="SW-77233_0" Pin1InfoVect2LinkObjId="g_3b5ba10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2253,-4552 2253,-4568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b6aa70">
     <polyline DF8003:Layer="0" fill="none" points="2308,-4552 2297,-4552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3b67c90@0" ObjectIDZND0="18441@1" Pin0InfoVect0LinkObjId="SW-83655_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b67c90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2308,-4552 2297,-4552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b6acd0">
     <polyline DF8003:Layer="0" fill="none" points="2261,-4552 2253,-4552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="18441@0" ObjectIDZND0="18440@x" ObjectIDZND1="16789@x" ObjectIDZND2="g_3b5ba10@0" Pin0InfoVect0LinkObjId="SW-83654_0" Pin0InfoVect1LinkObjId="SW-77233_0" Pin0InfoVect2LinkObjId="g_3b5ba10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-83655_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2261,-4552 2253,-4552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b6c6e0">
     <polyline DF8003:Layer="0" fill="none" points="2253,-4615 2253,-4640 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="18440@x" ObjectIDND1="0@x" ObjectIDND2="g_3b66fd0@0" ObjectIDZND0="18439@0" Pin0InfoVect0LinkObjId="SW-83653_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-83654_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_3b66fd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2253,-4615 2253,-4640 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b71500">
     <polyline DF8003:Layer="0" fill="none" points="823,-3396 837,-3396 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="19032@0" ObjectIDZND0="19030@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87817_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="823,-3396 837,-3396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b72ae0">
     <polyline DF8003:Layer="0" fill="none" points="892,-4399 911,-4399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3c46050@0" ObjectIDZND0="0@x" ObjectIDZND1="1793@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-11936_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c46050_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="892,-4399 911,-4399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b72cd0">
     <polyline DF8003:Layer="0" fill="none" points="911,-4399 948,-4399 948,-4386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="1793@x" ObjectIDND1="g_3c46050@0" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-11936_0" Pin1InfoVect1LinkObjId="g_3c46050_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="911,-4399 948,-4399 948,-4386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b72ec0">
     <polyline DF8003:Layer="0" fill="none" points="911,-4341 911,-4351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="1791@1" ObjectIDZND0="1793@0" Pin0InfoVect0LinkObjId="SW-11936_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-11932_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="911,-4341 911,-4351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b730d0">
     <polyline DF8003:Layer="0" fill="none" points="911,-4387 911,-4399 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="1793@1" ObjectIDZND0="0@x" ObjectIDZND1="g_3c46050@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_3c46050_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-11936_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="911,-4387 911,-4399 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3b902b0">
     <polyline DF8003:Layer="0" fill="none" points="519,-3263 540,-3262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_3b8c7b0@0" Pin0InfoVect0LinkObjId="g_3b8c7b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="519,-3263 540,-3262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b90510">
     <polyline DF8003:Layer="0" fill="none" points="384,-3289 384,-3303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3b8ba80@0" ObjectIDZND0="26897@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-166035_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b8ba80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="384,-3289 384,-3303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bc2760">
     <polyline DF8003:Layer="0" fill="none" points="148,-3800 177,-3800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26859@0" ObjectIDZND0="26911@0" Pin0InfoVect0LinkObjId="SW-166097_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3bc8870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="148,-3800 177,-3800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bc7a30">
     <polyline DF8003:Layer="0" fill="none" points="237,-3800 213,-3800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26910@1" ObjectIDZND0="26911@1" Pin0InfoVect0LinkObjId="SW-166097_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166096_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="237,-3800 213,-3800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bc7c90">
     <polyline DF8003:Layer="0" fill="none" points="280,-3789 280,-3800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="26913@1" ObjectIDZND0="26912@x" ObjectIDZND1="26910@x" Pin0InfoVect0LinkObjId="SW-166098_0" Pin0InfoVect1LinkObjId="SW-166096_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166099_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="280,-3789 280,-3800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bc7ef0">
     <polyline DF8003:Layer="0" fill="none" points="292,-3800 280,-3800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="26912@0" ObjectIDZND0="26913@x" ObjectIDZND1="26910@x" Pin0InfoVect0LinkObjId="SW-166099_0" Pin0InfoVect1LinkObjId="SW-166096_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166098_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="292,-3800 280,-3800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bc8150">
     <polyline DF8003:Layer="0" fill="none" points="280,-3800 264,-3800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26912@x" ObjectIDND1="26913@x" ObjectIDZND0="26910@0" Pin0InfoVect0LinkObjId="SW-166096_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166098_0" Pin1InfoVect1LinkObjId="SW-166099_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="280,-3800 264,-3800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bc83b0">
     <polyline DF8003:Layer="0" fill="none" points="355,-3791 355,-3800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="26914@1" ObjectIDZND0="26912@x" ObjectIDZND1="g_3b79900@0" Pin0InfoVect0LinkObjId="SW-166098_0" Pin0InfoVect1LinkObjId="g_3b79900_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166100_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="355,-3791 355,-3800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3bc8610">
     <polyline DF8003:Layer="0" fill="none" points="432,-3774 453,-3774 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_3b7a630@0" Pin0InfoVect0LinkObjId="g_3b7a630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="432,-3774 453,-3774 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bc8870">
     <polyline DF8003:Layer="0" fill="none" points="183,-3647 148,-3647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26906@0" ObjectIDZND0="26859@0" Pin0InfoVect0LinkObjId="g_3bcb370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166076_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="183,-3647 148,-3647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bc8ad0">
     <polyline DF8003:Layer="0" fill="none" points="239,-3647 219,-3647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26905@1" ObjectIDZND0="26906@1" Pin0InfoVect0LinkObjId="SW-166076_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166075_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="239,-3647 219,-3647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bc8d30">
     <polyline DF8003:Layer="0" fill="none" points="357,-3627 357,-3646 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26909@1" ObjectIDZND0="0@x" ObjectIDZND1="30417@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-199066_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166079_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="357,-3627 357,-3646 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bc8f90">
     <polyline DF8003:Layer="0" fill="none" points="357,-3591 357,-3578 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26909@0" ObjectIDZND0="g_3bbc700@0" Pin0InfoVect0LinkObjId="g_3bbc700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166079_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="357,-3591 357,-3578 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bc91f0">
     <polyline DF8003:Layer="0" fill="none" points="329,-3646 329,-3628 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="26907@1" ObjectIDZND0="g_3b84c70@0" Pin0InfoVect0LinkObjId="g_3b84c70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166077_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="329,-3646 329,-3628 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bc9450">
     <polyline DF8003:Layer="0" fill="none" points="148,-3447 183,-3447 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26963@0" ObjectIDZND0="26901@0" Pin0InfoVect0LinkObjId="SW-166055_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3bcb5d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="148,-3447 183,-3447 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bc96b0">
     <polyline DF8003:Layer="0" fill="none" points="219,-3447 240,-3447 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26901@1" ObjectIDZND0="26900@1" Pin0InfoVect0LinkObjId="SW-166054_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166055_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="219,-3447 240,-3447 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bc9910">
     <polyline DF8003:Layer="0" fill="none" points="283,-3447 283,-3423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26900@x" ObjectIDND1="26902@x" ObjectIDZND0="26903@1" Pin0InfoVect0LinkObjId="SW-166057_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166054_0" Pin1InfoVect1LinkObjId="SW-166056_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="283,-3447 283,-3423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bc9b70">
     <polyline DF8003:Layer="0" fill="none" points="283,-3387 283,-3379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26903@0" ObjectIDZND0="g_3bba810@0" Pin0InfoVect0LinkObjId="g_3bba810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166057_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="283,-3387 283,-3379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bc9dd0">
     <polyline DF8003:Layer="0" fill="none" points="331,-3445 331,-3427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="26902@1" ObjectIDZND0="g_3ba5100@0" Pin0InfoVect0LinkObjId="g_3ba5100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166056_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="331,-3445 331,-3427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bc9ff0">
     <polyline DF8003:Layer="0" fill="none" points="358,-3426 358,-3447 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="26904@1" ObjectIDZND0="19032@x" ObjectIDZND1="28645@x" ObjectIDZND2="g_3a82140@0" Pin0InfoVect0LinkObjId="SW-87817_0" Pin0InfoVect1LinkObjId="SW-188059_0" Pin0InfoVect2LinkObjId="g_3a82140_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166058_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="358,-3426 358,-3447 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bca1e0">
     <polyline DF8003:Layer="0" fill="none" points="358,-3390 358,-3380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26904@0" ObjectIDZND0="g_3bbb260@0" Pin0InfoVect0LinkObjId="g_3bbb260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166058_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="358,-3390 358,-3380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3bca3d0">
     <polyline DF8003:Layer="0" fill="none" points="421,-3407 432,-3407 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_3ba5e30@0" Pin0InfoVect0LinkObjId="g_3ba5e30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="421,-3407 432,-3407 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3bca5c0">
     <polyline DF8003:Layer="0" fill="none" points="421,-3468 426,-3468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="421,-3468 426,-3468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3bca7b0">
     <polyline DF8003:Layer="0" fill="none" points="455,-3608 434,-3608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_3b859a0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b859a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="455,-3608 434,-3608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bca9f0">
     <polyline DF8003:Layer="0" fill="none" points="382,-3608 382,-3647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="26909@x" ObjectIDZND1="30417@x" Pin0InfoVect0LinkObjId="SW-166079_0" Pin0InfoVect1LinkObjId="SW-199066_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="382,-3608 382,-3647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bcac50">
     <polyline DF8003:Layer="0" fill="none" points="281,-3646 281,-3624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="26907@x" ObjectIDND1="26905@x" ObjectIDZND0="26908@1" Pin0InfoVect0LinkObjId="SW-166078_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166077_0" Pin1InfoVect1LinkObjId="SW-166075_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="281,-3646 281,-3624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bcaeb0">
     <polyline DF8003:Layer="0" fill="none" points="281,-3588 281,-3575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26908@0" ObjectIDZND0="g_3bb9370@0" Pin0InfoVect0LinkObjId="g_3bb9370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166078_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="281,-3588 281,-3575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bcb110">
     <polyline DF8003:Layer="0" fill="none" points="204,-3541 204,-3552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26946@1" ObjectIDZND0="26948@1" Pin0InfoVect0LinkObjId="SW-166265_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166262_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="204,-3541 204,-3552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bcb370">
     <polyline DF8003:Layer="0" fill="none" points="168,-3552 148,-3552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26948@0" ObjectIDZND0="26859@0" Pin0InfoVect0LinkObjId="g_3bc8870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166265_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="168,-3552 148,-3552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bcb5d0">
     <polyline DF8003:Layer="0" fill="none" points="168,-3502 148,-3502 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26947@0" ObjectIDZND0="26963@0" Pin0InfoVect0LinkObjId="g_3bcbcf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166264_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="168,-3502 148,-3502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bcb830">
     <polyline DF8003:Layer="0" fill="none" points="204,-3502 204,-3514 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26947@1" ObjectIDZND0="26946@0" Pin0InfoVect0LinkObjId="SW-166262_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166264_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="204,-3502 204,-3514 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bcba90">
     <polyline DF8003:Layer="0" fill="none" points="237,-3303 217,-3303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26895@1" ObjectIDZND0="26896@1" Pin0InfoVect0LinkObjId="SW-166034_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166033_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="237,-3303 217,-3303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bcbcf0">
     <polyline DF8003:Layer="0" fill="none" points="181,-3303 148,-3303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26896@0" ObjectIDZND0="26963@0" Pin0InfoVect0LinkObjId="g_3bcb5d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166034_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="181,-3303 148,-3303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bcbf50">
     <polyline DF8003:Layer="0" fill="none" points="280,-3303 280,-3279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="26897@x" ObjectIDND1="26895@x" ObjectIDZND0="26898@1" Pin0InfoVect0LinkObjId="SW-166036_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166035_0" Pin1InfoVect1LinkObjId="SW-166033_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="280,-3303 280,-3279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bcc1b0">
     <polyline DF8003:Layer="0" fill="none" points="280,-3233 280,-3243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3bbbcb0@0" ObjectIDZND0="26898@0" Pin0InfoVect0LinkObjId="SW-166036_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3bbbcb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="280,-3233 280,-3243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bcc410">
     <polyline DF8003:Layer="0" fill="none" points="280,-3753 280,-3742 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26913@0" ObjectIDZND0="g_3bb8920@0" Pin0InfoVect0LinkObjId="g_3bb8920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166099_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="280,-3753 280,-3742 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bcc670">
     <polyline DF8003:Layer="0" fill="none" points="355,-3800 380,-3800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="26914@x" ObjectIDND1="26912@x" ObjectIDND2="g_3b79900@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-166100_0" Pin1InfoVect1LinkObjId="SW-166098_0" Pin1InfoVect2LinkObjId="g_3b79900_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="355,-3800 380,-3800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bcc8d0">
     <polyline DF8003:Layer="0" fill="none" points="294,-3647 281,-3647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="26907@0" ObjectIDZND0="26908@x" ObjectIDZND1="26905@x" Pin0InfoVect0LinkObjId="SW-166078_0" Pin0InfoVect1LinkObjId="SW-166075_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166077_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="294,-3647 281,-3647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bccb30">
     <polyline DF8003:Layer="0" fill="none" points="281,-3647 266,-3647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26908@x" ObjectIDND1="26907@x" ObjectIDZND0="26905@0" Pin0InfoVect0LinkObjId="SW-166075_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166078_0" Pin1InfoVect1LinkObjId="SW-166077_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="281,-3647 266,-3647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bccd90">
     <polyline DF8003:Layer="0" fill="none" points="330,-3647 357,-3647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDZND0="26909@x" ObjectIDZND1="0@x" ObjectIDZND2="30417@x" Pin0InfoVect0LinkObjId="SW-166079_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-199066_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="330,-3647 357,-3647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bccff0">
     <polyline DF8003:Layer="0" fill="none" points="357,-3647 382,-3647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26909@x" ObjectIDZND0="0@x" ObjectIDZND1="30417@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-199066_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166079_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="357,-3647 382,-3647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bcd250">
     <polyline DF8003:Layer="0" fill="none" points="267,-3447 283,-3447 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26900@0" ObjectIDZND0="26903@x" ObjectIDZND1="26902@x" Pin0InfoVect0LinkObjId="SW-166057_0" Pin0InfoVect1LinkObjId="SW-166056_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166054_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="267,-3447 283,-3447 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bcd4b0">
     <polyline DF8003:Layer="0" fill="none" points="283,-3447 295,-3447 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="26903@x" ObjectIDND1="26900@x" ObjectIDZND0="26902@0" Pin0InfoVect0LinkObjId="SW-166056_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166057_0" Pin1InfoVect1LinkObjId="SW-166054_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="283,-3447 295,-3447 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bcd710">
     <polyline DF8003:Layer="0" fill="none" points="331,-3447 358,-3447 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="breaker" EndDevType2="switch" ObjectIDZND0="26904@x" ObjectIDZND1="19032@x" ObjectIDZND2="28645@x" Pin0InfoVect0LinkObjId="SW-166058_0" Pin0InfoVect1LinkObjId="SW-87817_0" Pin0InfoVect2LinkObjId="SW-188059_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="331,-3447 358,-3447 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bcd970">
     <polyline DF8003:Layer="0" fill="none" points="294,-3303 280,-3303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="26897@0" ObjectIDZND0="26898@x" ObjectIDZND1="26895@x" Pin0InfoVect0LinkObjId="SW-166036_0" Pin0InfoVect1LinkObjId="SW-166033_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166035_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="294,-3303 280,-3303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bcdbd0">
     <polyline DF8003:Layer="0" fill="none" points="280,-3303 264,-3303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26897@x" ObjectIDND1="26898@x" ObjectIDZND0="26895@0" Pin0InfoVect0LinkObjId="SW-166033_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166035_0" Pin1InfoVect1LinkObjId="SW-166036_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="280,-3303 264,-3303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bcde30">
     <polyline DF8003:Layer="0" fill="none" points="328,-3800 334,-3800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="26912@1" ObjectIDZND0="26914@x" ObjectIDZND1="g_3b79900@0" Pin0InfoVect0LinkObjId="SW-166100_0" Pin0InfoVect1LinkObjId="g_3b79900_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166098_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="328,-3800 334,-3800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bce090">
     <polyline DF8003:Layer="0" fill="none" points="334,-3800 355,-3800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="26912@x" ObjectIDND1="g_3b79900@0" ObjectIDZND0="26914@x" Pin0InfoVect0LinkObjId="SW-166100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166098_0" Pin1InfoVect1LinkObjId="g_3b79900_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="334,-3800 355,-3800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bceb80">
     <polyline DF8003:Layer="0" fill="none" points="334,-3782 334,-3800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3b79900@0" ObjectIDZND0="26914@x" ObjectIDZND1="26912@x" Pin0InfoVect0LinkObjId="SW-166100_0" Pin0InfoVect1LinkObjId="SW-166098_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b79900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="334,-3782 334,-3800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bd18b0">
     <polyline DF8003:Layer="0" fill="none" points="355,-3755 355,-3744 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26914@0" ObjectIDZND0="g_3bb9dc0@0" Pin0InfoVect0LinkObjId="g_3bb9dc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="355,-3755 355,-3744 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3bd25a0">
     <polyline DF8003:Layer="0" fill="none" points="942,-4520 942,-4532 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_3bd1b10@0" Pin0InfoVect0LinkObjId="g_3bd1b10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="942,-4520 942,-4532 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bd32f0">
     <polyline DF8003:Layer="0" fill="none" points="560,-5030 560,-5015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="2949@0" ObjectIDZND0="g_3b38920@0" ObjectIDZND1="11813@x" ObjectIDZND2="11812@x" Pin0InfoVect0LinkObjId="g_3b38920_0" Pin0InfoVect1LinkObjId="SW-61724_0" Pin0InfoVect2LinkObjId="SW-61722_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19285_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="560,-5030 560,-5015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3bd9fb0">
     <polyline DF8003:Layer="0" fill="none" points="1039,-4471 1039,-4476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1039,-4471 1039,-4476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3be19b0">
     <polyline DF8003:Layer="0" fill="none" points="2319,-4616 2308,-4616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3be0f20@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3be0f20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2319,-4616 2308,-4616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3be9d50">
     <polyline DF8003:Layer="0" fill="none" points="398,-3975 398,-3986 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="398,-3975 398,-3986 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3be9fb0">
     <polyline DF8003:Layer="0" fill="none" points="398,-4022 398,-4040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="398,-4022 398,-4040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bed990">
     <polyline DF8003:Layer="0" fill="none" points="330,-3303 384,-3303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="26897@1" ObjectIDZND0="g_3b8ba80@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_3b8ba80_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166035_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="330,-3303 384,-3303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bedbf0">
     <polyline DF8003:Layer="0" fill="none" points="382,-3647 523,-3647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="26909@x" ObjectIDND1="0@x" ObjectIDZND0="30417@0" Pin0InfoVect0LinkObjId="SW-199066_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-166079_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="382,-3647 523,-3647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bede50">
     <polyline DF8003:Layer="0" fill="none" points="560,-4906 626,-4906 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3b38920@0" ObjectIDND1="2949@x" ObjectIDND2="g_3bef6c0@0" ObjectIDZND0="11813@x" ObjectIDZND1="11812@x" Pin0InfoVect0LinkObjId="SW-61724_0" Pin0InfoVect1LinkObjId="SW-61722_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3b38920_0" Pin1InfoVect1LinkObjId="SW-19285_0" Pin1InfoVect2LinkObjId="g_3bef6c0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="560,-4906 626,-4906 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bee0b0">
     <polyline DF8003:Layer="0" fill="none" points="560,-4906 560,-5015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="11813@x" ObjectIDND1="11812@x" ObjectIDND2="g_3bef6c0@0" ObjectIDZND0="g_3b38920@0" ObjectIDZND1="2949@x" Pin0InfoVect0LinkObjId="g_3b38920_0" Pin0InfoVect1LinkObjId="SW-19285_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-61724_0" Pin1InfoVect1LinkObjId="SW-61722_0" Pin1InfoVect2LinkObjId="g_3bef6c0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="560,-4906 560,-5015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bee310">
     <polyline DF8003:Layer="0" fill="none" points="489,-3800 380,-3800 380,-3774 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="489,-3800 380,-3800 380,-3774 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3beede0">
     <polyline DF8003:Layer="0" fill="none" points="1044,-4773 1070,-4773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="27248@1" Pin0InfoVect0LinkObjId="SW-171109_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1044,-4773 1070,-4773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3beefd0">
     <polyline DF8003:Layer="0" fill="none" points="1028,-4773 1044,-4773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="27250@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171111_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1028,-4773 1044,-4773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3bf03e0">
     <polyline DF8003:Layer="0" fill="none" points="962,-4761 971,-4761 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="962,-4761 971,-4761 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bf1ff0">
     <polyline DF8003:Layer="0" fill="none" points="936,-4773 989,-4773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_3bef6c0@0" ObjectIDND1="27252@x" ObjectIDND2="28265@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3bef6c0_0" Pin1InfoVect1LinkObjId="SW-171113_0" Pin1InfoVect2LinkObjId="SW-186630_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="936,-4773 989,-4773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bf2250">
     <polyline DF8003:Layer="0" fill="none" points="1170,-4773 1180,-4773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="27244@0" Pin0InfoVect0LinkObjId="g_3bfe8c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1170,-4773 1180,-4773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bf67f0">
     <polyline DF8003:Layer="0" fill="none" points="1044,-4659 1057,-4659 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="30131@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196756_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1044,-4659 1057,-4659 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bf8f80">
     <polyline DF8003:Layer="0" fill="none" points="1057,-4659 1083,-4659 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="30133@0" Pin0InfoVect0LinkObjId="SW-196758_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1057,-4659 1083,-4659 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bf91e0">
     <polyline DF8003:Layer="0" fill="none" points="1006,-4659 1017,-4659 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="30131@1" Pin0InfoVect0LinkObjId="SW-196756_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1006,-4659 1017,-4659 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bf9440">
     <polyline DF8003:Layer="0" fill="none" points="998,-4659 1006,-4659 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="30134@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196759_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="998,-4659 1006,-4659 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3bfa450">
     <polyline DF8003:Layer="0" fill="none" points="924,-4647 933,-4647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="924,-4647 933,-4647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bfb420">
     <polyline DF8003:Layer="0" fill="none" points="898,-4659 962,-4659 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3bf96a0@0" ObjectIDND1="g_3bfeb20@0" ObjectIDND2="30137@x" ObjectIDZND0="30134@0" Pin0InfoVect0LinkObjId="SW-196759_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3bf96a0_0" Pin1InfoVect1LinkObjId="g_3bfeb20_0" Pin1InfoVect2LinkObjId="SW-196762_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="898,-4659 962,-4659 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bfb680">
     <polyline DF8003:Layer="0" fill="none" points="1119,-4659 1135,-4659 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="30133@1" ObjectIDZND0="30132@0" Pin0InfoVect0LinkObjId="SW-196757_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196758_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1119,-4659 1135,-4659 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bfe8c0">
     <polyline DF8003:Layer="0" fill="none" points="1171,-4659 1180,-4659 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="30132@1" ObjectIDZND0="27244@0" Pin0InfoVect0LinkObjId="g_3bf2250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196757_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1171,-4659 1180,-4659 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3c00560">
     <polyline DF8003:Layer="0" fill="none" points="590,-4456 599,-4456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3bff5a0@0" ObjectIDZND0="g_3bffe20@0" Pin0InfoVect0LinkObjId="g_3bffe20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3bff5a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="590,-4456 599,-4456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c044f0">
     <polyline DF8003:Layer="0" fill="none" points="897,-4018 897,-3951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="27025@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166746_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="897,-4018 897,-3951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c04750">
     <polyline DF8003:Layer="0" fill="none" points="897,-3951 897,-3800 665,-3800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="27025@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166746_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="897,-3951 897,-3800 665,-3800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c08950">
     <polyline DF8003:Layer="0" fill="none" points="560,-4482 560,-4461 560,-4456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="switch" ObjectIDZND0="30180@x" ObjectIDZND1="30178@x" Pin0InfoVect0LinkObjId="SW-196901_0" Pin0InfoVect1LinkObjId="SW-196900_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="560,-4482 560,-4461 560,-4456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c08b40">
     <polyline DF8003:Layer="0" fill="none" points="541,-4433 560,-4433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="switch" ObjectIDZND0="30180@x" ObjectIDZND1="30178@x" Pin0InfoVect0LinkObjId="SW-196901_0" Pin0InfoVect1LinkObjId="SW-196900_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="541,-4433 560,-4433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c094b0">
     <polyline DF8003:Layer="0" fill="none" points="560,-4433 560,-4455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="30180@x" ObjectIDND1="30178@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-196901_0" Pin1InfoVect1LinkObjId="SW-196900_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="560,-4433 560,-4455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c0c490">
     <polyline DF8003:Layer="0" fill="none" points="560,-4405 568,-4405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="30178@x" ObjectIDZND0="30180@0" Pin0InfoVect0LinkObjId="SW-196901_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="560,-4405 568,-4405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c0cf80">
     <polyline DF8003:Layer="0" fill="none" points="560,-4405 560,-4433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="30180@x" ObjectIDND1="30178@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-196901_0" Pin1InfoVect1LinkObjId="SW-196900_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="560,-4405 560,-4433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c0d1e0">
     <polyline DF8003:Layer="0" fill="none" points="604,-4405 616,-4405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="30180@1" ObjectIDZND0="g_3c096c0@0" Pin0InfoVect0LinkObjId="g_3c096c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196901_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="604,-4405 616,-4405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c13300">
     <polyline DF8003:Layer="0" fill="none" points="560,-4384 560,-4405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="30178@0" ObjectIDZND0="30180@x" Pin0InfoVect0LinkObjId="SW-196901_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="560,-4384 560,-4405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c13560">
     <polyline DF8003:Layer="0" fill="none" points="560,-4246 560,-4296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="30175@0" ObjectIDZND0="30179@0" Pin0InfoVect0LinkObjId="SW-196900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="560,-4246 560,-4296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c15850">
     <polyline DF8003:Layer="0" fill="none" points="560,-4313 560,-4327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="30179@1" ObjectIDZND0="30177@0" Pin0InfoVect0LinkObjId="SW-196899_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196900_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="560,-4313 560,-4327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c15ab0">
     <polyline DF8003:Layer="0" fill="none" points="560,-4354 560,-4367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="30177@1" ObjectIDZND0="30178@1" Pin0InfoVect0LinkObjId="SW-196900_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196899_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="560,-4354 560,-4367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c15d10">
     <polyline DF8003:Layer="0" fill="none" points="560,-4456 568,-4456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="30180@x" ObjectIDND1="30178@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-196901_0" Pin1InfoVect1LinkObjId="SW-196900_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="560,-4456 568,-4456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c15f70">
     <polyline DF8003:Layer="0" fill="none" points="936,-4773 936,-4743 942,-4743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="27252@x" ObjectIDND1="28265@x" ObjectIDND2="11813@x" ObjectIDZND0="g_3bef6c0@0" Pin0InfoVect0LinkObjId="g_3bef6c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-171113_0" Pin1InfoVect1LinkObjId="SW-186630_0" Pin1InfoVect2LinkObjId="SW-61724_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="936,-4773 936,-4743 942,-4743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c161d0">
     <polyline DF8003:Layer="0" fill="none" points="898,-4659 898,-4629 904,-4629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="30134@x" ObjectIDND1="g_3bfeb20@0" ObjectIDND2="30137@x" ObjectIDZND0="g_3bf96a0@0" Pin0InfoVect0LinkObjId="g_3bf96a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-196759_0" Pin1InfoVect1LinkObjId="g_3bfeb20_0" Pin1InfoVect2LinkObjId="SW-196762_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="898,-4659 898,-4629 904,-4629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c16430">
     <polyline DF8003:Layer="0" fill="none" points="898,-3951 880,-3951 882,-3949 882,-3927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="27025@x" ObjectIDND1="0@x" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-166746_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="898,-3951 880,-3951 882,-3949 882,-3927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3c16f10">
     <polyline DF8003:Layer="0" fill="none" points="369,-3407 369,-3468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="369,-3407 369,-3468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c1b920">
     <polyline DF8003:Layer="0" fill="none" points="1158,-4581 1180,-4581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27255@1" ObjectIDZND0="27244@0" Pin0InfoVect0LinkObjId="g_3bf2250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171131_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1158,-4581 1180,-4581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c1d990">
     <polyline DF8003:Layer="0" fill="none" points="1011,-4581 1051,-4581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27256@1" ObjectIDZND0="27254@1" Pin0InfoVect0LinkObjId="SW-171130_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171132_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1011,-4581 1051,-4581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c1dbf0">
     <polyline DF8003:Layer="0" fill="none" points="1078,-4581 1122,-4581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27254@0" ObjectIDZND0="27255@0" Pin0InfoVect0LinkObjId="SW-171131_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171130_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1078,-4581 1122,-4581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c1e7c0">
     <polyline DF8003:Layer="0" fill="none" points="936,-4777 936,-4791 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3bef6c0@0" ObjectIDND1="28265@x" ObjectIDND2="11813@x" ObjectIDZND0="27252@0" Pin0InfoVect0LinkObjId="SW-171113_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3bef6c0_0" Pin1InfoVect1LinkObjId="SW-186630_0" Pin1InfoVect2LinkObjId="SW-61724_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="936,-4777 936,-4791 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c1e9b0">
     <polyline DF8003:Layer="0" fill="none" points="936,-4827 936,-4838 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27252@1" ObjectIDZND0="g_3c1eba0@0" Pin0InfoVect0LinkObjId="g_3c1eba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171113_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="936,-4827 936,-4838 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c22150">
     <polyline DF8003:Layer="0" fill="none" points="898,-4707 898,-4718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="30137@1" ObjectIDZND0="g_3c22340@0" Pin0InfoVect0LinkObjId="g_3c22340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196762_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="898,-4707 898,-4718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c2ad00">
     <polyline DF8003:Layer="0" fill="none" points="358,-3447 713,-3447 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="26904@x" ObjectIDZND0="19032@x" ObjectIDZND1="28645@x" ObjectIDZND2="g_3a82140@0" Pin0InfoVect0LinkObjId="SW-87817_0" Pin0InfoVect1LinkObjId="SW-188059_0" Pin0InfoVect2LinkObjId="g_3a82140_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-166058_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="358,-3447 713,-3447 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c2aef0">
     <polyline DF8003:Layer="0" fill="none" points="1239,-3479 1259,-3479 1259,-3391 1252,-3391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="breaker" ObjectIDND0="g_3a82140@0" ObjectIDND1="28645@x" ObjectIDND2="19032@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3a82140_0" Pin1InfoVect1LinkObjId="SW-188059_0" Pin1InfoVect2LinkObjId="SW-87817_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1239,-3479 1259,-3479 1259,-3391 1252,-3391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c2b0e0">
     <polyline DF8003:Layer="0" fill="none" points="1220,-3642 1220,-3629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="28722@0" ObjectIDZND0="28644@1" Pin0InfoVect0LinkObjId="SW-188058_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_185deb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1220,-3642 1220,-3629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c2c360">
     <polyline DF8003:Layer="0" fill="none" points="1056,-3592 1035,-3592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="32139@1" ObjectIDZND0="32083@1" Pin0InfoVect0LinkObjId="SW-215572_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1056,-3592 1035,-3592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c35c40">
     <polyline DF8003:Layer="0" fill="none" points="597,-3576 605,-3576 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3c32c80@0" ObjectIDZND0="30413@0" Pin0InfoVect0LinkObjId="SW-199024_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c32c80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="597,-3576 605,-3576 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c35ea0">
     <polyline DF8003:Layer="0" fill="none" points="641,-3576 650,-3576 650,-3592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="30413@1" ObjectIDZND0="30412@x" ObjectIDZND1="30411@x" Pin0InfoVect0LinkObjId="SW-199023_0" Pin0InfoVect1LinkObjId="SW-199022_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199024_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="641,-3576 650,-3576 650,-3592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c36990">
     <polyline DF8003:Layer="0" fill="none" points="625,-3592 650,-3592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="30412@1" ObjectIDZND0="30413@x" ObjectIDZND1="30411@x" Pin0InfoVect0LinkObjId="SW-199024_0" Pin0InfoVect1LinkObjId="SW-199022_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199023_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="625,-3592 650,-3592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c36bf0">
     <polyline DF8003:Layer="0" fill="none" points="650,-3592 658,-3592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="30413@x" ObjectIDND1="30412@x" ObjectIDZND0="30411@1" Pin0InfoVect0LinkObjId="SW-199022_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-199024_0" Pin1InfoVect1LinkObjId="SW-199023_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="650,-3592 658,-3592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c39e10">
     <polyline DF8003:Layer="0" fill="none" points="641,-3628 650,-3628 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3c39380@0" ObjectIDZND0="30415@0" Pin0InfoVect0LinkObjId="SW-199026_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3c39380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="641,-3628 650,-3628 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c3a900">
     <polyline DF8003:Layer="0" fill="none" points="704,-3592 695,-3592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="30414@0" ObjectIDZND0="30411@x" ObjectIDZND1="30415@x" Pin0InfoVect0LinkObjId="SW-199022_0" Pin0InfoVect1LinkObjId="SW-199026_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199025_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="704,-3592 695,-3592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c3ab60">
     <polyline DF8003:Layer="0" fill="none" points="685,-3592 695,-3592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="30411@0" ObjectIDZND0="30414@x" ObjectIDZND1="30415@x" Pin0InfoVect0LinkObjId="SW-199025_0" Pin0InfoVect1LinkObjId="SW-199026_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199022_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="685,-3592 695,-3592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c3dd80">
     <polyline DF8003:Layer="0" fill="none" points="697,-3569 689,-3569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="30416@0" ObjectIDZND0="g_3c3d2f0@0" Pin0InfoVect0LinkObjId="g_3c3d2f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199027_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="697,-3569 689,-3569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c3dfe0">
     <polyline DF8003:Layer="0" fill="none" points="748,-3592 748,-3569 733,-3569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="30414@x" ObjectIDND1="g_3b0fae0@0" ObjectIDND2="g_3b0e8c0@0" ObjectIDZND0="30416@1" Pin0InfoVect0LinkObjId="SW-199027_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-199025_0" Pin1InfoVect1LinkObjId="g_3b0fae0_0" Pin1InfoVect2LinkObjId="g_3b0e8c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="748,-3592 748,-3569 733,-3569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c3ead0">
     <polyline DF8003:Layer="0" fill="none" points="742,-3592 748,-3592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="30414@1" ObjectIDZND0="30416@x" ObjectIDZND1="g_3b0fae0@0" ObjectIDZND2="g_3b0e8c0@0" Pin0InfoVect0LinkObjId="SW-199027_0" Pin0InfoVect1LinkObjId="g_3b0fae0_0" Pin0InfoVect2LinkObjId="g_3b0e8c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199025_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="742,-3592 748,-3592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c3fea0">
     <polyline DF8003:Layer="0" fill="none" points="588,-3592 573,-3592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="30408@0" Pin0InfoVect0LinkObjId="g_3b16230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="588,-3592 573,-3592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c40090">
     <polyline DF8003:Layer="0" fill="none" points="695,-3592 695,-3628 686,-3628 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="30414@x" ObjectIDND1="30411@x" ObjectIDZND0="30415@1" Pin0InfoVect0LinkObjId="SW-199026_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-199025_0" Pin1InfoVect1LinkObjId="SW-199022_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="695,-3592 695,-3628 686,-3628 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c46b50">
     <polyline DF8003:Layer="0" fill="none" points="560,-4512 560,-4659 898,-4659 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3bfeb20@0" ObjectIDZND0="30134@x" ObjectIDZND1="g_3bf96a0@0" ObjectIDZND2="30137@x" Pin0InfoVect0LinkObjId="SW-196759_0" Pin0InfoVect1LinkObjId="g_3bf96a0_0" Pin0InfoVect2LinkObjId="SW-196762_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3bfeb20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="560,-4512 560,-4659 898,-4659 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c46db0">
     <polyline DF8003:Layer="0" fill="none" points="936,-4773 560,-4773 560,-4854 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3bef6c0@0" ObjectIDND1="27252@x" ObjectIDZND0="28265@x" ObjectIDZND1="11813@x" ObjectIDZND2="11812@x" Pin0InfoVect0LinkObjId="SW-186630_0" Pin0InfoVect1LinkObjId="SW-61724_0" Pin0InfoVect2LinkObjId="SW-61722_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3bef6c0_0" Pin1InfoVect1LinkObjId="SW-171113_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="936,-4773 560,-4773 560,-4854 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c47010">
     <polyline DF8003:Layer="0" fill="none" points="639,-4854 764,-4854 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28265@1" ObjectIDZND0="28262@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186630_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="639,-4854 764,-4854 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c4a820">
     <polyline DF8003:Layer="0" fill="none" points="603,-4854 560,-4854 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="28265@0" ObjectIDZND0="g_3bef6c0@0" ObjectIDZND1="27252@x" ObjectIDZND2="11813@x" Pin0InfoVect0LinkObjId="g_3bef6c0_0" Pin0InfoVect1LinkObjId="SW-171113_0" Pin0InfoVect2LinkObjId="SW-61724_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="603,-4854 560,-4854 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c4aa80">
     <polyline DF8003:Layer="0" fill="none" points="560,-4854 560,-4906 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3bef6c0@0" ObjectIDND1="27252@x" ObjectIDND2="28265@x" ObjectIDZND0="11813@x" ObjectIDZND1="11812@x" ObjectIDZND2="g_3b38920@0" Pin0InfoVect0LinkObjId="SW-61724_0" Pin0InfoVect1LinkObjId="SW-61722_0" Pin0InfoVect2LinkObjId="g_3b38920_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3bef6c0_0" Pin1InfoVect1LinkObjId="SW-171113_0" Pin1InfoVect2LinkObjId="SW-186630_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="560,-4854 560,-4906 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c4ace0">
     <polyline DF8003:Layer="0" fill="none" points="898,-4671 898,-4659 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="30137@0" ObjectIDZND0="30134@x" ObjectIDZND1="g_3bf96a0@0" ObjectIDZND2="g_3bfeb20@0" Pin0InfoVect0LinkObjId="SW-196759_0" Pin0InfoVect1LinkObjId="g_3bf96a0_0" Pin0InfoVect2LinkObjId="g_3bfeb20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196762_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="898,-4671 898,-4659 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c4af40">
     <polyline DF8003:Layer="0" fill="none" points="2253,-4616 2272,-4616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="18440@x" ObjectIDND1="18439@x" ObjectIDND2="g_3b66fd0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-83654_0" Pin1InfoVect1LinkObjId="SW-83653_0" Pin1InfoVect2LinkObjId="g_3b66fd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2253,-4616 2272,-4616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c4b1a0">
     <polyline DF8003:Layer="0" fill="none" points="2234,-4613 2253,-4613 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_3b66fd0@0" ObjectIDZND0="18440@x" ObjectIDZND1="18439@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-83654_0" Pin0InfoVect1LinkObjId="SW-83653_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b66fd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2234,-4613 2253,-4613 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c4b400">
     <polyline DF8003:Layer="0" fill="none" points="911,-4399 911,-4471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="1793@x" ObjectIDND2="g_3c46050@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="27256@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-171132_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-11936_0" Pin1InfoVect2LinkObjId="g_3c46050_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="911,-4399 911,-4471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c4bef0">
     <polyline DF8003:Layer="0" fill="none" points="942,-4471 954,-4471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="1793@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-11936_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="942,-4471 954,-4471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c4ccd0">
     <polyline DF8003:Layer="0" fill="none" points="975,-4581 927,-4581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="27256@0" ObjectIDZND0="g_3b73a30@0" ObjectIDZND1="0@x" ObjectIDZND2="1793@x" Pin0InfoVect0LinkObjId="g_3b73a30_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-11936_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-171132_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="975,-4581 927,-4581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c4cf30">
     <polyline DF8003:Layer="0" fill="none" points="937,-4548 927,-4548 927,-4581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3b73a30@0" ObjectIDZND0="27256@x" ObjectIDZND1="0@x" ObjectIDZND2="1793@x" Pin0InfoVect0LinkObjId="SW-171132_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-11936_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b73a30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="937,-4548 927,-4548 927,-4581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c4d190">
     <polyline DF8003:Layer="0" fill="none" points="1369,-3457 1369,-3303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="g_1861b60@0" ObjectIDND2="28641@x" ObjectIDZND0="0@x" ObjectIDZND1="g_3b8ba80@0" ObjectIDZND2="26897@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_3b8ba80_0" Pin0InfoVect2LinkObjId="SW-166035_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1861b60_0" Pin1InfoVect2LinkObjId="SW-188045_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1369,-3457 1369,-3303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c4d3f0">
     <polyline DF8003:Layer="0" fill="none" points="1369,-3479 1369,-3457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_1861b60@0" ObjectIDND2="28641@x" ObjectIDZND0="28642@x" ObjectIDZND1="0@x" ObjectIDZND2="g_3b8ba80@0" Pin0InfoVect0LinkObjId="SW-188046_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_3b8ba80_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1861b60_0" Pin1InfoVect2LinkObjId="SW-188045_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1369,-3479 1369,-3457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c4d650">
     <polyline DF8003:Layer="0" fill="none" points="1369,-3457 1406,-3457 1406,-3448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_1861b60@0" ObjectIDND2="28641@x" ObjectIDZND0="28642@1" Pin0InfoVect0LinkObjId="SW-188046_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1861b60_0" Pin1InfoVect2LinkObjId="SW-188045_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1369,-3457 1406,-3457 1406,-3448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c500e0">
     <polyline DF8003:Layer="0" fill="none" points="942,-4471 911,-4471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="1793@x" ObjectIDZND2="g_3c46050@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-11936_0" Pin0InfoVect2LinkObjId="g_3c46050_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="942,-4471 911,-4471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c50340">
     <polyline DF8003:Layer="0" fill="none" points="911,-4471 911,-4581 927,-4581 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="1793@x" ObjectIDND2="g_3c46050@0" ObjectIDZND0="27256@x" ObjectIDZND1="g_3b73a30@0" Pin0InfoVect0LinkObjId="SW-171132_0" Pin0InfoVect1LinkObjId="g_3b73a30_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-11936_0" Pin1InfoVect2LinkObjId="g_3c46050_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="911,-4471 911,-4581 927,-4581 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c519d0">
     <polyline DF8003:Layer="0" fill="none" points="1828,-3842 1846,-3842 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1828,-3842 1846,-3842 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c51c30">
     <polyline DF8003:Layer="0" fill="none" points="1846,-3842 1846,-3828 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1846,-3842 1846,-3828 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c552e0">
     <polyline DF8003:Layer="0" fill="none" points="467,-3263 451,-3263 451,-3303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_3b8ba80@0" ObjectIDZND1="26897@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_3b8ba80_0" Pin0InfoVect1LinkObjId="SW-166035_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="467,-3263 451,-3263 451,-3303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c55540">
     <polyline DF8003:Layer="0" fill="none" points="451,-3303 384,-3303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_1861b60@0" ObjectIDZND0="g_3b8ba80@0" ObjectIDZND1="26897@x" Pin0InfoVect0LinkObjId="g_3b8ba80_0" Pin0InfoVect1LinkObjId="SW-166035_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_1861b60_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="451,-3303 384,-3303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c557a0">
     <polyline DF8003:Layer="0" fill="none" points="731,-3396 713,-3396 713,-3447 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="19032@1" ObjectIDZND0="26904@x" ObjectIDZND1="28645@x" ObjectIDZND2="g_3a82140@0" Pin0InfoVect0LinkObjId="SW-166058_0" Pin0InfoVect1LinkObjId="SW-188059_0" Pin0InfoVect2LinkObjId="g_3a82140_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87817_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="731,-3396 713,-3396 713,-3447 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3c55fd0">
     <polyline DF8003:Layer="0" fill="none" points="1200,-3391 1189,-3391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1200,-3391 1189,-3391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c5bae0">
     <polyline DF8003:Layer="0" fill="none" points="713,-3447 1220,-3447 1220,-3464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="19032@x" ObjectIDND1="26904@x" ObjectIDZND0="28645@x" ObjectIDZND1="g_3a82140@0" ObjectIDZND2="28646@x" Pin0InfoVect0LinkObjId="SW-188059_0" Pin0InfoVect1LinkObjId="g_3a82140_0" Pin0InfoVect2LinkObjId="SW-188060_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-87817_0" Pin1InfoVect1LinkObjId="SW-166058_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="713,-3447 1220,-3447 1220,-3464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c5e020">
     <polyline DF8003:Layer="0" fill="none" points="845,-3579 845,-3592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="32081@1" ObjectIDZND0="32078@x" ObjectIDZND1="g_3b0fae0@0" ObjectIDZND2="g_3b0e8c0@0" Pin0InfoVect0LinkObjId="SW-215559_0" Pin0InfoVect1LinkObjId="g_3b0fae0_0" Pin0InfoVect2LinkObjId="g_3b0e8c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215562_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="845,-3579 845,-3592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c5eb10">
     <polyline DF8003:Layer="0" fill="none" points="845,-3592 858,-3592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="32081@x" ObjectIDND1="g_3b0fae0@0" ObjectIDND2="g_3b0e8c0@0" ObjectIDZND0="32078@0" Pin0InfoVect0LinkObjId="SW-215559_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-215562_0" Pin1InfoVect1LinkObjId="g_3b0fae0_0" Pin1InfoVect2LinkObjId="g_3b0e8c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="845,-3592 858,-3592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c5ed70">
     <polyline DF8003:Layer="0" fill="none" points="833,-3617 833,-3592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3b0fae0@0" ObjectIDND1="g_3b0e8c0@0" ObjectIDZND0="30416@x" ObjectIDZND1="30414@x" ObjectIDZND2="32081@x" Pin0InfoVect0LinkObjId="SW-199027_0" Pin0InfoVect1LinkObjId="SW-199025_0" Pin0InfoVect2LinkObjId="SW-215562_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3b0fae0_0" Pin1InfoVect1LinkObjId="g_3b0e8c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="833,-3617 833,-3592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c5f860">
     <polyline DF8003:Layer="0" fill="none" points="850,-3638 833,-3638 833,-3617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3b0fae0@0" ObjectIDZND0="30416@x" ObjectIDZND1="30414@x" ObjectIDZND2="32081@x" Pin0InfoVect0LinkObjId="SW-199027_0" Pin0InfoVect1LinkObjId="SW-199025_0" Pin0InfoVect2LinkObjId="SW-215562_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b0fae0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="850,-3638 833,-3638 833,-3617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c5fac0">
     <polyline DF8003:Layer="0" fill="none" points="833,-3617 846,-3617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="30416@x" ObjectIDND1="30414@x" ObjectIDND2="32081@x" ObjectIDZND0="g_3b0e8c0@1" Pin0InfoVect0LinkObjId="g_3b0e8c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-199027_0" Pin1InfoVect1LinkObjId="SW-199025_0" Pin1InfoVect2LinkObjId="SW-215562_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="833,-3617 846,-3617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c605b0">
     <polyline DF8003:Layer="0" fill="none" points="748,-3592 833,-3592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="30416@x" ObjectIDND1="30414@x" ObjectIDZND0="g_3b0fae0@0" ObjectIDZND1="g_3b0e8c0@0" ObjectIDZND2="32081@x" Pin0InfoVect0LinkObjId="g_3b0fae0_0" Pin0InfoVect1LinkObjId="g_3b0e8c0_0" Pin0InfoVect2LinkObjId="SW-215562_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-199027_0" Pin1InfoVect1LinkObjId="SW-199025_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="748,-3592 833,-3592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c60810">
     <polyline DF8003:Layer="0" fill="none" points="833,-3592 845,-3592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3b0fae0@0" ObjectIDND1="g_3b0e8c0@0" ObjectIDND2="30416@x" ObjectIDZND0="32081@x" ObjectIDZND1="32078@x" Pin0InfoVect0LinkObjId="SW-215562_0" Pin0InfoVect1LinkObjId="SW-215559_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3b0fae0_0" Pin1InfoVect1LinkObjId="g_3b0e8c0_0" Pin1InfoVect2LinkObjId="SW-199027_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="833,-3592 845,-3592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c61300">
     <polyline DF8003:Layer="0" fill="none" points="525,-3704 513,-3704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="30418@x" ObjectIDZND1="g_3b07810@0" Pin0InfoVect0LinkObjId="SW-199067_0" Pin0InfoVect1LinkObjId="g_3b07810_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="525,-3704 513,-3704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c61560">
     <polyline DF8003:Layer="0" fill="none" points="513,-3704 502,-3704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="30418@x" ObjectIDND1="0@x" ObjectIDZND0="g_3b07810@0" Pin0InfoVect0LinkObjId="g_3b07810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-199067_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="513,-3704 502,-3704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c62050">
     <polyline DF8003:Layer="0" fill="none" points="1515,-3981 1846,-3981 1846,-3842 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1515,-3981 1846,-3981 1846,-3842 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c62b40">
     <polyline DF8003:Layer="0" fill="none" points="1967,-4082 1967,-3303 1369,-3303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="1805@x" ObjectIDND2="0@x" ObjectIDZND0="0@x" ObjectIDZND1="g_1861b60@0" ObjectIDZND2="28641@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1861b60_0" Pin0InfoVect2LinkObjId="SW-188045_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-12111_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1967,-4082 1967,-3303 1369,-3303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c62da0">
     <polyline DF8003:Layer="0" fill="none" points="1084,-4006 1084,-3981 1515,-3981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3ad87f0@0" ObjectIDND1="0@x" ObjectIDND2="27022@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ad87f0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-166728_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1084,-4006 1084,-3981 1515,-3981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c63000">
     <polyline DF8003:Layer="0" fill="none" points="451,-3303 1369,-3303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="g_3b8ba80@0" ObjectIDND2="26897@x" ObjectIDZND0="0@x" ObjectIDZND1="g_1861b60@0" ObjectIDZND2="28641@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1861b60_0" Pin0InfoVect2LinkObjId="SW-188045_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3b8ba80_0" Pin1InfoVect2LinkObjId="SW-166035_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="451,-3303 1369,-3303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c63af0">
     <polyline DF8003:Layer="0" fill="none" points="1220,-3479 1220,-3464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="28645@x" ObjectIDND1="g_3a82140@0" ObjectIDZND0="19032@x" ObjectIDZND1="26904@x" ObjectIDZND2="28646@x" Pin0InfoVect0LinkObjId="SW-87817_0" Pin0InfoVect1LinkObjId="SW-166058_0" Pin0InfoVect2LinkObjId="SW-188060_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188059_0" Pin1InfoVect1LinkObjId="g_3a82140_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1220,-3479 1220,-3464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c63d50">
     <polyline DF8003:Layer="0" fill="none" points="1220,-3464 1199,-3464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="19032@x" ObjectIDND1="26904@x" ObjectIDND2="28645@x" ObjectIDZND0="28646@1" Pin0InfoVect0LinkObjId="SW-188060_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-87817_0" Pin1InfoVect1LinkObjId="SW-166058_0" Pin1InfoVect2LinkObjId="SW-188059_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1220,-3464 1199,-3464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cbcaf0">
     <polyline DF8003:Layer="0" fill="none" points="1704,-4895 1704,-4906 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_3cea1c0@0" ObjectIDND1="27190@x" ObjectIDND2="g_3cea1c0@0" ObjectIDZND0="27190@0" Pin0InfoVect0LinkObjId="SW-170314_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3cea1c0_0" Pin1InfoVect1LinkObjId="SW-170314_0" Pin1InfoVect2LinkObjId="g_3cea1c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1704,-4895 1704,-4906 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cbcd50">
     <polyline DF8003:Layer="0" fill="none" points="1704,-4942 1704,-4956 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="27190@1" ObjectIDZND0="g_3cbcfb0@0" Pin0InfoVect0LinkObjId="g_3cbcfb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170314_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1704,-4942 1704,-4956 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cc1af0">
     <polyline DF8003:Layer="0" fill="none" points="1761,-4895 1775,-4895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27189@1" ObjectIDZND0="27187@1" Pin0InfoVect0LinkObjId="SW-170310_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170313_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1761,-4895 1775,-4895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cc4280">
     <polyline DF8003:Layer="0" fill="none" points="1802,-4895 1828,-4895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="27187@0" ObjectIDZND0="27188@0" Pin0InfoVect0LinkObjId="SW-170312_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1802,-4895 1828,-4895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cc44e0">
     <polyline DF8003:Layer="0" fill="none" points="1864,-4895 1886,-4895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27188@1" ObjectIDZND0="27184@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-170312_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1864,-4895 1886,-4895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cc6250">
     <polyline DF8003:Layer="0" fill="none" points="1279,-4985 1363,-4985 1363,-4969 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="37490@x" ObjectIDND1="27106@x" ObjectIDND2="37487@x" ObjectIDZND0="g_3cc6750@0" Pin0InfoVect0LinkObjId="g_3cc6750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-225630_0" Pin1InfoVect1LinkObjId="SW-169478_0" Pin1InfoVect2LinkObjId="SW-225671_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1279,-4985 1363,-4985 1363,-4969 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ccafe0">
     <polyline DF8003:Layer="0" fill="none" points="1279,-5002 1297,-5002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="27106@x" ObjectIDND1="g_3cc6750@0" ObjectIDND2="37487@x" ObjectIDZND0="37490@0" Pin0InfoVect0LinkObjId="SW-225630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-169478_0" Pin1InfoVect1LinkObjId="g_3cc6750_0" Pin1InfoVect2LinkObjId="SW-225671_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1279,-5002 1297,-5002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ccb240">
     <polyline DF8003:Layer="0" fill="none" points="1333,-5002 1350,-5002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="37490@1" ObjectIDZND0="g_3cca550@0" Pin0InfoVect0LinkObjId="g_3cca550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225630_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1333,-5002 1350,-5002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ccb4a0">
     <polyline DF8003:Layer="0" fill="none" points="1496,-5157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1496,-5157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cce860">
     <polyline DF8003:Layer="0" fill="none" points="1474,-5003 1490,-5003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="37491@x" ObjectIDND1="37493@x" ObjectIDND2="37496@x" ObjectIDZND0="37496@0" Pin0InfoVect0LinkObjId="SW-225702_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-225700_0" Pin1InfoVect1LinkObjId="SW-225703_0" Pin1InfoVect2LinkObjId="SW-225702_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1474,-5003 1490,-5003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cceac0">
     <polyline DF8003:Layer="0" fill="none" points="1526,-5003 1541,-5003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="37496@1" ObjectIDZND0="g_3ccb8a0@0" Pin0InfoVect0LinkObjId="g_3ccb8a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225702_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1526,-5003 1541,-5003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cdd200">
     <polyline DF8003:Layer="0" fill="none" points="1279,-5177 1279,-5163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27088@0" ObjectIDZND0="27105@1" Pin0InfoVect0LinkObjId="SW-169477_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1279,-5177 1279,-5163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cdd460">
     <polyline DF8003:Layer="0" fill="none" points="1279,-5127 1279,-5110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27105@0" ObjectIDZND0="37497@1" Pin0InfoVect0LinkObjId="SW-225670_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169477_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1279,-5127 1279,-5110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cdd6c0">
     <polyline DF8003:Layer="0" fill="none" points="1279,-5083 1279,-5070 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="37497@0" ObjectIDZND0="27106@1" Pin0InfoVect0LinkObjId="SW-169478_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1279,-5083 1279,-5070 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cdd920">
     <polyline DF8003:Layer="0" fill="none" points="1474,-5177 1474,-5161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="27088@0" ObjectIDZND0="37492@1" Pin0InfoVect0LinkObjId="SW-225698_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1474,-5177 1474,-5161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cddb80">
     <polyline DF8003:Layer="0" fill="none" points="1474,-5125 1474,-5108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37492@0" ObjectIDZND0="37498@1" Pin0InfoVect0LinkObjId="SW-225697_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225698_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1474,-5125 1474,-5108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3cde710">
     <polyline DF8003:Layer="0" fill="none" points="1886,-4843 1857,-4843 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="27184@0" ObjectIDZND0="g_3cdea20@0" Pin0InfoVect0LinkObjId="g_3cdea20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3cc44e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1886,-4843 1857,-4843 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ce1680">
     <polyline DF8003:Layer="0" fill="none" points="1680,-4895 1704,-4895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3cea1c0@0" ObjectIDND1="27190@x" ObjectIDND2="g_3cea1c0@0" ObjectIDZND0="27190@x" ObjectIDZND1="27189@x" ObjectIDZND2="27190@x" Pin0InfoVect0LinkObjId="SW-170314_0" Pin0InfoVect1LinkObjId="SW-170313_0" Pin0InfoVect2LinkObjId="SW-170314_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3cea1c0_0" Pin1InfoVect1LinkObjId="SW-170314_0" Pin1InfoVect2LinkObjId="g_3cea1c0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1680,-4895 1704,-4895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ce1870">
     <polyline DF8003:Layer="0" fill="none" points="1704,-4895 1725,-4895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="27190@x" ObjectIDND1="g_3cea1c0@0" ObjectIDND2="27190@x" ObjectIDZND0="27189@0" Pin0InfoVect0LinkObjId="SW-170313_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-170314_0" Pin1InfoVect1LinkObjId="g_3cea1c0_0" Pin1InfoVect2LinkObjId="SW-170314_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1704,-4895 1725,-4895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ce1a60">
     <polyline DF8003:Layer="0" fill="none" points="1474,-5081 1474,-5064 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="37498@0" ObjectIDZND0="37491@1" Pin0InfoVect0LinkObjId="SW-225700_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225697_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1474,-5081 1474,-5064 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ce2f00">
     <polyline DF8003:Layer="0" fill="none" points="1474,-4962 1474,-4951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="37493@x" ObjectIDND1="37496@x" ObjectIDND2="37491@x" ObjectIDZND0="37493@x" ObjectIDZND1="37496@x" ObjectIDZND2="37491@x" Pin0InfoVect0LinkObjId="SW-225703_0" Pin0InfoVect1LinkObjId="SW-225702_0" Pin0InfoVect2LinkObjId="SW-225700_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-225703_0" Pin1InfoVect1LinkObjId="SW-225702_0" Pin1InfoVect2LinkObjId="SW-225700_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1474,-4962 1474,-4951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ce80f0">
     <polyline DF8003:Layer="0" fill="none" points="1313,-4917 1313,-4898 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="37487@0" ObjectIDZND0="g_3ce21d0@0" Pin0InfoVect0LinkObjId="g_3ce21d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225671_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-4917 1313,-4898 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ce9130">
     <polyline DF8003:Layer="0" fill="none" points="1474,-4961 1494,-4961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="37493@x" ObjectIDND1="37496@x" ObjectIDND2="37491@x" ObjectIDZND0="37493@0" Pin0InfoVect0LinkObjId="SW-225703_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-225703_0" Pin1InfoVect1LinkObjId="SW-225702_0" Pin1InfoVect2LinkObjId="SW-225700_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1474,-4961 1494,-4961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ce9390">
     <polyline DF8003:Layer="0" fill="none" points="1530,-4961 1548,-4961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="37493@1" ObjectIDZND0="g_3ce8930@0" Pin0InfoVect0LinkObjId="g_3ce8930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225703_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1530,-4961 1548,-4961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ce9e70">
     <polyline DF8003:Layer="0" fill="none" points="1680,-4895 1680,-4878 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="27190@x" ObjectIDND1="27189@x" ObjectIDND2="27190@x" ObjectIDZND0="g_3cea1c0@0" Pin0InfoVect0LinkObjId="g_3cea1c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-170314_0" Pin1InfoVect1LinkObjId="SW-170313_0" Pin1InfoVect2LinkObjId="SW-170314_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1680,-4895 1680,-4878 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c670a0">
     <polyline DF8003:Layer="0" fill="none" points="1279,-5034 1279,-5002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="27106@0" ObjectIDZND0="37490@x" ObjectIDZND1="g_3cc6750@0" ObjectIDZND2="37487@x" Pin0InfoVect0LinkObjId="SW-225630_0" Pin0InfoVect1LinkObjId="g_3cc6750_0" Pin0InfoVect2LinkObjId="SW-225671_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-169478_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1279,-5034 1279,-5002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d159a0">
     <polyline DF8003:Layer="0" fill="none" points="1279,-5002 1279,-4985 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="37490@x" ObjectIDND1="27106@x" ObjectIDZND0="g_3cc6750@0" ObjectIDZND1="37487@x" Pin0InfoVect0LinkObjId="g_3cc6750_0" Pin0InfoVect1LinkObjId="SW-225671_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-225630_0" Pin1InfoVect1LinkObjId="SW-169478_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1279,-5002 1279,-4985 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3c67290">
     <polyline DF8003:Layer="0" fill="none" points="1474,-5028 1474,-5003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="37491@0" ObjectIDZND0="37496@x" ObjectIDZND1="37493@x" ObjectIDZND2="37496@x" Pin0InfoVect0LinkObjId="SW-225702_0" Pin0InfoVect1LinkObjId="SW-225703_0" Pin0InfoVect2LinkObjId="SW-225702_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1474,-5028 1474,-5003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d15c10">
     <polyline DF8003:Layer="0" fill="none" points="1474,-5003 1474,-4961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="37496@x" ObjectIDND1="37491@x" ObjectIDZND0="37493@x" ObjectIDZND1="37496@x" ObjectIDZND2="37491@x" Pin0InfoVect0LinkObjId="SW-225703_0" Pin0InfoVect1LinkObjId="SW-225702_0" Pin0InfoVect2LinkObjId="SW-225700_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-225702_0" Pin1InfoVect1LinkObjId="SW-225700_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1474,-5003 1474,-4961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d16940">
     <polyline DF8003:Layer="0" fill="none" points="1474,-4961 1474,-4951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="37493@x" ObjectIDND1="37496@x" ObjectIDND2="37491@x" ObjectIDZND0="37493@x" ObjectIDZND1="37496@x" ObjectIDZND2="37491@x" Pin0InfoVect0LinkObjId="SW-225703_0" Pin0InfoVect1LinkObjId="SW-225702_0" Pin0InfoVect2LinkObjId="SW-225700_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-225703_0" Pin1InfoVect1LinkObjId="SW-225702_0" Pin1InfoVect2LinkObjId="SW-225700_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1474,-4961 1474,-4951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d164b0">
     <polyline DF8003:Layer="0" fill="none" points="1680,-4895 1704,-4895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="27190@x" ObjectIDND1="27189@x" ObjectIDND2="g_3cea1c0@0" ObjectIDZND0="27190@x" ObjectIDZND1="g_3cea1c0@0" ObjectIDZND2="27189@x" Pin0InfoVect0LinkObjId="SW-170314_0" Pin0InfoVect1LinkObjId="g_3cea1c0_0" Pin0InfoVect2LinkObjId="SW-170313_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-170314_0" Pin1InfoVect1LinkObjId="SW-170313_0" Pin1InfoVect2LinkObjId="g_3cea1c0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1680,-4895 1704,-4895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ce9a90">
     <polyline DF8003:Layer="0" fill="none" points="1680,-4895 1474,-4895 1474,-4951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="27190@x" ObjectIDND1="27189@x" ObjectIDND2="27190@x" ObjectIDZND0="37493@x" ObjectIDZND1="37496@x" ObjectIDZND2="37491@x" Pin0InfoVect0LinkObjId="SW-225703_0" Pin0InfoVect1LinkObjId="SW-225702_0" Pin0InfoVect2LinkObjId="SW-225700_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-170314_0" Pin1InfoVect1LinkObjId="SW-170313_0" Pin1InfoVect2LinkObjId="SW-170314_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1680,-4895 1474,-4895 1474,-4951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ebaee0">
     <polyline DF8003:Layer="0" fill="none" points="1279,-5131 1279,-4419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDZND0="g_38119e0@0" ObjectIDZND1="0@x" ObjectIDZND2="1799@x" Pin0InfoVect0LinkObjId="g_38119e0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-12023_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1279,-5131 1279,-4419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e87a20">
     <polyline DF8003:Layer="0" fill="none" points="1313,-4953 1313,-4961 1279,-4961 1279,-4985 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="37487@1" ObjectIDZND0="g_3cc6750@0" ObjectIDZND1="37490@x" ObjectIDZND2="27106@x" Pin0InfoVect0LinkObjId="g_3cc6750_0" Pin0InfoVect1LinkObjId="SW-225630_0" Pin0InfoVect2LinkObjId="SW-169478_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-225671_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-4953 1313,-4961 1279,-4961 1279,-4985 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="0:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer10="$AUDIT-BAD-LAYER:0.000000 0.000000" layer11="图层2:0.000000 0.000000" layer12="GDXT:0.000000 0.000000" layer13="Defpoints:0.000000 0.000000" layer14="yc:0.000000 0.000000" layer15="PUBLIC:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layer4="$AUDIT-BAD-LAYER:0.000000 0.000000" layer5="图层2:0.000000 0.000000" layer6="GDXT:0.000000 0.000000" layer7="Defpoints:0.000000 0.000000" layer8="yc:0.000000 0.000000" layer9="0:0.000000 0.000000" layerN="16" moveAndZoomFlag="1"/>
</svg>