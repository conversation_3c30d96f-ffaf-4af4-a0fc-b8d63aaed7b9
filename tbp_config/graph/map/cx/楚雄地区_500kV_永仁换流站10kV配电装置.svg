<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-183" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3118 -1199 2190 1307">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="99" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="currentTransformer:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="25" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="42" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="51" x2="51" y1="43" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="55" x2="55" y1="34" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="58" x2="58" y1="35" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="35" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="35" x2="35" y1="31" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="42" x2="42" y1="31" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="16" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="16" x2="16" y1="31" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="23" x2="23" y1="31" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="36" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="36" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="7" y2="4"/>
    <circle cx="41" cy="17" r="7.5" stroke-width="1"/>
    <circle cx="17" cy="17" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="26" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="20" y1="9" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="26" y1="9" y2="5"/>
    <circle cx="35" cy="7" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="42" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="45" x2="42" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="30" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="30" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="30" y1="18" y2="15"/>
    <circle cx="29" cy="17" r="7.5" stroke-width="1"/>
    <circle cx="23" cy="7" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="17" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="17" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="18" y2="15"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape101">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="39" y2="44"/>
    <polyline DF8003:Layer="PUBLIC" points="16,70 10,58 22,58 16,70 16,69 16,70 "/>
    <circle cx="16" cy="61" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="38" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="8" y2="8"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,39 40,39 40,8 " stroke-width="1"/>
    <circle cx="16" cy="39" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="39" y2="34"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="load:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="6" y2="15"/>
    <polyline DF8003:Layer="PUBLIC" points="1,15 10,15 5,25 0,15 1,15 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="15" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="15" y2="25"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape18_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="13" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape18_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="5" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor1">
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="14" y2="65"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="66"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
   </symbol>
   <symbol id="switch2:shape25_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape13_0">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="29" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="46" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape13_1">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="37" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="37" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape48_0">
    <ellipse cx="25" cy="29" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape48_1">
    <circle cx="25" cy="61" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="33" y1="59" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="16" y1="75" y2="59"/>
   </symbol>
   <symbol id="voltageTransformer:shape104">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="39" y1="41" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="36" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="38" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="37" y1="43" y2="43"/>
    <ellipse cx="8" cy="12" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="8" cy="24" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="10" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="4" y1="10" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="10" y2="14"/>
    <ellipse cx="19" cy="12" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <ellipse cx="19" cy="24" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="44" y1="19" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="44" y1="28" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="39" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="39" y1="12" y2="18"/>
    <rect height="13" stroke-width="1" width="5" x="37" y="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape106">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="6" y2="16"/>
    <rect height="13" stroke-width="1" width="5" x="3" y="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="35" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="6" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="1" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="1" y1="28" y2="19"/>
    <ellipse cx="25" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="34" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="25" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="25" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="37" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="37" y1="24" y2="22"/>
    <ellipse cx="25" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="34" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="40" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="34" y1="33" y2="33"/>
    <ellipse cx="36" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="36" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="25" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="25" y1="24" y2="22"/>
   </symbol>
   <symbol id="voltageTransformer:shape105">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="32" y1="33" y2="33"/>
    <ellipse cx="22" cy="14" rx="7.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="30" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="46" x2="46" y1="32" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="44" y1="31" y2="35"/>
    <ellipse cx="12" cy="20" rx="7.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="21" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="27" y1="16" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="21" y1="16" y2="12"/>
    <ellipse cx="12" cy="8" rx="7.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="13" y1="24" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="13" y1="18" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="16" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="13" y1="11" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="13" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="16" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="29" y1="38" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="32" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="33" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="19" y1="33" y2="33"/>
    <rect height="5" stroke-width="1" width="13" x="19" y="30"/>
   </symbol>
   <symbol id="voltageTransformer:shape107">
    <ellipse cx="14" cy="25" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="6" y2="16"/>
    <rect height="13" stroke-width="1" width="5" x="30" y="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="35" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="21" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="28" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="28" y1="28" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="35" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="8" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="8" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="21" y1="35" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="21" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="21" y1="37" y2="35"/>
    <ellipse cx="8" cy="35" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="12" y1="25" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="12" y1="23" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="12" y1="21" y2="27"/>
    <ellipse cx="20" cy="35" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="33" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="36" y1="6" y2="6"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_47aea20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_47af360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_47afd50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_47b0a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_47b1990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_47b25b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_47b2cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_47b3590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_47b3cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="55" stroke="rgb(255,0,0)" stroke-width="9.28571" width="98" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_47b4610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_47b4610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,35)">二种工作</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_47b5c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_47b5c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_47b66f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_47b83a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_47b8fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_47b9960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_47ba270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_47bba60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_47bc260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_47bc950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_47bd0e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_47be1c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_47beba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_47bf690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_47c46e0" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_47c5120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_47c5a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_47c2590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_47c3050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_47ca7c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_47c71a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1317" width="2200" x="3113" y="-1204"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3906" x2="3916" y1="-147" y2="-137"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5138" x2="5148" y1="-149" y2="-139"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3120" y="-1198"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3119" y="-1078"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3119" y="-598"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="28" stroke="rgb(60,120,255)" stroke-width="1" width="11" x="4251" y="-482"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="28" stroke="rgb(0,255,0)" stroke-width="1" width="11" x="3613" y="-791"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="28" stroke="rgb(60,120,255)" stroke-width="1" width="11" x="3806" y="-706"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="11" stroke="rgb(0,255,0)" stroke-width="1" width="28" x="4266" y="-712"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="28" stroke="rgb(0,255,0)" stroke-width="1" width="11" x="4752" y="-791"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="28" stroke="rgb(60,120,255)" stroke-width="1" width="11" x="4942" y="-707"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="16" stroke="rgb(60,120,255)" stroke-width="1" width="36" x="3870" y="16"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="16" stroke="rgb(60,120,255)" stroke-width="1" width="36" x="5102" y="14"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-127261">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3452.000000 -423.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23416" ObjectName="SW-CX_YRH.CX_YRH_05167SW"/>
     <cge:Meas_Ref ObjectId="127261"/>
    <cge:TPSR_Ref TObjectID="23416"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127262">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3580.000000 -422.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23417" ObjectName="SW-CX_YRH.CX_YRH_05267SW"/>
     <cge:Meas_Ref ObjectId="127262"/>
    <cge:TPSR_Ref TObjectID="23417"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127263">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3708.000000 -422.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23418" ObjectName="SW-CX_YRH.CX_YRH_05367SW"/>
     <cge:Meas_Ref ObjectId="127263"/>
    <cge:TPSR_Ref TObjectID="23418"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127264">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3836.000000 -422.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23419" ObjectName="SW-CX_YRH.CX_YRH_05467SW"/>
     <cge:Meas_Ref ObjectId="127264"/>
    <cge:TPSR_Ref TObjectID="23419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127265">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3959.000000 -466.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23420" ObjectName="SW-CX_YRH.CX_YRH_0121SW"/>
     <cge:Meas_Ref ObjectId="127265"/>
    <cge:TPSR_Ref TObjectID="23420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127282">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4166.925566 -468.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23437" ObjectName="SW-CX_YRH.CX_YRH_0133SW"/>
     <cge:Meas_Ref ObjectId="127282"/>
    <cge:TPSR_Ref TObjectID="23437"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4246.582524 -518.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127281">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4374.925566 -468.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23436" ObjectName="SW-CX_YRH.CX_YRH_0233SW"/>
     <cge:Meas_Ref ObjectId="127281"/>
    <cge:TPSR_Ref TObjectID="23436"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127272">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4684.000000 -424.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23427" ObjectName="SW-CX_YRH.CX_YRH_06167SW"/>
     <cge:Meas_Ref ObjectId="127272"/>
    <cge:TPSR_Ref TObjectID="23427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127273">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4812.000000 -424.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23428" ObjectName="SW-CX_YRH.CX_YRH_06267SW"/>
     <cge:Meas_Ref ObjectId="127273"/>
    <cge:TPSR_Ref TObjectID="23428"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127274">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4940.000000 -424.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23429" ObjectName="SW-CX_YRH.CX_YRH_06367SW"/>
     <cge:Meas_Ref ObjectId="127274"/>
    <cge:TPSR_Ref TObjectID="23429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127275">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5068.000000 -424.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23430" ObjectName="SW-CX_YRH.CX_YRH_06467SW"/>
     <cge:Meas_Ref ObjectId="127275"/>
    <cge:TPSR_Ref TObjectID="23430"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127260">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3630.000000 -839.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23415" ObjectName="SW-CX_YRH.CX_YRH_0167SW"/>
     <cge:Meas_Ref ObjectId="127260"/>
    <cge:TPSR_Ref TObjectID="23415"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3802.000000 -605.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127280">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4176.000000 -715.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23435" ObjectName="SW-CX_YRH.CX_YRH_0367SW"/>
     <cge:Meas_Ref ObjectId="127280"/>
    <cge:TPSR_Ref TObjectID="23435"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127254">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4175.000000 -927.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23409" ObjectName="SW-CX_YRH.CX_YRH_31167SW"/>
     <cge:Meas_Ref ObjectId="127254"/>
    <cge:TPSR_Ref TObjectID="23409"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127253">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4175.000000 -993.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23408" ObjectName="SW-CX_YRH.CX_YRH_31110SW"/>
     <cge:Meas_Ref ObjectId="127253"/>
    <cge:TPSR_Ref TObjectID="23408"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127252">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4174.000000 -1066.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23407" ObjectName="SW-CX_YRH.CX_YRH_31117SW"/>
     <cge:Meas_Ref ObjectId="127252"/>
    <cge:TPSR_Ref TObjectID="23407"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127251">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4219.000000 -1013.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23406" ObjectName="SW-CX_YRH.CX_YRH_3111SW"/>
     <cge:Meas_Ref ObjectId="127251"/>
    <cge:TPSR_Ref TObjectID="23406"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127271">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4769.000000 -839.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23426" ObjectName="SW-CX_YRH.CX_YRH_00267SW"/>
     <cge:Meas_Ref ObjectId="127271"/>
    <cge:TPSR_Ref TObjectID="23426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4938.000000 -606.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3880.000000 -266.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23916" ObjectName="SW-CX_YRH.CX_YRH_0546SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="23916"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3882.000000 -125.000000)" xlink:href="#switch2:shape25_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23917" ObjectName="SW-CX_YRH.CX_YRH_0816SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="23917"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5112.000000 -268.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23918" ObjectName="SW-CX_YRH.CX_YRH_0646SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="23918"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5114.000000 -127.000000)" xlink:href="#switch2:shape25_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23919" ObjectName="SW-CX_YRH.CX_YRH_0826SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="23919"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3f8eb40">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3793.000000 -720.000000)" xlink:href="#voltageTransformer:shape104"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_409d650">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4929.000000 -721.000000)" xlink:href="#voltageTransformer:shape104"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40af9c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4232.000000 -387.000000)" xlink:href="#voltageTransformer:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37aa170">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4334.000000 -698.000000)" xlink:href="#voltageTransformer:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_406c7f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3611.000000 -696.000000)" xlink:href="#voltageTransformer:shape107"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f595d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4750.000000 -696.000000)" xlink:href="#voltageTransformer:shape107"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3678.000000 -1129.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4223.000000 -1141.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4817.000000 -1129.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_40da560" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3429.000000 -422.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40236f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3553.000000 -421.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4054350" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3681.000000 -421.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fd9630" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3809.000000 -421.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f7e880" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4657.000000 -423.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fa0120" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4785.000000 -423.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37054c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4913.000000 -423.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3d2d0f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5041.000000 -423.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32ea640" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3603.000000 -838.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e73ea0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4149.000000 -714.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f87490" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4148.000000 -926.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4034bb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4148.000000 -992.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4042430" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4147.000000 -1065.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2965b80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4742.000000 -838.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_3fc93d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3505,-567 3505,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="23587@0" ObjectIDZND0="23411@0" Pin0InfoVect0LinkObjId="SW-127256_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3505,-567 3505,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33873a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3505,-428 3493,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="g_3218c00@0" ObjectIDND1="g_33871b0@0" ObjectIDND2="23411@x" ObjectIDZND0="23416@1" Pin0InfoVect0LinkObjId="SW-127261_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3218c00_0" Pin1InfoVect1LinkObjId="g_33871b0_0" Pin1InfoVect2LinkObjId="SW-127256_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3505,-428 3493,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e70e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3505,-396 3487,-396 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="23416@x" ObjectIDND1="23411@x" ObjectIDND2="g_33871b0@0" ObjectIDZND0="g_3218c00@0" Pin0InfoVect0LinkObjId="g_3218c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127261_0" Pin1InfoVect1LinkObjId="SW-127256_0" Pin1InfoVect2LinkObjId="g_33871b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3505,-396 3487,-396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31f0cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3505,-428 3505,-396 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="23416@x" ObjectIDND1="23411@x" ObjectIDZND0="g_3218c00@0" ObjectIDZND1="g_33871b0@0" Pin0InfoVect0LinkObjId="g_3218c00_0" Pin0InfoVect1LinkObjId="g_33871b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127261_0" Pin1InfoVect1LinkObjId="SW-127256_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3505,-428 3505,-396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40c3090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3505,-396 3505,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="lightningRod" ObjectIDND0="g_3218c00@0" ObjectIDND1="23416@x" ObjectIDND2="23411@x" ObjectIDZND0="g_33871b0@0" Pin0InfoVect0LinkObjId="g_33871b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3218c00_0" Pin1InfoVect1LinkObjId="SW-127261_0" Pin1InfoVect2LinkObjId="SW-127256_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3505,-396 3505,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40cfbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-567 3633,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="23587@0" ObjectIDZND0="23412@0" Pin0InfoVect0LinkObjId="SW-127257_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-567 3633,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_402f900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-453 3633,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="23412@1" ObjectIDZND0="23417@x" ObjectIDZND1="g_4144b60@0" ObjectIDZND2="g_4003b60@0" Pin0InfoVect0LinkObjId="SW-127262_0" Pin0InfoVect1LinkObjId="g_4144b60_0" Pin0InfoVect2LinkObjId="g_4003b60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127257_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-453 3633,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4099070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-427 3621,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="23412@x" ObjectIDND1="g_4144b60@0" ObjectIDND2="g_4003b60@0" ObjectIDZND0="23417@1" Pin0InfoVect0LinkObjId="SW-127262_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127257_0" Pin1InfoVect1LinkObjId="g_4144b60_0" Pin1InfoVect2LinkObjId="g_4003b60_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-427 3621,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_408a7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3585,-427 3571,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23417@0" ObjectIDZND0="g_40236f0@0" Pin0InfoVect0LinkObjId="g_40236f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127262_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3585,-427 3571,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_407e110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-395 3615,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="23412@x" ObjectIDND1="23417@x" ObjectIDND2="g_4003b60@0" ObjectIDZND0="g_4144b60@0" Pin0InfoVect0LinkObjId="g_4144b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127257_0" Pin1InfoVect1LinkObjId="SW-127262_0" Pin1InfoVect2LinkObjId="g_4003b60_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-395 3615,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40832d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-427 3633,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="23412@x" ObjectIDND1="23417@x" ObjectIDZND0="g_4144b60@0" ObjectIDZND1="g_4003b60@0" Pin0InfoVect0LinkObjId="g_4144b60_0" Pin0InfoVect1LinkObjId="g_4003b60_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127257_0" Pin1InfoVect1LinkObjId="SW-127262_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-427 3633,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ffdf90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-395 3633,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_4144b60@0" ObjectIDND1="23412@x" ObjectIDND2="23417@x" ObjectIDZND0="g_4003b60@0" Pin0InfoVect0LinkObjId="g_4003b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_4144b60_0" Pin1InfoVect1LinkObjId="SW-127257_0" Pin1InfoVect2LinkObjId="SW-127262_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-395 3633,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40da9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3761,-567 3761,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="23587@0" ObjectIDZND0="23413@0" Pin0InfoVect0LinkObjId="SW-127258_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3761,-567 3761,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a50810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3761,-453 3761,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="23413@1" ObjectIDZND0="23418@x" ObjectIDZND1="g_40b1560@0" ObjectIDZND2="g_3e86b10@0" Pin0InfoVect0LinkObjId="SW-127263_0" Pin0InfoVect1LinkObjId="g_40b1560_0" Pin0InfoVect2LinkObjId="g_3e86b10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127258_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3761,-453 3761,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4013b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3761,-427 3749,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="23413@x" ObjectIDND1="g_40b1560@0" ObjectIDND2="g_3e86b10@0" ObjectIDZND0="23418@1" Pin0InfoVect0LinkObjId="SW-127263_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127258_0" Pin1InfoVect1LinkObjId="g_40b1560_0" Pin1InfoVect2LinkObjId="g_3e86b10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3761,-427 3749,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4002810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3713,-427 3699,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23418@0" ObjectIDZND0="g_4054350@0" Pin0InfoVect0LinkObjId="g_4054350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127263_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3713,-427 3699,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4155650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3761,-395 3761,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3e86b10@0" ObjectIDND1="23413@x" ObjectIDND2="23418@x" ObjectIDZND0="g_40b1560@0" Pin0InfoVect0LinkObjId="g_40b1560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3e86b10_0" Pin1InfoVect1LinkObjId="SW-127258_0" Pin1InfoVect2LinkObjId="SW-127263_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3761,-395 3761,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40a7e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-567 3889,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="23587@0" ObjectIDZND0="23414@0" Pin0InfoVect0LinkObjId="SW-127259_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-567 3889,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41adb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-453 3889,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="23414@1" ObjectIDZND0="23419@x" ObjectIDZND1="g_40b6c20@0" ObjectIDZND2="g_4012f30@0" Pin0InfoVect0LinkObjId="SW-127264_0" Pin0InfoVect1LinkObjId="g_40b6c20_0" Pin0InfoVect2LinkObjId="g_4012f30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127259_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-453 3889,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_401ec90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-427 3877,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="23414@x" ObjectIDND1="g_40b6c20@0" ObjectIDND2="g_4012f30@0" ObjectIDZND0="23419@1" Pin0InfoVect0LinkObjId="SW-127264_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127259_0" Pin1InfoVect1LinkObjId="g_40b6c20_0" Pin1InfoVect2LinkObjId="g_4012f30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-427 3877,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ff6bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3841,-427 3827,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23419@0" ObjectIDZND0="g_3fd9630@0" Pin0InfoVect0LinkObjId="g_3fd9630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127264_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3841,-427 3827,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40704a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-395 3871,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="23414@x" ObjectIDND1="23419@x" ObjectIDND2="g_4012f30@0" ObjectIDZND0="g_40b6c20@0" Pin0InfoVect0LinkObjId="g_40b6c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127259_0" Pin1InfoVect1LinkObjId="SW-127264_0" Pin1InfoVect2LinkObjId="g_4012f30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-395 3871,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4067040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-427 3889,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="23414@x" ObjectIDND1="23419@x" ObjectIDZND0="g_40b6c20@0" ObjectIDZND1="g_4012f30@0" ObjectIDZND2="23916@x" Pin0InfoVect0LinkObjId="g_40b6c20_0" Pin0InfoVect1LinkObjId="g_4012f30_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127259_0" Pin1InfoVect1LinkObjId="SW-127264_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-427 3889,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40deda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3743,-395 3761,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_3e86b10@0" ObjectIDZND0="g_40b1560@0" ObjectIDZND1="23413@x" ObjectIDZND2="23418@x" Pin0InfoVect0LinkObjId="g_40b1560_0" Pin0InfoVect1LinkObjId="SW-127258_0" Pin0InfoVect2LinkObjId="SW-127263_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e86b10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3743,-395 3761,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_416f040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3761,-395 3761,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_40b1560@0" ObjectIDND1="g_3e86b10@0" ObjectIDZND0="23413@x" ObjectIDZND1="23418@x" Pin0InfoVect0LinkObjId="SW-127258_0" Pin0InfoVect1LinkObjId="SW-127263_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_40b1560_0" Pin1InfoVect1LinkObjId="g_3e86b10_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3761,-395 3761,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4019c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3969,-567 3969,-540 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23587@0" ObjectIDZND0="23420@1" Pin0InfoVect0LinkObjId="SW-127265_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3969,-567 3969,-540 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4034030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4049,-567 4049,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="23587@0" ObjectIDZND0="23432@0" Pin0InfoVect0LinkObjId="SW-127277_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4049,-567 4049,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_418d690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4177,-542 4177,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23437@1" ObjectIDZND0="23589@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127282_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4177,-542 4177,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f91690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4049,-428 4062,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="23432@x" ObjectIDND1="23437@x" ObjectIDZND0="g_408de10@0" Pin0InfoVect0LinkObjId="g_408de10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127277_0" Pin1InfoVect1LinkObjId="SW-127282_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4049,-428 4062,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40cbbf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4049,-453 4049,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="23432@1" ObjectIDZND0="g_408de10@0" ObjectIDZND1="23437@x" Pin0InfoVect0LinkObjId="g_408de10_0" Pin0InfoVect1LinkObjId="SW-127282_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127277_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4049,-453 4049,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40d26f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4049,-427 4049,-387 4177,-387 4177,-473 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_408de10@0" ObjectIDND1="23432@x" ObjectIDZND0="23437@0" Pin0InfoVect0LinkObjId="SW-127282_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_408de10_0" Pin1InfoVect1LinkObjId="SW-127277_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4049,-427 4049,-387 4177,-387 4177,-473 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40c12e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4257,-567 4257,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23589@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_418d690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4257,-567 4257,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_409c7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4257,-508 4282,-508 4282,-503 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_40af9c0@0" ObjectIDZND0="g_4081cc0@0" Pin0InfoVect0LinkObjId="g_4081cc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_40af9c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4257,-508 4282,-508 4282,-503 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_419dce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4257,-525 4257,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_40af9c0@0" ObjectIDZND1="g_4081cc0@0" Pin0InfoVect0LinkObjId="g_40af9c0_0" Pin0InfoVect1LinkObjId="g_4081cc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4257,-525 4257,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3fff910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4257,-508 4257,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="0@x" ObjectIDND1="g_4081cc0@0" ObjectIDZND0="g_40af9c0@0" Pin0InfoVect0LinkObjId="g_40af9c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_4081cc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4257,-508 4257,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ffea60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4385,-567 4385,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23589@0" ObjectIDZND0="23436@1" Pin0InfoVect0LinkObjId="SW-127281_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_418d690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4385,-567 4385,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fff600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-546 4545,-567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="23434@0" ObjectIDZND0="23588@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127279_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-546 4545,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f0e030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-428 4529,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="23436@x" ObjectIDND1="23434@x" ObjectIDZND0="g_400a970@0" Pin0InfoVect0LinkObjId="g_400a970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127281_0" Pin1InfoVect1LinkObjId="SW-127279_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-428 4529,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4028ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4385,-473 4385,-387 4545,-387 4545,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="23436@0" ObjectIDZND0="g_400a970@0" ObjectIDZND1="23434@x" Pin0InfoVect0LinkObjId="g_400a970_0" Pin0InfoVect1LinkObjId="SW-127279_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127281_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4385,-473 4385,-387 4545,-387 4545,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4013500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-426 4545,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_400a970@0" ObjectIDND1="23436@x" ObjectIDZND0="23434@1" Pin0InfoVect0LinkObjId="SW-127279_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_400a970_0" Pin1InfoVect1LinkObjId="SW-127281_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-426 4545,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41b28b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4641,-567 4641,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="23588@0" ObjectIDZND0="23433@0" Pin0InfoVect0LinkObjId="SW-127278_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fff600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4641,-567 4641,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_409a440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4641,-428 4641,-331 3969,-331 3969,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="23433@x" ObjectIDND1="g_40944b0@0" ObjectIDZND0="23420@0" Pin0InfoVect0LinkObjId="SW-127265_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127278_0" Pin1InfoVect1LinkObjId="g_40944b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4641,-428 4641,-331 3969,-331 3969,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_408b020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4737,-567 4737,-547 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="23588@0" ObjectIDZND0="23422@0" Pin0InfoVect0LinkObjId="SW-127267_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fff600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4737,-567 4737,-547 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4086830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4737,-455 4737,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="23422@1" ObjectIDZND0="23427@x" ObjectIDZND1="g_40acaa0@0" ObjectIDZND2="g_40e5b20@0" Pin0InfoVect0LinkObjId="SW-127272_0" Pin0InfoVect1LinkObjId="g_40acaa0_0" Pin0InfoVect2LinkObjId="g_40e5b20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127267_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4737,-455 4737,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f46d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4737,-429 4725,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="23422@x" ObjectIDND1="g_40acaa0@0" ObjectIDND2="g_40e5b20@0" ObjectIDZND0="23427@1" Pin0InfoVect0LinkObjId="SW-127272_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127267_0" Pin1InfoVect1LinkObjId="g_40acaa0_0" Pin1InfoVect2LinkObjId="g_40e5b20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4737,-429 4725,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f5a0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-429 4675,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23427@0" ObjectIDZND0="g_3f7e880@0" Pin0InfoVect0LinkObjId="g_3f7e880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127272_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-429 4675,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41b5b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4737,-397 4737,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_40e5b20@0" ObjectIDND1="23422@x" ObjectIDND2="23427@x" ObjectIDZND0="g_40acaa0@0" Pin0InfoVect0LinkObjId="g_40acaa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_40e5b20_0" Pin1InfoVect1LinkObjId="SW-127267_0" Pin1InfoVect2LinkObjId="SW-127272_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4737,-397 4737,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fea2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4719,-397 4737,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_40e5b20@0" ObjectIDZND0="g_40acaa0@0" ObjectIDZND1="23422@x" ObjectIDZND2="23427@x" Pin0InfoVect0LinkObjId="g_40acaa0_0" Pin0InfoVect1LinkObjId="SW-127267_0" Pin0InfoVect2LinkObjId="SW-127272_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40e5b20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4719,-397 4737,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e74790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4737,-397 4737,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_40acaa0@0" ObjectIDND1="g_40e5b20@0" ObjectIDZND0="23422@x" ObjectIDZND1="23427@x" Pin0InfoVect0LinkObjId="SW-127267_0" Pin0InfoVect1LinkObjId="SW-127272_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_40acaa0_0" Pin1InfoVect1LinkObjId="g_40e5b20_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4737,-397 4737,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f5b3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4865,-567 4865,-547 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="23588@0" ObjectIDZND0="23423@0" Pin0InfoVect0LinkObjId="SW-127268_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fff600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4865,-567 4865,-547 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f717a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4865,-455 4865,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="23423@1" ObjectIDZND0="23428@x" ObjectIDZND1="g_41424c0@0" ObjectIDZND2="g_39c7760@0" Pin0InfoVect0LinkObjId="SW-127273_0" Pin0InfoVect1LinkObjId="g_41424c0_0" Pin0InfoVect2LinkObjId="g_39c7760_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127268_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4865,-455 4865,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3faaa20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4865,-429 4853,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="23423@x" ObjectIDND1="g_41424c0@0" ObjectIDND2="g_39c7760@0" ObjectIDZND0="23428@1" Pin0InfoVect0LinkObjId="SW-127273_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127268_0" Pin1InfoVect1LinkObjId="g_41424c0_0" Pin1InfoVect2LinkObjId="g_39c7760_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4865,-429 4853,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fc09e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4817,-429 4803,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23428@0" ObjectIDZND0="g_3fa0120@0" Pin0InfoVect0LinkObjId="g_3fa0120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127273_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4817,-429 4803,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e76fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4865,-397 4865,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_39c7760@0" ObjectIDND1="23423@x" ObjectIDND2="23428@x" ObjectIDZND0="g_41424c0@0" Pin0InfoVect0LinkObjId="g_41424c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_39c7760_0" Pin1InfoVect1LinkObjId="SW-127268_0" Pin1InfoVect2LinkObjId="SW-127273_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4865,-397 4865,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40a9940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4847,-397 4865,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_39c7760@0" ObjectIDZND0="g_41424c0@0" ObjectIDZND1="23423@x" ObjectIDZND2="23428@x" Pin0InfoVect0LinkObjId="g_41424c0_0" Pin0InfoVect1LinkObjId="SW-127268_0" Pin0InfoVect2LinkObjId="SW-127273_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39c7760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4847,-397 4865,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40b7890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4865,-397 4865,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_41424c0@0" ObjectIDND1="g_39c7760@0" ObjectIDZND0="23423@x" ObjectIDZND1="23428@x" Pin0InfoVect0LinkObjId="SW-127268_0" Pin0InfoVect1LinkObjId="SW-127273_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_41424c0_0" Pin1InfoVect1LinkObjId="g_39c7760_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4865,-397 4865,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e79740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4993,-567 4993,-547 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="23588@0" ObjectIDZND0="23424@0" Pin0InfoVect0LinkObjId="SW-127269_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fff600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4993,-567 4993,-547 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2020350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4993,-455 4993,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="23424@1" ObjectIDZND0="23429@x" ObjectIDZND1="g_3f0c320@0" ObjectIDZND2="g_3f26dd0@0" Pin0InfoVect0LinkObjId="SW-127274_0" Pin0InfoVect1LinkObjId="g_3f0c320_0" Pin0InfoVect2LinkObjId="g_3f26dd0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127269_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4993,-455 4993,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f0e270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4993,-429 4981,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="23424@x" ObjectIDND1="g_3f0c320@0" ObjectIDND2="g_3f26dd0@0" ObjectIDZND0="23429@1" Pin0InfoVect0LinkObjId="SW-127274_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127269_0" Pin1InfoVect1LinkObjId="g_3f0c320_0" Pin1InfoVect2LinkObjId="g_3f26dd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4993,-429 4981,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fdba90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4945,-429 4931,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23429@0" ObjectIDZND0="g_37054c0@0" Pin0InfoVect0LinkObjId="g_37054c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127274_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4945,-429 4931,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f27bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4993,-397 4993,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3f26dd0@0" ObjectIDND1="23424@x" ObjectIDND2="23429@x" ObjectIDZND0="g_3f0c320@0" Pin0InfoVect0LinkObjId="g_3f0c320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3f26dd0_0" Pin1InfoVect1LinkObjId="SW-127269_0" Pin1InfoVect2LinkObjId="SW-127274_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4993,-397 4993,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3eb6e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4975,-397 4993,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_3f26dd0@0" ObjectIDZND0="g_3f0c320@0" ObjectIDZND1="23424@x" ObjectIDZND2="23429@x" Pin0InfoVect0LinkObjId="g_3f0c320_0" Pin0InfoVect1LinkObjId="SW-127269_0" Pin0InfoVect2LinkObjId="SW-127274_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f26dd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4975,-397 4993,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2001f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4993,-397 4993,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_3f0c320@0" ObjectIDND1="g_3f26dd0@0" ObjectIDZND0="23424@x" ObjectIDZND1="23429@x" Pin0InfoVect0LinkObjId="SW-127269_0" Pin0InfoVect1LinkObjId="SW-127274_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3f0c320_0" Pin1InfoVect1LinkObjId="g_3f26dd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4993,-397 4993,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40b9850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-567 5121,-547 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="23588@0" ObjectIDZND0="23425@0" Pin0InfoVect0LinkObjId="SW-127270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fff600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-567 5121,-547 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4029680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-455 5121,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="23425@1" ObjectIDZND0="23430@x" ObjectIDZND1="g_409ea70@0" ObjectIDZND2="23918@x" Pin0InfoVect0LinkObjId="SW-127275_0" Pin0InfoVect1LinkObjId="g_409ea70_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127270_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-455 5121,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f876e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-429 5109,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="23425@x" ObjectIDND1="g_409ea70@0" ObjectIDND2="23918@x" ObjectIDZND0="23430@1" Pin0InfoVect0LinkObjId="SW-127275_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127270_0" Pin1InfoVect1LinkObjId="g_409ea70_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-429 5109,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40472a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5073,-429 5059,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23430@0" ObjectIDZND0="g_3d2d0f0@0" Pin0InfoVect0LinkObjId="g_3d2d0f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127275_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5073,-429 5059,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f461d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-567 3683,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="23587@0" ObjectIDZND0="23410@1" Pin0InfoVect0LinkObjId="SW-127255_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-567 3683,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3884880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-812 3619,-812 3619,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="23410@x" ObjectIDND1="23415@x" ObjectIDND2="g_1fc6850@0" ObjectIDZND0="g_406c7f0@0" Pin0InfoVect0LinkObjId="g_406c7f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127255_0" Pin1InfoVect1LinkObjId="SW-127260_0" Pin1InfoVect2LinkObjId="g_1fc6850_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-812 3619,-812 3619,-739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4155340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4641,-454 4641,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="23433@1" ObjectIDZND0="23420@x" ObjectIDZND1="g_40944b0@0" Pin0InfoVect0LinkObjId="SW-127265_0" Pin0InfoVect1LinkObjId="g_40944b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127278_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4641,-454 4641,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40290e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4641,-428 4626,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="23420@x" ObjectIDND1="23433@x" ObjectIDZND0="g_40944b0@0" Pin0InfoVect0LinkObjId="g_40944b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127265_0" Pin1InfoVect1LinkObjId="SW-127278_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4641,-428 4626,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_404b0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3635,-844 3621,-844 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23415@0" ObjectIDZND0="g_32ea640@0" Pin0InfoVect0LinkObjId="g_32ea640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3635,-844 3621,-844 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e8dfc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-844 3671,-844 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_1fc6850@0" ObjectIDND1="23584@x" ObjectIDND2="g_3f0da90@0" ObjectIDZND0="23415@1" Pin0InfoVect0LinkObjId="SW-127260_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1fc6850_0" Pin1InfoVect1LinkObjId="g_4090760_0" Pin1InfoVect2LinkObjId="g_3f0da90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-844 3671,-844 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_205d500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-876 3698,-876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="breaker" EndDevType0="lightningRod" ObjectIDND0="23415@x" ObjectIDND1="g_406c7f0@0" ObjectIDND2="23410@x" ObjectIDZND0="g_1fc6850@0" Pin0InfoVect0LinkObjId="g_1fc6850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127260_0" Pin1InfoVect1LinkObjId="g_406c7f0_0" Pin1InfoVect2LinkObjId="SW-127255_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-876 3698,-876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f24620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3812,-567 3812,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23587@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3812,-567 3812,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_416a290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3812,-651 3780,-651 3780,-656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_3f8eb40@0" ObjectIDZND0="g_407eb30@0" Pin0InfoVect0LinkObjId="g_407eb30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3f8eb40_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3812,-651 3780,-651 3780,-656 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4095c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3812,-629 3812,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3f8eb40@0" ObjectIDZND1="g_407eb30@0" Pin0InfoVect0LinkObjId="g_3f8eb40_0" Pin0InfoVect1LinkObjId="g_407eb30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3812,-629 3812,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4028310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3812,-651 3812,-725 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="0@x" ObjectIDND1="g_407eb30@0" ObjectIDZND0="g_3f8eb40@0" Pin0InfoVect0LinkObjId="g_3f8eb40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_407eb30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3812,-651 3812,-725 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2021360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-567 4228,-593 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="23589@0" ObjectIDZND0="23431@1" Pin0InfoVect0LinkObjId="SW-127276_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_418d690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-567 4228,-593 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36f5a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4181,-720 4167,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23435@0" ObjectIDZND0="g_3e73ea0@0" Pin0InfoVect0LinkObjId="g_3e73ea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4181,-720 4167,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f84f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4180,-932 4166,-932 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23409@0" ObjectIDZND0="g_3f87490@0" Pin0InfoVect0LinkObjId="g_3f87490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127254_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4180,-932 4166,-932 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f478c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-932 4216,-932 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="23405@x" ObjectIDND1="23586@x" ObjectIDZND0="23409@1" Pin0InfoVect0LinkObjId="SW-127254_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127250_0" Pin1InfoVect1LinkObjId="g_40962a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-932 4216,-932 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3faf310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4180,-998 4166,-998 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23408@0" ObjectIDZND0="g_4034bb0@0" Pin0InfoVect0LinkObjId="g_4034bb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127253_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4180,-998 4166,-998 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40d2360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-998 4216,-998 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="23406@x" ObjectIDND1="23405@x" ObjectIDZND0="23408@1" Pin0InfoVect0LinkObjId="SW-127253_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127251_0" Pin1InfoVect1LinkObjId="SW-127250_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-998 4216,-998 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ff020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4179,-1071 4165,-1071 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23407@0" ObjectIDZND0="g_4042430@0" Pin0InfoVect0LinkObjId="g_4042430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127252_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4179,-1071 4165,-1071 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40b5e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4227,-1071 4215,-1071 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="currentTransformer" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_40bb4c0@0" ObjectIDND1="g_3f0b8f0@0" ObjectIDND2="0@x" ObjectIDZND0="23407@1" Pin0InfoVect0LinkObjId="SW-127252_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_40bb4c0_0" Pin1InfoVect1LinkObjId="g_3f0b8f0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4227,-1071 4215,-1071 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40dc1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-1100 4244,-1100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="currentTransformer" ObjectIDND0="g_3f0b8f0@0" ObjectIDND1="0@x" ObjectIDND2="23407@x" ObjectIDZND0="g_40bb4c0@0" Pin0InfoVect0LinkObjId="g_40bb4c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3f0b8f0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-127252_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-1100 4244,-1100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f44170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-1124 4213,-1124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="currentTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_40bb4c0@0" ObjectIDND1="23407@x" ObjectIDND2="23406@x" ObjectIDZND0="g_3f0b8f0@0" Pin0InfoVect0LinkObjId="g_3f0b8f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_40bb4c0_0" Pin1InfoVect1LinkObjId="SW-127252_0" Pin1InfoVect2LinkObjId="SW-127251_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-1124 4213,-1124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31eb010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-1100 4228,-1124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="currentTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_40bb4c0@0" ObjectIDND1="23407@x" ObjectIDND2="23406@x" ObjectIDZND0="g_3f0b8f0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_3f0b8f0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_40bb4c0_0" Pin1InfoVect1LinkObjId="SW-127252_0" Pin1InfoVect2LinkObjId="SW-127251_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-1100 4228,-1124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f9fdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-1124 4228,-1146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="currentTransformer" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_3f0b8f0@0" ObjectIDND1="g_40bb4c0@0" ObjectIDND2="23407@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3f0b8f0_0" Pin1InfoVect1LinkObjId="g_40bb4c0_0" Pin1InfoVect2LinkObjId="SW-127252_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-1124 4228,-1146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41b8b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-1007 3683,-972 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="23584@0" ObjectIDZND0="g_3f0da90@0" ObjectIDZND1="g_1fc6850@0" ObjectIDZND2="23415@x" Pin0InfoVect0LinkObjId="g_3f0da90_0" Pin0InfoVect1LinkObjId="g_1fc6850_0" Pin0InfoVect2LinkObjId="SW-127260_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4090760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-1007 3683,-972 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ffef30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-844 3683,-877 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="breaker" EndDevType0="lightningRod" EndDevType1="transformer2" EndDevType2="lightningRod" ObjectIDND0="23415@x" ObjectIDND1="g_406c7f0@0" ObjectIDND2="23410@x" ObjectIDZND0="g_1fc6850@0" ObjectIDZND1="23584@x" ObjectIDZND2="g_3f0da90@0" Pin0InfoVect0LinkObjId="g_1fc6850_0" Pin0InfoVect1LinkObjId="g_4090760_0" Pin0InfoVect2LinkObjId="g_3f0da90_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127260_0" Pin1InfoVect1LinkObjId="g_406c7f0_0" Pin1InfoVect2LinkObjId="SW-127255_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-844 3683,-877 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3496d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-700 3683,-812 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="23410@0" ObjectIDZND0="g_406c7f0@0" ObjectIDZND1="23415@x" ObjectIDZND2="g_1fc6850@0" Pin0InfoVect0LinkObjId="g_406c7f0_0" Pin0InfoVect1LinkObjId="SW-127260_0" Pin0InfoVect2LinkObjId="g_1fc6850_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127255_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-700 3683,-812 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40ebae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-844 3683,-812 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer2" EndDevType0="voltageTransformer" EndDevType1="breaker" ObjectIDND0="23415@x" ObjectIDND1="g_1fc6850@0" ObjectIDND2="23584@x" ObjectIDZND0="g_406c7f0@0" ObjectIDZND1="23410@x" Pin0InfoVect0LinkObjId="g_406c7f0_0" Pin0InfoVect1LinkObjId="SW-127255_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127260_0" Pin1InfoVect1LinkObjId="g_1fc6850_0" Pin1InfoVect2LinkObjId="g_4090760_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-844 3683,-812 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_408b470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-567 4822,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="23588@0" ObjectIDZND0="23421@1" Pin0InfoVect0LinkObjId="SW-127266_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fff600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-567 4822,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_407fa90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-812 4758,-812 4758,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="23421@x" ObjectIDND1="23426@x" ObjectIDND2="g_41567e0@0" ObjectIDZND0="g_3f595d0@0" Pin0InfoVect0LinkObjId="g_3f595d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127266_0" Pin1InfoVect1LinkObjId="SW-127271_0" Pin1InfoVect2LinkObjId="g_41567e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-812 4758,-812 4758,-739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f7c9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4774,-844 4760,-844 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23426@0" ObjectIDZND0="g_2965b80@0" Pin0InfoVect0LinkObjId="g_2965b80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127271_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4774,-844 4760,-844 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3faa0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-844 4810,-844 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_41567e0@0" ObjectIDND1="23585@x" ObjectIDND2="g_3feb1d0@0" ObjectIDZND0="23426@1" Pin0InfoVect0LinkObjId="SW-127271_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_41567e0_0" Pin1InfoVect1LinkObjId="g_402c7d0_0" Pin1InfoVect2LinkObjId="g_3feb1d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-844 4810,-844 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f87110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-876 4837,-876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="breaker" EndDevType0="lightningRod" ObjectIDND0="23426@x" ObjectIDND1="g_3f595d0@0" ObjectIDND2="23421@x" ObjectIDZND0="g_41567e0@0" Pin0InfoVect0LinkObjId="g_41567e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127271_0" Pin1InfoVect1LinkObjId="g_3f595d0_0" Pin1InfoVect2LinkObjId="SW-127266_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-876 4837,-876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40adfd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4948,-567 4948,-613 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23588@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1fff600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4948,-567 4948,-613 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3f6fb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4948,-652 4916,-652 4916,-657 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_409d650@0" ObjectIDZND0="g_3fa54b0@0" Pin0InfoVect0LinkObjId="g_3fa54b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_409d650_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4948,-652 4916,-652 4916,-657 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3e8b2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4948,-630 4948,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_409d650@0" ObjectIDZND1="g_3fa54b0@0" Pin0InfoVect0LinkObjId="g_409d650_0" Pin0InfoVect1LinkObjId="g_3fa54b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4948,-630 4948,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1fdc040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4948,-652 4948,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="0@x" ObjectIDND1="g_3fa54b0@0" ObjectIDZND0="g_409d650@0" Pin0InfoVect0LinkObjId="g_409d650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3fa54b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4948,-652 4948,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40c5cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-1007 4822,-972 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="23585@0" ObjectIDZND0="g_3feb1d0@0" ObjectIDZND1="g_41567e0@0" ObjectIDZND2="23426@x" Pin0InfoVect0LinkObjId="g_3feb1d0_0" Pin0InfoVect1LinkObjId="g_41567e0_0" Pin0InfoVect2LinkObjId="SW-127271_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_402c7d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-1007 4822,-972 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40cd210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-844 4822,-877 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="breaker" EndDevType0="lightningRod" EndDevType1="transformer2" EndDevType2="lightningRod" ObjectIDND0="23426@x" ObjectIDND1="g_3f595d0@0" ObjectIDND2="23421@x" ObjectIDZND0="g_41567e0@0" ObjectIDZND1="23585@x" ObjectIDZND2="g_3feb1d0@0" Pin0InfoVect0LinkObjId="g_41567e0_0" Pin0InfoVect1LinkObjId="g_402c7d0_0" Pin0InfoVect2LinkObjId="g_3feb1d0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127271_0" Pin1InfoVect1LinkObjId="g_3f595d0_0" Pin1InfoVect2LinkObjId="SW-127266_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-844 4822,-877 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41b2af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-700 4822,-812 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="23421@0" ObjectIDZND0="g_3f595d0@0" ObjectIDZND1="23426@x" ObjectIDZND2="g_41567e0@0" Pin0InfoVect0LinkObjId="g_3f595d0_0" Pin0InfoVect1LinkObjId="SW-127271_0" Pin0InfoVect2LinkObjId="g_41567e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127266_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-700 4822,-812 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_402f100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-844 4822,-812 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer2" EndDevType0="voltageTransformer" EndDevType1="breaker" ObjectIDND0="23426@x" ObjectIDND1="g_41567e0@0" ObjectIDND2="23585@x" ObjectIDZND0="g_3f595d0@0" ObjectIDZND1="23421@x" Pin0InfoVect0LinkObjId="g_3f595d0_0" Pin0InfoVect1LinkObjId="SW-127266_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-127271_0" Pin1InfoVect1LinkObjId="g_41567e0_0" Pin1InfoVect2LinkObjId="g_402c7d0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-844 4822,-812 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4069600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-1071 4228,-1100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="currentTransformer" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="23407@x" ObjectIDND1="23406@x" ObjectIDZND0="g_40bb4c0@0" ObjectIDZND1="g_3f0b8f0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_40bb4c0_0" Pin0InfoVect1LinkObjId="g_3f0b8f0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127252_0" Pin1InfoVect1LinkObjId="SW-127251_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-1071 4228,-1100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_41120f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-998 4228,-1018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="23408@x" ObjectIDND1="23405@x" ObjectIDZND0="23406@0" Pin0InfoVect0LinkObjId="SW-127251_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127253_0" Pin1InfoVect1LinkObjId="SW-127250_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-998 4228,-1018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4031620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-932 4228,-953 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="breaker" ObjectIDND0="23409@x" ObjectIDND1="23586@x" ObjectIDZND0="23405@0" Pin0InfoVect0LinkObjId="SW-127250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127254_0" Pin1InfoVect1LinkObjId="g_40962a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-932 4228,-953 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4010c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-909 4228,-932 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="23586@1" ObjectIDZND0="23409@x" ObjectIDZND1="23405@x" Pin0InfoVect0LinkObjId="SW-127254_0" Pin0InfoVect1LinkObjId="SW-127250_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40962a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-909 4228,-932 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_414ffa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-1054 4228,-1071 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="currentTransformer" EndDevType2="lightningRod" ObjectIDND0="23406@1" ObjectIDZND0="23407@x" ObjectIDZND1="g_40bb4c0@0" ObjectIDZND2="g_3f0b8f0@0" Pin0InfoVect0LinkObjId="SW-127252_0" Pin0InfoVect1LinkObjId="g_40bb4c0_0" Pin0InfoVect2LinkObjId="g_3f0b8f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127251_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-1054 4228,-1071 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40c37d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-980 4228,-998 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="23405@1" ObjectIDZND0="23408@x" ObjectIDZND1="23406@x" Pin0InfoVect0LinkObjId="SW-127253_0" Pin0InfoVect1LinkObjId="SW-127251_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127250_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-980 4228,-998 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40b9b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-829 4228,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="23586@0" ObjectIDZND0="g_3f11570@0" ObjectIDZND1="g_2033540@0" ObjectIDZND2="23435@x" Pin0InfoVect0LinkObjId="g_3f11570_0" Pin0InfoVect1LinkObjId="g_2033540_0" Pin0InfoVect2LinkObjId="SW-127280_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40962a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-829 4228,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4032040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-803 4243,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="23586@x" ObjectIDND1="g_2033540@0" ObjectIDND2="23435@x" ObjectIDZND0="g_3f11570@0" Pin0InfoVect0LinkObjId="g_3f11570_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_40962a0_0" Pin1InfoVect1LinkObjId="g_2033540_0" Pin1InfoVect2LinkObjId="SW-127280_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-803 4243,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40962a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4242,-742 4228,-742 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2033540@0" ObjectIDZND0="23586@x" ObjectIDZND1="g_3f11570@0" ObjectIDZND2="23435@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="g_3f11570_0" Pin0InfoVect2LinkObjId="SW-127280_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2033540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4242,-742 4228,-742 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4039aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-803 4228,-742 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="23586@x" ObjectIDND1="g_3f11570@0" ObjectIDZND0="g_2033540@0" ObjectIDZND1="23435@x" ObjectIDZND2="23431@x" Pin0InfoVect0LinkObjId="g_2033540_0" Pin0InfoVect1LinkObjId="SW-127280_0" Pin0InfoVect2LinkObjId="SW-127276_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_40962a0_0" Pin1InfoVect1LinkObjId="g_3f11570_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-803 4228,-742 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_403ac50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4217,-720 4228,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" EndDevType2="lightningRod" ObjectIDND0="23435@1" ObjectIDZND0="g_2033540@0" ObjectIDZND1="23586@x" ObjectIDZND2="g_3f11570@0" Pin0InfoVect0LinkObjId="g_2033540_0" Pin0InfoVect1LinkObjId="g_40962a0_0" Pin0InfoVect2LinkObjId="g_3f11570_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127280_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4217,-720 4228,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40f7600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-742 4228,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="breaker" EndDevType2="voltageTransformer" ObjectIDND0="g_2033540@0" ObjectIDND1="23586@x" ObjectIDND2="g_3f11570@0" ObjectIDZND0="23435@x" ObjectIDZND1="23431@x" ObjectIDZND2="g_37aa170@0" Pin0InfoVect0LinkObjId="SW-127280_0" Pin0InfoVect1LinkObjId="SW-127276_0" Pin0InfoVect2LinkObjId="g_37aa170_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2033540_0" Pin1InfoVect1LinkObjId="g_40962a0_0" Pin1InfoVect2LinkObjId="g_3f11570_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-742 4228,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fb1590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-706 4228,-682 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="g_37aa170@0" ObjectIDND1="23435@x" ObjectIDND2="g_2033540@0" ObjectIDZND0="23431@0" Pin0InfoVect0LinkObjId="SW-127276_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_37aa170_0" Pin1InfoVect1LinkObjId="SW-127280_0" Pin1InfoVect2LinkObjId="g_2033540_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-706 4228,-682 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4179050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4339,-706 4228,-706 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="breaker" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_37aa170@0" ObjectIDZND0="23431@x" ObjectIDZND1="23435@x" ObjectIDZND2="g_2033540@0" Pin0InfoVect0LinkObjId="SW-127276_0" Pin0InfoVect1LinkObjId="SW-127280_0" Pin0InfoVect2LinkObjId="g_2033540_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37aa170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4339,-706 4228,-706 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40c7040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4228,-706 4228,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="voltageTransformer" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="23431@x" ObjectIDND1="g_37aa170@0" ObjectIDZND0="23435@x" ObjectIDZND1="g_2033540@0" ObjectIDZND2="23586@x" Pin0InfoVect0LinkObjId="SW-127280_0" Pin0InfoVect1LinkObjId="g_2033540_0" Pin0InfoVect2LinkObjId="g_40962a0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127276_0" Pin1InfoVect1LinkObjId="g_37aa170_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4228,-706 4228,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4090760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3700,-972 3683,-972 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3f0da90@0" ObjectIDZND0="23584@x" ObjectIDZND1="g_1fc6850@0" ObjectIDZND2="23415@x" Pin0InfoVect0LinkObjId="g_5a5ad30_0" Pin0InfoVect1LinkObjId="g_1fc6850_0" Pin0InfoVect2LinkObjId="SW-127260_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f0da90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3700,-972 3683,-972 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_406f5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-972 3683,-877 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="23584@x" ObjectIDND1="g_3f0da90@0" ObjectIDZND0="g_1fc6850@0" ObjectIDZND1="23415@x" ObjectIDZND2="g_406c7f0@0" Pin0InfoVect0LinkObjId="g_1fc6850_0" Pin0InfoVect1LinkObjId="SW-127260_0" Pin0InfoVect2LinkObjId="g_406c7f0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4090760_0" Pin1InfoVect1LinkObjId="g_3f0da90_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-972 3683,-877 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_402c7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4839,-972 4822,-972 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3feb1d0@0" ObjectIDZND0="23585@x" ObjectIDZND1="g_41567e0@0" ObjectIDZND2="23426@x" Pin0InfoVect0LinkObjId="g_3ff9f80_0" Pin0InfoVect1LinkObjId="g_41567e0_0" Pin0InfoVect2LinkObjId="SW-127271_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3feb1d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4839,-972 4822,-972 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3e7f320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-972 4822,-877 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="23585@x" ObjectIDND1="g_3feb1d0@0" ObjectIDZND0="g_41567e0@0" ObjectIDZND1="23426@x" ObjectIDZND2="g_3f595d0@0" Pin0InfoVect0LinkObjId="g_41567e0_0" Pin0InfoVect1LinkObjId="SW-127271_0" Pin0InfoVect2LinkObjId="g_3f595d0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_402c7d0_0" Pin1InfoVect1LinkObjId="g_3feb1d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-972 4822,-877 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4012d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-322 3871,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_40b6c20@0" ObjectIDND1="23414@x" ObjectIDND2="23419@x" ObjectIDZND0="g_4012f30@0" Pin0InfoVect0LinkObjId="g_4012f30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_40b6c20_0" Pin1InfoVect1LinkObjId="SW-127259_0" Pin1InfoVect2LinkObjId="SW-127264_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-322 3871,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40634b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-395 3889,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_40b6c20@0" ObjectIDND1="23414@x" ObjectIDND2="23419@x" ObjectIDZND0="g_4012f30@0" ObjectIDZND1="23916@x" Pin0InfoVect0LinkObjId="g_4012f30_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_40b6c20_0" Pin1InfoVect1LinkObjId="SW-127259_0" Pin1InfoVect2LinkObjId="SW-127264_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-395 3889,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f54200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-322 3889,-307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="g_4012f30@0" ObjectIDND1="g_40b6c20@0" ObjectIDND2="23414@x" ObjectIDZND0="23916@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_4012f30_0" Pin1InfoVect1LinkObjId="g_40b6c20_0" Pin1InfoVect2LinkObjId="SW-127259_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-322 3889,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f49e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-257 3871,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="23916@x" ObjectIDND1="g_3fb9d00@0" ObjectIDND2="23917@x" ObjectIDZND0="g_3f6a150@0" Pin0InfoVect0LinkObjId="g_3f6a150_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3fb9d00_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-257 3871,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d82db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-271 3889,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="23916@0" ObjectIDZND0="g_3f6a150@0" ObjectIDZND1="g_3fb9d00@0" ObjectIDZND2="23917@x" Pin0InfoVect0LinkObjId="g_3f6a150_0" Pin0InfoVect1LinkObjId="g_3fb9d00_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-271 3889,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d82fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-201 3871,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3f6a150@0" ObjectIDND1="23916@x" ObjectIDND2="23917@x" ObjectIDZND0="g_3fb9d00@0" Pin0InfoVect0LinkObjId="g_3fb9d00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3f6a150_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-201 3871,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_402b8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-257 3889,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3f6a150@0" ObjectIDND1="23916@x" ObjectIDZND0="g_3fb9d00@0" ObjectIDZND1="23917@x" Pin0InfoVect0LinkObjId="g_3fb9d00_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3f6a150_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-257 3889,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_402bac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-201 3889,-182 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3fb9d00@0" ObjectIDND1="g_3f6a150@0" ObjectIDND2="23916@x" ObjectIDZND0="23917@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3fb9d00_0" Pin1InfoVect1LinkObjId="g_3f6a150_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-201 3889,-182 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f615c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3871,-118 3889,-118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="g_40f6290@0" ObjectIDZND0="23917@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_40f6290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3871,-118 3889,-118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f61820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-118 3889,-130 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_40f6290@0" ObjectIDND1="0@x" ObjectIDZND0="23917@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_40f6290_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-118 3889,-130 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f0e510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-118 3889,-104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_40f6290@0" ObjectIDND1="23917@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_40f6290_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-118 3889,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_40f2c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3871,-34 3889,-34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3f0e770@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f0e770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3871,-34 3889,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_40e0ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-44 3889,-34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_3f0e770@0" Pin0InfoVect0LinkObjId="g_3f0e770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-44 3889,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_40e1130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-34 3889,107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" ObjectIDND0="g_3f0e770@0" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3f0e770_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-34 3889,107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40c62b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-324 5103,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3f10160@0" ObjectIDND1="23425@x" ObjectIDND2="23430@x" ObjectIDZND0="g_409ea70@0" Pin0InfoVect0LinkObjId="g_409ea70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3f10160_0" Pin1InfoVect1LinkObjId="SW-127270_0" Pin1InfoVect2LinkObjId="SW-127275_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-324 5103,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ff5a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-397 5121,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3f10160@0" ObjectIDND1="23425@x" ObjectIDND2="23430@x" ObjectIDZND0="g_409ea70@0" ObjectIDZND1="23918@x" Pin0InfoVect0LinkObjId="g_409ea70_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3f10160_0" Pin1InfoVect1LinkObjId="SW-127270_0" Pin1InfoVect2LinkObjId="SW-127275_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-397 5121,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4108090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-324 5121,-309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="g_409ea70@0" ObjectIDND1="g_3f10160@0" ObjectIDND2="23425@x" ObjectIDZND0="23918@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_409ea70_0" Pin1InfoVect1LinkObjId="g_3f10160_0" Pin1InfoVect2LinkObjId="SW-127270_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-324 5121,-309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ee4a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-259 5103,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="23918@x" ObjectIDND1="g_3f2a7d0@0" ObjectIDND2="23919@x" ObjectIDZND0="g_41ab5d0@0" Pin0InfoVect0LinkObjId="g_41ab5d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3f2a7d0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-259 5103,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_403c2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-273 5121,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="23918@0" ObjectIDZND0="g_41ab5d0@0" ObjectIDZND1="g_3f2a7d0@0" ObjectIDZND2="23919@x" Pin0InfoVect0LinkObjId="g_41ab5d0_0" Pin0InfoVect1LinkObjId="g_3f2a7d0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-273 5121,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f2a5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-203 5103,-203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_41ab5d0@0" ObjectIDND1="23918@x" ObjectIDND2="23919@x" ObjectIDZND0="g_3f2a7d0@0" Pin0InfoVect0LinkObjId="g_3f2a7d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_41ab5d0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-203 5103,-203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40f1960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-259 5121,-203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_41ab5d0@0" ObjectIDND1="23918@x" ObjectIDZND0="g_3f2a7d0@0" ObjectIDZND1="23919@x" Pin0InfoVect0LinkObjId="g_3f2a7d0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_41ab5d0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-259 5121,-203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fc4ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-203 5121,-184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3f2a7d0@0" ObjectIDND1="g_41ab5d0@0" ObjectIDND2="23918@x" ObjectIDZND0="23919@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3f2a7d0_0" Pin1InfoVect1LinkObjId="g_41ab5d0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-203 5121,-184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f6be50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5103,-120 5121,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="g_41b8510@0" ObjectIDZND0="23919@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41b8510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5103,-120 5121,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f6c0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-120 5121,-132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_41b8510@0" ObjectIDND1="0@x" ObjectIDZND0="23919@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_41b8510_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-120 5121,-132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f68950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-120 5121,-106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_41b8510@0" ObjectIDND1="23919@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_41b8510_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-120 5121,-106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4021950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5103,-36 5121,-36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_411a800@0" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_411a800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5103,-36 5121,-36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ff8a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-46 5121,-36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_411a800@0" Pin0InfoVect0LinkObjId="g_411a800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-46 5121,-36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3ff8c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-36 5121,105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" ObjectIDND0="g_411a800@0" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_411a800_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-36 5121,105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40bd790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5103,-397 5121,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_3f10160@0" ObjectIDZND0="g_409ea70@0" ObjectIDZND1="23918@x" ObjectIDZND2="23425@x" Pin0InfoVect0LinkObjId="g_409ea70_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-127270_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f10160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5103,-397 5121,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3884d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-397 5121,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_409ea70@0" ObjectIDND1="23918@x" ObjectIDND2="g_3f10160@0" ObjectIDZND0="23425@x" ObjectIDZND1="23430@x" Pin0InfoVect0LinkObjId="SW-127270_0" Pin0InfoVect1LinkObjId="SW-127275_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_409ea70_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_3f10160_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-397 5121,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_409bde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3505,-454 3505,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="23411@1" ObjectIDZND0="23416@x" ObjectIDZND1="g_3218c00@0" ObjectIDZND2="g_33871b0@0" Pin0InfoVect0LinkObjId="SW-127261_0" Pin0InfoVect1LinkObjId="g_3218c00_0" Pin0InfoVect2LinkObjId="g_33871b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127256_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3505,-454 3505,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_409bfd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3457,-428 3447,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23416@0" ObjectIDZND0="g_40da560@0" Pin0InfoVect0LinkObjId="g_40da560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127261_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3457,-428 3447,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_5a591d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-1134 3683,-1124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="23454@1" Pin0InfoVect0LinkObjId="SW-127299_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-1134 3683,-1124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_5a5ad30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-1097 3683,-1087 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="23454@0" ObjectIDZND0="23584@1" Pin0InfoVect0LinkObjId="g_4090760_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127299_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-1097 3683,-1087 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_5b22190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-1134 4822,-1124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="23474@1" Pin0InfoVect0LinkObjId="SW-127319_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-1134 4822,-1124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ff9f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-1097 4822,-1087 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="23474@0" ObjectIDZND0="23585@1" Pin0InfoVect0LinkObjId="g_402c7d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127319_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-1097 4822,-1087 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="23587" cx="3505" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23587" cx="3633" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23587" cx="3761" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23587" cx="3889" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23587" cx="3969" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23587" cx="4049" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23589" cx="4177" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23589" cx="4257" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23589" cx="4385" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23588" cx="4545" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23588" cx="4641" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23588" cx="4737" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23588" cx="4865" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23588" cx="4993" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23588" cx="5121" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23587" cx="3683" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23587" cx="3812" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23589" cx="4228" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23588" cx="4822" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23588" cx="4948" cy="-567" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="267" x="3230" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="267" x="3230" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3193" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3193" y="-1194"/></g>
  </g><g id="MotifButton_Layer">
   <g href="楚雄地区_500kV_永仁换流站.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="267" x="3230" y="-1177"/></g>
   <g href="楚雄地区_500kV_永仁换流站.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3193" y="-1194"/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3761,-362 3756,-372 3766,-372 3761,-362 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3761,-350 3756,-340 3766,-340 3761,-350 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3633,-362 3628,-372 3638,-372 3633,-362 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3633,-350 3628,-340 3638,-340 3633,-350 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3505,-362 3500,-372 3510,-372 3505,-362 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3505,-350 3500,-340 3510,-340 3505,-350 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4737,-364 4732,-374 4742,-374 4737,-364 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4737,-352 4732,-342 4742,-342 4737,-352 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4865,-364 4860,-374 4870,-374 4865,-364 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4865,-352 4860,-342 4870,-342 4865,-352 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4993,-364 4988,-374 4998,-374 4993,-364 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4993,-352 4988,-342 4998,-342 4993,-352 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3683,-930 3678,-940 3688,-940 3683,-930 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3683,-918 3678,-908 3688,-908 3683,-918 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4228,-777 4223,-787 4233,-787 4228,-777 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4228,-765 4223,-755 4233,-755 4228,-765 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4822,-930 4817,-940 4827,-940 4822,-930 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4822,-918 4817,-908 4827,-908 4822,-918 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3889,-362 3884,-372 3894,-372 3889,-362 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3889,-350 3884,-340 3894,-340 3889,-350 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3889,-234 3884,-244 3894,-244 3889,-234 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3889,-222 3884,-212 3894,-212 3889,-222 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3889,-13 3884,-23 3894,-23 3889,-13 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3889,-1 3884,9 3894,9 3889,-1 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3889,48 3884,38 3894,38 3889,48 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3889,60 3884,70 3894,70 3889,60 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5121,-364 5116,-374 5126,-374 5121,-364 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5121,-352 5116,-342 5126,-342 5121,-352 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5121,-236 5116,-246 5126,-246 5121,-236 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5121,-224 5116,-214 5126,-214 5121,-224 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5121,-15 5116,-25 5126,-25 5121,-15 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5121,-3 5116,7 5126,7 5121,-3 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5121,46 5116,36 5126,36 5121,46 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5121,58 5116,68 5126,68 5121,58 " stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="CurrentTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_40bb4c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4239.000000 -1062.000000)" xlink:href="#currentTransformer:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_YRH.CX_YRH_Zyb3">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="33339"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3646.000000 -1002.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3646.000000 -1002.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="23584" ObjectName="TF-CX_YRH.CX_YRH_Zyb3"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_YRH.CX_YRH_Zyb5">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="33347"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4191.000000 -824.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4191.000000 -824.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="23586" ObjectName="TF-CX_YRH.CX_YRH_Zyb5"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_YRH.CX_YRH_Zyb4">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="33343"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4785.000000 -1002.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4785.000000 -1002.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="23585" ObjectName="TF-CX_YRH.CX_YRH_Zyb4"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.740000 -0.000000 0.000000 -0.752467 3871.000000 -40.353772)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.740000 -0.000000 0.000000 -0.752467 3871.000000 -40.353772)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.740000 -0.000000 0.000000 -0.752467 5103.000000 -42.353772)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.740000 -0.000000 0.000000 -0.752467 5103.000000 -42.353772)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3229.500000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127081" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3643.000000 -1135.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127081" ObjectName="CX_YRH:CX_YRH_Zyb3_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127082" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3643.000000 -1120.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127082" ObjectName="CX_YRH:CX_YRH_Zyb3_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127080" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3643.000000 -1105.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127080" ObjectName="CX_YRH:CX_YRH_Zyb3_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127085" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4784.000000 -1133.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127085" ObjectName="CX_YRH:CX_YRH_Zyb4_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127086" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4784.000000 -1118.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127086" ObjectName="CX_YRH:CX_YRH_Zyb4_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127084" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4784.000000 -1103.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127084" ObjectName="CX_YRH:CX_YRH_Zyb4_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127089" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4248.000000 -1160.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127089" ObjectName="CX_YRH:CX_YRH_Zyb5_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127090" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4248.000000 -1145.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127090" ObjectName="CX_YRH:CX_YRH_Zyb5_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127088" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4248.000000 -1130.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127088" ObjectName="CX_YRH:CX_YRH_Zyb5_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127083" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3488.000000 -617.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127083" ObjectName="CX_YRH:CX_YRH_Zyb3_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127091" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4361.000000 -617.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127091" ObjectName="CX_YRH:CX_YRH_Zyb5_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127087" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4631.000000 -617.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127087" ObjectName="CX_YRH:CX_YRH_Zyb4_Uab"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_41221a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3271.500000 -1166.500000) translate(0,16)">永仁换流站10kV配电装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_416b070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_416b070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_416b070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_416b070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_416b070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_416b070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_416b070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_416b070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_416b070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_416b070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_416b070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_416b070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_416b070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_416b070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_416b070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_416b070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_416b070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_416b070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -588.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4022090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -1026.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4022090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -1026.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4022090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -1026.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4022090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -1026.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4022090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -1026.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4022090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -1026.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4022090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -1026.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4065a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3519.000000 -417.000000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4080ca0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3649.000000 -448.000000) translate(0,15)">极</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4080ca0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3649.000000 -448.000000) translate(0,33)">二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4080ca0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3649.000000 -448.000000) translate(0,51)"/>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4080ca0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3649.000000 -448.000000) translate(0,69)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4080ca0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3649.000000 -448.000000) translate(0,87)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4080ca0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3649.000000 -448.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2091580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3652.000000 -415.000000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_416ae00" transform="matrix(1.000000 0.000000 0.000000 1.000000 3776.000000 -430.000000) translate(0,15)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_416ae00" transform="matrix(1.000000 0.000000 0.000000 1.000000 3776.000000 -430.000000) translate(0,33)">公</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_416ae00" transform="matrix(1.000000 0.000000 0.000000 1.000000 3776.000000 -430.000000) translate(0,51)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_416ae00" transform="matrix(1.000000 0.000000 0.000000 1.000000 3776.000000 -430.000000) translate(0,69)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_416ae00" transform="matrix(1.000000 0.000000 0.000000 1.000000 3776.000000 -430.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20ac010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3780.000000 -450.000000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4133e20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3907.000000 -374.000000) translate(0,15)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4133e20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3907.000000 -374.000000) translate(0,33)">外</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4133e20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3907.000000 -374.000000) translate(0,51)">泵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4133e20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3907.000000 -374.000000) translate(0,69)">房</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4133e20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3907.000000 -374.000000) translate(0,87)">Ⅰ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4133e20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3907.000000 -374.000000) translate(0,105)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4133e20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3907.000000 -374.000000) translate(0,123)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_47beec0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4063.000000 -408.000000) translate(0,15)">Ⅰ Ⅲ母联线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4010630" transform="matrix(1.000000 0.000000 0.000000 1.000000 4063.000000 -352.000000) translate(0,15)">Ⅰ Ⅱ母联线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40c1120" transform="matrix(1.000000 0.000000 0.000000 1.000000 4253.000000 -396.000000) translate(0,15)">10kVⅢ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3d6bf00" transform="matrix(1.000000 0.000000 0.000000 1.000000 4228.000000 -377.000000) translate(0,15)">母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40418b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4419.000000 -408.000000) translate(0,15)">Ⅱ Ⅲ母联线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41b22a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4213.000000 -529.000000) translate(0,12)">0903</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f316b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5005.000000 -448.000000) translate(0,15)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f316b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5005.000000 -448.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f316b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5005.000000 -448.000000) translate(0,51)">公</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f316b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5005.000000 -448.000000) translate(0,69)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f316b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5005.000000 -448.000000) translate(0,87)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f316b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5005.000000 -448.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3b13220" transform="matrix(1.000000 0.000000 0.000000 1.000000 5148.000000 -378.000000) translate(0,15)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3b13220" transform="matrix(1.000000 0.000000 0.000000 1.000000 5148.000000 -378.000000) translate(0,33)">外</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3b13220" transform="matrix(1.000000 0.000000 0.000000 1.000000 5148.000000 -378.000000) translate(0,51)">泵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3b13220" transform="matrix(1.000000 0.000000 0.000000 1.000000 5148.000000 -378.000000) translate(0,69)">房</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3b13220" transform="matrix(1.000000 0.000000 0.000000 1.000000 5148.000000 -378.000000) translate(0,87)">Ⅱ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3b13220" transform="matrix(1.000000 0.000000 0.000000 1.000000 5148.000000 -378.000000) translate(0,105)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3b13220" transform="matrix(1.000000 0.000000 0.000000 1.000000 5148.000000 -378.000000) translate(0,123)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f63bc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4748.000000 -448.000000) translate(0,15)">极</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f63bc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4748.000000 -448.000000) translate(0,33)">一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f63bc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4748.000000 -448.000000) translate(0,51)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f63bc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4748.000000 -448.000000) translate(0,69)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f63bc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4748.000000 -448.000000) translate(0,87)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f63bc0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4748.000000 -448.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fdefb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4876.000000 -448.000000) translate(0,15)">极</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fdefb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4876.000000 -448.000000) translate(0,33)">二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fdefb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4876.000000 -448.000000) translate(0,51)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fdefb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4876.000000 -448.000000) translate(0,69)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fdefb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4876.000000 -448.000000) translate(0,87)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fdefb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4876.000000 -448.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4142200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3430.000000 -592.000000) translate(0,15)">10kV Ⅰ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_419a170" transform="matrix(1.000000 0.000000 0.000000 1.000000 3517.000000 -448.000000) translate(0,15)">极</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_419a170" transform="matrix(1.000000 0.000000 0.000000 1.000000 3517.000000 -448.000000) translate(0,33)">一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_419a170" transform="matrix(1.000000 0.000000 0.000000 1.000000 3517.000000 -448.000000) translate(0,51)"/>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_419a170" transform="matrix(1.000000 0.000000 0.000000 1.000000 3517.000000 -448.000000) translate(0,69)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_419a170" transform="matrix(1.000000 0.000000 0.000000 1.000000 3517.000000 -448.000000) translate(0,87)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_419a170" transform="matrix(1.000000 0.000000 0.000000 1.000000 3517.000000 -448.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_411cac0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3729.000000 -1096.000000) translate(0,15)">1号站用变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_411cac0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3729.000000 -1096.000000) translate(0,33)">油浸自冷式（ONAN）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_411cac0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3729.000000 -1096.000000) translate(0,51)">36±4×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_411cac0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3729.000000 -1096.000000) translate(0,69)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_411cac0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3729.000000 -1096.000000) translate(0,87)">5000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_411cac0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3729.000000 -1096.000000) translate(0,105)">Uk%=7</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40cf890" transform="matrix(1.000000 0.000000 0.000000 1.000000 3595.000000 -1181.000000) translate(0,15)">引自1号站用变35kV低压侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4004e40" transform="matrix(1.000000 0.000000 0.000000 1.000000 3534.000000 -1059.000000) translate(0,15)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2059770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3631.000000 -837.000000) translate(0,12)">00167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f71350" transform="matrix(1.000000 0.000000 0.000000 1.000000 3585.000000 -729.000000) translate(0,15)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fec5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3826.000000 -625.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_403e710" transform="matrix(1.000000 0.000000 0.000000 1.000000 3753.000000 -822.000000) translate(0,15)">10kVⅠ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e96b20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3753.000000 -802.000000) translate(0,15)">母线电压</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e6f280" transform="matrix(1.000000 0.000000 0.000000 1.000000 3762.000000 -778.000000) translate(0,15)">互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f4ac90" transform="matrix(1.000000 0.000000 0.000000 1.000000 4341.000000 -696.000000) translate(0,15)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34fda50" transform="matrix(1.000000 0.000000 0.000000 1.000000 4126.000000 -1193.000000) translate(0,15)">引自220kV方山变35kV间隔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_325f090" transform="matrix(1.000000 0.000000 0.000000 1.000000 4277.000000 -924.000000) translate(0,15)">3号站用变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_325f090" transform="matrix(1.000000 0.000000 0.000000 1.000000 4277.000000 -924.000000) translate(0,33)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_325f090" transform="matrix(1.000000 0.000000 0.000000 1.000000 4277.000000 -924.000000) translate(0,51)">35±4×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_325f090" transform="matrix(1.000000 0.000000 0.000000 1.000000 4277.000000 -924.000000) translate(0,69)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_325f090" transform="matrix(1.000000 0.000000 0.000000 1.000000 4277.000000 -924.000000) translate(0,87)">5000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_325f090" transform="matrix(1.000000 0.000000 0.000000 1.000000 4277.000000 -924.000000) translate(0,105)">Uk%=7</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40dc4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4301.000000 -592.000000) translate(0,15)">10kV Ⅲ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_325f3f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4868.000000 -1096.000000) translate(0,15)">2号站用变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_325f3f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4868.000000 -1096.000000) translate(0,33)">油浸自冷式（ONAN）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_325f3f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4868.000000 -1096.000000) translate(0,51)">36±4×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_325f3f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4868.000000 -1096.000000) translate(0,69)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_325f3f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4868.000000 -1096.000000) translate(0,87)">5000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_325f3f0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4868.000000 -1096.000000) translate(0,105)">Uk%=7</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fc1660" transform="matrix(1.000000 0.000000 0.000000 1.000000 4734.000000 -1181.000000) translate(0,15)">引自2号站用变35kV低压侧</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4821760" transform="matrix(1.000000 0.000000 0.000000 1.000000 4673.000000 -1059.000000) translate(0,15)">35kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4821970" transform="matrix(1.000000 0.000000 0.000000 1.000000 4724.000000 -729.000000) translate(0,15)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fe10c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4962.000000 -626.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4195f50" transform="matrix(1.000000 0.000000 0.000000 1.000000 4889.000000 -823.000000) translate(0,15)">10kVⅡ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4008900" transform="matrix(1.000000 0.000000 0.000000 1.000000 4889.000000 -803.000000) translate(0,15)">母线电压</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_414e260" transform="matrix(1.000000 0.000000 0.000000 1.000000 4898.000000 -779.000000) translate(0,15)">互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40c2d30" transform="matrix(1.000000 0.000000 0.000000 1.000000 3484.000000 -253.000000) translate(0,15)">极一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40c5ea0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3488.500000 -233.000000) translate(0,15)">1号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40cd400" transform="matrix(1.000000 0.000000 0.000000 1.000000 3475.000000 -215.000000) translate(0,15)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_41b2ce0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3616.000000 -253.000000) translate(0,15)">极二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_41a22d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3620.500000 -233.000000) translate(0,15)">1号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4073080" transform="matrix(1.000000 0.000000 0.000000 1.000000 3607.000000 -215.000000) translate(0,15)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_41a4ed0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3749.000000 -253.000000) translate(0,15)">1号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_4059040" transform="matrix(1.000000 0.000000 0.000000 1.000000 3744.500000 -233.000000) translate(0,15)">公用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ea3100" transform="matrix(1.000000 0.000000 0.000000 1.000000 3735.500000 -215.000000) translate(0,15)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_33011d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4714.000000 -253.000000) translate(0,15)">极一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f2aca0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4718.500000 -233.000000) translate(0,15)">2号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fdebe0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4705.000000 -215.000000) translate(0,15)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3b893d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4846.000000 -253.000000) translate(0,15)">极二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f06750" transform="matrix(1.000000 0.000000 0.000000 1.000000 4850.500000 -233.000000) translate(0,15)">2号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3dcd940" transform="matrix(1.000000 0.000000 0.000000 1.000000 4837.000000 -215.000000) translate(0,15)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_41539e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4979.000000 -253.000000) translate(0,15)">2号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40e1cb0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4974.500000 -233.000000) translate(0,15)">公用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3fcf9c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4965.500000 -215.000000) translate(0,15)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_409f710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4565.000000 -592.000000) translate(0,15)">10kV Ⅱ段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40892a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4239.000000 -974.000000) translate(0,12)">311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4084390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4232.000000 -1041.000000) translate(0,12)">3111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40845d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4171.000000 -1095.000000) translate(0,12)">31117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_405c450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4172.000000 -1022.000000) translate(0,12)">31110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_405c690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4171.000000 -956.000000) translate(0,12)">31167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32ca1e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3695.000000 -659.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4039690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4243.000000 -646.000000) translate(0,12)">003</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40398d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4175.000000 -743.000000) translate(0,12)">00367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20acce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4834.000000 -661.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20acf20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4771.000000 -867.000000) translate(0,12)">00267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f92740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3469.000000 -505.000000) translate(0,12)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4112940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3455.000000 -451.000000) translate(0,12)">05167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4112b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3599.000000 -506.000000) translate(0,12)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f376e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3583.000000 -451.000000) translate(0,12)">05267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f37920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3727.000000 -506.000000) translate(0,12)">053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fb4370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3709.000000 -451.000000) translate(0,12)">05367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f07550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3856.000000 -505.000000) translate(0,12)">054</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f07790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3839.000000 -449.000000) translate(0,12)">05467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41583c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4061.000000 -506.000000) translate(0,12)">013</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4158600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4138.000000 -508.000000) translate(0,12)">0133</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40e29a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3934.000000 -511.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40e2be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4510.000000 -504.000000) translate(0,12)">023</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e40ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4395.000000 -515.000000) translate(0,12)">0233</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e410e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4653.000000 -507.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f6f7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4749.000000 -508.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3efbf00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4684.000000 -452.000000) translate(0,12)">06167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3efc140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4877.000000 -508.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f436e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4811.000000 -452.000000) translate(0,12)">06267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f438e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5005.000000 -508.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fd9660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4939.000000 -452.000000) translate(0,12)">06367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3954a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5133.000000 -508.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3954cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5068.000000 -452.000000) translate(0,12)">06467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e96260" transform="matrix(1.000000 0.000000 0.000000 1.000000 4108.000000 -1100.000000) translate(0,15)">永</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e96260" transform="matrix(1.000000 0.000000 0.000000 1.000000 4108.000000 -1100.000000) translate(0,33)">仁</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e96260" transform="matrix(1.000000 0.000000 0.000000 1.000000 4108.000000 -1100.000000) translate(0,51)">换</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e96260" transform="matrix(1.000000 0.000000 0.000000 1.000000 4108.000000 -1100.000000) translate(0,69)">流</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e96260" transform="matrix(1.000000 0.000000 0.000000 1.000000 4108.000000 -1100.000000) translate(0,87)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3e96260" transform="matrix(1.000000 0.000000 0.000000 1.000000 4108.000000 -1100.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ee54c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3921.000000 -110.000000) translate(0,15)">1号抽水专变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ee54c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3921.000000 -110.000000) translate(0,33)">S11-100/10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ee54c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3921.000000 -110.000000) translate(0,51)">10±2×2.5%/0.4kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ee54c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3921.000000 -110.000000) translate(0,69)">Dyn11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3ee54c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3921.000000 -110.000000) translate(0,87)">Uk%=4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2042350" transform="matrix(1.000000 0.000000 0.000000 1.000000 5156.000000 -110.000000) translate(0,15)">2号抽水专变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2042350" transform="matrix(1.000000 0.000000 0.000000 1.000000 5156.000000 -110.000000) translate(0,33)">S11-100/10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2042350" transform="matrix(1.000000 0.000000 0.000000 1.000000 5156.000000 -110.000000) translate(0,51)">10±2×2.5%/0.4kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2042350" transform="matrix(1.000000 0.000000 0.000000 1.000000 5156.000000 -110.000000) translate(0,69)">Dyn11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2042350" transform="matrix(1.000000 0.000000 0.000000 1.000000 5156.000000 -110.000000) translate(0,87)">Uk%=4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2042630" transform="matrix(1.000000 0.000000 0.000000 1.000000 3728.000000 -82.000000) translate(0,15)">10kV1号抽水专变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40966d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4960.000000 -82.000000) translate(0,15)">10kV2号抽水专变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40968e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3921.000000 18.000000) translate(0,15)">JP柜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f95ad0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5156.000000 18.000000) translate(0,15)">JP柜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_411e3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3848.000000 -295.000000) translate(0,12)">0546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_411e5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3848.000000 -159.000000) translate(0,12)">0816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40d1070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5076.000000 -299.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40d1270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5076.000000 -165.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41a6970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4079.000000 -878.000000) translate(0,12)">35kV_3号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5b1f680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3692.000000 -1118.000000) translate(0,12)">317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_5a60c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4831.000000 -1118.000000) translate(0,12)">327</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_416a860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3600.000000 1106.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4000310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3586.000000 1136.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e9c9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3575.000000 1121.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40a3320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3427.000000 617.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f31200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4294.000000 617.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e96000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4564.000000 617.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_YRH.CX_YRH_9ⅡM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4483,-567 5150,-567 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="23588" ObjectName="BS-CX_YRH.CX_YRH_9ⅡM"/>
    <cge:TPSR_Ref TObjectID="23588"/></metadata>
   <polyline fill="none" opacity="0" points="4483,-567 5150,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YRH.CX_YRH_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3425,-567 4081,-567 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="23587" ObjectName="BS-CX_YRH.CX_YRH_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="23587"/></metadata>
   <polyline fill="none" opacity="0" points="3425,-567 4081,-567 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YRH.CX_YRH_9ⅢM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4131,-567 4418,-567 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="23589" ObjectName="BS-CX_YRH.CX_YRH_9ⅢM"/>
    <cge:TPSR_Ref TObjectID="23589"/></metadata>
   <polyline fill="none" opacity="0" points="4131,-567 4418,-567 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-0" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3411.000000 -1086.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-127256">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3495.000000 -447.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23411" ObjectName="SW-CX_YRH.CX_YRH_051BK"/>
     <cge:Meas_Ref ObjectId="127256"/>
    <cge:TPSR_Ref TObjectID="23411"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127257">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3623.000000 -446.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23412" ObjectName="SW-CX_YRH.CX_YRH_052BK"/>
     <cge:Meas_Ref ObjectId="127257"/>
    <cge:TPSR_Ref TObjectID="23412"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127258">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3751.000000 -446.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23413" ObjectName="SW-CX_YRH.CX_YRH_053BK"/>
     <cge:Meas_Ref ObjectId="127258"/>
    <cge:TPSR_Ref TObjectID="23413"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127259">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3879.000000 -446.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23414" ObjectName="SW-CX_YRH.CX_YRH_054BK"/>
     <cge:Meas_Ref ObjectId="127259"/>
    <cge:TPSR_Ref TObjectID="23414"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127277">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4038.925566 -446.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23432" ObjectName="SW-CX_YRH.CX_YRH_013BK"/>
     <cge:Meas_Ref ObjectId="127277"/>
    <cge:TPSR_Ref TObjectID="23432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127279">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4534.925566 -446.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23434" ObjectName="SW-CX_YRH.CX_YRH_023BK"/>
     <cge:Meas_Ref ObjectId="127279"/>
    <cge:TPSR_Ref TObjectID="23434"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127278">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4630.925566 -447.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23433" ObjectName="SW-CX_YRH.CX_YRH_012BK"/>
     <cge:Meas_Ref ObjectId="127278"/>
    <cge:TPSR_Ref TObjectID="23433"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127267">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4727.000000 -448.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23422" ObjectName="SW-CX_YRH.CX_YRH_061BK"/>
     <cge:Meas_Ref ObjectId="127267"/>
    <cge:TPSR_Ref TObjectID="23422"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127268">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4855.000000 -448.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23423" ObjectName="SW-CX_YRH.CX_YRH_062BK"/>
     <cge:Meas_Ref ObjectId="127268"/>
    <cge:TPSR_Ref TObjectID="23423"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127269">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4983.000000 -448.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23424" ObjectName="SW-CX_YRH.CX_YRH_063BK"/>
     <cge:Meas_Ref ObjectId="127269"/>
    <cge:TPSR_Ref TObjectID="23424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127270">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5111.000000 -448.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23425" ObjectName="SW-CX_YRH.CX_YRH_064BK"/>
     <cge:Meas_Ref ObjectId="127270"/>
    <cge:TPSR_Ref TObjectID="23425"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127255">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3673.000000 -601.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23410" ObjectName="SW-CX_YRH.CX_YRH_001BK"/>
     <cge:Meas_Ref ObjectId="127255"/>
    <cge:TPSR_Ref TObjectID="23410"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127276">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4218.000000 -583.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23431" ObjectName="SW-CX_YRH.CX_YRH_003BK"/>
     <cge:Meas_Ref ObjectId="127276"/>
    <cge:TPSR_Ref TObjectID="23431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127250">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4219.000000 -945.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23405" ObjectName="SW-CX_YRH.CX_YRH_311BK"/>
     <cge:Meas_Ref ObjectId="127250"/>
    <cge:TPSR_Ref TObjectID="23405"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127266">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4812.000000 -601.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23421" ObjectName="SW-CX_YRH.CX_YRH_002BK"/>
     <cge:Meas_Ref ObjectId="127266"/>
    <cge:TPSR_Ref TObjectID="23421"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127299">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3674.000000 -1089.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23454" ObjectName="SW-CX_YRH.CX_YRH_317BK"/>
     <cge:Meas_Ref ObjectId="127299"/>
    <cge:TPSR_Ref TObjectID="23454"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127319">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4813.000000 -1089.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23474" ObjectName="SW-CX_YRH.CX_YRH_327BK"/>
     <cge:Meas_Ref ObjectId="127319"/>
    <cge:TPSR_Ref TObjectID="23474"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3218c00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3430.000000 -389.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33871b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3489.000000 -244.000000)" xlink:href="#lightningRod:shape101"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4144b60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3558.000000 -388.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4003b60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3617.000000 -245.000000)" xlink:href="#lightningRod:shape101"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e86b10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3686.000000 -388.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40b1560">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3745.000000 -242.000000)" xlink:href="#lightningRod:shape101"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40b6c20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3814.000000 -388.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_408de10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4057.925566 -420.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4081cc0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4274.582524 -449.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_400a970">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4472.000000 -421.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40944b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4569.000000 -421.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40e5b20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4662.000000 -390.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40acaa0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4721.000000 -244.000000)" xlink:href="#lightningRod:shape101"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39c7760">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4790.000000 -390.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41424c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4849.000000 -244.000000)" xlink:href="#lightningRod:shape101"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f26dd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4918.000000 -390.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f0c320">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4977.000000 -244.000000)" xlink:href="#lightningRod:shape101"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f10160">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5046.000000 -390.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fc6850">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3694.000000 -868.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f0da90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3696.000000 -964.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_407eb30">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3773.000000 -652.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2033540">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4238.000000 -734.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f11570">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4239.000000 -795.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f0b8f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4156.000000 -1117.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41567e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4833.000000 -868.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3feb1d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4835.000000 -964.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fa54b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4909.000000 -653.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4012f30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3814.000000 -315.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f6a150">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3814.000000 -250.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fb9d00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3814.000000 -194.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_40f6290">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3814.000000 -111.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f0e770">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3814.000000 -27.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_409ea70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5046.000000 -317.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41ab5d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5046.000000 -252.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f2a7d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5046.000000 -196.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41b8510">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5046.000000 -113.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_411a800">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5046.000000 -29.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1"/>
</svg>