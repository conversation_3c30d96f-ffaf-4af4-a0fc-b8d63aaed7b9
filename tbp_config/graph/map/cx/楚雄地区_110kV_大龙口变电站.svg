<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-32" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3013 -1226 2098 1224">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.416609" width="14" x="3" y="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="25" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="91" y2="82"/>
    <polyline points="10,25 10,39 " stroke-width="1.94231"/>
    <polyline points="10,82 10,66 " stroke-width="1.94231"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="99" y2="90"/>
    <rect height="27" stroke-width="0.416609" width="14" x="3" y="40"/>
    <polyline points="10,82 10,66 " stroke-width="1"/>
    <polyline points="11,25 11,40 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="20" x2="11" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="11" x2="2" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="9" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="98" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <polyline points="10,82 10,66 " stroke-width="1"/>
    <rect height="27" stroke-width="0.416609" width="14" x="3" y="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="25" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="91" y2="82"/>
    <polyline points="10,25 10,39 " stroke-width="1"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="98" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="9" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="11" x2="2" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="20" x2="11" y1="17" y2="8"/>
    <polyline points="11,25 11,40 " stroke-width="1"/>
    <polyline points="10,82 10,66 " stroke-width="1"/>
    <rect height="27" stroke-width="0.416609" width="14" x="3" y="40"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="13" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="20" y2="20"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="58" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="8" y2="37"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape83">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.504561" x1="37" x2="37" y1="19" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="57" x2="57" y1="5" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="2" x2="2" y1="58" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="20" x2="20" y1="5" y2="11"/>
    <rect height="26" stroke-width="0.398039" width="12" x="31" y="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.393258" x1="21" x2="56" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="68" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="68" y2="59"/>
    <polyline arcFlag="1" points="13,48 12,48 12,48 11,48 10,48 10,49 9,49 8,50 8,50 8,51 7,52 7,52 7,53 7,54 7,55 7,55 8,56 8,57 8,57 9,58 10,58 10,59 11,59 12,59 12,59 13,59 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,37 12,37 12,37 11,37 10,37 10,38 9,38 8,39 8,39 8,40 7,41 7,41 7,42 7,43 7,44 7,44 8,45 8,46 8,46 9,47 10,47 10,48 11,48 12,48 12,48 13,48 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,26 12,26 12,26 11,26 10,26 10,27 9,27 8,28 8,28 8,29 7,30 7,30 7,31 7,32 7,33 7,33 8,34 8,35 8,35 9,36 10,36 10,37 11,37 12,37 12,37 13,37 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="26" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.529576" x1="37" x2="37" y1="75" y2="28"/>
   </symbol>
   <symbol id="lightningRod:shape12">
    <polyline points="9,14 3,17 1,18 1,19 1,19 3,21 6,22 10,24 11,25 11,25 11,26 10,27 6,28 3,30 2,30 2,31 2,32 3,33 6,34 10,36 11,36 11,37 11,38 10,38 6,40 3,41 2,42 2,43 2,44 3,44 6,46 10,47 11,48 11,49 11,49 10,50 6,52 3,53 1,55 1,55 1,56 3,57 9,60 " stroke-width="2.00006"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="15" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="59" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="6" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="0" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="4" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape17">
    <rect height="29" stroke-width="2" width="15" x="1" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.00003" x1="8" x2="11" y1="23" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.00003" x1="9" x2="6" y1="23" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.8" x1="8" x2="8" y1="8" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="6" x2="11" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="1" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="4" x2="13" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="8" x2="8" y1="51" y2="23"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape105">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="13" x2="13" y1="39" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44" x1="13" x2="13" y1="5" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="0" x2="12" y1="26" y2="26"/>
    <polyline points="13,39 15,39 17,38 18,38 20,37 21,36 23,35 24,33 25,31 25,30 26,28 26,26 26,24 25,22 25,21 24,19 23,18 21,16 20,15 18,14 17,14 15,13 13,13 11,13 9,14 8,14 6,15 5,16 3,18 2,19 1,21 1,22 0,24 0,26 " stroke-width="0.0972"/>
   </symbol>
   <symbol id="lightningRod:shape117">
    <rect height="23" stroke-width="1" width="35" x="0" y="0"/>
    <text font-family="SimSun" font-size="12" graphid="g_370a190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7.000000 16.000000) translate(0,10)">SVG</text>
   </symbol>
   <symbol id="lightningRod:shape118">
    <ellipse cx="13" cy="17" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="18" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="18" y1="41" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="9" y1="41" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="15" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="11" y2="15"/>
    <circle cx="13" cy="34" fillStyle="0" r="13" stroke-width="0.265306"/>
   </symbol>
   <symbol id="lightningRod:shape141">
    <polyline DF8003:Layer="PUBLIC" points="18,1 18,16 30,8 18,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="4" x2="84" y1="9" y2="9"/>
    <polyline DF8003:Layer="PUBLIC" points="72,1 72,16 60,8 72,1 "/>
   </symbol>
   <symbol id="lightningRod:shape154">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.360938" x1="2" x2="9" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="6" x2="6" y1="6" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.229167" x1="4" x2="7" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="11" x2="0" y1="33" y2="33"/>
   </symbol>
   <symbol id="lightningRod:shape75">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="26" y1="33" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="1" x2="17" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="1" x2="18" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.676705" x1="9" x2="9" y1="25" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="1" x2="17" y1="44" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="1" x2="18" y1="51" y2="51"/>
    <circle cx="42" cy="29" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="35" cy="33" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="42" cy="39" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="11" y1="66" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="5" x2="13" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="9" y1="60" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="15" x2="3" y1="61" y2="60"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="transformer2:shape22_0">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline points="64,100 64,93 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="30" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="30" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="38" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="38" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="62" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="62" y2="71"/>
   </symbol>
   <symbol id="transformer2:shape22_1">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="transformer2:shape16_0">
    <circle cx="29" cy="25" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="31" x2="23" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="23" x2="15" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="15" x2="23" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape16_1">
    <ellipse cx="60" cy="25" fillStyle="0" rx="24.5" ry="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="74" x2="66" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="66" x2="58" y1="25" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="58" x2="66" y1="17" y2="25"/>
   </symbol>
   <symbol id="voltageTransformer:shape16">
    <circle cx="44" cy="32" r="11" stroke-width="1.15698"/>
    <polyline points="9,28 17,28 " stroke-width="1"/>
    <polyline points="1,28 9,28 " stroke-width="1"/>
    <polyline points="44,16 44,7 " stroke-width="1"/>
    <polyline points="44,37 44,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.15692" x1="40" x2="44" y1="40" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.15692" x1="49" x2="44" y1="40" y2="37"/>
    <polyline points="6,40 12,40 " stroke-width="1"/>
    <polyline points="4,34 14,34 " stroke-width="1"/>
    <polyline points="33,20 9,20 9,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.18573" x1="64" x2="58" y1="24" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.15695" x1="58" x2="58" y1="28" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.19217" x1="64" x2="58" y1="24" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.15692" x1="49" x2="44" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.15692" x1="40" x2="44" y1="19" y2="16"/>
    <circle cx="44" cy="15" r="11" stroke-width="1.15698"/>
    <ellipse cx="58" cy="24" rx="11.5" ry="11" stroke-width="1.15698"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3139710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30e09f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30cd5f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30778b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30781e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30cb970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30e02a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_30ced90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3117bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3117bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30750e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30750e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30119c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30119c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_3116e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30d0500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_30f8d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2fb5100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2fbc4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3118dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3012620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3075910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3117e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3115ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3083910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3053860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_307d3e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2fb67b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3075ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_307dfd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2fbbc80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ff8030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3136570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_31aa750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_30c7040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1234" width="2108" x="3008" y="-1231"/>
  </g><g id="CircleFilled_Layer">
   <ellipse DF8003:Layer="PUBLIC" cx="5000" cy="-824" fill="none" fillStyle="0" rx="8" ry="8.5" stroke="rgb(60,120,255)" stroke-width="0.173469"/>
   <ellipse DF8003:Layer="PUBLIC" cx="5000" cy="-836" fill="none" fillStyle="0" rx="8" ry="8.5" stroke="rgb(60,120,255)" stroke-width="0.173469"/>
   <ellipse DF8003:Layer="PUBLIC" cx="5061" cy="-824" fill="none" fillStyle="0" rx="8" ry="8.5" stroke="rgb(60,120,255)" stroke-width="0.173469"/>
   <ellipse DF8003:Layer="PUBLIC" cx="5061" cy="-836" fill="none" fillStyle="0" rx="8" ry="8.5" stroke="rgb(60,120,255)" stroke-width="0.173469"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.596178" x1="5110" x2="5099" y1="-185" y2="-185"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.322998" x1="5101" x2="5108" y1="-180" y2="-180"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.228882" x1="5103" x2="5106" y1="-176" y2="-176"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5104,-185 5104,-225 5062,-225 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.596178" x1="3604" x2="3593" y1="-190" y2="-190"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.322998" x1="3595" x2="3602" y1="-185" y2="-185"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.228882" x1="3597" x2="3600" y1="-181" y2="-181"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3598,-190 3598,-230 3555,-230 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1.92605" x1="5055" x2="5062" y1="-257" y2="-252"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1.92605" x1="5068" x2="5062" y1="-257" y2="-252"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1.92606" x1="5062" x2="5062" y1="-252" y2="-246"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1.92605" x1="5055" x2="5062" y1="-230" y2="-225"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1.92606" x1="5062" x2="5062" y1="-225" y2="-219"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1.92605" x1="5068" x2="5062" y1="-230" y2="-225"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5000" x2="5000" y1="-799" y2="-816"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.173469" x1="5058" x2="5061" y1="-839" y2="-837"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.173469" x1="5061" x2="5063" y1="-837" y2="-839"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.173469" x1="5058" x2="5063" y1="-840" y2="-840"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4999" x2="4999" y1="-845" y2="-869"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5000" x2="5031" y1="-824" y2="-824"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.173469" x1="4997" x2="5000" y1="-826" y2="-824"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.173469" x1="5000" x2="5002" y1="-824" y2="-826"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.173469" x1="5000" x2="5000" y1="-821" y2="-824"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5061" x2="5061" y1="-799" y2="-816"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5060" x2="5060" y1="-845" y2="-869"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5061" x2="5092" y1="-824" y2="-824"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.173469" x1="5058" x2="5061" y1="-826" y2="-824"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.173469" x1="5061" x2="5063" y1="-824" y2="-826"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.173469" x1="5061" x2="5061" y1="-821" y2="-824"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.173469" x1="4997" x2="5000" y1="-840" y2="-838"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.173469" x1="5000" x2="5002" y1="-838" y2="-840"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.173469" x1="4997" x2="5002" y1="-841" y2="-841"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-179 5056,-188 5065,-188 5060,-179 5060,-180 5060,-179 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3554,-184 3550,-193 3559,-193 3554,-184 3554,-185 3554,-184 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4999,-853 4995,-859 5003,-859 4999,-853 4999,-854 4999,-853 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-853 5056,-859 5064,-859 5060,-853 5060,-854 5060,-853 " stroke="rgb(60,120,255)"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-25172">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3829.389652 -1003.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3913" ObjectName="SW-CX_DLK.CX_DLK_122BK"/>
     <cge:Meas_Ref ObjectId="25172"/>
    <cge:TPSR_Ref TObjectID="3913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25193">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4058.507647 -599.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3934" ObjectName="SW-CX_DLK.CX_DLK_302BK"/>
     <cge:Meas_Ref ObjectId="25193"/>
    <cge:TPSR_Ref TObjectID="3934"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25274">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3546.300126 -428.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4012" ObjectName="SW-CX_DLK.CX_DLK_387BK"/>
     <cge:Meas_Ref ObjectId="25274"/>
    <cge:TPSR_Ref TObjectID="4012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25190">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4058.507647 -800.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3931" ObjectName="SW-CX_DLK.CX_DLK_102BK"/>
     <cge:Meas_Ref ObjectId="25190"/>
    <cge:TPSR_Ref TObjectID="3931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25178">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4317.321014 -838.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3919" ObjectName="SW-CX_DLK.CX_DLK_112BK"/>
     <cge:Meas_Ref ObjectId="25178"/>
    <cge:TPSR_Ref TObjectID="3919"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25166">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4755.389652 -1007.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3907" ObjectName="SW-CX_DLK.CX_DLK_121BK"/>
     <cge:Meas_Ref ObjectId="25166"/>
    <cge:TPSR_Ref TObjectID="3907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25270">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3650.142497 -430.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4008" ObjectName="SW-CX_DLK.CX_DLK_386BK"/>
     <cge:Meas_Ref ObjectId="25270"/>
    <cge:TPSR_Ref TObjectID="4008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25252">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4080.996217 -432.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3990" ObjectName="SW-CX_DLK.CX_DLK_382BK"/>
     <cge:Meas_Ref ObjectId="25252"/>
    <cge:TPSR_Ref TObjectID="3990"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25266">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3759.142497 -428.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4004" ObjectName="SW-CX_DLK.CX_DLK_385BK"/>
     <cge:Meas_Ref ObjectId="25266"/>
    <cge:TPSR_Ref TObjectID="4004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25262">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3864.142497 -435.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4000" ObjectName="SW-CX_DLK.CX_DLK_384BK"/>
     <cge:Meas_Ref ObjectId="25262"/>
    <cge:TPSR_Ref TObjectID="4000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25258">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3976.142497 -431.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3996" ObjectName="SW-CX_DLK.CX_DLK_383BK"/>
     <cge:Meas_Ref ObjectId="25258"/>
    <cge:TPSR_Ref TObjectID="3996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25246">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4184.996217 -430.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3984" ObjectName="SW-CX_DLK.CX_DLK_381BK"/>
     <cge:Meas_Ref ObjectId="25246"/>
    <cge:TPSR_Ref TObjectID="3984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25243">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4245.821014 -620.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3981" ObjectName="SW-CX_DLK.CX_DLK_312BK"/>
     <cge:Meas_Ref ObjectId="25243"/>
    <cge:TPSR_Ref TObjectID="3981"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25227">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4536.142497 -432.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3965" ObjectName="SW-CX_DLK.CX_DLK_363BK"/>
     <cge:Meas_Ref ObjectId="25227"/>
    <cge:TPSR_Ref TObjectID="3965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25237">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4325.996217 -433.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3975" ObjectName="SW-CX_DLK.CX_DLK_361BK"/>
     <cge:Meas_Ref ObjectId="25237"/>
    <cge:TPSR_Ref TObjectID="3975"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25231">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4431.996217 -432.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3969" ObjectName="SW-CX_DLK.CX_DLK_362BK"/>
     <cge:Meas_Ref ObjectId="25231"/>
    <cge:TPSR_Ref TObjectID="3969"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25223">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4640.142497 -433.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3961" ObjectName="SW-CX_DLK.CX_DLK_364BK"/>
     <cge:Meas_Ref ObjectId="25223"/>
    <cge:TPSR_Ref TObjectID="3961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25219">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4745.142497 -432.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3957" ObjectName="SW-CX_DLK.CX_DLK_365BK"/>
     <cge:Meas_Ref ObjectId="25219"/>
    <cge:TPSR_Ref TObjectID="3957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25215">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4847.142497 -431.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3953" ObjectName="SW-CX_DLK.CX_DLK_366BK"/>
     <cge:Meas_Ref ObjectId="25215"/>
    <cge:TPSR_Ref TObjectID="3953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25211">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4951.142497 -431.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3949" ObjectName="SW-CX_DLK.CX_DLK_367BK"/>
     <cge:Meas_Ref ObjectId="25211"/>
    <cge:TPSR_Ref TObjectID="3949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25207">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5052.300126 -432.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3945" ObjectName="SW-CX_DLK.CX_DLK_368BK"/>
     <cge:Meas_Ref ObjectId="25207"/>
    <cge:TPSR_Ref TObjectID="3945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25186">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4522.507647 -598.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3927" ObjectName="SW-CX_DLK.CX_DLK_301BK"/>
     <cge:Meas_Ref ObjectId="25186"/>
    <cge:TPSR_Ref TObjectID="3927"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25183">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4522.507647 -800.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3924" ObjectName="SW-CX_DLK.CX_DLK_101BK"/>
     <cge:Meas_Ref ObjectId="25183"/>
    <cge:TPSR_Ref TObjectID="3924"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.600000 -0.000000 0.000000 -0.514851 4847.000000 -718.455446)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.600000 -0.000000 0.000000 -0.514851 4994.000000 -717.455446)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.600000 -0.000000 0.000000 -0.514851 5055.000000 -717.455446)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -0.600000 -0.514851 -0.000000 4955.544554 -649.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_367e4c0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4125.000000 -1072.000000)" xlink:href="#voltageTransformer:shape16"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3680550">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4464.000000 -1070.000000)" xlink:href="#voltageTransformer:shape16"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_381ec90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3762.000000 -677.000000)" xlink:href="#voltageTransformer:shape16"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3821170">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4697.000000 -671.000000)" xlink:href="#voltageTransformer:shape16"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_DLK.CX_DLK_1ⅡM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3493,-927 4306,-927 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="3904" ObjectName="BS-CX_DLK.CX_DLK_1ⅡM"/>
    <cge:TPSR_Ref TObjectID="3904"/></metadata>
   <polyline fill="none" opacity="0" points="3493,-927 4306,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DLK.CX_DLK_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3491,-536 4234,-536 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="3906" ObjectName="BS-CX_DLK.CX_DLK_3IIM"/>
    <cge:TPSR_Ref TObjectID="3906"/></metadata>
   <polyline fill="none" opacity="0" points="3491,-536 4234,-536 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DLK.CX_DLK_1ⅠM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4371,-926 5100,-926 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="3903" ObjectName="BS-CX_DLK.CX_DLK_1ⅠM"/>
    <cge:TPSR_Ref TObjectID="3903"/></metadata>
   <polyline fill="none" opacity="0" points="4371,-926 5100,-926 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DLK.CX_DLK_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4293,-538 5102,-538 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="3905" ObjectName="BS-CX_DLK.CX_DLK_3IM"/>
    <cge:TPSR_Ref TObjectID="3905"/></metadata>
   <polyline fill="none" opacity="0" points="4293,-538 5102,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4824,-676 4891,-676 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4824,-676 4891,-676 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4958,-674 5108,-674 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4958,-674 5108,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4824,-712 5108,-712 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4824,-712 5108,-712 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_DLK.CX_DLK_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="5778"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4030.000000 -698.000000)" xlink:href="#transformer2:shape22_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4030.000000 -698.000000)" xlink:href="#transformer2:shape22_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="4017" ObjectName="TF-CX_DLK.CX_DLK_2T"/>
    <cge:TPSR_Ref TObjectID="4017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 0.364583 0.360000 -0.000000 4844.000000 -848.906250)" xlink:href="#transformer2:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 0.364583 0.360000 -0.000000 4844.000000 -848.906250)" xlink:href="#transformer2:shape16_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_DLK.CX_DLK_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="5774"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4494.000000 -698.000000)" xlink:href="#transformer2:shape22_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4494.000000 -698.000000)" xlink:href="#transformer2:shape22_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="4016" ObjectName="TF-CX_DLK.CX_DLK_1T"/>
    <cge:TPSR_Ref TObjectID="4016"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_304b4c0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3993.000000 -697.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31aa440">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4012.000000 -698.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30d91d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3676.142497 -365.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30da1a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3545.300126 -285.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fca600">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3649.142497 -287.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fc1a90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3674.142497 -220.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fc3180">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4079.996217 -291.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3047510">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4103.996217 -367.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3124960">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3758.142497 -290.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_311ccb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3783.142497 -220.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_309d960">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3785.142497 -363.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_300a170">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3863.142497 -288.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3028720">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3888.142497 -221.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3029bb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3890.142497 -370.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fe4540">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3975.142497 -288.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_305dc40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4000.142497 -223.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_305f0d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4002.142497 -366.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3005f40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4183.996217 -289.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f8c290">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4207.996217 -365.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30ea6a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4535.142497 -291.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30ee290">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4563.142497 -224.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fcbce0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4565.142497 -367.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fd1420">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4297.996217 -70.000000)" xlink:href="#lightningRod:shape83"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fd3760">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4324.996217 -292.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fd7350">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4351.000000 -106.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fd80c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4352.996217 -368.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31b59c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4430.996217 -291.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31b95b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4454.996217 -367.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37f3a80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4639.142497 -292.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37f7670">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4664.142497 -225.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37f8b00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4666.142497 -368.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f78d10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4744.142497 -291.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f7c900">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4769.142497 -224.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f7d670">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4771.142497 -367.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f859f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4846.142497 -290.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f895e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4871.142497 -223.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30883a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4873.142497 -366.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30906d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4950.142497 -290.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30942c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4975.142497 -223.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3095750">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4977.142497 -366.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f9e6a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5051.300126 -291.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3311fb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5005.000000 -122.000000)" xlink:href="#lightningRod:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3329120">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4202.000000 -1108.000000)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33322d0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4541.000000 -1106.000000)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36d3390">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4734.000000 -608.055336)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36d67a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3799.000000 -614.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36f6680">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5078.142497 -366.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3707670">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4077.000000 -169.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3708050">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4322.000000 -171.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3709c10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4424.000000 -137.000000)" xlink:href="#lightningRod:shape117"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_370afd0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4428.000000 -175.000000)" xlink:href="#lightningRod:shape118"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_370bfd0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4181.000000 -172.000000)" xlink:href="#lightningRod:shape118"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_370d250">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4177.000000 -132.000000)" xlink:href="#lightningRod:shape117"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_370e3d0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4458.000000 -698.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_370ee20">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4477.000000 -699.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3711770">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4052.996217 -68.000000)" xlink:href="#lightningRod:shape83"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3713850">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4106.000000 -104.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37252a0">
    <use class="BV-35KV" transform="matrix(1.384615 -0.000000 0.000000 -1.288462 3537.000000 -211.000000)" xlink:href="#lightningRod:shape118"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_367d200">
    <use class="BV-110KV" transform="matrix(-0.000000 -1.000000 -1.000000 0.000000 3930.000000 -1129.000000)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_368a980">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4482.000000 -591.000000)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_368b720">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4019.000000 -592.000000)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_369a5c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3572.142497 -366.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36a4720">
    <use class="BV-0KV" transform="matrix(0.000000 -0.235955 -0.235294 -0.000000 4855.000000 -774.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36ab270">
    <use class="BV-0KV" transform="matrix(0.000000 -0.235955 -0.235294 -0.000000 5002.000000 -773.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36b2280">
    <use class="BV-0KV" transform="matrix(0.000000 -0.235955 -0.235294 -0.000000 5063.000000 -773.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36c38b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5087.000000 -717.000000)" xlink:href="#lightningRod:shape154"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3815900">
    <use class="BV-35KV" transform="matrix(0.714286 -0.000000 0.000000 -0.537815 4921.000000 -758.058824)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38193c0">
    <use class="BV-35KV" transform="matrix(0.625000 -0.000000 0.000000 -0.400000 4881.000000 -759.000000)" xlink:href="#lightningRod:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3838a80">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3869.500000 -1128.500000)" xlink:href="#lightningRod:shape75"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_383a4e0">
    <use class="BV-110KV" transform="matrix(-0.000000 -1.000000 -1.000000 0.000000 4896.000000 -1118.000000)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_383b5c0">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4826.500000 -1106.500000)" xlink:href="#lightningRod:shape75"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38427a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4779.000000 -606.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3844290">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3851.000000 -628.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3149.000000 -1143.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-25070" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3880.000000 -1166.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25070" ObjectName="CX_DLK:CX_DLK_122BK_Ux"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-25064" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4789.000000 -1164.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25064" ObjectName="CX_DLK:CX_DLK_121BK_Ux"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-78586" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3173.538462 -1041.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78586" ObjectName="CX_DLK:CX_DLK_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-61620" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3170.538462 -998.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="61620" ObjectName="CX_DLK:CX_DLK_sumQ"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="3906" cx="3555" cy="-536" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3906" cx="3659" cy="-536" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3906" cx="3768" cy="-536" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3906" cx="3873" cy="-536" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3906" cx="3985" cy="-536" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3906" cx="4090" cy="-536" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3906" cx="4194" cy="-536" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3906" cx="4068" cy="-536" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3906" cx="3806" cy="-536" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3903" cx="4386" cy="-926" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3903" cx="4764" cy="-926" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3903" cx="4532" cy="-926" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3903" cx="4508" cy="-926" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3904" cx="4068" cy="-927" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3904" cx="4293" cy="-927" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3904" cx="4169" cy="-927" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3904" cx="3838" cy="-927" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3905" cx="4335" cy="-538" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3905" cx="4441" cy="-538" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3905" cx="4544" cy="-538" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3905" cx="4649" cy="-538" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3905" cx="4754" cy="-538" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3905" cx="4960" cy="-538" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3905" cx="5061" cy="-538" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3905" cx="4741" cy="-538" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3905" cx="4856" cy="-538" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4853" cy="-676" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5000" cy="-674" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5061" cy="-674" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4977" cy="-674" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4870" cy="-676" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5093" cy="-712" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4833" cy="-712" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3905" cx="4532" cy="-538" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3906" cx="4222" cy="-536" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3905" cx="4310" cy="-538" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4980" cy="-712" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5041" cy="-712" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-25011" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3332.000000 -1111.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3876" ObjectName="DYN-CX_DLK"/>
     <cge:Meas_Ref ObjectId="25011"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_310e770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3514.000000 -136.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3085900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3633.000000 -216.000000) translate(0,15)">小箐山</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3085900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3633.000000 -216.000000) translate(0,33)">Ⅲ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f9dac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5027.000000 -143.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36d8050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3849.000000 -1032.000000) translate(0,12)">122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36d8d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3845.000000 -973.000000) translate(0,12)">1222</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36d8ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3844.500000 -1087.000000) translate(0,12)">1226</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36d9320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3784.000000 -1021.000000) translate(0,12)">12227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36d9820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3783.500000 -1075.000000) translate(0,12)">12260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36d9d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4175.000000 -1008.000000) translate(0,12)">1902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36da550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4115.000000 -988.000000) translate(0,12)">19020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36da7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4115.000000 -1057.000000) translate(0,12)">19027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36db780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4326.000000 -870.000000) translate(0,12)">112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36dbc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4453.000000 -985.000000) translate(0,12)">19010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36dbe40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4453.000000 -1057.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36dc080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4774.000000 -1036.000000) translate(0,12)">121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36dc2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4771.000000 -976.000000) translate(0,12)">1211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36dc500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4771.000000 -1092.000000) translate(0,12)">1216</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36dc740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4711.500000 -1026.000000) translate(0,12)">12117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36dc980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4711.000000 -1081.000000) translate(0,12)">12160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36dcbc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4711.500000 -1148.000000) translate(0,12)">K1217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36dd0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4082.500000 -829.000000) translate(0,12)">102</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36dd360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4078.500000 -895.000000) translate(0,12)">1022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36dd5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4020.000000 -875.000000) translate(0,12)">10227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36dd7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4547.500000 -829.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36dda20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4542.500000 -895.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ddc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4475.000000 -874.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ddea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4398.000000 -749.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36de0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3935.000000 -747.000000) translate(0,12)">1020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36de320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3514.000000 -457.500000) translate(0,12)">387</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36debc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3510.000000 -512.000000) translate(0,12)">3872</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36dee40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3509.500000 -401.000000) translate(0,12)">3876</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36df080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3505.000000 -375.000000) translate(0,12)">38767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36df2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3620.000000 -457.500000) translate(0,12)">386</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36df500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3616.000000 -512.000000) translate(0,12)">3862</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36df740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3615.500000 -401.000000) translate(0,12)">3866</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36df980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3611.000000 -375.000000) translate(0,12)">38667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36dfbc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3730.000000 -457.500000) translate(0,12)">385</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36dfe90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3726.000000 -512.000000) translate(0,12)">3852</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e0370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3725.500000 -401.000000) translate(0,12)">3856</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e05b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3721.000000 -375.000000) translate(0,12)">38567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e07f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3832.000000 -457.500000) translate(0,12)">384</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e0aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3828.000000 -512.000000) translate(0,12)">3842</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e0f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3823.000000 -375.000000) translate(0,12)">38467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e11d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3946.000000 -457.500000) translate(0,12)">383</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e1410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3942.000000 -512.000000) translate(0,12)">3832</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e1650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3941.500000 -401.000000) translate(0,12)">3836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e1890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3937.000000 -375.000000) translate(0,12)">38367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e1ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4049.000000 -457.500000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e1d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4045.000000 -512.000000) translate(0,12)">3822</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e1f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4044.500000 -409.000000) translate(0,12)">3823</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e4580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4095.000000 -276.000000) translate(0,12)">3826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e4bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4018.000000 -373.000000) translate(0,12)">38237</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e4df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4040.000000 -260.000000) translate(0,12)">38267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e7770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4158.000000 -457.500000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e7da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4154.000000 -512.000000) translate(0,12)">3812</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e7fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4144.500000 -404.000000) translate(0,12)">3813</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e8220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4199.000000 -276.000000) translate(0,12)">3816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e8460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4124.000000 -373.000000) translate(0,12)">38137</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e86a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4149.000000 -260.000000) translate(0,12)">38167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e88e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4256.000000 -635.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e8b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4318.000000 -587.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e8d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4176.000000 -585.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e8fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4297.000000 -457.500000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e91e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4293.000000 -512.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e9420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4292.500000 -401.000000) translate(0,12)">3613</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ee630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4340.000000 -276.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36eec60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4288.000000 -375.000000) translate(0,12)">36137</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36eeea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4288.000000 -260.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ef0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4402.000000 -457.500000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ef320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4398.000000 -512.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ef560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4446.000000 -276.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ef7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4397.500000 -401.000000) translate(0,12)">3623</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ef9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4393.000000 -375.000000) translate(0,12)">36237</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36efc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4393.000000 -260.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36efe60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4506.000000 -457.500000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f00a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4502.000000 -512.000000) translate(0,12)">3631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f02e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4501.500000 -401.000000) translate(0,12)">3636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f0520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4497.000000 -375.000000) translate(0,12)">36367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f0760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4610.000000 -457.500000) translate(0,12)">364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f09a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4606.000000 -512.000000) translate(0,12)">3641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f0be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4605.500000 -401.000000) translate(0,12)">3646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f0e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4601.000000 -375.000000) translate(0,12)">36467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f1060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4713.000000 -457.500000) translate(0,12)">365</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f12a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4709.000000 -512.000000) translate(0,12)">3651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f14e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4708.500000 -401.000000) translate(0,12)">3656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f1720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4704.000000 -375.000000) translate(0,12)">36567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f1960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4817.000000 -457.500000) translate(0,12)">366</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f1ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4813.000000 -512.000000) translate(0,12)">3661</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f1de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4812.500000 -401.000000) translate(0,12)">3666</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f2020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4808.000000 -375.000000) translate(0,12)">36667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f2260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4921.000000 -457.500000) translate(0,12)">367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f24a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4916.500000 -401.000000) translate(0,12)">3676</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f26e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4912.000000 -375.000000) translate(0,12)">36767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f2920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5023.000000 -457.500000) translate(0,12)">368</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f2b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5019.000000 -512.000000) translate(0,12)">3681</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f2da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5018.500000 -401.000000) translate(0,12)">3686</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f73f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5014.000000 -375.000000) translate(0,12)">36867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f7a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4917.000000 -512.000000) translate(0,12)">3671</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f7c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4082.000000 -631.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f7ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4078.000000 -578.000000) translate(0,12)">3022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f80e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4077.500000 -675.500000) translate(0,12)">3026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f8320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4547.000000 -629.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f8560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4543.000000 -580.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f87a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4542.500000 -675.500000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_36f89e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3740.000000 -216.000000) translate(0,15)">尖山梁子</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_36f89e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3740.000000 -216.000000) translate(0,33)">Ⅲ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_36f9650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3847.000000 -216.000000) translate(0,15)">尖山梁子</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_36f9650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3847.000000 -216.000000) translate(0,33)">Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_36f9be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3957.000000 -216.000000) translate(0,15)">尖山梁子</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_36f9be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3957.000000 -216.000000) translate(0,33)">Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_36fa160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4040.000000 -54.000000) translate(0,15)">2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_36fab50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3743.000000 -178.000000) translate(0,10)">（18号、</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_36fab50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3743.000000 -178.000000) translate(0,22)">  20~23、</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_36fab50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3743.000000 -178.000000) translate(0,34)">  27~33</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_36fab50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3743.000000 -178.000000) translate(0,46)">号发电机）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_36fdb80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3837.000000 -179.000000) translate(0,10)">（10、11、19、</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_36fdb80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3837.000000 -179.000000) translate(0,22)">   13~17、</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_36fdb80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3837.000000 -179.000000) translate(0,34)">   24~26</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_36fdb80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3837.000000 -179.000000) translate(0,46)">  号发电机）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_36fe9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3957.000000 -179.000000) translate(0,10)">（1~9、12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_36fe9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3957.000000 -179.000000) translate(0,22)"> 号发电机）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_36febf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4290.500000 -54.000000) translate(0,15)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_36fee30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4152.000000 -123.000000) translate(0,15)">2号动态无功</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_36fee30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4152.000000 -123.000000) translate(0,33)">补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3700670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4399.000000 -123.000000) translate(0,15)">1号动态无功</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3700670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4399.000000 -123.000000) translate(0,33)">补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3700900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4417.000000 -88.000000) translate(0,15)">±13MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3701900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4520.000000 -225.000000) translate(0,15)">小箐山</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3701900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4520.000000 -225.000000) translate(0,33)">Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3701dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4619.000000 -225.000000) translate(0,15)">小箐山</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3701dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4619.000000 -225.000000) translate(0,33)">Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3702000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4726.000000 -225.000000) translate(0,15)">梅家山</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3702000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4726.000000 -225.000000) translate(0,33)">Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_3702860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4728.000000 -185.000000) translate(0,10)">（16~27</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_3702860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4728.000000 -185.000000) translate(0,22)"> 号发电机）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_3702a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4827.000000 -182.000000) translate(0,10)">（1~11、15</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_3702a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4827.000000 -182.000000) translate(0,22)"> 号发电机）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3703070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4830.000000 -225.000000) translate(0,15)">梅家山</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3703070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4830.000000 -225.000000) translate(0,33)">Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3703270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4929.000000 -225.000000) translate(0,15)">梅家山</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3703270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4929.000000 -225.000000) translate(0,33)">Ⅲ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_37034b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4932.000000 -182.000000) translate(0,10)">（12~14、</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_37034b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4932.000000 -182.000000) translate(0,22)"> 28~33</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_37034b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4932.000000 -182.000000) translate(0,34)">号发电机）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3703710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3978.000000 -802.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3703be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4438.000000 -798.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3703e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3724.000000 -1164.000000) translate(0,15)">大龙口II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3704d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4132.000000 -1136.000000) translate(0,15)">110kVⅡ母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3705bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4468.000000 -1136.000000) translate(0,15)">110kVⅠ母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3705e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3494.000000 -951.000000) translate(0,15)">110kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3706440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4951.000000 -951.000000) translate(0,15)">110kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3706640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3767.000000 -744.000000) translate(0,15)">35kVⅡ母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3706b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4706.000000 -744.000000) translate(0,15)">35kVⅠ母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3706d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3493.000000 -564.000000) translate(0,15)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3706fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3813.000000 -584.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37071f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4748.000000 -578.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3707430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5007.000000 -267.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3667e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4515.000000 -1006.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3673780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3054.000000 -604.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3673780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3054.000000 -604.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3673780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3054.000000 -604.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3673780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3054.000000 -604.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3673780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3054.000000 -604.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3673780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3054.000000 -604.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3673780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3054.000000 -604.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3673780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3054.000000 -604.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3673780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3054.000000 -604.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3673780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3054.000000 -604.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3673780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3054.000000 -604.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3673780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3054.000000 -604.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3673780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3054.000000 -604.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3673780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3054.000000 -604.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3673780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3054.000000 -604.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3673780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3054.000000 -604.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3673780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3054.000000 -604.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3673780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3054.000000 -604.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3675970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3046.000000 -1083.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3675970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3046.000000 -1083.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3675970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3046.000000 -1083.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3675970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3046.000000 -1083.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3675970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3046.000000 -1083.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3675970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3046.000000 -1083.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3675970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3046.000000 -1083.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_3679910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3182.000000 -1192.500000) translate(0,16)">大龙口升压站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_367c7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3693.000000 -1160.000000) translate(0,12)">N32</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_367de90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3783.000000 -1133.000000) translate(0,12)">K1227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_36833b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5002.000000 -564.000000) translate(0,15)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3688ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4242.000000 -826.000000) translate(0,12)">11227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3689310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4389.000000 -826.000000) translate(0,12)">11217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_368a2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4246.500000 -895.000000) translate(0,12)">1122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_368a740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4393.500000 -895.000000) translate(0,12)">1121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_368e470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3827.500000 -401.000000) translate(0,12)">3846</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3691860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3705.000000 -1134.000000) translate(0,15)">110kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3691860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3705.000000 -1134.000000) translate(0,33)">姚</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3691860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3705.000000 -1134.000000) translate(0,51)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3691860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3705.000000 -1134.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3696730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4162.000000 -89.000000) translate(0,15)">±8MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_3696d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4064.000000 -69.000000) translate(0,10)">5004KVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_36976c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4308.000000 -69.000000) translate(0,10)">9000KVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_36b9750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4871.000000 -646.000000) translate(0,8)">升压站站用电接线示意图</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="8" graphid="g_36bbb90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4815.000000 -667.000000) translate(0,7)">0.4kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="8" graphid="g_36bda40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5055.000000 -667.000000) translate(0,7)">0.4kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_36be1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4825.000000 -892.000000) translate(0,8)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_36be1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4825.000000 -892.000000) translate(0,18)"> 1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_36c0400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4966.000000 -892.000000) translate(0,8)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_36c0400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4966.000000 -892.000000) translate(0,18)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_36c0dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5036.000000 -910.000000) translate(0,8)">35kV前厂变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_36c0dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5036.000000 -910.000000) translate(0,18)">10kV庄科线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_36c0dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5036.000000 -910.000000) translate(0,28)">杨冈箐支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_36c0dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5036.000000 -910.000000) translate(0,38)">3号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_36c2c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4891.000000 -797.000000) translate(0,10)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_36c2ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4861.000000 -851.000000) translate(0,8)">11B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_36c3210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5005.000000 -854.000000) translate(0,8)">12B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_36c3670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5063.000000 -854.000000) translate(0,8)">13B</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3812b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3854.000000 -1167.000000) translate(0,12)">Ux:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3813870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4764.000000 -1164.000000) translate(0,12)">Ux:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_383db10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4683.000000 -839.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_383db10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4683.000000 -839.000000) translate(0,27)">SZ11-90000/110GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_383db10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4683.000000 -839.000000) translate(0,42)">121±8×1.25%/35kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_383db10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4683.000000 -839.000000) translate(0,57)">90000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_383db10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4683.000000 -839.000000) translate(0,72)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_383db10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4683.000000 -839.000000) translate(0,87)">U%=12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3841cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3861.000000 -852.000000) translate(0,12)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3841cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3861.000000 -852.000000) translate(0,27)">SZ11-63000/110GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3841cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3861.000000 -852.000000) translate(0,42)">121±8×1.25%/35kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3841cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3861.000000 -852.000000) translate(0,57)">63000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3841cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3861.000000 -852.000000) translate(0,72)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3841cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3861.000000 -852.000000) translate(0,87)">U%=12</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3848be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3173.000000 -249.000000) translate(0,15)">4722、5842468</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_384bfb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3013.000000 -545.000000) translate(0,15)">全站检修停电前应挂“全站检修”牌，“禁止刷新”牌</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_384bfb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3013.000000 -545.000000) translate(0,33)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_384bfb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3013.000000 -545.000000) translate(0,51)">全站检修完工后仅可摘除“禁止刷新”牌</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_384bfb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3013.000000 -545.000000) translate(0,69)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_384bfb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3013.000000 -545.000000) translate(0,87)">全站检修复电后才可以摘除“全站检修”牌</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_38518c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3627.000000 -175.000000) translate(0,10)">（3、4、7、9</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_38518c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3627.000000 -175.000000) translate(0,22)"> 号发电机）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_3851b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4526.000000 -185.000000) translate(0,10)">（8、11、</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_3851b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4526.000000 -185.000000) translate(0,22)">17~23 号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_3851b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4526.000000 -185.000000) translate(0,34)">发电机）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_3851d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4620.000000 -186.000000) translate(0,10)">（1、2、5、6、</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_3851d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4620.000000 -186.000000) translate(0,22)">10、12~16 号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_3851d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4620.000000 -186.000000) translate(0,34)">发电机）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3866290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3307.000000 -1081.000000) translate(0,16)">AGC/AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3866290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3307.000000 -1081.000000) translate(0,36)">(大龙口Ⅰ回线)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3867780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4829.000000 -1171.000000) translate(0,15)">110kV大龙口Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3945d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3307.000000 -1020.000000) translate(0,16)">AGC/AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3945d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3307.000000 -1020.000000) translate(0,36)">(大龙口Ⅱ回线)</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_366e440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4058.000000 18.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_366f7a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4033.000000 33.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36709b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4313.000000 18.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3670c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4288.000000 33.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36c7860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4098.500000 745.000000) translate(0,12)">油温(℃)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36c88a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4098.500000 760.000000) translate(0,12)">档位(档)：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36c9330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4557.500000 745.000000) translate(0,12)">油温(℃)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36c95d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4557.500000 760.000000) translate(0,12)">档位(档)：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3805280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4929.000000 18.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3805f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4911.000000 65.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38067d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4925.000000 35.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3806a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4900.000000 50.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3806d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4827.000000 18.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3806ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4809.000000 65.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3807230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4823.000000 35.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3807470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4798.000000 50.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38077a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4719.000000 18.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3807a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4701.000000 65.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3807c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4715.000000 35.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3807e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4690.000000 50.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38081c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4616.000000 18.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3808430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4598.000000 65.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3808670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4612.000000 35.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38088b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4587.000000 50.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3808be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4511.000000 18.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3808e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4493.000000 65.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3809090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4507.000000 35.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38092d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4482.000000 50.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3809600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4171.000000 18.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3809870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4153.000000 65.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3809ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4167.000000 35.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3809cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4142.000000 50.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380a020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4421.000000 18.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380a290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4403.000000 65.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380a4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4417.000000 35.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380a710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4392.000000 50.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380aa40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3962.000000 18.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380acb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3944.000000 65.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380aef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3958.000000 35.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380b130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3933.000000 50.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380b460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3849.000000 18.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380b6d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3831.000000 65.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380b910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3845.000000 35.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380bb50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3820.000000 50.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380be80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3736.000000 18.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380c0f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3718.000000 65.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380c330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3732.000000 35.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380c570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3707.000000 50.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380c8a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3626.000000 18.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380cb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3608.000000 65.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380cd50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3622.000000 35.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380cf90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3597.000000 50.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380d2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4141.000000 611.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380d530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4123.000000 658.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380d770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4137.000000 628.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380d9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4112.000000 643.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380dce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4251.000000 652.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380df50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4233.000000 699.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380e190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4247.000000 669.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380e3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4222.000000 684.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380e700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4600.000000 611.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380e970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4582.000000 658.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380ebb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4596.000000 628.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380edf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4571.000000 643.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380f120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4141.000000 810.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380f390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4123.000000 857.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380f5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4137.000000 827.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380f810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4112.000000 842.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380fb40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3879.000000 1047.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380fdb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3903.500000 1032.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_380fff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3899.000000 1017.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3810ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3908.000000 1002.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3810df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3889.500000 1062.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3811120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4591.000000 1017.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3811390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4595.500000 1032.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38115d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4571.000000 1047.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3811810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4581.500000 1062.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3811a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4600.000000 1002.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3812160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4580.000000 857.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38124b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4594.000000 827.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38126f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4569.000000 842.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3812930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4600.000000 813.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3847c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5037.000000 18.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38480f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5024.000000 65.000000) translate(0,12)">P(kW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3848330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5033.000000 35.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3848570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5013.000000 50.000000) translate(0,12)">Q(kVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_384b480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3513.000000 19.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_384b8f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3500.000000 66.000000) translate(0,12)">P(kW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_384bb30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3509.000000 36.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_384bd70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3489.000000 51.000000) translate(0,12)">Q(kVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38530a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3623.000000 997.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3853820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3629.000000 981.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3853aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3613.000000 966.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3853fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3623.000000 1027.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3854200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3629.000000 950.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3854440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3623.000000 1012.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3854770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4852.000000 998.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38549f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4858.000000 982.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3854c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4842.000000 967.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3854e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4852.000000 1028.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38550b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4858.000000 951.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38552f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4852.000000 1013.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3855620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3620.000000 608.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38558a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3626.000000 592.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3855ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3610.000000 577.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3855d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3620.000000 638.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3855f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3626.000000 561.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38561a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3620.000000 623.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38564d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4867.000000 605.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3856750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4873.000000 589.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3856990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4857.000000 574.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3856bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4867.000000 635.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3856e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4873.000000 558.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3857050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4867.000000 620.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3033" y="-625"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3033" y="-1105"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3033" y="-1225"/>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_DLK.P1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3654.000000 -220.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43261" ObjectName="SM-CX_DLK.P1"/>
    <cge:TPSR_Ref TObjectID="43261"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_DLK.P2">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3763.000000 -221.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43262" ObjectName="SM-CX_DLK.P2"/>
    <cge:TPSR_Ref TObjectID="43262"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_DLK.P3">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3868.000000 -222.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43263" ObjectName="SM-CX_DLK.P3"/>
    <cge:TPSR_Ref TObjectID="43263"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_DLK.P4">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3980.000000 -224.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43264" ObjectName="SM-CX_DLK.P4"/>
    <cge:TPSR_Ref TObjectID="43264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_DLK.P5">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4540.000000 -238.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43265" ObjectName="SM-CX_DLK.P5"/>
    <cge:TPSR_Ref TObjectID="43265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_DLK.P6">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4644.000000 -232.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43266" ObjectName="SM-CX_DLK.P6"/>
    <cge:TPSR_Ref TObjectID="43266"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_DLK.P7">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4749.000000 -233.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43267" ObjectName="SM-CX_DLK.P7"/>
    <cge:TPSR_Ref TObjectID="43267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_DLK.P8">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4851.000000 -230.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43268" ObjectName="SM-CX_DLK.P8"/>
    <cge:TPSR_Ref TObjectID="43268"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_DLK.P9">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4955.000000 -236.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43269" ObjectName="SM-CX_DLK.P9"/>
    <cge:TPSR_Ref TObjectID="43269"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_307f3d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3770.389652 -980.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_307f700" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3773.389652 -1035.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3012e40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3773.389652 -1093.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38009f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3999.507647 -832.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36ce990" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4287.081309 -769.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36cf3c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4380.081309 -769.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fc4fc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4696.389652 -984.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f700a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4699.389652 -1039.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3057c70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4698.389652 -1100.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_303e330" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4100.983105 -947.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3015e40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4099.983105 -1017.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30d84e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3969.000000 -717.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_304dc20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3607.463511 -320.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3127e30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3716.463511 -320.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_331f020" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4463.507647 -832.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33252f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4434.000000 -719.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_332b1b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4439.983105 -945.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_332eb70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4438.983105 -1015.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3717e60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3820.463511 -320.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3718b10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3931.463511 -320.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_371b660" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3503.463511 -321.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_371c310" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4035.463511 -319.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_371cd60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4143.463511 -320.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_371da10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4037.463511 -202.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_371e6c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4141.463511 -202.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_371f110" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4282.463511 -320.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_371fb60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4387.463511 -319.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37205b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4388.463511 -201.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3721000" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4490.463511 -321.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3721a50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4595.463511 -319.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37224a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4701.463511 -320.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3723150" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4804.463511 -320.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3723ba0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4907.463511 -320.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37245f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5009.463511 -320.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3835c10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4282.463511 -202.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3841f20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4779.000000 -653.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38435a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3851.000000 -675.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectPoint_Layer"/><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3161" y="-1202"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3161" y="-1202"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3112" y="-1219"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3112" y="-1219"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="3301,-1084 3298,-1087 3298,-1033 3301,-1036 3301,-1084" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="3301,-1084 3298,-1087 3447,-1087 3444,-1084 3301,-1084" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(112,119,119)" points="3301,-1036 3298,-1033 3447,-1033 3444,-1036 3301,-1036" stroke="rgb(112,119,119)"/>
     <polygon fill="rgb(112,119,119)" points="3444,-1084 3447,-1087 3447,-1033 3444,-1036 3444,-1084" stroke="rgb(112,119,119)"/>
     <rect fill="rgb(224,238,238)" height="48" stroke="rgb(224,238,238)" width="143" x="3301" y="-1084"/>
     <rect fill="none" height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="3301" y="-1084"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="3301,-1022 3298,-1025 3298,-971 3301,-974 3301,-1022" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="3301,-1022 3298,-1025 3447,-1025 3444,-1022 3301,-1022" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(112,119,119)" points="3301,-974 3298,-971 3447,-971 3444,-974 3301,-974" stroke="rgb(112,119,119)"/>
     <polygon fill="rgb(112,119,119)" points="3444,-1022 3447,-1025 3447,-971 3444,-974 3444,-1022" stroke="rgb(112,119,119)"/>
     <rect fill="rgb(224,238,238)" height="48" stroke="rgb(224,238,238)" width="143" x="3301" y="-1022"/>
     <rect fill="none" height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="3301" y="-1022"/>
    </a>
   <metadata/></g>
  </g><g id="Circle_Layer">
   <ellipse DF8003:Layer="PUBLIC" cx="5061" cy="-253" fill="none" rx="17.5" ry="18" stroke="rgb(255,255,0)" stroke-width="1.88048"/>
   <circle DF8003:Layer="PUBLIC" cx="5061" cy="-224" fill="none" r="17.5" stroke="rgb(255,255,0)" stroke-width="1.88048"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-25173">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3823.389652 -926.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3914" ObjectName="SW-CX_DLK.CX_DLK_1222SW"/>
     <cge:Meas_Ref ObjectId="25173"/>
    <cge:TPSR_Ref TObjectID="3914"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25174">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3823.389652 -1040.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3915" ObjectName="SW-CX_DLK.CX_DLK_1226SW"/>
     <cge:Meas_Ref ObjectId="25174"/>
    <cge:TPSR_Ref TObjectID="3915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25175">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3799.610348 -971.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3916" ObjectName="SW-CX_DLK.CX_DLK_12227SW"/>
     <cge:Meas_Ref ObjectId="25175"/>
    <cge:TPSR_Ref TObjectID="3916"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25176">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3801.610348 -1026.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3917" ObjectName="SW-CX_DLK.CX_DLK_12260SW"/>
     <cge:Meas_Ref ObjectId="25176"/>
    <cge:TPSR_Ref TObjectID="3917"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25177">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3799.610348 -1084.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3918" ObjectName="SW-CX_DLK.CX_DLK_K1227SW"/>
     <cge:Meas_Ref ObjectId="25177"/>
    <cge:TPSR_Ref TObjectID="3918"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25200">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4153.983105 -961.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3941" ObjectName="SW-CX_DLK.CX_DLK_1902SW"/>
     <cge:Meas_Ref ObjectId="25200"/>
    <cge:TPSR_Ref TObjectID="3941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25194">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4052.507647 -629.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3935" ObjectName="SW-CX_DLK.CX_DLK_3026SW"/>
     <cge:Meas_Ref ObjectId="25194"/>
    <cge:TPSR_Ref TObjectID="3935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25195">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4052.507647 -532.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3936" ObjectName="SW-CX_DLK.CX_DLK_3022SW"/>
     <cge:Meas_Ref ObjectId="25195"/>
    <cge:TPSR_Ref TObjectID="3936"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25275">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3540.300126 -462.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4013" ObjectName="SW-CX_DLK.CX_DLK_3872SW"/>
     <cge:Meas_Ref ObjectId="25275"/>
    <cge:TPSR_Ref TObjectID="4013"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25276">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3540.300126 -345.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4014" ObjectName="SW-CX_DLK.CX_DLK_3876SW"/>
     <cge:Meas_Ref ObjectId="25276"/>
    <cge:TPSR_Ref TObjectID="4014"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25192">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4027.492353 -823.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3933" ObjectName="SW-CX_DLK.CX_DLK_10227SW"/>
     <cge:Meas_Ref ObjectId="25192"/>
    <cge:TPSR_Ref TObjectID="3933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25191">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4052.507647 -847.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3932" ObjectName="SW-CX_DLK.CX_DLK_1022SW"/>
     <cge:Meas_Ref ObjectId="25191"/>
    <cge:TPSR_Ref TObjectID="3932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25180">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4278.321014 -844.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3921" ObjectName="SW-CX_DLK.CX_DLK_1122SW"/>
     <cge:Meas_Ref ObjectId="25180"/>
    <cge:TPSR_Ref TObjectID="3921"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25179">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4371.321014 -844.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3920" ObjectName="SW-CX_DLK.CX_DLK_1121SW"/>
     <cge:Meas_Ref ObjectId="25179"/>
    <cge:TPSR_Ref TObjectID="3920"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25168">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4749.389652 -1045.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3909" ObjectName="SW-CX_DLK.CX_DLK_1216SW"/>
     <cge:Meas_Ref ObjectId="25168"/>
    <cge:TPSR_Ref TObjectID="3909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25169">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4726.610348 -975.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3910" ObjectName="SW-CX_DLK.CX_DLK_12117SW"/>
     <cge:Meas_Ref ObjectId="25169"/>
    <cge:TPSR_Ref TObjectID="3910"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25170">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4723.610348 -1030.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3911" ObjectName="SW-CX_DLK.CX_DLK_12160SW"/>
     <cge:Meas_Ref ObjectId="25170"/>
    <cge:TPSR_Ref TObjectID="3911"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25171">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4729.610348 -1098.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3912" ObjectName="SW-CX_DLK.CX_DLK_K1217SW"/>
     <cge:Meas_Ref ObjectId="25171"/>
    <cge:TPSR_Ref TObjectID="3912"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25167">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4749.389652 -929.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3908" ObjectName="SW-CX_DLK.CX_DLK_1211SW"/>
     <cge:Meas_Ref ObjectId="25167"/>
    <cge:TPSR_Ref TObjectID="3908"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25201">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4129.016895 -938.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3942" ObjectName="SW-CX_DLK.CX_DLK_19020SW"/>
     <cge:Meas_Ref ObjectId="25201"/>
    <cge:TPSR_Ref TObjectID="3942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25202">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4129.016895 -1008.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3943" ObjectName="SW-CX_DLK.CX_DLK_19027SW"/>
     <cge:Meas_Ref ObjectId="25202"/>
    <cge:TPSR_Ref TObjectID="3943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25196">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3960.000000 -700.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3937" ObjectName="SW-CX_DLK.CX_DLK_1020SW"/>
     <cge:Meas_Ref ObjectId="25196"/>
    <cge:TPSR_Ref TObjectID="3937"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25272">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3644.142497 -347.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4010" ObjectName="SW-CX_DLK.CX_DLK_3866SW"/>
     <cge:Meas_Ref ObjectId="25272"/>
    <cge:TPSR_Ref TObjectID="4010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25271">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3644.142497 -464.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4009" ObjectName="SW-CX_DLK.CX_DLK_3862SW"/>
     <cge:Meas_Ref ObjectId="25271"/>
    <cge:TPSR_Ref TObjectID="4009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25273">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3621.536489 -326.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4011" ObjectName="SW-CX_DLK.CX_DLK_38667SW"/>
     <cge:Meas_Ref ObjectId="25273"/>
    <cge:TPSR_Ref TObjectID="4011"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25253">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4074.996217 -466.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3991" ObjectName="SW-CX_DLK.CX_DLK_3822SW"/>
     <cge:Meas_Ref ObjectId="25253"/>
    <cge:TPSR_Ref TObjectID="3991"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25254">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4074.996217 -349.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3992" ObjectName="SW-CX_DLK.CX_DLK_3823SW"/>
     <cge:Meas_Ref ObjectId="25254"/>
    <cge:TPSR_Ref TObjectID="3992"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25268">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3753.142497 -345.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4006" ObjectName="SW-CX_DLK.CX_DLK_3856SW"/>
     <cge:Meas_Ref ObjectId="25268"/>
    <cge:TPSR_Ref TObjectID="4006"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25267">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3753.142497 -462.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4005" ObjectName="SW-CX_DLK.CX_DLK_3852SW"/>
     <cge:Meas_Ref ObjectId="25267"/>
    <cge:TPSR_Ref TObjectID="4005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25269">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3730.536489 -326.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4007" ObjectName="SW-CX_DLK.CX_DLK_38567SW"/>
     <cge:Meas_Ref ObjectId="25269"/>
    <cge:TPSR_Ref TObjectID="4007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25264">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3858.142497 -352.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4002" ObjectName="SW-CX_DLK.CX_DLK_3846SW"/>
     <cge:Meas_Ref ObjectId="25264"/>
    <cge:TPSR_Ref TObjectID="4002"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25263">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3858.142497 -469.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4001" ObjectName="SW-CX_DLK.CX_DLK_3842SW"/>
     <cge:Meas_Ref ObjectId="25263"/>
    <cge:TPSR_Ref TObjectID="4001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25265">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3834.536489 -326.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4003" ObjectName="SW-CX_DLK.CX_DLK_38467SW"/>
     <cge:Meas_Ref ObjectId="25265"/>
    <cge:TPSR_Ref TObjectID="4003"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25260">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3970.142497 -348.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3998" ObjectName="SW-CX_DLK.CX_DLK_3836SW"/>
     <cge:Meas_Ref ObjectId="25260"/>
    <cge:TPSR_Ref TObjectID="3998"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25259">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3970.142497 -465.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3997" ObjectName="SW-CX_DLK.CX_DLK_3832SW"/>
     <cge:Meas_Ref ObjectId="25259"/>
    <cge:TPSR_Ref TObjectID="3997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25261">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3945.536489 -326.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3999" ObjectName="SW-CX_DLK.CX_DLK_38367SW"/>
     <cge:Meas_Ref ObjectId="25261"/>
    <cge:TPSR_Ref TObjectID="3999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25255">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4050.536489 -326.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3993" ObjectName="SW-CX_DLK.CX_DLK_38237SW"/>
     <cge:Meas_Ref ObjectId="25255"/>
    <cge:TPSR_Ref TObjectID="3993"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25257">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4051.536489 -208.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3995" ObjectName="SW-CX_DLK.CX_DLK_38267SW"/>
     <cge:Meas_Ref ObjectId="25257"/>
    <cge:TPSR_Ref TObjectID="3995"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25247">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4178.996217 -464.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3985" ObjectName="SW-CX_DLK.CX_DLK_3812SW"/>
     <cge:Meas_Ref ObjectId="25247"/>
    <cge:TPSR_Ref TObjectID="3985"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25248">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4178.996217 -347.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3986" ObjectName="SW-CX_DLK.CX_DLK_3813SW"/>
     <cge:Meas_Ref ObjectId="25248"/>
    <cge:TPSR_Ref TObjectID="3986"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25251">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4156.136489 -208.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3989" ObjectName="SW-CX_DLK.CX_DLK_38167SW"/>
     <cge:Meas_Ref ObjectId="25251"/>
    <cge:TPSR_Ref TObjectID="3989"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25245">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4207.321014 -537.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3983" ObjectName="SW-CX_DLK.CX_DLK_3122SW"/>
     <cge:Meas_Ref ObjectId="25245"/>
    <cge:TPSR_Ref TObjectID="3983"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25244">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4296.321014 -539.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3982" ObjectName="SW-CX_DLK.CX_DLK_3121SW"/>
     <cge:Meas_Ref ObjectId="25244"/>
    <cge:TPSR_Ref TObjectID="3982"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25229">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4530.142497 -349.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3967" ObjectName="SW-CX_DLK.CX_DLK_3636SW"/>
     <cge:Meas_Ref ObjectId="25229"/>
    <cge:TPSR_Ref TObjectID="3967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25228">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4529.142497 -466.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3966" ObjectName="SW-CX_DLK.CX_DLK_3631SW"/>
     <cge:Meas_Ref ObjectId="25228"/>
    <cge:TPSR_Ref TObjectID="3966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25230">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4505.536489 -326.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3968" ObjectName="SW-CX_DLK.CX_DLK_36367SW"/>
     <cge:Meas_Ref ObjectId="25230"/>
    <cge:TPSR_Ref TObjectID="3968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25238">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4319.996217 -467.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3976" ObjectName="SW-CX_DLK.CX_DLK_3611SW"/>
     <cge:Meas_Ref ObjectId="25238"/>
    <cge:TPSR_Ref TObjectID="3976"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25239">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4319.996217 -350.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3977" ObjectName="SW-CX_DLK.CX_DLK_3613SW"/>
     <cge:Meas_Ref ObjectId="25239"/>
    <cge:TPSR_Ref TObjectID="3977"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25240">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4296.536489 -326.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3978" ObjectName="SW-CX_DLK.CX_DLK_36137SW"/>
     <cge:Meas_Ref ObjectId="25240"/>
    <cge:TPSR_Ref TObjectID="3978"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25242">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4296.536489 -208.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3980" ObjectName="SW-CX_DLK.CX_DLK_36167SW"/>
     <cge:Meas_Ref ObjectId="25242"/>
    <cge:TPSR_Ref TObjectID="3980"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25232">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4425.996217 -466.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3970" ObjectName="SW-CX_DLK.CX_DLK_3621SW"/>
     <cge:Meas_Ref ObjectId="25232"/>
    <cge:TPSR_Ref TObjectID="3970"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25233">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4425.996217 -349.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3971" ObjectName="SW-CX_DLK.CX_DLK_3623SW"/>
     <cge:Meas_Ref ObjectId="25233"/>
    <cge:TPSR_Ref TObjectID="3971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25234">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4402.536489 -326.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3972" ObjectName="SW-CX_DLK.CX_DLK_36237SW"/>
     <cge:Meas_Ref ObjectId="25234"/>
    <cge:TPSR_Ref TObjectID="3972"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25236">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4402.536489 -208.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3974" ObjectName="SW-CX_DLK.CX_DLK_36267SW"/>
     <cge:Meas_Ref ObjectId="25236"/>
    <cge:TPSR_Ref TObjectID="3974"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25224">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4634.142497 -467.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3962" ObjectName="SW-CX_DLK.CX_DLK_3641SW"/>
     <cge:Meas_Ref ObjectId="25224"/>
    <cge:TPSR_Ref TObjectID="3962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25226">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4609.536489 -326.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3964" ObjectName="SW-CX_DLK.CX_DLK_36467SW"/>
     <cge:Meas_Ref ObjectId="25226"/>
    <cge:TPSR_Ref TObjectID="3964"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25225">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4634.142497 -350.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3963" ObjectName="SW-CX_DLK.CX_DLK_3646SW"/>
     <cge:Meas_Ref ObjectId="25225"/>
    <cge:TPSR_Ref TObjectID="3963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25220">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4739.142497 -466.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3958" ObjectName="SW-CX_DLK.CX_DLK_3651SW"/>
     <cge:Meas_Ref ObjectId="25220"/>
    <cge:TPSR_Ref TObjectID="3958"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25222">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4715.536489 -326.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3960" ObjectName="SW-CX_DLK.CX_DLK_36567SW"/>
     <cge:Meas_Ref ObjectId="25222"/>
    <cge:TPSR_Ref TObjectID="3960"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25221">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4739.142497 -349.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3959" ObjectName="SW-CX_DLK.CX_DLK_3656SW"/>
     <cge:Meas_Ref ObjectId="25221"/>
    <cge:TPSR_Ref TObjectID="3959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25216">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4841.142497 -465.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3954" ObjectName="SW-CX_DLK.CX_DLK_3661SW"/>
     <cge:Meas_Ref ObjectId="25216"/>
    <cge:TPSR_Ref TObjectID="3954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25218">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4818.536489 -326.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3956" ObjectName="SW-CX_DLK.CX_DLK_36667SW"/>
     <cge:Meas_Ref ObjectId="25218"/>
    <cge:TPSR_Ref TObjectID="3956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25217">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4841.142497 -348.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3955" ObjectName="SW-CX_DLK.CX_DLK_3666SW"/>
     <cge:Meas_Ref ObjectId="25217"/>
    <cge:TPSR_Ref TObjectID="3955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25212">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4945.142497 -465.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3950" ObjectName="SW-CX_DLK.CX_DLK_3671SW"/>
     <cge:Meas_Ref ObjectId="25212"/>
    <cge:TPSR_Ref TObjectID="3950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25214">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4921.536489 -326.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3952" ObjectName="SW-CX_DLK.CX_DLK_36767SW"/>
     <cge:Meas_Ref ObjectId="25214"/>
    <cge:TPSR_Ref TObjectID="3952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25213">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4945.142497 -348.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3951" ObjectName="SW-CX_DLK.CX_DLK_3676SW"/>
     <cge:Meas_Ref ObjectId="25213"/>
    <cge:TPSR_Ref TObjectID="3951"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25209">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5046.300126 -349.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3947" ObjectName="SW-CX_DLK.CX_DLK_3686SW"/>
     <cge:Meas_Ref ObjectId="25209"/>
    <cge:TPSR_Ref TObjectID="3947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25208">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5046.300126 -466.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3946" ObjectName="SW-CX_DLK.CX_DLK_3681SW"/>
     <cge:Meas_Ref ObjectId="25208"/>
    <cge:TPSR_Ref TObjectID="3946"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25277">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3517.536489 -326.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4015" ObjectName="SW-CX_DLK.CX_DLK_38767SW"/>
     <cge:Meas_Ref ObjectId="25277"/>
    <cge:TPSR_Ref TObjectID="4015"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25249">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4157.536489 -326.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3987" ObjectName="SW-CX_DLK.CX_DLK_38137SW"/>
     <cge:Meas_Ref ObjectId="25249"/>
    <cge:TPSR_Ref TObjectID="3987"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26036">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4999.142497 -176.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4188" ObjectName="SW-CX_DLK.CX_DLK_3010SW"/>
     <cge:Meas_Ref ObjectId="26036"/>
    <cge:TPSR_Ref TObjectID="4188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25188">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4516.507647 -531.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3929" ObjectName="SW-CX_DLK.CX_DLK_3011SW"/>
     <cge:Meas_Ref ObjectId="25188"/>
    <cge:TPSR_Ref TObjectID="3929"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25185">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4491.492353 -823.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3926" ObjectName="SW-CX_DLK.CX_DLK_10117SW"/>
     <cge:Meas_Ref ObjectId="25185"/>
    <cge:TPSR_Ref TObjectID="3926"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25184">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4516.507647 -847.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3925" ObjectName="SW-CX_DLK.CX_DLK_1011SW"/>
     <cge:Meas_Ref ObjectId="25184"/>
    <cge:TPSR_Ref TObjectID="3925"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25189">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4425.000000 -703.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3930" ObjectName="SW-CX_DLK.CX_DLK_1010SW"/>
     <cge:Meas_Ref ObjectId="25189"/>
    <cge:TPSR_Ref TObjectID="3930"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25187">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4516.507647 -628.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3928" ObjectName="SW-CX_DLK.CX_DLK_3016SW"/>
     <cge:Meas_Ref ObjectId="25187"/>
    <cge:TPSR_Ref TObjectID="3928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25198">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4470.016895 -936.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3939" ObjectName="SW-CX_DLK.CX_DLK_19010SW"/>
     <cge:Meas_Ref ObjectId="25198"/>
    <cge:TPSR_Ref TObjectID="3939"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25199">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4468.016895 -1006.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3940" ObjectName="SW-CX_DLK.CX_DLK_19017SW"/>
     <cge:Meas_Ref ObjectId="25199"/>
    <cge:TPSR_Ref TObjectID="3940"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25318">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.983105 -538.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4018" ObjectName="SW-CX_DLK.CX_DLK_3901SW"/>
     <cge:Meas_Ref ObjectId="25318"/>
    <cge:TPSR_Ref TObjectID="4018"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25319">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3790.983105 -537.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4019" ObjectName="SW-CX_DLK.CX_DLK_3902SW"/>
     <cge:Meas_Ref ObjectId="25319"/>
    <cge:TPSR_Ref TObjectID="4019"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25256">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4074.996217 -227.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3994" ObjectName="SW-CX_DLK.CX_DLK_3826SW"/>
     <cge:Meas_Ref ObjectId="25256"/>
    <cge:TPSR_Ref TObjectID="3994"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25250">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4178.996217 -227.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3988" ObjectName="SW-CX_DLK.CX_DLK_3816SW"/>
     <cge:Meas_Ref ObjectId="25250"/>
    <cge:TPSR_Ref TObjectID="3988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25241">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4319.996217 -228.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3979" ObjectName="SW-CX_DLK.CX_DLK_3616SW"/>
     <cge:Meas_Ref ObjectId="25241"/>
    <cge:TPSR_Ref TObjectID="3979"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25235">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4425.996217 -228.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3973" ObjectName="SW-CX_DLK.CX_DLK_3626SW"/>
     <cge:Meas_Ref ObjectId="25235"/>
    <cge:TPSR_Ref TObjectID="3973"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25210">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5023.536489 -326.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3948" ObjectName="SW-CX_DLK.CX_DLK_36867SW"/>
     <cge:Meas_Ref ObjectId="25210"/>
    <cge:TPSR_Ref TObjectID="3948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25197">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4493.000000 -959.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3938" ObjectName="SW-CX_DLK.CX_DLK_1901SW"/>
     <cge:Meas_Ref ObjectId="25197"/>
    <cge:TPSR_Ref TObjectID="3938"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25182">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4278.321014 -779.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3923" ObjectName="SW-CX_DLK.CX_DLK_11227SW"/>
     <cge:Meas_Ref ObjectId="25182"/>
    <cge:TPSR_Ref TObjectID="3923"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-25181">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4371.321014 -779.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3922" ObjectName="SW-CX_DLK.CX_DLK_11217SW"/>
     <cge:Meas_Ref ObjectId="25181"/>
    <cge:TPSR_Ref TObjectID="3922"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.857143 -0.000000 0.000000 -0.804348 4873.857143 -779.326087)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_365fde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3555,-520 3555,-536 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4013@1" ObjectIDZND0="3906@0" Pin0InfoVect0LinkObjId="g_30e76d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25275_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3555,-520 3555,-536 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f725e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4068,-905 4068,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3932@1" ObjectIDZND0="3904@0" Pin0InfoVect0LinkObjId="g_3014eb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25191_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4068,-905 4068,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_30d0c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4068,-849 4068,-869 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="3931@x" ObjectIDND1="3933@x" ObjectIDZND0="3932@0" Pin0InfoVect0LinkObjId="SW-25191_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25190_0" Pin1InfoVect1LinkObjId="SW-25192_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4068,-849 4068,-869 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_30d0e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3838,-1011 3838,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="3913@0" ObjectIDZND0="3916@x" ObjectIDZND1="3914@x" Pin0InfoVect0LinkObjId="SW-25175_0" Pin0InfoVect1LinkObjId="SW-25173_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25172_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3838,-1011 3838,-997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_30d10e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3838,-997 3838,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="3913@x" ObjectIDND1="3916@x" ObjectIDZND0="3914@1" Pin0InfoVect0LinkObjId="SW-25173_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25172_0" Pin1InfoVect1LinkObjId="SW-25175_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3838,-997 3838,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_30d1340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3838,-1062 3838,-1052 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="3915@0" ObjectIDZND0="3913@x" ObjectIDZND1="3917@x" Pin0InfoVect0LinkObjId="SW-25172_0" Pin0InfoVect1LinkObjId="SW-25176_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25174_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3838,-1062 3838,-1052 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_310e2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3838,-1038 3838,-1052 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="3913@1" ObjectIDZND0="3917@x" ObjectIDZND1="3915@x" Pin0InfoVect0LinkObjId="SW-25176_0" Pin0InfoVect1LinkObjId="SW-25174_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25172_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3838,-1038 3838,-1052 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_310e510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4068,-703 4068,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="4017@1" ObjectIDZND0="3935@1" Pin0InfoVect0LinkObjId="SW-25194_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_304b260_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4068,-703 4068,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_30134c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4386,-902 4386,-926 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3920@1" ObjectIDZND0="3903@0" Pin0InfoVect0LinkObjId="g_303e0d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25179_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4386,-902 4386,-926 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36ce730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4386,-849 4386,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="3919@x" ObjectIDND1="3922@x" ObjectIDZND0="3920@0" Pin0InfoVect0LinkObjId="SW-25179_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25178_0" Pin1InfoVect1LinkObjId="SW-25181_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4386,-849 4386,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2fc4d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4718,-1001 4702,-1001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3910@0" ObjectIDZND0="g_2fc4fc0@0" Pin0InfoVect0LinkObjId="g_2fc4fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25169_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4718,-1001 4702,-1001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f70af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4705,-1056 4721,-1056 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2f700a0@0" ObjectIDZND0="3911@1" Pin0InfoVect0LinkObjId="SW-25170_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f700a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4705,-1056 4721,-1056 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3017bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-1015 4764,-1001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="3907@0" ObjectIDZND0="3910@x" ObjectIDZND1="3908@x" Pin0InfoVect0LinkObjId="SW-25169_0" Pin0InfoVect1LinkObjId="SW-25167_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25166_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-1015 4764,-1001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3017e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-1001 4764,-1001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="3910@1" ObjectIDZND0="3907@x" ObjectIDZND1="3908@x" Pin0InfoVect0LinkObjId="SW-25166_0" Pin0InfoVect1LinkObjId="SW-25167_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25169_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-1001 4764,-1001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3018070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-1001 4764,-987 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="3910@x" ObjectIDND1="3907@x" ObjectIDZND0="3908@1" Pin0InfoVect0LinkObjId="SW-25167_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25169_0" Pin1InfoVect1LinkObjId="SW-25166_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-1001 4764,-987 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_30182d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-1067 4764,-1056 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3909@0" ObjectIDZND0="3907@x" Pin0InfoVect0LinkObjId="SW-25166_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25168_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-1067 4764,-1056 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3018530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-1042 4764,-1056 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3907@1" ObjectIDZND0="3909@x" Pin0InfoVect0LinkObjId="SW-25168_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25166_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-1042 4764,-1056 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3018790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-1056 4757,-1056 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" ObjectIDND0="3907@x" ObjectIDND1="3909@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25166_0" Pin1InfoVect1LinkObjId="SW-25168_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-1056 4757,-1056 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_303e0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-951 4764,-926 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3908@0" ObjectIDZND0="3903@0" Pin0InfoVect0LinkObjId="g_30134c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25167_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-951 4764,-926 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3014eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4293,-902 4293,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3921@1" ObjectIDZND0="3904@0" Pin0InfoVect0LinkObjId="g_2f725e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25180_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4293,-902 4293,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3015980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4169,-983 4169,-964 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="3941@1" ObjectIDZND0="3904@0" ObjectIDZND1="3942@x" Pin0InfoVect0LinkObjId="g_2f725e0_0" Pin0InfoVect1LinkObjId="SW-25201_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25200_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4169,-983 4169,-964 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3015be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4169,-964 4169,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="3941@x" ObjectIDND1="3942@x" ObjectIDZND0="3904@0" Pin0InfoVect0LinkObjId="g_2f725e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25200_0" Pin1InfoVect1LinkObjId="SW-25201_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4169,-964 4169,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_304b000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4169,-1034 4169,-1019 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3329120@0" ObjectIDND1="g_367e4c0@0" ObjectIDND2="3943@x" ObjectIDZND0="3941@0" Pin0InfoVect0LinkObjId="SW-25200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3329120_0" Pin1InfoVect1LinkObjId="g_367e4c0_0" Pin1InfoVect2LinkObjId="SW-25202_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4169,-1034 4169,-1019 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_304b260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4068,-808 4068,-790 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="3931@0" ObjectIDZND0="4017@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4068,-808 4068,-790 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_30d8f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4068,-849 4068,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="3932@x" ObjectIDND1="3933@x" ObjectIDZND0="3931@1" Pin0InfoVect0LinkObjId="SW-25190_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25191_0" Pin1InfoVect1LinkObjId="SW-25192_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4068,-849 4068,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30d9f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3555,-463 3555,-484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4012@1" ObjectIDZND0="4013@0" Pin0InfoVect0LinkObjId="SW-25275_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25274_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3555,-463 3555,-484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f62b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4068,-590 4068,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3936@1" ObjectIDZND0="3934@0" Pin0InfoVect0LinkObjId="SW-25193_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25195_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4068,-590 4068,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fca3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3659,-465 3659,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4008@1" ObjectIDZND0="4009@0" Pin0InfoVect0LinkObjId="SW-25271_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25270_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3659,-465 3659,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fc2800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3659,-274 3681,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="lightningRod" ObjectIDND0="g_2fca600@0" ObjectIDND1="43261@x" ObjectIDZND0="g_2fc1a90@0" Pin0InfoVect0LinkObjId="g_2fc1a90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2fca600_0" Pin1InfoVect1LinkObjId="SM-CX_DLK.P1_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3659,-274 3681,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fc2a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3659,-292 3659,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="g_2fca600@0" ObjectIDZND0="g_2fc1a90@0" ObjectIDZND1="43261@x" Pin0InfoVect0LinkObjId="g_2fc1a90_0" Pin0InfoVect1LinkObjId="SM-CX_DLK.P1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fca600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3659,-292 3659,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fc2cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3659,-274 3659,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="generator" ObjectIDND0="g_2fc1a90@0" ObjectIDND1="g_2fca600@0" ObjectIDZND0="43261@0" Pin0InfoVect0LinkObjId="SM-CX_DLK.P1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2fc1a90_0" Pin1InfoVect1LinkObjId="g_2fca600_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3659,-274 3659,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fc2f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4090,-467 4090,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3990@1" ObjectIDZND0="3991@0" Pin0InfoVect0LinkObjId="SW-25253_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25252_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4090,-467 4090,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3046df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4090,-421 4111,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="3992@x" ObjectIDND1="3990@x" ObjectIDZND0="g_3047510@0" Pin0InfoVect0LinkObjId="g_3047510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25254_0" Pin1InfoVect1LinkObjId="SW-25252_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4090,-421 4111,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3047050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4090,-407 4090,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="3992@1" ObjectIDZND0="g_3047510@0" ObjectIDZND1="3990@x" Pin0InfoVect0LinkObjId="g_3047510_0" Pin0InfoVect1LinkObjId="SW-25252_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25254_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4090,-407 4090,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30472b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4090,-421 4090,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_3047510@0" ObjectIDND1="3992@x" ObjectIDZND0="3990@0" Pin0InfoVect0LinkObjId="SW-25252_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3047510_0" Pin1InfoVect1LinkObjId="SW-25254_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4090,-421 4090,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3124700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3768,-463 3768,-484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4004@1" ObjectIDZND0="4005@0" Pin0InfoVect0LinkObjId="SW-25267_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25266_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3768,-463 3768,-484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_311c590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3768,-417 3792,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="4006@x" ObjectIDND1="4004@x" ObjectIDZND0="g_309d960@0" Pin0InfoVect0LinkObjId="g_309d960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25268_0" Pin1InfoVect1LinkObjId="SW-25266_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3768,-417 3792,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_311c7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3768,-403 3768,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="4006@1" ObjectIDZND0="g_309d960@0" ObjectIDZND1="4004@x" Pin0InfoVect0LinkObjId="g_309d960_0" Pin0InfoVect1LinkObjId="SW-25266_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25268_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3768,-403 3768,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_311ca50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3768,-417 3768,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_309d960@0" ObjectIDND1="4006@x" ObjectIDZND0="4004@0" Pin0InfoVect0LinkObjId="SW-25266_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_309d960_0" Pin1InfoVect1LinkObjId="SW-25268_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3768,-417 3768,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_311da20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3768,-274 3790,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="lightningRod" ObjectIDND0="g_3124960@0" ObjectIDND1="43262@x" ObjectIDZND0="g_311ccb0@0" Pin0InfoVect0LinkObjId="g_311ccb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3124960_0" Pin1InfoVect1LinkObjId="SM-CX_DLK.P2_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3768,-274 3790,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_311dc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3768,-292 3768,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="g_3124960@0" ObjectIDZND0="g_311ccb0@0" ObjectIDZND1="43262@x" Pin0InfoVect0LinkObjId="g_311ccb0_0" Pin0InfoVect1LinkObjId="SM-CX_DLK.P2_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3124960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3768,-292 3768,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_309d730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3768,-274 3768,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="generator" ObjectIDND0="g_311ccb0@0" ObjectIDND1="g_3124960@0" ObjectIDZND0="43262@0" Pin0InfoVect0LinkObjId="SM-CX_DLK.P2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_311ccb0_0" Pin1InfoVect1LinkObjId="g_3124960_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3768,-274 3768,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3009f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3873,-470 3873,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4000@1" ObjectIDZND0="4001@0" Pin0InfoVect0LinkObjId="SW-25263_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25262_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3873,-470 3873,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3028000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3873,-424 3897,-424 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="4002@x" ObjectIDND1="4000@x" ObjectIDZND0="g_3029bb0@0" Pin0InfoVect0LinkObjId="g_3029bb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25264_0" Pin1InfoVect1LinkObjId="SW-25262_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3873,-424 3897,-424 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3028260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3873,-410 3873,-424 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="4002@1" ObjectIDZND0="g_3029bb0@0" ObjectIDZND1="4000@x" Pin0InfoVect0LinkObjId="g_3029bb0_0" Pin0InfoVect1LinkObjId="SW-25262_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25264_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3873,-410 3873,-424 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30284c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3873,-424 3873,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_3029bb0@0" ObjectIDND1="4002@x" ObjectIDZND0="4000@0" Pin0InfoVect0LinkObjId="SW-25262_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3029bb0_0" Pin1InfoVect1LinkObjId="SW-25264_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3873,-424 3873,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3029490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3873,-275 3895,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="lightningRod" ObjectIDND0="g_300a170@0" ObjectIDND1="43263@x" ObjectIDZND0="g_3028720@0" Pin0InfoVect0LinkObjId="g_3028720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_300a170_0" Pin1InfoVect1LinkObjId="SM-CX_DLK.P3_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3873,-275 3895,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30296f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3873,-293 3873,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="g_300a170@0" ObjectIDZND0="g_3028720@0" ObjectIDZND1="43263@x" Pin0InfoVect0LinkObjId="g_3028720_0" Pin0InfoVect1LinkObjId="SM-CX_DLK.P3_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_300a170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3873,-293 3873,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3029950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3873,-275 3873,-243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="generator" ObjectIDND0="g_3028720@0" ObjectIDND1="g_300a170@0" ObjectIDZND0="43263@0" Pin0InfoVect0LinkObjId="SM-CX_DLK.P3_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3028720_0" Pin1InfoVect1LinkObjId="g_300a170_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3873,-275 3873,-243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fe42e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3985,-466 3985,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3996@1" ObjectIDZND0="3997@0" Pin0InfoVect0LinkObjId="SW-25259_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25258_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3985,-466 3985,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_305d520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3985,-420 4009,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="3998@x" ObjectIDND1="3996@x" ObjectIDZND0="g_305f0d0@0" Pin0InfoVect0LinkObjId="g_305f0d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25260_0" Pin1InfoVect1LinkObjId="SW-25258_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3985,-420 4009,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_305d780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3985,-406 3985,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="3998@1" ObjectIDZND0="g_305f0d0@0" ObjectIDZND1="3996@x" Pin0InfoVect0LinkObjId="g_305f0d0_0" Pin0InfoVect1LinkObjId="SW-25258_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25260_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3985,-406 3985,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_305d9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3985,-420 3985,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_305f0d0@0" ObjectIDND1="3998@x" ObjectIDZND0="3996@0" Pin0InfoVect0LinkObjId="SW-25258_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_305f0d0_0" Pin1InfoVect1LinkObjId="SW-25260_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3985,-420 3985,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_305e9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3985,-277 4007,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="lightningRod" ObjectIDND0="g_2fe4540@0" ObjectIDND1="43264@x" ObjectIDZND0="g_305dc40@0" Pin0InfoVect0LinkObjId="g_305dc40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2fe4540_0" Pin1InfoVect1LinkObjId="SM-CX_DLK.P4_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3985,-277 4007,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_305ec10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3985,-295 3985,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="g_2fe4540@0" ObjectIDZND0="g_305dc40@0" ObjectIDZND1="43264@x" Pin0InfoVect0LinkObjId="g_305dc40_0" Pin0InfoVect1LinkObjId="SM-CX_DLK.P4_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fe4540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3985,-295 3985,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_305ee70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3985,-277 3985,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="generator" ObjectIDND0="g_305dc40@0" ObjectIDND1="g_2fe4540@0" ObjectIDZND0="43264@0" Pin0InfoVect0LinkObjId="SM-CX_DLK.P4_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_305dc40_0" Pin1InfoVect1LinkObjId="g_2fe4540_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3985,-277 3985,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3005a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4090,-296 4090,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2fc3180@0" ObjectIDZND0="3994@1" Pin0InfoVect0LinkObjId="SW-25256_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fc3180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4090,-296 4090,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3005ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4194,-465 4194,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3984@1" ObjectIDZND0="3985@0" Pin0InfoVect0LinkObjId="SW-25247_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25246_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4194,-465 4194,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f8bb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4194,-419 4215,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="3986@x" ObjectIDND1="3984@x" ObjectIDZND0="g_2f8c290@0" Pin0InfoVect0LinkObjId="g_2f8c290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25248_0" Pin1InfoVect1LinkObjId="SW-25246_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4194,-419 4215,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f8bdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4194,-405 4194,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="3986@1" ObjectIDZND0="g_2f8c290@0" ObjectIDZND1="3984@x" Pin0InfoVect0LinkObjId="g_2f8c290_0" Pin0InfoVect1LinkObjId="SW-25246_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25248_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4194,-405 4194,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f8c030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4194,-419 4194,-438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_2f8c290@0" ObjectIDND1="3986@x" ObjectIDZND0="3984@0" Pin0InfoVect0LinkObjId="SW-25246_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f8c290_0" Pin1InfoVect1LinkObjId="SW-25248_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4194,-419 4194,-438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30e7470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4310,-561 4310,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3982@0" ObjectIDZND0="3905@0" Pin0InfoVect0LinkObjId="g_371a5c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25244_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4310,-561 4310,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30e76d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4222,-558 4222,-536 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3983@0" ObjectIDZND0="3906@0" Pin0InfoVect0LinkObjId="g_365fde0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25245_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4222,-558 4222,-536 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30e7930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4194,-178 4194,-155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_370bfd0@1" ObjectIDZND0="g_370d250@0" Pin0InfoVect0LinkObjId="g_370d250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_370bfd0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4194,-178 4194,-155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30ea440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4544,-467 4544,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3965@1" ObjectIDZND0="3966@0" Pin0InfoVect0LinkObjId="SW-25228_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25227_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4544,-467 4544,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30edb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-421 4572,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="3967@x" ObjectIDND1="3965@x" ObjectIDZND0="g_2fcbce0@0" Pin0InfoVect0LinkObjId="g_2fcbce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25229_0" Pin1InfoVect1LinkObjId="SW-25227_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-421 4572,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30eddd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-407 4545,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="3967@1" ObjectIDZND0="g_2fcbce0@0" ObjectIDZND1="3965@x" Pin0InfoVect0LinkObjId="g_2fcbce0_0" Pin0InfoVect1LinkObjId="SW-25227_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25229_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-407 4545,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30ee030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-421 4545,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_2fcbce0@0" ObjectIDND1="3967@x" ObjectIDZND0="3965@0" Pin0InfoVect0LinkObjId="SW-25227_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2fcbce0_0" Pin1InfoVect1LinkObjId="SW-25229_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-421 4545,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30ef000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-278 4570,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="lightningRod" ObjectIDND0="g_30ea6a0@0" ObjectIDND1="43265@x" ObjectIDZND0="g_30ee290@0" Pin0InfoVect0LinkObjId="g_30ee290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_30ea6a0_0" Pin1InfoVect1LinkObjId="SM-CX_DLK.P5_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-278 4570,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30ef260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-296 4545,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="g_30ea6a0@0" ObjectIDZND0="g_30ee290@0" ObjectIDZND1="43265@x" Pin0InfoVect0LinkObjId="g_30ee290_0" Pin0InfoVect1LinkObjId="SM-CX_DLK.P5_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30ea6a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-296 4545,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30ef4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-278 4545,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="generator" ObjectIDND0="g_30ee290@0" ObjectIDND1="g_30ea6a0@0" ObjectIDZND0="43265@0" Pin0InfoVect0LinkObjId="SM-CX_DLK.P5_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_30ee290_0" Pin1InfoVect1LinkObjId="g_30ea6a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-278 4545,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fd3500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4335,-468 4335,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3975@1" ObjectIDZND0="3976@0" Pin0InfoVect0LinkObjId="SW-25238_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25237_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4335,-468 4335,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fd6c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4335,-422 4360,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="3977@x" ObjectIDND1="3975@x" ObjectIDZND0="g_2fd80c0@0" Pin0InfoVect0LinkObjId="g_2fd80c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25239_0" Pin1InfoVect1LinkObjId="SW-25237_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4335,-422 4360,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fd6e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4335,-408 4335,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="3977@1" ObjectIDZND0="g_2fd80c0@0" ObjectIDZND1="3975@x" Pin0InfoVect0LinkObjId="g_2fd80c0_0" Pin0InfoVect1LinkObjId="SW-25237_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25239_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4335,-408 4335,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fd70f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4335,-422 4335,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_2fd80c0@0" ObjectIDND1="3977@x" ObjectIDZND0="3975@0" Pin0InfoVect0LinkObjId="SW-25237_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2fd80c0_0" Pin1InfoVect1LinkObjId="SW-25239_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4335,-422 4335,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31afd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4335,-160 4348,-160 4358,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3708050@0" ObjectIDND1="g_2fd1420@0" ObjectIDZND0="g_2fd7350@0" Pin0InfoVect0LinkObjId="g_2fd7350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3708050_0" Pin1InfoVect1LinkObjId="g_2fd1420_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4335,-160 4348,-160 4358,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31b5500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4335,-297 4335,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2fd3760@0" ObjectIDZND0="3979@1" Pin0InfoVect0LinkObjId="SW-25241_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fd3760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4335,-297 4335,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31b5760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4441,-467 4441,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3969@1" ObjectIDZND0="3970@0" Pin0InfoVect0LinkObjId="SW-25232_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25231_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4441,-467 4441,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31b8e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4441,-421 4462,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="3971@x" ObjectIDND1="3969@x" ObjectIDZND0="g_31b95b0@0" Pin0InfoVect0LinkObjId="g_31b95b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25233_0" Pin1InfoVect1LinkObjId="SW-25231_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4441,-421 4462,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31b90f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4441,-407 4441,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="3971@1" ObjectIDZND0="g_31b95b0@0" ObjectIDZND1="3969@x" Pin0InfoVect0LinkObjId="g_31b95b0_0" Pin0InfoVect1LinkObjId="SW-25231_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25233_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4441,-407 4441,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31b9350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4441,-421 4441,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_31b95b0@0" ObjectIDND1="3971@x" ObjectIDZND0="3969@0" Pin0InfoVect0LinkObjId="SW-25231_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_31b95b0_0" Pin1InfoVect1LinkObjId="SW-25233_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4441,-421 4441,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37f3820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4649,-469 4649,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3961@1" ObjectIDZND0="3962@0" Pin0InfoVect0LinkObjId="SW-25224_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25223_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4649,-469 4649,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37f6f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4649,-422 4673,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="3963@x" ObjectIDND1="3961@x" ObjectIDZND0="g_37f8b00@0" Pin0InfoVect0LinkObjId="g_37f8b00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25225_0" Pin1InfoVect1LinkObjId="SW-25223_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4649,-422 4673,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37f71b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4649,-408 4649,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="3963@1" ObjectIDZND0="g_37f8b00@0" ObjectIDZND1="3961@x" Pin0InfoVect0LinkObjId="g_37f8b00_0" Pin0InfoVect1LinkObjId="SW-25223_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25225_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4649,-408 4649,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37f7410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4649,-422 4649,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_37f8b00@0" ObjectIDND1="3963@x" ObjectIDZND0="3961@0" Pin0InfoVect0LinkObjId="SW-25223_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_37f8b00_0" Pin1InfoVect1LinkObjId="SW-25225_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4649,-422 4649,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37f83e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4649,-279 4671,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="lightningRod" ObjectIDND0="g_37f3a80@0" ObjectIDND1="43266@x" ObjectIDZND0="g_37f7670@0" Pin0InfoVect0LinkObjId="g_37f7670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_37f3a80_0" Pin1InfoVect1LinkObjId="SM-CX_DLK.P6_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4649,-279 4671,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37f8640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4649,-297 4649,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="g_37f3a80@0" ObjectIDZND0="g_37f7670@0" ObjectIDZND1="43266@x" Pin0InfoVect0LinkObjId="g_37f7670_0" Pin0InfoVect1LinkObjId="SM-CX_DLK.P6_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37f3a80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4649,-297 4649,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37f88a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4649,-279 4649,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="generator" ObjectIDND0="g_37f7670@0" ObjectIDND1="g_37f3a80@0" ObjectIDZND0="43266@0" Pin0InfoVect0LinkObjId="SM-CX_DLK.P6_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_37f7670_0" Pin1InfoVect1LinkObjId="g_37f3a80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4649,-279 4649,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f78ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-467 4754,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3957@1" ObjectIDZND0="3958@0" Pin0InfoVect0LinkObjId="SW-25220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25219_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-467 4754,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f7c1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-421 4778,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="3959@x" ObjectIDND1="3957@x" ObjectIDZND0="g_2f7d670@0" Pin0InfoVect0LinkObjId="g_2f7d670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25221_0" Pin1InfoVect1LinkObjId="SW-25219_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-421 4778,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f7c440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-407 4754,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="3959@1" ObjectIDZND0="g_2f7d670@0" ObjectIDZND1="3957@x" Pin0InfoVect0LinkObjId="g_2f7d670_0" Pin0InfoVect1LinkObjId="SW-25219_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25221_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-407 4754,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f7c6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-421 4754,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_2f7d670@0" ObjectIDND1="3959@x" ObjectIDZND0="3957@0" Pin0InfoVect0LinkObjId="SW-25219_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f7d670_0" Pin1InfoVect1LinkObjId="SW-25221_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-421 4754,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f85790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4856,-466 4856,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3953@1" ObjectIDZND0="3954@0" Pin0InfoVect0LinkObjId="SW-25216_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25215_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4856,-466 4856,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f88ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4856,-420 4880,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="3955@x" ObjectIDND1="3953@x" ObjectIDZND0="g_30883a0@0" Pin0InfoVect0LinkObjId="g_30883a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25217_0" Pin1InfoVect1LinkObjId="SW-25215_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4856,-420 4880,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f89120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4856,-406 4856,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="3955@1" ObjectIDZND0="g_30883a0@0" ObjectIDZND1="3953@x" Pin0InfoVect0LinkObjId="g_30883a0_0" Pin0InfoVect1LinkObjId="SW-25215_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25217_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4856,-406 4856,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f89380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4856,-420 4856,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_30883a0@0" ObjectIDND1="3955@x" ObjectIDZND0="3953@0" Pin0InfoVect0LinkObjId="SW-25215_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_30883a0_0" Pin1InfoVect1LinkObjId="SW-25217_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4856,-420 4856,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f8a350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4856,-277 4878,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="lightningRod" ObjectIDND0="g_2f859f0@0" ObjectIDND1="43268@x" ObjectIDZND0="g_2f895e0@0" Pin0InfoVect0LinkObjId="g_2f895e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f859f0_0" Pin1InfoVect1LinkObjId="SM-CX_DLK.P8_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4856,-277 4878,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f8a5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4856,-295 4856,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="g_2f859f0@0" ObjectIDZND0="g_2f895e0@0" ObjectIDZND1="43268@x" Pin0InfoVect0LinkObjId="g_2f895e0_0" Pin0InfoVect1LinkObjId="SM-CX_DLK.P8_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f859f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4856,-295 4856,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3088140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4856,-277 4856,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="generator" ObjectIDND0="g_2f895e0@0" ObjectIDND1="g_2f859f0@0" ObjectIDZND0="43268@0" Pin0InfoVect0LinkObjId="SM-CX_DLK.P8_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f895e0_0" Pin1InfoVect1LinkObjId="g_2f859f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4856,-277 4856,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3090470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-466 4960,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3949@1" ObjectIDZND0="3950@0" Pin0InfoVect0LinkObjId="SW-25212_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25211_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-466 4960,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3093ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-420 4984,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="3951@x" ObjectIDND1="3949@x" ObjectIDZND0="g_3095750@0" Pin0InfoVect0LinkObjId="g_3095750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25213_0" Pin1InfoVect1LinkObjId="SW-25211_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-420 4984,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3093e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-406 4960,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="3951@1" ObjectIDZND0="g_3095750@0" ObjectIDZND1="3949@x" Pin0InfoVect0LinkObjId="g_3095750_0" Pin0InfoVect1LinkObjId="SW-25211_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25213_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-406 4960,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3094060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-420 4960,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_3095750@0" ObjectIDND1="3951@x" ObjectIDZND0="3949@0" Pin0InfoVect0LinkObjId="SW-25211_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3095750_0" Pin1InfoVect1LinkObjId="SW-25213_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-420 4960,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3095030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-277 4982,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="lightningRod" ObjectIDND0="g_30906d0@0" ObjectIDND1="43269@x" ObjectIDZND0="g_30942c0@0" Pin0InfoVect0LinkObjId="g_30942c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_30906d0_0" Pin1InfoVect1LinkObjId="SM-CX_DLK.P9_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-277 4982,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3095290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-295 4960,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="g_30906d0@0" ObjectIDZND0="g_30942c0@0" ObjectIDZND1="43269@x" Pin0InfoVect0LinkObjId="g_30942c0_0" Pin0InfoVect1LinkObjId="SM-CX_DLK.P9_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30906d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-295 4960,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30954f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-277 4960,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="generator" ObjectIDND0="g_30942c0@0" ObjectIDND1="g_30906d0@0" ObjectIDZND0="43269@0" Pin0InfoVect0LinkObjId="SM-CX_DLK.P9_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_30942c0_0" Pin1InfoVect1LinkObjId="g_30906d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-277 4960,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f9e4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5061,-467 5061,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3945@1" ObjectIDZND0="3946@0" Pin0InfoVect0LinkObjId="SW-25208_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25207_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5061,-467 5061,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fa7ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4068,-536 4068,-554 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3906@0" ObjectIDZND0="3936@0" Pin0InfoVect0LinkObjId="SW-25195_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_365fde0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4068,-536 4068,-554 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3315760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5014,-234 5014,-251 5061,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="4188@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26036_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5014,-234 5014,-251 5061,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33159c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5014,-188 5014,-198 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3311fb0@0" ObjectIDZND0="4188@0" Pin0InfoVect0LinkObjId="SW-26036_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3311fb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5014,-188 5014,-198 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3322320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4532,-905 4532,-926 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3925@1" ObjectIDZND0="3903@0" Pin0InfoVect0LinkObjId="g_30134c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25184_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4532,-905 4532,-926 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3322580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4532,-849 4532,-869 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="3924@x" ObjectIDND1="3926@x" ObjectIDZND0="3925@0" Pin0InfoVect0LinkObjId="SW-25184_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25183_0" Pin1InfoVect1LinkObjId="SW-25185_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4532,-849 4532,-869 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3325090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4440,-724 4440,-714 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3930@0" ObjectIDZND0="g_33252f0@0" Pin0InfoVect0LinkObjId="g_33252f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25189_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4440,-724 4440,-714 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3325d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4532,-849 4532,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="3925@x" ObjectIDND1="3926@x" ObjectIDZND0="3924@1" Pin0InfoVect0LinkObjId="SW-25183_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25184_0" Pin1InfoVect1LinkObjId="SW-25185_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4532,-849 4532,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_332a200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4170,-1057 4210,-1057 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="3941@x" ObjectIDND1="3943@x" ObjectIDND2="g_367e4c0@0" ObjectIDZND0="g_3329120@0" Pin0InfoVect0LinkObjId="g_3329120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-25200_0" Pin1InfoVect1LinkObjId="SW-25202_0" Pin1InfoVect2LinkObjId="g_367e4c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4170,-1057 4210,-1057 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_332acf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4169,-1075 4169,-1057 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_367e4c0@0" ObjectIDZND0="g_3329120@0" ObjectIDZND1="3941@x" ObjectIDZND2="3943@x" Pin0InfoVect0LinkObjId="g_3329120_0" Pin0InfoVect1LinkObjId="SW-25200_0" Pin0InfoVect2LinkObjId="SW-25202_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_367e4c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4169,-1075 4169,-1057 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_332af50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4169,-1057 4169,-1034 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3329120@0" ObjectIDND1="g_367e4c0@0" ObjectIDZND0="3941@x" ObjectIDZND1="3943@x" Pin0InfoVect0LinkObjId="SW-25200_0" Pin0InfoVect1LinkObjId="SW-25202_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3329120_0" Pin1InfoVect1LinkObjId="g_367e4c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4169,-1057 4169,-1034 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_332e6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4508,-981 4508,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="3938@0" ObjectIDZND0="3903@0" ObjectIDZND1="3939@x" Pin0InfoVect0LinkObjId="g_30134c0_0" Pin0InfoVect1LinkObjId="SW-25198_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25197_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4508,-981 4508,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_332e910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4508,-962 4508,-926 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="3938@x" ObjectIDND1="3939@x" ObjectIDZND0="3903@0" Pin0InfoVect0LinkObjId="g_30134c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25197_0" Pin1InfoVect1LinkObjId="SW-25198_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4508,-962 4508,-926 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3332070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4508,-1032 4508,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_33322d0@0" ObjectIDND1="g_3680550@0" ObjectIDND2="3940@x" ObjectIDZND0="3938@1" Pin0InfoVect0LinkObjId="SW-25197_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_33322d0_0" Pin1InfoVect1LinkObjId="g_3680550_0" Pin1InfoVect2LinkObjId="SW-25199_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4508,-1032 4508,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33333b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4509,-1055 4549,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="3938@x" ObjectIDND1="3940@x" ObjectIDND2="g_3680550@0" ObjectIDZND0="g_33322d0@0" Pin0InfoVect0LinkObjId="g_33322d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-25197_0" Pin1InfoVect1LinkObjId="SW-25199_0" Pin1InfoVect2LinkObjId="g_3680550_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4509,-1055 4549,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3333610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4508,-1073 4508,-1055 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3680550@0" ObjectIDZND0="g_33322d0@0" ObjectIDZND1="3938@x" ObjectIDZND2="3940@x" Pin0InfoVect0LinkObjId="g_33322d0_0" Pin0InfoVect1LinkObjId="SW-25197_0" Pin0InfoVect2LinkObjId="SW-25199_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3680550_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4508,-1073 4508,-1055 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3333870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4508,-1055 4508,-1032 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_33322d0@0" ObjectIDND1="g_3680550@0" ObjectIDZND0="3938@x" ObjectIDZND1="3940@x" Pin0InfoVect0LinkObjId="SW-25197_0" Pin0InfoVect1LinkObjId="SW-25199_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33322d0_0" Pin1InfoVect1LinkObjId="g_3680550_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4508,-1055 4508,-1032 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36d3130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4741,-674 4741,-657 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_3821170@0" ObjectIDZND0="g_36d3390@1" Pin0InfoVect0LinkObjId="g_36d3390_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3821170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4741,-674 4741,-657 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36d6540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3806,-680 3806,-663 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_381ec90@0" ObjectIDZND0="g_36d67a0@1" Pin0InfoVect0LinkObjId="g_36d67a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_381ec90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3806,-680 3806,-663 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36d70a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3806,-559 3806,-536 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4019@1" ObjectIDZND0="3906@0" Pin0InfoVect0LinkObjId="g_365fde0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25319_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3806,-559 3806,-536 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36d7300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3806,-619 3806,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_36d67a0@0" ObjectIDZND0="4019@x" ObjectIDZND1="g_3844290@0" Pin0InfoVect0LinkObjId="SW-25319_0" Pin0InfoVect1LinkObjId="g_3844290_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36d67a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3806,-619 3806,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36d7560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3806,-607 3806,-595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_36d67a0@0" ObjectIDND1="g_3844290@0" ObjectIDZND0="4019@0" Pin0InfoVect0LinkObjId="SW-25319_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_36d67a0_0" Pin1InfoVect1LinkObjId="g_3844290_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3806,-607 3806,-595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36db190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4293,-848 4293,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="3919@x" ObjectIDND1="3923@x" ObjectIDZND0="3921@0" Pin0InfoVect0LinkObjId="SW-25180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25178_0" Pin1InfoVect1LinkObjId="SW-25182_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4293,-848 4293,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36db380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4386,-848 4353,-848 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="3920@x" ObjectIDND1="3922@x" ObjectIDZND0="3919@0" Pin0InfoVect0LinkObjId="SW-25178_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25179_0" Pin1InfoVect1LinkObjId="SW-25181_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4386,-848 4353,-848 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36db570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4326,-848 4293,-848 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="3919@1" ObjectIDZND0="3921@x" ObjectIDZND1="3923@x" Pin0InfoVect0LinkObjId="SW-25180_0" Pin0InfoVect1LinkObjId="SW-25182_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25178_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4326,-848 4293,-848 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36e5030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4194,-285 4194,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3988@1" ObjectIDZND0="g_3005f40@0" Pin0InfoVect0LinkObjId="g_3005f40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25250_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4194,-285 4194,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36ee3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4441,-286 4441,-295 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3973@1" ObjectIDZND0="g_31b59c0@0" Pin0InfoVect0LinkObjId="g_31b59c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25235_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4441,-286 4441,-295 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36f56d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5061,-420 5085,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="3947@x" ObjectIDND1="3945@x" ObjectIDZND0="g_36f6680@0" Pin0InfoVect0LinkObjId="g_36f6680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25209_0" Pin1InfoVect1LinkObjId="SW-25207_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5061,-420 5085,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36f61c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5061,-407 5061,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="3947@1" ObjectIDZND0="g_36f6680@0" ObjectIDZND1="3945@x" Pin0InfoVect0LinkObjId="g_36f6680_0" Pin0InfoVect1LinkObjId="SW-25207_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25209_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5061,-407 5061,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36f6420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5061,-420 5061,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_36f6680@0" ObjectIDND1="3947@x" ObjectIDZND0="3945@0" Pin0InfoVect0LinkObjId="SW-25207_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_36f6680_0" Pin1InfoVect1LinkObjId="SW-25209_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5061,-420 5061,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37094f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4335,-145 4335,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_2fd1420@0" ObjectIDZND0="g_2fd7350@0" ObjectIDZND1="g_3708050@0" Pin0InfoVect0LinkObjId="g_2fd7350_0" Pin0InfoVect1LinkObjId="g_3708050_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fd1420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4335,-145 4335,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3709750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4335,-160 4335,-176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2fd7350@0" ObjectIDND1="g_2fd1420@0" ObjectIDZND0="g_3708050@0" Pin0InfoVect0LinkObjId="g_3708050_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2fd7350_0" Pin1InfoVect1LinkObjId="g_2fd1420_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4335,-160 4335,-176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37099b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4441,-160 4441,-180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3709c10@0" ObjectIDZND0="g_370afd0@1" Pin0InfoVect0LinkObjId="g_370afd0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3709c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4441,-160 4441,-180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_370de00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3999,-754 3999,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_304b4c0@0" ObjectIDZND0="g_31aa440@0" ObjectIDZND1="3937@x" Pin0InfoVect0LinkObjId="g_31aa440_0" Pin0InfoVect1LinkObjId="SW-25196_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_304b4c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3999,-754 3999,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_370dff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4019,-752 4019,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_31aa440@0" ObjectIDZND0="g_304b4c0@0" ObjectIDZND1="3937@x" Pin0InfoVect0LinkObjId="g_304b4c0_0" Pin0InfoVect1LinkObjId="SW-25196_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31aa440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4019,-752 4019,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_370e1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3999,-768 4019,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_304b4c0@0" ObjectIDND1="3937@x" ObjectIDZND0="g_31aa440@0" Pin0InfoVect0LinkObjId="g_31aa440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_304b4c0_0" Pin1InfoVect1LinkObjId="SW-25196_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3999,-768 4019,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_370fae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4464,-755 4464,-769 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_370e3d0@0" ObjectIDZND0="3930@x" ObjectIDZND1="g_370ee20@0" Pin0InfoVect0LinkObjId="SW-25189_0" Pin0InfoVect1LinkObjId="g_370ee20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_370e3d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4464,-755 4464,-769 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_370fd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4440,-761 4440,-769 4464,-769 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="3930@1" ObjectIDZND0="g_370e3d0@0" ObjectIDZND1="g_370ee20@0" Pin0InfoVect0LinkObjId="g_370e3d0_0" Pin0InfoVect1LinkObjId="g_370ee20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25189_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4440,-761 4440,-769 4464,-769 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_370ffa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4484,-753 4484,-769 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_370ee20@0" ObjectIDZND0="g_370e3d0@0" ObjectIDZND1="3930@x" Pin0InfoVect0LinkObjId="g_370e3d0_0" Pin0InfoVect1LinkObjId="SW-25189_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_370ee20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4484,-753 4484,-769 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3710200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4464,-769 4484,-769 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_370e3d0@0" ObjectIDND1="3930@x" ObjectIDZND0="g_370ee20@0" Pin0InfoVect0LinkObjId="g_370ee20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_370e3d0_0" Pin1InfoVect1LinkObjId="SW-25189_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4464,-769 4484,-769 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3710460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4484,-769 4533,-769 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="g_370ee20@0" ObjectIDND1="g_370e3d0@0" ObjectIDND2="3930@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_370ee20_0" Pin1InfoVect1LinkObjId="g_370e3d0_0" Pin1InfoVect2LinkObjId="SW-25189_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4484,-769 4533,-769 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37145c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4090,-158 4103,-158 4113,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3711770@0" ObjectIDND1="g_3707670@0" ObjectIDZND0="g_3713850@0" Pin0InfoVect0LinkObjId="g_3713850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3711770_0" Pin1InfoVect1LinkObjId="g_3707670_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4090,-158 4103,-158 4113,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3714820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4090,-143 4090,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_3711770@0" ObjectIDZND0="g_3713850@0" ObjectIDZND1="g_3707670@0" Pin0InfoVect0LinkObjId="g_3713850_0" Pin0InfoVect1LinkObjId="g_3707670_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3711770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4090,-143 4090,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3714a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4090,-158 4090,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_3713850@0" ObjectIDND1="g_3711770@0" ObjectIDZND0="g_3707670@0" Pin0InfoVect0LinkObjId="g_3707670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3713850_0" Pin1InfoVect1LinkObjId="g_3711770_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4090,-158 4090,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3715b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3659,-522 3659,-536 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4009@1" ObjectIDZND0="3906@0" Pin0InfoVect0LinkObjId="g_365fde0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25271_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3659,-522 3659,-536 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3715da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3768,-520 3768,-536 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4005@1" ObjectIDZND0="3906@0" Pin0InfoVect0LinkObjId="g_365fde0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25267_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3768,-520 3768,-536 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3716000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3873,-527 3873,-536 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4001@1" ObjectIDZND0="3906@0" Pin0InfoVect0LinkObjId="g_365fde0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25263_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3873,-527 3873,-536 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3716260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3985,-523 3985,-536 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3997@1" ObjectIDZND0="3906@0" Pin0InfoVect0LinkObjId="g_365fde0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25259_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3985,-523 3985,-536 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3717c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3722,-352 3722,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4007@0" ObjectIDZND0="g_3127e30@0" Pin0InfoVect0LinkObjId="g_3127e30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25269_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3722,-352 3722,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37188b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3826,-352 3826,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4003@0" ObjectIDZND0="g_3717e60@0" Pin0InfoVect0LinkObjId="g_3717e60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25265_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3826,-352 3826,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3719560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4090,-524 4090,-536 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3991@1" ObjectIDZND0="3906@0" Pin0InfoVect0LinkObjId="g_365fde0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25253_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4090,-524 4090,-536 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37197c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4194,-522 4194,-536 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3985@1" ObjectIDZND0="3906@0" Pin0InfoVect0LinkObjId="g_365fde0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25247_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4194,-522 4194,-536 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_371a5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4335,-525 4335,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3976@1" ObjectIDZND0="3905@0" Pin0InfoVect0LinkObjId="g_30e7470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25238_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4335,-525 4335,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_371a820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4441,-524 4441,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3970@1" ObjectIDZND0="3905@0" Pin0InfoVect0LinkObjId="g_30e7470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25232_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4441,-524 4441,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_371aa80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4544,-524 4544,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3966@1" ObjectIDZND0="3905@0" Pin0InfoVect0LinkObjId="g_30e7470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25228_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4544,-524 4544,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_371ace0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4649,-525 4649,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3962@1" ObjectIDZND0="3905@0" Pin0InfoVect0LinkObjId="g_30e7470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25224_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4649,-525 4649,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_371af40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-524 4754,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3958@1" ObjectIDZND0="3905@0" Pin0InfoVect0LinkObjId="g_30e7470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25220_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-524 4754,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_371b1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-523 4960,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3950@1" ObjectIDZND0="3905@0" Pin0InfoVect0LinkObjId="g_30e7470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25212_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-523 4960,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_371b400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5061,-524 5061,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3946@1" ObjectIDZND0="3905@0" Pin0InfoVect0LinkObjId="g_30e7470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25208_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5061,-524 5061,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_371c0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3509,-352 3509,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4015@0" ObjectIDZND0="g_371b660@0" Pin0InfoVect0LinkObjId="g_371b660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25277_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3509,-352 3509,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_371d7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4149,-352 4149,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3987@0" ObjectIDZND0="g_371cd60@0" Pin0InfoVect0LinkObjId="g_371cd60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25249_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4149,-352 4149,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_371e460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4043,-234 4043,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3995@0" ObjectIDZND0="g_371da10@0" Pin0InfoVect0LinkObjId="g_371da10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25257_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4043,-234 4043,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3722ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4707,-352 4707,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3960@0" ObjectIDZND0="g_37224a0@0" Pin0InfoVect0LinkObjId="g_37224a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25222_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4707,-352 4707,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3725040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5015,-352 5015,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3948@0" ObjectIDZND0="g_37245f0@0" Pin0InfoVect0LinkObjId="g_37245f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5015,-352 5015,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3664cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3555,-271 3555,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_37252a0@0" ObjectIDZND0="g_30da1a0@0" Pin0InfoVect0LinkObjId="g_30da1a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37252a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3555,-271 3555,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3664f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5061,-296 5061,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_2f9e6a0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f9e6a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5061,-296 5061,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3665300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3555,-152 3555,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_37252a0@1" Pin0InfoVect0LinkObjId="g_37252a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3555,-152 3555,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3668440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3779,-1052 3793,-1052 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_307f700@0" ObjectIDZND0="3917@0" Pin0InfoVect0LinkObjId="SW-25176_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_307f700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3779,-1052 3793,-1052 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3668630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3829,-1052 3838,-1052 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="3917@1" ObjectIDZND0="3913@x" ObjectIDZND1="3915@x" Pin0InfoVect0LinkObjId="SW-25172_0" Pin0InfoVect1LinkObjId="SW-25174_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25176_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3829,-1052 3838,-1052 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3668820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3779,-1110 3791,-1110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3012e40@0" ObjectIDZND0="3918@0" Pin0InfoVect0LinkObjId="SW-25177_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3012e40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3779,-1110 3791,-1110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3668a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3776,-997 3791,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_307f3d0@0" ObjectIDZND0="3916@0" Pin0InfoVect0LinkObjId="SW-25175_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_307f3d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3776,-997 3791,-997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3668c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3827,-997 3838,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="3916@1" ObjectIDZND0="3913@x" ObjectIDZND1="3914@x" Pin0InfoVect0LinkObjId="SW-25172_0" Pin0InfoVect1LinkObjId="SW-25173_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25175_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3827,-997 3838,-997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3668e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4168,-1034 4156,-1034 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="3941@x" ObjectIDND1="g_3329120@0" ObjectIDND2="g_367e4c0@0" ObjectIDZND0="3943@1" Pin0InfoVect0LinkObjId="SW-25202_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-25200_0" Pin1InfoVect1LinkObjId="g_3329120_0" Pin1InfoVect2LinkObjId="g_367e4c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4168,-1034 4156,-1034 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36690a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4120,-1034 4106,-1034 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3943@0" ObjectIDZND0="g_3015e40@0" Pin0InfoVect0LinkObjId="g_3015e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25202_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4120,-1034 4106,-1034 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36692d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4169,-964 4156,-964 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="3941@x" ObjectIDND1="3904@0" ObjectIDZND0="3942@1" Pin0InfoVect0LinkObjId="SW-25201_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25200_0" Pin1InfoVect1LinkObjId="g_2f725e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4169,-964 4156,-964 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3669500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4120,-964 4107,-964 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3942@0" ObjectIDZND0="g_303e330@0" Pin0InfoVect0LinkObjId="g_303e330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25201_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4120,-964 4107,-964 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3669730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4068,-849 4054,-849 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="3932@x" ObjectIDND1="3931@x" ObjectIDZND0="3933@1" Pin0InfoVect0LinkObjId="SW-25192_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25191_0" Pin1InfoVect1LinkObjId="SW-25190_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4068,-849 4054,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3669990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4018,-849 4006,-849 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3933@0" ObjectIDZND0="g_38009f0@0" Pin0InfoVect0LinkObjId="g_38009f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25192_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4018,-849 4006,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3669bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3975,-712 3975,-722 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_30d84e0@0" ObjectIDZND0="3937@0" Pin0InfoVect0LinkObjId="SW-25196_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30d84e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3975,-712 3975,-722 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3669e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3975,-758 3975,-768 3999,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="3937@1" ObjectIDZND0="g_304b4c0@0" ObjectIDZND1="g_31aa440@0" Pin0InfoVect0LinkObjId="g_304b4c0_0" Pin0InfoVect1LinkObjId="g_31aa440_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25196_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3975,-758 3975,-768 3999,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_366a0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4532,-849 4518,-849 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="3925@x" ObjectIDND1="3924@x" ObjectIDZND0="3926@1" Pin0InfoVect0LinkObjId="SW-25185_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25184_0" Pin1InfoVect1LinkObjId="SW-25183_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4532,-849 4518,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_366a310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4482,-849 4470,-849 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3926@0" ObjectIDZND0="g_331f020@0" Pin0InfoVect0LinkObjId="g_331f020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25185_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4482,-849 4470,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_366a570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4507,-1032 4495,-1032 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_33322d0@0" ObjectIDND1="g_3680550@0" ObjectIDND2="3938@x" ObjectIDZND0="3940@1" Pin0InfoVect0LinkObjId="SW-25199_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_33322d0_0" Pin1InfoVect1LinkObjId="g_3680550_0" Pin1InfoVect2LinkObjId="SW-25197_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4507,-1032 4495,-1032 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_366a7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4459,-1032 4445,-1032 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3940@0" ObjectIDZND0="g_332eb70@0" Pin0InfoVect0LinkObjId="g_332eb70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25199_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4459,-1032 4445,-1032 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_366aa30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4446,-962 4461,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_332b1b0@0" ObjectIDZND0="3939@0" Pin0InfoVect0LinkObjId="SW-25198_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_332b1b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4446,-962 4461,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_366ac90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4497,-962 4508,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="3939@1" ObjectIDZND0="3903@0" ObjectIDZND1="3938@x" Pin0InfoVect0LinkObjId="g_30134c0_0" Pin0InfoVect1LinkObjId="SW-25197_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25198_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4497,-962 4508,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_366aef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4496,-353 4496,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3968@0" ObjectIDZND0="g_3721000@0" Pin0InfoVect0LinkObjId="g_3721000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4496,-353 4496,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_366b150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4393,-351 4393,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3972@0" ObjectIDZND0="g_371fb60@0" Pin0InfoVect0LinkObjId="g_371fb60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25234_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4393,-351 4393,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_366b3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4041,-351 4041,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3993@0" ObjectIDZND0="g_371c310@0" Pin0InfoVect0LinkObjId="g_371c310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25255_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4041,-351 4041,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_366b610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3937,-352 3937,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3999@0" ObjectIDZND0="g_3718b10@0" Pin0InfoVect0LinkObjId="g_3718b10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25261_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3937,-352 3937,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_366d480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4741,-560 4741,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4018@0" ObjectIDZND0="3905@0" Pin0InfoVect0LinkObjId="g_30e7470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25318_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4741,-560 4741,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3672f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4532,-808 4532,-790 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="3924@0" ObjectIDZND0="4016@0" Pin0InfoVect0LinkObjId="g_3824cc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25183_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4532,-808 4532,-790 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_367af70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3838,-948 3838,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3914@0" ObjectIDZND0="3904@0" Pin0InfoVect0LinkObjId="g_2f725e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25173_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3838,-948 3838,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_367b160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4019,-768 4068,-768 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="g_31aa440@0" ObjectIDND1="g_304b4c0@0" ObjectIDND2="3937@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_31aa440_0" Pin1InfoVect1LinkObjId="g_304b4c0_0" Pin1InfoVect2LinkObjId="SW-25196_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4019,-768 4068,-768 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3688360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4386,-849 4386,-837 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="3920@x" ObjectIDND1="3919@x" ObjectIDZND0="3922@1" Pin0InfoVect0LinkObjId="SW-25181_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25179_0" Pin1InfoVect1LinkObjId="SW-25178_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4386,-849 4386,-837 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36885c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4386,-801 4386,-786 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3922@0" ObjectIDZND0="g_36cf3c0@0" Pin0InfoVect0LinkObjId="g_36cf3c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25181_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4386,-801 4386,-786 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3688820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4293,-786 4293,-801 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_36ce990@0" ObjectIDZND0="3923@0" Pin0InfoVect0LinkObjId="SW-25182_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36ce990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4293,-786 4293,-801 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3688a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4293,-837 4293,-848 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="3923@1" ObjectIDZND0="3921@x" ObjectIDZND1="3919@x" Pin0InfoVect0LinkObjId="SW-25180_0" Pin0InfoVect1LinkObjId="SW-25178_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25182_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4293,-837 4293,-848 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3689550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-278 4754,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="g_2f7c900@0" ObjectIDZND0="g_2f78d10@0" ObjectIDZND1="43267@x" Pin0InfoVect0LinkObjId="g_2f78d10_0" Pin0InfoVect1LinkObjId="SM-CX_DLK.P7_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f7c900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-278 4754,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3689ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-296 4754,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="g_2f78d10@0" ObjectIDZND0="g_2f7c900@0" ObjectIDZND1="43267@x" Pin0InfoVect0LinkObjId="g_2f7c900_0" Pin0InfoVect1LinkObjId="SM-CX_DLK.P7_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f78d10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-296 4754,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_368a0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-278 4754,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="generator" ObjectIDND0="g_2f7c900@0" ObjectIDND1="g_2f78d10@0" ObjectIDZND0="43267@0" Pin0InfoVect0LinkObjId="SM-CX_DLK.P7_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2f7c900_0" Pin1InfoVect1LinkObjId="g_2f78d10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-278 4754,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_368c770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4027,-643 4068,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_368b720@0" ObjectIDZND0="3934@x" ObjectIDZND1="3935@x" Pin0InfoVect0LinkObjId="SW-25193_0" Pin0InfoVect1LinkObjId="SW-25194_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_368b720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4027,-643 4068,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_368d260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4068,-634 4068,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3934@1" ObjectIDZND0="g_368b720@0" ObjectIDZND1="3935@x" Pin0InfoVect0LinkObjId="g_368b720_0" Pin0InfoVect1LinkObjId="SW-25194_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25193_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4068,-634 4068,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_368d4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4068,-643 4068,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_368b720@0" ObjectIDND1="3934@x" ObjectIDZND0="3935@0" Pin0InfoVect0LinkObjId="SW-25194_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_368b720_0" Pin1InfoVect1LinkObjId="SW-25193_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4068,-643 4068,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_368dfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4741,-596 4741,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="4018@1" ObjectIDZND0="g_36d3390@0" ObjectIDZND1="g_38427a0@0" Pin0InfoVect0LinkObjId="g_36d3390_0" Pin0InfoVect1LinkObjId="g_38427a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25318_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4741,-596 4741,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_368e210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4741,-604 4741,-613 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="4018@x" ObjectIDND1="g_38427a0@0" ObjectIDZND0="g_36d3390@0" Pin0InfoVect0LinkObjId="g_36d3390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25318_0" Pin1InfoVect1LinkObjId="g_38427a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4741,-604 4741,-613 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_368eaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4856,-523 4856,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3954@1" ObjectIDZND0="3905@0" Pin0InfoVect0LinkObjId="g_30e7470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25216_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4856,-523 4856,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3697ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5051,-352 5061,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3948@1" ObjectIDZND0="g_2f9e6a0@0" ObjectIDZND1="3947@x" Pin0InfoVect0LinkObjId="g_2f9e6a0_0" Pin0InfoVect1LinkObjId="SW-25209_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25210_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5051,-352 5061,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3698440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5061,-335 5061,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2f9e6a0@1" ObjectIDZND0="3948@x" ObjectIDZND1="3947@x" Pin0InfoVect0LinkObjId="SW-25210_0" Pin0InfoVect1LinkObjId="SW-25209_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f9e6a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5061,-335 5061,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3698630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5061,-352 5061,-371 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="3948@x" ObjectIDND1="g_2f9e6a0@0" ObjectIDZND0="3947@0" Pin0InfoVect0LinkObjId="SW-25209_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25210_0" Pin1InfoVect1LinkObjId="g_2f9e6a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5061,-352 5061,-371 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3698840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4913,-352 4913,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3952@0" ObjectIDZND0="g_3723ba0@0" Pin0InfoVect0LinkObjId="g_3723ba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25214_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4913,-352 4913,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3698a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4810,-352 4810,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3956@0" ObjectIDZND0="g_3723150@0" Pin0InfoVect0LinkObjId="g_3723150_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25218_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4810,-352 4810,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3698cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4601,-352 4601,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3964@0" ObjectIDZND0="g_3721a50@0" Pin0InfoVect0LinkObjId="g_3721a50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25226_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4601,-352 4601,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3698f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4288,-352 4288,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3978@0" ObjectIDZND0="g_371f110@0" Pin0InfoVect0LinkObjId="g_371f110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4288,-352 4288,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3699190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4185,-352 4194,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3987@1" ObjectIDZND0="3986@x" ObjectIDZND1="g_3005f40@0" Pin0InfoVect0LinkObjId="SW-25248_0" Pin0InfoVect1LinkObjId="g_3005f40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25249_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4185,-352 4194,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3699c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4194,-369 4194,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3986@0" ObjectIDZND0="g_3005f40@0" ObjectIDZND1="3987@x" Pin0InfoVect0LinkObjId="g_3005f40_0" Pin0InfoVect1LinkObjId="SW-25249_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25248_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4194,-369 4194,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3699ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4194,-352 4194,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="3986@x" ObjectIDND1="3987@x" ObjectIDZND0="g_3005f40@1" Pin0InfoVect0LinkObjId="g_3005f40_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25248_0" Pin1InfoVect1LinkObjId="SW-25249_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4194,-352 4194,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_369a100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4077,-352 4090,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3993@1" ObjectIDZND0="g_2fc3180@0" ObjectIDZND1="3992@x" Pin0InfoVect0LinkObjId="g_2fc3180_0" Pin0InfoVect1LinkObjId="SW-25254_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25255_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4077,-352 4090,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_369a360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3613,-352 3613,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4011@0" ObjectIDZND0="g_304dc20@0" Pin0InfoVect0LinkObjId="g_304dc20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25273_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3613,-352 3613,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_369b1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3579,-420 3555,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_369a5c0@0" ObjectIDZND0="4014@x" ObjectIDZND1="4012@x" Pin0InfoVect0LinkObjId="SW-25276_0" Pin0InfoVect1LinkObjId="SW-25274_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_369a5c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3579,-420 3555,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_369bc90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3555,-403 3555,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="4014@1" ObjectIDZND0="g_369a5c0@0" ObjectIDZND1="4012@x" Pin0InfoVect0LinkObjId="g_369a5c0_0" Pin0InfoVect1LinkObjId="SW-25274_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25276_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3555,-403 3555,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_369bef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3555,-420 3555,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_369a5c0@0" ObjectIDND1="4014@x" ObjectIDZND0="4012@0" Pin0InfoVect0LinkObjId="SW-25274_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_369a5c0_0" Pin1InfoVect1LinkObjId="SW-25276_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3555,-420 3555,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_369c150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3683,-419 3659,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_30d91d0@0" ObjectIDZND0="4010@x" ObjectIDZND1="4008@x" Pin0InfoVect0LinkObjId="SW-25272_0" Pin0InfoVect1LinkObjId="SW-25270_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30d91d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3683,-419 3659,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_369cc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3659,-405 3659,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="4010@1" ObjectIDZND0="g_30d91d0@0" ObjectIDZND1="4008@x" Pin0InfoVect0LinkObjId="g_30d91d0_0" Pin0InfoVect1LinkObjId="SW-25270_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25272_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3659,-405 3659,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_369cea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3659,-419 3659,-438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_30d91d0@0" ObjectIDND1="4010@x" ObjectIDZND0="4008@0" Pin0InfoVect0LinkObjId="SW-25270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_30d91d0_0" Pin1InfoVect1LinkObjId="SW-25272_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3659,-419 3659,-438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_369d5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5061,-207 5061,-147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5061,-207 5061,-147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36a44c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4853,-676 4853,-723 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4853,-676 4853,-723 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36a51a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4853,-769 4853,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_36a4720@1" Pin0InfoVect0LinkObjId="g_36a4720_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4853,-769 4853,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36a5400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4853,-773 4833,-773 4833,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4853,-773 4833,-773 4833,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36abcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5000,-768 5000,-774 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_36ab270@1" Pin0InfoVect0LinkObjId="g_36ab270_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5000,-768 5000,-774 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36abf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5000,-793 5000,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_36ab270@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36ab270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5000,-793 5000,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36ac1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5000,-772 4980,-772 4980,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5000,-772 4980,-772 4980,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36ac410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5000,-674 5000,-722 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5000,-674 5000,-722 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36b2d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5061,-768 5061,-774 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_36b2280@1" Pin0InfoVect0LinkObjId="g_36b2280_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5061,-768 5061,-774 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36b2f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5061,-793 5061,-799 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_36b2280@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36b2280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5061,-793 5061,-799 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36b31c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5061,-772 5041,-772 5041,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5061,-772 5041,-772 5041,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36b3420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5061,-674 5061,-722 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5061,-674 5061,-722 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36b9290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4977,-674 4977,-655 4951,-655 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4977,-674 4977,-655 4951,-655 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36b94f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4905,-655 4870,-655 4870,-676 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4905,-655 4870,-655 4870,-676 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36c40d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5093,-722 5093,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_36c38b0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36c38b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5093,-722 5093,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3811c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4222,-595 4222,-610 4255,-610 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3983@1" ObjectIDZND0="3981@1" Pin0InfoVect0LinkObjId="SW-25243_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25245_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4222,-595 4222,-610 4255,-610 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3811e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4282,-610 4311,-610 4311,-596 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3981@0" ObjectIDZND0="3982@1" Pin0InfoVect0LinkObjId="SW-25244_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25243_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4282,-610 4311,-610 4311,-596 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3815440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4853,-840 4886,-840 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDZND0="g_3815900@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_3815900_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4853,-840 4886,-840 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38156a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4886,-840 4926,-840 4926,-789 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_3815900@0" Pin0InfoVect0LinkObjId="g_3815900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4886,-840 4926,-840 4926,-789 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3818f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4886,-840 4886,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_3815900@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3815900_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4886,-840 4886,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3819160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4886,-797 4886,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_38193c0@0" Pin0InfoVect0LinkObjId="g_38193c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4886,-797 4886,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_381a2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4853,-825 4873,-825 4873,-820 4853,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="0@x" ObjectIDZND0="g_36a4720@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_36a4720_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4853,-825 4873,-825 4873,-820 4853,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_381a510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4853,-794 4853,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="transformer2" ObjectIDND0="g_36a4720@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36a4720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4853,-794 4853,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_381a770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4853,-808 4853,-817 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="0@x" ObjectIDND1="g_36a4720@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_36a4720_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4853,-808 4853,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_381b260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4853,-847 4853,-871 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4853,-847 4853,-871 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3824cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4532,-686 4532,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="3928@1" ObjectIDZND0="4016@1" Pin0InfoVect0LinkObjId="g_3672f60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25187_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4532,-686 4532,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3824eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4532,-589 4532,-606 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3929@1" ObjectIDZND0="3927@0" Pin0InfoVect0LinkObjId="SW-25186_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25188_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4532,-589 4532,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38250a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4532,-553 4532,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3929@0" ObjectIDZND0="3905@0" Pin0InfoVect0LinkObjId="g_30e7470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25188_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4532,-553 4532,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3825880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4490,-642 4532,-642 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_368a980@0" ObjectIDZND0="3927@x" ObjectIDZND1="3928@x" Pin0InfoVect0LinkObjId="SW-25186_0" Pin0InfoVect1LinkObjId="SW-25187_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_368a980_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4490,-642 4532,-642 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3826370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4532,-633 4532,-642 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3927@1" ObjectIDZND0="g_368a980@0" ObjectIDZND1="3928@x" Pin0InfoVect0LinkObjId="g_368a980_0" Pin0InfoVect1LinkObjId="SW-25187_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25186_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4532,-633 4532,-642 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38265d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4532,-642 4532,-650 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_368a980@0" ObjectIDND1="3927@x" ObjectIDZND0="3928@0" Pin0InfoVect0LinkObjId="SW-25187_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_368a980_0" Pin1InfoVect1LinkObjId="SW-25186_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4532,-642 4532,-650 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38270c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4090,-352 4090,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="3993@x" ObjectIDND1="3992@x" ObjectIDZND0="g_2fc3180@1" Pin0InfoVect0LinkObjId="g_2fc3180_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25255_0" Pin1InfoVect1LinkObjId="SW-25254_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4090,-352 4090,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3827320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4090,-371 4090,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3992@0" ObjectIDZND0="g_2fc3180@0" ObjectIDZND1="3993@x" Pin0InfoVect0LinkObjId="g_2fc3180_0" Pin0InfoVect1LinkObjId="SW-25255_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25254_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4090,-371 4090,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3827580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3985,-354 3985,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="3999@x" ObjectIDND1="g_2fe4540@0" ObjectIDZND0="3998@0" Pin0InfoVect0LinkObjId="SW-25260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25261_0" Pin1InfoVect1LinkObjId="g_2fe4540_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3985,-354 3985,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3828070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3973,-352 3985,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3999@1" ObjectIDZND0="3998@x" ObjectIDZND1="g_2fe4540@0" Pin0InfoVect0LinkObjId="SW-25260_0" Pin0InfoVect1LinkObjId="g_2fe4540_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25261_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3973,-352 3985,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38282d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3985,-352 3985,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="3998@x" ObjectIDND1="3999@x" ObjectIDZND0="g_2fe4540@1" Pin0InfoVect0LinkObjId="g_2fe4540_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25260_0" Pin1InfoVect1LinkObjId="SW-25261_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3985,-352 3985,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3828530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3545,-352 3555,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="4015@1" ObjectIDZND0="g_30da1a0@0" ObjectIDZND1="4014@x" Pin0InfoVect0LinkObjId="g_30da1a0_0" Pin0InfoVect1LinkObjId="SW-25276_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25277_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3545,-352 3555,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3829020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3555,-329 3555,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_30da1a0@1" ObjectIDZND0="4015@x" ObjectIDZND1="4014@x" Pin0InfoVect0LinkObjId="SW-25277_0" Pin0InfoVect1LinkObjId="SW-25276_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30da1a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3555,-329 3555,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3829280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3555,-352 3555,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="4015@x" ObjectIDND1="g_30da1a0@0" ObjectIDZND0="4014@0" Pin0InfoVect0LinkObjId="SW-25276_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25277_0" Pin1InfoVect1LinkObjId="g_30da1a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3555,-352 3555,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38294e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3649,-352 3659,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="4011@1" ObjectIDZND0="g_2fca600@0" ObjectIDZND1="4010@x" Pin0InfoVect0LinkObjId="g_2fca600_0" Pin0InfoVect1LinkObjId="SW-25272_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25273_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3649,-352 3659,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3829fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3659,-331 3659,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2fca600@1" ObjectIDZND0="4011@x" ObjectIDZND1="4010@x" Pin0InfoVect0LinkObjId="SW-25273_0" Pin0InfoVect1LinkObjId="SW-25272_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fca600_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3659,-331 3659,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_382a230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3659,-352 3659,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="4011@x" ObjectIDND1="g_2fca600@0" ObjectIDZND0="4010@0" Pin0InfoVect0LinkObjId="SW-25272_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25273_0" Pin1InfoVect1LinkObjId="g_2fca600_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3659,-352 3659,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_382a490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3758,-352 3768,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="4007@1" ObjectIDZND0="g_3124960@0" ObjectIDZND1="4006@x" Pin0InfoVect0LinkObjId="g_3124960_0" Pin0InfoVect1LinkObjId="SW-25268_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25269_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3758,-352 3768,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_382af80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3768,-334 3768,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3124960@1" ObjectIDZND0="4007@x" ObjectIDZND1="4006@x" Pin0InfoVect0LinkObjId="SW-25269_0" Pin0InfoVect1LinkObjId="SW-25268_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3124960_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3768,-334 3768,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_382b1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3768,-352 3768,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="4007@x" ObjectIDND1="g_3124960@0" ObjectIDZND0="4006@0" Pin0InfoVect0LinkObjId="SW-25268_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25269_0" Pin1InfoVect1LinkObjId="g_3124960_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3768,-352 3768,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_382b440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3862,-352 3873,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="4003@1" ObjectIDZND0="g_300a170@0" ObjectIDZND1="4002@x" Pin0InfoVect0LinkObjId="g_300a170_0" Pin0InfoVect1LinkObjId="SW-25264_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25265_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3862,-352 3873,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_382bf30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3873,-332 3873,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_300a170@1" ObjectIDZND0="4003@x" ObjectIDZND1="4002@x" Pin0InfoVect0LinkObjId="SW-25265_0" Pin0InfoVect1LinkObjId="SW-25264_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_300a170_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3873,-332 3873,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_382c190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3873,-352 3873,-374 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="4003@x" ObjectIDND1="g_300a170@0" ObjectIDZND0="4002@0" Pin0InfoVect0LinkObjId="SW-25264_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25265_0" Pin1InfoVect1LinkObjId="g_300a170_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3873,-352 3873,-374 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_382c3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4324,-352 4335,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3978@1" ObjectIDZND0="3977@x" ObjectIDZND1="g_2fd3760@0" Pin0InfoVect0LinkObjId="SW-25239_0" Pin0InfoVect1LinkObjId="g_2fd3760_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25240_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4324,-352 4335,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_382cee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4335,-372 4335,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3977@0" ObjectIDZND0="3978@x" ObjectIDZND1="g_2fd3760@0" Pin0InfoVect0LinkObjId="SW-25240_0" Pin0InfoVect1LinkObjId="g_2fd3760_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25239_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4335,-372 4335,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_382d140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4335,-352 4335,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="3978@x" ObjectIDND1="3977@x" ObjectIDZND0="g_2fd3760@1" Pin0InfoVect0LinkObjId="g_2fd3760_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25240_0" Pin1InfoVect1LinkObjId="SW-25239_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4335,-352 4335,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_382d3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4430,-352 4441,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3972@1" ObjectIDZND0="3971@x" ObjectIDZND1="g_31b59c0@0" Pin0InfoVect0LinkObjId="SW-25233_0" Pin0InfoVect1LinkObjId="g_31b59c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25234_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4430,-352 4441,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_382de90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4441,-371 4441,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3971@0" ObjectIDZND0="3972@x" ObjectIDZND1="g_31b59c0@0" Pin0InfoVect0LinkObjId="SW-25234_0" Pin0InfoVect1LinkObjId="g_31b59c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25233_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4441,-371 4441,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_382e0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4441,-352 4441,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="3972@x" ObjectIDND1="3971@x" ObjectIDZND0="g_31b59c0@1" Pin0InfoVect0LinkObjId="g_31b59c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25234_0" Pin1InfoVect1LinkObjId="SW-25233_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4441,-352 4441,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_382e350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4533,-352 4545,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3968@1" ObjectIDZND0="g_30ea6a0@0" ObjectIDZND1="3967@x" Pin0InfoVect0LinkObjId="g_30ea6a0_0" Pin0InfoVect1LinkObjId="SW-25229_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25230_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4533,-352 4545,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_382ee40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-335 4545,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_30ea6a0@1" ObjectIDZND0="3968@x" ObjectIDZND1="3967@x" Pin0InfoVect0LinkObjId="SW-25230_0" Pin0InfoVect1LinkObjId="SW-25229_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30ea6a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-335 4545,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_382f0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-352 4545,-371 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="3968@x" ObjectIDND1="g_30ea6a0@0" ObjectIDZND0="3967@0" Pin0InfoVect0LinkObjId="SW-25229_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25230_0" Pin1InfoVect1LinkObjId="g_30ea6a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-352 4545,-371 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_382fb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4637,-352 4649,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3964@1" ObjectIDZND0="g_37f3a80@0" ObjectIDZND1="3963@x" Pin0InfoVect0LinkObjId="g_37f3a80_0" Pin0InfoVect1LinkObjId="SW-25225_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25226_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4637,-352 4649,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3830680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4649,-336 4649,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_37f3a80@1" ObjectIDZND0="3964@x" ObjectIDZND1="3963@x" Pin0InfoVect0LinkObjId="SW-25226_0" Pin0InfoVect1LinkObjId="SW-25225_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37f3a80_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4649,-336 4649,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38308e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4649,-352 4649,-372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="3964@x" ObjectIDND1="g_37f3a80@0" ObjectIDZND0="3963@0" Pin0InfoVect0LinkObjId="SW-25225_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25226_0" Pin1InfoVect1LinkObjId="g_37f3a80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4649,-352 4649,-372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3830b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4743,-352 4754,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3960@1" ObjectIDZND0="g_2f78d10@0" ObjectIDZND1="3959@x" Pin0InfoVect0LinkObjId="g_2f78d10_0" Pin0InfoVect1LinkObjId="SW-25221_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25222_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4743,-352 4754,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3831630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-335 4754,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2f78d10@1" ObjectIDZND0="3960@x" ObjectIDZND1="3959@x" Pin0InfoVect0LinkObjId="SW-25222_0" Pin0InfoVect1LinkObjId="SW-25221_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f78d10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-335 4754,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3831890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4754,-352 4754,-371 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="3960@x" ObjectIDND1="g_2f78d10@0" ObjectIDZND0="3959@0" Pin0InfoVect0LinkObjId="SW-25221_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25222_0" Pin1InfoVect1LinkObjId="g_2f78d10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4754,-352 4754,-371 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3831af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4846,-352 4856,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3956@1" ObjectIDZND0="g_2f859f0@0" ObjectIDZND1="3955@x" Pin0InfoVect0LinkObjId="g_2f859f0_0" Pin0InfoVect1LinkObjId="SW-25217_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25218_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4846,-352 4856,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38325e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4856,-334 4856,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2f859f0@1" ObjectIDZND0="3956@x" ObjectIDZND1="3955@x" Pin0InfoVect0LinkObjId="SW-25218_0" Pin0InfoVect1LinkObjId="SW-25217_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f859f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4856,-334 4856,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3832840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4856,-352 4856,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="3956@x" ObjectIDND1="g_2f859f0@0" ObjectIDZND0="3955@0" Pin0InfoVect0LinkObjId="SW-25217_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25218_0" Pin1InfoVect1LinkObjId="g_2f859f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4856,-352 4856,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3832aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4949,-352 4960,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3952@1" ObjectIDZND0="g_30906d0@0" ObjectIDZND1="3951@x" Pin0InfoVect0LinkObjId="g_30906d0_0" Pin0InfoVect1LinkObjId="SW-25213_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25214_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4949,-352 4960,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3833590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-334 4960,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_30906d0@1" ObjectIDZND0="3952@x" ObjectIDZND1="3951@x" Pin0InfoVect0LinkObjId="SW-25214_0" Pin0InfoVect1LinkObjId="SW-25213_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30906d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-334 4960,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38337f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-352 4960,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="3952@x" ObjectIDND1="g_30906d0@0" ObjectIDZND0="3951@0" Pin0InfoVect0LinkObjId="SW-25213_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25214_0" Pin1InfoVect1LinkObjId="g_30906d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-352 4960,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3833a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4079,-234 4090,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3995@1" ObjectIDZND0="3994@x" ObjectIDZND1="g_3707670@0" Pin0InfoVect0LinkObjId="SW-25256_0" Pin0InfoVect1LinkObjId="g_3707670_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25257_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4079,-234 4090,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3834540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4090,-249 4090,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3994@0" ObjectIDZND0="g_3707670@0" ObjectIDZND1="3995@x" Pin0InfoVect0LinkObjId="g_3707670_0" Pin0InfoVect1LinkObjId="SW-25257_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25256_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4090,-249 4090,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38347a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4090,-234 4090,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="3994@x" ObjectIDND1="3995@x" ObjectIDZND0="g_3707670@1" Pin0InfoVect0LinkObjId="g_3707670_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25256_0" Pin1InfoVect1LinkObjId="SW-25257_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4090,-234 4090,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3834a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4183,-234 4194,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3989@1" ObjectIDZND0="3988@x" ObjectIDZND1="g_370bfd0@0" Pin0InfoVect0LinkObjId="SW-25250_0" Pin0InfoVect1LinkObjId="g_370bfd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25251_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4183,-234 4194,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38354f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4194,-249 4194,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3988@0" ObjectIDZND0="g_370bfd0@0" ObjectIDZND1="3989@x" Pin0InfoVect0LinkObjId="g_370bfd0_0" Pin0InfoVect1LinkObjId="SW-25251_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4194,-249 4194,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3835750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4194,-234 4194,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="3988@x" ObjectIDND1="3989@x" ObjectIDZND0="g_370bfd0@0" Pin0InfoVect0LinkObjId="g_370bfd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25250_0" Pin1InfoVect1LinkObjId="SW-25251_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4194,-234 4194,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38359b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4147,-219 4147,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_371e6c0@0" ObjectIDZND0="3989@0" Pin0InfoVect0LinkObjId="SW-25251_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_371e6c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4147,-219 4147,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3836660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4324,-234 4335,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3980@1" ObjectIDZND0="3979@x" ObjectIDZND1="g_3708050@0" Pin0InfoVect0LinkObjId="SW-25241_0" Pin0InfoVect1LinkObjId="g_3708050_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25242_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4324,-234 4335,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3837150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4335,-250 4335,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3979@0" ObjectIDZND0="g_3708050@0" ObjectIDZND1="3980@x" Pin0InfoVect0LinkObjId="g_3708050_0" Pin0InfoVect1LinkObjId="SW-25242_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25241_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4335,-250 4335,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38373b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4335,-234 4335,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="3979@x" ObjectIDND1="3980@x" ObjectIDZND0="g_3708050@1" Pin0InfoVect0LinkObjId="g_3708050_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25241_0" Pin1InfoVect1LinkObjId="SW-25242_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4335,-234 4335,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3837610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4288,-219 4288,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3835c10@0" ObjectIDZND0="3980@0" Pin0InfoVect0LinkObjId="SW-25242_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3835c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4288,-219 4288,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3837870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4430,-234 4441,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3974@1" ObjectIDZND0="3973@x" ObjectIDZND1="g_370afd0@0" Pin0InfoVect0LinkObjId="SW-25235_0" Pin0InfoVect1LinkObjId="g_370afd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25236_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4430,-234 4441,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3838360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4441,-250 4441,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3973@0" ObjectIDZND0="3974@x" ObjectIDZND1="g_370afd0@0" Pin0InfoVect0LinkObjId="SW-25236_0" Pin0InfoVect1LinkObjId="g_370afd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25235_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4441,-250 4441,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38385c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4441,-234 4441,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="3974@x" ObjectIDND1="3973@x" ObjectIDZND0="g_370afd0@0" Pin0InfoVect0LinkObjId="g_370afd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25236_0" Pin1InfoVect1LinkObjId="SW-25235_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4441,-234 4441,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3838820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4394,-218 4394,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_37205b0@0" ObjectIDZND0="3974@0" Pin0InfoVect0LinkObjId="SW-25236_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37205b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4394,-218 4394,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_383d020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4831,-1097 4815,-1097 4815,-1143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_383b5c0@0" ObjectIDZND0="3909@x" ObjectIDZND1="3912@x" ObjectIDZND2="g_383a4e0@0" Pin0InfoVect0LinkObjId="SW-25168_0" Pin0InfoVect1LinkObjId="SW-25171_0" Pin0InfoVect2LinkObjId="g_383a4e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_383b5c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4831,-1097 4815,-1097 4815,-1143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_38430e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4788,-658 4788,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_3841f20@0" ObjectIDZND0="g_38427a0@0" Pin0InfoVect0LinkObjId="g_38427a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3841f20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4788,-658 4788,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3843340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4788,-611 4788,-604 4741,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_38427a0@1" ObjectIDZND0="4018@x" ObjectIDZND1="g_36d3390@0" Pin0InfoVect0LinkObjId="SW-25318_0" Pin0InfoVect1LinkObjId="g_36d3390_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38427a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4788,-611 4788,-604 4741,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3844030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3860,-680 3860,-665 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_38435a0@0" ObjectIDZND0="g_3844290@0" Pin0InfoVect0LinkObjId="g_3844290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38435a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3860,-680 3860,-665 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3844c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3806,-607 3860,-607 3860,-633 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_36d67a0@0" ObjectIDND1="4019@x" ObjectIDZND0="g_3844290@1" Pin0InfoVect0LinkObjId="g_3844290_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_36d67a0_0" Pin1InfoVect1LinkObjId="SW-25319_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3806,-607 3860,-607 3860,-633 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3849b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3838,-1119 3838,-1110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3838a80@0" ObjectIDND1="g_367d200@0" ObjectIDND2="12973@1" ObjectIDZND0="3918@x" ObjectIDZND1="3915@x" Pin0InfoVect0LinkObjId="SW-25177_0" Pin0InfoVect1LinkObjId="SW-25174_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3838a80_0" Pin1InfoVect1LinkObjId="g_367d200_0" Pin1InfoVect2LinkObjId="g_3857420_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3838,-1119 3838,-1110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_384a480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3874,-1119 3838,-1119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3838a80@0" ObjectIDZND0="3918@x" ObjectIDZND1="3915@x" ObjectIDZND2="g_367d200@0" Pin0InfoVect0LinkObjId="SW-25177_0" Pin0InfoVect1LinkObjId="SW-25174_0" Pin0InfoVect2LinkObjId="g_367d200_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3838a80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3874,-1119 3838,-1119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_384aea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3827,-1110 3838,-1110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="3918@1" ObjectIDZND0="g_3838a80@0" ObjectIDZND1="g_367d200@0" ObjectIDZND2="12973@1" Pin0InfoVect0LinkObjId="g_3838a80_0" Pin0InfoVect1LinkObjId="g_367d200_0" Pin0InfoVect2LinkObjId="g_3857420_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25177_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3827,-1110 3838,-1110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_384b100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3838,-1110 3838,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_3838a80@0" ObjectIDND1="g_367d200@0" ObjectIDND2="12973@1" ObjectIDZND0="3915@1" Pin0InfoVect0LinkObjId="SW-25174_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3838a80_0" Pin1InfoVect1LinkObjId="g_367d200_0" Pin1InfoVect2LinkObjId="g_3857420_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3838,-1110 3838,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3852770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4765,-1103 4765,-1124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="3909@1" ObjectIDZND0="g_383b5c0@0" ObjectIDZND1="g_383a4e0@0" ObjectIDZND2="12956@1" Pin0InfoVect0LinkObjId="g_383b5c0_0" Pin0InfoVect1LinkObjId="g_383a4e0_0" Pin0InfoVect2LinkObjId="g_3858520_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25168_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4765,-1103 4765,-1124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3852960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4765,-1124 4765,-1143 4815,-1143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="3909@x" ObjectIDND1="3912@x" ObjectIDZND0="g_383b5c0@0" ObjectIDZND1="g_383a4e0@0" ObjectIDZND2="12956@1" Pin0InfoVect0LinkObjId="g_383b5c0_0" Pin0InfoVect1LinkObjId="g_383a4e0_0" Pin0InfoVect2LinkObjId="g_3858520_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-25168_0" Pin1InfoVect1LinkObjId="SW-25171_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4765,-1124 4765,-1143 4815,-1143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3852b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4765,-1124 4757,-1124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="3909@x" ObjectIDND1="g_383b5c0@0" ObjectIDND2="g_383a4e0@0" ObjectIDZND0="3912@1" Pin0InfoVect0LinkObjId="SW-25171_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-25168_0" Pin1InfoVect1LinkObjId="g_383b5c0_0" Pin1InfoVect2LinkObjId="g_383a4e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4765,-1124 4757,-1124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3852d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4721,-1124 4704,-1124 4704,-1117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3912@0" ObjectIDZND0="g_3057c70@0" Pin0InfoVect0LinkObjId="g_3057c70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-25171_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4721,-1124 4704,-1124 4704,-1117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3857420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3838,-1137 3838,-1156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_367d200@0" ObjectIDND1="3918@x" ObjectIDND2="3915@x" ObjectIDZND0="12973@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_367d200_0" Pin1InfoVect1LinkObjId="SW-25177_0" Pin1InfoVect2LinkObjId="SW-25174_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3838,-1137 3838,-1156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3857e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3879,-1137 3838,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_367d200@0" ObjectIDZND0="3918@x" ObjectIDZND1="3915@x" ObjectIDZND2="g_3838a80@0" Pin0InfoVect0LinkObjId="SW-25177_0" Pin0InfoVect1LinkObjId="SW-25174_0" Pin0InfoVect2LinkObjId="g_3838a80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_367d200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3879,-1137 3838,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3858030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3838,-1137 3838,-1119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_367d200@0" ObjectIDND1="12973@1" ObjectIDZND0="3918@x" ObjectIDZND1="3915@x" ObjectIDZND2="g_3838a80@0" Pin0InfoVect0LinkObjId="SW-25177_0" Pin0InfoVect1LinkObjId="SW-25174_0" Pin0InfoVect2LinkObjId="g_3838a80_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_367d200_0" Pin1InfoVect1LinkObjId="g_3857420_1" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3838,-1137 3838,-1119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3858520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4832,-1143 4869,-1143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_383a4e0@0" ObjectIDND1="g_383b5c0@0" ObjectIDND2="3909@x" ObjectIDZND0="12956@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_383a4e0_0" Pin1InfoVect1LinkObjId="g_383b5c0_0" Pin1InfoVect2LinkObjId="SW-25168_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4832,-1143 4869,-1143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3859010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4845,-1126 4832,-1126 4832,-1143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_383a4e0@0" ObjectIDZND0="g_383b5c0@0" ObjectIDZND1="3909@x" ObjectIDZND2="3912@x" Pin0InfoVect0LinkObjId="g_383b5c0_0" Pin0InfoVect1LinkObjId="SW-25168_0" Pin0InfoVect2LinkObjId="SW-25171_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_383a4e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4845,-1126 4832,-1126 4832,-1143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3859270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4832,-1143 4815,-1143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_383a4e0@0" ObjectIDND1="12956@1" ObjectIDZND0="g_383b5c0@0" ObjectIDZND1="3909@x" ObjectIDZND2="3912@x" Pin0InfoVect0LinkObjId="g_383b5c0_0" Pin0InfoVect1LinkObjId="SW-25168_0" Pin0InfoVect2LinkObjId="SW-25171_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_383a4e0_0" Pin1InfoVect1LinkObjId="g_3858520_1" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4832,-1143 4815,-1143 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_风电.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3161" y="-1202"/></g>
   <g href="cx_索引_接线图_地调直调_风电.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3112" y="-1219"/></g>
   <g href="AVC大龙口.svg" style="fill-opacity:0"><rect height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="3301" y="-1084"/></g>
   <g href="AVC大龙口2.svg" style="fill-opacity:0"><rect height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="3301" y="-1022"/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5104,-225 5061,-188 " stroke="rgb(255,255,0)" stroke-width="0.734118"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3598,-230 3555,-193 " stroke="rgb(255,255,0)" stroke-width="0.734118"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5000,-802 5031,-811 5031,-824 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5061,-802 5092,-811 5092,-824 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4190,-387 4167,-387 4167,-357 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4087,-387 4063,-387 4063,-357 " stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-25066" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3950.000000 -1062.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25066" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3913"/>
     <cge:Term_Ref ObjectID="5566"/>
    <cge:TPSR_Ref TObjectID="3913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-25067" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3950.000000 -1062.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25067" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3913"/>
     <cge:Term_Ref ObjectID="5566"/>
    <cge:TPSR_Ref TObjectID="3913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-25065" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3950.000000 -1062.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25065" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3913"/>
     <cge:Term_Ref ObjectID="5566"/>
    <cge:TPSR_Ref TObjectID="3913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-25069" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3950.000000 -1062.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25069" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3913"/>
     <cge:Term_Ref ObjectID="5566"/>
    <cge:TPSR_Ref TObjectID="3913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-25068" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3950.000000 -1062.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25068" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3913"/>
     <cge:Term_Ref ObjectID="5566"/>
    <cge:TPSR_Ref TObjectID="3913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-25120" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4359.000000 -33.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25120" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3975"/>
     <cge:Term_Ref ObjectID="5690"/>
    <cge:TPSR_Ref TObjectID="3975"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-25121" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4359.000000 -33.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25121" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3975"/>
     <cge:Term_Ref ObjectID="5690"/>
    <cge:TPSR_Ref TObjectID="3975"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-25159" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4179.000000 -760.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25159" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4017"/>
     <cge:Term_Ref ObjectID="0"/>
    <cge:TPSR_Ref TObjectID="4017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-25160" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4179.000000 -760.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25160" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4017"/>
     <cge:Term_Ref ObjectID="0"/>
    <cge:TPSR_Ref TObjectID="4017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-25157" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4639.000000 -760.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25157" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4016"/>
     <cge:Term_Ref ObjectID="0"/>
    <cge:TPSR_Ref TObjectID="4016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-25158" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4639.000000 -760.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25158" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4016"/>
     <cge:Term_Ref ObjectID="0"/>
    <cge:TPSR_Ref TObjectID="4016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-25132" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4099.000000 -32.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25132" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3990"/>
     <cge:Term_Ref ObjectID="5720"/>
    <cge:TPSR_Ref TObjectID="3990"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-25133" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4099.000000 -32.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25133" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3990"/>
     <cge:Term_Ref ObjectID="5720"/>
    <cge:TPSR_Ref TObjectID="3990"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-25123" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4288.500000 -697.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25123" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3981"/>
     <cge:Term_Ref ObjectID="5702"/>
    <cge:TPSR_Ref TObjectID="3981"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-25124" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4288.500000 -697.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25124" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3981"/>
     <cge:Term_Ref ObjectID="5702"/>
    <cge:TPSR_Ref TObjectID="3981"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-25125" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4288.500000 -697.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25125" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3981"/>
     <cge:Term_Ref ObjectID="5702"/>
    <cge:TPSR_Ref TObjectID="3981"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-25126" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4288.500000 -697.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25126" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3981"/>
     <cge:Term_Ref ObjectID="5702"/>
    <cge:TPSR_Ref TObjectID="3981"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-25060" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4639.000000 -1062.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25060" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3907"/>
     <cge:Term_Ref ObjectID="5554"/>
    <cge:TPSR_Ref TObjectID="3907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-25061" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4639.000000 -1062.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25061" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3907"/>
     <cge:Term_Ref ObjectID="5554"/>
    <cge:TPSR_Ref TObjectID="3907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-25059" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4639.000000 -1062.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25059" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3907"/>
     <cge:Term_Ref ObjectID="5554"/>
    <cge:TPSR_Ref TObjectID="3907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-25063" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4639.000000 -1062.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25063" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3907"/>
     <cge:Term_Ref ObjectID="5554"/>
    <cge:TPSR_Ref TObjectID="3907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-25062" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4639.000000 -1062.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25062" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3907"/>
     <cge:Term_Ref ObjectID="5554"/>
    <cge:TPSR_Ref TObjectID="3907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-25039" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4906.000000 -1023.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25039" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3903"/>
     <cge:Term_Ref ObjectID="5550"/>
    <cge:TPSR_Ref TObjectID="3903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-25040" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4906.000000 -1023.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25040" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3903"/>
     <cge:Term_Ref ObjectID="5550"/>
    <cge:TPSR_Ref TObjectID="3903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-25312" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4906.000000 -1023.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25312" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3903"/>
     <cge:Term_Ref ObjectID="5550"/>
    <cge:TPSR_Ref TObjectID="3903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-57277" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4906.000000 -1023.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57277" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3903"/>
     <cge:Term_Ref ObjectID="5550"/>
    <cge:TPSR_Ref TObjectID="3903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-25041" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4906.000000 -1023.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25041" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3903"/>
     <cge:Term_Ref ObjectID="5550"/>
    <cge:TPSR_Ref TObjectID="3903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-57278" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4906.000000 -1023.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57278" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3903"/>
     <cge:Term_Ref ObjectID="5550"/>
    <cge:TPSR_Ref TObjectID="3903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-25044" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3681.000000 -1024.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25044" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3904"/>
     <cge:Term_Ref ObjectID="5551"/>
    <cge:TPSR_Ref TObjectID="3904"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-25045" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3681.000000 -1024.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25045" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3904"/>
     <cge:Term_Ref ObjectID="5551"/>
    <cge:TPSR_Ref TObjectID="3904"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-25313" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3681.000000 -1024.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25313" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3904"/>
     <cge:Term_Ref ObjectID="5551"/>
    <cge:TPSR_Ref TObjectID="3904"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-57279" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3681.000000 -1024.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57279" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3904"/>
     <cge:Term_Ref ObjectID="5551"/>
    <cge:TPSR_Ref TObjectID="3904"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-25046" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3681.000000 -1024.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25046" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3904"/>
     <cge:Term_Ref ObjectID="5551"/>
    <cge:TPSR_Ref TObjectID="3904"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-57280" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3681.000000 -1024.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57280" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3904"/>
     <cge:Term_Ref ObjectID="5551"/>
    <cge:TPSR_Ref TObjectID="3904"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-25054" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3675.000000 -631.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25054" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3906"/>
     <cge:Term_Ref ObjectID="5553"/>
    <cge:TPSR_Ref TObjectID="3906"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-25055" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3675.000000 -631.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25055" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3906"/>
     <cge:Term_Ref ObjectID="5553"/>
    <cge:TPSR_Ref TObjectID="3906"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-25315" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3675.000000 -631.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25315" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3906"/>
     <cge:Term_Ref ObjectID="5553"/>
    <cge:TPSR_Ref TObjectID="3906"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-57284" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3675.000000 -631.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57284" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3906"/>
     <cge:Term_Ref ObjectID="5553"/>
    <cge:TPSR_Ref TObjectID="3906"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-25056" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3675.000000 -631.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25056" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3906"/>
     <cge:Term_Ref ObjectID="5553"/>
    <cge:TPSR_Ref TObjectID="3906"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-57283" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3675.000000 -631.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57283" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3906"/>
     <cge:Term_Ref ObjectID="5553"/>
    <cge:TPSR_Ref TObjectID="3906"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-25049" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4922.000000 -631.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25049" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3905"/>
     <cge:Term_Ref ObjectID="5552"/>
    <cge:TPSR_Ref TObjectID="3905"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-25050" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4922.000000 -631.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25050" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3905"/>
     <cge:Term_Ref ObjectID="5552"/>
    <cge:TPSR_Ref TObjectID="3905"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-25314" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4922.000000 -631.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25314" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3905"/>
     <cge:Term_Ref ObjectID="5552"/>
    <cge:TPSR_Ref TObjectID="3905"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-57282" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4922.000000 -631.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57282" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3905"/>
     <cge:Term_Ref ObjectID="5552"/>
    <cge:TPSR_Ref TObjectID="3905"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-25051" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4922.000000 -631.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25051" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3905"/>
     <cge:Term_Ref ObjectID="5552"/>
    <cge:TPSR_Ref TObjectID="3905"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-57281" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4922.000000 -631.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="57281" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3905"/>
     <cge:Term_Ref ObjectID="5552"/>
    <cge:TPSR_Ref TObjectID="3905"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-25079" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4179.000000 -856.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25079" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3931"/>
     <cge:Term_Ref ObjectID="5602"/>
    <cge:TPSR_Ref TObjectID="3931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-25080" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4179.000000 -856.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25080" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3931"/>
     <cge:Term_Ref ObjectID="5602"/>
    <cge:TPSR_Ref TObjectID="3931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-25081" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4179.000000 -856.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25081" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3931"/>
     <cge:Term_Ref ObjectID="5602"/>
    <cge:TPSR_Ref TObjectID="3931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-25082" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4179.000000 -856.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25082" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3931"/>
     <cge:Term_Ref ObjectID="5602"/>
    <cge:TPSR_Ref TObjectID="3931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-25083" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4179.000000 -657.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25083" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3934"/>
     <cge:Term_Ref ObjectID="5608"/>
    <cge:TPSR_Ref TObjectID="3934"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-25084" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4179.000000 -657.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25084" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3934"/>
     <cge:Term_Ref ObjectID="5608"/>
    <cge:TPSR_Ref TObjectID="3934"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-25085" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4179.000000 -657.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25085" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3934"/>
     <cge:Term_Ref ObjectID="5608"/>
    <cge:TPSR_Ref TObjectID="3934"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-25086" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4179.000000 -657.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25086" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3934"/>
     <cge:Term_Ref ObjectID="5608"/>
    <cge:TPSR_Ref TObjectID="3934"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-25071" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4639.000000 -856.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25071" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3924"/>
     <cge:Term_Ref ObjectID="5588"/>
    <cge:TPSR_Ref TObjectID="3924"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-25072" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4639.000000 -856.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25072" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3924"/>
     <cge:Term_Ref ObjectID="5588"/>
    <cge:TPSR_Ref TObjectID="3924"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-25073" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4639.000000 -856.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25073" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3924"/>
     <cge:Term_Ref ObjectID="5588"/>
    <cge:TPSR_Ref TObjectID="3924"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-25074" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4639.000000 -856.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25074" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3924"/>
     <cge:Term_Ref ObjectID="5588"/>
    <cge:TPSR_Ref TObjectID="3924"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-25075" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4639.000000 -657.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25075" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3927"/>
     <cge:Term_Ref ObjectID="5594"/>
    <cge:TPSR_Ref TObjectID="3927"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-25076" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4639.000000 -657.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25076" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3927"/>
     <cge:Term_Ref ObjectID="5594"/>
    <cge:TPSR_Ref TObjectID="3927"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-25077" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4639.000000 -657.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25077" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3927"/>
     <cge:Term_Ref ObjectID="5594"/>
    <cge:TPSR_Ref TObjectID="3927"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-25078" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4639.000000 -657.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25078" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3927"/>
     <cge:Term_Ref ObjectID="5594"/>
    <cge:TPSR_Ref TObjectID="3927"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-25316" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3557.000000 -63.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25316" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4012"/>
     <cge:Term_Ref ObjectID="5764"/>
    <cge:TPSR_Ref TObjectID="4012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-25317" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3557.000000 -63.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25317" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4012"/>
     <cge:Term_Ref ObjectID="5764"/>
    <cge:TPSR_Ref TObjectID="4012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-25151" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3557.000000 -63.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25151" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4012"/>
     <cge:Term_Ref ObjectID="5764"/>
    <cge:TPSR_Ref TObjectID="4012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-25152" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3557.000000 -63.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25152" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4012"/>
     <cge:Term_Ref ObjectID="5764"/>
    <cge:TPSR_Ref TObjectID="4012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-25147" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3663.000000 -64.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25147" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4008"/>
     <cge:Term_Ref ObjectID="5756"/>
    <cge:TPSR_Ref TObjectID="4008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-25148" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3663.000000 -64.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25148" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4008"/>
     <cge:Term_Ref ObjectID="5756"/>
    <cge:TPSR_Ref TObjectID="4008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-25149" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3663.000000 -64.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25149" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4008"/>
     <cge:Term_Ref ObjectID="5756"/>
    <cge:TPSR_Ref TObjectID="4008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="1" id="ME-25150" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3663.000000 -64.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25150" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4008"/>
     <cge:Term_Ref ObjectID="5756"/>
    <cge:TPSR_Ref TObjectID="4008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-25143" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3772.000000 -63.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25143" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4004"/>
     <cge:Term_Ref ObjectID="5748"/>
    <cge:TPSR_Ref TObjectID="4004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-25144" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3772.000000 -63.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25144" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4004"/>
     <cge:Term_Ref ObjectID="5748"/>
    <cge:TPSR_Ref TObjectID="4004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-25145" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3772.000000 -63.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25145" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4004"/>
     <cge:Term_Ref ObjectID="5748"/>
    <cge:TPSR_Ref TObjectID="4004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="1" id="ME-25146" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3772.000000 -63.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25146" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4004"/>
     <cge:Term_Ref ObjectID="5748"/>
    <cge:TPSR_Ref TObjectID="4004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-25139" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3883.000000 -63.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25139" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4000"/>
     <cge:Term_Ref ObjectID="5740"/>
    <cge:TPSR_Ref TObjectID="4000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-25140" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3883.000000 -63.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25140" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4000"/>
     <cge:Term_Ref ObjectID="5740"/>
    <cge:TPSR_Ref TObjectID="4000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-25141" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3883.000000 -63.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25141" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4000"/>
     <cge:Term_Ref ObjectID="5740"/>
    <cge:TPSR_Ref TObjectID="4000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="1" id="ME-25142" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3883.000000 -63.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25142" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4000"/>
     <cge:Term_Ref ObjectID="5740"/>
    <cge:TPSR_Ref TObjectID="4000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-25135" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3998.000000 -67.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25135" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3996"/>
     <cge:Term_Ref ObjectID="5732"/>
    <cge:TPSR_Ref TObjectID="3996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-25136" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3998.000000 -67.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25136" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3996"/>
     <cge:Term_Ref ObjectID="5732"/>
    <cge:TPSR_Ref TObjectID="3996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-25137" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3998.000000 -67.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25137" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3996"/>
     <cge:Term_Ref ObjectID="5732"/>
    <cge:TPSR_Ref TObjectID="3996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="1" id="ME-25138" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3998.000000 -67.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25138" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3996"/>
     <cge:Term_Ref ObjectID="5732"/>
    <cge:TPSR_Ref TObjectID="3996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-25127" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4204.000000 -64.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25127" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3984"/>
     <cge:Term_Ref ObjectID="5708"/>
    <cge:TPSR_Ref TObjectID="3984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-25128" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4204.000000 -64.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25128" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3984"/>
     <cge:Term_Ref ObjectID="5708"/>
    <cge:TPSR_Ref TObjectID="3984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-25129" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4204.000000 -64.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25129" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3984"/>
     <cge:Term_Ref ObjectID="5708"/>
    <cge:TPSR_Ref TObjectID="3984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="1" id="ME-25130" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4204.000000 -64.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25130" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3984"/>
     <cge:Term_Ref ObjectID="5708"/>
    <cge:TPSR_Ref TObjectID="3984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-25115" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4459.000000 -63.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25115" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3969"/>
     <cge:Term_Ref ObjectID="5678"/>
    <cge:TPSR_Ref TObjectID="3969"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-25116" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4459.000000 -63.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25116" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3969"/>
     <cge:Term_Ref ObjectID="5678"/>
    <cge:TPSR_Ref TObjectID="3969"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-25117" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4459.000000 -63.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25117" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3969"/>
     <cge:Term_Ref ObjectID="5678"/>
    <cge:TPSR_Ref TObjectID="3969"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="1" id="ME-25118" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4459.000000 -63.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25118" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3969"/>
     <cge:Term_Ref ObjectID="5678"/>
    <cge:TPSR_Ref TObjectID="3969"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-25111" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4548.000000 -63.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25111" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3965"/>
     <cge:Term_Ref ObjectID="5670"/>
    <cge:TPSR_Ref TObjectID="3965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-25112" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4548.000000 -63.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25112" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3965"/>
     <cge:Term_Ref ObjectID="5670"/>
    <cge:TPSR_Ref TObjectID="3965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-25113" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4548.000000 -63.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25113" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3965"/>
     <cge:Term_Ref ObjectID="5670"/>
    <cge:TPSR_Ref TObjectID="3965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="1" id="ME-25114" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4548.000000 -63.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25114" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3965"/>
     <cge:Term_Ref ObjectID="5670"/>
    <cge:TPSR_Ref TObjectID="3965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-25107" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4653.000000 -64.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25107" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3961"/>
     <cge:Term_Ref ObjectID="5662"/>
    <cge:TPSR_Ref TObjectID="3961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-25108" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4653.000000 -64.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25108" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3961"/>
     <cge:Term_Ref ObjectID="5662"/>
    <cge:TPSR_Ref TObjectID="3961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-25109" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4653.000000 -64.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25109" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3961"/>
     <cge:Term_Ref ObjectID="5662"/>
    <cge:TPSR_Ref TObjectID="3961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="1" id="ME-25110" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4653.000000 -64.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25110" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3961"/>
     <cge:Term_Ref ObjectID="5662"/>
    <cge:TPSR_Ref TObjectID="3961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-25103" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4756.000000 -64.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25103" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3957"/>
     <cge:Term_Ref ObjectID="5654"/>
    <cge:TPSR_Ref TObjectID="3957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-25104" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4756.000000 -64.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25104" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3957"/>
     <cge:Term_Ref ObjectID="5654"/>
    <cge:TPSR_Ref TObjectID="3957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-25105" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4756.000000 -64.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25105" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3957"/>
     <cge:Term_Ref ObjectID="5654"/>
    <cge:TPSR_Ref TObjectID="3957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="1" id="ME-25106" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4756.000000 -64.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25106" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3957"/>
     <cge:Term_Ref ObjectID="5654"/>
    <cge:TPSR_Ref TObjectID="3957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-25099" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4863.000000 -65.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25099" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3953"/>
     <cge:Term_Ref ObjectID="5646"/>
    <cge:TPSR_Ref TObjectID="3953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-25100" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4863.000000 -65.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25100" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3953"/>
     <cge:Term_Ref ObjectID="5646"/>
    <cge:TPSR_Ref TObjectID="3953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-25101" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4863.000000 -65.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25101" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3953"/>
     <cge:Term_Ref ObjectID="5646"/>
    <cge:TPSR_Ref TObjectID="3953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="1" id="ME-25102" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4863.000000 -65.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25102" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3953"/>
     <cge:Term_Ref ObjectID="5646"/>
    <cge:TPSR_Ref TObjectID="3953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-25095" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4964.000000 -63.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25095" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3949"/>
     <cge:Term_Ref ObjectID="5638"/>
    <cge:TPSR_Ref TObjectID="3949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-25096" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4964.000000 -63.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25096" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3949"/>
     <cge:Term_Ref ObjectID="5638"/>
    <cge:TPSR_Ref TObjectID="3949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-25097" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4964.000000 -63.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25097" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3949"/>
     <cge:Term_Ref ObjectID="5638"/>
    <cge:TPSR_Ref TObjectID="3949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="1" id="ME-25098" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4964.000000 -63.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25098" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3949"/>
     <cge:Term_Ref ObjectID="5638"/>
    <cge:TPSR_Ref TObjectID="3949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-25091" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5074.000000 -63.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25091" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3945"/>
     <cge:Term_Ref ObjectID="5630"/>
    <cge:TPSR_Ref TObjectID="3945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-25092" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5074.000000 -63.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25092" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3945"/>
     <cge:Term_Ref ObjectID="5630"/>
    <cge:TPSR_Ref TObjectID="3945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-25093" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5074.000000 -63.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25093" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3945"/>
     <cge:Term_Ref ObjectID="5630"/>
    <cge:TPSR_Ref TObjectID="3945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="1" id="ME-25094" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5074.000000 -63.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="25094" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3945"/>
     <cge:Term_Ref ObjectID="5630"/>
    <cge:TPSR_Ref TObjectID="3945"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="CX_DLK" flowDrawDirect="1" flowShape="0" id="AC-110kV.yaodaTdalong_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3838,-1155 3838,-1192 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="12973" ObjectName="AC-110kV.yaodaTdalong_line"/>
    <cge:TPSR_Ref TObjectID="12973_SS-32"/></metadata>
   <polyline fill="none" opacity="0" points="3838,-1155 3838,-1192 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YA" endPointId="0" endStationName="CX_DLK" flowDrawDirect="1" flowShape="0" id="AC-110kV.dalongkou1Tdlk_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4868,-1143 4959,-1143 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="12956" ObjectName="AC-110kV.dalongkou1Tdlk_line"/>
    <cge:TPSR_Ref TObjectID="12956_SS-32"/></metadata>
   <polyline fill="none" opacity="0" points="4868,-1143 4959,-1143 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_DLK"/>
</svg>