<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-183" aopId="1835014" id="thSvg" product="E8000V2" version="1.0" viewBox="3978 -1366 1547 1399">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_182aef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_182b710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_182be80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_182cb90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_182dc00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_182e680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_182f160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_182fc10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18305d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1830e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1830e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1832a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1832a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1833dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1835990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18365e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1836e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1837790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1838f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1839790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_123c190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_183aa60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_183bc90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_183c610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_183d100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1842360" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1842ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_183ee60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_18402f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1841070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_184faf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_18452f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1409" width="1557" x="3973" y="-1371"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="5" x1="4232" x2="4232" y1="-1103" y2="-975"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="5" x1="4791" x2="4791" y1="-1103" y2="-975"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="5" x1="5312" x2="5312" y1="-1103" y2="-975"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-127336">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4094.000000 -589.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23491" ObjectName="SW-CX_YRH.CX_YRH_561BK"/>
     <cge:Meas_Ref ObjectId="127336"/>
    <cge:TPSR_Ref TObjectID="23491"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127340">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4237.000000 -589.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23495" ObjectName="SW-CX_YRH.CX_YRH_562BK"/>
     <cge:Meas_Ref ObjectId="127340"/>
    <cge:TPSR_Ref TObjectID="23495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127332">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4362.000000 -589.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23487" ObjectName="SW-CX_YRH.CX_YRH_563BK"/>
     <cge:Meas_Ref ObjectId="127332"/>
    <cge:TPSR_Ref TObjectID="23487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127349">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4653.000000 -589.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23504" ObjectName="SW-CX_YRH.CX_YRH_571BK"/>
     <cge:Meas_Ref ObjectId="127349"/>
    <cge:TPSR_Ref TObjectID="23504"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127353">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4796.000000 -589.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23508" ObjectName="SW-CX_YRH.CX_YRH_572BK"/>
     <cge:Meas_Ref ObjectId="127353"/>
    <cge:TPSR_Ref TObjectID="23508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127345">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4921.000000 -589.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23500" ObjectName="SW-CX_YRH.CX_YRH_573BK"/>
     <cge:Meas_Ref ObjectId="127345"/>
    <cge:TPSR_Ref TObjectID="23500"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127362">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5174.000000 -589.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23517" ObjectName="SW-CX_YRH.CX_YRH_581BK"/>
     <cge:Meas_Ref ObjectId="127362"/>
    <cge:TPSR_Ref TObjectID="23517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127366">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5317.000000 -589.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23521" ObjectName="SW-CX_YRH.CX_YRH_582BK"/>
     <cge:Meas_Ref ObjectId="127366"/>
    <cge:TPSR_Ref TObjectID="23521"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127358">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5442.000000 -589.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23513" ObjectName="SW-CX_YRH.CX_YRH_583BK"/>
     <cge:Meas_Ref ObjectId="127358"/>
    <cge:TPSR_Ref TObjectID="23513"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_YRH.CX_YRH_5ⅥM">
    <g class="BV-500KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-974 4418,-974 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="23300" ObjectName="BS-CX_YRH.CX_YRH_5ⅥM"/>
    <cge:TPSR_Ref TObjectID="23300"/></metadata>
   <polyline fill="none" opacity="0" points="4050,-974 4418,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YRH.CX_YRH_5ⅦM">
    <g class="BV-500KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4609,-974 4977,-974 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="23301" ObjectName="BS-CX_YRH.CX_YRH_5ⅦM"/>
    <cge:TPSR_Ref TObjectID="23301"/></metadata>
   <polyline fill="none" opacity="0" points="4609,-974 4977,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YRH.CX_YRH_5ⅧM">
    <g class="BV-500KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5130,-974 5498,-974 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="23302" ObjectName="BS-CX_YRH.CX_YRH_5ⅧM"/>
    <cge:TPSR_Ref TObjectID="23302"/></metadata>
   <polyline fill="none" opacity="0" points="5130,-974 5498,-974 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Link_Layer">
   <g class="BV-500KV" id="g_12a4ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4156,-1030 4156,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23499@1" ObjectIDZND0="g_12b51f0@0" Pin0InfoVect0LinkObjId="g_12b51f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127344_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4156,-1030 4156,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_127ba80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4156,-994 4156,-974 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23499@0" ObjectIDZND0="23300@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127344_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4156,-994 4156,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1299350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4103,-974 4103,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23300@0" ObjectIDZND0="23492@1" Pin0InfoVect0LinkObjId="SW-127337_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_127ba80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4103,-974 4103,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_11ebcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4102,-140 4102,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_11eb690@0" Pin0InfoVect0LinkObjId="g_11eb690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4102,-140 4102,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_126f7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4103,-749 4103,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23492@0" ObjectIDZND0="23491@x" ObjectIDZND1="23493@x" Pin0InfoVect0LinkObjId="SW-127336_0" Pin0InfoVect1LinkObjId="SW-127338_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127337_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4103,-749 4103,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_126f9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4103,-684 4103,-624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="23492@x" ObjectIDND1="23493@x" ObjectIDZND0="23491@1" Pin0InfoVect0LinkObjId="SW-127336_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127337_0" Pin1InfoVect1LinkObjId="SW-127338_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4103,-684 4103,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_126fbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-684 4152,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_11ebeb0@0" ObjectIDZND0="23493@1" Pin0InfoVect0LinkObjId="SW-127338_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11ebeb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-684 4152,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_126fdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-684 4103,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="23493@0" ObjectIDZND0="23492@x" ObjectIDZND1="23491@x" Pin0InfoVect0LinkObjId="SW-127337_0" Pin0InfoVect1LinkObjId="SW-127336_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127338_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-684 4103,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_12706a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4103,-597 4103,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23491@0" ObjectIDZND0="23494@x" Pin0InfoVect0LinkObjId="SW-127339_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127336_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4103,-597 4103,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1270890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4103,-537 4103,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" ObjectIDND0="23491@x" ObjectIDND1="23494@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127336_0" Pin1InfoVect1LinkObjId="SW-127339_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4103,-537 4103,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_122ee00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4160,-537 4152,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_11ec4e0@0" ObjectIDZND0="23494@1" Pin0InfoVect0LinkObjId="SW-127339_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11ec4e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4160,-537 4152,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_122eff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-537 4103,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23494@0" ObjectIDZND0="23491@x" Pin0InfoVect0LinkObjId="SW-127336_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127339_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-537 4103,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_122d380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-140 4245,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_122cd50@0" Pin0InfoVect0LinkObjId="g_122cd50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-140 4245,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_122e2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4246,-749 4246,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23496@0" ObjectIDZND0="23495@x" ObjectIDZND1="23497@x" Pin0InfoVect0LinkObjId="SW-127340_0" Pin0InfoVect1LinkObjId="SW-127342_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127341_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4246,-749 4246,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_122e4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4246,-684 4246,-624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="23496@x" ObjectIDND1="23497@x" ObjectIDZND0="23495@1" Pin0InfoVect0LinkObjId="SW-127340_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127341_0" Pin1InfoVect1LinkObjId="SW-127342_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4246,-684 4246,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_122e6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4303,-684 4295,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_122d570@0" ObjectIDZND0="23497@1" Pin0InfoVect0LinkObjId="SW-127342_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_122d570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4303,-684 4295,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_122e8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4259,-684 4246,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23497@0" ObjectIDZND0="23495@x" ObjectIDZND1="23496@x" Pin0InfoVect0LinkObjId="SW-127340_0" Pin0InfoVect1LinkObjId="SW-127341_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127342_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4259,-684 4246,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_122ea90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4246,-597 4246,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23495@0" ObjectIDZND0="23498@x" Pin0InfoVect0LinkObjId="SW-127343_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127340_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4246,-597 4246,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1237930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4246,-537 4246,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" ObjectIDND0="23495@x" ObjectIDND1="23498@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127340_0" Pin1InfoVect1LinkObjId="SW-127343_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4246,-537 4246,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1237b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4303,-537 4295,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_122dba0@0" ObjectIDZND0="23498@1" Pin0InfoVect0LinkObjId="SW-127343_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_122dba0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4303,-537 4295,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1237d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4259,-537 4246,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23498@0" ObjectIDZND0="23495@x" Pin0InfoVect0LinkObjId="SW-127340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127343_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4259,-537 4246,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1237f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4246,-974 4246,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23300@0" ObjectIDZND0="23496@1" Pin0InfoVect0LinkObjId="SW-127341_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_127ba80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4246,-974 4246,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_11d9c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4370,-140 4370,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_11d9550@0" Pin0InfoVect0LinkObjId="g_11d9550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4370,-140 4370,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11dadd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4371,-749 4371,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23488@0" ObjectIDZND0="23487@x" ObjectIDZND1="23489@x" Pin0InfoVect0LinkObjId="SW-127332_0" Pin0InfoVect1LinkObjId="SW-127334_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127333_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4371,-749 4371,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11dafc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4371,-684 4371,-624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="23488@x" ObjectIDND1="23489@x" ObjectIDZND0="23487@1" Pin0InfoVect0LinkObjId="SW-127332_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127333_0" Pin1InfoVect1LinkObjId="SW-127334_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4371,-684 4371,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11db1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4428,-684 4420,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_11d9e70@0" ObjectIDZND0="23489@1" Pin0InfoVect0LinkObjId="SW-127334_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11d9e70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4428,-684 4420,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11db3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4384,-684 4371,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23489@0" ObjectIDZND0="23487@x" ObjectIDZND1="23488@x" Pin0InfoVect0LinkObjId="SW-127332_0" Pin0InfoVect1LinkObjId="SW-127333_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127334_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4384,-684 4371,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_120e230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4371,-597 4371,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23487@0" ObjectIDZND0="23490@x" Pin0InfoVect0LinkObjId="SW-127335_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127332_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4371,-597 4371,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_120e420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4371,-537 4371,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" ObjectIDND0="23487@x" ObjectIDND1="23490@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127332_0" Pin1InfoVect1LinkObjId="SW-127335_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4371,-537 4371,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_120e610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4428,-537 4420,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_11da620@0" ObjectIDZND0="23490@1" Pin0InfoVect0LinkObjId="SW-127335_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11da620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4428,-537 4420,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_120e800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4384,-537 4371,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23490@0" ObjectIDZND0="23487@x" Pin0InfoVect0LinkObjId="SW-127332_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127335_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4384,-537 4371,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_120e9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4371,-974 4371,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23300@0" ObjectIDZND0="23488@1" Pin0InfoVect0LinkObjId="SW-127333_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_127ba80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4371,-974 4371,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11d7940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4715,-1030 4715,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23512@1" ObjectIDZND0="g_11d7d20@0" Pin0InfoVect0LinkObjId="g_11d7d20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127357_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4715,-1030 4715,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11d7b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4715,-994 4715,-974 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23512@0" ObjectIDZND0="23301@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127357_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4715,-994 4715,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11a3dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4662,-974 4662,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23301@0" ObjectIDZND0="23505@1" Pin0InfoVect0LinkObjId="SW-127350_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11d7b30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4662,-974 4662,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_125a600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4661,-140 4661,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_1259ed0@0" Pin0InfoVect0LinkObjId="g_1259ed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4661,-140 4661,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_125ba20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4662,-749 4662,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23505@0" ObjectIDZND0="23504@x" ObjectIDZND1="23506@x" Pin0InfoVect0LinkObjId="SW-127349_0" Pin0InfoVect1LinkObjId="SW-127351_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4662,-749 4662,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_125bc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4662,-684 4662,-624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="23505@x" ObjectIDND1="23506@x" ObjectIDZND0="23504@1" Pin0InfoVect0LinkObjId="SW-127349_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127350_0" Pin1InfoVect1LinkObjId="SW-127351_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4662,-684 4662,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_125be60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4719,-684 4711,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_125a7f0@0" ObjectIDZND0="23506@1" Pin0InfoVect0LinkObjId="SW-127351_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_125a7f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4719,-684 4711,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_125c080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4675,-684 4662,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23506@0" ObjectIDZND0="23504@x" ObjectIDZND1="23505@x" Pin0InfoVect0LinkObjId="SW-127349_0" Pin0InfoVect1LinkObjId="SW-127350_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127351_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4675,-684 4662,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_125c2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4662,-597 4662,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23504@0" ObjectIDZND0="23507@x" Pin0InfoVect0LinkObjId="SW-127352_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127349_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4662,-597 4662,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_116d780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4662,-537 4662,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" ObjectIDND0="23504@x" ObjectIDND1="23507@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127349_0" Pin1InfoVect1LinkObjId="SW-127352_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4662,-537 4662,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_116d9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4719,-537 4711,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_125b0f0@0" ObjectIDZND0="23507@1" Pin0InfoVect0LinkObjId="SW-127352_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_125b0f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4719,-537 4711,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_116dbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4675,-537 4662,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23507@0" ObjectIDZND0="23504@x" Pin0InfoVect0LinkObjId="SW-127349_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127352_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4675,-537 4662,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_117c710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4804,-140 4804,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_117bfe0@0" Pin0InfoVect0LinkObjId="g_117bfe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4804,-140 4804,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11bb2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4805,-749 4805,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23509@0" ObjectIDZND0="23508@x" ObjectIDZND1="23510@x" Pin0InfoVect0LinkObjId="SW-127353_0" Pin0InfoVect1LinkObjId="SW-127355_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127354_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4805,-749 4805,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11bb4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4805,-684 4805,-624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="23509@x" ObjectIDND1="23510@x" ObjectIDZND0="23508@1" Pin0InfoVect0LinkObjId="SW-127353_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127354_0" Pin1InfoVect1LinkObjId="SW-127355_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4805,-684 4805,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11bb6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4862,-684 4854,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_117c900@0" ObjectIDZND0="23510@1" Pin0InfoVect0LinkObjId="SW-127355_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_117c900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4862,-684 4854,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11bb910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4818,-684 4805,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23510@0" ObjectIDZND0="23508@x" ObjectIDZND1="23509@x" Pin0InfoVect0LinkObjId="SW-127353_0" Pin0InfoVect1LinkObjId="SW-127354_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127355_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4818,-684 4805,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11bbb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4805,-597 4805,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23508@0" ObjectIDZND0="23511@x" Pin0InfoVect0LinkObjId="SW-127356_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127353_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4805,-597 4805,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11bbd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4805,-537 4805,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" ObjectIDND0="23508@x" ObjectIDND1="23511@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127353_0" Pin1InfoVect1LinkObjId="SW-127356_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4805,-537 4805,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11bbf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4862,-537 4854,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_117d230@0" ObjectIDZND0="23511@1" Pin0InfoVect0LinkObjId="SW-127356_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_117d230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4862,-537 4854,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11bc190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4818,-537 4805,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23511@0" ObjectIDZND0="23508@x" Pin0InfoVect0LinkObjId="SW-127353_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127356_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4818,-537 4805,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11bc3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4805,-974 4805,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23301@0" ObjectIDZND0="23509@1" Pin0InfoVect0LinkObjId="SW-127354_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11d7b30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4805,-974 4805,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_11c9040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4929,-140 4929,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_11c0240@0" Pin0InfoVect0LinkObjId="g_11c0240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4929,-140 4929,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11ca6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4930,-749 4930,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23501@0" ObjectIDZND0="23500@x" ObjectIDZND1="23502@x" Pin0InfoVect0LinkObjId="SW-127345_0" Pin0InfoVect1LinkObjId="SW-127347_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127346_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4930,-749 4930,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11ca930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4930,-684 4930,-624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="23501@x" ObjectIDND1="23502@x" ObjectIDZND0="23500@1" Pin0InfoVect0LinkObjId="SW-127345_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127346_0" Pin1InfoVect1LinkObjId="SW-127347_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4930,-684 4930,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11cab90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4987,-684 4979,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_11c9270@0" ObjectIDZND0="23502@1" Pin0InfoVect0LinkObjId="SW-127347_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11c9270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4987,-684 4979,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11cadf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4943,-684 4930,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23502@0" ObjectIDZND0="23500@x" ObjectIDZND1="23501@x" Pin0InfoVect0LinkObjId="SW-127345_0" Pin0InfoVect1LinkObjId="SW-127346_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127347_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4943,-684 4930,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11cb050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4930,-597 4930,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23500@0" ObjectIDZND0="23503@x" Pin0InfoVect0LinkObjId="SW-127348_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127345_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4930,-597 4930,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11cb2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4930,-537 4930,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" ObjectIDND0="23500@x" ObjectIDND1="23503@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127345_0" Pin1InfoVect1LinkObjId="SW-127348_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4930,-537 4930,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11cb510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4987,-537 4979,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_11c9ca0@0" ObjectIDZND0="23503@1" Pin0InfoVect0LinkObjId="SW-127348_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11c9ca0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4987,-537 4979,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11658c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4943,-537 4930,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23503@0" ObjectIDZND0="23500@x" Pin0InfoVect0LinkObjId="SW-127345_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127348_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4943,-537 4930,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1165b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4930,-974 4930,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23301@0" ObjectIDZND0="23501@1" Pin0InfoVect0LinkObjId="SW-127346_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11d7b30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4930,-974 4930,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11f4ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5236,-1030 5236,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23525@1" ObjectIDZND0="g_11f5380@0" Pin0InfoVect0LinkObjId="g_11f5380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127370_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5236,-1030 5236,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11f5120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5236,-994 5236,-974 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23525@0" ObjectIDZND0="23302@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5236,-994 5236,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11a10f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5183,-974 5183,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23302@0" ObjectIDZND0="23518@1" Pin0InfoVect0LinkObjId="SW-127363_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11f5120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5183,-974 5183,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_11fe950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5182,-140 5182,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_11fe140@0" Pin0InfoVect0LinkObjId="g_11fe140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5182,-140 5182,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_11fff90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5183,-749 5183,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23518@0" ObjectIDZND0="23517@x" ObjectIDZND1="23519@x" Pin0InfoVect0LinkObjId="SW-127362_0" Pin0InfoVect1LinkObjId="SW-127364_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127363_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5183,-749 5183,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_12001d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5183,-684 5183,-624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="23518@x" ObjectIDND1="23519@x" ObjectIDZND0="23517@1" Pin0InfoVect0LinkObjId="SW-127362_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127363_0" Pin1InfoVect1LinkObjId="SW-127364_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5183,-684 5183,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1200430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5240,-684 5232,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_11feb80@0" ObjectIDZND0="23519@1" Pin0InfoVect0LinkObjId="SW-127364_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11feb80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5240,-684 5232,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1200690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5196,-684 5183,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23519@0" ObjectIDZND0="23517@x" ObjectIDZND1="23518@x" Pin0InfoVect0LinkObjId="SW-127362_0" Pin0InfoVect1LinkObjId="SW-127363_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127364_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5196,-684 5183,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_12008f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5183,-597 5183,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23517@0" ObjectIDZND0="23520@x" Pin0InfoVect0LinkObjId="SW-127365_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127362_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5183,-597 5183,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1200b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5183,-537 5183,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" ObjectIDND0="23517@x" ObjectIDND1="23520@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127362_0" Pin1InfoVect1LinkObjId="SW-127365_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5183,-537 5183,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1200db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5240,-537 5232,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_11ff580@0" ObjectIDZND0="23520@1" Pin0InfoVect0LinkObjId="SW-127365_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11ff580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5240,-537 5232,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1201010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5196,-537 5183,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23520@0" ObjectIDZND0="23517@x" Pin0InfoVect0LinkObjId="SW-127362_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127365_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5196,-537 5183,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1216a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5325,-140 5325,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_1216290@0" Pin0InfoVect0LinkObjId="g_1216290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5325,-140 5325,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_112cf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5326,-749 5326,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23522@0" ObjectIDZND0="23521@x" ObjectIDZND1="23523@x" Pin0InfoVect0LinkObjId="SW-127366_0" Pin0InfoVect1LinkObjId="SW-127368_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127367_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5326,-749 5326,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_112d1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5326,-684 5326,-624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="23522@x" ObjectIDND1="23523@x" ObjectIDZND0="23521@1" Pin0InfoVect0LinkObjId="SW-127366_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127367_0" Pin1InfoVect1LinkObjId="SW-127368_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5326,-684 5326,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_112d410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5383,-684 5375,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1216c90@0" ObjectIDZND0="23523@1" Pin0InfoVect0LinkObjId="SW-127368_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1216c90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5383,-684 5375,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_112d670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5339,-684 5326,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23523@0" ObjectIDZND0="23521@x" ObjectIDZND1="23522@x" Pin0InfoVect0LinkObjId="SW-127366_0" Pin0InfoVect1LinkObjId="SW-127367_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127368_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5339,-684 5326,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_112d8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5326,-597 5326,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23521@0" ObjectIDZND0="23524@x" Pin0InfoVect0LinkObjId="SW-127369_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127366_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5326,-597 5326,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_112db30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5326,-537 5326,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" ObjectIDND0="23521@x" ObjectIDND1="23524@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127366_0" Pin1InfoVect1LinkObjId="SW-127369_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5326,-537 5326,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_112dd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5383,-537 5375,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_12176c0@0" ObjectIDZND0="23524@1" Pin0InfoVect0LinkObjId="SW-127369_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12176c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5383,-537 5375,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_112dff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5339,-537 5326,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23524@0" ObjectIDZND0="23521@x" Pin0InfoVect0LinkObjId="SW-127366_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127369_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5339,-537 5326,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_112e250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5326,-974 5326,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23302@0" ObjectIDZND0="23522@1" Pin0InfoVect0LinkObjId="SW-127367_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11f5120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5326,-974 5326,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1285930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5450,-140 5450,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_1285160@0" Pin0InfoVect0LinkObjId="g_1285160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5450,-140 5450,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_127cd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5451,-749 5451,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23514@0" ObjectIDZND0="23513@x" ObjectIDZND1="23515@x" Pin0InfoVect0LinkObjId="SW-127358_0" Pin0InfoVect1LinkObjId="SW-127360_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127359_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5451,-749 5451,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_127cfd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5451,-684 5451,-624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="23514@x" ObjectIDND1="23515@x" ObjectIDZND0="23513@1" Pin0InfoVect0LinkObjId="SW-127358_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127359_0" Pin1InfoVect1LinkObjId="SW-127360_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5451,-684 5451,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_127d230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5508,-684 5500,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1285b60@0" ObjectIDZND0="23515@1" Pin0InfoVect0LinkObjId="SW-127360_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1285b60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5508,-684 5500,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_127d490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5464,-684 5451,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23515@0" ObjectIDZND0="23513@x" ObjectIDZND1="23514@x" Pin0InfoVect0LinkObjId="SW-127358_0" Pin0InfoVect1LinkObjId="SW-127359_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5464,-684 5451,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_127d6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5451,-597 5451,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23513@0" ObjectIDZND0="23516@x" Pin0InfoVect0LinkObjId="SW-127361_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127358_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5451,-597 5451,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_127d950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5451,-537 5451,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" ObjectIDND0="23513@x" ObjectIDND1="23516@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127358_0" Pin1InfoVect1LinkObjId="SW-127361_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5451,-537 5451,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_127dbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5508,-537 5500,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1286590@0" ObjectIDZND0="23516@1" Pin0InfoVect0LinkObjId="SW-127361_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1286590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5508,-537 5500,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_127de10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5464,-537 5451,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23516@0" ObjectIDZND0="23513@x" Pin0InfoVect0LinkObjId="SW-127358_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127361_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5464,-537 5451,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_127e070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5451,-974 5451,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23302@0" ObjectIDZND0="23514@1" Pin0InfoVect0LinkObjId="SW-127359_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11f5120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5451,-974 5451,-785 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="23300" cx="4156" cy="-974" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23300" cx="4103" cy="-974" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23300" cx="4246" cy="-974" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23300" cx="4371" cy="-974" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23301" cx="4715" cy="-974" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23301" cx="4662" cy="-974" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23301" cx="4805" cy="-974" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23301" cx="4930" cy="-974" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23302" cx="5236" cy="-974" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23302" cx="5183" cy="-974" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23302" cx="5326" cy="-974" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23302" cx="5451" cy="-974" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimSun" font-size="38" graphid="g_1256b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4645.500000 -1313.500000) translate(0,31)">500kV永仁换流站滤波系统</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_11eb430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4085.000000 -291.500000) translate(0,15)">ACF</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1263880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4228.000000 -291.500000) translate(0,15)">ACF</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_11d9360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4353.000000 -291.500000) translate(0,15)">ACF</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1231ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4011.000000 -994.000000) translate(0,15)">500kV#6M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_11a6070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4644.000000 -291.500000) translate(0,15)">ACF</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_117bb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4787.000000 -291.500000) translate(0,15)">ACF</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_11bfd50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4912.000000 -291.500000) translate(0,15)">ACF</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1165d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4570.000000 -994.000000) translate(0,15)">500kV#7M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1166230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4064.000000 -97.500000) translate(0,15)">DT 11/24</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_11665f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4199.500000 -97.500000) translate(0,15)">TT 3/13/36</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1166a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4353.000000 -97.500000) translate(0,15)">SC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1168440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4064.000000 -80.500000) translate(0,15)">175MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1256ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4071.000000 -41.500000) translate(0,15)">561交流</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1256ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4071.000000 -41.500000) translate(0,33)">滤波器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12573e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4087.000000 14.500000) translate(0,15)">A型</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1257840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4208.000000 -80.500000) translate(0,15)">175MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1257c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4215.000000 -41.500000) translate(0,15)">562交流</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1257c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4215.000000 -41.500000) translate(0,33)">滤波器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1257ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4231.000000 14.500000) translate(0,15)">B型</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12580e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4329.500000 -80.500000) translate(0,15)">175MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12582f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4337.000000 -41.500000) translate(0,15)">563交流</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12582f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4337.000000 -41.500000) translate(0,33)">滤波器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1258500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4626.000000 -97.500000) translate(0,15)">DT 11/24</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1258740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4761.500000 -97.500000) translate(0,15)">TT 3/13/36</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1258990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4915.000000 -97.500000) translate(0,15)">SC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1258bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4626.000000 -80.500000) translate(0,15)">175MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1258e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4633.000000 -41.500000) translate(0,15)">571交流</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1258e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4633.000000 -41.500000) translate(0,33)">滤波器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1259040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4770.000000 -80.500000) translate(0,15)">175MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1259280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4777.000000 -41.500000) translate(0,15)">572交流</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1259280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4777.000000 -41.500000) translate(0,33)">滤波器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12594c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4891.500000 -80.500000) translate(0,15)">175MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1259700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4899.000000 -41.500000) translate(0,15)">573交流</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1259700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4899.000000 -41.500000) translate(0,33)">滤波器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1259940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4355.000000 14.500000) translate(0,15)">C型</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_11f2670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4649.000000 14.500000) translate(0,15)">A型</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_11f28b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4793.000000 14.500000) translate(0,15)">B型</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_11f2af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4917.000000 14.500000) translate(0,15)">C型</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_11a3730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5165.000000 -291.500000) translate(0,15)">ACF</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1215dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5308.000000 -291.500000) translate(0,15)">ACF</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1284c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5433.000000 -291.500000) translate(0,15)">ACF</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_127e2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5091.000000 -994.000000) translate(0,15)">500kV#8M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_127e7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5147.000000 -97.500000) translate(0,15)">DT 11/24</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_127e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5282.500000 -97.500000) translate(0,15)">TT 3/13/36</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_127ec10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5436.000000 -97.500000) translate(0,15)">SC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_127ee40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5147.000000 -80.500000) translate(0,15)">175MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_127f080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5154.000000 -41.500000) translate(0,15)">581交流</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_127f080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5154.000000 -41.500000) translate(0,33)">滤波器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_127f2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5291.000000 -80.500000) translate(0,15)">175MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_127f500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5298.000000 -41.500000) translate(0,15)">582交流</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_127f500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5298.000000 -41.500000) translate(0,33)">滤波器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_127f740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5412.500000 -80.500000) translate(0,15)">175MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_127f980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5420.000000 -41.500000) translate(0,15)">583交流</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_127f980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5420.000000 -41.500000) translate(0,33)">滤波器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_127fbc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5170.000000 14.500000) translate(0,15)">A型</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_127fe00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5314.000000 14.500000) translate(0,15)">B型</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1280040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5438.000000 14.500000) translate(0,15)">C型</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1280280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4150.000000 -1146.500000) translate(0,15)">第一大组交流滤波器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12250c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4208.500000 -1125.500000) translate(0,15)">ACF1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12255b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4709.000000 -1146.500000) translate(0,15)">第二大组交流滤波器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1280b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4767.500000 -1125.500000) translate(0,15)">ACF2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_11e2820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5234.000000 -1146.500000) translate(0,15)">第三大组交流滤波器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_11e2a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5292.500000 -1125.500000) translate(0,15)">ACF3</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e2ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4112.000000 -618.000000) translate(0,12)">561</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e2eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4163.000000 -1019.000000) translate(0,12)">5617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e30e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4110.000000 -773.000000) translate(0,12)">5611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e3320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4115.000000 -710.000000) translate(0,12)">56117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e3560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4114.000000 -563.000000) translate(0,12)">56127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e37a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4255.000000 -618.000000) translate(0,12)">562</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e39e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4257.000000 -563.000000) translate(0,12)">56227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e3c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4253.000000 -781.000000) translate(0,12)">5621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e3e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4257.000000 -710.000000) translate(0,12)">56217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e40a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4380.000000 -618.000000) translate(0,12)">563</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e42e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4382.000000 -563.000000) translate(0,12)">56327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e4740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4382.000000 -710.000000) translate(0,12)">56317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e4980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4378.000000 -774.000000) translate(0,12)">5631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e4bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4671.000000 -618.000000) translate(0,12)">571</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e4e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4722.000000 -1019.000000) translate(0,12)">5717</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e5040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4673.000000 -563.000000) translate(0,12)">57127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e5280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4669.000000 -774.000000) translate(0,12)">5711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e54c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4674.000000 -710.000000) translate(0,12)">57117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e5700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4814.000000 -618.000000) translate(0,12)">572</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e5940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4816.000000 -563.000000) translate(0,12)">57227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e5b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4812.000000 -774.000000) translate(0,12)">5721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e5dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4816.000000 -710.000000) translate(0,12)">57217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e6000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4939.000000 -618.000000) translate(0,12)">573</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e6240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4941.000000 -563.000000) translate(0,12)">57327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e6480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4941.000000 -710.000000) translate(0,12)">57317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e66c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4937.000000 -774.000000) translate(0,12)">5731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e6900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5192.000000 -618.000000) translate(0,12)">581</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e6b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5243.000000 -1019.000000) translate(0,12)">5817</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e6dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5194.000000 -563.000000) translate(0,12)">58127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e7000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5190.000000 -774.000000) translate(0,12)">5811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1204510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5195.000000 -710.000000) translate(0,12)">58117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1204750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5335.000000 -618.000000) translate(0,12)">582</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1204990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5337.000000 -563.000000) translate(0,12)">58227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1204bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5333.000000 -774.000000) translate(0,12)">5821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1204e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5337.000000 -710.000000) translate(0,12)">58217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1205050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5460.000000 -618.000000) translate(0,12)">583</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1205290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5462.000000 -563.000000) translate(0,12)">58327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12054d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5458.000000 -774.000000) translate(0,12)">5831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1205710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5462.000000 -710.000000) translate(0,12)">58317</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="285" stroke="rgb(255,255,255)" stroke-width="1" width="80" x="4062" y="-425"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="285" stroke="rgb(255,255,255)" stroke-width="1" width="80" x="4205" y="-425"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="285" stroke="rgb(255,255,255)" stroke-width="1" width="80" x="4330" y="-425"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="285" stroke="rgb(255,255,255)" stroke-width="1" width="80" x="4621" y="-425"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="285" stroke="rgb(255,255,255)" stroke-width="1" width="80" x="4764" y="-425"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="285" stroke="rgb(255,255,255)" stroke-width="1" width="80" x="4889" y="-425"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="285" stroke="rgb(255,255,255)" stroke-width="1" width="80" x="5142" y="-425"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="285" stroke="rgb(255,255,255)" stroke-width="1" width="80" x="5285" y="-425"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="285" stroke="rgb(255,255,255)" stroke-width="1" width="80" x="5410" y="-425"/>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_12b51f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4150.000000 -1036.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11eb690" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4096.000000 -104.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11ebeb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4156.000000 -678.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11ec4e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4156.000000 -531.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_122cd50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4239.000000 -104.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_122d570" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4299.000000 -678.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_122dba0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4299.000000 -531.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11d9550" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4364.000000 -104.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11d9e70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4424.000000 -678.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11da620" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4424.000000 -531.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11d7d20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4709.000000 -1036.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1259ed0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4655.000000 -104.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_125a7f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4715.000000 -678.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_125b0f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4715.000000 -531.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_117bfe0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4798.000000 -104.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_117c900" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4858.000000 -678.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_117d230" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4858.000000 -531.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11c0240" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4923.000000 -104.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11c9270" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4983.000000 -678.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11c9ca0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4983.000000 -531.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11f5380" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5230.000000 -1036.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11fe140" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5176.000000 -104.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11feb80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5236.000000 -678.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11ff580" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5236.000000 -531.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1216290" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5319.000000 -104.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1216c90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5379.000000 -678.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12176c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5379.000000 -531.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1285160" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5444.000000 -104.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1285b60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5504.000000 -678.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1286590" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5504.000000 -531.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="84" qtmmishow="hidden" width="534" x="4570" y="-1337"/>
    </a>
   <metadata/><rect fill="white" height="84" opacity="0" stroke="white" transform="" width="534" x="4570" y="-1337"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="131" qtmmishow="hidden" width="173" x="4471" y="-1365"/>
    </a>
   <metadata/><rect fill="white" height="131" opacity="0" stroke="white" transform="" width="173" x="4471" y="-1365"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1206680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4241.000000 1084.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1207e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4266.000000 1069.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11de4c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3978.000000 453.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11df4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4529.000000 450.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11e0480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5057.000000 453.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 4736.500000 -1227.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127093" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4310.000000 -1083.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127093" ObjectName="CX_YRH:CX_YRH_5ⅥM_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127092" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4310.000000 -1068.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127092" ObjectName="CX_YRH:CX_YRH_5ⅥM_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127095" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4807.000000 -1083.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127095" ObjectName="CX_YRH:CX_YRH_5ⅦM_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127094" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4807.000000 -1068.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127094" ObjectName="CX_YRH:CX_YRH_5ⅦM_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127097" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5331.000000 -1083.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127097" ObjectName="CX_YRH:CX_YRH_5ⅧM_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127096" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5331.000000 -1068.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127096" ObjectName="CX_YRH:CX_YRH_5ⅧM_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127098" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4320.000000 -452.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127098" ObjectName="CX_YRH:CX_YRH_LBXT_Q11_60"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127099" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4053.000000 -452.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127099" ObjectName="CX_YRH:CX_YRH_LBXT_Q12_61"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127100" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4193.000000 -452.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127100" ObjectName="CX_YRH:CX_YRH_LBXT_Q13_62"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127101" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4870.000000 -451.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127101" ObjectName="CX_YRH:CX_YRH_LBXT_Q21_63"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127102" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4601.000000 -451.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127102" ObjectName="CX_YRH:CX_YRH_LBXT_Q22_64"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127103" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4744.000000 -451.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127103" ObjectName="CX_YRH:CX_YRH_LBXT_Q23_65"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127104" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5396.000000 -454.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127104" ObjectName="CX_YRH:CX_YRH_LBXT_Q31_66"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127105" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5128.000000 -454.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127105" ObjectName="CX_YRH:CX_YRH_LBXT_Q32_67"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127106" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5271.000000 -454.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127106" ObjectName="CX_YRH:CX_YRH_LBXT_Q33_68"/>
    </metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-127344">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4147.000000 -989.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23499" ObjectName="SW-CX_YRH.CX_YRH_5617SW"/>
     <cge:Meas_Ref ObjectId="127344"/>
    <cge:TPSR_Ref TObjectID="23499"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127337">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4094.000000 -744.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23492" ObjectName="SW-CX_YRH.CX_YRH_5611SW"/>
     <cge:Meas_Ref ObjectId="127337"/>
    <cge:TPSR_Ref TObjectID="23492"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127338">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4111.000000 -679.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23493" ObjectName="SW-CX_YRH.CX_YRH_56117SW"/>
     <cge:Meas_Ref ObjectId="127338"/>
    <cge:TPSR_Ref TObjectID="23493"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127339">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4111.000000 -532.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23494" ObjectName="SW-CX_YRH.CX_YRH_56127SW"/>
     <cge:Meas_Ref ObjectId="127339"/>
    <cge:TPSR_Ref TObjectID="23494"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127341">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4237.000000 -744.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23496" ObjectName="SW-CX_YRH.CX_YRH_5621SW"/>
     <cge:Meas_Ref ObjectId="127341"/>
    <cge:TPSR_Ref TObjectID="23496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127342">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4254.000000 -679.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23497" ObjectName="SW-CX_YRH.CX_YRH_56217SW"/>
     <cge:Meas_Ref ObjectId="127342"/>
    <cge:TPSR_Ref TObjectID="23497"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127343">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4254.000000 -532.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23498" ObjectName="SW-CX_YRH.CX_YRH_56227SW"/>
     <cge:Meas_Ref ObjectId="127343"/>
    <cge:TPSR_Ref TObjectID="23498"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127333">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4362.000000 -744.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23488" ObjectName="SW-CX_YRH.CX_YRH_5631SW"/>
     <cge:Meas_Ref ObjectId="127333"/>
    <cge:TPSR_Ref TObjectID="23488"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127334">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4379.000000 -679.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23489" ObjectName="SW-CX_YRH.CX_YRH_56317SW"/>
     <cge:Meas_Ref ObjectId="127334"/>
    <cge:TPSR_Ref TObjectID="23489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127335">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4379.000000 -532.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23490" ObjectName="SW-CX_YRH.CX_YRH_56327SW"/>
     <cge:Meas_Ref ObjectId="127335"/>
    <cge:TPSR_Ref TObjectID="23490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127357">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4706.000000 -989.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23512" ObjectName="SW-CX_YRH.CX_YRH_5717SW"/>
     <cge:Meas_Ref ObjectId="127357"/>
    <cge:TPSR_Ref TObjectID="23512"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127350">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4653.000000 -744.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23505" ObjectName="SW-CX_YRH.CX_YRH_5711SW"/>
     <cge:Meas_Ref ObjectId="127350"/>
    <cge:TPSR_Ref TObjectID="23505"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127351">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4670.000000 -679.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23506" ObjectName="SW-CX_YRH.CX_YRH_57117SW"/>
     <cge:Meas_Ref ObjectId="127351"/>
    <cge:TPSR_Ref TObjectID="23506"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127352">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4670.000000 -532.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23507" ObjectName="SW-CX_YRH.CX_YRH_57127SW"/>
     <cge:Meas_Ref ObjectId="127352"/>
    <cge:TPSR_Ref TObjectID="23507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127354">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4796.000000 -744.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23509" ObjectName="SW-CX_YRH.CX_YRH_5721SW"/>
     <cge:Meas_Ref ObjectId="127354"/>
    <cge:TPSR_Ref TObjectID="23509"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127355">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4813.000000 -679.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23510" ObjectName="SW-CX_YRH.CX_YRH_57217SW"/>
     <cge:Meas_Ref ObjectId="127355"/>
    <cge:TPSR_Ref TObjectID="23510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127356">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4813.000000 -532.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23511" ObjectName="SW-CX_YRH.CX_YRH_57227SW"/>
     <cge:Meas_Ref ObjectId="127356"/>
    <cge:TPSR_Ref TObjectID="23511"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127346">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4921.000000 -744.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23501" ObjectName="SW-CX_YRH.CX_YRH_5731SW"/>
     <cge:Meas_Ref ObjectId="127346"/>
    <cge:TPSR_Ref TObjectID="23501"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127347">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4938.000000 -679.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23502" ObjectName="SW-CX_YRH.CX_YRH_57317SW"/>
     <cge:Meas_Ref ObjectId="127347"/>
    <cge:TPSR_Ref TObjectID="23502"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127348">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4938.000000 -532.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23503" ObjectName="SW-CX_YRH.CX_YRH_57327SW"/>
     <cge:Meas_Ref ObjectId="127348"/>
    <cge:TPSR_Ref TObjectID="23503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127370">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5227.000000 -989.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23525" ObjectName="SW-CX_YRH.CX_YRH_5817SW"/>
     <cge:Meas_Ref ObjectId="127370"/>
    <cge:TPSR_Ref TObjectID="23525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127363">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5174.000000 -744.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23518" ObjectName="SW-CX_YRH.CX_YRH_5811SW"/>
     <cge:Meas_Ref ObjectId="127363"/>
    <cge:TPSR_Ref TObjectID="23518"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127364">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5191.000000 -679.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23519" ObjectName="SW-CX_YRH.CX_YRH_58117SW"/>
     <cge:Meas_Ref ObjectId="127364"/>
    <cge:TPSR_Ref TObjectID="23519"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127365">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5191.000000 -532.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23520" ObjectName="SW-CX_YRH.CX_YRH_58127SW"/>
     <cge:Meas_Ref ObjectId="127365"/>
    <cge:TPSR_Ref TObjectID="23520"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127367">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5317.000000 -744.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23522" ObjectName="SW-CX_YRH.CX_YRH_5821SW"/>
     <cge:Meas_Ref ObjectId="127367"/>
    <cge:TPSR_Ref TObjectID="23522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127368">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5334.000000 -679.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23523" ObjectName="SW-CX_YRH.CX_YRH_58217SW"/>
     <cge:Meas_Ref ObjectId="127368"/>
    <cge:TPSR_Ref TObjectID="23523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127369">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5334.000000 -532.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23524" ObjectName="SW-CX_YRH.CX_YRH_58227SW"/>
     <cge:Meas_Ref ObjectId="127369"/>
    <cge:TPSR_Ref TObjectID="23524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127359">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5442.000000 -744.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23514" ObjectName="SW-CX_YRH.CX_YRH_5831SW"/>
     <cge:Meas_Ref ObjectId="127359"/>
    <cge:TPSR_Ref TObjectID="23514"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127360">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5459.000000 -679.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23515" ObjectName="SW-CX_YRH.CX_YRH_58317SW"/>
     <cge:Meas_Ref ObjectId="127360"/>
    <cge:TPSR_Ref TObjectID="23515"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127361">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5459.000000 -532.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23516" ObjectName="SW-CX_YRH.CX_YRH_58327SW"/>
     <cge:Meas_Ref ObjectId="127361"/>
    <cge:TPSR_Ref TObjectID="23516"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="楚雄地区_500kV_永仁换流站.svg" style="fill-opacity:0"><rect height="84" qtmmishow="hidden" width="534" x="4570" y="-1337"/></g>
   <g href="楚雄地区_500kV_永仁换流站.svg" style="fill-opacity:0"><rect height="131" qtmmishow="hidden" width="173" x="4471" y="-1365"/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1"/>
</svg>