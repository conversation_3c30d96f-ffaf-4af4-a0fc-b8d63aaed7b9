<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-27" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3025 -1244 2329 1327">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape13">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="10" x2="10" y1="5" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="0" x2="20" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="0" x2="20" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="10" x2="10" y1="23" y2="33"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="13" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="20" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape82">
    <ellipse cx="42" cy="44" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <circle cx="35" cy="38" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="42" cy="34" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="27" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="63" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="23" x2="26" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="21" x2="29" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="25" x2="25" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="31" x2="19" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="43" y1="17" y2="17"/>
    <rect height="27" stroke-width="0.416667" width="14" x="0" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="42" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="26" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="63" y2="72"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="58" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="8" y2="37"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape83">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.504561" x1="37" x2="37" y1="19" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="57" x2="57" y1="5" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="2" x2="2" y1="58" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="20" x2="20" y1="5" y2="11"/>
    <rect height="26" stroke-width="0.398039" width="12" x="31" y="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.393258" x1="21" x2="56" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="68" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="68" y2="59"/>
    <polyline arcFlag="1" points="13,48 12,48 12,48 11,48 10,48 10,49 9,49 8,50 8,50 8,51 7,52 7,52 7,53 7,54 7,55 7,55 8,56 8,57 8,57 9,58 10,58 10,59 11,59 12,59 12,59 13,59 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,37 12,37 12,37 11,37 10,37 10,38 9,38 8,39 8,39 8,40 7,41 7,41 7,42 7,43 7,44 7,44 8,45 8,46 8,46 9,47 10,47 10,48 11,48 12,48 12,48 13,48 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,26 12,26 12,26 11,26 10,26 10,27 9,27 8,28 8,28 8,29 7,30 7,30 7,31 7,32 7,33 7,33 8,34 8,35 8,35 9,36 10,36 10,37 11,37 12,37 12,37 13,37 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="26" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.529576" x1="37" x2="37" y1="75" y2="28"/>
   </symbol>
   <symbol id="lightningRod:shape87">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="29" x2="32" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="54" x2="54" y1="26" y2="16"/>
    <circle cx="54" cy="33" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="44" cy="32" fillStyle="0" r="8.5" stroke-width="1"/>
    <ellipse cx="47" cy="42" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="27" x2="35" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="31" x2="31" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="37" x2="25" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="53" y1="17" y2="17"/>
    <rect height="27" stroke-width="0.416667" width="14" x="0" y="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="84" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="47" y1="84" y2="84"/>
    <rect height="27" stroke-width="0.416667" width="14" x="41" y="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="42" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="28" y1="84" y2="94"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="48" x2="48" y1="84" y2="50"/>
   </symbol>
   <symbol id="lightningRod:shape74">
    <circle cx="39" cy="14" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="19" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="19" y2="19"/>
    <circle cx="30" cy="9" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="30" cy="20" fillStyle="0" r="8.5" stroke-width="1"/>
    <rect height="27" stroke-width="0.416667" width="14" x="0" y="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="71" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="31" y1="71" y2="71"/>
    <rect height="27" stroke-width="0.416667" width="14" x="24" y="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="82" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0587025" x1="31" x2="34" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.173913" x1="30" x2="30" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.108974" x1="30" x2="27" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0587025" x1="31" x2="34" y1="9" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.173913" x1="30" x2="30" y1="7" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.108974" x1="30" x2="27" y1="8" y2="11"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,17 39,15 45,15 43,18 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape53">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="5" x2="5" y1="13" y2="4"/>
    <polyline arcFlag="1" points="5,13 6,13 6,13 7,13 8,13 8,14 9,14 10,15 10,15 10,16 11,17 11,17 11,18 11,19 11,20 11,20 10,21 10,22 10,22 9,23 8,23 8,24 7,24 6,24 6,24 5,24 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="5,24 6,24 6,24 7,24 8,24 8,25 9,25 10,26 10,26 10,27 11,28 11,28 11,29 11,30 11,31 11,31 10,32 10,33 10,33 9,34 8,34 8,35 7,35 6,35 6,35 5,35 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="5,35 6,35 6,35 7,35 8,35 8,36 9,36 10,37 10,37 10,38 11,39 11,39 11,40 11,41 11,42 11,42 10,43 10,44 10,44 9,45 8,45 8,46 7,46 6,46 6,46 5,46 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="5" x2="5" y1="55" y2="46"/>
   </symbol>
   <symbol id="lightningRod:shape81">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="40" y2="27"/>
    <ellipse cx="9" cy="18" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <circle cx="9" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="18" cy="12" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape85">
    <circle cx="8" cy="17" fillStyle="0" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="20" y2="20"/>
    <rect height="27" stroke-width="0.416667" width="14" x="1" y="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="73" y2="25"/>
    <circle cx="8" cy="8" fillStyle="0" r="7.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="55" x2="55" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="54" x2="46" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="59" x2="59" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="62" x2="62" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="19" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="39" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape105">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="13" x2="13" y1="39" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44" x1="13" x2="13" y1="5" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="0" x2="12" y1="26" y2="26"/>
    <polyline points="13,39 15,39 17,38 18,38 20,37 21,36 23,35 24,33 25,31 25,30 26,28 26,26 26,24 25,22 25,21 24,19 23,18 21,16 20,15 18,14 17,14 15,13 13,13 11,13 9,14 8,14 6,15 5,16 3,18 2,19 1,21 1,22 0,24 0,26 " stroke-width="0.0972"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape17_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="37" x2="15" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="38" x2="38" y1="7" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.430622" x1="38" x2="47" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.430622" x1="6" x2="15" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape17_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="37" x2="15" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="38" x2="38" y1="7" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.430622" x1="38" x2="47" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.430622" x1="6" x2="15" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape17-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="37" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="14" x2="14" y1="7" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.430622" x1="14" x2="5" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.430622" x1="46" x2="37" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape17-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="37" x2="15" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="38" x2="38" y1="7" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.430622" x1="38" x2="47" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.430622" x1="6" x2="15" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer:shape16_0">
    <ellipse cx="70" cy="46" fillStyle="0" rx="26.5" ry="26" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="0" x2="71" y1="29" y2="100"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="80" x2="73" y1="47" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="87" x2="80" y1="54" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="80" x2="80" y1="38" y2="47"/>
   </symbol>
   <symbol id="transformer:shape16_1">
    <ellipse cx="41" cy="61" fillStyle="0" rx="26" ry="26.5" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="41" x2="34" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="49" x2="41" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="41" x2="41" y1="62" y2="71"/>
   </symbol>
   <symbol id="transformer:shape16-2">
    <circle cx="41" cy="30" fillStyle="0" r="26.5" stroke-width="0.55102"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="31" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="49" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="31" x2="49" y1="16" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape10_0">
    <circle cx="13" cy="37" fillStyle="0" r="13.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.227273" x1="14" x2="18" y1="39" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.181624" x1="14" x2="9" y1="40" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.217391" x1="14" x2="14" y1="41" y2="46"/>
   </symbol>
   <symbol id="transformer2:shape10_1">
    <circle cx="13" cy="17" fillStyle="0" r="13.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.227273" x1="14" x2="18" y1="15" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.181624" x1="14" x2="9" y1="16" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.217391" x1="14" x2="14" y1="17" y2="22"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c44ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c456b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c45ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c46f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c48200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c48ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c49a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c4a440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c4acb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="55" stroke="rgb(255,0,0)" stroke-width="9.28571" width="98" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c4b690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c4b690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,35)">二种工作</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c4d480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c4d480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2c4e830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c504e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c51060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c51950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c52290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c627e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c63020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2917e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c64280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c65460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c65de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c668d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c6b890" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c6c4f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c68590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c69b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c6a860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2c78bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2c6e950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1337" width="2339" x="3020" y="-1249"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3063" x2="3063" y1="-1202" y2="-2"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3065" x2="3105" y1="-1201" y2="-1201"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3060" x2="3108" y1="-1078" y2="-1078"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3065" x2="3090" y1="-594" y2="-594"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3120" x2="5267" y1="-1212" y2="-1212"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3118" x2="3118" y1="-1240" y2="-1213"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3478" x2="3478" y1="-1237" y2="-1213"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="5268" x2="5268" y1="-1240" y2="-1214"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-20013">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3852.000000 -1027.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3166" ObjectName="SW-CX_BLX.CX_BLX_142BK"/>
     <cge:Meas_Ref ObjectId="20013"/>
    <cge:TPSR_Ref TObjectID="3166"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19926">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4387.000000 -838.402299)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3145" ObjectName="SW-CX_BLX.CX_BLX_112BK"/>
     <cge:Meas_Ref ObjectId="19926"/>
    <cge:TPSR_Ref TObjectID="3145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19874">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3719.000000 -499.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3134" ObjectName="SW-CX_BLX.CX_BLX_001BK"/>
     <cge:Meas_Ref ObjectId="19874"/>
    <cge:TPSR_Ref TObjectID="3134"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20222">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3600.000000 -329.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3181" ObjectName="SW-CX_BLX.CX_BLX_067BK"/>
     <cge:Meas_Ref ObjectId="20222"/>
    <cge:TPSR_Ref TObjectID="3181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19843">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3720.000000 -835.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3130" ObjectName="SW-CX_BLX.CX_BLX_111BK"/>
     <cge:Meas_Ref ObjectId="19843"/>
    <cge:TPSR_Ref TObjectID="3130"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19983">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4308.000000 -1026.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3160" ObjectName="SW-CX_BLX.CX_BLX_141BK"/>
     <cge:Meas_Ref ObjectId="19983"/>
    <cge:TPSR_Ref TObjectID="3160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19956">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4386.000000 -496.402299)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3150" ObjectName="SW-CX_BLX.CX_BLX_002BK"/>
     <cge:Meas_Ref ObjectId="19956"/>
    <cge:TPSR_Ref TObjectID="3150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20104">
    <use class="BV-38KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4743.000000 85.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3178" ObjectName="SW-CX_BLX.CX_BLX_K003BK"/>
     <cge:Meas_Ref ObjectId="20104"/>
    <cge:TPSR_Ref TObjectID="3178"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19533">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4263.000000 -485.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3081" ObjectName="SW-CX_BLX.CX_BLX_012BK"/>
     <cge:Meas_Ref ObjectId="19533"/>
    <cge:TPSR_Ref TObjectID="3081"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19726">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4957.000000 -774.045977)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3117" ObjectName="SW-CX_BLX.CX_BLX_312BK"/>
     <cge:Meas_Ref ObjectId="19726"/>
    <cge:TPSR_Ref TObjectID="3117"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19941">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4761.000000 -918.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3147" ObjectName="SW-CX_BLX.CX_BLX_302BK"/>
     <cge:Meas_Ref ObjectId="19941"/>
    <cge:TPSR_Ref TObjectID="3147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19858">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4759.000000 -677.402299)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3131" ObjectName="SW-CX_BLX.CX_BLX_301BK"/>
     <cge:Meas_Ref ObjectId="19858"/>
    <cge:TPSR_Ref TObjectID="3131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19476">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4000.666667 -327.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3069" ObjectName="SW-CX_BLX.CX_BLX_063BK"/>
     <cge:Meas_Ref ObjectId="19476"/>
    <cge:TPSR_Ref TObjectID="3069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4000.666667 3.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4049.666667 -0.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20367">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4960.000000 -1159.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3194" ObjectName="SW-CX_BLX.CX_BLX_361BK"/>
     <cge:Meas_Ref ObjectId="20367"/>
    <cge:TPSR_Ref TObjectID="3194"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20385">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4959.000000 -1064.333333)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3195" ObjectName="SW-CX_BLX.CX_BLX_362BK"/>
     <cge:Meas_Ref ObjectId="20385"/>
    <cge:TPSR_Ref TObjectID="3195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19748">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4959.000000 -970.666667)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3120" ObjectName="SW-CX_BLX.CX_BLX_363BK"/>
     <cge:Meas_Ref ObjectId="19748"/>
    <cge:TPSR_Ref TObjectID="3120"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19767">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4962.000000 -875.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3123" ObjectName="SW-CX_BLX.CX_BLX_364BK"/>
     <cge:Meas_Ref ObjectId="19767"/>
    <cge:TPSR_Ref TObjectID="3123"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19706">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4960.000000 -691.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3114" ObjectName="SW-CX_BLX.CX_BLX_365BK"/>
     <cge:Meas_Ref ObjectId="19706"/>
    <cge:TPSR_Ref TObjectID="3114"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19686">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4959.000000 -597.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3111" ObjectName="SW-CX_BLX.CX_BLX_366BK"/>
     <cge:Meas_Ref ObjectId="19686"/>
    <cge:TPSR_Ref TObjectID="3111"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19666">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4959.000000 -503.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3107" ObjectName="SW-CX_BLX.CX_BLX_367BK"/>
     <cge:Meas_Ref ObjectId="19666"/>
    <cge:TPSR_Ref TObjectID="3107"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20246">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5190.000000 -328.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3187" ObjectName="SW-CX_BLX.CX_BLX_087BK"/>
     <cge:Meas_Ref ObjectId="20246"/>
    <cge:TPSR_Ref TObjectID="3187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20099">
    <use class="BV-38KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5100.000000 54.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3177" ObjectName="SW-CX_BLX.CX_BLX_K001BK"/>
     <cge:Meas_Ref ObjectId="20099"/>
    <cge:TPSR_Ref TObjectID="3177"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19629">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5019.000000 -326.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3100" ObjectName="SW-CX_BLX.CX_BLX_085BK"/>
     <cge:Meas_Ref ObjectId="19629"/>
    <cge:TPSR_Ref TObjectID="3100"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19647">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4934.000000 -326.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3103" ObjectName="SW-CX_BLX.CX_BLX_086BK"/>
     <cge:Meas_Ref ObjectId="19647"/>
    <cge:TPSR_Ref TObjectID="3103"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19610">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4807.500000 -327.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3096" ObjectName="SW-CX_BLX.CX_BLX_084BK"/>
     <cge:Meas_Ref ObjectId="19610"/>
    <cge:TPSR_Ref TObjectID="3096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19591">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4680.000000 -325.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3092" ObjectName="SW-CX_BLX.CX_BLX_083BK"/>
     <cge:Meas_Ref ObjectId="19591"/>
    <cge:TPSR_Ref TObjectID="3092"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19572">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4552.500000 -328.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3088" ObjectName="SW-CX_BLX.CX_BLX_082BK"/>
     <cge:Meas_Ref ObjectId="19572"/>
    <cge:TPSR_Ref TObjectID="3088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19553">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4425.000000 -326.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3084" ObjectName="SW-CX_BLX.CX_BLX_081BK"/>
     <cge:Meas_Ref ObjectId="19553"/>
    <cge:TPSR_Ref TObjectID="3084"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19514">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4245.000000 -326.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3077" ObjectName="SW-CX_BLX.CX_BLX_066BK"/>
     <cge:Meas_Ref ObjectId="19514"/>
    <cge:TPSR_Ref TObjectID="3077"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20110">
    <use class="BV-38KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4305.000000 67.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3179" ObjectName="SW-CX_BLX.CX_BLX_K002BK"/>
     <cge:Meas_Ref ObjectId="20110"/>
    <cge:TPSR_Ref TObjectID="3179"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19495">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4142.000000 -328.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3073" ObjectName="SW-CX_BLX.CX_BLX_064BK"/>
     <cge:Meas_Ref ObjectId="19495"/>
    <cge:TPSR_Ref TObjectID="3073"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19457">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3859.333333 -328.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3065" ObjectName="SW-CX_BLX.CX_BLX_062BK"/>
     <cge:Meas_Ref ObjectId="19457"/>
    <cge:TPSR_Ref TObjectID="3065"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19438">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3718.000000 -326.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3061" ObjectName="SW-CX_BLX.CX_BLX_061BK"/>
     <cge:Meas_Ref ObjectId="19438"/>
    <cge:TPSR_Ref TObjectID="3061"/></metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_BLX.CX_BLX_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="4702"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3688.000000 -678.000000)" xlink:href="#transformer:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="4704"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3688.000000 -678.000000)" xlink:href="#transformer:shape16_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="4706"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3688.000000 -678.000000)" xlink:href="#transformer:shape16-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="3209" ObjectName="TF-CX_BLX.CX_BLX_1T"/>
    <cge:TPSR_Ref TObjectID="3209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_BLX.CX_BLX_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="4709"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4355.000000 -681.402299)" xlink:href="#transformer:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="4711"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4355.000000 -681.402299)" xlink:href="#transformer:shape16_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="4713"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4355.000000 -681.402299)" xlink:href="#transformer:shape16-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="3210" ObjectName="TF-CX_BLX.CX_BLX_2T"/>
    <cge:TPSR_Ref TObjectID="3210"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_ZX" endPointId="0" endStationName="CX_BLX" flowDrawDirect="1" flowShape="0" id="AC-110kV.zibaixi_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3861,-1167 3861,-1196 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6570" ObjectName="AC-110kV.zibaixi_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3861,-1167 3861,-1196 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" maxValue="0" overHighLimitColor="rgb(255,0,0)" overLimitFlag="1" overLowLimitColor="rgb(0,255,0)" runFlow="1" showPieFlag="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4317,-1166 4317,-1195 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4317,-1166 4317,-1195 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_BLX.CX_BLX_362Ld">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5226.000000 -1065.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3208" ObjectName="EC-CX_BLX.CX_BLX_362Ld"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_BLX.CX_BLX_361Ld">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5229.000000 -1160.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3207" ObjectName="EC-CX_BLX.CX_BLX_361Ld"/>
    <cge:TPSR_Ref TObjectID="3207"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2cad890" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3792.000000 -1004.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_337cdb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3793.000000 -1061.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a2bc80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3793.000000 -1129.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a2afd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4033.000000 -1016.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_337f9c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4327.000000 -814.402299)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33d9960" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4328.000000 -870.402299)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cc11f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3542.000000 -248.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_337ff90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3618.000000 -688.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_334c8c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3940.000000 -1055.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ca8d40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3660.000000 -811.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cc1620" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3661.000000 -867.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29afe10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4248.000000 -1001.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29c83e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4249.000000 -1058.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33d40f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4249.000000 -1126.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b8cd00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4399.000000 -1050.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2969420" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3541.000000 -309.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b48f80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3603.000000 -42.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cb38a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3970.000000 -580.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29bdda0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4737.000000 -847.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29ade60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4734.000000 -604.402299)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a17a10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4293.000000 -699.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_294fd10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5132.000000 -247.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2960910" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5131.000000 -308.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29668d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5193.000000 -43.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_2b56130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3813,-1021 3798,-1021 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3169@1" ObjectIDZND0="g_2cad890@0" Pin0InfoVect0LinkObjId="g_2cad890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20024_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3813,-1021 3798,-1021 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_341a510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3799,-1078 3814,-1078 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_337cdb0@0" ObjectIDZND0="3170@1" Pin0InfoVect0LinkObjId="SW-20025_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_337cdb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3799,-1078 3814,-1078 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29d04c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3799,-1146 3814,-1146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2a2bc80@0" ObjectIDZND0="3171@1" Pin0InfoVect0LinkObjId="SW-20026_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a2bc80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3799,-1146 3814,-1146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33d7dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3850,-1146 3861,-1146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="capacitor" ObjectIDND0="3171@0" ObjectIDZND0="3168@x" ObjectIDZND1="g_2b5ba80@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-20023_0" Pin0InfoVect1LinkObjId="g_2b5ba80_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20026_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3850,-1146 3861,-1146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ac9930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3861,-1146 3861,-1131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="3171@x" ObjectIDND1="g_2b5ba80@0" ObjectIDND2="0@x" ObjectIDZND0="3168@0" Pin0InfoVect0LinkObjId="SW-20023_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-20026_0" Pin1InfoVect1LinkObjId="g_2b5ba80_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3861,-1146 3861,-1131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29b3b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4099,-1033 4091,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="3173@x" ObjectIDND1="g_296d0d0@0" ObjectIDZND0="3174@0" Pin0InfoVect0LinkObjId="SW-20040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-20039_0" Pin1InfoVect1LinkObjId="g_296d0d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4099,-1033 4091,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29b5400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4055,-1033 4039,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3174@1" ObjectIDZND0="g_2a2afd0@0" Pin0InfoVect0LinkObjId="g_2a2afd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20040_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4055,-1033 4039,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34193e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4099,-1033 4099,-1007 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="3174@x" ObjectIDND1="g_296d0d0@0" ObjectIDZND0="3173@0" Pin0InfoVect0LinkObjId="SW-20039_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-20040_0" Pin1InfoVect1LinkObjId="g_296d0d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4099,-1033 4099,-1007 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ccb7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4099,-1047 4099,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_296d0d0@0" ObjectIDZND0="3174@x" ObjectIDZND1="3173@x" Pin0InfoVect0LinkObjId="SW-20040_0" Pin0InfoVect1LinkObjId="SW-20039_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_296d0d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4099,-1047 4099,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3324e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4349,-831 4333,-831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3156@1" ObjectIDZND0="g_337f9c0@0" Pin0InfoVect0LinkObjId="g_337f9c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19967_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4349,-831 4333,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b55000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-887 4350,-887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_33d9960@0" ObjectIDZND0="3157@1" Pin0InfoVect0LinkObjId="SW-19968_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33d9960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-887 4350,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cccdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3609,-384 3609,-364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3182@0" ObjectIDZND0="3181@1" Pin0InfoVect0LinkObjId="SW-20222_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20224_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3609,-384 3609,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cc0f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3564,-265 3548,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3185@1" ObjectIDZND0="g_2cc11f0@0" Pin0InfoVect0LinkObjId="g_2cc11f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20227_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3564,-265 3548,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a1ad50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3861,-971 3861,-951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3167@1" ObjectIDZND0="3460@0" Pin0InfoVect0LinkObjId="g_2cbd650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20022_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3861,-971 3861,-951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29cdc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3609,-420 3609,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3182@1" ObjectIDZND0="3462@0" Pin0InfoVect0LinkObjId="g_332f970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20224_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3609,-420 3609,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2cbd650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4099,-971 4099,-951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3173@1" ObjectIDZND0="3460@0" Pin0InfoVect0LinkObjId="g_2a1ad50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20039_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4099,-971 4099,-951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_296e110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4396,-937 4396,-951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3159@1" ObjectIDZND0="3460@0" Pin0InfoVect0LinkObjId="g_2a1ad50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19970_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4396,-937 4396,-951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_296e370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4396,-781 4396,-769 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="3158@1" ObjectIDZND0="3210@1" Pin0InfoVect0LinkObjId="g_2b3f660_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19969_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4396,-781 4396,-769 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2cabf10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4385,-831 4396,-831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="3156@0" ObjectIDZND0="3145@x" ObjectIDZND1="3158@x" Pin0InfoVect0LinkObjId="SW-19926_0" Pin0InfoVect1LinkObjId="SW-19969_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19967_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4385,-831 4396,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2cac170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4396,-846 4396,-831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="3145@0" ObjectIDZND0="3156@x" ObjectIDZND1="3158@x" Pin0InfoVect0LinkObjId="SW-19967_0" Pin0InfoVect1LinkObjId="SW-19969_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19926_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4396,-846 4396,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_296e6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4396,-831 4396,-817 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="3145@x" ObjectIDND1="3156@x" ObjectIDZND0="3158@0" Pin0InfoVect0LinkObjId="SW-19969_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19926_0" Pin1InfoVect1LinkObjId="SW-19967_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4396,-831 4396,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_296e920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4386,-887 4396,-887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="3157@0" ObjectIDZND0="3145@x" ObjectIDZND1="3159@x" Pin0InfoVect0LinkObjId="SW-19926_0" Pin0InfoVect1LinkObjId="SW-19970_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19968_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4386,-887 4396,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_296cb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4396,-873 4396,-887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="3145@1" ObjectIDZND0="3157@x" ObjectIDZND1="3159@x" Pin0InfoVect0LinkObjId="SW-19968_0" Pin0InfoVect1LinkObjId="SW-19970_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19926_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4396,-873 4396,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_296cd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4396,-887 4396,-901 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="3157@x" ObjectIDND1="3145@x" ObjectIDZND0="3159@0" Pin0InfoVect0LinkObjId="SW-19970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19968_0" Pin1InfoVect1LinkObjId="SW-19926_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4396,-887 4396,-901 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ca8ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3680,-828 3666,-828 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3140@1" ObjectIDZND0="g_2ca8d40@0" Pin0InfoVect0LinkObjId="g_2ca8d40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19882_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3680,-828 3666,-828 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29a3390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3667,-884 3681,-884 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2cc1620@0" ObjectIDZND0="3139@1" Pin0InfoVect0LinkObjId="SW-19881_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cc1620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3667,-884 3681,-884 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3cb5f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-934 3729,-951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3137@1" ObjectIDZND0="3460@0" Pin0InfoVect0LinkObjId="g_2a1ad50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19879_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-934 3729,-951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b510d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-778 3729,-766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="3138@1" ObjectIDZND0="3209@1" Pin0InfoVect0LinkObjId="g_2b51b40_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19880_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-778 3729,-766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b51330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3716,-828 3729,-828 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="3140@0" ObjectIDZND0="3130@x" ObjectIDZND1="3138@x" Pin0InfoVect0LinkObjId="SW-19843_0" Pin0InfoVect1LinkObjId="SW-19880_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19882_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3716,-828 3729,-828 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29b29f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-843 3729,-828 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="3130@0" ObjectIDZND0="3140@x" ObjectIDZND1="3138@x" Pin0InfoVect0LinkObjId="SW-19882_0" Pin0InfoVect1LinkObjId="SW-19880_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19843_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-843 3729,-828 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29b2c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-828 3729,-814 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="3140@x" ObjectIDND1="3130@x" ObjectIDZND0="3138@0" Pin0InfoVect0LinkObjId="SW-19880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19882_0" Pin1InfoVect1LinkObjId="SW-19843_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-828 3729,-814 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29b2eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3717,-884 3729,-884 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="3139@0" ObjectIDZND0="3130@x" ObjectIDZND1="3137@x" Pin0InfoVect0LinkObjId="SW-19843_0" Pin0InfoVect1LinkObjId="SW-19879_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19881_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3717,-884 3729,-884 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ca4cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-870 3729,-884 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="3130@1" ObjectIDZND0="3139@x" ObjectIDZND1="3137@x" Pin0InfoVect0LinkObjId="SW-19881_0" Pin0InfoVect1LinkObjId="SW-19879_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19843_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-870 3729,-884 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2ca4f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3729,-884 3729,-898 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="3139@x" ObjectIDND1="3130@x" ObjectIDZND0="3137@0" Pin0InfoVect0LinkObjId="SW-19879_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19881_0" Pin1InfoVect1LinkObjId="SW-19843_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3729,-884 3729,-898 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b862e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3861,-1035 3861,-1021 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="3166@0" ObjectIDZND0="3169@x" ObjectIDZND1="3167@x" Pin0InfoVect0LinkObjId="SW-20024_0" Pin0InfoVect1LinkObjId="SW-20022_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20013_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3861,-1035 3861,-1021 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b47a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3847,-1021 3861,-1021 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="3169@0" ObjectIDZND0="3166@x" ObjectIDZND1="3167@x" Pin0InfoVect0LinkObjId="SW-20013_0" Pin0InfoVect1LinkObjId="SW-20022_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20024_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3847,-1021 3861,-1021 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b47c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3861,-1021 3861,-1007 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="3166@x" ObjectIDND1="3169@x" ObjectIDZND0="3167@0" Pin0InfoVect0LinkObjId="SW-20022_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-20013_0" Pin1InfoVect1LinkObjId="SW-20024_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3861,-1021 3861,-1007 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b47ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3861,-1093 3861,-1078 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="3168@1" ObjectIDZND0="3166@x" ObjectIDZND1="3170@x" Pin0InfoVect0LinkObjId="SW-20013_0" Pin0InfoVect1LinkObjId="SW-20025_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20023_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3861,-1093 3861,-1078 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2cad050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3861,-1064 3861,-1078 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="3166@1" ObjectIDZND0="3168@x" ObjectIDZND1="3170@x" Pin0InfoVect0LinkObjId="SW-20023_0" Pin0InfoVect1LinkObjId="SW-20025_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20013_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3861,-1064 3861,-1078 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b45d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3861,-1078 3850,-1078 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="3168@x" ObjectIDND1="3166@x" ObjectIDZND0="3170@0" Pin0InfoVect0LinkObjId="SW-20025_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-20023_0" Pin1InfoVect1LinkObjId="SW-20013_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3861,-1078 3850,-1078 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29afbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4269,-1018 4254,-1018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3163@1" ObjectIDZND0="g_29afe10@0" Pin0InfoVect0LinkObjId="g_29afe10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19994_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4269,-1018 4254,-1018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2c70350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4255,-1075 4270,-1075 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_29c83e0@0" ObjectIDZND0="3164@1" Pin0InfoVect0LinkObjId="SW-19995_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29c83e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4255,-1075 4270,-1075 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2959e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4255,-1143 4270,-1143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_33d40f0@0" ObjectIDZND0="3165@1" Pin0InfoVect0LinkObjId="SW-19996_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33d40f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4255,-1143 4270,-1143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_295a0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4306,-1143 4317,-1143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="capacitor" ObjectIDND0="3165@0" ObjectIDZND0="3162@x" ObjectIDZND1="g_2b5c4f0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-19993_0" Pin0InfoVect1LinkObjId="g_2b5c4f0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19996_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4306,-1143 4317,-1143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_295a340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4317,-1143 4317,-1128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="3165@x" ObjectIDND1="g_2b5c4f0@0" ObjectIDND2="0@x" ObjectIDZND0="3162@0" Pin0InfoVect0LinkObjId="SW-19993_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-19996_0" Pin1InfoVect1LinkObjId="g_2b5c4f0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4317,-1143 4317,-1128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b2e540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4317,-968 4317,-951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3161@1" ObjectIDZND0="3460@0" Pin0InfoVect0LinkObjId="g_2a1ad50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19992_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4317,-968 4317,-951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2af62e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4317,-1032 4317,-1018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="3160@0" ObjectIDZND0="3163@x" ObjectIDZND1="3161@x" Pin0InfoVect0LinkObjId="SW-19994_0" Pin0InfoVect1LinkObjId="SW-19992_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19983_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4317,-1032 4317,-1018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2af6540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4303,-1018 4317,-1018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="3163@0" ObjectIDZND0="3160@x" ObjectIDZND1="3161@x" Pin0InfoVect0LinkObjId="SW-19983_0" Pin0InfoVect1LinkObjId="SW-19992_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19994_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4303,-1018 4317,-1018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2af67a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4317,-1018 4317,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="3160@x" ObjectIDND1="3163@x" ObjectIDZND0="3161@0" Pin0InfoVect0LinkObjId="SW-19992_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19983_0" Pin1InfoVect1LinkObjId="SW-19994_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4317,-1018 4317,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2af6a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4317,-1090 4317,-1075 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="3162@1" ObjectIDZND0="3160@x" ObjectIDZND1="3164@x" Pin0InfoVect0LinkObjId="SW-19983_0" Pin0InfoVect1LinkObjId="SW-19995_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19993_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4317,-1090 4317,-1075 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33da350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4317,-1061 4317,-1075 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="3160@1" ObjectIDZND0="3162@x" ObjectIDZND1="3164@x" Pin0InfoVect0LinkObjId="SW-19993_0" Pin0InfoVect1LinkObjId="SW-19995_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19983_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4317,-1061 4317,-1075 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33da5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4317,-1075 4306,-1075 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="3162@x" ObjectIDND1="3160@x" ObjectIDZND0="3164@0" Pin0InfoVect0LinkObjId="SW-19995_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19993_0" Pin1InfoVect1LinkObjId="SW-19983_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4317,-1075 4306,-1075 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b38960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4395,-686 4395,-657 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" ObjectIDND0="3210@2" ObjectIDZND0="g_2cae320@0" Pin0InfoVect0LinkObjId="g_2cae320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_296e370_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4395,-686 4395,-657 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28fdfd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3624,-696 3624,-686 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3141@0" ObjectIDZND0="g_337ff90@0" Pin0InfoVect0LinkObjId="g_337ff90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19883_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3624,-696 3624,-686 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28fe230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3563,-326 3547,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3184@1" ObjectIDZND0="g_2969420@0" Pin0InfoVect0LinkObjId="g_2969420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20226_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3563,-326 3547,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28fe490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3599,-326 3609,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="3184@0" ObjectIDZND0="3181@x" ObjectIDZND1="3183@x" Pin0InfoVect0LinkObjId="SW-20222_0" Pin0InfoVect1LinkObjId="SW-20225_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20226_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3599,-326 3609,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2caf7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3609,-337 3609,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="3181@0" ObjectIDZND0="3184@x" ObjectIDZND1="3183@x" Pin0InfoVect0LinkObjId="SW-20226_0" Pin0InfoVect1LinkObjId="SW-20225_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20222_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3609,-337 3609,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b57260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3609,-326 3609,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="3184@x" ObjectIDND1="3181@x" ObjectIDZND0="3183@1" Pin0InfoVect0LinkObjId="SW-20225_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-20226_0" Pin1InfoVect1LinkObjId="SW-20222_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3609,-326 3609,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b48860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3609,-193 3609,-201 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_31ef5b0@0" ObjectIDZND0="g_292e040@0" Pin0InfoVect0LinkObjId="g_292e040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31ef5b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3609,-193 3609,-201 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b48ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3609,-56 3609,-72 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2b48f80@0" ObjectIDZND0="3186@0" Pin0InfoVect0LinkObjId="SW-20228_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b48f80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3609,-56 3609,-72 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b48d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3609,-108 3609,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3186@1" ObjectIDZND0="g_31ef5b0@1" Pin0InfoVect0LinkObjId="g_31ef5b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20228_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3609,-108 3609,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33e0eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-429 4225,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3462@0" ObjectIDZND0="3082@1" Pin0InfoVect0LinkObjId="SW-19548_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29cdc60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4225,-429 4225,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3378f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4344,-450 4344,-431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3083@1" ObjectIDZND0="3465@0" Pin0InfoVect0LinkObjId="g_33de560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19549_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4344,-450 4344,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3379f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3609,-265 3609,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="3185@x" ObjectIDND1="g_292e040@0" ObjectIDND2="g_33791c0@0" ObjectIDZND0="3183@0" Pin0InfoVect0LinkObjId="SW-20225_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-20227_0" Pin1InfoVect1LinkObjId="g_292e040_0" Pin1InfoVect2LinkObjId="g_33791c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3609,-265 3609,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_333d360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3609,-265 3600,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="3183@x" ObjectIDND1="g_292e040@0" ObjectIDND2="g_33791c0@0" ObjectIDZND0="3185@0" Pin0InfoVect0LinkObjId="SW-20227_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-20225_0" Pin1InfoVect1LinkObjId="g_292e040_0" Pin1InfoVect2LinkObjId="g_33791c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3609,-265 3600,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_333d5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3609,-254 3646,-254 3647,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_292e040@0" ObjectIDND1="3183@x" ObjectIDND2="3185@x" ObjectIDZND0="g_33791c0@0" Pin0InfoVect0LinkObjId="g_33791c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_292e040_0" Pin1InfoVect1LinkObjId="SW-20225_0" Pin1InfoVect2LinkObjId="SW-20227_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3609,-254 3646,-254 3647,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30ddc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3609,-247 3609,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_292e040@1" ObjectIDZND0="3183@x" ObjectIDZND1="3185@x" ObjectIDZND2="g_33791c0@0" Pin0InfoVect0LinkObjId="SW-20225_0" Pin0InfoVect1LinkObjId="SW-20227_0" Pin0InfoVect2LinkObjId="g_33791c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_292e040_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3609,-247 3609,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30ddec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3609,-254 3609,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_292e040@0" ObjectIDND1="g_33791c0@0" ObjectIDZND0="3183@x" ObjectIDZND1="3185@x" Pin0InfoVect0LinkObjId="SW-20225_0" Pin0InfoVect1LinkObjId="SW-20227_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_292e040_0" Pin1InfoVect1LinkObjId="g_33791c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3609,-254 3609,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3339b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4871,-843 4904,-843 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3461@0" ObjectIDZND0="3118@1" Pin0InfoVect0LinkObjId="SW-19743_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b33d20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4871,-843 4904,-843 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29bcb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4871,-1061 4854,-1061 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3461@0" ObjectIDZND0="3198@0" Pin0InfoVect0LinkObjId="SW-20400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b33d20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4871,-1061 4854,-1061 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3343e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4810,-1061 4792,-1061 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3198@1" ObjectIDZND0="g_3344060@0" Pin0InfoVect0LinkObjId="g_3344060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20400_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4810,-1061 4792,-1061 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cb3640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3976,-613 3976,-597 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_2cb2700@1" ObjectIDZND0="g_2cb38a0@0" Pin0InfoVect0LinkObjId="g_2cb38a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cb2700_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3976,-613 3976,-597 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b33d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4855,-928 4871,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3148@1" ObjectIDZND0="3461@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19942_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4855,-928 4871,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b33f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4797,-928 4817,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3147@0" ObjectIDZND0="3148@0" Pin0InfoVect0LinkObjId="SW-19942_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19941_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4797,-928 4817,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29bd680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4746,-928 4770,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="3146@x" ObjectIDND1="3149@x" ObjectIDZND0="3147@1" Pin0InfoVect0LinkObjId="SW-19941_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19939_0" Pin1InfoVect1LinkObjId="SW-19943_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4746,-928 4770,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29bd8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4743,-928 4743,-915 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="3147@x" ObjectIDND1="3149@x" ObjectIDZND0="3146@0" Pin0InfoVect0LinkObjId="SW-19939_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19941_0" Pin1InfoVect1LinkObjId="SW-19943_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4743,-928 4743,-915 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29bdb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4743,-879 4743,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3146@1" ObjectIDZND0="g_29bdda0@0" Pin0InfoVect0LinkObjId="g_29bdda0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19939_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4743,-879 4743,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33de560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4395,-449 4395,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3152@1" ObjectIDZND0="3465@0" Pin0InfoVect0LinkObjId="g_3378f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19960_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4395,-449 4395,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33de7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4395,-485 4395,-504 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3152@0" ObjectIDZND0="3150@0" Pin0InfoVect0LinkObjId="SW-19956_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4395,-485 4395,-504 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33dea20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4795,-687 4815,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3131@0" ObjectIDZND0="3132@0" Pin0InfoVect0LinkObjId="SW-19860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19858_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4795,-687 4815,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33dec80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-687 4872,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3132@1" ObjectIDZND0="3464@0" Pin0InfoVect0LinkObjId="g_2d7ab70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19860_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-687 4872,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33df750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4723,-687 4740,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="3133@1" ObjectIDZND0="3131@x" ObjectIDZND1="3193@x" Pin0InfoVect0LinkObjId="SW-19858_0" Pin0InfoVect1LinkObjId="SW-20329_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19861_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4723,-687 4740,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33df9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4740,-687 4768,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="3133@x" ObjectIDND1="3193@x" ObjectIDZND0="3131@1" Pin0InfoVect0LinkObjId="SW-19858_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19861_0" Pin1InfoVect1LinkObjId="SW-20329_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4740,-687 4768,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33dfc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4740,-621 4740,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_29ade60@0" ObjectIDZND0="3193@1" Pin0InfoVect0LinkObjId="SW-20329_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29ade60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4740,-621 4740,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33dfe70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4740,-672 4740,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="3193@0" ObjectIDZND0="3133@x" ObjectIDZND1="3131@x" Pin0InfoVect0LinkObjId="SW-19861_0" Pin0InfoVect1LinkObjId="SW-19858_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20329_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4740,-672 4740,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3358440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4966,-809 4966,-843 4940,-843 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3117@1" ObjectIDZND0="3118@0" Pin0InfoVect0LinkObjId="SW-19743_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19726_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4966,-809 4966,-843 4940,-843 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ad6150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4717,-928 4746,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="3149@1" ObjectIDZND0="3147@x" ObjectIDZND1="3146@x" Pin0InfoVect0LinkObjId="SW-19941_0" Pin0InfoVect1LinkObjId="SW-19939_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19943_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4717,-928 4746,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b3f660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4687,-687 4660,-687 4660,-728 4448,-728 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="3133@0" ObjectIDZND0="3210@0" Pin0InfoVect0LinkObjId="g_296e370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19861_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4687,-687 4660,-687 4660,-728 4448,-728 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b3f8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3783,-725 4205,-725 4205,-667 4589,-667 4589,-928 4681,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="3209@0" ObjectIDZND0="3149@0" Pin0InfoVect0LinkObjId="SW-19943_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b510d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3783,-725 4205,-725 4205,-667 4589,-667 4589,-928 4681,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b406d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4344,-485 4344,-495 4299,-495 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3083@0" ObjectIDZND0="3081@0" Pin0InfoVect0LinkObjId="SW-19533_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19549_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4344,-485 4344,-495 4299,-495 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b40930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4272,-495 4225,-495 4225,-484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3081@1" ObjectIDZND0="3082@0" Pin0InfoVect0LinkObjId="SW-19548_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19533_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4272,-495 4225,-495 4225,-484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b40b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4435,-722 4435,-677 4151,-677 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="3210@x" ObjectIDZND0="3154@0" Pin0InfoVect0LinkObjId="SW-19962_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_296e370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4435,-722 4435,-677 4151,-677 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b40df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4057,-661 4057,-677 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2cb4430@0" ObjectIDZND0="3154@x" ObjectIDZND1="g_2cb2700@0" ObjectIDZND2="3143@x" Pin0InfoVect0LinkObjId="SW-19962_0" Pin0InfoVect1LinkObjId="g_2cb2700_0" Pin0InfoVect2LinkObjId="SW-19888_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cb4430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4057,-661 4057,-677 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b418e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4109,-677 4057,-677 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="3154@1" ObjectIDZND0="g_2cb4430@0" ObjectIDZND1="g_2cb2700@0" ObjectIDZND2="3143@x" Pin0InfoVect0LinkObjId="g_2cb4430_0" Pin0InfoVect1LinkObjId="g_2cb2700_0" Pin0InfoVect2LinkObjId="SW-19888_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19962_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4109,-677 4057,-677 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b41b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3976,-662 3976,-677 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2cb2700@0" ObjectIDZND0="3143@x" ObjectIDZND1="g_2cb4430@0" ObjectIDZND2="3154@x" Pin0InfoVect0LinkObjId="SW-19888_0" Pin0InfoVect1LinkObjId="g_2cb4430_0" Pin0InfoVect2LinkObjId="SW-19962_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cb2700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3976,-662 3976,-677 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b51b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3901,-677 3769,-677 3769,-718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="3143@1" ObjectIDZND0="3209@x" Pin0InfoVect0LinkObjId="g_2b510d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19888_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3901,-677 3769,-677 3769,-718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b52610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3976,-677 3943,-677 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2cb2700@0" ObjectIDND1="g_2cb4430@0" ObjectIDND2="3154@x" ObjectIDZND0="3143@0" Pin0InfoVect0LinkObjId="SW-19888_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2cb2700_0" Pin1InfoVect1LinkObjId="g_2cb4430_0" Pin1InfoVect2LinkObjId="SW-19962_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3976,-677 3943,-677 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b52870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4057,-677 3976,-677 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2cb4430@0" ObjectIDND1="3154@x" ObjectIDZND0="g_2cb2700@0" ObjectIDZND1="3143@x" Pin0InfoVect0LinkObjId="g_2cb2700_0" Pin0InfoVect1LinkObjId="SW-19888_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2cb4430_0" Pin1InfoVect1LinkObjId="SW-19962_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4057,-677 3976,-677 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_332f970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3728,-450 3728,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3136@1" ObjectIDZND0="3462@0" Pin0InfoVect0LinkObjId="g_29cdc60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19878_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3728,-450 3728,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3330050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3599,-506 3599,-484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_30de120@0" ObjectIDZND0="3175@0" Pin0InfoVect0LinkObjId="SW-20055_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30de120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3599,-506 3599,-484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3330260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3599,-448 3599,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3175@1" ObjectIDZND0="3462@0" Pin0InfoVect0LinkObjId="g_29cdc60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20055_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3599,-448 3599,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3355ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4606,-430 4606,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3465@0" ObjectIDZND0="3176@1" Pin0InfoVect0LinkObjId="SW-20056_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3378f90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4606,-430 4606,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3355f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4606,-484 4606,-502 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3176@0" ObjectIDZND0="g_33304c0@0" Pin0InfoVect0LinkObjId="g_33304c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20056_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4606,-484 4606,-502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2900470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4010,-382 4010,-362 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3070@0" ObjectIDZND0="3069@1" Pin0InfoVect0LinkObjId="SW-19476_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19489_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4010,-382 4010,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2901420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4010,-430 4010,-418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3462@0" ObjectIDZND0="3070@1" Pin0InfoVect0LinkObjId="SW-19489_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29cdc60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4010,-430 4010,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2901680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4010,-335 4010,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="3069@0" ObjectIDZND0="g_29006d0@0" Pin0InfoVect0LinkObjId="g_29006d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19476_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4010,-335 4010,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29018e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4010,-267 4010,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_29006d0@1" ObjectIDZND0="3071@1" Pin0InfoVect0LinkObjId="SW-19490_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29006d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4010,-267 4010,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b2daf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4010,-221 4010,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="3071@0" ObjectIDZND0="3072@x" ObjectIDZND1="g_2ca5b70@0" ObjectIDZND2="g_2ca6d10@0" Pin0InfoVect0LinkObjId="SW-19491_0" Pin0InfoVect1LinkObjId="g_2ca5b70_0" Pin0InfoVect2LinkObjId="g_2ca6d10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4010,-221 4010,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b2dd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4040,-153 4040,-165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="3072@0" Pin0InfoVect0LinkObjId="SW-19491_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4040,-153 4040,-165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ca5910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4040,-201 4040,-210 4010,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="3072@1" ObjectIDZND0="3071@x" ObjectIDZND1="g_2ca5b70@0" ObjectIDZND2="g_2ca6d10@0" Pin0InfoVect0LinkObjId="SW-19490_0" Pin0InfoVect1LinkObjId="g_2ca5b70_0" Pin0InfoVect2LinkObjId="g_2ca6d10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19491_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4040,-201 4040,-210 4010,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ca6850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3980,-139 4010,-139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2ca5b70@0" ObjectIDZND0="3072@x" ObjectIDZND1="3071@x" ObjectIDZND2="g_2ca6d10@0" Pin0InfoVect0LinkObjId="SW-19491_0" Pin0InfoVect1LinkObjId="SW-19490_0" Pin0InfoVect2LinkObjId="g_2ca6d10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ca5b70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3980,-139 4010,-139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ca6ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4010,-139 4010,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2ca5b70@0" ObjectIDND1="g_2ca6d10@0" ObjectIDZND0="3072@x" ObjectIDZND1="3071@x" Pin0InfoVect0LinkObjId="SW-19491_0" Pin0InfoVect1LinkObjId="SW-19490_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ca5b70_0" Pin1InfoVect1LinkObjId="g_2ca6d10_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4010,-139 4010,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ca7a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4010,-139 4010,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="3072@x" ObjectIDND1="3071@x" ObjectIDND2="g_2ca5b70@0" ObjectIDZND0="g_2ca6d10@0" Pin0InfoVect0LinkObjId="g_2ca6d10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-19491_0" Pin1InfoVect1LinkObjId="SW-19490_0" Pin1InfoVect2LinkObjId="g_2ca5b70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4010,-139 4010,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aee3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4010,-73 4010,-65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="breaker" ObjectIDND0="g_2ca6d10@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ca6d10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4010,-73 4010,-65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2aee650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4059,7 4059,-8 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4059,7 4059,-8 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aee8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4059,-35 4059,-65 4010,-65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="0@1" ObjectIDZND0="g_2ca6d10@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2ca6d10_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4059,-35 4059,-65 4010,-65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2aeeb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4010,6 4010,-5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4010,6 4010,-5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aeed70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4010,-32 4010,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="0@1" ObjectIDZND0="g_2ca6d10@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2ca6d10_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4010,-32 4010,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2af3a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4872,-548 4857,-548 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3464@0" ObjectIDZND0="3199@0" Pin0InfoVect0LinkObjId="SW-20403_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33dec80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4872,-548 4857,-548 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2af3c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4813,-548 4795,-548 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3199@1" ObjectIDZND0="g_33b6330@0" Pin0InfoVect0LinkObjId="g_33b6330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20403_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4813,-548 4795,-548 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a28140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4872,-1169 4902,-1169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3461@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b33d20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4872,-1169 4902,-1169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a283a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4938,-1169 4969,-1169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="3194@1" Pin0InfoVect0LinkObjId="SW-20367_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4938,-1169 4969,-1169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a28600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4996,-1169 5024,-1169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3194@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20367_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4996,-1169 5024,-1169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3367c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5083,-1169 5083,-1147 5101,-1147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_2a28860@0" ObjectIDND2="3207@x" ObjectIDZND0="g_2a29550@0" Pin0InfoVect0LinkObjId="g_2a29550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2a28860_0" Pin1InfoVect2LinkObjId="EC-CX_BLX.CX_BLX_361Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5083,-1169 5083,-1147 5101,-1147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33686f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-1169 5083,-1169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="0@0" ObjectIDZND0="g_2a29550@0" ObjectIDZND1="g_2a28860@0" ObjectIDZND2="3207@x" Pin0InfoVect0LinkObjId="g_2a29550_0" Pin0InfoVect1LinkObjId="g_2a28860_0" Pin0InfoVect2LinkObjId="EC-CX_BLX.CX_BLX_361Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5060,-1169 5083,-1169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3368950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5105,-1169 5105,-1192 5128,-1192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="g_2a29550@0" ObjectIDND1="0@x" ObjectIDND2="3207@x" ObjectIDZND0="g_2a28860@0" Pin0InfoVect0LinkObjId="g_2a28860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a29550_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="EC-CX_BLX.CX_BLX_361Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5105,-1169 5105,-1192 5128,-1192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3369440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5083,-1169 5105,-1169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_2a29550@0" ObjectIDND1="0@x" ObjectIDZND0="g_2a28860@0" ObjectIDZND1="3207@x" Pin0InfoVect0LinkObjId="g_2a28860_0" Pin0InfoVect1LinkObjId="EC-CX_BLX.CX_BLX_361Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a29550_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5083,-1169 5105,-1169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33696a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5105,-1169 5202,-1169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_2a28860@0" ObjectIDND1="g_2a29550@0" ObjectIDND2="0@x" ObjectIDZND0="3207@0" Pin0InfoVect0LinkObjId="EC-CX_BLX.CX_BLX_361Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a28860_0" Pin1InfoVect1LinkObjId="g_2a29550_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5105,-1169 5202,-1169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_291a090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4872,-1074 4901,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3461@0" ObjectIDZND0="3196@1" Pin0InfoVect0LinkObjId="SW-20396_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b33d20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4872,-1074 4901,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_291a2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4937,-1074 4968,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3196@0" ObjectIDZND0="3195@1" Pin0InfoVect0LinkObjId="SW-20385_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20396_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4937,-1074 4968,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_291a550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4995,-1074 5023,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3195@0" ObjectIDZND0="3197@1" Pin0InfoVect0LinkObjId="SW-20397_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20385_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4995,-1074 5023,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_291c250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5082,-1074 5082,-1052 5100,-1052 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="3197@x" ObjectIDND1="g_291a7b0@0" ObjectIDND2="3208@x" ObjectIDZND0="g_291b4a0@0" Pin0InfoVect0LinkObjId="g_291b4a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-20397_0" Pin1InfoVect1LinkObjId="g_291a7b0_0" Pin1InfoVect2LinkObjId="EC-CX_BLX.CX_BLX_362Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5082,-1074 5082,-1052 5100,-1052 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_291c4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5059,-1074 5082,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="3197@0" ObjectIDZND0="g_291b4a0@0" ObjectIDZND1="g_291a7b0@0" ObjectIDZND2="3208@x" Pin0InfoVect0LinkObjId="g_291b4a0_0" Pin0InfoVect1LinkObjId="g_291a7b0_0" Pin0InfoVect2LinkObjId="EC-CX_BLX.CX_BLX_362Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20397_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5059,-1074 5082,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_291c710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5104,-1074 5104,-1097 5127,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="3197@x" ObjectIDND1="g_291b4a0@0" ObjectIDND2="3208@x" ObjectIDZND0="g_291a7b0@0" Pin0InfoVect0LinkObjId="g_291a7b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-20397_0" Pin1InfoVect1LinkObjId="g_291b4a0_0" Pin1InfoVect2LinkObjId="EC-CX_BLX.CX_BLX_362Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5104,-1074 5104,-1097 5127,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_291c970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5082,-1074 5104,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="3197@x" ObjectIDND1="g_291b4a0@0" ObjectIDZND0="g_291a7b0@0" ObjectIDZND1="3208@x" Pin0InfoVect0LinkObjId="g_291a7b0_0" Pin0InfoVect1LinkObjId="EC-CX_BLX.CX_BLX_362Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-20397_0" Pin1InfoVect1LinkObjId="g_291b4a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5082,-1074 5104,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_291cbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5104,-1074 5199,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="3197@x" ObjectIDND1="g_291b4a0@0" ObjectIDND2="g_291a7b0@0" ObjectIDZND0="3208@0" Pin0InfoVect0LinkObjId="EC-CX_BLX.CX_BLX_362Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-20397_0" Pin1InfoVect1LinkObjId="g_291b4a0_0" Pin1InfoVect2LinkObjId="g_291a7b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5104,-1074 5199,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3366970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4872,-980 4901,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3461@0" ObjectIDZND0="3121@1" Pin0InfoVect0LinkObjId="SW-19759_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b33d20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4872,-980 4901,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3366bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4937,-980 4968,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3121@0" ObjectIDZND0="3120@1" Pin0InfoVect0LinkObjId="SW-19748_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19759_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4937,-980 4968,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3366e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4995,-980 5023,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3120@0" ObjectIDZND0="3122@1" Pin0InfoVect0LinkObjId="SW-19760_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19748_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4995,-980 5023,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3331340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5082,-980 5082,-958 5100,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="3122@x" ObjectIDND1="g_33349d0@0" ObjectIDZND0="g_3367090@0" Pin0InfoVect0LinkObjId="g_3367090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19760_0" Pin1InfoVect1LinkObjId="g_33349d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5082,-980 5082,-958 5100,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33315a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5059,-980 5082,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="3122@0" ObjectIDZND0="g_3367090@0" ObjectIDZND1="g_33349d0@0" Pin0InfoVect0LinkObjId="g_3367090_0" Pin0InfoVect1LinkObjId="g_33349d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5059,-980 5082,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3331800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5104,-980 5104,-1003 5127,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="3122@x" ObjectIDND1="g_3367090@0" ObjectIDZND0="g_33349d0@0" Pin0InfoVect0LinkObjId="g_33349d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19760_0" Pin1InfoVect1LinkObjId="g_3367090_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5104,-980 5104,-1003 5127,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3331a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5082,-980 5104,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="3122@x" ObjectIDND1="g_3367090@0" ObjectIDZND0="g_33349d0@0" Pin0InfoVect0LinkObjId="g_33349d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19760_0" Pin1InfoVect1LinkObjId="g_3367090_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5082,-980 5104,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3331cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5104,-980 5236,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" ObjectIDND0="3122@x" ObjectIDND1="g_3367090@0" ObjectIDND2="g_33349d0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-19760_0" Pin1InfoVect1LinkObjId="g_3367090_0" Pin1InfoVect2LinkObjId="g_33349d0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5104,-980 5236,-980 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2afdd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4872,-885 4904,-885 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3461@0" ObjectIDZND0="3124@1" Pin0InfoVect0LinkObjId="SW-19779_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b33d20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4872,-885 4904,-885 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2afdf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4940,-885 4971,-885 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3124@0" ObjectIDZND0="3123@1" Pin0InfoVect0LinkObjId="SW-19767_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19779_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4940,-885 4971,-885 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2afe1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4998,-885 5026,-885 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3123@0" ObjectIDZND0="3125@1" Pin0InfoVect0LinkObjId="SW-19780_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19767_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4998,-885 5026,-885 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2952ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5085,-885 5085,-863 5103,-863 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="3125@x" ObjectIDND1="g_2afe440@0" ObjectIDZND0="g_2952240@0" Pin0InfoVect0LinkObjId="g_2952240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19780_0" Pin1InfoVect1LinkObjId="g_2afe440_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5085,-885 5085,-863 5103,-863 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2953250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5062,-885 5085,-885 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="3125@0" ObjectIDZND0="g_2952240@0" ObjectIDZND1="g_2afe440@0" Pin0InfoVect0LinkObjId="g_2952240_0" Pin0InfoVect1LinkObjId="g_2afe440_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19780_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5062,-885 5085,-885 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29534b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5107,-885 5107,-908 5130,-908 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="3125@x" ObjectIDND1="g_2952240@0" ObjectIDZND0="g_2afe440@0" Pin0InfoVect0LinkObjId="g_2afe440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19780_0" Pin1InfoVect1LinkObjId="g_2952240_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5107,-885 5107,-908 5130,-908 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2953710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5085,-885 5107,-885 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="3125@x" ObjectIDND1="g_2952240@0" ObjectIDZND0="g_2afe440@0" Pin0InfoVect0LinkObjId="g_2afe440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19780_0" Pin1InfoVect1LinkObjId="g_2952240_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5085,-885 5107,-885 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2953970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5107,-885 5239,-885 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" ObjectIDND0="3125@x" ObjectIDND1="g_2952240@0" ObjectIDND2="g_2afe440@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-19780_0" Pin1InfoVect1LinkObjId="g_2952240_0" Pin1InfoVect2LinkObjId="g_2afe440_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5107,-885 5239,-885 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a83c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4996,-701 5024,-701 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3114@0" ObjectIDZND0="3116@1" Pin0InfoVect0LinkObjId="SW-19719_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19706_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4996,-701 5024,-701 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b4b190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5083,-701 5083,-679 5101,-679 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="3116@x" ObjectIDND1="g_2a83ef0@0" ObjectIDZND0="g_2b4a3e0@0" Pin0InfoVect0LinkObjId="g_2b4a3e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19719_0" Pin1InfoVect1LinkObjId="g_2a83ef0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5083,-701 5083,-679 5101,-679 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b4b3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-701 5083,-701 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="3116@0" ObjectIDZND0="g_2b4a3e0@0" ObjectIDZND1="g_2a83ef0@0" Pin0InfoVect0LinkObjId="g_2b4a3e0_0" Pin0InfoVect1LinkObjId="g_2a83ef0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19719_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5060,-701 5083,-701 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b4b650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5105,-701 5105,-724 5128,-724 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="3116@x" ObjectIDND1="g_2b4a3e0@0" ObjectIDZND0="g_2a83ef0@0" Pin0InfoVect0LinkObjId="g_2a83ef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19719_0" Pin1InfoVect1LinkObjId="g_2b4a3e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5105,-701 5105,-724 5128,-724 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b4b8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5083,-701 5105,-701 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="3116@x" ObjectIDND1="g_2b4a3e0@0" ObjectIDZND0="g_2a83ef0@0" Pin0InfoVect0LinkObjId="g_2a83ef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19719_0" Pin1InfoVect1LinkObjId="g_2b4a3e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5083,-701 5105,-701 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b4bb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5105,-701 5237,-701 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" ObjectIDND0="3116@x" ObjectIDND1="g_2b4a3e0@0" ObjectIDND2="g_2a83ef0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-19719_0" Pin1InfoVect1LinkObjId="g_2b4a3e0_0" Pin1InfoVect2LinkObjId="g_2a83ef0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5105,-701 5237,-701 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_335d850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4995,-607 5023,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3111@0" ObjectIDZND0="3113@1" Pin0InfoVect0LinkObjId="SW-19699_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19686_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4995,-607 5023,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_335f550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5082,-607 5082,-585 5100,-585 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="3113@x" ObjectIDND1="g_335dab0@0" ObjectIDZND0="g_335e7a0@0" Pin0InfoVect0LinkObjId="g_335e7a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19699_0" Pin1InfoVect1LinkObjId="g_335dab0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5082,-607 5082,-585 5100,-585 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_335f7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5059,-607 5082,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="3113@0" ObjectIDZND0="g_335e7a0@0" ObjectIDZND1="g_335dab0@0" Pin0InfoVect0LinkObjId="g_335e7a0_0" Pin0InfoVect1LinkObjId="g_335dab0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19699_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5059,-607 5082,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_335fa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5104,-607 5104,-630 5127,-630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="3113@x" ObjectIDND1="g_335e7a0@0" ObjectIDZND0="g_335dab0@0" Pin0InfoVect0LinkObjId="g_335dab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19699_0" Pin1InfoVect1LinkObjId="g_335e7a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5104,-607 5104,-630 5127,-630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_335fc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5082,-607 5104,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="3113@x" ObjectIDND1="g_335e7a0@0" ObjectIDZND0="g_335dab0@0" Pin0InfoVect0LinkObjId="g_335dab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19699_0" Pin1InfoVect1LinkObjId="g_335e7a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5082,-607 5104,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_335fed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5104,-607 5236,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" ObjectIDND0="3113@x" ObjectIDND1="g_335e7a0@0" ObjectIDND2="g_335dab0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-19699_0" Pin1InfoVect1LinkObjId="g_335e7a0_0" Pin1InfoVect2LinkObjId="g_335dab0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5104,-607 5236,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2920300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4995,-513 5023,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3107@0" ObjectIDZND0="3109@1" Pin0InfoVect0LinkObjId="SW-19679_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19666_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4995,-513 5023,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2922000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5082,-513 5082,-491 5100,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="3109@x" ObjectIDND1="g_2920560@0" ObjectIDZND0="g_2921250@0" Pin0InfoVect0LinkObjId="g_2921250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19679_0" Pin1InfoVect1LinkObjId="g_2920560_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5082,-513 5082,-491 5100,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2922260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5059,-513 5082,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="3109@0" ObjectIDZND0="g_2921250@0" ObjectIDZND1="g_2920560@0" Pin0InfoVect0LinkObjId="g_2921250_0" Pin0InfoVect1LinkObjId="g_2920560_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19679_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5059,-513 5082,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29224c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5104,-513 5104,-536 5127,-536 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="3109@x" ObjectIDND1="g_2921250@0" ObjectIDZND0="g_2920560@0" Pin0InfoVect0LinkObjId="g_2920560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19679_0" Pin1InfoVect1LinkObjId="g_2921250_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5104,-513 5104,-536 5127,-536 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2922720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5082,-513 5104,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="3109@x" ObjectIDND1="g_2921250@0" ObjectIDZND0="g_2920560@0" Pin0InfoVect0LinkObjId="g_2920560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19679_0" Pin1InfoVect1LinkObjId="g_2921250_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5082,-513 5104,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2922980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5104,-513 5236,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" ObjectIDND0="3109@x" ObjectIDND1="g_2921250@0" ObjectIDND2="g_2920560@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-19679_0" Pin1InfoVect1LinkObjId="g_2921250_0" Pin1InfoVect2LinkObjId="g_2920560_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5104,-513 5236,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_294a4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5199,-383 5199,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3188@0" ObjectIDZND0="3187@1" Pin0InfoVect0LinkObjId="SW-20246_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20248_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5199,-383 5199,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_294fab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5154,-264 5138,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3191@1" ObjectIDZND0="g_294fd10@0" Pin0InfoVect0LinkObjId="g_294fd10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20251_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5154,-264 5138,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2950760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5199,-419 5199,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3188@1" ObjectIDZND0="3465@0" Pin0InfoVect0LinkObjId="g_3378f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20248_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5199,-419 5199,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29509c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5153,-325 5137,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3190@1" ObjectIDZND0="g_2960910@0" Pin0InfoVect0LinkObjId="g_2960910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20250_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5153,-325 5137,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2950c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5189,-325 5199,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="3190@0" ObjectIDZND0="3187@x" ObjectIDZND1="3189@x" Pin0InfoVect0LinkObjId="SW-20246_0" Pin0InfoVect1LinkObjId="SW-20249_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5189,-325 5199,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2961360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5199,-336 5199,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="3187@0" ObjectIDZND0="3190@x" ObjectIDZND1="3189@x" Pin0InfoVect0LinkObjId="SW-20250_0" Pin0InfoVect1LinkObjId="SW-20249_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20246_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5199,-336 5199,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29615c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5199,-325 5199,-309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="3187@x" ObjectIDND1="3190@x" ObjectIDZND0="3189@1" Pin0InfoVect0LinkObjId="SW-20249_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-20246_0" Pin1InfoVect1LinkObjId="SW-20250_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5199,-325 5199,-309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29661b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5199,-192 5199,-200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_29640d0@0" ObjectIDZND0="g_29392d0@0" Pin0InfoVect0LinkObjId="g_29392d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29640d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5199,-192 5199,-200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2966410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5199,-60 5199,-76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_29668d0@0" ObjectIDZND0="3192@0" Pin0InfoVect0LinkObjId="SW-20252_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29668d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5199,-60 5199,-76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2966670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5199,-112 5199,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3192@1" ObjectIDZND0="g_29640d0@1" Pin0InfoVect0LinkObjId="g_29640d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20252_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5199,-112 5199,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2968090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5199,-264 5199,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="3191@x" ObjectIDND1="g_29392d0@0" ObjectIDND2="g_2967320@0" ObjectIDZND0="3189@0" Pin0InfoVect0LinkObjId="SW-20249_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-20251_0" Pin1InfoVect1LinkObjId="g_29392d0_0" Pin1InfoVect2LinkObjId="g_2967320_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5199,-264 5199,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29682f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5199,-264 5190,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="3189@x" ObjectIDND1="g_29392d0@0" ObjectIDND2="g_2967320@0" ObjectIDZND0="3191@0" Pin0InfoVect0LinkObjId="SW-20251_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-20249_0" Pin1InfoVect1LinkObjId="g_29392d0_0" Pin1InfoVect2LinkObjId="g_2967320_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5199,-264 5190,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2968550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5199,-253 5236,-253 5236,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_29392d0@0" ObjectIDND1="3191@x" ObjectIDND2="3189@x" ObjectIDZND0="g_2967320@0" Pin0InfoVect0LinkObjId="g_2967320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29392d0_0" Pin1InfoVect1LinkObjId="SW-20251_0" Pin1InfoVect2LinkObjId="SW-20249_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5199,-253 5236,-253 5236,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bb08e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5199,-246 5199,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_29392d0@1" ObjectIDZND0="3191@x" ObjectIDZND1="3189@x" ObjectIDZND2="g_2967320@0" Pin0InfoVect0LinkObjId="SW-20251_0" Pin0InfoVect1LinkObjId="SW-20249_0" Pin0InfoVect2LinkObjId="g_2967320_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29392d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5199,-246 5199,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bb0b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5199,-253 5199,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_29392d0@0" ObjectIDND1="g_2967320@0" ObjectIDZND0="3191@x" ObjectIDZND1="3189@x" Pin0InfoVect0LinkObjId="SW-20251_0" Pin0InfoVect1LinkObjId="SW-20249_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29392d0_0" Pin1InfoVect1LinkObjId="g_2967320_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5199,-253 5199,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29d4b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5028,-381 5028,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3101@0" ObjectIDZND0="3100@1" Pin0InfoVect0LinkObjId="SW-19629_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19642_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5028,-381 5028,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29d5ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5028,-430 5028,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3465@0" ObjectIDZND0="3101@1" Pin0InfoVect0LinkObjId="SW-19642_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3378f90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5028,-430 5028,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29d5d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5028,-334 5028,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="3100@0" ObjectIDZND0="g_29d4d70@0" Pin0InfoVect0LinkObjId="g_29d4d70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19629_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5028,-334 5028,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29d5f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5028,-266 5028,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_29d4d70@1" ObjectIDZND0="3102@1" Pin0InfoVect0LinkObjId="SW-19643_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29d4d70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5028,-266 5028,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29d61e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5028,-220 5028,-153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3102@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19643_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5028,-220 5028,-153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29dad40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4943,-381 4943,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3104@0" ObjectIDZND0="3103@1" Pin0InfoVect0LinkObjId="SW-19647_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4943,-381 4943,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29dbcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4943,-430 4943,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3465@0" ObjectIDZND0="3104@1" Pin0InfoVect0LinkObjId="SW-19660_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3378f90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4943,-430 4943,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29dbf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4943,-334 4943,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="3103@0" ObjectIDZND0="g_29dafa0@0" Pin0InfoVect0LinkObjId="g_29dafa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19647_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4943,-334 4943,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29dc1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4943,-266 4943,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_29dafa0@1" ObjectIDZND0="3105@1" Pin0InfoVect0LinkObjId="SW-19661_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29dafa0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4943,-266 4943,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c8bce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4943,-220 4943,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3105@0" ObjectIDZND0="3106@x" ObjectIDZND1="g_2c8c1a0@0" Pin0InfoVect0LinkObjId="SW-19662_0" Pin0InfoVect1LinkObjId="g_2c8c1a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19661_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4943,-220 4943,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c8bf40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4973,-200 4973,-209 4943,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3106@1" ObjectIDZND0="3105@x" ObjectIDZND1="g_2c8c1a0@0" Pin0InfoVect0LinkObjId="SW-19661_0" Pin0InfoVect1LinkObjId="g_2c8c1a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19662_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4973,-200 4973,-209 4943,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c8cf10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4943,-138 4943,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_2c8c1a0@0" ObjectIDND1="3106@x" ObjectIDND2="3105@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2c8c1a0_0" Pin1InfoVect1LinkObjId="SW-19662_0" Pin1InfoVect2LinkObjId="SW-19661_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4943,-138 4943,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c8d170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4913,-138 4943,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2c8c1a0@0" ObjectIDZND0="3106@x" ObjectIDZND1="3105@x" Pin0InfoVect0LinkObjId="SW-19662_0" Pin0InfoVect1LinkObjId="SW-19661_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c8c1a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4913,-138 4943,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c8d3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4943,-138 4943,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2c8c1a0@0" ObjectIDZND0="3106@x" ObjectIDZND1="3105@x" Pin0InfoVect0LinkObjId="SW-19662_0" Pin0InfoVect1LinkObjId="SW-19661_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c8c1a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4943,-138 4943,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c97090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4817,-382 4817,-362 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3097@0" ObjectIDZND0="3096@1" Pin0InfoVect0LinkObjId="SW-19610_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19623_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4817,-382 4817,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c98040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4818,-430 4817,-418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3465@0" ObjectIDZND0="3097@1" Pin0InfoVect0LinkObjId="SW-19623_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3378f90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4818,-430 4817,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c982a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4817,-335 4817,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="3096@0" ObjectIDZND0="g_2c972f0@0" Pin0InfoVect0LinkObjId="g_2c972f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4817,-335 4817,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c98500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4817,-267 4817,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2c972f0@1" ObjectIDZND0="3098@1" Pin0InfoVect0LinkObjId="SW-19624_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c972f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4817,-267 4817,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ada800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4817,-221 4817,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3098@0" ObjectIDZND0="3099@x" ObjectIDZND1="g_2adaf00@0" Pin0InfoVect0LinkObjId="SW-19625_0" Pin0InfoVect1LinkObjId="g_2adaf00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19624_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4817,-221 4817,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2adaa40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4848,-153 4847,-165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="3099@0" Pin0InfoVect0LinkObjId="SW-19625_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4848,-153 4847,-165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2adaca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4847,-201 4847,-210 4817,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3099@1" ObjectIDZND0="3098@x" ObjectIDZND1="g_2adaf00@0" Pin0InfoVect0LinkObjId="SW-19624_0" Pin0InfoVect1LinkObjId="g_2adaf00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19625_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4847,-201 4847,-210 4817,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2adbbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4817,-139 4817,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_2adaf00@0" ObjectIDND1="3099@x" ObjectIDND2="3098@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2adaf00_0" Pin1InfoVect1LinkObjId="SW-19625_0" Pin1InfoVect2LinkObjId="SW-19624_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4817,-139 4817,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2adbe10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4787,-139 4817,-139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2adaf00@0" ObjectIDZND0="3099@x" ObjectIDZND1="3098@x" Pin0InfoVect0LinkObjId="SW-19625_0" Pin0InfoVect1LinkObjId="SW-19624_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2adaf00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4787,-139 4817,-139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2adc070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4817,-139 4817,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2adaf00@0" ObjectIDZND0="3099@x" ObjectIDZND1="3098@x" Pin0InfoVect0LinkObjId="SW-19625_0" Pin0InfoVect1LinkObjId="SW-19624_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2adaf00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4817,-139 4817,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ae09f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-380 4689,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3093@0" ObjectIDZND0="3092@1" Pin0InfoVect0LinkObjId="SW-19591_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19604_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-380 4689,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ae19a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-430 4689,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3465@0" ObjectIDZND0="3093@1" Pin0InfoVect0LinkObjId="SW-19604_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3378f90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-430 4689,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ae1c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-333 4689,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="3092@0" ObjectIDZND0="g_2ae0c50@0" Pin0InfoVect0LinkObjId="g_2ae0c50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19591_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-333 4689,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ae1e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-265 4689,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2ae0c50@1" ObjectIDZND0="3094@1" Pin0InfoVect0LinkObjId="SW-19605_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ae0c50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-265 4689,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ae4970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-219 4689,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3094@0" ObjectIDZND0="3095@x" ObjectIDZND1="g_2ae4e30@0" Pin0InfoVect0LinkObjId="SW-19606_0" Pin0InfoVect1LinkObjId="g_2ae4e30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19605_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-219 4689,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ae4bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4719,-199 4719,-208 4689,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3095@1" ObjectIDZND0="3094@x" ObjectIDZND1="g_2ae4e30@0" Pin0InfoVect0LinkObjId="SW-19605_0" Pin0InfoVect1LinkObjId="g_2ae4e30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19606_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4719,-199 4719,-208 4689,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ae5ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-137 4689,-118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_2ae4e30@0" ObjectIDND1="3095@x" ObjectIDND2="3094@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2ae4e30_0" Pin1InfoVect1LinkObjId="SW-19606_0" Pin1InfoVect2LinkObjId="SW-19605_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-137 4689,-118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ae5e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4659,-137 4689,-137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2ae4e30@0" ObjectIDZND0="3095@x" ObjectIDZND1="3094@x" Pin0InfoVect0LinkObjId="SW-19606_0" Pin0InfoVect1LinkObjId="SW-19605_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ae4e30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4659,-137 4689,-137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ae6060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-137 4689,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2ae4e30@0" ObjectIDZND0="3095@x" ObjectIDZND1="3094@x" Pin0InfoVect0LinkObjId="SW-19606_0" Pin0InfoVect1LinkObjId="SW-19605_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ae4e30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-137 4689,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33be9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4562,-383 4562,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3089@0" ObjectIDZND0="3088@1" Pin0InfoVect0LinkObjId="SW-19572_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19585_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4562,-383 4562,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33bf980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4563,-430 4562,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3465@0" ObjectIDZND0="3089@1" Pin0InfoVect0LinkObjId="SW-19585_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3378f90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4563,-430 4562,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33bfbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4562,-336 4562,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="3088@0" ObjectIDZND0="g_33bec30@0" Pin0InfoVect0LinkObjId="g_33bec30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19572_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4562,-336 4562,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33bfe40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4562,-268 4562,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_33bec30@1" ObjectIDZND0="3090@1" Pin0InfoVect0LinkObjId="SW-19586_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33bec30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4562,-268 4562,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33c2950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4562,-222 4562,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3090@0" ObjectIDZND0="3091@x" ObjectIDZND1="g_33c3790@0" Pin0InfoVect0LinkObjId="SW-19587_0" Pin0InfoVect1LinkObjId="g_33c3790_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19586_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4562,-222 4562,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33c2bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4593,-153 4592,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="3091@0" Pin0InfoVect0LinkObjId="SW-19587_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4593,-153 4592,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33c2e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4592,-202 4592,-211 4562,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3091@1" ObjectIDZND0="3090@x" ObjectIDZND1="g_33c3790@0" Pin0InfoVect0LinkObjId="SW-19586_0" Pin0InfoVect1LinkObjId="g_33c3790_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19587_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4592,-202 4592,-211 4562,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33c3070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4562,-140 4562,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="3091@x" ObjectIDND1="3090@x" ObjectIDND2="g_33c3790@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-19587_0" Pin1InfoVect1LinkObjId="SW-19586_0" Pin1InfoVect2LinkObjId="g_33c3790_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4562,-140 4562,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33c32d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4562,-140 4562,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_33c3790@0" ObjectIDZND0="3091@x" ObjectIDZND1="3090@x" Pin0InfoVect0LinkObjId="SW-19587_0" Pin0InfoVect1LinkObjId="SW-19586_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33c3790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4562,-140 4562,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33c3530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4532,-140 4562,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_33c3790@0" ObjectIDZND0="3091@x" ObjectIDZND1="3090@x" Pin0InfoVect0LinkObjId="SW-19587_0" Pin0InfoVect1LinkObjId="SW-19586_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33c3790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4532,-140 4562,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33cb6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4434,-381 4434,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3085@0" ObjectIDZND0="3084@1" Pin0InfoVect0LinkObjId="SW-19553_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19566_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4434,-381 4434,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33cc660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4434,-430 4434,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3465@0" ObjectIDZND0="3085@1" Pin0InfoVect0LinkObjId="SW-19566_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3378f90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4434,-430 4434,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33cc8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4434,-334 4434,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="3084@0" ObjectIDZND0="g_33cb910@0" Pin0InfoVect0LinkObjId="g_33cb910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19553_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4434,-334 4434,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33ccb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4434,-266 4434,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_33cb910@1" ObjectIDZND0="3086@1" Pin0InfoVect0LinkObjId="SW-19567_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33cb910_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4434,-266 4434,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33cf630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4434,-220 4434,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3086@0" ObjectIDZND0="3087@x" ObjectIDZND1="g_33d0210@0" Pin0InfoVect0LinkObjId="SW-19568_0" Pin0InfoVect1LinkObjId="g_33d0210_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19567_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4434,-220 4434,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33cf890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4464,-200 4464,-209 4434,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3087@1" ObjectIDZND0="3086@x" ObjectIDZND1="g_33d0210@0" Pin0InfoVect0LinkObjId="SW-19567_0" Pin0InfoVect1LinkObjId="g_33d0210_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19568_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4464,-200 4464,-209 4434,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33cfaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4434,-138 4434,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="3087@x" ObjectIDND1="3086@x" ObjectIDND2="g_33d0210@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-19568_0" Pin1InfoVect1LinkObjId="SW-19567_0" Pin1InfoVect2LinkObjId="g_33d0210_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4434,-138 4434,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33cfd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4434,-138 4434,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_33d0210@0" ObjectIDZND0="3087@x" ObjectIDZND1="3086@x" Pin0InfoVect0LinkObjId="SW-19568_0" Pin0InfoVect1LinkObjId="SW-19567_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33d0210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4434,-138 4434,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33cffb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4404,-138 4434,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_33d0210@0" ObjectIDZND0="3087@x" ObjectIDZND1="3086@x" Pin0InfoVect0LinkObjId="SW-19568_0" Pin0InfoVect1LinkObjId="SW-19567_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33d0210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4404,-138 4434,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b95170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4254,-381 4254,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3078@0" ObjectIDZND0="3077@1" Pin0InfoVect0LinkObjId="SW-19514_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19527_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4254,-381 4254,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b96120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4254,-430 4254,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3462@0" ObjectIDZND0="3078@1" Pin0InfoVect0LinkObjId="SW-19527_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29cdc60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4254,-430 4254,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b96380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4254,-334 4254,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="3077@0" ObjectIDZND0="g_2b953d0@0" Pin0InfoVect0LinkObjId="g_2b953d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19514_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4254,-334 4254,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b965e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4254,-266 4254,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b953d0@1" ObjectIDZND0="3079@1" Pin0InfoVect0LinkObjId="SW-19528_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b953d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4254,-266 4254,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b990f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4254,-220 4254,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="3079@0" ObjectIDZND0="3080@x" ObjectIDZND1="g_2b99810@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-19529_0" Pin0InfoVect1LinkObjId="g_2b99810_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19528_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4254,-220 4254,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b99350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4284,-153 4284,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="3080@0" Pin0InfoVect0LinkObjId="SW-19529_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4284,-153 4284,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b995b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4284,-200 4284,-209 4254,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="3080@1" ObjectIDZND0="3079@x" ObjectIDZND1="g_2b99810@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-19528_0" Pin0InfoVect1LinkObjId="g_2b99810_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19529_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4284,-200 4284,-209 4254,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b9a580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4224,-138 4254,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2b99810@0" ObjectIDZND0="3080@x" ObjectIDZND1="3079@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-19529_0" Pin0InfoVect1LinkObjId="SW-19528_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b99810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4224,-138 4254,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b9a7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4254,-138 4254,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2b99810@0" ObjectIDND1="0@x" ObjectIDZND0="3080@x" ObjectIDZND1="3079@x" Pin0InfoVect0LinkObjId="SW-19529_0" Pin0InfoVect1LinkObjId="SW-19528_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b99810_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4254,-138 4254,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b9d290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4254,-138 4254,-129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="3080@x" ObjectIDND1="3079@x" ObjectIDND2="g_2b99810@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-19529_0" Pin1InfoVect1LinkObjId="SW-19528_0" Pin1InfoVect2LinkObjId="g_2b99810_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4254,-138 4254,-129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a36630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-383 4151,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3074@0" ObjectIDZND0="3073@1" Pin0InfoVect0LinkObjId="SW-19495_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19508_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-383 4151,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a375e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-430 4151,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3462@0" ObjectIDZND0="3074@1" Pin0InfoVect0LinkObjId="SW-19508_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29cdc60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-430 4151,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a37840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-336 4151,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="3073@0" ObjectIDZND0="g_2a36890@0" Pin0InfoVect0LinkObjId="g_2a36890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19495_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-336 4151,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a37aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-268 4151,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2a36890@1" ObjectIDZND0="3075@1" Pin0InfoVect0LinkObjId="SW-19509_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a36890_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-268 4151,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a3a5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-222 4151,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3075@0" ObjectIDZND0="3076@x" ObjectIDZND1="g_2a3acd0@0" Pin0InfoVect0LinkObjId="SW-19510_0" Pin0InfoVect1LinkObjId="g_2a3acd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19509_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-222 4151,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a3a810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4181,-153 4181,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="3076@0" Pin0InfoVect0LinkObjId="SW-19510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4181,-153 4181,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a3aa70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4181,-202 4181,-211 4151,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3076@1" ObjectIDZND0="3075@x" ObjectIDZND1="g_2a3acd0@0" Pin0InfoVect0LinkObjId="SW-19509_0" Pin0InfoVect1LinkObjId="g_2a3acd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19510_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4181,-202 4181,-211 4151,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a3ba40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-140 4151,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_2a3acd0@0" ObjectIDND1="3076@x" ObjectIDND2="3075@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a3acd0_0" Pin1InfoVect1LinkObjId="SW-19510_0" Pin1InfoVect2LinkObjId="SW-19509_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-140 4151,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a3bca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4121,-140 4151,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2a3acd0@0" ObjectIDZND0="3076@x" ObjectIDZND1="3075@x" Pin0InfoVect0LinkObjId="SW-19510_0" Pin0InfoVect1LinkObjId="SW-19509_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a3acd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4121,-140 4151,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a3bf00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-140 4151,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2a3acd0@0" ObjectIDZND0="3076@x" ObjectIDZND1="3075@x" Pin0InfoVect0LinkObjId="SW-19510_0" Pin0InfoVect1LinkObjId="SW-19509_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a3acd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-140 4151,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2981db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3868,-383 3868,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3066@0" ObjectIDZND0="3065@1" Pin0InfoVect0LinkObjId="SW-19457_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3868,-383 3868,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2982d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3868,-430 3868,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3462@0" ObjectIDZND0="3066@1" Pin0InfoVect0LinkObjId="SW-19470_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29cdc60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3868,-430 3868,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2982fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3868,-336 3868,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="3065@0" ObjectIDZND0="g_2982010@0" Pin0InfoVect0LinkObjId="g_2982010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19457_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3868,-336 3868,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2983220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3868,-268 3868,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2982010@1" ObjectIDZND0="3067@1" Pin0InfoVect0LinkObjId="SW-19471_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2982010_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3868,-268 3868,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2985d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3868,-222 3868,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3067@0" ObjectIDZND0="3068@x" ObjectIDZND1="g_2986450@0" Pin0InfoVect0LinkObjId="SW-19472_0" Pin0InfoVect1LinkObjId="g_2986450_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19471_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3868,-222 3868,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2985f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3898,-153 3898,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="3068@0" Pin0InfoVect0LinkObjId="SW-19472_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3898,-153 3898,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29861f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3898,-202 3898,-211 3868,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3068@1" ObjectIDZND0="3067@x" ObjectIDZND1="g_2986450@0" Pin0InfoVect0LinkObjId="SW-19471_0" Pin0InfoVect1LinkObjId="g_2986450_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19472_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3898,-202 3898,-211 3868,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29871c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3868,-140 3868,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_2986450@0" ObjectIDND1="3068@x" ObjectIDND2="3067@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2986450_0" Pin1InfoVect1LinkObjId="SW-19472_0" Pin1InfoVect2LinkObjId="SW-19471_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3868,-140 3868,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2987420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3838,-140 3868,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2986450@0" ObjectIDZND0="3068@x" ObjectIDZND1="3067@x" Pin0InfoVect0LinkObjId="SW-19472_0" Pin0InfoVect1LinkObjId="SW-19471_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2986450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3838,-140 3868,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2987680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3868,-140 3868,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2986450@0" ObjectIDZND0="3068@x" ObjectIDZND1="3067@x" Pin0InfoVect0LinkObjId="SW-19472_0" Pin0InfoVect1LinkObjId="SW-19471_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2986450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3868,-140 3868,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_298ea90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3727,-381 3727,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3062@0" ObjectIDZND0="3061@1" Pin0InfoVect0LinkObjId="SW-19438_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19451_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3727,-381 3727,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_298fa40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3727,-430 3727,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3462@0" ObjectIDZND0="3062@1" Pin0InfoVect0LinkObjId="SW-19451_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29cdc60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3727,-430 3727,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_298fca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3727,-334 3727,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="3061@0" ObjectIDZND0="g_298ecf0@0" Pin0InfoVect0LinkObjId="g_298ecf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19438_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3727,-334 3727,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_298ff00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3727,-266 3727,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_298ecf0@1" ObjectIDZND0="3063@1" Pin0InfoVect0LinkObjId="SW-19452_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_298ecf0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3727,-266 3727,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29932a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3727,-220 3727,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3063@0" ObjectIDZND0="3064@x" ObjectIDZND1="g_29939c0@0" Pin0InfoVect0LinkObjId="SW-19453_0" Pin0InfoVect1LinkObjId="g_29939c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19452_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3727,-220 3727,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2993500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3757,-153 3757,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="3064@0" Pin0InfoVect0LinkObjId="SW-19453_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3757,-153 3757,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2993760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3757,-200 3757,-209 3727,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3064@1" ObjectIDZND0="3063@x" ObjectIDZND1="g_29939c0@0" Pin0InfoVect0LinkObjId="SW-19452_0" Pin0InfoVect1LinkObjId="g_29939c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19453_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3757,-200 3757,-209 3727,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2994730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3727,-138 3727,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_29939c0@0" ObjectIDND1="3063@x" ObjectIDND2="3064@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29939c0_0" Pin1InfoVect1LinkObjId="SW-19452_0" Pin1InfoVect2LinkObjId="SW-19453_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3727,-138 3727,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d68660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3697,-138 3727,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_29939c0@0" ObjectIDZND0="3063@x" ObjectIDZND1="3064@x" Pin0InfoVect0LinkObjId="SW-19452_0" Pin0InfoVect1LinkObjId="SW-19453_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29939c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3697,-138 3727,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d688c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3727,-138 3727,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_29939c0@0" ObjectIDZND0="3063@x" ObjectIDZND1="3064@x" Pin0InfoVect0LinkObjId="SW-19452_0" Pin0InfoVect1LinkObjId="SW-19453_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29939c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3727,-138 3727,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d6d630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3669,-732 3669,-748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2cc2030@0" ObjectIDZND0="3209@x" ObjectIDZND1="g_2b38bc0@0" ObjectIDZND2="3141@x" Pin0InfoVect0LinkObjId="g_2b510d0_0" Pin0InfoVect1LinkObjId="g_2b38bc0_0" Pin0InfoVect2LinkObjId="SW-19883_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cc2030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3669,-732 3669,-748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d6dfa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3669,-748 3728,-748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="transformer" ObjectIDND0="g_2cc2030@0" ObjectIDND1="g_2b38bc0@0" ObjectIDND2="3141@x" ObjectIDZND0="3209@x" Pin0InfoVect0LinkObjId="g_2b510d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2cc2030_0" Pin1InfoVect1LinkObjId="g_2b38bc0_0" Pin1InfoVect2LinkObjId="SW-19883_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3669,-748 3728,-748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d6e190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3648,-732 3648,-748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer" EndDevType2="switch" ObjectIDND0="g_2b38bc0@0" ObjectIDZND0="g_2cc2030@0" ObjectIDZND1="3209@x" ObjectIDZND2="3141@x" Pin0InfoVect0LinkObjId="g_2cc2030_0" Pin0InfoVect1LinkObjId="g_2b510d0_0" Pin0InfoVect2LinkObjId="SW-19883_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b38bc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3648,-732 3648,-748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d6ebe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3648,-748 3669,-748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="transformer" ObjectIDND0="g_2b38bc0@0" ObjectIDND1="3141@x" ObjectIDZND0="g_2cc2030@0" ObjectIDZND1="3209@x" Pin0InfoVect0LinkObjId="g_2cc2030_0" Pin0InfoVect1LinkObjId="g_2b510d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b38bc0_0" Pin1InfoVect1LinkObjId="SW-19883_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3648,-748 3669,-748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d6ee20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3624,-732 3624,-748 3648,-748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="transformer" ObjectIDND0="3141@1" ObjectIDZND0="g_2b38bc0@0" ObjectIDZND1="g_2cc2030@0" ObjectIDZND2="3209@x" Pin0InfoVect0LinkObjId="g_2b38bc0_0" Pin0InfoVect1LinkObjId="g_2cc2030_0" Pin0InfoVect2LinkObjId="g_2b510d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19883_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3624,-732 3624,-748 3648,-748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d6f080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4323,-739 4323,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer" EndDevType2="switch" ObjectIDND0="g_2a184a0@0" ObjectIDZND0="g_2b5ad40@0" ObjectIDZND1="3210@x" ObjectIDZND2="3155@x" Pin0InfoVect0LinkObjId="g_2b5ad40_0" Pin0InfoVect1LinkObjId="g_296e370_0" Pin0InfoVect2LinkObjId="SW-19966_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a184a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4323,-739 4323,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d6fb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4342,-739 4342,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="transformer" ObjectIDND0="g_2b5ad40@0" ObjectIDZND0="g_2a184a0@0" ObjectIDZND1="3155@x" ObjectIDZND2="3210@x" Pin0InfoVect0LinkObjId="g_2a184a0_0" Pin0InfoVect1LinkObjId="SW-19966_0" Pin0InfoVect2LinkObjId="g_296e370_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b5ad40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4342,-739 4342,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d705f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4323,-752 4342,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="transformer" ObjectIDND0="g_2a184a0@0" ObjectIDND1="3155@x" ObjectIDZND0="g_2b5ad40@0" ObjectIDZND1="3210@x" Pin0InfoVect0LinkObjId="g_2b5ad40_0" Pin0InfoVect1LinkObjId="g_296e370_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a184a0_0" Pin1InfoVect1LinkObjId="SW-19966_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4323,-752 4342,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d70850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4342,-752 4395,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="transformer" ObjectIDND0="g_2b5ad40@0" ObjectIDND1="g_2a184a0@0" ObjectIDND2="3155@x" ObjectIDZND0="3210@x" Pin0InfoVect0LinkObjId="g_296e370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b5ad40_0" Pin1InfoVect1LinkObjId="g_2a184a0_0" Pin1InfoVect2LinkObjId="SW-19966_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4342,-752 4395,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d70ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4973,-164 4973,-153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3106@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19662_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4973,-164 4973,-153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d712c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4719,-163 4719,-153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3095@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19606_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4719,-163 4719,-153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d71a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4464,-164 4464,-153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3087@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19568_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4464,-164 4464,-153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d77720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4395,-531 4395,-549 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3150@1" ObjectIDZND0="3151@1" Pin0InfoVect0LinkObjId="SW-19959_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19956_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4395,-531 4395,-549 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d77980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4395,-585 4395,-604 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3151@0" ObjectIDZND0="g_2cae320@1" Pin0InfoVect0LinkObjId="g_2cae320_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19959_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4395,-585 4395,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d7a980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4969,-701 4941,-701 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3114@1" ObjectIDZND0="3115@1" Pin0InfoVect0LinkObjId="SW-19718_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19706_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4969,-701 4941,-701 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d7ab70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4905,-701 4872,-701 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3115@0" ObjectIDZND0="3464@0" Pin0InfoVect0LinkObjId="g_33dec80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19718_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4905,-701 4872,-701 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d7ad60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4966,-782 4966,-746 4945,-746 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3117@0" ObjectIDZND0="3119@1" Pin0InfoVect0LinkObjId="SW-19744_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19726_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4966,-782 4966,-746 4945,-746 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d7af50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4909,-746 4872,-746 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3119@0" ObjectIDZND0="3464@0" Pin0InfoVect0LinkObjId="g_33dec80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19744_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4909,-746 4872,-746 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d7b180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-607 4941,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3111@1" ObjectIDZND0="3112@1" Pin0InfoVect0LinkObjId="SW-19698_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19686_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-607 4941,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d7b3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4905,-607 4872,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3112@0" ObjectIDZND0="3464@0" Pin0InfoVect0LinkObjId="g_33dec80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19698_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4905,-607 4872,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d7b5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4968,-513 4938,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3107@1" ObjectIDZND0="3108@1" Pin0InfoVect0LinkObjId="SW-19678_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19666_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4968,-513 4938,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d7b810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4902,-513 4872,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3108@0" ObjectIDZND0="3464@0" Pin0InfoVect0LinkObjId="g_33dec80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19678_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4902,-513 4872,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d7ba40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3904,-1169 3904,-1160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="lightningRod" ObjectIDND0="3171@x" ObjectIDND1="3168@x" ObjectIDND2="0@x" ObjectIDZND0="g_2b5ba80@0" Pin0InfoVect0LinkObjId="g_2b5ba80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-20026_0" Pin1InfoVect1LinkObjId="SW-20023_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3904,-1169 3904,-1160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d7c500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3861,-1146 3861,-1169 3904,-1169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="capacitor" ObjectIDND0="3171@x" ObjectIDND1="3168@x" ObjectIDZND0="g_2b5ba80@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2b5ba80_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-20026_0" Pin1InfoVect1LinkObjId="SW-20023_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3861,-1146 3861,-1169 3904,-1169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d7c760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3946,-1161 3946,-1169 3904,-1169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_2b5ba80@0" ObjectIDZND1="3171@x" ObjectIDZND2="3168@x" Pin0InfoVect0LinkObjId="g_2b5ba80_0" Pin0InfoVect1LinkObjId="SW-20026_0" Pin0InfoVect2LinkObjId="SW-20023_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3946,-1161 3946,-1169 3904,-1169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d7c9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3946,-1072 3946,-1086 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_334c8c0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_334c8c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3946,-1072 3946,-1086 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d7cc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3946,-1122 3946,-1133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3946,-1122 3946,-1133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d7ce80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4361,-1166 4361,-1157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="lightningRod" ObjectIDND0="3165@x" ObjectIDND1="3162@x" ObjectIDND2="0@x" ObjectIDZND0="g_2b5c4f0@0" Pin0InfoVect0LinkObjId="g_2b5c4f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-19996_0" Pin1InfoVect1LinkObjId="SW-19993_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4361,-1166 4361,-1157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d7d970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4317,-1143 4317,-1166 4361,-1166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="capacitor" ObjectIDND0="3165@x" ObjectIDND1="3162@x" ObjectIDZND0="g_2b5c4f0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2b5c4f0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-19996_0" Pin1InfoVect1LinkObjId="SW-19993_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4317,-1143 4317,-1166 4361,-1166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d7dbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4405,-1157 4405,-1166 4360,-1166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="3165@x" ObjectIDZND1="3162@x" ObjectIDZND2="g_2b5c4f0@0" Pin0InfoVect0LinkObjId="SW-19996_0" Pin0InfoVect1LinkObjId="SW-19993_0" Pin0InfoVect2LinkObjId="g_2b5c4f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4405,-1157 4405,-1166 4360,-1166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d7de30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4405,-1067 4405,-1081 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2b8cd00@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b8cd00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4405,-1067 4405,-1081 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d7e090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4405,-1117 4405,-1129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4405,-1117 4405,-1129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d7e2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4299,-694 4299,-705 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2a17a10@0" ObjectIDZND0="3155@0" Pin0InfoVect0LinkObjId="SW-19966_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a17a10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4299,-694 4299,-705 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2d7e550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4299,-741 4299,-750 4299,-752 4323,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="transformer" ObjectIDND0="3155@1" ObjectIDZND0="g_2a184a0@0" ObjectIDZND1="g_2b5ad40@0" ObjectIDZND2="3210@x" Pin0InfoVect0LinkObjId="g_2a184a0_0" Pin0InfoVect1LinkObjId="g_2b5ad40_0" Pin0InfoVect2LinkObjId="g_296e370_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19966_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4299,-741 4299,-750 4299,-752 4323,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d7e7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3728,-683 3728,-664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" ObjectIDND0="3209@2" ObjectIDZND0="g_2a30360@0" Pin0InfoVect0LinkObjId="g_2a30360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b510d0_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3728,-683 3728,-664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d7ea20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3728,-611 3728,-591 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2a30360@1" ObjectIDZND0="3135@1" Pin0InfoVect0LinkObjId="SW-19877_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a30360_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3728,-611 3728,-591 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d7ec80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3728,-486 3728,-507 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3136@0" ObjectIDZND0="3134@0" Pin0InfoVect0LinkObjId="SW-19874_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19878_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3728,-486 3728,-507 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d7eee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3728,-534 3728,-555 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3134@1" ObjectIDZND0="3135@0" Pin0InfoVect0LinkObjId="SW-19877_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19874_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3728,-534 3728,-555 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29323e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4254,-58 4254,-82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDZND0="0@x" ObjectIDZND1="g_2b9d4f0@0" ObjectIDZND2="g_2b9f480@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2b9d4f0_0" Pin0InfoVect2LinkObjId="g_2b9f480_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4254,-58 4254,-82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29325d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4254,-82 4254,-93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b9d4f0@0" ObjectIDND1="g_2b9f480@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b9d4f0_0" Pin1InfoVect1LinkObjId="g_2b9f480_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4254,-82 4254,-93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29327c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-82 4281,-72 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_2b9f480@0" ObjectIDZND0="g_2b9d4f0@0" Pin0InfoVect0LinkObjId="g_2b9d4f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2b9f480_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-82 4281,-72 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2933210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4254,-82 4281,-82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDZND0="g_2b9d4f0@0" ObjectIDZND1="g_2b9f480@0" Pin0InfoVect0LinkObjId="g_2b9d4f0_0" Pin0InfoVect1LinkObjId="g_2b9f480_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4254,-82 4281,-82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_2933450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4778,75 5109,75 5109,45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="breaker" ObjectIDND0="3178@0" ObjectIDZND0="3177@0" Pin0InfoVect0LinkObjId="SW-20099_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20104_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4778,75 5109,75 5109,45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_29336b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4752,75 4314,75 4314,58 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="breaker" ObjectIDND0="3178@1" ObjectIDZND0="3179@0" Pin0InfoVect0LinkObjId="SW-20110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-20104_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4752,75 4314,75 4314,58 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_2933910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4314,20 4314,31 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="3179@1" Pin0InfoVect0LinkObjId="SW-20110_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4314,20 4314,31 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2933b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-82 4314,-82 4314,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2b9d4f0@0" ObjectIDND1="0@x" ObjectIDZND0="g_2b9f480@1" Pin0InfoVect0LinkObjId="g_2b9f480_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b9d4f0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-82 4314,-82 4314,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2933dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4314,-39 4314,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2b9f480@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b9f480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4314,-39 4314,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2937790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5109,-92 5109,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2bb3ed0@1" ObjectIDZND0="3110@0" Pin0InfoVect0LinkObjId="SW-19681_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bb3ed0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5109,-92 5109,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2937980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5109,-365 5109,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3110@1" ObjectIDZND0="3465@0" Pin0InfoVect0LinkObjId="g_3378f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-19681_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5109,-365 5109,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3206610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5109,-61 5109,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2bb3ed0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bb3ed0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5109,-61 5109,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_3206800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5109,1 5109,18 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="3177@1" Pin0InfoVect0LinkObjId="SW-20099_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5109,1 5109,18 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="3460" cx="3861" cy="-951" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3460" cx="4099" cy="-951" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3460" cx="3729" cy="-951" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3460" cx="4317" cy="-951" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3460" cx="4396" cy="-951" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3461" cx="4871" cy="-843" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3461" cx="4871" cy="-1061" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3461" cx="4871" cy="-928" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3464" cx="4872" cy="-746" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3464" cx="4872" cy="-687" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3465" cx="4395" cy="-430" fill="rgb(0,72,216)" r="4" stroke="rgb(0,72,216)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3462" cx="3609" cy="-430" fill="rgb(0,72,216)" r="4" stroke="rgb(0,72,216)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3462" cx="3728" cy="-430" fill="rgb(0,72,216)" r="4" stroke="rgb(0,72,216)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3462" cx="3599" cy="-430" fill="rgb(0,72,216)" r="4" stroke="rgb(0,72,216)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3465" cx="4606" cy="-430" fill="rgb(0,72,216)" r="4" stroke="rgb(0,72,216)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3462" cx="4225" cy="-430" fill="rgb(0,72,216)" r="4" stroke="rgb(0,72,216)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3465" cx="4344" cy="-430" fill="rgb(0,72,216)" r="4" stroke="rgb(0,72,216)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3462" cx="4010" cy="-430" fill="rgb(0,72,216)" r="4" stroke="rgb(0,72,216)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4040" cy="-153" fill="rgb(127,127,127)" r="4" stroke="rgb(127,127,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3464" cx="4872" cy="-548" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3461" cx="4872" cy="-1169" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3461" cx="4872" cy="-1074" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3461" cx="4872" cy="-980" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3461" cx="4872" cy="-885" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3464" cx="4872" cy="-701" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3464" cx="4872" cy="-607" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3464" cx="4872" cy="-513" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3465" cx="5199" cy="-430" fill="rgb(0,72,216)" r="4" stroke="rgb(0,72,216)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3465" cx="5028" cy="-430" fill="rgb(0,72,216)" r="4" stroke="rgb(0,72,216)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5028" cy="-153" fill="rgb(127,127,127)" r="4" stroke="rgb(127,127,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3465" cx="4943" cy="-430" fill="rgb(0,72,216)" r="4" stroke="rgb(0,72,216)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3465" cx="4817" cy="-430" fill="rgb(0,72,216)" r="4" stroke="rgb(0,72,216)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4847" cy="-153" fill="rgb(127,127,127)" r="4" stroke="rgb(127,127,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3465" cx="4689" cy="-430" fill="rgb(0,72,216)" r="4" stroke="rgb(0,72,216)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3465" cx="4562" cy="-430" fill="rgb(0,72,216)" r="4" stroke="rgb(0,72,216)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4592" cy="-153" fill="rgb(127,127,127)" r="4" stroke="rgb(127,127,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3465" cx="4434" cy="-430" fill="rgb(0,72,216)" r="4" stroke="rgb(0,72,216)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3462" cx="4254" cy="-430" fill="rgb(0,72,216)" r="4" stroke="rgb(0,72,216)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4284" cy="-153" fill="rgb(127,127,127)" r="4" stroke="rgb(127,127,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3462" cx="4151" cy="-430" fill="rgb(0,72,216)" r="4" stroke="rgb(0,72,216)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4181" cy="-153" fill="rgb(127,127,127)" r="4" stroke="rgb(127,127,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3462" cx="3868" cy="-430" fill="rgb(0,72,216)" r="4" stroke="rgb(0,72,216)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3898" cy="-153" fill="rgb(127,127,127)" r="4" stroke="rgb(127,127,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3462" cx="3727" cy="-430" fill="rgb(0,72,216)" r="4" stroke="rgb(0,72,216)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3757" cy="-153" fill="rgb(127,127,127)" r="4" stroke="rgb(127,127,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4973" cy="-153" fill="rgb(127,127,127)" r="4" stroke="rgb(127,127,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4719" cy="-153" fill="rgb(127,127,127)" r="4" stroke="rgb(127,127,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4464" cy="-153" fill="rgb(127,127,127)" r="4" stroke="rgb(127,127,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="3465" cx="5109" cy="-430" fill="rgb(0,72,216)" r="4" stroke="rgb(0,72,216)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a7e2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3119.000000 -1155.000000) translate(0,17)">加南网标志（288＊90）：白龙新变电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2cd28e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3030.000000 -1139.000000) translate(0,12)">0.1h</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2cd2ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3025.000000 -925.000000) translate(0,12)">0.4h</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2cd3220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3032.000000 -500.000000) translate(0,12)">0.5h</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2cd3460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3167.000000 -1120.000000) translate(0,12)">系统时间（180＊36）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cd43d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -977.000000) translate(0,17)">频率</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cd43d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -977.000000) translate(0,38)">全站有功</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cd43d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -977.000000) translate(0,59)">全站无功</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cd43d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -977.000000) translate(0,80)">并网联络点的电压和交换功率</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b2f970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3118.000000 -589.000000) translate(0,17)">危险点说明</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b2f970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3118.000000 -589.000000) translate(0,38)">联系方式</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2b2fe60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3289.000000 -1231.000000) translate(0,12)">0.3h</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b52ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4340.000000 -1188.000000) translate(0,15)">谢白线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b53500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4079.000000 -1134.000000) translate(0,15)">母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b53780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4654.000000 -1092.000000) translate(0,15)">II</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b53780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4654.000000 -1092.000000) translate(0,33)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b53780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4654.000000 -1092.000000) translate(0,51)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b53780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4654.000000 -1092.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b53780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4654.000000 -1092.000000) translate(0,87)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b539d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4691.000000 -586.000000) translate(0,15)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b539d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4691.000000 -586.000000) translate(0,33)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b539d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4691.000000 -586.000000) translate(0,51)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b539d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4691.000000 -586.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b539d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4691.000000 -586.000000) translate(0,87)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b53c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4554.000000 -594.000000) translate(0,15)">II</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b53c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4554.000000 -594.000000) translate(0,33)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b53c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4554.000000 -594.000000) translate(0,51)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b53c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4554.000000 -594.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b53c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4554.000000 -594.000000) translate(0,87)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b53e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3537.000000 -627.000000) translate(0,15)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b53e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3537.000000 -627.000000) translate(0,33)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b53e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3537.000000 -627.000000) translate(0,51)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b53e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3537.000000 -627.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b53e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3537.000000 -627.000000) translate(0,87)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b54090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5207.000000 -999.000000) translate(0,15)">白田线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a22110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5207.000000 -906.000000) translate(0,15)">备用三</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a22540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5207.000000 -723.000000) translate(0,15)">白大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a229a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5207.000000 -628.000000) translate(0,15)">白子东线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a22de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5207.000000 -535.000000) translate(0,15)">白妥线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a23340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3548.000000 -211.000000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a23340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3548.000000 -211.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a23340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3548.000000 -211.000000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a23340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3548.000000 -211.000000) translate(0,69)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a23340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3548.000000 -211.000000) translate(0,87)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a23340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3548.000000 -211.000000) translate(0,105)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a238c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3753.000000 -143.000000) translate(0,15)">一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a238c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3753.000000 -143.000000) translate(0,33)">五</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a238c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3753.000000 -143.000000) translate(0,51)">三</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a238c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3753.000000 -143.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a24130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3886.000000 -143.000000) translate(0,15)">前</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a24130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3886.000000 -143.000000) translate(0,33)">进</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a24130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3886.000000 -143.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a24680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3988.000000 -136.000000) translate(0,15)">星</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a24680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3988.000000 -136.000000) translate(0,33)">泰</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a24680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3988.000000 -136.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_332e8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4024.000000 -143.000000) translate(0,15)">中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_332e8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4024.000000 -143.000000) translate(0,33)">纤</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_332e8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4024.000000 -143.000000) translate(0,51)">厂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_332e8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4024.000000 -143.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c9b170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3893.000000 13.000000) translate(0,15)">至10kV星泰配电室</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c9bd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4041.000000 9.000000) translate(0,15)">至10kV中纤厂配电室</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9c160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4160.000000 -143.000000) translate(0,12)">楚</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9c160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4160.000000 -143.000000) translate(0,27)">城</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9c160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4160.000000 -143.000000) translate(0,42)">IV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9c160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4160.000000 -143.000000) translate(0,57)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9c160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4160.000000 -143.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9c5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4199.000000 -143.000000) translate(0,12)">楚</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9c5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4199.000000 -143.000000) translate(0,27)">城</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9c5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4199.000000 -143.000000) translate(0,42)">II</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9c5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4199.000000 -143.000000) translate(0,57)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9c5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4199.000000 -143.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9c810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4336.000000 -36.000000) translate(0,12)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9c810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4336.000000 -36.000000) translate(0,27)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9c810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4336.000000 -36.000000) translate(0,42)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9c810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4336.000000 -36.000000) translate(0,57)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9c810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4336.000000 -36.000000) translate(0,72)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9ca50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4445.000000 -143.000000) translate(0,12)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9ca50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4445.000000 -143.000000) translate(0,27)">力</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9ca50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4445.000000 -143.000000) translate(0,42)">公</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9ca50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4445.000000 -143.000000) translate(0,57)">司</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c9ca50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4445.000000 -143.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2c9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4575.000000 -143.000000) translate(0,12)">楚</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2c9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4575.000000 -143.000000) translate(0,27)">城</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2c9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4575.000000 -143.000000) translate(0,42)">V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2c9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4575.000000 -143.000000) translate(0,57)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2c9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4575.000000 -143.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2cc70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4699.000000 -143.000000) translate(0,12)">转</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2cc70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4699.000000 -143.000000) translate(0,27)">播</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2cc70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4699.000000 -143.000000) translate(0,42)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2cc70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4699.000000 -143.000000) translate(0,57)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2d4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4822.000000 -143.000000) translate(0,12)">楚</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2d4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4822.000000 -143.000000) translate(0,27)">城</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2d4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4822.000000 -143.000000) translate(0,42)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2d4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4822.000000 -143.000000) translate(0,57)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2d4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4822.000000 -143.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2d730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4955.000000 -143.000000) translate(0,12)">楚</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2d730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4955.000000 -143.000000) translate(0,27)">城</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2d730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4955.000000 -143.000000) translate(0,42)">III</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2d730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4955.000000 -143.000000) translate(0,57)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2d730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4955.000000 -143.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2d970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5041.000000 -143.000000) translate(0,12)">旁</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2d970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5041.000000 -143.000000) translate(0,27)">路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2de80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5129.000000 -74.000000) translate(0,12)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2de80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5129.000000 -74.000000) translate(0,27)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2de80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5129.000000 -74.000000) translate(0,42)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2de80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5129.000000 -74.000000) translate(0,57)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2de80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5129.000000 -74.000000) translate(0,72)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2e110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5142.000000 -193.000000) translate(0,12)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2e110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5142.000000 -193.000000) translate(0,27)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2e110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5142.000000 -193.000000) translate(0,42)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2e110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5142.000000 -193.000000) translate(0,57)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2e110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5142.000000 -193.000000) translate(0,72)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2e110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5142.000000 -193.000000) translate(0,87)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2e320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3868.000000 -996.000000) translate(0,12)">1421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2e580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3868.000000 -1118.000000) translate(0,12)">1422</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2e7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3813.000000 -1047.000000) translate(0,12)">1721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2ea00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3816.000000 -1104.000000) translate(0,12)">1722</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2ec40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3816.000000 -1172.000000) translate(0,12)">1723</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2ee80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3870.000000 -1056.000000) translate(0,12)">142</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2f0c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4106.000000 -996.000000) translate(0,12)">1600</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2f300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4057.000000 -1055.000000) translate(0,12)">1700</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2f540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4326.000000 -1055.000000) translate(0,12)">141</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2f780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4324.000000 -993.000000) translate(0,12)">1411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b57f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4324.000000 -1115.000000) translate(0,12)">1412</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b5a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4270.000000 -1044.000000) translate(0,12)">1711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b5c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4272.000000 -1101.000000) translate(0,12)">1712</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b5eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4272.000000 -1169.000000) translate(0,12)">1713</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b60f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4815.000000 -1087.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2af4490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4817.000000 -574.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6b2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3738.000000 -864.000000) translate(0,12)">111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6b8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3736.000000 -923.000000) translate(0,12)">1111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6bb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3736.000000 -803.000000) translate(0,12)">1112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6bd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3684.000000 -910.000000) translate(0,12)">1714</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6bfb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3683.000000 -854.000000) translate(0,12)">1715</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6c1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4352.000000 -913.000000) translate(0,12)">1724</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6c430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4351.000000 -857.000000) translate(0,12)">1725</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6c670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4405.000000 -867.000000) translate(0,12)">112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6c8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4403.000000 -926.000000) translate(0,12)">1121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6caf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4403.000000 -806.000000) translate(0,12)">1122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d6cd30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4306.000000 -779.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d6cf70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3647.000000 -784.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6d1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3577.000000 -730.000000) translate(0,12)">1701</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6d3f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4260.000000 -734.000000) translate(0,12)">1702</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d72270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3737.000000 -526.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d728a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3735.000000 -475.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d72ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3606.000000 -473.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d72d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5250.000000 -447.000000) translate(0,15)">10kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d72f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3482.000000 -454.000000) translate(0,15)">10kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d731a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3607.000000 -973.000000) translate(0,15)">110kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d733e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4907.000000 -485.000000) translate(0,15)">35kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d73620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4768.000000 -1197.000000) translate(0,15)">35kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d73860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3903.000000 -706.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d73aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4112.000000 -706.000000) translate(0,12)">3020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d73ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4770.000000 -955.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d73f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4817.000000 -957.000000) translate(0,12)">3022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d74160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4681.000000 -957.000000) translate(0,12)">3026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d743a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4750.000000 -906.000000) translate(0,12)">30260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d745e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4769.000000 -711.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d74820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4817.000000 -713.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d74a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4747.000000 -661.000000) translate(0,12)">30160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d74ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4689.000000 -713.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d74ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4404.000000 -525.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d7a490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3733.000000 -579.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d7f140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4402.000000 -474.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d7f5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4402.000000 -574.000000) translate(0,12)">0026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d7f830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4273.000000 -519.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d7fa70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4232.000000 -473.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d7fcb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4351.000000 -474.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d7fef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4613.000000 -473.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d80130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4970.000000 -1193.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d80370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4969.000000 -1098.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d805b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4903.000000 -1100.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d807f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5024.000000 -1100.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d80a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4969.000000 -1005.000000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d80c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4903.000000 -1005.000000) translate(0,12)">3631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d80eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5024.000000 -1005.000000) translate(0,12)">3636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d810f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4972.000000 -909.000000) translate(0,12)">364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d81330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4906.000000 -911.000000) translate(0,12)">3641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d81570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5028.000000 -911.000000) translate(0,12)">3646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d817b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4975.000000 -803.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d819f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4906.000000 -869.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d81c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4911.000000 -772.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d81e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4970.000000 -725.000000) translate(0,12)">365</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d820b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4907.000000 -727.000000) translate(0,12)">3652</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d822f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5026.000000 -727.000000) translate(0,12)">3656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d82530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4969.000000 -631.000000) translate(0,12)">366</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_292c9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4906.000000 -633.000000) translate(0,12)">3662</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_292cc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5024.000000 -633.000000) translate(0,12)">3666</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_292ce40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4969.000000 -537.000000) translate(0,12)">367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_292d080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4903.000000 -539.000000) translate(0,12)">3672</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_292d2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5024.000000 -539.000000) translate(0,12)">3676</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_292d500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3618.000000 -358.000000) translate(0,12)">067</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_292d740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3616.000000 -409.000000) translate(0,12)">0671</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_292d980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3616.000000 -299.000000) translate(0,12)">0676</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_292dbc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3560.000000 -352.000000) translate(0,12)">06760</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_292de00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3561.000000 -291.000000) translate(0,12)">06767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_292e930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3616.000000 -97.000000) translate(0,12)">06700</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_292ef60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3736.000000 -355.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_292f1a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3734.000000 -406.000000) translate(0,12)">0611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_292f3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3734.000000 -245.000000) translate(0,12)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_292f620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3764.000000 -189.000000) translate(0,12)">0615</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_292f860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3878.000000 -357.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_292faa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3875.000000 -408.000000) translate(0,12)">0621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_292fce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3905.000000 -191.000000) translate(0,12)">0625</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_292ff20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3875.000000 -247.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2930160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4019.000000 -356.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29303a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4017.000000 -407.000000) translate(0,12)">0631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29305e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4047.000000 -190.000000) translate(0,12)">0635</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2930820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4017.000000 -246.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2930a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4160.000000 -357.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2930ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4158.000000 -408.000000) translate(0,12)">0641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2930ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4188.000000 -191.000000) translate(0,12)">0645</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2931120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4158.000000 -247.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2931360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4263.000000 -355.000000) translate(0,12)">066</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29315a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4261.000000 -406.000000) translate(0,12)">0661</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29317e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4291.000000 -189.000000) translate(0,12)">0665</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2931a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4261.000000 -245.000000) translate(0,12)">0666</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2934030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4443.000000 -355.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2934610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4441.000000 -406.000000) translate(0,12)">0812</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2934850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4471.000000 -189.000000) translate(0,12)">0815</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2934a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4441.000000 -245.000000) translate(0,12)">0816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2934cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4571.000000 -357.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2934f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4569.000000 -408.000000) translate(0,12)">0822</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2935150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4599.000000 -191.000000) translate(0,12)">0825</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2935390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4569.000000 -247.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29355d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4698.000000 -354.000000) translate(0,12)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2935810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4696.000000 -405.000000) translate(0,12)">0832</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2935a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4696.000000 -244.000000) translate(0,12)">0836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2935c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4726.000000 -188.000000) translate(0,12)">0835</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2935ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4826.000000 -356.000000) translate(0,12)">084</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2936110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4824.000000 -407.000000) translate(0,12)">0842</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2936350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4854.000000 -190.000000) translate(0,12)">0845</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2936590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4824.000000 -246.000000) translate(0,12)">0846</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29367d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4952.000000 -355.000000) translate(0,12)">086</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2936a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4950.000000 -406.000000) translate(0,12)">0862</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2936c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4980.000000 -189.000000) translate(0,12)">0865</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2936e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4950.000000 -245.000000) translate(0,12)">0866</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29370d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5037.000000 -355.000000) translate(0,12)">085</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2937310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5035.000000 -406.000000) translate(0,12)">0852</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2937550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5035.000000 -245.000000) translate(0,12)">0855</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2937b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5116.000000 -354.000000) translate(0,12)">0882</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2937e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5118.000000 24.000000) translate(0,12)">K001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29380d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4323.000000 37.000000) translate(0,12)">K002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2938310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4749.000000 51.000000) translate(0,12)">K003</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2938550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5208.000000 -357.000000) translate(0,12)">087</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2938790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5206.000000 -408.000000) translate(0,12)">0872</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29389d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5206.000000 -298.000000) translate(0,12)">0876</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2938c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5150.000000 -351.000000) translate(0,12)">08760</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2938e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5151.000000 -290.000000) translate(0,12)">08767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2939090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5206.000000 -101.000000) translate(0,12)">08700</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="50" graphid="g_3203d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3200.000000 -1071.000000) translate(0,40)">白龙新变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32069f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3930.000000 -576.000000) translate(0,15)">35kV1号主变消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3206e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3868.000000 -1196.000000) translate(0,12)">紫白西线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_32084d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5217.000000 -1189.000000) translate(0,12)">备用一线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3208c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5207.000000 -1098.000000) translate(0,12)">备用二线</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(255,0,0)" stroke-width="1" width="360" x="3118" y="-1199"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="1201" stroke="rgb(255,0,0)" stroke-width="1" width="2150" x="3117" y="-1202"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(255,0,0)" stroke-width="1" width="360" x="3117" y="-1080"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(255,0,0)" stroke-width="1" width="360" x="3117" y="-598"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_BLX.CX_BLX_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4872,-1195 4872,-827 " stroke-width="5.99982"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="3461" ObjectName="BS-CX_BLX.CX_BLX_3IM"/>
    <cge:TPSR_Ref TObjectID="3461"/></metadata>
   <polyline fill="none" opacity="0" points="4872,-1195 4872,-827 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_BLX.CX_BLX_1IM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3608,-951 4553,-951 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="3460" ObjectName="BS-CX_BLX.CX_BLX_1IM"/>
    <cge:TPSR_Ref TObjectID="3460"/></metadata>
   <polyline fill="none" opacity="0" points="3608,-951 4553,-951 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_BLX.CX_BLX_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3482,-430 4275,-430 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="3462" ObjectName="BS-CX_BLX.CX_BLX_9IM"/>
    <cge:TPSR_Ref TObjectID="3462"/></metadata>
   <polyline fill="none" opacity="0" points="3482,-430 4275,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_BLX.CX_BLX_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4323,-430 5244,-430 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="3465" ObjectName="BS-CX_BLX.CX_BLX_9IIM"/>
    <cge:TPSR_Ref TObjectID="3465"/></metadata>
   <polyline fill="none" opacity="0" points="4323,-430 5244,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3679,-153 5057,-153 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3679,-153 5057,-153 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_BLX.CX_BLX_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4872,-765 4872,-453 " stroke-width="6.00003"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="3464" ObjectName="BS-CX_BLX.CX_BLX_3IIM"/>
    <cge:TPSR_Ref TObjectID="3464"/></metadata>
   <polyline fill="none" opacity="0" points="4872,-765 4872,-453 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5095.000000 6.000000)" xlink:href="#transformer2:shape10_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5095.000000 6.000000)" xlink:href="#transformer2:shape10_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4300.000000 25.000000)" xlink:href="#transformer2:shape10_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4300.000000 25.000000)" xlink:href="#transformer2:shape10_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.400000 3230.000000 -1009.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-21586" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3957.000000 -767.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21586" ObjectName="CX_BLX:CX_BLX_1T_TMP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3953.000000 -752.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-21587" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4631.000000 -765.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21587" ObjectName="CX_BLX:CX_BLX_2T_TMP1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4627.000000 -751.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2947380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3808.000000 1244.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2947610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3797.000000 1229.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2947850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3822.000000 1214.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f0c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4264.000000 1244.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f0ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4253.000000 1229.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f1100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4278.000000 1214.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f1520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5241.000000 1165.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f17e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5230.000000 1150.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f1a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5255.000000 1135.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f1e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5241.000000 1070.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f2100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5230.000000 1055.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f2340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5255.000000 1040.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f2760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5241.000000 976.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f2a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5230.000000 961.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f2c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5255.000000 946.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f3080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5241.000000 881.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f3340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5230.000000 866.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f3580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5255.000000 851.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f39a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5022.000000 815.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f3c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5011.000000 800.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f3ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5036.000000 785.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f42c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5241.000000 697.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f4580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5230.000000 682.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f47c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5255.000000 667.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f4be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5241.000000 603.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f4ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5230.000000 588.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f50e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5255.000000 573.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f5500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5241.000000 509.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f57c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5230.000000 494.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f5a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5255.000000 479.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f5e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4724.000000 760.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f60e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4713.000000 745.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f6320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4738.000000 730.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f6740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4724.000000 1002.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f6a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4713.000000 987.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f6c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4738.000000 972.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f7060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4442.000000 875.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f7320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4431.000000 860.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f7560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4456.000000 845.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f7980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3777.000000 875.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f7c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3766.000000 860.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f7e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3791.000000 845.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f82a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3775.000000 541.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f8560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3764.000000 526.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f87a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3789.000000 511.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f8bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3685.000000 71.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f8e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3674.000000 56.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f90c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3699.000000 41.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f94e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3810.000000 71.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f97a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3799.000000 56.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f99e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3824.000000 41.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31f9e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3957.000000 -34.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fa0c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3946.000000 -49.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fa300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3971.000000 -64.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fa720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4094.000000 66.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fa9e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4083.000000 51.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fac20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4108.000000 36.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fb040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4186.000000 -34.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fb300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4175.000000 -49.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fb540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4200.000000 -64.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fb960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4231.000000 568.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fbc20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4220.000000 553.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fbe60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4245.000000 538.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fc280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4441.000000 542.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fc540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4430.000000 527.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fc780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4455.000000 512.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fcba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4375.000000 66.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fce60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4364.000000 51.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fd0a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4389.000000 36.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fd4c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4497.000000 66.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fd780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4486.000000 51.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fd9c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4511.000000 36.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fdde0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4619.000000 66.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fe0a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4608.000000 51.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fe2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4633.000000 36.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fe700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4742.000000 66.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fe9c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4731.000000 51.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31fec00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4756.000000 36.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ff020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4865.000000 66.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ff2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4854.000000 51.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ff520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4879.000000 36.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ff940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4987.000000 66.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ffc00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4976.000000 51.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ffe40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5001.000000 36.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3200260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3543.000000 39.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3200560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3568.000000 24.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3200980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5151.000000 39.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3200c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5176.000000 24.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32010a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3818.000000 783.000000) translate(0,12)">档位（档）：</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32013a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3818.000000 768.000000) translate(0,12)">油温（℃）：</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32015e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3788.000000 753.000000) translate(0,12)">绕组温度（℃）：</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3201d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4496.000000 779.000000) translate(0,12)">档位（档）：</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32020b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4496.000000 764.000000) translate(0,12)">油温（℃）：</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32022f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4466.000000 749.000000) translate(0,12)">绕组温度（℃）：</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3202710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3565.000000 946.000000) translate(0,12)">Uab（kV）：</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3202bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4706.000000 1176.000000) translate(0,12)">Uab（kV）：</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32030d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4878.000000 464.000000) translate(0,12)">Uab（kV）：</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32035b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5212.000000 424.000000) translate(0,12)">Uab（kV）：</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3203a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3448.000000 423.000000) translate(0,12)">Uab（kV）：</text>
    </g>
   <metadata/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3936.000000 -1128.000000)" xlink:href="#capacitor:shape13"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4395.000000 -1124.000000)" xlink:href="#capacitor:shape13"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-21442" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3863.000000 -1244.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21442" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3166"/>
     <cge:Term_Ref ObjectID="4624"/>
    <cge:TPSR_Ref TObjectID="3166"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-21443" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3863.000000 -1244.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21443" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3166"/>
     <cge:Term_Ref ObjectID="4624"/>
    <cge:TPSR_Ref TObjectID="3166"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-21439" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3863.000000 -1244.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21439" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3166"/>
     <cge:Term_Ref ObjectID="4624"/>
    <cge:TPSR_Ref TObjectID="3166"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-21664" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4501.000000 -875.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21664" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3145"/>
     <cge:Term_Ref ObjectID="4582"/>
    <cge:TPSR_Ref TObjectID="3145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-21665" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4501.000000 -875.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21665" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3145"/>
     <cge:Term_Ref ObjectID="4582"/>
    <cge:TPSR_Ref TObjectID="3145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-21661" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4501.000000 -875.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21661" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3145"/>
     <cge:Term_Ref ObjectID="4582"/>
    <cge:TPSR_Ref TObjectID="3145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-21657" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3831.000000 -541.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21657" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3134"/>
     <cge:Term_Ref ObjectID="4560"/>
    <cge:TPSR_Ref TObjectID="3134"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-21658" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3831.000000 -541.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21658" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3134"/>
     <cge:Term_Ref ObjectID="4560"/>
    <cge:TPSR_Ref TObjectID="3134"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-21654" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3831.000000 -541.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21654" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3134"/>
     <cge:Term_Ref ObjectID="4560"/>
    <cge:TPSR_Ref TObjectID="3134"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-21644" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3835.000000 -875.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21644" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3130"/>
     <cge:Term_Ref ObjectID="4552"/>
    <cge:TPSR_Ref TObjectID="3130"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-21645" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3835.000000 -875.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21645" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3130"/>
     <cge:Term_Ref ObjectID="4552"/>
    <cge:TPSR_Ref TObjectID="3130"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-21641" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3835.000000 -875.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21641" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3130"/>
     <cge:Term_Ref ObjectID="4552"/>
    <cge:TPSR_Ref TObjectID="3130"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-21436" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4319.000000 -1244.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21436" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3160"/>
     <cge:Term_Ref ObjectID="4612"/>
    <cge:TPSR_Ref TObjectID="3160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-21437" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4319.000000 -1244.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21437" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3160"/>
     <cge:Term_Ref ObjectID="4612"/>
    <cge:TPSR_Ref TObjectID="3160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-21433" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4319.000000 -1244.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21433" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3160"/>
     <cge:Term_Ref ObjectID="4612"/>
    <cge:TPSR_Ref TObjectID="3160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-21677" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4496.000000 -542.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21677" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3150"/>
     <cge:Term_Ref ObjectID="4592"/>
    <cge:TPSR_Ref TObjectID="3150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-21678" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4496.000000 -542.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21678" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3150"/>
     <cge:Term_Ref ObjectID="4592"/>
    <cge:TPSR_Ref TObjectID="3150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-21674" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4496.000000 -542.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21674" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3150"/>
     <cge:Term_Ref ObjectID="4592"/>
    <cge:TPSR_Ref TObjectID="3150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-21523" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4289.000000 -569.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21523" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3081"/>
     <cge:Term_Ref ObjectID="4454"/>
    <cge:TPSR_Ref TObjectID="3081"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-21524" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4289.000000 -569.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21524" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3081"/>
     <cge:Term_Ref ObjectID="4454"/>
    <cge:TPSR_Ref TObjectID="3081"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-21522" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4289.000000 -569.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21522" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3081"/>
     <cge:Term_Ref ObjectID="4454"/>
    <cge:TPSR_Ref TObjectID="3081"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-21481" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5080.000000 -815.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21481" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3117"/>
     <cge:Term_Ref ObjectID="4526"/>
    <cge:TPSR_Ref TObjectID="3117"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-21482" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5080.000000 -815.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21482" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3117"/>
     <cge:Term_Ref ObjectID="4526"/>
    <cge:TPSR_Ref TObjectID="3117"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-21479" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5080.000000 -815.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21479" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3117"/>
     <cge:Term_Ref ObjectID="4526"/>
    <cge:TPSR_Ref TObjectID="3117"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-21670" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4782.000000 -1002.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21670" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3147"/>
     <cge:Term_Ref ObjectID="4586"/>
    <cge:TPSR_Ref TObjectID="3147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-21671" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4782.000000 -1002.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21671" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3147"/>
     <cge:Term_Ref ObjectID="4586"/>
    <cge:TPSR_Ref TObjectID="3147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-21667" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4782.000000 -1002.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21667" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3147"/>
     <cge:Term_Ref ObjectID="4586"/>
    <cge:TPSR_Ref TObjectID="3147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-21650" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4782.000000 -760.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21650" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3131"/>
     <cge:Term_Ref ObjectID="4554"/>
    <cge:TPSR_Ref TObjectID="3131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-21651" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4782.000000 -760.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21651" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3131"/>
     <cge:Term_Ref ObjectID="4554"/>
    <cge:TPSR_Ref TObjectID="3131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-21647" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4782.000000 -760.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21647" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3131"/>
     <cge:Term_Ref ObjectID="4554"/>
    <cge:TPSR_Ref TObjectID="3131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-21514" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4018.000000 34.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21514" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3069"/>
     <cge:Term_Ref ObjectID="4430"/>
    <cge:TPSR_Ref TObjectID="3069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-21515" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4018.000000 34.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21515" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3069"/>
     <cge:Term_Ref ObjectID="4430"/>
    <cge:TPSR_Ref TObjectID="3069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-21513" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4018.000000 34.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21513" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3069"/>
     <cge:Term_Ref ObjectID="4430"/>
    <cge:TPSR_Ref TObjectID="3069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-21611" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5299.000000 -1165.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21611" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3194"/>
     <cge:Term_Ref ObjectID="4680"/>
    <cge:TPSR_Ref TObjectID="3194"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-21612" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5299.000000 -1165.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21612" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3194"/>
     <cge:Term_Ref ObjectID="4680"/>
    <cge:TPSR_Ref TObjectID="3194"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-21609" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5299.000000 -1165.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21609" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3194"/>
     <cge:Term_Ref ObjectID="4680"/>
    <cge:TPSR_Ref TObjectID="3194"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="1" id="ME-21615" prefix="P  " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5298.000000 -1070.000000) translate(0,12)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21615" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3195"/>
     <cge:Term_Ref ObjectID="4682"/>
    <cge:TPSR_Ref TObjectID="3195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="1" id="ME-21616" prefix="Q " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5298.000000 -1070.000000) translate(0,27)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21616" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3195"/>
     <cge:Term_Ref ObjectID="4682"/>
    <cge:TPSR_Ref TObjectID="3195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-21613" prefix="Ia  " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5298.000000 -1070.000000) translate(0,42)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21613" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3195"/>
     <cge:Term_Ref ObjectID="4682"/>
    <cge:TPSR_Ref TObjectID="3195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-21485" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5297.000000 -976.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21485" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3120"/>
     <cge:Term_Ref ObjectID="4532"/>
    <cge:TPSR_Ref TObjectID="3120"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-21486" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5297.000000 -976.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21486" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3120"/>
     <cge:Term_Ref ObjectID="4532"/>
    <cge:TPSR_Ref TObjectID="3120"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-21483" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5297.000000 -976.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21483" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3120"/>
     <cge:Term_Ref ObjectID="4532"/>
    <cge:TPSR_Ref TObjectID="3120"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-21489" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5297.000000 -881.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21489" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3123"/>
     <cge:Term_Ref ObjectID="4538"/>
    <cge:TPSR_Ref TObjectID="3123"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-21490" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5297.000000 -881.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21490" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3123"/>
     <cge:Term_Ref ObjectID="4538"/>
    <cge:TPSR_Ref TObjectID="3123"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-21487" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5297.000000 -881.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21487" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3123"/>
     <cge:Term_Ref ObjectID="4538"/>
    <cge:TPSR_Ref TObjectID="3123"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-21505" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5297.000000 -697.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21505" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3114"/>
     <cge:Term_Ref ObjectID="4520"/>
    <cge:TPSR_Ref TObjectID="3114"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-21506" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5297.000000 -697.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21506" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3114"/>
     <cge:Term_Ref ObjectID="4520"/>
    <cge:TPSR_Ref TObjectID="3114"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-21503" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5297.000000 -697.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21503" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3114"/>
     <cge:Term_Ref ObjectID="4520"/>
    <cge:TPSR_Ref TObjectID="3114"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-21497" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5297.000000 -601.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21497" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3111"/>
     <cge:Term_Ref ObjectID="4514"/>
    <cge:TPSR_Ref TObjectID="3111"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-21498" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5297.000000 -601.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21498" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3111"/>
     <cge:Term_Ref ObjectID="4514"/>
    <cge:TPSR_Ref TObjectID="3111"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-21495" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5297.000000 -601.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21495" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3111"/>
     <cge:Term_Ref ObjectID="4514"/>
    <cge:TPSR_Ref TObjectID="3111"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-21493" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5297.000000 -509.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21493" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3107"/>
     <cge:Term_Ref ObjectID="4506"/>
    <cge:TPSR_Ref TObjectID="3107"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-21494" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5297.000000 -509.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21494" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3107"/>
     <cge:Term_Ref ObjectID="4506"/>
    <cge:TPSR_Ref TObjectID="3107"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-21491" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5297.000000 -509.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21491" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3107"/>
     <cge:Term_Ref ObjectID="4506"/>
    <cge:TPSR_Ref TObjectID="3107"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-21538" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5042.000000 -66.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21538" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3100"/>
     <cge:Term_Ref ObjectID="4492"/>
    <cge:TPSR_Ref TObjectID="3100"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-21539" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5042.000000 -66.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21539" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3100"/>
     <cge:Term_Ref ObjectID="4492"/>
    <cge:TPSR_Ref TObjectID="3100"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-21537" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5042.000000 -66.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21537" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3100"/>
     <cge:Term_Ref ObjectID="4492"/>
    <cge:TPSR_Ref TObjectID="3100"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-21541" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4920.000000 -66.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21541" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3103"/>
     <cge:Term_Ref ObjectID="4498"/>
    <cge:TPSR_Ref TObjectID="3103"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-21542" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4920.000000 -66.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21542" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3103"/>
     <cge:Term_Ref ObjectID="4498"/>
    <cge:TPSR_Ref TObjectID="3103"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-21540" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4920.000000 -66.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21540" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3103"/>
     <cge:Term_Ref ObjectID="4498"/>
    <cge:TPSR_Ref TObjectID="3103"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-21535" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4798.000000 -66.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21535" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3096"/>
     <cge:Term_Ref ObjectID="4484"/>
    <cge:TPSR_Ref TObjectID="3096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-21536" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4798.000000 -66.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21536" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3096"/>
     <cge:Term_Ref ObjectID="4484"/>
    <cge:TPSR_Ref TObjectID="3096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-21534" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4798.000000 -66.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21534" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3096"/>
     <cge:Term_Ref ObjectID="4484"/>
    <cge:TPSR_Ref TObjectID="3096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-21532" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4675.000000 -66.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21532" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3092"/>
     <cge:Term_Ref ObjectID="4476"/>
    <cge:TPSR_Ref TObjectID="3092"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-21533" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4675.000000 -66.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21533" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3092"/>
     <cge:Term_Ref ObjectID="4476"/>
    <cge:TPSR_Ref TObjectID="3092"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-21531" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4675.000000 -66.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21531" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3092"/>
     <cge:Term_Ref ObjectID="4476"/>
    <cge:TPSR_Ref TObjectID="3092"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-21529" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4554.000000 -66.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21529" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3088"/>
     <cge:Term_Ref ObjectID="4468"/>
    <cge:TPSR_Ref TObjectID="3088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-21530" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4554.000000 -66.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21530" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3088"/>
     <cge:Term_Ref ObjectID="4468"/>
    <cge:TPSR_Ref TObjectID="3088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-21528" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4554.000000 -66.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21528" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3088"/>
     <cge:Term_Ref ObjectID="4468"/>
    <cge:TPSR_Ref TObjectID="3088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-21526" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4432.000000 -66.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21526" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3084"/>
     <cge:Term_Ref ObjectID="4460"/>
    <cge:TPSR_Ref TObjectID="3084"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-21527" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4432.000000 -66.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21527" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3084"/>
     <cge:Term_Ref ObjectID="4460"/>
    <cge:TPSR_Ref TObjectID="3084"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-21525" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4432.000000 -66.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21525" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3084"/>
     <cge:Term_Ref ObjectID="4460"/>
    <cge:TPSR_Ref TObjectID="3084"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-21520" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4247.000000 34.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21520" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3077"/>
     <cge:Term_Ref ObjectID="4446"/>
    <cge:TPSR_Ref TObjectID="3077"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-21521" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4247.000000 34.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21521" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3077"/>
     <cge:Term_Ref ObjectID="4446"/>
    <cge:TPSR_Ref TObjectID="3077"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-21519" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4247.000000 34.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21519" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3077"/>
     <cge:Term_Ref ObjectID="4446"/>
    <cge:TPSR_Ref TObjectID="3077"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-21517" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4153.000000 -66.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21517" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3073"/>
     <cge:Term_Ref ObjectID="4438"/>
    <cge:TPSR_Ref TObjectID="3073"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-21518" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4153.000000 -66.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21518" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3073"/>
     <cge:Term_Ref ObjectID="4438"/>
    <cge:TPSR_Ref TObjectID="3073"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-21516" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4153.000000 -66.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21516" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3073"/>
     <cge:Term_Ref ObjectID="4438"/>
    <cge:TPSR_Ref TObjectID="3073"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-21511" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3868.000000 -71.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21511" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3065"/>
     <cge:Term_Ref ObjectID="4422"/>
    <cge:TPSR_Ref TObjectID="3065"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-21512" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3868.000000 -71.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21512" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3065"/>
     <cge:Term_Ref ObjectID="4422"/>
    <cge:TPSR_Ref TObjectID="3065"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-21510" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3868.000000 -71.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21510" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3065"/>
     <cge:Term_Ref ObjectID="4422"/>
    <cge:TPSR_Ref TObjectID="3065"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-21508" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3744.000000 -71.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21508" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3061"/>
     <cge:Term_Ref ObjectID="4414"/>
    <cge:TPSR_Ref TObjectID="3061"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-21509" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3744.000000 -71.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21509" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3061"/>
     <cge:Term_Ref ObjectID="4414"/>
    <cge:TPSR_Ref TObjectID="3061"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-21507" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3744.000000 -71.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21507" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3061"/>
     <cge:Term_Ref ObjectID="4414"/>
    <cge:TPSR_Ref TObjectID="3061"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-21599" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3616.000000 -39.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21599" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3181"/>
     <cge:Term_Ref ObjectID="4654"/>
    <cge:TPSR_Ref TObjectID="3181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-21597" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3616.000000 -39.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21597" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3181"/>
     <cge:Term_Ref ObjectID="4654"/>
    <cge:TPSR_Ref TObjectID="3181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-21602" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5224.000000 -39.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21602" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3187"/>
     <cge:Term_Ref ObjectID="4666"/>
    <cge:TPSR_Ref TObjectID="3187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-21600" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5224.000000 -39.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21600" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3187"/>
     <cge:Term_Ref ObjectID="4666"/>
    <cge:TPSR_Ref TObjectID="3187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-21546" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3651.000000 -946.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21546" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3460"/>
     <cge:Term_Ref ObjectID="4692"/>
    <cge:TPSR_Ref TObjectID="3460"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-21558" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3532.000000 -423.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21558" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3462"/>
     <cge:Term_Ref ObjectID="4694"/>
    <cge:TPSR_Ref TObjectID="3462"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-21576" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5297.000000 -424.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21576" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3465"/>
     <cge:Term_Ref ObjectID="4697"/>
    <cge:TPSR_Ref TObjectID="3465"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-21570" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4968.000000 -464.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21570" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3464"/>
     <cge:Term_Ref ObjectID="4696"/>
    <cge:TPSR_Ref TObjectID="3464"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-21552" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4800.000000 -1176.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21552" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3461"/>
     <cge:Term_Ref ObjectID="4693"/>
    <cge:TPSR_Ref TObjectID="3461"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-21585" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3955.000000 -782.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21585" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3209"/>
     <cge:Term_Ref ObjectID="4703"/>
    <cge:TPSR_Ref TObjectID="3209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-21690" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4629.000000 -779.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="21690" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3210"/>
     <cge:Term_Ref ObjectID="4712"/>
    <cge:TPSR_Ref TObjectID="3210"/></metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-20022">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3846.000000 -949.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3167" ObjectName="SW-CX_BLX.CX_BLX_1421SW"/>
     <cge:Meas_Ref ObjectId="20022"/>
    <cge:TPSR_Ref TObjectID="3167"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20023">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3846.000000 -1071.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3168" ObjectName="SW-CX_BLX.CX_BLX_1422SW"/>
     <cge:Meas_Ref ObjectId="20023"/>
    <cge:TPSR_Ref TObjectID="3168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20024">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3820.000000 -995.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3169" ObjectName="SW-CX_BLX.CX_BLX_1721SW"/>
     <cge:Meas_Ref ObjectId="20024"/>
    <cge:TPSR_Ref TObjectID="3169"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20025">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3823.000000 -1052.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3170" ObjectName="SW-CX_BLX.CX_BLX_1722SW"/>
     <cge:Meas_Ref ObjectId="20025"/>
    <cge:TPSR_Ref TObjectID="3170"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20026">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3823.000000 -1120.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3171" ObjectName="SW-CX_BLX.CX_BLX_1723SW"/>
     <cge:Meas_Ref ObjectId="20026"/>
    <cge:TPSR_Ref TObjectID="3171"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20039">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4084.000000 -949.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3173" ObjectName="SW-CX_BLX.CX_BLX_1600SW"/>
     <cge:Meas_Ref ObjectId="20039"/>
    <cge:TPSR_Ref TObjectID="3173"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20040">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4064.000000 -1007.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3174" ObjectName="SW-CX_BLX.CX_BLX_1700SW"/>
     <cge:Meas_Ref ObjectId="20040"/>
    <cge:TPSR_Ref TObjectID="3174"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19969">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4381.000000 -758.597701)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3158" ObjectName="SW-CX_BLX.CX_BLX_1122SW"/>
     <cge:Meas_Ref ObjectId="19969"/>
    <cge:TPSR_Ref TObjectID="3158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19967">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4358.000000 -805.402299)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3156" ObjectName="SW-CX_BLX.CX_BLX_1725SW"/>
     <cge:Meas_Ref ObjectId="19967"/>
    <cge:TPSR_Ref TObjectID="3156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19968">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4359.000000 -861.402299)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3157" ObjectName="SW-CX_BLX.CX_BLX_1724SW"/>
     <cge:Meas_Ref ObjectId="19968"/>
    <cge:TPSR_Ref TObjectID="3157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19878">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3713.000000 -428.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3136" ObjectName="SW-CX_BLX.CX_BLX_0011SW"/>
     <cge:Meas_Ref ObjectId="19878"/>
    <cge:TPSR_Ref TObjectID="3136"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20224">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3594.000000 -362.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3182" ObjectName="SW-CX_BLX.CX_BLX_0671SW"/>
     <cge:Meas_Ref ObjectId="20224"/>
    <cge:TPSR_Ref TObjectID="3182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20225">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3594.000000 -252.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3183" ObjectName="SW-CX_BLX.CX_BLX_0676SW"/>
     <cge:Meas_Ref ObjectId="20225"/>
    <cge:TPSR_Ref TObjectID="3183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20227">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3573.000000 -239.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3185" ObjectName="SW-CX_BLX.CX_BLX_06767SW"/>
     <cge:Meas_Ref ObjectId="20227"/>
    <cge:TPSR_Ref TObjectID="3185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19883">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3609.000000 -674.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3141" ObjectName="SW-CX_BLX.CX_BLX_1701SW"/>
     <cge:Meas_Ref ObjectId="19883"/>
    <cge:TPSR_Ref TObjectID="3141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3931.000000 -1064.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19970">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4381.000000 -879.402299)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3159" ObjectName="SW-CX_BLX.CX_BLX_1121SW"/>
     <cge:Meas_Ref ObjectId="19970"/>
    <cge:TPSR_Ref TObjectID="3159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19882">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3689.000000 -802.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3140" ObjectName="SW-CX_BLX.CX_BLX_1715SW"/>
     <cge:Meas_Ref ObjectId="19882"/>
    <cge:TPSR_Ref TObjectID="3140"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19881">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3690.000000 -858.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3139" ObjectName="SW-CX_BLX.CX_BLX_1714SW"/>
     <cge:Meas_Ref ObjectId="19881"/>
    <cge:TPSR_Ref TObjectID="3139"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19879">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3714.000000 -876.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3137" ObjectName="SW-CX_BLX.CX_BLX_1111SW"/>
     <cge:Meas_Ref ObjectId="19879"/>
    <cge:TPSR_Ref TObjectID="3137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19880">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3714.000000 -756.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3138" ObjectName="SW-CX_BLX.CX_BLX_1112SW"/>
     <cge:Meas_Ref ObjectId="19880"/>
    <cge:TPSR_Ref TObjectID="3138"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19992">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4302.000000 -946.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3161" ObjectName="SW-CX_BLX.CX_BLX_1411SW"/>
     <cge:Meas_Ref ObjectId="19992"/>
    <cge:TPSR_Ref TObjectID="3161"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19993">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4302.000000 -1068.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3162" ObjectName="SW-CX_BLX.CX_BLX_1412SW"/>
     <cge:Meas_Ref ObjectId="19993"/>
    <cge:TPSR_Ref TObjectID="3162"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19994">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4276.000000 -992.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3163" ObjectName="SW-CX_BLX.CX_BLX_1711SW"/>
     <cge:Meas_Ref ObjectId="19994"/>
    <cge:TPSR_Ref TObjectID="3163"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19995">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4279.000000 -1049.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3164" ObjectName="SW-CX_BLX.CX_BLX_1712SW"/>
     <cge:Meas_Ref ObjectId="19995"/>
    <cge:TPSR_Ref TObjectID="3164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19996">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4279.000000 -1117.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3165" ObjectName="SW-CX_BLX.CX_BLX_1713SW"/>
     <cge:Meas_Ref ObjectId="19996"/>
    <cge:TPSR_Ref TObjectID="3165"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4390.000000 -1059.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19960">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4380.000000 -426.597701)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3152" ObjectName="SW-CX_BLX.CX_BLX_0022SW"/>
     <cge:Meas_Ref ObjectId="19960"/>
    <cge:TPSR_Ref TObjectID="3152"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20226">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3572.000000 -300.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3184" ObjectName="SW-CX_BLX.CX_BLX_06760SW"/>
     <cge:Meas_Ref ObjectId="20226"/>
    <cge:TPSR_Ref TObjectID="3184"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20228">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3594.000000 -50.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3186" ObjectName="SW-CX_BLX.CX_BLX_06700SW"/>
     <cge:Meas_Ref ObjectId="20228"/>
    <cge:TPSR_Ref TObjectID="3186"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19548">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4210.000000 -426.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3082" ObjectName="SW-CX_BLX.CX_BLX_0121SW"/>
     <cge:Meas_Ref ObjectId="19548"/>
    <cge:TPSR_Ref TObjectID="3082"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19549">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4329.000000 -427.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3083" ObjectName="SW-CX_BLX.CX_BLX_0122SW"/>
     <cge:Meas_Ref ObjectId="19549"/>
    <cge:TPSR_Ref TObjectID="3083"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20055">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3584.000000 -426.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3175" ObjectName="SW-CX_BLX.CX_BLX_0901SW"/>
     <cge:Meas_Ref ObjectId="20055"/>
    <cge:TPSR_Ref TObjectID="3175"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19743">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4913.000000 -817.045977)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3118" ObjectName="SW-CX_BLX.CX_BLX_3121SW"/>
     <cge:Meas_Ref ObjectId="19743"/>
    <cge:TPSR_Ref TObjectID="3118"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19744">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4918.000000 -720.045977)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3119" ObjectName="SW-CX_BLX.CX_BLX_3122SW"/>
     <cge:Meas_Ref ObjectId="19744"/>
    <cge:TPSR_Ref TObjectID="3119"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20400">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4805.000000 -1056.000000)" xlink:href="#switch2:shape17_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3198" ObjectName="SW-CX_BLX.CX_BLX_3901SW"/>
     <cge:Meas_Ref ObjectId="20400"/>
    <cge:TPSR_Ref TObjectID="3198"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19888">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3896.000000 -672.000000)" xlink:href="#switch2:shape17_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3143" ObjectName="SW-CX_BLX.CX_BLX_3010SW"/>
     <cge:Meas_Ref ObjectId="19888"/>
    <cge:TPSR_Ref TObjectID="3143"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19942">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4826.000000 -902.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3148" ObjectName="SW-CX_BLX.CX_BLX_3022SW"/>
     <cge:Meas_Ref ObjectId="19942"/>
    <cge:TPSR_Ref TObjectID="3148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19943">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4690.000000 -902.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3149" ObjectName="SW-CX_BLX.CX_BLX_3026SW"/>
     <cge:Meas_Ref ObjectId="19943"/>
    <cge:TPSR_Ref TObjectID="3149"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19939">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4728.000000 -857.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3146" ObjectName="SW-CX_BLX.CX_BLX_30260SW"/>
     <cge:Meas_Ref ObjectId="19939"/>
    <cge:TPSR_Ref TObjectID="3146"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19860">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4824.000000 -661.402299)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3132" ObjectName="SW-CX_BLX.CX_BLX_3011SW"/>
     <cge:Meas_Ref ObjectId="19860"/>
    <cge:TPSR_Ref TObjectID="3132"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19861">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4696.000000 -661.402299)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3133" ObjectName="SW-CX_BLX.CX_BLX_3016SW"/>
     <cge:Meas_Ref ObjectId="19861"/>
    <cge:TPSR_Ref TObjectID="3133"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20329">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 -613.597701)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3193" ObjectName="SW-CX_BLX.CX_BLX_30160SW"/>
     <cge:Meas_Ref ObjectId="20329"/>
    <cge:TPSR_Ref TObjectID="3193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19966">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4284.000000 -683.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3155" ObjectName="SW-CX_BLX.CX_BLX_1702SW"/>
     <cge:Meas_Ref ObjectId="19966"/>
    <cge:TPSR_Ref TObjectID="3155"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19962">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4104.000000 -672.000000)" xlink:href="#switch2:shape17_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3154" ObjectName="SW-CX_BLX.CX_BLX_3020SW"/>
     <cge:Meas_Ref ObjectId="19962"/>
    <cge:TPSR_Ref TObjectID="3154"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20056">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4591.000000 -426.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3176" ObjectName="SW-CX_BLX.CX_BLX_0902SW"/>
     <cge:Meas_Ref ObjectId="20056"/>
    <cge:TPSR_Ref TObjectID="3176"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19489">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3994.666667 -360.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3070" ObjectName="SW-CX_BLX.CX_BLX_0631SW"/>
     <cge:Meas_Ref ObjectId="19489"/>
    <cge:TPSR_Ref TObjectID="3070"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19490">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3994.666667 -199.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3071" ObjectName="SW-CX_BLX.CX_BLX_0636SW"/>
     <cge:Meas_Ref ObjectId="19490"/>
    <cge:TPSR_Ref TObjectID="3071"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19491">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4024.666667 -143.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3072" ObjectName="SW-CX_BLX.CX_BLX_0635SW"/>
     <cge:Meas_Ref ObjectId="19491"/>
    <cge:TPSR_Ref TObjectID="3072"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20403">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4808.000000 -543.000000)" xlink:href="#switch2:shape17_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3199" ObjectName="SW-CX_BLX.CX_BLX_3902SW"/>
     <cge:Meas_Ref ObjectId="20403"/>
    <cge:TPSR_Ref TObjectID="3199"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4911.000000 -1143.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5033.000000 -1143.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20396">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4910.000000 -1048.333333)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3196" ObjectName="SW-CX_BLX.CX_BLX_3621SW"/>
     <cge:Meas_Ref ObjectId="20396"/>
    <cge:TPSR_Ref TObjectID="3196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20397">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5032.000000 -1048.333333)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3197" ObjectName="SW-CX_BLX.CX_BLX_3626SW"/>
     <cge:Meas_Ref ObjectId="20397"/>
    <cge:TPSR_Ref TObjectID="3197"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19760">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5032.000000 -953.666667)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3122" ObjectName="SW-CX_BLX.CX_BLX_3636SW"/>
     <cge:Meas_Ref ObjectId="19760"/>
    <cge:TPSR_Ref TObjectID="3122"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19759">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4910.000000 -953.666667)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3121" ObjectName="SW-CX_BLX.CX_BLX_3631SW"/>
     <cge:Meas_Ref ObjectId="19759"/>
    <cge:TPSR_Ref TObjectID="3121"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19780">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5035.000000 -859.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3125" ObjectName="SW-CX_BLX.CX_BLX_3646SW"/>
     <cge:Meas_Ref ObjectId="19780"/>
    <cge:TPSR_Ref TObjectID="3125"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19779">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4913.000000 -859.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3124" ObjectName="SW-CX_BLX.CX_BLX_3641SW"/>
     <cge:Meas_Ref ObjectId="19779"/>
    <cge:TPSR_Ref TObjectID="3124"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19718">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4914.000000 -675.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3115" ObjectName="SW-CX_BLX.CX_BLX_3652SW"/>
     <cge:Meas_Ref ObjectId="19718"/>
    <cge:TPSR_Ref TObjectID="3115"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19719">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5033.000000 -675.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3116" ObjectName="SW-CX_BLX.CX_BLX_3656SW"/>
     <cge:Meas_Ref ObjectId="19719"/>
    <cge:TPSR_Ref TObjectID="3116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19698">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4914.000000 -581.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3112" ObjectName="SW-CX_BLX.CX_BLX_3662SW"/>
     <cge:Meas_Ref ObjectId="19698"/>
    <cge:TPSR_Ref TObjectID="3112"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19699">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5032.000000 -581.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3113" ObjectName="SW-CX_BLX.CX_BLX_3666SW"/>
     <cge:Meas_Ref ObjectId="19699"/>
    <cge:TPSR_Ref TObjectID="3113"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19679">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5032.000000 -487.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3109" ObjectName="SW-CX_BLX.CX_BLX_3676SW"/>
     <cge:Meas_Ref ObjectId="19679"/>
    <cge:TPSR_Ref TObjectID="3109"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19678">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4911.000000 -487.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3108" ObjectName="SW-CX_BLX.CX_BLX_3672SW"/>
     <cge:Meas_Ref ObjectId="19678"/>
    <cge:TPSR_Ref TObjectID="3108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20248">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5184.000000 -361.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3188" ObjectName="SW-CX_BLX.CX_BLX_0872SW"/>
     <cge:Meas_Ref ObjectId="20248"/>
    <cge:TPSR_Ref TObjectID="3188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20249">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5184.000000 -251.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3189" ObjectName="SW-CX_BLX.CX_BLX_0876SW"/>
     <cge:Meas_Ref ObjectId="20249"/>
    <cge:TPSR_Ref TObjectID="3189"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20251">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5163.000000 -238.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3191" ObjectName="SW-CX_BLX.CX_BLX_08767SW"/>
     <cge:Meas_Ref ObjectId="20251"/>
    <cge:TPSR_Ref TObjectID="3191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20250">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5162.000000 -299.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3190" ObjectName="SW-CX_BLX.CX_BLX_08760SW"/>
     <cge:Meas_Ref ObjectId="20250"/>
    <cge:TPSR_Ref TObjectID="3190"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-20252">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5184.000000 -54.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3192" ObjectName="SW-CX_BLX.CX_BLX_08700SW"/>
     <cge:Meas_Ref ObjectId="20252"/>
    <cge:TPSR_Ref TObjectID="3192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19681">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5094.000000 -307.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3110" ObjectName="SW-CX_BLX.CX_BLX_0882SW"/>
     <cge:Meas_Ref ObjectId="19681"/>
    <cge:TPSR_Ref TObjectID="3110"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19642">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5013.000000 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3101" ObjectName="SW-CX_BLX.CX_BLX_0852SW"/>
     <cge:Meas_Ref ObjectId="19642"/>
    <cge:TPSR_Ref TObjectID="3101"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19643">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5013.000000 -198.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3102" ObjectName="SW-CX_BLX.CX_BLX_0855SW"/>
     <cge:Meas_Ref ObjectId="19643"/>
    <cge:TPSR_Ref TObjectID="3102"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19661">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4928.000000 -198.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3105" ObjectName="SW-CX_BLX.CX_BLX_0866SW"/>
     <cge:Meas_Ref ObjectId="19661"/>
    <cge:TPSR_Ref TObjectID="3105"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19662">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4958.000000 -142.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3106" ObjectName="SW-CX_BLX.CX_BLX_0865SW"/>
     <cge:Meas_Ref ObjectId="19662"/>
    <cge:TPSR_Ref TObjectID="3106"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19660">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4928.000000 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3104" ObjectName="SW-CX_BLX.CX_BLX_0862SW"/>
     <cge:Meas_Ref ObjectId="19660"/>
    <cge:TPSR_Ref TObjectID="3104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19623">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4802.500000 -360.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3097" ObjectName="SW-CX_BLX.CX_BLX_0842SW"/>
     <cge:Meas_Ref ObjectId="19623"/>
    <cge:TPSR_Ref TObjectID="3097"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19624">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4802.500000 -199.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3098" ObjectName="SW-CX_BLX.CX_BLX_0846SW"/>
     <cge:Meas_Ref ObjectId="19624"/>
    <cge:TPSR_Ref TObjectID="3098"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19625">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4832.500000 -143.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3099" ObjectName="SW-CX_BLX.CX_BLX_0845SW"/>
     <cge:Meas_Ref ObjectId="19625"/>
    <cge:TPSR_Ref TObjectID="3099"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19605">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4674.000000 -197.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3094" ObjectName="SW-CX_BLX.CX_BLX_0836SW"/>
     <cge:Meas_Ref ObjectId="19605"/>
    <cge:TPSR_Ref TObjectID="3094"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19606">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4704.000000 -141.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3095" ObjectName="SW-CX_BLX.CX_BLX_0835SW"/>
     <cge:Meas_Ref ObjectId="19606"/>
    <cge:TPSR_Ref TObjectID="3095"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19604">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4674.000000 -358.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3093" ObjectName="SW-CX_BLX.CX_BLX_0832SW"/>
     <cge:Meas_Ref ObjectId="19604"/>
    <cge:TPSR_Ref TObjectID="3093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19585">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4547.500000 -361.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3089" ObjectName="SW-CX_BLX.CX_BLX_0822SW"/>
     <cge:Meas_Ref ObjectId="19585"/>
    <cge:TPSR_Ref TObjectID="3089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19586">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4547.500000 -200.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3090" ObjectName="SW-CX_BLX.CX_BLX_0826SW"/>
     <cge:Meas_Ref ObjectId="19586"/>
    <cge:TPSR_Ref TObjectID="3090"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19587">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4577.500000 -144.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3091" ObjectName="SW-CX_BLX.CX_BLX_0825SW"/>
     <cge:Meas_Ref ObjectId="19587"/>
    <cge:TPSR_Ref TObjectID="3091"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19566">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4419.000000 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3085" ObjectName="SW-CX_BLX.CX_BLX_0812SW"/>
     <cge:Meas_Ref ObjectId="19566"/>
    <cge:TPSR_Ref TObjectID="3085"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19567">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4419.000000 -198.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3086" ObjectName="SW-CX_BLX.CX_BLX_0816SW"/>
     <cge:Meas_Ref ObjectId="19567"/>
    <cge:TPSR_Ref TObjectID="3086"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19568">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4449.000000 -142.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3087" ObjectName="SW-CX_BLX.CX_BLX_0815SW"/>
     <cge:Meas_Ref ObjectId="19568"/>
    <cge:TPSR_Ref TObjectID="3087"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19527">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4239.000000 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3078" ObjectName="SW-CX_BLX.CX_BLX_0661SW"/>
     <cge:Meas_Ref ObjectId="19527"/>
    <cge:TPSR_Ref TObjectID="3078"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19528">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4239.000000 -198.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3079" ObjectName="SW-CX_BLX.CX_BLX_0666SW"/>
     <cge:Meas_Ref ObjectId="19528"/>
    <cge:TPSR_Ref TObjectID="3079"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19529">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4269.000000 -142.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3080" ObjectName="SW-CX_BLX.CX_BLX_0665SW"/>
     <cge:Meas_Ref ObjectId="19529"/>
    <cge:TPSR_Ref TObjectID="3080"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4239.000000 -71.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19508">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4136.000000 -361.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3074" ObjectName="SW-CX_BLX.CX_BLX_0641SW"/>
     <cge:Meas_Ref ObjectId="19508"/>
    <cge:TPSR_Ref TObjectID="3074"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19509">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4136.000000 -200.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3075" ObjectName="SW-CX_BLX.CX_BLX_0646SW"/>
     <cge:Meas_Ref ObjectId="19509"/>
    <cge:TPSR_Ref TObjectID="3075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19510">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4166.000000 -144.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3076" ObjectName="SW-CX_BLX.CX_BLX_0645SW"/>
     <cge:Meas_Ref ObjectId="19510"/>
    <cge:TPSR_Ref TObjectID="3076"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19470">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3853.333333 -361.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3066" ObjectName="SW-CX_BLX.CX_BLX_0621SW"/>
     <cge:Meas_Ref ObjectId="19470"/>
    <cge:TPSR_Ref TObjectID="3066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19471">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3853.333333 -200.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3067" ObjectName="SW-CX_BLX.CX_BLX_0626SW"/>
     <cge:Meas_Ref ObjectId="19471"/>
    <cge:TPSR_Ref TObjectID="3067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19472">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3883.333333 -144.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3068" ObjectName="SW-CX_BLX.CX_BLX_0625SW"/>
     <cge:Meas_Ref ObjectId="19472"/>
    <cge:TPSR_Ref TObjectID="3068"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19451">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3712.000000 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3062" ObjectName="SW-CX_BLX.CX_BLX_0611SW"/>
     <cge:Meas_Ref ObjectId="19451"/>
    <cge:TPSR_Ref TObjectID="3062"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19452">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3712.000000 -198.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3063" ObjectName="SW-CX_BLX.CX_BLX_0616SW"/>
     <cge:Meas_Ref ObjectId="19452"/>
    <cge:TPSR_Ref TObjectID="3063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19453">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3742.000000 -142.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3064" ObjectName="SW-CX_BLX.CX_BLX_0615SW"/>
     <cge:Meas_Ref ObjectId="19453"/>
    <cge:TPSR_Ref TObjectID="3064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19959">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4380.000000 -526.597701)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3151" ObjectName="SW-CX_BLX.CX_BLX_0026SW"/>
     <cge:Meas_Ref ObjectId="19959"/>
    <cge:TPSR_Ref TObjectID="3151"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-19877">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3713.000000 -533.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3135" ObjectName="SW-CX_BLX.CX_BLX_0016SW"/>
     <cge:Meas_Ref ObjectId="19877"/>
    <cge:TPSR_Ref TObjectID="3135"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_296d0d0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4074.000000 -1118.000000)" xlink:href="#lightningRod:shape82"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a30360">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3723.000000 -606.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cae320">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4390.000000 -599.402299)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b38bc0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3642.000000 -675.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cc2030">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3662.000000 -678.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31ef5b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3572.000000 -118.000000)" xlink:href="#lightningRod:shape83"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33791c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3639.000000 -192.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30de120">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3572.000000 -600.000000)" xlink:href="#lightningRod:shape87"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3344060">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4711.500000 -1092.500000)" xlink:href="#lightningRod:shape74"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cb2700">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3971.000000 -608.000000)" xlink:href="#lightningRod:shape53"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cb4430">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4050.000000 -607.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a184a0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4317.000000 -682.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b5ad40">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4335.000000 -685.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b5ba80">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3895.000000 -1121.000000)" xlink:href="#lightningRod:shape81"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b5c4f0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4352.000000 -1118.000000)" xlink:href="#lightningRod:shape81"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33304c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4579.000000 -596.000000)" xlink:href="#lightningRod:shape87"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29006d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4004.666667 -262.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ca5b70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3972.666667 -85.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ca6d10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4004.666667 -68.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33b6330">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4714.500000 -579.500000)" xlink:href="#lightningRod:shape74"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a28860">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5201.000000 -1184.000000)" xlink:href="#lightningRod:shape85"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a29550">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5097.000000 -1141.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_291a7b0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5200.000000 -1089.333333)" xlink:href="#lightningRod:shape85"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_291b4a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5096.000000 -1045.333333)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3367090">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5096.000000 -951.666667)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33349d0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5200.000000 -994.666667)" xlink:href="#lightningRod:shape85"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2afe440">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5203.000000 -900.000000)" xlink:href="#lightningRod:shape85"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2952240">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5099.000000 -857.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a83ef0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5201.000000 -716.000000)" xlink:href="#lightningRod:shape85"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b4a3e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5097.000000 -673.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_335dab0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5200.000000 -622.000000)" xlink:href="#lightningRod:shape85"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_335e7a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5096.000000 -579.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2920560">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5200.000000 -528.000000)" xlink:href="#lightningRod:shape85"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2921250">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5096.000000 -485.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29640d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5162.000000 -117.000000)" xlink:href="#lightningRod:shape83"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2967320">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5229.000000 -188.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bb3ed0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5100.000000 -56.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29d4d70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5023.000000 -261.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29dafa0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4938.000000 -261.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c8c1a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4906.000000 -84.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c972f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4812.500000 -262.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2adaf00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4780.500000 -85.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ae0c50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4684.000000 -260.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ae4e30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4652.000000 -83.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33bec30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4557.500000 -263.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33c3790">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4525.500000 -86.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33cb910">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4429.000000 -261.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33d0210">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4397.000000 -84.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b953d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4249.000000 -261.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b99810">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4217.000000 -84.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b9d4f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4274.000000 -18.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b9f480">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4305.000000 -34.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a36890">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4146.000000 -263.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a3acd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4114.000000 -86.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2982010">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3863.333333 -263.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2986450">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3831.333333 -86.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_298ecf0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3722.000000 -261.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29939c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3690.000000 -84.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_292e040">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3596.000000 -200.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29392d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5186.000000 -199.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1"/>
</svg>