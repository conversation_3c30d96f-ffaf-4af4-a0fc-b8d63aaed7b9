<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-67" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3117 -1201 2028 1201">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="29" x2="29" y1="7" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="25" x2="25" y1="6" y2="13"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape85">
    <circle cx="8" cy="17" fillStyle="0" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="20" y2="20"/>
    <rect height="27" stroke-width="0.416667" width="14" x="1" y="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="73" y2="25"/>
    <circle cx="8" cy="8" fillStyle="0" r="7.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape170">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.11074" x1="14" x2="21" y1="46" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.11074" x1="29" x2="22" y1="46" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.11075" x1="21" x2="21" y1="52" y2="58"/>
    <ellipse cx="21" cy="51" rx="20" ry="19.5" stroke-width="2.11074"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.11074" x1="14" x2="21" y1="15" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.11075" x1="21" x2="21" y1="21" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.11074" x1="29" x2="22" y1="15" y2="21"/>
    <ellipse cx="21" cy="20" rx="20" ry="19.5" stroke-width="2.11074"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape193">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="19,39 10,27 1,39 19,39 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="27" y2="17"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="load:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="5" y2="29"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,19 9,31 17,19 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="transformer2:shape13_0">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="38" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape13_1">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="46" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="29" y1="34" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape43_0">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="60" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="68" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="76" y2="68"/>
   </symbol>
   <symbol id="transformer2:shape43_1">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape72">
    <ellipse cx="19" cy="63" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.816532" x1="52" x2="59" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="45" y1="53" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="17" x2="20" y1="65" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="23" x2="20" y1="65" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="20" x2="20" y1="63" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="5" x2="11" y1="62" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="9" x2="11" y1="65" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="55" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="55" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="53" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16267" x1="41" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="44" x2="46" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.35645" x1="42" x2="48" y1="5" y2="5"/>
    <polyline points="20,63 45,63 45,42 " stroke-width="1"/>
    <ellipse cx="16" cy="55" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="8" cy="64" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="7" x2="5" y1="65" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.809524" x1="45" x2="45" y1="25" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="52" y1="23" y2="40"/>
    <rect height="17" stroke-width="0.888889" width="8" x="41" y="25"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2df8630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2df9080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2b5e030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2b5eb70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2da6490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2da6fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2da7950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2da8410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2c10ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2c10ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d9c7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d9c7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d9e450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d9e450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2d9f470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2da10e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2da1d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2da2b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2da3250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dd2da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dd37d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dd4060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2dccf20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dcdf70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dce8f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dcf3e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2dcfda0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2dd09a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2dd13f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_314da50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_314e670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3154810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3155100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_314f270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3150780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1211" width="2038" x="3112" y="-1206"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4916" x2="4916" y1="-913" y2="-809"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5028" x2="5028" y1="-913" y2="-809"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4916" x2="4916" y1="-782" y2="-739"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5028" x2="5028" y1="-782" y2="-739"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5028" x2="4916" y1="-739" y2="-739"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4974" x2="4974" y1="-739" y2="-658"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="4628" x2="4584" y1="-753" y2="-726"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="5143" x2="5143" y1="-799" y2="-780"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="34" stroke="rgb(255,255,0)" stroke-width="1" width="170" x="4634" y="-770"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3119" y="-1200"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1080"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-600"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(0,255,0)" stroke-width="0.416667" width="14" x="4909" y="-866"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(0,255,0)" stroke-width="0.416667" width="14" x="5021" y="-866"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="3357" y="-1082"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-41106">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 3847.000000 -941.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6778" ObjectName="SW-CX_XC.CX_XC_3616SW"/>
     <cge:Meas_Ref ObjectId="41106"/>
    <cge:TPSR_Ref TObjectID="6778"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41107">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 3847.000000 -815.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6779" ObjectName="SW-CX_XC.CX_XC_3611SW"/>
     <cge:Meas_Ref ObjectId="41107"/>
    <cge:TPSR_Ref TObjectID="6779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41102">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4216.000000 -941.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6774" ObjectName="SW-CX_XC.CX_XC_3626SW"/>
     <cge:Meas_Ref ObjectId="41102"/>
    <cge:TPSR_Ref TObjectID="6774"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41103">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4216.000000 -815.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6775" ObjectName="SW-CX_XC.CX_XC_3621SW"/>
     <cge:Meas_Ref ObjectId="41103"/>
    <cge:TPSR_Ref TObjectID="6775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41110">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4582.000000 -941.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6782" ObjectName="SW-CX_XC.CX_XC_3636SW"/>
     <cge:Meas_Ref ObjectId="41110"/>
    <cge:TPSR_Ref TObjectID="6782"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41111">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4582.000000 -815.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6783" ObjectName="SW-CX_XC.CX_XC_3631SW"/>
     <cge:Meas_Ref ObjectId="41111"/>
    <cge:TPSR_Ref TObjectID="6783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41125">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4504.000000 -406.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6797" ObjectName="SW-CX_XC.CX_XC_0021SW"/>
     <cge:Meas_Ref ObjectId="41125"/>
    <cge:TPSR_Ref TObjectID="6797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41120">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4505.000000 -736.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6792" ObjectName="SW-CX_XC.CX_XC_3021SW"/>
     <cge:Meas_Ref ObjectId="41120"/>
    <cge:TPSR_Ref TObjectID="6792"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41117">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4325.000000 -734.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6789" ObjectName="SW-CX_XC.CX_XC_3901SW"/>
     <cge:Meas_Ref ObjectId="41117"/>
    <cge:TPSR_Ref TObjectID="6789"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-102577">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4375.000000 -689.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20496" ObjectName="SW-CX_XC.CX_XC_39017SW"/>
     <cge:Meas_Ref ObjectId="102577"/>
    <cge:TPSR_Ref TObjectID="20496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41123">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4109.000000 -400.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6795" ObjectName="SW-CX_XC.CX_XC_0011SW"/>
     <cge:Meas_Ref ObjectId="41123"/>
    <cge:TPSR_Ref TObjectID="6795"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41114">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4108.000000 -736.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6786" ObjectName="SW-CX_XC.CX_XC_3011SW"/>
     <cge:Meas_Ref ObjectId="41114"/>
    <cge:TPSR_Ref TObjectID="6786"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41115">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4108.000000 -630.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6787" ObjectName="SW-CX_XC.CX_XC_3016SW"/>
     <cge:Meas_Ref ObjectId="41115"/>
    <cge:TPSR_Ref TObjectID="6787"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41116">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4159.000000 -594.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6788" ObjectName="SW-CX_XC.CX_XC_30167SW"/>
     <cge:Meas_Ref ObjectId="41116"/>
    <cge:TPSR_Ref TObjectID="6788"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-102598">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4705.000000 -308.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20497" ObjectName="SW-CX_XC.CX_XC_0901"/>
     <cge:Meas_Ref ObjectId="102598"/>
    <cge:TPSR_Ref TObjectID="20497"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41128">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 3941.000000 -307.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6800" ObjectName="SW-CX_XC.CX_XC_0611SW"/>
     <cge:Meas_Ref ObjectId="41128"/>
    <cge:TPSR_Ref TObjectID="6800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41129">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 3941.000000 -203.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6801" ObjectName="SW-CX_XC.CX_XC_0616SW"/>
     <cge:Meas_Ref ObjectId="41129"/>
    <cge:TPSR_Ref TObjectID="6801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41132">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4269.000000 -307.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6804" ObjectName="SW-CX_XC.CX_XC_0631SW"/>
     <cge:Meas_Ref ObjectId="41132"/>
    <cge:TPSR_Ref TObjectID="6804"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41133">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4269.000000 -203.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6805" ObjectName="SW-CX_XC.CX_XC_0636SW"/>
     <cge:Meas_Ref ObjectId="41133"/>
    <cge:TPSR_Ref TObjectID="6805"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41134">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4456.000000 -307.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6806" ObjectName="SW-CX_XC.CX_XC_0641SW"/>
     <cge:Meas_Ref ObjectId="41134"/>
    <cge:TPSR_Ref TObjectID="6806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41135">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4456.000000 -203.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6807" ObjectName="SW-CX_XC.CX_XC_0646SW"/>
     <cge:Meas_Ref ObjectId="41135"/>
    <cge:TPSR_Ref TObjectID="6807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41130">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4096.000000 -308.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6802" ObjectName="SW-CX_XC.CX_XC_0621SW"/>
     <cge:Meas_Ref ObjectId="41130"/>
    <cge:TPSR_Ref TObjectID="6802"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41131">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4096.000000 -204.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6803" ObjectName="SW-CX_XC.CX_XC_0626SW"/>
     <cge:Meas_Ref ObjectId="41131"/>
    <cge:TPSR_Ref TObjectID="6803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41121">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4556.000000 -691.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6793" ObjectName="SW-CX_XC.CX_XC_30217SW"/>
     <cge:Meas_Ref ObjectId="41121"/>
    <cge:TPSR_Ref TObjectID="6793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41108">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3810.000000 -879.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6780" ObjectName="SW-CX_XC.CX_XC_36160SW"/>
     <cge:Meas_Ref ObjectId="41108"/>
    <cge:TPSR_Ref TObjectID="6780"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41104">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4180.000000 -879.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6776" ObjectName="SW-CX_XC.CX_XC_36260SW"/>
     <cge:Meas_Ref ObjectId="41104"/>
    <cge:TPSR_Ref TObjectID="6776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41112">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4545.000000 -879.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6784" ObjectName="SW-CX_XC.CX_XC_36360SW"/>
     <cge:Meas_Ref ObjectId="41112"/>
    <cge:TPSR_Ref TObjectID="6784"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3909.000000 -960.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4279.000000 -963.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4644.000000 -963.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4773.000000 -392.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4887.000000 -392.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4599.000000 -310.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4599.000000 -206.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_XC.CX_XC_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3681,-798 4707,-798 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6771" ObjectName="BS-CX_XC.CX_XC_3IM"/>
    <cge:TPSR_Ref TObjectID="6771"/></metadata>
   <polyline fill="none" opacity="0" points="3681,-798 4707,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_XC.CX_XC_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3669,-373 4809,-373 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6772" ObjectName="BS-CX_XC.CX_XC_9IM"/>
    <cge:TPSR_Ref TObjectID="6772"/></metadata>
   <polyline fill="none" opacity="0" points="3669,-373 4809,-373 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_XC.061Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3940.000000 -108.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34515" ObjectName="EC-CX_XC.061Ld"/>
    <cge:TPSR_Ref TObjectID="34515"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_XC.062Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4095.000000 -108.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34516" ObjectName="EC-CX_XC.062Ld"/>
    <cge:TPSR_Ref TObjectID="34516"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_XC.063Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4268.000000 -118.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34517" ObjectName="EC-CX_XC.063Ld"/>
    <cge:TPSR_Ref TObjectID="34517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_XC.064Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4455.000000 -121.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34518" ObjectName="EC-CX_XC.064Ld"/>
    <cge:TPSR_Ref TObjectID="34518"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_XC.361Ld">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3846.000000 -1100.000000)" xlink:href="#load:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41851" ObjectName="EC-CX_XC.361Ld"/>
    <cge:TPSR_Ref TObjectID="41851"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4598.000000 -87.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2bc1790" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4414.621622 -705.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bdc170" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4198.621622 -610.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bca6c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4593.621622 -707.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b92290" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3813.000000 -857.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b9d8f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4183.000000 -857.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2aff620" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4548.000000 -857.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2b2a3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-856 3855,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6779@1" ObjectIDZND0="6777@0" Pin0InfoVect0LinkObjId="SW-41105_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41107_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-856 3855,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bacfe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-798 3855,-820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6771@0" ObjectIDZND0="6779@0" Pin0InfoVect0LinkObjId="SW-41107_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b76140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-798 3855,-820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bad1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3819,-920 3819,-927 3855,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6780@1" ObjectIDZND0="6777@x" ObjectIDZND1="6778@x" Pin0InfoVect0LinkObjId="SW-41105_0" Pin0InfoVect1LinkObjId="SW-41106_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41108_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3819,-920 3819,-927 3855,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c518a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-906 3855,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6777@1" ObjectIDZND0="6778@x" ObjectIDZND1="6780@x" Pin0InfoVect0LinkObjId="SW-41106_0" Pin0InfoVect1LinkObjId="SW-41108_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41105_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-906 3855,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c51a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-927 3855,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6777@x" ObjectIDND1="6780@x" ObjectIDZND0="6778@0" Pin0InfoVect0LinkObjId="SW-41106_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-41105_0" Pin1InfoVect1LinkObjId="SW-41108_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-927 3855,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b55e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-856 4225,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6775@1" ObjectIDZND0="6773@0" Pin0InfoVect0LinkObjId="SW-41101_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41103_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4225,-856 4225,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b56040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-798 4225,-820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6771@0" ObjectIDZND0="6775@0" Pin0InfoVect0LinkObjId="SW-41103_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b76140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4225,-798 4225,-820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c2f6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4591,-856 4591,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6783@1" ObjectIDZND0="6781@0" Pin0InfoVect0LinkObjId="SW-41109_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41111_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4591,-856 4591,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c2f890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4590,-798 4590,-820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6771@0" ObjectIDZND0="6783@0" Pin0InfoVect0LinkObjId="SW-41111_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b76140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4590,-798 4590,-820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bcf2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-497 4512,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="6796@1" ObjectIDZND0="6809@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41124_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-497 4512,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bd0c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-373 4512,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6772@0" ObjectIDZND0="6797@0" Pin0InfoVect0LinkObjId="SW-41125_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b994b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-373 4512,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bd0e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-447 4512,-470 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6797@1" ObjectIDZND0="6796@0" Pin0InfoVect0LinkObjId="SW-41124_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41125_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-447 4512,-470 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b76140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4513,-777 4513,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6792@1" ObjectIDZND0="6771@0" Pin0InfoVect0LinkObjId="g_2bc11c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41120_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4513,-777 4513,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b76330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-684 4382,-684 4382,-667 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="20496@x" ObjectIDND1="6789@x" ObjectIDND2="g_2ac8850@0" ObjectIDZND0="g_2b4e140@0" Pin0InfoVect0LinkObjId="g_2b4e140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-102577_0" Pin1InfoVect1LinkObjId="SW-41117_0" Pin1InfoVect2LinkObjId="g_2ac8850_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-684 4382,-684 4382,-667 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bc11c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-775 4334,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6789@1" ObjectIDZND0="6771@0" Pin0InfoVect0LinkObjId="g_2b76140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41117_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-775 4334,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bc13b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-662 4334,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2ac8850@0" ObjectIDZND0="20496@x" ObjectIDZND1="6789@x" ObjectIDZND2="g_2b4e140@0" Pin0InfoVect0LinkObjId="SW-102577_0" Pin0InfoVect1LinkObjId="SW-41117_0" Pin0InfoVect2LinkObjId="g_2b4e140_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ac8850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-662 4334,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bc15a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4402,-715 4420,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="20496@1" ObjectIDZND0="g_2bc1790@0" Pin0InfoVect0LinkObjId="g_2bc1790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-102577_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4402,-715 4420,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b1dc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4366,-715 4334,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="20496@0" ObjectIDZND0="g_2b4e140@0" ObjectIDZND1="g_2ac8850@0" ObjectIDZND2="6789@x" Pin0InfoVect0LinkObjId="g_2b4e140_0" Pin0InfoVect1LinkObjId="g_2ac8850_0" Pin0InfoVect2LinkObjId="SW-41117_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-102577_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4366,-715 4334,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b1de30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-739 4334,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="6789@0" ObjectIDZND0="20496@x" ObjectIDZND1="g_2b4e140@0" ObjectIDZND2="g_2ac8850@0" Pin0InfoVect0LinkObjId="SW-102577_0" Pin0InfoVect1LinkObjId="g_2b4e140_0" Pin0InfoVect2LinkObjId="g_2ac8850_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41117_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-739 4334,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b1e020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-715 4334,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="20496@x" ObjectIDND1="6789@x" ObjectIDZND0="g_2b4e140@0" ObjectIDZND1="g_2ac8850@0" Pin0InfoVect0LinkObjId="g_2b4e140_0" Pin0InfoVect1LinkObjId="g_2ac8850_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-102577_0" Pin1InfoVect1LinkObjId="SW-41117_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-715 4334,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b1f7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-494 4116,-521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="6794@1" ObjectIDZND0="6808@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41122_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-494 4116,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bea3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4117,-373 4117,-405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6772@0" ObjectIDZND0="6795@0" Pin0InfoVect0LinkObjId="SW-41123_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b994b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4117,-373 4117,-405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bea5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4117,-441 4117,-464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6795@1" ObjectIDZND0="6794@0" Pin0InfoVect0LinkObjId="SW-41122_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41123_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4117,-441 4117,-464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bdbb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-777 4116,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6786@1" ObjectIDZND0="6771@0" Pin0InfoVect0LinkObjId="g_2b76140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41114_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-777 4116,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bdbd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-671 4116,-693 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6787@1" ObjectIDZND0="6785@0" Pin0InfoVect0LinkObjId="SW-41113_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41115_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-671 4116,-693 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bdbf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-720 4116,-741 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6785@1" ObjectIDZND0="6786@0" Pin0InfoVect0LinkObjId="SW-41114_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41113_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-720 4116,-741 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bd41e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-600 4116,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6808@0" ObjectIDZND0="6788@x" ObjectIDZND1="6787@x" Pin0InfoVect0LinkObjId="SW-41116_0" Pin0InfoVect1LinkObjId="SW-41115_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b1f7c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-600 4116,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bd4400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4714,-258 4762,-258 4762,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="20497@x" ObjectIDND1="g_2b0ce10@0" ObjectIDZND0="g_2b4f7a0@0" Pin0InfoVect0LinkObjId="g_2b4f7a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-102598_0" Pin1InfoVect1LinkObjId="g_2b0ce10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4714,-258 4762,-258 4762,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b994b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4714,-349 4714,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20497@1" ObjectIDZND0="6772@0" Pin0InfoVect0LinkObjId="g_2b08e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-102598_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4714,-349 4714,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b996a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4714,-236 4714,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2b0ce10@1" ObjectIDZND0="g_2b4f7a0@0" ObjectIDZND1="20497@x" Pin0InfoVect0LinkObjId="g_2b4f7a0_0" Pin0InfoVect1LinkObjId="SW-102598_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b0ce10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4714,-236 4714,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b998c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4714,-313 4714,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="20497@0" ObjectIDZND0="g_2b4f7a0@0" ObjectIDZND1="g_2b0ce10@0" Pin0InfoVect0LinkObjId="g_2b4f7a0_0" Pin0InfoVect1LinkObjId="g_2b0ce10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-102598_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4714,-313 4714,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bb60f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3949,-373 3949,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6772@0" ObjectIDZND0="6800@1" Pin0InfoVect0LinkObjId="SW-41128_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b994b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3949,-373 3949,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bb6310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3949,-312 3949,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6800@0" ObjectIDZND0="6799@1" Pin0InfoVect0LinkObjId="SW-41127_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41128_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3949,-312 3949,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bb8750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3949,-265 3949,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6799@0" ObjectIDZND0="6801@1" Pin0InfoVect0LinkObjId="SW-41129_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41127_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3949,-265 3949,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c1a550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4277,-312 4277,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6804@0" ObjectIDZND0="6811@1" Pin0InfoVect0LinkObjId="SW-41185_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41132_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4277,-312 4277,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b49d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4277,-265 4277,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6811@0" ObjectIDZND0="6805@1" Pin0InfoVect0LinkObjId="SW-41133_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41185_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4277,-265 4277,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b705b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4464,-312 4464,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6806@0" ObjectIDZND0="6812@1" Pin0InfoVect0LinkObjId="SW-41186_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41134_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4464,-312 4464,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b729c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4464,-265 4464,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6812@0" ObjectIDZND0="6807@1" Pin0InfoVect0LinkObjId="SW-41135_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41186_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4464,-265 4464,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b73770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4104,-313 4104,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6802@0" ObjectIDZND0="6810@1" Pin0InfoVect0LinkObjId="SW-41184_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41130_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4104,-313 4104,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b73df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4104,-266 4104,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6810@0" ObjectIDZND0="6803@1" Pin0InfoVect0LinkObjId="SW-41131_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41184_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4104,-266 4104,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bc9c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-620 4150,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="6787@x" ObjectIDND1="6808@x" ObjectIDZND0="6788@0" Pin0InfoVect0LinkObjId="SW-41116_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-41115_0" Pin1InfoVect1LinkObjId="g_2b1f7c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-620 4150,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bc9e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4186,-620 4204,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6788@1" ObjectIDZND0="g_2bdc170@0" Pin0InfoVect0LinkObjId="g_2bdc170_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41116_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4186,-620 4204,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bc9ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-619 4116,-635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="6788@x" ObjectIDND1="6808@x" ObjectIDZND0="6787@0" Pin0InfoVect0LinkObjId="SW-41115_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-41116_0" Pin1InfoVect1LinkObjId="g_2b1f7c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-619 4116,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bcac40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-603 4513,-665 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="6809@0" ObjectIDZND0="6791@0" Pin0InfoVect0LinkObjId="SW-41119_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bcf2e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-603 4513,-665 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bcaea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4513,-717 4547,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6791@x" ObjectIDND1="6792@x" ObjectIDZND0="6793@0" Pin0InfoVect0LinkObjId="SW-41121_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-41119_0" Pin1InfoVect1LinkObjId="SW-41120_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4513,-717 4547,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bcb100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4583,-717 4599,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6793@1" ObjectIDZND0="g_2bca6c0@0" Pin0InfoVect0LinkObjId="g_2bca6c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41121_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4583,-717 4599,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2abcc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-1024 4184,-1024 4184,-1009 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="6774@x" ObjectIDND1="0@x" ObjectIDND2="34590@1" ObjectIDZND0="g_2b4e4a0@0" Pin0InfoVect0LinkObjId="g_2b4e4a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-41102_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4225,-1024 4184,-1024 4184,-1009 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2abce20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3949,-187 3986,-187 3986,-169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="6801@x" ObjectIDND1="34515@x" ObjectIDZND0="g_2b4e850@0" Pin0InfoVect0LinkObjId="g_2b4e850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-41129_0" Pin1InfoVect1LinkObjId="EC-CX_XC.061Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3949,-187 3986,-187 3986,-169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2abd010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4144,-170 4144,-188 4104,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2b4ec30@0" ObjectIDZND0="6803@x" ObjectIDZND1="34516@x" Pin0InfoVect0LinkObjId="SW-41131_0" Pin0InfoVect1LinkObjId="EC-CX_XC.062Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b4ec30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4144,-170 4144,-188 4104,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2abd240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4277,-187 4277,-145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2b4efe0@0" ObjectIDND1="6805@x" ObjectIDZND0="34517@0" Pin0InfoVect0LinkObjId="EC-CX_XC.063Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b4efe0_0" Pin1InfoVect1LinkObjId="SW-41133_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4277,-187 4277,-145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2abdc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4277,-208 4277,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="6805@0" ObjectIDZND0="g_2b4efe0@0" ObjectIDZND1="34517@x" Pin0InfoVect0LinkObjId="g_2b4efe0_0" Pin0InfoVect1LinkObjId="EC-CX_XC.063Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41133_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4277,-208 4277,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2abde70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4277,-187 4314,-187 4314,-169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="6805@x" ObjectIDND1="34517@x" ObjectIDZND0="g_2b4efe0@0" Pin0InfoVect0LinkObjId="g_2b4efe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-41133_0" Pin1InfoVect1LinkObjId="EC-CX_XC.063Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4277,-187 4314,-187 4314,-169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2abe0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4464,-187 4464,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2b4f3c0@0" ObjectIDND1="6807@x" ObjectIDZND0="34518@0" Pin0InfoVect0LinkObjId="EC-CX_XC.064Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b4f3c0_0" Pin1InfoVect1LinkObjId="SW-41135_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4464,-187 4464,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b4dc90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4464,-208 4464,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="6807@0" ObjectIDZND0="g_2b4f3c0@0" ObjectIDZND1="34518@x" Pin0InfoVect0LinkObjId="g_2b4f3c0_0" Pin0InfoVect1LinkObjId="EC-CX_XC.064Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41135_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4464,-208 4464,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b4dee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4464,-187 4503,-187 4503,-169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="6807@x" ObjectIDND1="34518@x" ObjectIDZND0="g_2b4f3c0@0" Pin0InfoVect0LinkObjId="g_2b4f3c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-41135_0" Pin1InfoVect1LinkObjId="EC-CX_XC.064Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4464,-187 4503,-187 4503,-169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b79e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4714,-191 4714,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2b0ce10@0" ObjectIDZND0="g_2ba6040@0" Pin0InfoVect0LinkObjId="g_2ba6040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b0ce10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4714,-191 4714,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b92ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3819,-875 3819,-884 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2b92290@0" ObjectIDZND0="6780@0" Pin0InfoVect0LinkObjId="SW-41108_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b92290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3819,-875 3819,-884 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b9e340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4189,-875 4189,-884 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2b9d8f0@0" ObjectIDZND0="6776@0" Pin0InfoVect0LinkObjId="SW-41104_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b9d8f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4189,-875 4189,-884 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b9e5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-927 4189,-927 4189,-920 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6773@x" ObjectIDND1="6774@x" ObjectIDZND0="6776@1" Pin0InfoVect0LinkObjId="SW-41104_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-41101_0" Pin1InfoVect1LinkObjId="SW-41102_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4225,-927 4189,-927 4189,-920 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b9f090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-906 4225,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6773@1" ObjectIDZND0="6776@x" ObjectIDZND1="6774@x" Pin0InfoVect0LinkObjId="SW-41104_0" Pin0InfoVect1LinkObjId="SW-41102_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41101_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4225,-906 4225,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b9f2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-927 4225,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6773@x" ObjectIDND1="6776@x" ObjectIDZND0="6774@0" Pin0InfoVect0LinkObjId="SW-41102_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-41101_0" Pin1InfoVect1LinkObjId="SW-41104_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4225,-927 4225,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ba0cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-1076 4225,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="34590@1" ObjectIDZND0="6774@x" ObjectIDZND1="g_2b4e4a0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-41102_0" Pin0InfoVect1LinkObjId="g_2b4e4a0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4225,-1076 4225,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ba0f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-1024 4225,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_2b4e4a0@0" ObjectIDND1="0@x" ObjectIDND2="34590@1" ObjectIDZND0="6774@1" Pin0InfoVect0LinkObjId="SW-41102_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b4e4a0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4225,-1024 4225,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2afba50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-1024 3814,-1024 3814,-1009 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="6778@x" ObjectIDND1="0@x" ObjectIDND2="41851@x" ObjectIDZND0="g_2ba1ea0@0" Pin0InfoVect0LinkObjId="g_2ba1ea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-41106_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="EC-CX_XC.361Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-1024 3814,-1024 3814,-1009 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2afc760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-982 3855,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="6778@1" ObjectIDZND0="g_2ba1ea0@0" ObjectIDZND1="0@x" ObjectIDZND2="41851@x" Pin0InfoVect0LinkObjId="g_2ba1ea0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="EC-CX_XC.361Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41106_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-982 3855,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2afc9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-1024 3855,-1105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_2ba1ea0@0" ObjectIDND1="6778@x" ObjectIDND2="0@x" ObjectIDZND0="41851@0" Pin0InfoVect0LinkObjId="EC-CX_XC.361Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2ba1ea0_0" Pin1InfoVect1LinkObjId="SW-41106_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-1024 3855,-1105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b00260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4554,-875 4554,-884 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2aff620@0" ObjectIDZND0="6784@0" Pin0InfoVect0LinkObjId="SW-41112_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2aff620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4554,-875 4554,-884 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b004c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4590,-927 4554,-927 4554,-920 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6781@x" ObjectIDND1="6782@x" ObjectIDZND0="6784@1" Pin0InfoVect0LinkObjId="SW-41112_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-41109_0" Pin1InfoVect1LinkObjId="SW-41110_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4590,-927 4554,-927 4554,-920 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b01320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4590,-1024 4549,-1024 4549,-1009 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="6782@x" ObjectIDND1="0@x" ObjectIDND2="49190@1" ObjectIDZND0="g_2b00070@0" Pin0InfoVect0LinkObjId="g_2b00070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-41110_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_2b03cd0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4590,-1024 4549,-1024 4549,-1009 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b02b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4590,-906 4590,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6781@1" ObjectIDZND0="6782@x" ObjectIDZND1="6784@x" Pin0InfoVect0LinkObjId="SW-41110_0" Pin0InfoVect1LinkObjId="SW-41112_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41109_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4590,-906 4590,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b02d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4590,-927 4590,-946 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6781@x" ObjectIDND1="6784@x" ObjectIDZND0="6782@0" Pin0InfoVect0LinkObjId="SW-41110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-41109_0" Pin1InfoVect1LinkObjId="SW-41112_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4590,-927 4590,-946 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b03a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4590,-982 4590,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="6782@1" ObjectIDZND0="g_2b00070@0" ObjectIDZND1="0@x" ObjectIDZND2="49190@1" Pin0InfoVect0LinkObjId="g_2b00070_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2b03cd0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41110_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4590,-982 4590,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b03cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4590,-1024 4590,-1067 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2b00070@0" ObjectIDND1="6782@x" ObjectIDND2="0@x" ObjectIDZND0="49190@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b00070_0" Pin1InfoVect1LinkObjId="SW-41110_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4590,-1024 4590,-1067 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2af6380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3920,-671 3920,-685 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2af53a0@0" ObjectIDZND0="g_2b2b860@0" Pin0InfoVect0LinkObjId="g_2b2b860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2af53a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3920,-671 3920,-685 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2af65e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3886,-752 3886,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="busSection" ObjectIDND0="g_2b050a0@0" ObjectIDND1="g_2b2b860@0" ObjectIDZND0="6771@0" Pin0InfoVect0LinkObjId="g_2b76140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b050a0_0" Pin1InfoVect1LinkObjId="g_2b2b860_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3886,-752 3886,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2af70d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3920,-730 3920,-752 3886,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" EndDevType1="lightningRod" ObjectIDND0="g_2b2b860@1" ObjectIDZND0="6771@0" ObjectIDZND1="g_2b050a0@0" Pin0InfoVect0LinkObjId="g_2b76140_0" Pin0InfoVect1LinkObjId="g_2b050a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b2b860_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3920,-730 3920,-752 3886,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2af7330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3886,-752 3855,-752 3855,-722 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="6771@0" ObjectIDND1="g_2b2b860@0" ObjectIDZND0="g_2b050a0@0" Pin0InfoVect0LinkObjId="g_2b050a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b76140_0" Pin1InfoVect1LinkObjId="g_2b2b860_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3886,-752 3855,-752 3855,-722 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2af9740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4513,-692 4513,-708 4513,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6791@1" ObjectIDZND0="6793@x" ObjectIDZND1="6792@x" Pin0InfoVect0LinkObjId="SW-41121_0" Pin0InfoVect1LinkObjId="SW-41120_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41119_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4513,-692 4513,-708 4513,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2af9980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4513,-741 4513,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="6792@0" ObjectIDZND0="6793@x" ObjectIDZND1="6791@x" Pin0InfoVect0LinkObjId="SW-41121_0" Pin0InfoVect1LinkObjId="SW-41119_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4513,-741 4513,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b07d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3822,-246 3822,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2afabf0@0" ObjectIDZND0="g_2b0c4e0@0" Pin0InfoVect0LinkObjId="g_2b0c4e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2afabf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3822,-246 3822,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b07fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3822,-305 3822,-327 3788,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="busSection" ObjectIDND0="g_2b0c4e0@1" ObjectIDZND0="g_2af9e80@0" ObjectIDZND1="6772@0" Pin0InfoVect0LinkObjId="g_2af9e80_0" Pin0InfoVect1LinkObjId="g_2b994b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b0c4e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3822,-305 3822,-327 3788,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b08240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3788,-327 3757,-327 3757,-297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="busSection" EndDevType0="lightningRod" ObjectIDND0="g_2b0c4e0@0" ObjectIDND1="6772@0" ObjectIDZND0="g_2af9e80@0" Pin0InfoVect0LinkObjId="g_2af9e80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b0c4e0_0" Pin1InfoVect1LinkObjId="g_2b994b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3788,-327 3757,-327 3757,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b08e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3788,-327 3788,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="busSection" ObjectIDND0="g_2b0c4e0@0" ObjectIDND1="g_2af9e80@0" ObjectIDZND0="6772@0" Pin0InfoVect0LinkObjId="g_2b994b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b0c4e0_0" Pin1InfoVect1LinkObjId="g_2af9e80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3788,-327 3788,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bee450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3949,-135 3949,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34515@0" ObjectIDZND0="g_2b4e850@0" ObjectIDZND1="6801@x" Pin0InfoVect0LinkObjId="g_2b4e850_0" Pin0InfoVect1LinkObjId="SW-41129_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_XC.061Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3949,-135 3949,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bee640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3949,-187 3949,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2b4e850@0" ObjectIDND1="34515@x" ObjectIDZND0="6801@0" Pin0InfoVect0LinkObjId="SW-41129_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b4e850_0" Pin1InfoVect1LinkObjId="EC-CX_XC.061Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3949,-187 3949,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2beefe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4104,-135 4104,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34516@0" ObjectIDZND0="g_2b4ec30@0" ObjectIDZND1="6803@x" Pin0InfoVect0LinkObjId="g_2b4ec30_0" Pin0InfoVect1LinkObjId="SW-41131_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_XC.062Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4104,-135 4104,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bef220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4104,-188 4104,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2b4ec30@0" ObjectIDND1="34516@x" ObjectIDZND0="6803@0" Pin0InfoVect0LinkObjId="SW-41131_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b4ec30_0" Pin1InfoVect1LinkObjId="EC-CX_XC.062Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4104,-188 4104,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ac9220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-608 4334,-630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2bf59e0@0" ObjectIDZND0="g_2ac8850@1" Pin0InfoVect0LinkObjId="g_2ac8850_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bf59e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-608 4334,-630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ace550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-1024 3914,-1024 3914,-1010 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_2ba1ea0@0" ObjectIDND1="6778@x" ObjectIDND2="41851@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2ba1ea0_0" Pin1InfoVect1LinkObjId="SW-41106_0" Pin1InfoVect2LinkObjId="EC-CX_XC.361Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-1024 3914,-1024 3914,-1010 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ace7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3914,-965 3914,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2ba11b0@0" Pin0InfoVect0LinkObjId="g_2ba11b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3914,-965 3914,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ad1bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-1024 4284,-1024 4284,-1013 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="6774@x" ObjectIDND1="g_2b4e4a0@0" ObjectIDND2="34590@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-41102_0" Pin1InfoVect1LinkObjId="g_2b4e4a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4225,-1024 4284,-1024 4284,-1013 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ad1e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4284,-968 4284,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2b9f550@0" Pin0InfoVect0LinkObjId="g_2b9f550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4284,-968 4284,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aecfd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4590,-1024 4649,-1024 4649,-1013 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_2b00070@0" ObjectIDND1="6782@x" ObjectIDND2="49190@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b00070_0" Pin1InfoVect1LinkObjId="SW-41110_0" Pin1InfoVect2LinkObjId="g_2b03cd0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4590,-1024 4649,-1024 4649,-1013 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aed230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4649,-968 4649,-951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2b01580@0" Pin0InfoVect0LinkObjId="g_2b01580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4649,-968 4649,-951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aefc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4781,-397 4781,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="6772@0" Pin0InfoVect0LinkObjId="g_2b994b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4781,-397 4781,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2af2210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4820,-433 4781,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4820,-433 4781,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2af4c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4895,-433 4847,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4895,-433 4847,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2af4e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4895,-397 4895,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4895,-397 4895,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b87490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4607,-315 4607,-295 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="20499@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4607,-315 4607,-295 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b89ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4607,-268 4607,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20499@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4607,-268 4607,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b8a130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4607,-211 4607,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2b8a5f0@0" ObjectIDZND1="g_2b8bc30@0" Pin0InfoVect0LinkObjId="g_2b8a5f0_0" Pin0InfoVect1LinkObjId="g_2b8bc30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4607,-211 4607,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b8a390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4607,-190 4646,-190 4646,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_2b8bc30@0" ObjectIDZND0="g_2b8a5f0@0" Pin0InfoVect0LinkObjId="g_2b8a5f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2b8bc30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4607,-190 4646,-190 4646,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b8c6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4607,-190 4607,-170 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_2b8a5f0@0" ObjectIDZND0="g_2b8bc30@1" Pin0InfoVect0LinkObjId="g_2b8bc30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2b8a5f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4607,-190 4607,-170 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b8c910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4607,-136 4607,-114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2b8bc30@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b8bc30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4607,-136 4607,-114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b8cb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4104,-349 4104,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6802@1" ObjectIDZND0="6772@0" Pin0InfoVect0LinkObjId="g_2b994b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41130_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4104,-349 4104,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aab000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4277,-348 4277,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6804@1" ObjectIDZND0="6772@0" Pin0InfoVect0LinkObjId="g_2b994b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41132_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4277,-348 4277,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aab830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4464,-348 4464,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6806@1" ObjectIDZND0="6772@0" Pin0InfoVect0LinkObjId="g_2b994b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41134_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4464,-348 4464,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aac060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4607,-351 4607,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="6772@0" Pin0InfoVect0LinkObjId="g_2b994b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4607,-351 4607,-373 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="6772" cx="4512" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6772" cx="4714" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6772" cx="3949" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6772" cx="3788" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6771" cx="3855" cy="-798" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6771" cx="4590" cy="-798" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6771" cx="4513" cy="-798" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6771" cx="4334" cy="-798" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6771" cx="4116" cy="-798" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6771" cx="3886" cy="-798" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6772" cx="4117" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6771" cx="4225" cy="-798" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6772" cx="4781" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6772" cx="4104" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6772" cx="4277" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6772" cx="4464" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6772" cx="4607" cy="-373" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-37336" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3378.000000 -1093.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5918" ObjectName="DYN-CX_XC"/>
     <cge:Meas_Ref ObjectId="37336"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2aaa860" transform="matrix(1.000000 0.000000 0.000000 1.000000 3798.000000 -1152.000000) translate(0,15)">35kV密新线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c46280" transform="matrix(1.000000 0.000000 0.000000 1.000000 4167.000000 -1151.000000) translate(0,15)">35kV大新舍线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_296b330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3900.000000 -200.000000) translate(0,15)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_296b330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3900.000000 -200.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_296b330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3900.000000 -200.000000) translate(0,51)">马</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_296b330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3900.000000 -200.000000) translate(0,69)">鞍</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_296b330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3900.000000 -200.000000) translate(0,87)">山</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_296b330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3900.000000 -200.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b2a5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4647.000000 -101.000000) translate(0,15)">10kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c46070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4246.000000 -565.000000) translate(0,15)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c48710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3865.000000 -900.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b9c360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3862.000000 -845.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c0f800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3669.000000 -792.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c0ffc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3768.000000 -906.000000) translate(0,12)">36160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b475d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3862.000000 -971.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b477b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4234.000000 -900.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b47990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4232.000000 -971.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b47d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4232.000000 -845.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b47f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4600.000000 -900.000000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b48110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -971.000000) translate(0,12)">3636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c007f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -845.000000) translate(0,12)">3631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bb8970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4232.000000 -207.000000) translate(0,15)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bb8970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4232.000000 -207.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bb8970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4232.000000 -207.000000) translate(0,51)">红</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bb8970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4232.000000 -207.000000) translate(0,69)">星</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bb8970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4232.000000 -207.000000) translate(0,87)">树</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bb8970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4232.000000 -207.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b49f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4426.000000 -206.000000) translate(0,15)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b49f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4426.000000 -206.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b49f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4426.000000 -206.000000) translate(0,51)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b49f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4426.000000 -206.000000) translate(0,69)">地</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b49f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4426.000000 -206.000000) translate(0,87)">基</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b49f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4426.000000 -206.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b72be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4067.000000 -180.000000) translate(0,15)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b72be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4067.000000 -180.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b72be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4067.000000 -180.000000) translate(0,51)">西</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b72be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4067.000000 -180.000000) translate(0,69)">叉</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b72be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4067.000000 -180.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc7e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4126.000000 -714.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc83b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4123.000000 -766.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc85f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4147.000000 -643.000000) translate(0,12)">30167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc8830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4123.000000 -660.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc8c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4127.000000 -485.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc8e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4124.000000 -430.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2bc90d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3634.000000 -367.000000) translate(0,15)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc9310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4524.000000 -490.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc9550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4521.000000 -430.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc9790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4529.000000 -689.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc99d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4520.000000 -767.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bcb360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4545.000000 -740.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bcb850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4341.000000 -764.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b51510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4364.000000 -741.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b51750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4424.000000 -571.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b51990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3958.000000 -286.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b51e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3954.000000 -338.000000) translate(0,12)">0611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b52060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3955.000000 -233.000000) translate(0,12)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b522a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4116.000000 -287.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b524e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4113.000000 -234.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b52720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4112.000000 -339.000000) translate(0,12)">0621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b52960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4286.000000 -286.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b52ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4282.000000 -338.000000) translate(0,12)">0631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b52de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4283.000000 -233.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b53020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4475.000000 -286.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b53260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4471.000000 -338.000000) translate(0,12)">0641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b53680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4472.000000 -233.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b538c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4721.000000 -338.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2b4fb80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3280.500000 -1168.500000) translate(0,16)">新村变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b50770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -590.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b50770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -590.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b50770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -590.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b50770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -590.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b50770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -590.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b50770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -590.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b50770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -590.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b50770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -590.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b50770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -590.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b50770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -590.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b50770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -590.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b50770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -590.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b50770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -590.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b50770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -590.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b50770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -590.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b50770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -590.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b50770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -590.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b50770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -590.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b46920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1028.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b46920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1028.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b46920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1028.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b46920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1028.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b46920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1028.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b46920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1028.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b46920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1028.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2b7a0c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3252.000000 -241.000000) translate(0,17)">3817049</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b92f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4138.000000 -906.000000) translate(0,12)">36260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afcc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4503.000000 -906.000000) translate(0,12)">36360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2af7590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3835.000000 -653.000000) translate(0,15)">35kV1号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2af7590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3835.000000 -653.000000) translate(0,33)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2af7590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3835.000000 -653.000000) translate(0,51)">S9-50kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b084a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3735.000000 -222.000000) translate(0,15)">10kV2号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b084a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3735.000000 -222.000000) translate(0,33)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b084a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3735.000000 -222.000000) translate(0,51)">SH15-50kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_2b2c410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4145.000000 -558.000000) translate(0,13)">S9-1250/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" graphid="g_2b2e740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4402.000000 -552.000000) translate(0,13)">SZ9-3150/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2b2ed50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3252.000000 -221.000000) translate(0,17)">4783</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ac2e30" transform="matrix(1.000000 0.000000 0.000000 1.000000 4843.000000 -799.000000) translate(0,15)">ATSⅠ位</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ac3930" transform="matrix(1.000000 0.000000 0.000000 1.000000 5041.000000 -799.000000) translate(0,15)">ATSⅡ位</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ac3f90" transform="matrix(1.000000 0.000000 0.000000 1.000000 4871.000000 -953.000000) translate(0,15)">35kV站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ac4210" transform="matrix(1.000000 0.000000 0.000000 1.000000 4984.000000 -953.000000) translate(0,15)">10kV站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ac4450" transform="matrix(1.000000 0.000000 0.000000 1.000000 4898.000000 -934.000000) translate(0,15)">进线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ac4970" transform="matrix(1.000000 0.000000 0.000000 1.000000 5011.000000 -934.000000) translate(0,15)">进线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ac4bf0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4847.500000 -818.000000) translate(0,15)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ac4e30" transform="matrix(1.000000 0.000000 0.000000 1.000000 5045.500000 -818.000000) translate(0,15)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bf0500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3172.000000 -741.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2bf1590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3477.000000 -1155.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2b462f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3477.000000 -1190.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b59200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4716.000000 -431.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b596f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4821.000000 -461.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b59930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4897.000000 -423.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b59c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4635.000000 -768.000000) translate(0,12)">此接地开关上传信号异常,</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b59c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4635.000000 -768.000000) translate(0,27)">双遥信点测值均为0.</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2b5d770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3121.000000 -179.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2b5d770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3121.000000 -179.000000) translate(0,38)">心变运三班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2b5fef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3252.000000 -189.500000) translate(0,17)">18787878955</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2b5fef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3252.000000 -189.500000) translate(0,38)">18787878953</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2b5fef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3252.000000 -189.500000) translate(0,59)">18787878979</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2b60790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3373.500000 -1071.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b61f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3951.000000 -561.000000) translate(0,12)">油温(℃):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b64900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4151.000000 -583.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_2b81680" transform="matrix(0.666667 -0.000000 -0.000000 0.666667 3165.333333 -705.333333) translate(0,20)">隔刀远控</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b828a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4569.000000 -191.000000) translate(0,15)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b828a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4569.000000 -191.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b828a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4569.000000 -191.000000) translate(0,51)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b828a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4569.000000 -191.000000) translate(0,69)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b828a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4569.000000 -191.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aac890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4617.000000 -289.000000) translate(0,12)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aacea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4623.000000 -336.000000) translate(0,12)">0651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aad2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4625.000000 -236.000000) translate(0,12)">0656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2aada40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4546.000000 -1158.000000) translate(0,15)">35kV新地线</text>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4513,-646 4508,-656 4518,-656 4513,-646 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4513,-626 4508,-616 4518,-616 4513,-626 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3855,-1080 3850,-1090 3860,-1090 3855,-1080 " stroke="rgb(255,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3855,-1060 3850,-1050 3860,-1050 3855,-1060 " stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2ba6040">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4695.000000 -103.000000)" xlink:href="#voltageTransformer:shape72"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bf59e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4315.000000 -538.000000)" xlink:href="#voltageTransformer:shape72"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_XC.CX_XC_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="10003"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4474.000000 -518.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4474.000000 -518.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="6809" ObjectName="TF-CX_XC.CX_XC_2T"/>
    <cge:TPSR_Ref TObjectID="6809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_XC.CX_XC_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="9999"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4091.000000 -515.000000)" xlink:href="#transformer2:shape43_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4091.000000 -515.000000)" xlink:href="#transformer2:shape43_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="6808" ObjectName="TF-CX_XC.CX_XC_1T"/>
    <cge:TPSR_Ref TObjectID="6808"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-102152" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4024.000000 -561.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102152" ObjectName="CX_XC:CX_XC_1T_Tmp1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-102163" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4647.000000 -579.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102163" ObjectName="CX_XC:CX_XC_2T_Tmp1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3189.500000 -1120.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-102165" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4699.000000 -596.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102165" ObjectName="CX_XC:CX_XC_2T_Tap"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-200699" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3246.538462 -989.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200699" ObjectName="CX_XC:CX_XC_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-200700" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3246.538462 -946.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200700" ObjectName="CX_XC:CX_XC_sumQ"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="3240" y="-1179"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="3240" y="-1179"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3192" y="-1196"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3192" y="-1196"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3958" y="-286"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3958" y="-286"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4116" y="-287"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4116" y="-287"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4286" y="-286"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4286" y="-286"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4475" y="-286"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4475" y="-286"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3865" y="-900"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3865" y="-900"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4234" y="-900"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4234" y="-900"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4600" y="-900"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4600" y="-900"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="62" x="4424" y="-571"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="62" x="4424" y="-571"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="60" x="3172" y="-741"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="60" x="3172" y="-741"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3466" y="-1163"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3466" y="-1163"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3466" y="-1198"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3466" y="-1198"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="3356" y="-1083"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="3356" y="-1083"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="63" x="4151" y="-583"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="63" x="4151" y="-583"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="16" qtmmishow="hidden" width="64" x="3164" y="-706"/>
    </a>
   <metadata/><rect fill="white" height="16" opacity="0" stroke="white" transform="" width="64" x="3164" y="-706"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c110e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3905.000000 1152.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b38820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3894.000000 1137.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c0f660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3919.000000 1122.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c00bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4302.000000 1152.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c00d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4291.000000 1137.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c00f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4316.000000 1122.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c01330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4660.000000 1150.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c01510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4649.000000 1135.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c016f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4674.000000 1120.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b54110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3911.000000 84.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b543d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3900.000000 69.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b54610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3925.000000 54.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b54a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4076.000000 84.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b54cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4065.000000 69.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b54f30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4090.000000 54.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aba650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4257.000000 84.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aba8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4246.000000 69.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2abab20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4271.000000 54.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2abaf40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4446.000000 84.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2abb200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4435.000000 69.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2abb440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4460.000000 54.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2abb860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4169.000000 497.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2abbb20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4158.000000 482.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2abbd60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4183.000000 467.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2abc180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4568.000000 501.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2abc440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4557.000000 486.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2abc680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4582.000000 471.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b09d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3640.000000 838.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b0a5e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3634.000000 853.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b0ae60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3634.000000 867.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b0b3c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3626.000000 823.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b0b640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3634.000000 882.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b0b970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3696.000000 412.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b0bbe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3690.000000 427.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b0be20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3690.000000 441.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b0c060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3682.000000 397.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b0c2a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3690.000000 456.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac5e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4164.000000 727.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac6460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4189.000000 712.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac66a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4194.000000 696.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac71e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4175.000000 742.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac7550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4569.000000 678.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac77c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4594.000000 663.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bed0a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4599.000000 647.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bed2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4580.000000 693.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b62e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4590.000000 580.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b63110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4590.000000 595.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_XC" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_duxinsheTxc" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4225,-1130 4225,-1076 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34590" ObjectName="AC-35kV.LN_duxinsheTxc"/>
    <cge:TPSR_Ref TObjectID="34590_SS-67"/></metadata>
   <polyline fill="none" opacity="0" points="4225,-1130 4225,-1076 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_DDJ" endPointId="0" endStationName="CX_XC" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_XinDi" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4590,-1067 4590,-1129 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="49190" ObjectName="AC-35kV.LN_XinDi"/>
    <cge:TPSR_Ref TObjectID="49190_SS-67"/></metadata>
   <polyline fill="none" opacity="0" points="4590,-1067 4590,-1129 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-41059" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3749.000000 -455.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41059" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6772"/>
     <cge:Term_Ref ObjectID="9829"/>
    <cge:TPSR_Ref TObjectID="6772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-41060" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3749.000000 -455.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41060" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6772"/>
     <cge:Term_Ref ObjectID="9829"/>
    <cge:TPSR_Ref TObjectID="6772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-41061" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3749.000000 -455.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41061" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6772"/>
     <cge:Term_Ref ObjectID="9829"/>
    <cge:TPSR_Ref TObjectID="6772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-41062" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3749.000000 -455.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41062" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6772"/>
     <cge:Term_Ref ObjectID="9829"/>
    <cge:TPSR_Ref TObjectID="6772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-41071" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3749.000000 -455.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41071" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6772"/>
     <cge:Term_Ref ObjectID="9829"/>
    <cge:TPSR_Ref TObjectID="6772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-41038" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3694.000000 -882.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41038" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6771"/>
     <cge:Term_Ref ObjectID="9828"/>
    <cge:TPSR_Ref TObjectID="6771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-41039" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3694.000000 -882.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41039" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6771"/>
     <cge:Term_Ref ObjectID="9828"/>
    <cge:TPSR_Ref TObjectID="6771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-41040" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3694.000000 -882.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41040" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6771"/>
     <cge:Term_Ref ObjectID="9828"/>
    <cge:TPSR_Ref TObjectID="6771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-41041" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3694.000000 -882.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41041" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6771"/>
     <cge:Term_Ref ObjectID="9828"/>
    <cge:TPSR_Ref TObjectID="6771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-41050" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3694.000000 -882.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41050" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6771"/>
     <cge:Term_Ref ObjectID="9828"/>
    <cge:TPSR_Ref TObjectID="6771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-41032" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3968.000000 -1152.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41032" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6777"/>
     <cge:Term_Ref ObjectID="9870"/>
    <cge:TPSR_Ref TObjectID="6777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-41033" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3968.000000 -1152.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41033" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6777"/>
     <cge:Term_Ref ObjectID="9870"/>
    <cge:TPSR_Ref TObjectID="6777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-41028" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3968.000000 -1152.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41028" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6777"/>
     <cge:Term_Ref ObjectID="9870"/>
    <cge:TPSR_Ref TObjectID="6777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-41017" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4363.000000 -1153.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41017" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6773"/>
     <cge:Term_Ref ObjectID="9854"/>
    <cge:TPSR_Ref TObjectID="6773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-41018" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4363.000000 -1153.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41018" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6773"/>
     <cge:Term_Ref ObjectID="9854"/>
    <cge:TPSR_Ref TObjectID="6773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-41013" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4363.000000 -1153.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41013" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6773"/>
     <cge:Term_Ref ObjectID="9854"/>
    <cge:TPSR_Ref TObjectID="6773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-41046" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4720.000000 -1151.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41046" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6781"/>
     <cge:Term_Ref ObjectID="9862"/>
    <cge:TPSR_Ref TObjectID="6781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-41047" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4720.000000 -1151.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41047" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6781"/>
     <cge:Term_Ref ObjectID="9862"/>
    <cge:TPSR_Ref TObjectID="6781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-41042" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4720.000000 -1151.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41042" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6781"/>
     <cge:Term_Ref ObjectID="9862"/>
    <cge:TPSR_Ref TObjectID="6781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-41067" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4231.000000 -496.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41067" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6794"/>
     <cge:Term_Ref ObjectID="9840"/>
    <cge:TPSR_Ref TObjectID="6794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-41068" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4231.000000 -496.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41068" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6794"/>
     <cge:Term_Ref ObjectID="9840"/>
    <cge:TPSR_Ref TObjectID="6794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-41063" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4231.000000 -496.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41063" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6794"/>
     <cge:Term_Ref ObjectID="9840"/>
    <cge:TPSR_Ref TObjectID="6794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-41088" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4626.000000 -499.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41088" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6796"/>
     <cge:Term_Ref ObjectID="9850"/>
    <cge:TPSR_Ref TObjectID="6796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-41089" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4626.000000 -499.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41089" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6796"/>
     <cge:Term_Ref ObjectID="9850"/>
    <cge:TPSR_Ref TObjectID="6796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-41084" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4626.000000 -499.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41084" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6796"/>
     <cge:Term_Ref ObjectID="9850"/>
    <cge:TPSR_Ref TObjectID="6796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-41097" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3973.000000 -83.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41097" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6799"/>
     <cge:Term_Ref ObjectID="9896"/>
    <cge:TPSR_Ref TObjectID="6799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-41098" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3973.000000 -83.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41098" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6799"/>
     <cge:Term_Ref ObjectID="9896"/>
    <cge:TPSR_Ref TObjectID="6799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-41094" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3973.000000 -83.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41094" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6799"/>
     <cge:Term_Ref ObjectID="9896"/>
    <cge:TPSR_Ref TObjectID="6799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-58427" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4135.000000 -83.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58427" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6810"/>
     <cge:Term_Ref ObjectID="9890"/>
    <cge:TPSR_Ref TObjectID="6810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-58428" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4135.000000 -83.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58428" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6810"/>
     <cge:Term_Ref ObjectID="9890"/>
    <cge:TPSR_Ref TObjectID="6810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-58416" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4135.000000 -83.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58416" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6810"/>
     <cge:Term_Ref ObjectID="9890"/>
    <cge:TPSR_Ref TObjectID="6810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-58425" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4314.000000 -84.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58425" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6811"/>
     <cge:Term_Ref ObjectID="9884"/>
    <cge:TPSR_Ref TObjectID="6811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-58426" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4314.000000 -84.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58426" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6811"/>
     <cge:Term_Ref ObjectID="9884"/>
    <cge:TPSR_Ref TObjectID="6811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-58422" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4314.000000 -84.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58422" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6811"/>
     <cge:Term_Ref ObjectID="9884"/>
    <cge:TPSR_Ref TObjectID="6811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-58439" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4504.000000 -82.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58439" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6812"/>
     <cge:Term_Ref ObjectID="9878"/>
    <cge:TPSR_Ref TObjectID="6812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-58440" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4504.000000 -82.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58440" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6812"/>
     <cge:Term_Ref ObjectID="9878"/>
    <cge:TPSR_Ref TObjectID="6812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-58436" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4504.000000 -82.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58436" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6812"/>
     <cge:Term_Ref ObjectID="9878"/>
    <cge:TPSR_Ref TObjectID="6812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-41056" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4233.000000 -741.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41056" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6785"/>
     <cge:Term_Ref ObjectID="9832"/>
    <cge:TPSR_Ref TObjectID="6785"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-41057" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4233.000000 -741.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41057" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6785"/>
     <cge:Term_Ref ObjectID="9832"/>
    <cge:TPSR_Ref TObjectID="6785"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-41053" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4233.000000 -741.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41053" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6785"/>
     <cge:Term_Ref ObjectID="9832"/>
    <cge:TPSR_Ref TObjectID="6785"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-102149" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4233.000000 -741.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102149" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6785"/>
     <cge:Term_Ref ObjectID="9832"/>
    <cge:TPSR_Ref TObjectID="6785"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-41077" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4638.000000 -694.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41077" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6791"/>
     <cge:Term_Ref ObjectID="9844"/>
    <cge:TPSR_Ref TObjectID="6791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-41078" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4638.000000 -694.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41078" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6791"/>
     <cge:Term_Ref ObjectID="9844"/>
    <cge:TPSR_Ref TObjectID="6791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-41074" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4638.000000 -694.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41074" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6791"/>
     <cge:Term_Ref ObjectID="9844"/>
    <cge:TPSR_Ref TObjectID="6791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-102160" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4638.000000 -694.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="102160" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6791"/>
     <cge:Term_Ref ObjectID="9844"/>
    <cge:TPSR_Ref TObjectID="6791"/></metadata>
   </g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-41105">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3846.333333 -871.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6777" ObjectName="SW-CX_XC.CX_XC_361BK"/>
     <cge:Meas_Ref ObjectId="41105"/>
    <cge:TPSR_Ref TObjectID="6777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41101">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4215.833333 -871.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6773" ObjectName="SW-CX_XC.CX_XC_362BK"/>
     <cge:Meas_Ref ObjectId="41101"/>
    <cge:TPSR_Ref TObjectID="6773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41109">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4581.333333 -871.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6781" ObjectName="SW-CX_XC.CX_XC_363BK"/>
     <cge:Meas_Ref ObjectId="41109"/>
    <cge:TPSR_Ref TObjectID="6781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41124">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4503.200000 -462.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6796" ObjectName="SW-CX_XC.CX_XC_002BK"/>
     <cge:Meas_Ref ObjectId="41124"/>
    <cge:TPSR_Ref TObjectID="6796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41119">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4504.333333 -657.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6791" ObjectName="SW-CX_XC.CX_XC_302BK"/>
     <cge:Meas_Ref ObjectId="41119"/>
    <cge:TPSR_Ref TObjectID="6791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41122">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4108.200000 -456.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6794" ObjectName="SW-CX_XC.CX_XC_001BK"/>
     <cge:Meas_Ref ObjectId="41122"/>
    <cge:TPSR_Ref TObjectID="6794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41113">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4107.333333 -685.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6785" ObjectName="SW-CX_XC.CX_XC_301BK"/>
     <cge:Meas_Ref ObjectId="41113"/>
    <cge:TPSR_Ref TObjectID="6785"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41127">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3940.333333 -257.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6799" ObjectName="SW-CX_XC.CX_XC_061BK"/>
     <cge:Meas_Ref ObjectId="41127"/>
    <cge:TPSR_Ref TObjectID="6799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41185">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4268.333333 -257.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6811" ObjectName="SW-CX_XC.CX_XC_063BK"/>
     <cge:Meas_Ref ObjectId="41185"/>
    <cge:TPSR_Ref TObjectID="6811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41186">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4455.333333 -257.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6812" ObjectName="SW-CX_XC.CX_XC_064BK"/>
     <cge:Meas_Ref ObjectId="41186"/>
    <cge:TPSR_Ref TObjectID="6812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41184">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4095.333333 -258.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6810" ObjectName="SW-CX_XC.CX_XC_062BK"/>
     <cge:Meas_Ref ObjectId="41184"/>
    <cge:TPSR_Ref TObjectID="6810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109193">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4907.000000 -773.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21235" ObjectName="SW-CX_XC.CX_XC_ZYB1BK"/>
     <cge:Meas_Ref ObjectId="109193"/>
    <cge:TPSR_Ref TObjectID="21235"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109194">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5019.000000 -773.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21236" ObjectName="SW-CX_XC.CX_XC_ZYB2BK"/>
     <cge:Meas_Ref ObjectId="109194"/>
    <cge:TPSR_Ref TObjectID="21236"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4811.000000 -423.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4598.333333 -260.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20499" ObjectName="SW-CX_XC.CX_XC_065BK"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="20499"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="3240" y="-1179"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3192" y="-1196"/></g>
   <g href="35kV新村变10kV马鞍山线061断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3958" y="-286"/></g>
   <g href="35kV新村变10kV西叉线062断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4116" y="-287"/></g>
   <g href="35kV新村变10kV红星树线063断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4286" y="-286"/></g>
   <g href="35kV新村变10kV大地基线064断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4475" y="-286"/></g>
   <g href="35kV新村变35kV密新线361断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3865" y="-900"/></g>
   <g href="35kV新村变35kV杜新舍线362断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4234" y="-900"/></g>
   <g href="35kV新村变35kV新地线363断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4600" y="-900"/></g>
   <g href="35kV新村变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="62" x="4424" y="-571"/></g>
   <g href="35kV新村变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="60" x="3172" y="-741"/></g>
   <g href="cx_配调_配网接线图35_楚雄.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3466" y="-1163"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3466" y="-1198"/></g>
   <g href="AVC新村站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="3356" y="-1083"/></g>
   <g href="35kV新村变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="63" x="4151" y="-583"/></g>
   <g href="35kV新村变隔刀开关远方遥控清单.svg" style="fill-opacity:0"><rect height="16" qtmmishow="hidden" width="64" x="3164" y="-706"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2b4e140">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4375.000000 -609.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b4e4a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4177.000000 -951.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b4e850">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3979.000000 -111.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b4ec30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4137.000000 -112.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b4efe0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4307.000000 -111.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b4f3c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4496.000000 -111.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b4f7a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4755.000000 -183.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b9f550">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4276.000000 -876.000000)" xlink:href="#lightningRod:shape85"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ba11b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3906.000000 -872.000000)" xlink:href="#lightningRod:shape85"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ba1ea0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3807.000000 -951.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b00070">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4542.000000 -951.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b01580">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4641.000000 -878.000000)" xlink:href="#lightningRod:shape85"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b050a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3848.000000 -664.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2af53a0">
    <use class="BV-35KV" transform="matrix(0.714286 -0.000000 0.000000 -0.750000 3905.000000 -618.000000)" xlink:href="#lightningRod:shape170"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2af9e80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3750.000000 -239.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2afabf0">
    <use class="BV-10KV" transform="matrix(0.714286 -0.000000 0.000000 -0.750000 3807.000000 -193.000000)" xlink:href="#lightningRod:shape170"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b0c4e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3817.000000 -255.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b0ce10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4709.000000 -186.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b2b860">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3915.000000 -680.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ac8850">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4325.000000 -625.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b8a5f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4639.000000 -114.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b8bc30">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4597.000000 -131.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_XC"/>
</svg>