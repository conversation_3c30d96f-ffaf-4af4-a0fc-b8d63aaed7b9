<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-233" aopId="3934726" id="thSvg" product="E8000V2" version="1.0" viewBox="-754 -1274 2112 1137">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape45">
    <polyline arcFlag="1" points="19,100 17,100 15,99 14,99 12,98 11,97 9,96 8,94 7,92 7,91 6,89 6,87 6,85 7,83 7,82 8,80 9,79 11,77 12,76 14,75 15,75 17,74 19,74 21,74 23,75 24,75 26,76 27,77 29,79 30,80 31,82 31,83 32,85 32,87 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="32" x2="19" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="19" x2="19" y1="100" y2="111"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="14" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="55" y2="47"/>
    <polyline arcFlag="1" points="36,14 37,14 38,14 38,14 39,14 39,15 40,15 40,16 41,16 41,17 41,17 41,18 42,19 42,19 42,20 41,21 41,21 41,22 41,22 40,23 40,23 39,24 39,24 38,24 38,25 37,25 36,25 " stroke-width="1"/>
    <polyline arcFlag="1" points="36,36 37,36 38,36 38,37 39,37 39,37 40,38 40,38 41,39 41,39 41,40 41,40 42,41 42,42 42,42 41,43 41,44 41,44 41,45 40,45 40,46 39,46 39,47 38,47 38,47 37,47 36,47 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.423529" x1="19" x2="19" y1="87" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="2" x2="35" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44164" x1="19" x2="36" y1="8" y2="8"/>
    <rect height="23" stroke-width="0.369608" width="12" x="13" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.356919" x1="19" x2="36" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="2" x2="2" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="35" x2="35" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="19" x2="19" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="10" x2="26" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="11" x2="26" y1="21" y2="21"/>
    <polyline arcFlag="1" points="36,25 37,25 38,25 38,25 39,26 39,26 40,26 40,27 41,27 41,28 41,29 41,29 42,30 42,31 42,31 41,32 41,33 41,33 41,34 40,34 40,35 39,35 39,35 38,36 38,36 37,36 36,36 " stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape30_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape36_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
   </symbol>
   <symbol id="switch2:shape36_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="5" y1="39" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-16" x2="-4" y1="31" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-4" x2="3" y1="18" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="3" y1="38" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="-16" y1="38" y2="31"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="25" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,49 16,27 28,27 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="29"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="27" y2="27"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="7"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,6 16,28 28,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="30" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape75_0">
    <circle cx="15" cy="85" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="62" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="62" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="10" y1="62" y2="67"/>
    <circle cx="15" cy="63" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="48" y2="6"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,62 49,62 49,33 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="49" y1="29" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="54" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="46" x2="52" y1="31" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="48" x2="50" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.382653" x1="16" x2="9" y1="81" y2="93"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.382653" x1="16" x2="23" y1="81" y2="93"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.382653" x1="9" x2="23" y1="93" y2="93"/>
   </symbol>
   <symbol id="transformer2:shape75_1"/>
   <symbol id="transformer2:shape56_0">
    <circle cx="16" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="16,43 41,43 41,72 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="57" y2="99"/>
    <polyline DF8003:Layer="PUBLIC" points="16,84 22,71 9,71 16,84 16,83 16,84 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="35" x2="47" y1="72" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="45" x2="37" y1="75" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="39" y1="78" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="16" y1="54" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="43" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="43" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="43" y2="38"/>
   </symbol>
   <symbol id="transformer2:shape56_1">
    <circle cx="16" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="12" y1="14" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="20" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="16" y1="20" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape13_0">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="38" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape13_1">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="46" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="29" y1="34" y2="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape21">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="27" y1="11" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="11" y2="5"/>
    <circle cx="27" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="13" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="voltageTransformer:shape138">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="38" y1="43" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="41" y1="43" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="39" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="40" y1="45" y2="45"/>
    <ellipse cx="8" cy="13" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="8" cy="25" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="16" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="9" y1="14" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="9" y1="12" y2="10"/>
    <ellipse cx="21" cy="13" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="8" y1="29" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="8" y1="29" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="27" y2="24"/>
    <ellipse cx="21" cy="25" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="42" y1="21" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="42" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="21" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="38" y1="14" y2="20"/>
    <rect height="13" stroke-width="1" width="5" x="35" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="21" y1="16" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="21" y1="16" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="21" y1="14" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="21" y1="29" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="21" y1="29" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="21" y1="27" y2="24"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34cbdc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34cc770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34cd120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34cde00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34cf000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34cfc10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34d0470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34d0e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34d16f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="55" stroke="rgb(255,0,0)" stroke-width="9.28571" width="98" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34d20d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34d20d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,35)">二种工作</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34d3ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34d3ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_34d4eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34d6bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34d7780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34d8070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34d89b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34da0c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34da890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34daf80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34db9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34dcb80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34dd500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34ddff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34e32c0" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34e3f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34dfcd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34e1150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_34e1bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_34e5380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_34e67e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1147" width="2122" x="-759" y="-1279"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-156294">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 407.241796 -770.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26362" ObjectName="SW-DY_YLP.DY_YLP_301BK"/>
     <cge:Meas_Ref ObjectId="156294"/>
    <cge:TPSR_Ref TObjectID="26362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156355">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 407.241796 -617.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26365" ObjectName="SW-DY_YLP.DY_YLP_001BK"/>
     <cge:Meas_Ref ObjectId="156355"/>
    <cge:TPSR_Ref TObjectID="26365"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156243">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -59.758204 -989.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26354" ObjectName="SW-DY_YLP.DY_YLP_371BK"/>
     <cge:Meas_Ref ObjectId="156243"/>
    <cge:TPSR_Ref TObjectID="26354"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156413">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -248.787729 -392.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26367" ObjectName="SW-DY_YLP.DY_YLP_071BK"/>
     <cge:Meas_Ref ObjectId="156413"/>
    <cge:TPSR_Ref TObjectID="26367"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156452">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -26.787729 -390.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26370" ObjectName="SW-DY_YLP.DY_YLP_072BK"/>
     <cge:Meas_Ref ObjectId="156452"/>
    <cge:TPSR_Ref TObjectID="26370"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156491">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 216.212271 -388.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26373" ObjectName="SW-DY_YLP.DY_YLP_073BK"/>
     <cge:Meas_Ref ObjectId="156491"/>
    <cge:TPSR_Ref TObjectID="26373"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156530">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 452.212271 -387.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26376" ObjectName="SW-DY_YLP.DY_YLP_074BK"/>
     <cge:Meas_Ref ObjectId="156530"/>
    <cge:TPSR_Ref TObjectID="26376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156569">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 711.212271 -385.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26379" ObjectName="SW-DY_YLP.DY_YLP_075BK"/>
     <cge:Meas_Ref ObjectId="156569"/>
    <cge:TPSR_Ref TObjectID="26379"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156608">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 967.212271 -385.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26382" ObjectName="SW-DY_YLP.DY_YLP_076BK"/>
     <cge:Meas_Ref ObjectId="156608"/>
    <cge:TPSR_Ref TObjectID="26382"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156647">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1240.212271 -449.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26385" ObjectName="SW-DY_YLP.DY_YLP_077BK"/>
     <cge:Meas_Ref ObjectId="156647"/>
    <cge:TPSR_Ref TObjectID="26385"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_35e60f0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 6.500000 -1208.500000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b4f340">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 940.000000 -1104.000000)" xlink:href="#voltageTransformer:shape138"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35f0b40">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1019.000000 -687.000000)" xlink:href="#voltageTransformer:shape138"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_ZX" endPointId="0" endStationName="DY_YLP" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_liuzhongyangshi" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="-50,-1184 -50,-1222 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38069" ObjectName="AC-35kV.LN_liuzhongyangshi"/>
    <cge:TPSR_Ref TObjectID="38069_SS-233"/></metadata>
   <polyline fill="none" opacity="0" points="-50,-1184 -50,-1222 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-DY_YLP.071Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -248.238710 -237.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34249" ObjectName="EC-DY_YLP.071Ld"/>
    <cge:TPSR_Ref TObjectID="34249"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_YLP.072Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -26.238710 -235.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34250" ObjectName="EC-DY_YLP.072Ld"/>
    <cge:TPSR_Ref TObjectID="34250"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_YLP.073Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 215.761290 -233.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34251" ObjectName="EC-DY_YLP.073Ld"/>
    <cge:TPSR_Ref TObjectID="34251"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 451.761290 -232.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_YLP.075Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 710.761290 -230.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34252" ObjectName="EC-DY_YLP.075Ld"/>
    <cge:TPSR_Ref TObjectID="34252"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_YLP.076Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 966.761290 -230.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34253" ObjectName="EC-DY_YLP.076Ld"/>
    <cge:TPSR_Ref TObjectID="34253"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2ae1980" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 494.241796 -820.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3648180" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1052.881701 -1014.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37111e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 36.881701 -1033.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35e1fb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1291.000000 -285.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36bab30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1337.241796 -214.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_377ab30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 32.881701 -967.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_372a680" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 29.881701 -1112.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2af8860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="416,-826 449,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="26363@x" ObjectIDND1="26362@x" ObjectIDZND0="26364@1" Pin0InfoVect0LinkObjId="SW-156296_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156295_0" Pin1InfoVect1LinkObjId="SW-156294_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="416,-826 449,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d8cf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="485,-826 499,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26364@0" ObjectIDZND0="g_2ae1980@0" Pin0InfoVect0LinkObjId="g_2ae1980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156296_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="485,-826 499,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35cb690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="416,-848 416,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="26363@0" ObjectIDZND0="26364@x" ObjectIDZND1="26362@x" Pin0InfoVect0LinkObjId="SW-156296_0" Pin0InfoVect1LinkObjId="SW-156294_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156295_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="416,-848 416,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ff3e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="416,-884 416,-901 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26363@1" ObjectIDZND0="26351@0" Pin0InfoVect0LinkObjId="g_2ae3720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156295_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="416,-884 416,-901 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35a5bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1043,-1020 1057,-1020 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26360@0" ObjectIDZND0="g_3648180@0" Pin0InfoVect0LinkObjId="g_3648180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156288_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1043,-1020 1057,-1020 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3aad9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1007,-1020 959,-1020 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="26360@1" ObjectIDZND0="26359@x" ObjectIDZND1="g_2a94060@0" ObjectIDZND2="g_30ed640@0" Pin0InfoVect0LinkObjId="SW-156287_0" Pin0InfoVect1LinkObjId="g_2a94060_0" Pin0InfoVect2LinkObjId="g_30ed640_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156288_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1007,-1020 959,-1020 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30a4840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="960,-980 960,-1020 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="26359@1" ObjectIDZND0="26360@x" ObjectIDZND1="g_2a94060@0" ObjectIDZND2="g_30ed640@0" Pin0InfoVect0LinkObjId="SW-156288_0" Pin0InfoVect1LinkObjId="g_2a94060_0" Pin0InfoVect2LinkObjId="g_30ed640_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156287_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="960,-980 960,-1020 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_407ad20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="961,-1109 961,-1095 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_3b4f340@0" ObjectIDZND0="g_30ed640@1" Pin0InfoVect0LinkObjId="g_30ed640_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b4f340_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="961,-1109 961,-1095 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d8ca90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="960,-1034 905,-1034 905,-1058 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="26360@x" ObjectIDND1="26359@x" ObjectIDND2="g_30ed640@0" ObjectIDZND0="g_2a94060@0" Pin0InfoVect0LinkObjId="g_2a94060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-156288_0" Pin1InfoVect1LinkObjId="SW-156287_0" Pin1InfoVect2LinkObjId="g_30ed640_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="960,-1034 905,-1034 905,-1058 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_39f0340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="960,-1020 960,-1034 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="26360@x" ObjectIDND1="26359@x" ObjectIDZND0="g_2a94060@0" ObjectIDZND1="g_30ed640@0" Pin0InfoVect0LinkObjId="g_2a94060_0" Pin0InfoVect1LinkObjId="g_30ed640_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156288_0" Pin1InfoVect1LinkObjId="SW-156287_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="960,-1020 960,-1034 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b91130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="960,-1034 960,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2a94060@0" ObjectIDND1="26360@x" ObjectIDND2="26359@x" ObjectIDZND0="g_30ed640@0" Pin0InfoVect0LinkObjId="g_30ed640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a94060_0" Pin1InfoVect1LinkObjId="SW-156288_0" Pin1InfoVect2LinkObjId="SW-156287_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="960,-1034 960,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ff9850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-144,-660 -144,-627 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_36df200@1" Pin0InfoVect0LinkObjId="g_36df200_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35e60f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-144,-660 -144,-627 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b8eb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="27,-1039 41,-1039 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26357@0" ObjectIDZND0="g_37111e0@0" Pin0InfoVect0LinkObjId="g_37111e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156246_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="27,-1039 41,-1039 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f67000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-84,-1150 -50,-1150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3704d60@0" ObjectIDZND0="0@x" ObjectIDZND1="26393@x" ObjectIDZND2="26356@x" Pin0InfoVect0LinkObjId="g_35e60f0_0" Pin0InfoVect1LinkObjId="SW-156247_0" Pin0InfoVect2LinkObjId="SW-156245_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3704d60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-84,-1150 -50,-1150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f67ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-50,-1150 -50,-1185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_3704d60@0" ObjectIDND1="0@x" ObjectIDND2="26393@x" ObjectIDZND0="38069@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3704d60_0" Pin1InfoVect1LinkObjId="g_35e60f0_0" Pin1InfoVect2LinkObjId="SW-156247_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-50,-1150 -50,-1185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35d7080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-50,-1150 -1,-1150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3704d60@0" ObjectIDND1="26393@x" ObjectIDND2="26356@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_35e60f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3704d60_0" Pin1InfoVect1LinkObjId="SW-156247_0" Pin1InfoVect2LinkObjId="SW-156245_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-50,-1150 -1,-1150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36fbe20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-1,-1195 -2,-1213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="g_35e60f0@0" Pin0InfoVect0LinkObjId="g_35e60f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35e60f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-1,-1195 -2,-1213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ae3720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-50,-921 -50,-901 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26355@0" ObjectIDZND0="26351@0" Pin0InfoVect0LinkObjId="g_3ff3e10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156244_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-50,-921 -50,-901 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3780e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="960,-944 960,-901 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26359@0" ObjectIDZND0="26351@0" Pin0InfoVect0LinkObjId="g_3ff3e10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156287_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="960,-944 960,-901 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3648910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-239,-503 -239,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26368@1" ObjectIDZND0="26352@0" Pin0InfoVect0LinkObjId="g_39ec7a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156414_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-239,-503 -239,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3748630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-239,-467 -239,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26368@0" ObjectIDZND0="26367@1" Pin0InfoVect0LinkObjId="SW-156413_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156414_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-239,-467 -239,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39d3520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-239,-400 -239,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26367@0" ObjectIDZND0="26369@1" Pin0InfoVect0LinkObjId="SW-156415_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156413_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-239,-400 -239,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30cd070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-203,-292 -203,-307 -239,-307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_3619210@0" ObjectIDZND0="26369@x" ObjectIDZND1="34249@x" Pin0InfoVect0LinkObjId="SW-156415_0" Pin0InfoVect1LinkObjId="EC-DY_YLP.071Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3619210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-203,-292 -203,-307 -239,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b5a460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-239,-325 -239,-307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26369@0" ObjectIDZND0="g_3619210@0" ObjectIDZND1="34249@x" Pin0InfoVect0LinkObjId="g_3619210_0" Pin0InfoVect1LinkObjId="EC-DY_YLP.071Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156415_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-239,-325 -239,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3f66a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-239,-307 -239,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_3619210@0" ObjectIDND1="26369@x" ObjectIDZND0="34249@0" Pin0InfoVect0LinkObjId="EC-DY_YLP.071Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3619210_0" Pin1InfoVect1LinkObjId="SW-156415_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-239,-307 -239,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39ec7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-17,-501 -17,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26371@1" ObjectIDZND0="26352@0" Pin0InfoVect0LinkObjId="g_3648910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156453_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-17,-501 -17,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37d0b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-17,-465 -17,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26371@0" ObjectIDZND0="26370@1" Pin0InfoVect0LinkObjId="SW-156452_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156453_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-17,-465 -17,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30baf40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-17,-398 -17,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26370@0" ObjectIDZND0="26372@1" Pin0InfoVect0LinkObjId="SW-156454_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156452_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-17,-398 -17,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_370de90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="19,-290 19,-305 -17,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_45032d0@0" ObjectIDZND0="26372@x" ObjectIDZND1="34250@x" Pin0InfoVect0LinkObjId="SW-156454_0" Pin0InfoVect1LinkObjId="EC-DY_YLP.072Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_45032d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="19,-290 19,-305 -17,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dca390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-17,-323 -17,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26372@0" ObjectIDZND0="g_45032d0@0" ObjectIDZND1="34250@x" Pin0InfoVect0LinkObjId="g_45032d0_0" Pin0InfoVect1LinkObjId="EC-DY_YLP.072Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156454_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-17,-323 -17,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bd7cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-17,-305 -17,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_45032d0@0" ObjectIDND1="26372@x" ObjectIDZND0="34250@0" Pin0InfoVect0LinkObjId="EC-DY_YLP.072Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_45032d0_0" Pin1InfoVect1LinkObjId="SW-156454_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-17,-305 -17,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35ff110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="225,-499 225,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26374@1" ObjectIDZND0="26352@0" Pin0InfoVect0LinkObjId="g_3648910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156492_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="225,-499 225,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44ef1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="225,-463 225,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26374@0" ObjectIDZND0="26373@1" Pin0InfoVect0LinkObjId="SW-156491_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156492_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="225,-463 225,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3717d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="225,-396 225,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26373@0" ObjectIDZND0="26375@1" Pin0InfoVect0LinkObjId="SW-156493_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156491_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="225,-396 225,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3936990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="261,-288 261,-303 225,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_358e900@0" ObjectIDZND0="26375@x" ObjectIDZND1="34251@x" Pin0InfoVect0LinkObjId="SW-156493_0" Pin0InfoVect1LinkObjId="EC-DY_YLP.073Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_358e900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="261,-288 261,-303 225,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c665a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="225,-321 225,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26375@0" ObjectIDZND0="g_358e900@0" ObjectIDZND1="34251@x" Pin0InfoVect0LinkObjId="g_358e900_0" Pin0InfoVect1LinkObjId="EC-DY_YLP.073Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156493_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="225,-321 225,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3624480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="225,-303 225,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_358e900@0" ObjectIDND1="26375@x" ObjectIDZND0="34251@0" Pin0InfoVect0LinkObjId="EC-DY_YLP.073Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_358e900_0" Pin1InfoVect1LinkObjId="SW-156493_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="225,-303 225,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a57c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="461,-498 461,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26377@1" ObjectIDZND0="26352@0" Pin0InfoVect0LinkObjId="g_3648910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156531_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="461,-498 461,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bd13a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="461,-462 461,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26377@0" ObjectIDZND0="26376@1" Pin0InfoVect0LinkObjId="SW-156530_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156531_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="461,-462 461,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b864d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="461,-395 461,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26376@0" ObjectIDZND0="26378@1" Pin0InfoVect0LinkObjId="SW-156532_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="461,-395 461,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3b82430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="497,-287 497,-302 461,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_3fe4600@0" ObjectIDZND0="26378@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-156532_0" Pin0InfoVect1LinkObjId="g_35e60f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3fe4600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="497,-287 497,-302 461,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36d43c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="461,-320 461,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26378@0" ObjectIDZND0="g_3fe4600@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_3fe4600_0" Pin0InfoVect1LinkObjId="g_35e60f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156532_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="461,-320 461,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3586310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="461,-302 461,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_3fe4600@0" ObjectIDND1="26378@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_35e60f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3fe4600_0" Pin1InfoVect1LinkObjId="SW-156532_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="461,-302 461,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_362a890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="720,-496 720,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26380@1" ObjectIDZND0="26352@0" Pin0InfoVect0LinkObjId="g_3648910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156570_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="720,-496 720,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36d9160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="720,-460 720,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26380@0" ObjectIDZND0="26379@1" Pin0InfoVect0LinkObjId="SW-156569_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="720,-460 720,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36038a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="720,-393 720,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26379@0" ObjectIDZND0="26381@1" Pin0InfoVect0LinkObjId="SW-156571_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156569_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="720,-393 720,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3611790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="756,-285 756,-300 720,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_3baf610@0" ObjectIDZND0="26381@x" ObjectIDZND1="34252@x" Pin0InfoVect0LinkObjId="SW-156571_0" Pin0InfoVect1LinkObjId="EC-DY_YLP.075Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3baf610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="756,-285 756,-300 720,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34ce710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="720,-318 720,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26381@0" ObjectIDZND0="g_3baf610@0" ObjectIDZND1="34252@x" Pin0InfoVect0LinkObjId="g_3baf610_0" Pin0InfoVect1LinkObjId="EC-DY_YLP.075Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156571_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="720,-318 720,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3751a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="720,-300 720,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_3baf610@0" ObjectIDND1="26381@x" ObjectIDZND0="34252@0" Pin0InfoVect0LinkObjId="EC-DY_YLP.075Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3baf610_0" Pin1InfoVect1LinkObjId="SW-156571_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="720,-300 720,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3094fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="976,-496 976,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26383@1" ObjectIDZND0="26352@0" Pin0InfoVect0LinkObjId="g_3648910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156609_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="976,-496 976,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37042e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="976,-460 976,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26383@0" ObjectIDZND0="26382@1" Pin0InfoVect0LinkObjId="SW-156608_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156609_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="976,-460 976,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bdb7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="976,-393 976,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26382@0" ObjectIDZND0="26384@1" Pin0InfoVect0LinkObjId="SW-156610_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156608_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="976,-393 976,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37610f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1012,-285 1012,-300 976,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_37aa750@0" ObjectIDZND0="26384@x" ObjectIDZND1="34253@x" Pin0InfoVect0LinkObjId="SW-156610_0" Pin0InfoVect1LinkObjId="EC-DY_YLP.076Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37aa750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1012,-285 1012,-300 976,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36fdcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="976,-318 976,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="26384@0" ObjectIDZND0="g_37aa750@0" ObjectIDZND1="34253@x" Pin0InfoVect0LinkObjId="g_37aa750_0" Pin0InfoVect1LinkObjId="EC-DY_YLP.076Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="976,-318 976,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35e3e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="976,-300 976,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_37aa750@0" ObjectIDND1="26384@x" ObjectIDZND0="34253@0" Pin0InfoVect0LinkObjId="EC-DY_YLP.076Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_37aa750_0" Pin1InfoVect1LinkObjId="SW-156610_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="976,-300 976,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33f8090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1249,-539 1249,-528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="26352@0" ObjectIDZND0="26386@1" Pin0InfoVect0LinkObjId="SW-156648_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3648910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1249,-539 1249,-528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3712240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1248,-456 1248,-447 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="26385@0" ObjectIDZND0="g_36b3b00@1" Pin0InfoVect0LinkObjId="g_36b3b00_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156647_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1248,-456 1248,-447 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36587d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1248,-408 1248,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_36b3b00@0" ObjectIDZND0="26387@1" Pin0InfoVect0LinkObjId="SW-156649_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36b3b00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1248,-408 1248,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35930e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1248,-356 1297,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="26387@x" ObjectIDND1="g_3bbd870@0" ObjectIDND2="41852@x" ObjectIDZND0="26388@1" Pin0InfoVect0LinkObjId="SW-156650_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-156649_0" Pin1InfoVect1LinkObjId="g_3bbd870_0" Pin1InfoVect2LinkObjId="CB-DY_YLP.1C_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1248,-356 1297,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_372afe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1248,-364 1248,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="capacitor" ObjectIDND0="26387@0" ObjectIDZND0="26388@x" ObjectIDZND1="g_3bbd870@0" ObjectIDZND2="41852@x" Pin0InfoVect0LinkObjId="SW-156650_0" Pin0InfoVect1LinkObjId="g_3bbd870_0" Pin0InfoVect2LinkObjId="CB-DY_YLP.1C_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156649_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1248,-364 1248,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_373d560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1248,-356 1248,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="capacitor" ObjectIDND0="26388@x" ObjectIDND1="26387@x" ObjectIDND2="g_3bbd870@0" ObjectIDZND0="41852@0" Pin0InfoVect0LinkObjId="CB-DY_YLP.1C_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-156650_0" Pin1InfoVect1LinkObjId="SW-156649_0" Pin1InfoVect2LinkObjId="g_3bbd870_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1248,-356 1248,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3614350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1188,-321 1188,-356 1248,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_3bbd870@0" ObjectIDZND0="26388@x" ObjectIDZND1="26387@x" ObjectIDZND2="41852@x" Pin0InfoVect0LinkObjId="SW-156650_0" Pin0InfoVect1LinkObjId="SW-156649_0" Pin0InfoVect2LinkObjId="CB-DY_YLP.1C_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3bbd870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1188,-321 1188,-356 1248,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3710cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1297,-320 1297,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26388@0" ObjectIDZND0="g_35e1fb0@0" Pin0InfoVect0LinkObjId="g_35e1fb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1297,-320 1297,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30a4030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1248,-230 1248,-220 1292,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="26389@1" Pin0InfoVect0LinkObjId="SW-156651_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1248,-230 1248,-220 1292,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_45035a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1249,-492 1249,-484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="26386@0" ObjectIDZND0="26385@1" Pin0InfoVect0LinkObjId="SW-156647_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156648_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1249,-492 1249,-484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a61400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1328,-220 1342,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26389@0" ObjectIDZND0="g_36bab30@0" Pin0InfoVect0LinkObjId="g_36bab30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156651_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1328,-220 1342,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_368f180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="416,-625 416,-605 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="26365@0" ObjectIDZND0="26366@1" Pin0InfoVect0LinkObjId="SW-156356_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156355_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="416,-625 416,-605 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36e56b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="416,-569 416,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26366@0" ObjectIDZND0="26352@0" Pin0InfoVect0LinkObjId="g_3648910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156356_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="416,-569 416,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36fd240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-144,-582 -144,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_36df200@0" ObjectIDZND0="26352@0" Pin0InfoVect0LinkObjId="g_3648910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36df200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-144,-582 -144,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3658e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-692 1040,-672 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_35f0b40@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_35e60f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35f0b40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-692 1040,-672 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bbf170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-627 1040,-605 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="26361@1" Pin0InfoVect0LinkObjId="SW-156291_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35e60f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-627 1040,-605 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4502b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1040,-569 1040,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="26361@0" ObjectIDZND0="26352@0" Pin0InfoVect0LinkObjId="g_3648910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156291_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1040,-569 1040,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_44f3940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-9,-1039 -50,-1039 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="26357@1" ObjectIDZND0="26354@x" ObjectIDZND1="26356@x" Pin0InfoVect0LinkObjId="SW-156243_0" Pin0InfoVect1LinkObjId="SW-156245_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156246_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-9,-1039 -50,-1039 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3719b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-50,-1021 -50,-1039 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="26354@1" ObjectIDZND0="26357@x" ObjectIDZND1="26356@x" Pin0InfoVect0LinkObjId="SW-156246_0" Pin0InfoVect1LinkObjId="SW-156245_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156243_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-50,-1021 -50,-1039 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_345c5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-50,-1039 -50,-1052 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="26357@x" ObjectIDND1="26354@x" ObjectIDZND0="26356@0" Pin0InfoVect0LinkObjId="SW-156245_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156246_0" Pin1InfoVect1LinkObjId="SW-156243_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-50,-1039 -50,-1052 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36367d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="23,-973 37,-973 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26358@0" ObjectIDZND0="g_377ab30@0" Pin0InfoVect0LinkObjId="g_377ab30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156708_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="23,-973 37,-973 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36b9c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="20,-1118 34,-1118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="26393@0" ObjectIDZND0="g_372a680@0" Pin0InfoVect0LinkObjId="g_372a680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156247_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="20,-1118 34,-1118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36d5310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-13,-973 -50,-973 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="26358@1" ObjectIDZND0="26355@x" ObjectIDZND1="26354@x" Pin0InfoVect0LinkObjId="SW-156244_0" Pin0InfoVect1LinkObjId="SW-156243_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156708_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-13,-973 -50,-973 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b37ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-50,-957 -50,-973 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="26355@1" ObjectIDZND0="26358@x" ObjectIDZND1="26354@x" Pin0InfoVect0LinkObjId="SW-156708_0" Pin0InfoVect1LinkObjId="SW-156243_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156244_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-50,-957 -50,-973 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bcf390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-50,-973 -50,-997 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26358@x" ObjectIDND1="26355@x" ObjectIDZND0="26354@0" Pin0InfoVect0LinkObjId="SW-156243_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156708_0" Pin1InfoVect1LinkObjId="SW-156244_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-50,-973 -50,-997 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_37148d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-16,-1118 -50,-1118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="26393@1" ObjectIDZND0="26356@x" ObjectIDZND1="g_3704d60@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-156245_0" Pin0InfoVect1LinkObjId="g_3704d60_0" Pin0InfoVect2LinkObjId="g_35e60f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156247_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-16,-1118 -50,-1118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_45f8ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-50,-1088 -50,-1118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="26356@1" ObjectIDZND0="26393@x" ObjectIDZND1="g_3704d60@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-156247_0" Pin0InfoVect1LinkObjId="g_3704d60_0" Pin0InfoVect2LinkObjId="g_35e60f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156245_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-50,-1088 -50,-1118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d03ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-50,-1118 -50,-1150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="26393@x" ObjectIDND1="26356@x" ObjectIDZND0="g_3704d60@0" ObjectIDZND1="0@x" ObjectIDZND2="38069@1" Pin0InfoVect0LinkObjId="g_3704d60_0" Pin0InfoVect1LinkObjId="g_35e60f0_0" Pin0InfoVect2LinkObjId="g_3f67ba0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156247_0" Pin1InfoVect1LinkObjId="SW-156245_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-50,-1118 -50,-1150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_396f470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="304,-1007 304,-989 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_36e45c0@1" Pin0InfoVect0LinkObjId="g_36e45c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_35e60f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="304,-1007 304,-989 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3097880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="304,-944 304,-901 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_36e45c0@0" ObjectIDZND0="26351@0" Pin0InfoVect0LinkObjId="g_3ff3e10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36e45c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="304,-944 304,-901 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3603620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="416,-826 416,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="26364@x" ObjectIDND1="26363@x" ObjectIDZND0="26362@1" Pin0InfoVect0LinkObjId="SW-156294_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-156296_0" Pin1InfoVect1LinkObjId="SW-156295_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="416,-826 416,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3608540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="416,-778 416,-756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="26362@0" ObjectIDZND0="26390@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-156294_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="416,-778 416,-756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35e62c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="416,-676 416,-653 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="26390@1" ObjectIDZND0="26365@1" Pin0InfoVect0LinkObjId="SW-156355_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3608540_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="416,-676 416,-653 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="26351" cx="-50" cy="-901" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26351" cx="960" cy="-901" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26352" cx="-239" cy="-539" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26352" cx="1249" cy="-539" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26352" cx="976" cy="-539" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26352" cx="720" cy="-539" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26351" cx="416" cy="-901" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26352" cx="416" cy="-539" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26352" cx="461" cy="-539" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26352" cx="225" cy="-539" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26352" cx="-17" cy="-539" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26352" cx="-144" cy="-539" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26352" cx="1040" cy="-539" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="26351" cx="304" cy="-901" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-153550" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -430.000000 -1163.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26041" ObjectName="DYN-DY_YLP"/>
     <cge:Meas_Ref ObjectId="153550"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(64,64,64)" font-family="SimSun" font-size="20" graphid="g_37900d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -588.000000 -1244.500000) translate(0,16)">杨柳坡变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36347d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36347d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36347d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,59)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36347d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36347d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,101)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36347d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36347d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,143)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36347d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36347d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,185)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36347d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_36347d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,227)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3654850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3654850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3654850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3654850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3654850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3654850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3654850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3654850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3654850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3654850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3654850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3654850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3654850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3654850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3654850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3654850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3654850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3654850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,374)">联系方式：0878-6148336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b310b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -185.903475 -774.000000) translate(0,12)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2af3c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -91.000000 -1264.000000) translate(0,12)">35kV六中杨石线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33f69c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 238.000000 -1121.000000) translate(0,12)">35kV1号站用变压器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36f8d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 914.000000 -1174.000000) translate(0,12)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3694160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -230.238710 -421.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_372bec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -260.000000 -222.000000) translate(0,12)">波西线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3648b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -8.238710 -419.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_368e700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -37.000000 -217.000000) translate(0,12)">双河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c4fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 233.761290 -417.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b41b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 199.000000 -215.000000) translate(0,12)">大仓线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37827c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 469.761290 -416.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c4cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 435.000000 -213.000000) translate(0,12)">备用2线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_340e7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 728.761290 -414.000000) translate(0,12)">075</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34d2640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 699.000000 -213.000000) translate(0,12)">红光线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35e0580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 984.761290 -414.000000) translate(0,12)">076</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c6030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 954.000000 -214.000000) translate(0,12)">外期地线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3662af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1263.761290 -475.000000) translate(0,12)">077</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ba55d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1001.000000 -761.000000) translate(0,12)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_370c4c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1218.000000 -200.000000) translate(0,12)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_378f3a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 531.000000 -926.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a96ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -562.000000) translate(0,12)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36af2e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1256.000000 -517.000000) translate(0,12)">0771</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c6e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1255.000000 -389.000000) translate(0,12)">0776</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_360fbf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1304.000000 -345.000000) translate(0,12)">07767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_370e0c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1289.000000 -246.000000) translate(0,12)">07760</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35cffd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 987.000000 -486.000000) translate(0,12)">0761</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35cdef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 983.000000 -343.000000) translate(0,12)">0766</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_374f200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 727.000000 -485.000000) translate(0,12)">0751</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36031e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 727.000000 -343.000000) translate(0,12)">0756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35adf30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 468.000000 -487.000000) translate(0,12)">0741</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35e13a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 468.000000 -345.000000) translate(0,12)">0746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37259f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 232.000000 -488.000000) translate(0,12)">0731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36d5bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 232.000000 -346.000000) translate(0,12)">0736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35e3b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -10.000000 -490.000000) translate(0,12)">0721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3aae340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -10.000000 -348.000000) translate(0,12)">0726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3842440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -232.000000 -492.000000) translate(0,12)">0711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ca780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -232.000000 -350.000000) translate(0,12)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35b9160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 426.000000 -646.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30c47a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 423.000000 -594.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_44f35a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 426.000000 -799.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39132a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 454.000000 -852.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_44efd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 423.000000 -873.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3939580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1056.000000 -594.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3592ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 967.000000 -969.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b93010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1010.000000 -1046.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b579f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -41.000000 -1018.000000) translate(0,12)">371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b57570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -945.000000) translate(0,12)">3711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b57330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -5.000000 -1064.000000) translate(0,12)">37160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_406f2a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -45.000000 -1079.000000) translate(0,12)">3716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2adb9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -11.000000 -1143.000000) translate(0,12)">37167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b79b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -7.000000 -997.000000) translate(0,12)">37117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36033e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 268.000000 -724.000000) translate(0,12)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36033e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 268.000000 -724.000000) translate(0,27)">35±5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36033e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 268.000000 -724.000000) translate(0,42)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36033e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 268.000000 -724.000000) translate(0,57)">6.57%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_340f6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -402.000000 -1229.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_35a77f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -402.000000 -1264.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33f4cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -702.000000 -818.000000) translate(0,12)">公共信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="17" graphid="g_36af5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -749.000000 -768.000000) translate(0,14)">现场有工作时，核实1、2号站用变数据转发点号是否做错。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_361cca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -754.000000 -260.000000) translate(0,17)">姚安巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3553060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -600.000000 -270.500000) translate(0,17)">18787878958</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3553060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -600.000000 -270.500000) translate(0,38)">18787878954</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_368d3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 457.000000 -752.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3720590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -471.000000 -1248.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4358a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -348.000000 -622.500000) translate(0,12)">Uc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4358c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -348.000000 -637.750000) translate(0,12)">Ub(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4358f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -348.000000 -653.000000) translate(0,12)">Ua(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4359080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -342.000000 -605.250000) translate(0,12)">U0(V):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_43593f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -356.000000 -590.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4359560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -356.000000 -574.000000) translate(0,12)">Ubc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_43596d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -356.000000 -559.000000) translate(0,12)">Uca(kV):</text>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-156295">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 407.241796 -843.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26363" ObjectName="SW-DY_YLP.DY_YLP_3011SW"/>
     <cge:Meas_Ref ObjectId="156295"/>
    <cge:TPSR_Ref TObjectID="26363"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156356">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 407.241796 -564.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26366" ObjectName="SW-DY_YLP.DY_YLP_0011SW"/>
     <cge:Meas_Ref ObjectId="156356"/>
    <cge:TPSR_Ref TObjectID="26366"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156296">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 444.241796 -821.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26364" ObjectName="SW-DY_YLP.DY_YLP_30117SW"/>
     <cge:Meas_Ref ObjectId="156296"/>
    <cge:TPSR_Ref TObjectID="26364"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156244">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -59.118299 -916.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26355" ObjectName="SW-DY_YLP.DY_YLP_3711SW"/>
     <cge:Meas_Ref ObjectId="156244"/>
    <cge:TPSR_Ref TObjectID="26355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156287">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 950.881701 -939.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26359" ObjectName="SW-DY_YLP.DY_YLP_3901SW"/>
     <cge:Meas_Ref ObjectId="156287"/>
    <cge:TPSR_Ref TObjectID="26359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156288">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1001.937257 -1015.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26360" ObjectName="SW-DY_YLP.DY_YLP_39017SW"/>
     <cge:Meas_Ref ObjectId="156288"/>
    <cge:TPSR_Ref TObjectID="26360"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156246">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -14.118299 -1034.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26357" ObjectName="SW-DY_YLP.DY_YLP_37160SW"/>
     <cge:Meas_Ref ObjectId="156246"/>
    <cge:TPSR_Ref TObjectID="26357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156245">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -59.118299 -1047.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26356" ObjectName="SW-DY_YLP.DY_YLP_3716SW"/>
     <cge:Meas_Ref ObjectId="156245"/>
    <cge:TPSR_Ref TObjectID="26356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -5.796969 -1145.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156414">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -248.238710 -462.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26368" ObjectName="SW-DY_YLP.DY_YLP_0711SW"/>
     <cge:Meas_Ref ObjectId="156414"/>
    <cge:TPSR_Ref TObjectID="26368"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156415">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -248.238710 -320.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26369" ObjectName="SW-DY_YLP.DY_YLP_0716SW"/>
     <cge:Meas_Ref ObjectId="156415"/>
    <cge:TPSR_Ref TObjectID="26369"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156453">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -26.238710 -460.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26371" ObjectName="SW-DY_YLP.DY_YLP_0721SW"/>
     <cge:Meas_Ref ObjectId="156453"/>
    <cge:TPSR_Ref TObjectID="26371"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156454">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -26.238710 -318.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26372" ObjectName="SW-DY_YLP.DY_YLP_0726SW"/>
     <cge:Meas_Ref ObjectId="156454"/>
    <cge:TPSR_Ref TObjectID="26372"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156492">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 215.761290 -458.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26374" ObjectName="SW-DY_YLP.DY_YLP_0731SW"/>
     <cge:Meas_Ref ObjectId="156492"/>
    <cge:TPSR_Ref TObjectID="26374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156493">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 215.761290 -316.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26375" ObjectName="SW-DY_YLP.DY_YLP_0736SW"/>
     <cge:Meas_Ref ObjectId="156493"/>
    <cge:TPSR_Ref TObjectID="26375"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156532">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 451.761290 -315.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26378" ObjectName="SW-DY_YLP.DY_YLP_0746SW"/>
     <cge:Meas_Ref ObjectId="156532"/>
    <cge:TPSR_Ref TObjectID="26378"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156531">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 451.761290 -457.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26377" ObjectName="SW-DY_YLP.DY_YLP_0741SW"/>
     <cge:Meas_Ref ObjectId="156531"/>
    <cge:TPSR_Ref TObjectID="26377"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156571">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 710.761290 -313.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26381" ObjectName="SW-DY_YLP.DY_YLP_0756SW"/>
     <cge:Meas_Ref ObjectId="156571"/>
    <cge:TPSR_Ref TObjectID="26381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156570">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 710.761290 -455.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26380" ObjectName="SW-DY_YLP.DY_YLP_0751SW"/>
     <cge:Meas_Ref ObjectId="156570"/>
    <cge:TPSR_Ref TObjectID="26380"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156610">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 966.761290 -313.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26384" ObjectName="SW-DY_YLP.DY_YLP_0766SW"/>
     <cge:Meas_Ref ObjectId="156610"/>
    <cge:TPSR_Ref TObjectID="26384"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156609">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 966.761290 -455.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26383" ObjectName="SW-DY_YLP.DY_YLP_0761SW"/>
     <cge:Meas_Ref ObjectId="156609"/>
    <cge:TPSR_Ref TObjectID="26383"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156649">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1238.761290 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26387" ObjectName="SW-DY_YLP.DY_YLP_0776SW"/>
     <cge:Meas_Ref ObjectId="156649"/>
    <cge:TPSR_Ref TObjectID="26387"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156648">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1239.761290 -487.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26386" ObjectName="SW-DY_YLP.DY_YLP_0771SW"/>
     <cge:Meas_Ref ObjectId="156648"/>
    <cge:TPSR_Ref TObjectID="26386"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156650">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1287.761290 -315.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26388" ObjectName="SW-DY_YLP.DY_YLP_07767SW"/>
     <cge:Meas_Ref ObjectId="156650"/>
    <cge:TPSR_Ref TObjectID="26388"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156651">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1287.241796 -215.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26389" ObjectName="SW-DY_YLP.DY_YLP_07760SW"/>
     <cge:Meas_Ref ObjectId="156651"/>
    <cge:TPSR_Ref TObjectID="26389"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156291">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1030.881701 -564.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26361" ObjectName="SW-DY_YLP.DY_YLP_0901SW"/>
     <cge:Meas_Ref ObjectId="156291"/>
    <cge:TPSR_Ref TObjectID="26361"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1035.203031 -622.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156708">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -18.118299 -968.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26358" ObjectName="SW-DY_YLP.DY_YLP_37117SW"/>
     <cge:Meas_Ref ObjectId="156708"/>
    <cge:TPSR_Ref TObjectID="26358"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-156247">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -21.118299 -1113.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26393" ObjectName="SW-DY_YLP.DY_YLP_37167SW"/>
     <cge:Meas_Ref ObjectId="156247"/>
    <cge:TPSR_Ref TObjectID="26393"/></metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-DY_YLP.1C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1230.000000 -228.000000)" xlink:href="#capacitor:shape45"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41852" ObjectName="CB-DY_YLP.1C"/>
    <cge:TPSR_Ref TObjectID="41852"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3704d60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -91.000000 -1146.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30ed640">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 953.000000 -1046.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a94060">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 898.000000 -1053.692308)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3619210">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -210.323558 -238.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_45032d0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 11.676442 -236.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_358e900">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 253.676442 -234.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3fe4600">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 489.676442 -233.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3baf610">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 748.676442 -231.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37aa750">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1004.676442 -231.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36b3b00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1238.000000 -403.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3bbd870">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1180.676442 -267.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36e45c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 299.000000 -939.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36df200">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -149.000000 -577.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-156162" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 535.000000 -723.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156162" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26390"/>
     <cge:Term_Ref ObjectID="37310"/>
    <cge:TPSR_Ref TObjectID="26390"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-156161" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 533.000000 -693.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156161" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26390"/>
     <cge:Term_Ref ObjectID="37310"/>
    <cge:TPSR_Ref TObjectID="26390"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-156145" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -135.000000 -1032.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156145" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26354"/>
     <cge:Term_Ref ObjectID="37235"/>
    <cge:TPSR_Ref TObjectID="26354"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-156146" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -135.000000 -1032.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156146" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26354"/>
     <cge:Term_Ref ObjectID="37235"/>
    <cge:TPSR_Ref TObjectID="26354"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-156142" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -135.000000 -1032.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156142" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26354"/>
     <cge:Term_Ref ObjectID="37235"/>
    <cge:TPSR_Ref TObjectID="26354"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-156152" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 340.000000 -815.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156152" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26362"/>
     <cge:Term_Ref ObjectID="37251"/>
    <cge:TPSR_Ref TObjectID="26362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-156153" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 340.000000 -815.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156153" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26362"/>
     <cge:Term_Ref ObjectID="37251"/>
    <cge:TPSR_Ref TObjectID="26362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-156149" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 340.000000 -815.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156149" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26362"/>
     <cge:Term_Ref ObjectID="37251"/>
    <cge:TPSR_Ref TObjectID="26362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-156158" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 343.000000 -652.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156158" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26365"/>
     <cge:Term_Ref ObjectID="37257"/>
    <cge:TPSR_Ref TObjectID="26365"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-156159" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 343.000000 -652.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156159" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26365"/>
     <cge:Term_Ref ObjectID="37257"/>
    <cge:TPSR_Ref TObjectID="26365"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-156155" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 343.000000 -652.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156155" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26365"/>
     <cge:Term_Ref ObjectID="37257"/>
    <cge:TPSR_Ref TObjectID="26365"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-156182" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -236.000000 -183.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156182" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26367"/>
     <cge:Term_Ref ObjectID="37261"/>
    <cge:TPSR_Ref TObjectID="26367"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-156183" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -236.000000 -183.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156183" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26367"/>
     <cge:Term_Ref ObjectID="37261"/>
    <cge:TPSR_Ref TObjectID="26367"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-156179" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -236.000000 -183.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156179" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26367"/>
     <cge:Term_Ref ObjectID="37261"/>
    <cge:TPSR_Ref TObjectID="26367"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-156188" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -9.000000 -182.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156188" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26370"/>
     <cge:Term_Ref ObjectID="37267"/>
    <cge:TPSR_Ref TObjectID="26370"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-156189" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -9.000000 -182.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156189" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26370"/>
     <cge:Term_Ref ObjectID="37267"/>
    <cge:TPSR_Ref TObjectID="26370"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-156185" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -9.000000 -182.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156185" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26370"/>
     <cge:Term_Ref ObjectID="37267"/>
    <cge:TPSR_Ref TObjectID="26370"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-156194" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 236.000000 -182.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156194" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26373"/>
     <cge:Term_Ref ObjectID="37273"/>
    <cge:TPSR_Ref TObjectID="26373"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-156195" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 236.000000 -182.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156195" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26373"/>
     <cge:Term_Ref ObjectID="37273"/>
    <cge:TPSR_Ref TObjectID="26373"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-156191" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 236.000000 -182.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156191" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26373"/>
     <cge:Term_Ref ObjectID="37273"/>
    <cge:TPSR_Ref TObjectID="26373"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-156200" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 470.000000 -182.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156200" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26376"/>
     <cge:Term_Ref ObjectID="37279"/>
    <cge:TPSR_Ref TObjectID="26376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-156201" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 470.000000 -182.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156201" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26376"/>
     <cge:Term_Ref ObjectID="37279"/>
    <cge:TPSR_Ref TObjectID="26376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-156197" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 470.000000 -182.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156197" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26376"/>
     <cge:Term_Ref ObjectID="37279"/>
    <cge:TPSR_Ref TObjectID="26376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-156206" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 732.000000 -182.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156206" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26379"/>
     <cge:Term_Ref ObjectID="37285"/>
    <cge:TPSR_Ref TObjectID="26379"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-156207" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 732.000000 -182.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156207" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26379"/>
     <cge:Term_Ref ObjectID="37285"/>
    <cge:TPSR_Ref TObjectID="26379"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-156203" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 732.000000 -182.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156203" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26379"/>
     <cge:Term_Ref ObjectID="37285"/>
    <cge:TPSR_Ref TObjectID="26379"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-156212" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 993.000000 -182.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156212" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26382"/>
     <cge:Term_Ref ObjectID="37291"/>
    <cge:TPSR_Ref TObjectID="26382"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-156213" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 993.000000 -182.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156213" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26382"/>
     <cge:Term_Ref ObjectID="37291"/>
    <cge:TPSR_Ref TObjectID="26382"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-156209" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 993.000000 -182.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156209" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26382"/>
     <cge:Term_Ref ObjectID="37291"/>
    <cge:TPSR_Ref TObjectID="26382"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-0" prefix="" rightAlign="0">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1259.000000 -182.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26385"/>
     <cge:Term_Ref ObjectID="37297"/>
    <cge:TPSR_Ref TObjectID="26385"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-156218" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1259.000000 -182.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156218" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26385"/>
     <cge:Term_Ref ObjectID="37297"/>
    <cge:TPSR_Ref TObjectID="26385"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-156215" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1259.000000 -182.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156215" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26385"/>
     <cge:Term_Ref ObjectID="37297"/>
    <cge:TPSR_Ref TObjectID="26385"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-156171" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -295.000000 -650.000000) translate(0,12)">156171.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156171" ObjectName="DY_YLP.DY_YLP_9ⅠM:F"/>
     <cge:PSR_Ref ObjectID="26352"/>
     <cge:Term_Ref ObjectID="37230"/>
    <cge:TPSR_Ref TObjectID="26352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-156172" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -295.000000 -650.000000) translate(0,27)">156172.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156172" ObjectName="DY_YLP.DY_YLP_9ⅠM:F"/>
     <cge:PSR_Ref ObjectID="26352"/>
     <cge:Term_Ref ObjectID="37230"/>
    <cge:TPSR_Ref TObjectID="26352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-156173" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -295.000000 -650.000000) translate(0,42)">156173.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156173" ObjectName="DY_YLP.DY_YLP_9ⅠM:F"/>
     <cge:PSR_Ref ObjectID="26352"/>
     <cge:Term_Ref ObjectID="37230"/>
    <cge:TPSR_Ref TObjectID="26352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-156177" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -295.000000 -650.000000) translate(0,57)">156177.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156177" ObjectName="DY_YLP.DY_YLP_9ⅠM:F"/>
     <cge:PSR_Ref ObjectID="26352"/>
     <cge:Term_Ref ObjectID="37230"/>
    <cge:TPSR_Ref TObjectID="26352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-156174" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -295.000000 -650.000000) translate(0,72)">156174.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156174" ObjectName="DY_YLP.DY_YLP_9ⅠM:F"/>
     <cge:PSR_Ref ObjectID="26352"/>
     <cge:Term_Ref ObjectID="37230"/>
    <cge:TPSR_Ref TObjectID="26352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-156175" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -295.000000 -650.000000) translate(0,87)">156175.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156175" ObjectName="DY_YLP.DY_YLP_9ⅠM:F"/>
     <cge:PSR_Ref ObjectID="26352"/>
     <cge:Term_Ref ObjectID="37230"/>
    <cge:TPSR_Ref TObjectID="26352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-156176" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -295.000000 -650.000000) translate(0,102)">156176.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156176" ObjectName="DY_YLP.DY_YLP_9ⅠM:F"/>
     <cge:PSR_Ref ObjectID="26352"/>
     <cge:Term_Ref ObjectID="37230"/>
    <cge:TPSR_Ref TObjectID="26352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-156163" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1246.000000 -981.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156163" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26351"/>
     <cge:Term_Ref ObjectID="37229"/>
    <cge:TPSR_Ref TObjectID="26351"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-156164" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1246.000000 -981.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156164" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26351"/>
     <cge:Term_Ref ObjectID="37229"/>
    <cge:TPSR_Ref TObjectID="26351"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-156165" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1246.000000 -981.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156165" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26351"/>
     <cge:Term_Ref ObjectID="37229"/>
    <cge:TPSR_Ref TObjectID="26351"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-156169" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1246.000000 -981.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156169" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26351"/>
     <cge:Term_Ref ObjectID="37229"/>
    <cge:TPSR_Ref TObjectID="26351"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-156166" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1246.000000 -981.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156166" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="26351"/>
     <cge:Term_Ref ObjectID="37229"/>
    <cge:TPSR_Ref TObjectID="26351"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-627" y="-1257"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-676" y="-1274"/></g>
   <g href="35kV杨柳坡变波西线071间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="-231" y="-421"/></g>
   <g href="35kV杨柳坡变双河线072间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="-9" y="-419"/></g>
   <g href="35kV杨柳坡变大仓线073间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="28" x="233" y="-417"/></g>
   <g href="35kV杨柳坡变备用2线074间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="28" x="469" y="-416"/></g>
   <g href="35kV杨柳坡变红光线075间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="28" x="728" y="-414"/></g>
   <g href="35kV杨柳坡变外期地线076间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="28" x="984" y="-414"/></g>
   <g href="35kV杨柳坡变1号电容器组077间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="28" x="1263" y="-475"/></g>
   <g href="35kV杨柳坡变六中杨石线371间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="-41" y="-1018"/></g>
   <g href="cx_配调_配网接线图35_大姚.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-413" y="-1237"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-413" y="-1272"/></g>
   <g href="35kV杨柳坡变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="60" x="-702" y="-818"/></g>
   <g href="35kV杨柳坡变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="457" y="-752"/></g>
   <g href="AVC杨柳坡站.svg" style="fill-opacity:0"><rect height="43" qtmmishow="hidden" stroke="rgb(0,0,0)" width="47" x="-476" y="-1259"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dc1660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 487.000000 722.000000) translate(0,12)">档位:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_346cce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 457.500000 691.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_376ffb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -191.000000 1032.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c5150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -202.000000 1017.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3647a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -177.000000 1002.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_362c630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 284.000000 815.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2af3870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 273.000000 800.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37c53b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 298.000000 785.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35d9e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 285.000000 654.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ac7d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 274.000000 639.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35e9690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 299.000000 624.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_372c380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -290.000000 184.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ad2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -301.000000 169.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_372c5e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -276.000000 154.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_361ca40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1176.000000 936.000000) translate(0,12)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36ffb70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1169.000000 952.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36d4f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1169.000000 965.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3778250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1161.000000 921.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35ea7c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1169.000000 983.000000) translate(0,12)">Ua（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30ec600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -61.000000 183.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36b6100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -72.000000 168.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3791fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -47.000000 153.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35c9660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 185.000000 182.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37622d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 174.000000 167.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a28d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 199.000000 152.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36edd30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 418.000000 183.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3788e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 407.000000 168.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e9460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 432.000000 153.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3766710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 680.000000 182.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3629020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 669.000000 167.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_373f280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 694.000000 152.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_44ed780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 942.000000 185.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3939850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 931.000000 170.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3725280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 956.000000 155.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b51c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1206.000000 183.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bc1b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1195.000000 168.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bc22a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1220.000000 153.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(-0.944444 -0.000000 -0.000000 0.942857 319.000000 -1102.000000)" xlink:href="#transformer2:shape75_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.944444 -0.000000 -0.000000 0.942857 319.000000 -1102.000000)" xlink:href="#transformer2:shape75_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -160.000000 -655.000000)" xlink:href="#transformer2:shape56_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -160.000000 -655.000000)" xlink:href="#transformer2:shape56_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-DY_YLP.DY_YLP_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="37309"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 378.000000 -671.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 378.000000 -671.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="26390" ObjectName="TF-DY_YLP.DY_YLP_1T"/>
    <cge:TPSR_Ref TObjectID="26390"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-627" y="-1257"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-627" y="-1257"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-676" y="-1274"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-676" y="-1274"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="-231" y="-421"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="-231" y="-421"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="-9" y="-419"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="-9" y="-419"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="28" x="233" y="-417"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="28" x="233" y="-417"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="28" x="469" y="-416"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="28" x="469" y="-416"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="28" x="728" y="-414"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="28" x="728" y="-414"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="28" x="984" y="-414"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="28" x="984" y="-414"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="28" x="1263" y="-475"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="28" x="1263" y="-475"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="-41" y="-1018"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="-41" y="-1018"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-413" y="-1237"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-413" y="-1237"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-413" y="-1272"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-413" y="-1272"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="60" x="-702" y="-818"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="60" x="-702" y="-818"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="457" y="-752"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="457" y="-752"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="-476,-1259 -479,-1262 -479,-1213 -476,-1216 -476,-1259" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="-476,-1259 -479,-1262 -426,-1262 -429,-1259 -476,-1259" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="-476,-1216 -479,-1213 -426,-1213 -429,-1216 -476,-1216" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="-429,-1259 -426,-1262 -426,-1213 -429,-1216 -429,-1259" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="43" stroke="rgb(255,255,255)" width="47" x="-476" y="-1259"/>
     <rect fill="none" height="43" qtmmishow="hidden" stroke="rgb(0,0,0)" width="47" x="-476" y="-1259"/>
    </a>
   <metadata/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-DY_YLP.DY_YLP_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-291,-901 1325,-901 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26351" ObjectName="BS-DY_YLP.DY_YLP_3ⅠM"/>
    <cge:TPSR_Ref TObjectID="26351"/></metadata>
   <polyline fill="none" opacity="0" points="-291,-901 1325,-901 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_YLP.DY_YLP_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-288,-539 1324,-539 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="26352" ObjectName="BS-DY_YLP.DY_YLP_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="26352"/></metadata>
   <polyline fill="none" opacity="0" points="-288,-539 1324,-539 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -652.000000 -1193.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-156170" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -641.000000 -1098.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156170" ObjectName="DY_YLP:DY_YLP_3ⅠM_Hz"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-156727" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -592.000000 -973.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156727" ObjectName="DY_YLP:DY_YLP_YGZJ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-156728" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -593.000000 -932.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156728" ObjectName="DY_YLP:DY_YLP_WGZJ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-156152" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -595.000000 -1057.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156152" ObjectName="DY_YLP:DY_YLP_301BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-156152" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -595.000000 -1016.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="156152" ObjectName="DY_YLP:DY_YLP_301BK_P"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="DY_YLP"/>
</svg>