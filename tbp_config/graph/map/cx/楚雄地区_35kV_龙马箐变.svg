<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-278" aopId="3932678" id="thSvg" product="E8000V2" version="1.0" viewBox="16 -1066 2554 1370">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape28">
    <polyline arcFlag="1" points="19,105 17,105 15,104 14,104 12,103 11,102 9,101 8,99 7,97 7,96 6,94 6,92 6,90 7,88 7,87 8,85 9,84 11,82 12,81 14,80 15,80 17,79 19,79 21,79 23,80 24,80 26,81 27,82 29,84 30,85 31,87 31,88 32,90 32,92 " stroke-width="0.0972"/>
    <polyline arcFlag="1" points="36,30 37,30 38,30 38,30 39,31 39,31 40,31 40,32 41,32 41,33 41,34 41,34 42,35 42,36 42,36 41,37 41,38 41,38 41,39 40,39 40,40 39,40 39,40 38,41 38,41 37,41 36,41 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="11" x2="26" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="10" x2="26" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="19" x2="19" y1="19" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="35" x2="35" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="2" x2="2" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.356919" x1="19" x2="36" y1="60" y2="60"/>
    <rect height="23" stroke-width="0.369608" width="12" x="13" y="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44164" x1="19" x2="36" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="2" x2="35" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.423529" x1="19" x2="19" y1="92" y2="26"/>
    <polyline arcFlag="1" points="36,41 37,41 38,41 38,42 39,42 39,42 40,43 40,43 41,44 41,44 41,45 41,45 42,46 42,47 42,47 41,48 41,49 41,49 41,50 40,50 40,51 39,51 39,52 38,52 38,52 37,52 36,52 " stroke-width="1"/>
    <polyline arcFlag="1" points="36,19 37,19 38,19 38,19 39,19 39,20 40,20 40,21 41,21 41,22 41,22 41,23 42,24 42,24 42,25 41,26 41,26 41,27 41,27 40,28 40,28 39,29 39,29 38,29 38,30 37,30 36,30 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="60" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="19" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="19" x2="19" y1="105" y2="116"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="32" x2="19" y1="92" y2="92"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape5_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape5_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape24_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="41" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
   </symbol>
   <symbol id="switch2:shape24_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="36" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="11" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
   </symbol>
   <symbol id="switch2:shape24-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="41" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
   </symbol>
   <symbol id="switch2:shape24-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="36" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="11" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
   </symbol>
   <symbol id="transformer2:shape22_0">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline points="64,100 64,93 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="30" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="30" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="38" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="38" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="62" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="62" y2="71"/>
   </symbol>
   <symbol id="transformer2:shape22_1">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="transformer2:shape95_0">
    <ellipse cx="14" cy="43" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="13" y1="38" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="13" y1="7" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="9" y1="0" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="31" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="42" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="37" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="39" x2="35" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="46" x2="28" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="40" x2="33" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="46" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="42" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="38" y2="42"/>
   </symbol>
   <symbol id="transformer2:shape95_1">
    <circle cx="13" cy="62" fillStyle="0" r="13" stroke-width="0.265306"/>
    <polyline DF8003:Layer="PUBLIC" points="12,70 8,61 18,61 12,70 "/>
   </symbol>
   <symbol id="voltageTransformer:shape33">
    <ellipse cx="8" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="67" y2="23"/>
    <rect height="24" stroke-width="0.379884" width="14" x="1" y="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="6" y2="6"/>
   </symbol>
   <symbol id="voltageTransformer:shape144">
    <circle cx="17" cy="36" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="0" x2="10" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07362" x1="3" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="2" x2="8" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="5" x2="10" y1="37" y2="37"/>
    <polyline points="5,7 5,37 " stroke-width="1"/>
    <circle cx="17" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="19" x2="17" y1="33" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="19" x2="17" y1="39" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="17" x2="15" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="30" x2="30" y1="20" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="34" x2="30" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="34" x2="30" y1="24" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="43" x2="41" y1="27" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="43" x2="41" y1="33" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="41" x2="38" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="32" x2="30" y1="33" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="30" x2="27" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="32" x2="30" y1="39" y2="36"/>
    <circle cx="30" cy="23" r="7.5" stroke-width="0.804311"/>
    <circle cx="29" cy="36" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="19" x2="17" y1="22" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="19" x2="17" y1="28" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="17" x2="14" y1="25" y2="25"/>
    <circle cx="40" cy="30" r="7.5" stroke-width="0.804311"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a8e690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a8f050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a8f9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a906b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a918b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a924c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a93070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a93aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a950b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a950b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a96a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a96a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a98890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a98890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2a998a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a9b530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2a9c180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2a9d060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a9d940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a9f100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a9f900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a9fff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2aa0a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aa1bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aa2570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aa3060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2aa3a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2aa4f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2aa5a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2aa6aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2aa76e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ab5eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2aa8d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2aa9f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2aab4e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1380" width="2564" x="11" y="-1071"/>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="424,89 419,79 429,79 424,89 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="494,214 489,204 499,204 494,214 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2182,87 2177,77 2187,77 2182,87 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2252,212 2247,202 2257,202 2252,212 " stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-230939">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1302.000000 -793.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38493" ObjectName="SW-CX_LMQ.CX_LMQ_375XC1"/>
     <cge:Meas_Ref ObjectId="230939"/>
    <cge:TPSR_Ref TObjectID="38493"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230939">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1302.000000 -728.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38492" ObjectName="SW-CX_LMQ.CX_LMQ_375XC"/>
     <cge:Meas_Ref ObjectId="230939"/>
    <cge:TPSR_Ref TObjectID="38492"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230963">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 645.000000 -153.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38503" ObjectName="SW-CX_LMQ.CX_LMQ_032XC"/>
     <cge:Meas_Ref ObjectId="230963"/>
    <cge:TPSR_Ref TObjectID="38503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230963">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 645.000000 -88.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38504" ObjectName="SW-CX_LMQ.CX_LMQ_032XC1"/>
     <cge:Meas_Ref ObjectId="230963"/>
    <cge:TPSR_Ref TObjectID="38504"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230964">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 680.000000 -29.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38505" ObjectName="SW-CX_LMQ.CX_LMQ_03260SW"/>
     <cge:Meas_Ref ObjectId="230964"/>
    <cge:TPSR_Ref TObjectID="38505"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230968">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 808.000000 -151.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38507" ObjectName="SW-CX_LMQ.CX_LMQ_033XC"/>
     <cge:Meas_Ref ObjectId="230968"/>
    <cge:TPSR_Ref TObjectID="38507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230968">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 808.000000 -86.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38508" ObjectName="SW-CX_LMQ.CX_LMQ_033XC1"/>
     <cge:Meas_Ref ObjectId="230968"/>
    <cge:TPSR_Ref TObjectID="38508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230969">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 843.000000 -27.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38509" ObjectName="SW-CX_LMQ.CX_LMQ_03360SW"/>
     <cge:Meas_Ref ObjectId="230969"/>
    <cge:TPSR_Ref TObjectID="38509"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231000">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1466.000000 -154.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38533" ObjectName="SW-CX_LMQ.CX_LMQ_012XC"/>
     <cge:Meas_Ref ObjectId="231000"/>
    <cge:TPSR_Ref TObjectID="38533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231000">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1466.000000 -89.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38534" ObjectName="SW-CX_LMQ.CX_LMQ_012XC1"/>
     <cge:Meas_Ref ObjectId="231000"/>
    <cge:TPSR_Ref TObjectID="38534"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230978">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1716.000000 -153.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38515" ObjectName="SW-CX_LMQ.CX_LMQ_041XC"/>
     <cge:Meas_Ref ObjectId="230978"/>
    <cge:TPSR_Ref TObjectID="38515"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230978">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1716.000000 -88.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38516" ObjectName="SW-CX_LMQ.CX_LMQ_041XC1"/>
     <cge:Meas_Ref ObjectId="230978"/>
    <cge:TPSR_Ref TObjectID="38516"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230979">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1751.000000 -29.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38517" ObjectName="SW-CX_LMQ.CX_LMQ_04160SW"/>
     <cge:Meas_Ref ObjectId="230979"/>
    <cge:TPSR_Ref TObjectID="38517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230984">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1899.000000 -29.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38521" ObjectName="SW-CX_LMQ.CX_LMQ_04260SW"/>
     <cge:Meas_Ref ObjectId="230984"/>
    <cge:TPSR_Ref TObjectID="38521"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230983">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1864.000000 -153.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38519" ObjectName="SW-CX_LMQ.CX_LMQ_042XC"/>
     <cge:Meas_Ref ObjectId="230983"/>
    <cge:TPSR_Ref TObjectID="38519"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230983">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1864.000000 -88.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38520" ObjectName="SW-CX_LMQ.CX_LMQ_042XC1"/>
     <cge:Meas_Ref ObjectId="230983"/>
    <cge:TPSR_Ref TObjectID="38520"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231001">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1311.000000 -147.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38535" ObjectName="SW-CX_LMQ.CX_LMQ_0121XC"/>
     <cge:Meas_Ref ObjectId="231001"/>
    <cge:TPSR_Ref TObjectID="38535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231001">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1311.000000 -96.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38550" ObjectName="SW-CX_LMQ.CX_LMQ_0121XC1"/>
     <cge:Meas_Ref ObjectId="231001"/>
    <cge:TPSR_Ref TObjectID="38550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230941">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1330.000000 -975.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38495" ObjectName="SW-CX_LMQ.CX_LMQ_37567SW"/>
     <cge:Meas_Ref ObjectId="230941"/>
    <cge:TPSR_Ref TObjectID="38495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230938">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1303.000000 -914.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38491" ObjectName="SW-CX_LMQ.CX_LMQ_3756SW"/>
     <cge:Meas_Ref ObjectId="230938"/>
    <cge:TPSR_Ref TObjectID="38491"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230940">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1331.000000 -902.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38494" ObjectName="SW-CX_LMQ.CX_LMQ_37560SW"/>
     <cge:Meas_Ref ObjectId="230940"/>
    <cge:TPSR_Ref TObjectID="38494"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230918">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1051.000000 -292.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38483" ObjectName="SW-CX_LMQ.CX_LMQ_001XC1"/>
     <cge:Meas_Ref ObjectId="230918"/>
    <cge:TPSR_Ref TObjectID="38483"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230918">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1051.000000 -226.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38482" ObjectName="SW-CX_LMQ.CX_LMQ_001XC"/>
     <cge:Meas_Ref ObjectId="230918"/>
    <cge:TPSR_Ref TObjectID="38482"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230916">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1051.000000 -650.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38479" ObjectName="SW-CX_LMQ.CX_LMQ_301XC"/>
     <cge:Meas_Ref ObjectId="230916"/>
    <cge:TPSR_Ref TObjectID="38479"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230916">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1051.000000 -584.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38480" ObjectName="SW-CX_LMQ.CX_LMQ_301XC1"/>
     <cge:Meas_Ref ObjectId="230916"/>
    <cge:TPSR_Ref TObjectID="38480"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230929">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1863.000000 -293.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38489" ObjectName="SW-CX_LMQ.CX_LMQ_002XC1"/>
     <cge:Meas_Ref ObjectId="230929"/>
    <cge:TPSR_Ref TObjectID="38489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230929">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1863.000000 -227.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38488" ObjectName="SW-CX_LMQ.CX_LMQ_002XC"/>
     <cge:Meas_Ref ObjectId="230929"/>
    <cge:TPSR_Ref TObjectID="38488"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230927">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1863.000000 -650.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38485" ObjectName="SW-CX_LMQ.CX_LMQ_302XC"/>
     <cge:Meas_Ref ObjectId="230927"/>
    <cge:TPSR_Ref TObjectID="38485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230927">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1863.000000 -585.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38486" ObjectName="SW-CX_LMQ.CX_LMQ_302XC1"/>
     <cge:Meas_Ref ObjectId="230927"/>
    <cge:TPSR_Ref TObjectID="38486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231004">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1250.000000 -607.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38537" ObjectName="SW-CX_LMQ.CX_LMQ_3901XC1"/>
     <cge:Meas_Ref ObjectId="231004"/>
    <cge:TPSR_Ref TObjectID="38537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231004">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1250.000000 -650.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38536" ObjectName="SW-CX_LMQ.CX_LMQ_3901XC"/>
     <cge:Meas_Ref ObjectId="231004"/>
    <cge:TPSR_Ref TObjectID="38536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230957">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 484.000000 -153.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38497" ObjectName="SW-CX_LMQ.CX_LMQ_031XC"/>
     <cge:Meas_Ref ObjectId="230957"/>
    <cge:TPSR_Ref TObjectID="38497"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230957">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 484.000000 -88.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38498" ObjectName="SW-CX_LMQ.CX_LMQ_031XC1"/>
     <cge:Meas_Ref ObjectId="230957"/>
    <cge:TPSR_Ref TObjectID="38498"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230958">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 519.000000 -39.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38499" ObjectName="SW-CX_LMQ.CX_LMQ_03160SW"/>
     <cge:Meas_Ref ObjectId="230958"/>
    <cge:TPSR_Ref TObjectID="38499"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230959">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 485.000000 63.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38500" ObjectName="SW-CX_LMQ.CX_LMQ_0316SW"/>
     <cge:Meas_Ref ObjectId="230959"/>
    <cge:TPSR_Ref TObjectID="38500"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230960">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 461.500000 69.500000)" xlink:href="#switch2:shape24_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38501" ObjectName="SW-CX_LMQ.CX_LMQ_03167SW"/>
     <cge:Meas_Ref ObjectId="230960"/>
    <cge:TPSR_Ref TObjectID="38501"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231007">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 938.000000 -149.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38542" ObjectName="SW-CX_LMQ.CX_LMQ_0341XC"/>
     <cge:Meas_Ref ObjectId="231007"/>
    <cge:TPSR_Ref TObjectID="38542"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231007">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 938.000000 -84.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38543" ObjectName="SW-CX_LMQ.CX_LMQ_0341XC1"/>
     <cge:Meas_Ref ObjectId="231007"/>
    <cge:TPSR_Ref TObjectID="38543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230973">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1051.000000 -150.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38511" ObjectName="SW-CX_LMQ.CX_LMQ_035XC"/>
     <cge:Meas_Ref ObjectId="230973"/>
    <cge:TPSR_Ref TObjectID="38511"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230973">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1051.000000 -85.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38512" ObjectName="SW-CX_LMQ.CX_LMQ_035XC1"/>
     <cge:Meas_Ref ObjectId="230973"/>
    <cge:TPSR_Ref TObjectID="38512"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230974">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1086.000000 -26.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38513" ObjectName="SW-CX_LMQ.CX_LMQ_03560SW"/>
     <cge:Meas_Ref ObjectId="230974"/>
    <cge:TPSR_Ref TObjectID="38513"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231005">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1219.000000 -103.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38539" ObjectName="SW-CX_LMQ.CX_LMQ_0901XC1"/>
     <cge:Meas_Ref ObjectId="231005"/>
    <cge:TPSR_Ref TObjectID="38539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231005">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1219.000000 -146.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38538" ObjectName="SW-CX_LMQ.CX_LMQ_0901XC"/>
     <cge:Meas_Ref ObjectId="231005"/>
    <cge:TPSR_Ref TObjectID="38538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231006">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1590.000000 -103.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38541" ObjectName="SW-CX_LMQ.CX_LMQ_0902XC1"/>
     <cge:Meas_Ref ObjectId="231006"/>
    <cge:TPSR_Ref TObjectID="38541"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231006">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1590.000000 -146.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38540" ObjectName="SW-CX_LMQ.CX_LMQ_0902XC"/>
     <cge:Meas_Ref ObjectId="231006"/>
    <cge:TPSR_Ref TObjectID="38540"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230988">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2032.000000 -153.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38523" ObjectName="SW-CX_LMQ.CX_LMQ_043XC"/>
     <cge:Meas_Ref ObjectId="230988"/>
    <cge:TPSR_Ref TObjectID="38523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230988">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2032.000000 -88.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38524" ObjectName="SW-CX_LMQ.CX_LMQ_043XC1"/>
     <cge:Meas_Ref ObjectId="230988"/>
    <cge:TPSR_Ref TObjectID="38524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230989">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2067.000000 -29.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38525" ObjectName="SW-CX_LMQ.CX_LMQ_04360SW"/>
     <cge:Meas_Ref ObjectId="230989"/>
    <cge:TPSR_Ref TObjectID="38525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230994">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2242.000000 -155.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38527" ObjectName="SW-CX_LMQ.CX_LMQ_044XC"/>
     <cge:Meas_Ref ObjectId="230994"/>
    <cge:TPSR_Ref TObjectID="38527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230994">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2242.000000 -90.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38528" ObjectName="SW-CX_LMQ.CX_LMQ_044XC1"/>
     <cge:Meas_Ref ObjectId="230994"/>
    <cge:TPSR_Ref TObjectID="38528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230995">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2277.000000 -41.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38529" ObjectName="SW-CX_LMQ.CX_LMQ_04460SW"/>
     <cge:Meas_Ref ObjectId="230995"/>
    <cge:TPSR_Ref TObjectID="38529"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230996">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2243.000000 61.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38530" ObjectName="SW-CX_LMQ.CX_LMQ_0446SW"/>
     <cge:Meas_Ref ObjectId="230996"/>
    <cge:TPSR_Ref TObjectID="38530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230997">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2219.500000 67.500000)" xlink:href="#switch2:shape24_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38531" ObjectName="SW-CX_LMQ.CX_LMQ_04467SW"/>
     <cge:Meas_Ref ObjectId="230997"/>
    <cge:TPSR_Ref TObjectID="38531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231008">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2406.000000 -147.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38544" ObjectName="SW-CX_LMQ.CX_LMQ_0451XC"/>
     <cge:Meas_Ref ObjectId="231008"/>
    <cge:TPSR_Ref TObjectID="38544"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-231008">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2406.000000 -82.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38545" ObjectName="SW-CX_LMQ.CX_LMQ_0451XC1"/>
     <cge:Meas_Ref ObjectId="231008"/>
    <cge:TPSR_Ref TObjectID="38545"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_LMQ.CX_LMQ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="884,-708 2041,-708 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38475" ObjectName="BS-CX_LMQ.CX_LMQ_3IM"/>
    <cge:TPSR_Ref TObjectID="38475"/></metadata>
   <polyline fill="none" opacity="0" points="884,-708 2041,-708 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_LMQ.CX_LMQ_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="421,-205 1371,-205 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38476" ObjectName="BS-CX_LMQ.CX_LMQ_9IM"/>
    <cge:TPSR_Ref TObjectID="38476"/></metadata>
   <polyline fill="none" opacity="0" points="421,-205 1371,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_LMQ.CX_LMQ_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1441,-205 2569,-205 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38477" ObjectName="BS-CX_LMQ.CX_LMQ_9IIM"/>
    <cge:TPSR_Ref TObjectID="38477"/></metadata>
   <polyline fill="none" opacity="0" points="1441,-205 2569,-205 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 813.000000 204.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 650.000000 203.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1056.000000 205.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1869.000000 212.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1721.000000 210.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2037.000000 210.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2b1aef0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 683.000000 -8.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28ccb30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 846.000000 -6.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28869d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1754.000000 -8.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2184170" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1902.000000 -8.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21acb30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 522.000000 -18.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2191bf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1089.000000 -5.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2864890" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2070.000000 -8.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2144670" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2280.000000 -20.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2819000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-789 1312,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38490@1" ObjectIDZND0="38493@1" Pin0InfoVect0LinkObjId="SW-230939_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230937_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1312,-789 1312,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2819260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-752 1312,-762 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38492@1" ObjectIDZND0="38490@0" Pin0InfoVect0LinkObjId="SW-230937_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230939_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1312,-752 1312,-762 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22845e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="655,-149 655,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38502@1" ObjectIDZND0="38503@1" Pin0InfoVect0LinkObjId="SW-230963_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230962_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="655,-149 655,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2284840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="655,-112 655,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38504@1" ObjectIDZND0="38502@0" Pin0InfoVect0LinkObjId="SW-230962_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230963_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="655,-112 655,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b1b940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="689,-26 689,-34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2b1aef0@0" ObjectIDZND0="38505@0" Pin0InfoVect0LinkObjId="SW-230964_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b1aef0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="689,-26 689,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b1c8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="655,2 655,182 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2284aa0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2284aa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="655,2 655,182 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_213a080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="818,-147 818,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38506@1" ObjectIDZND0="38507@1" Pin0InfoVect0LinkObjId="SW-230968_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230967_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="818,-147 818,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_213a2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="818,-110 818,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38508@1" ObjectIDZND0="38506@0" Pin0InfoVect0LinkObjId="SW-230967_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230968_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="818,-110 818,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28cd4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="852,-24 852,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_28ccb30@0" ObjectIDZND0="38509@0" Pin0InfoVect0LinkObjId="SW-230969_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28ccb30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="852,-24 852,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28ce480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="818,4 818,183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_213a540@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_213a540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="818,4 818,183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28ce6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="784,-68 818,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_28cd750@0" ObjectIDZND0="38508@x" ObjectIDZND1="g_213a540@0" ObjectIDZND2="38509@x" Pin0InfoVect0LinkObjId="SW-230968_0" Pin0InfoVect1LinkObjId="g_213a540_0" Pin0InfoVect2LinkObjId="SW-230969_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28cd750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="784,-68 818,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28ce940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="818,-93 818,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="38508@0" ObjectIDZND0="g_28cd750@0" ObjectIDZND1="g_213a540@0" ObjectIDZND2="38509@x" Pin0InfoVect0LinkObjId="g_28cd750_0" Pin0InfoVect1LinkObjId="g_213a540_0" Pin0InfoVect2LinkObjId="SW-230969_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230968_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="818,-93 818,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28ceba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="818,-68 818,-49 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_28cd750@0" ObjectIDND1="38508@x" ObjectIDND2="38509@x" ObjectIDZND0="g_213a540@1" Pin0InfoVect0LinkObjId="g_213a540_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28cd750_0" Pin1InfoVect1LinkObjId="SW-230968_0" Pin1InfoVect2LinkObjId="SW-230969_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="818,-68 818,-49 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28cee00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="852,-68 816,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="38509@1" ObjectIDZND0="g_28cd750@0" ObjectIDZND1="38508@x" ObjectIDZND2="g_213a540@0" Pin0InfoVect0LinkObjId="g_28cd750_0" Pin0InfoVect1LinkObjId="SW-230968_0" Pin0InfoVect2LinkObjId="g_213a540_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230969_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="852,-68 816,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31bb390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1476,-150 1476,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38532@1" ObjectIDZND0="38533@1" Pin0InfoVect0LinkObjId="SW-231000_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230999_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1476,-150 1476,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31bb5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1476,-113 1476,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38534@1" ObjectIDZND0="38532@0" Pin0InfoVect0LinkObjId="SW-230999_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231000_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1476,-113 1476,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31bb850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="621,-70 655,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2b1bba0@0" ObjectIDZND0="38504@x" ObjectIDZND1="g_2284aa0@0" ObjectIDZND2="38505@x" Pin0InfoVect0LinkObjId="SW-230963_0" Pin0InfoVect1LinkObjId="g_2284aa0_0" Pin0InfoVect2LinkObjId="SW-230964_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b1bba0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="621,-70 655,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31bc560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="655,-95 655,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="38504@0" ObjectIDZND0="g_2b1bba0@0" ObjectIDZND1="g_2284aa0@0" ObjectIDZND2="38505@x" Pin0InfoVect0LinkObjId="g_2b1bba0_0" Pin0InfoVect1LinkObjId="g_2284aa0_0" Pin0InfoVect2LinkObjId="SW-230964_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230963_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="655,-95 655,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31bc7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="655,-70 655,-51 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2b1bba0@0" ObjectIDND1="38504@x" ObjectIDND2="38505@x" ObjectIDZND0="g_2284aa0@1" Pin0InfoVect0LinkObjId="g_2284aa0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b1bba0_0" Pin1InfoVect1LinkObjId="SW-230963_0" Pin1InfoVect2LinkObjId="SW-230964_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="655,-70 655,-51 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31bca20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="689,-70 654,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="38505@1" ObjectIDZND0="g_2b1bba0@0" ObjectIDZND1="38504@x" ObjectIDZND2="g_2284aa0@0" Pin0InfoVect0LinkObjId="g_2b1bba0_0" Pin0InfoVect1LinkObjId="SW-230963_0" Pin0InfoVect2LinkObjId="g_2284aa0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230964_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="689,-70 654,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21a0690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1726,-149 1726,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38514@1" ObjectIDZND0="38515@1" Pin0InfoVect0LinkObjId="SW-230978_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230977_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1726,-149 1726,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21a08f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1726,-112 1726,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38516@1" ObjectIDZND0="38514@0" Pin0InfoVect0LinkObjId="SW-230977_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230978_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1726,-112 1726,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2887420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1760,-26 1760,-34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_28869d0@0" ObjectIDZND0="38517@0" Pin0InfoVect0LinkObjId="SW-230979_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28869d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1760,-26 1760,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28883b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1726,2 1726,189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_21a0b50@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21a0b50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1726,2 1726,189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2889ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1692,-70 1726,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2887680@0" ObjectIDZND0="38516@1" ObjectIDZND1="g_21a0b50@0" ObjectIDZND2="38517@x" Pin0InfoVect0LinkObjId="SW-230978_1" Pin0InfoVect1LinkObjId="g_21a0b50_0" Pin0InfoVect2LinkObjId="SW-230979_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2887680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1692,-70 1726,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_288a0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1726,-95 1726,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="38516@0" ObjectIDZND0="g_2887680@0" ObjectIDZND1="g_21a0b50@0" ObjectIDZND2="38517@x" Pin0InfoVect0LinkObjId="g_2887680_0" Pin0InfoVect1LinkObjId="g_21a0b50_0" Pin0InfoVect2LinkObjId="SW-230979_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230978_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1726,-95 1726,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_288a2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1726,-70 1726,-51 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="38516@1" ObjectIDND1="g_2887680@0" ObjectIDND2="38517@x" ObjectIDZND0="g_21a0b50@1" Pin0InfoVect0LinkObjId="g_21a0b50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-230978_1" Pin1InfoVect1LinkObjId="g_2887680_0" Pin1InfoVect2LinkObjId="SW-230979_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1726,-70 1726,-51 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_288a490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1760,-70 1725,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="38517@1" ObjectIDZND0="38516@1" ObjectIDZND1="g_2887680@0" ObjectIDZND2="g_21a0b50@0" Pin0InfoVect0LinkObjId="SW-230978_1" Pin0InfoVect1LinkObjId="g_2887680_0" Pin0InfoVect2LinkObjId="g_21a0b50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230979_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1760,-70 1725,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2180a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1874,-149 1874,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38518@1" ObjectIDZND0="38519@1" Pin0InfoVect0LinkObjId="SW-230983_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230982_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1874,-149 1874,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2180cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1874,-112 1874,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38520@1" ObjectIDZND0="38518@0" Pin0InfoVect0LinkObjId="SW-230982_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230983_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1874,-112 1874,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2184bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1908,-26 1908,-34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2184170@0" ObjectIDZND0="38521@0" Pin0InfoVect0LinkObjId="SW-230984_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2184170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1908,-26 1908,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2185b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1874,2 1874,191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2180f50@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2180f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1874,2 1874,191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2185db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1840,-70 1874,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2184e20@0" ObjectIDZND0="38520@x" ObjectIDZND1="38521@x" ObjectIDZND2="g_2180f50@0" Pin0InfoVect0LinkObjId="SW-230983_0" Pin0InfoVect1LinkObjId="SW-230984_0" Pin0InfoVect2LinkObjId="g_2180f50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2184e20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1840,-70 1874,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2186010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1874,-95 1874,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="38520@0" ObjectIDZND0="g_2184e20@0" ObjectIDZND1="38521@x" ObjectIDZND2="g_2180f50@0" Pin0InfoVect0LinkObjId="g_2184e20_0" Pin0InfoVect1LinkObjId="SW-230984_0" Pin0InfoVect2LinkObjId="g_2180f50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230983_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1874,-95 1874,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2186270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1874,-70 1874,-51 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="38520@x" ObjectIDND1="g_2184e20@0" ObjectIDND2="38521@x" ObjectIDZND0="g_2180f50@1" Pin0InfoVect0LinkObjId="g_2180f50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-230983_0" Pin1InfoVect1LinkObjId="g_2184e20_0" Pin1InfoVect2LinkObjId="SW-230984_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1874,-70 1874,-51 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21864d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1908,-70 1872,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="38521@1" ObjectIDZND0="38520@x" ObjectIDZND1="g_2184e20@0" ObjectIDZND2="g_2180f50@0" Pin0InfoVect0LinkObjId="SW-230983_0" Pin0InfoVect1LinkObjId="g_2184e20_0" Pin0InfoVect2LinkObjId="g_2180f50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230984_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1908,-70 1872,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b9f380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-735 1312,-708 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38492@0" ObjectIDZND0="38475@0" Pin0InfoVect0LinkObjId="g_2b09df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230939_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1312,-735 1312,-708 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b9f5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1476,-178 1476,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38533@0" ObjectIDZND0="38477@0" Pin0InfoVect0LinkObjId="g_2ba01d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1476,-178 1476,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b9f840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1321,-171 1321,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38535@0" ObjectIDZND0="38476@0" Pin0InfoVect0LinkObjId="g_2b9fd10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231001_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1321,-171 1321,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b9faa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1321,-103 1321,-79 1476,-79 1476,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="38550@0" ObjectIDZND0="38534@0" Pin0InfoVect0LinkObjId="SW-231000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231001_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1321,-103 1321,-79 1476,-79 1476,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b9fd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="818,-175 818,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38507@0" ObjectIDZND0="38476@0" Pin0InfoVect0LinkObjId="g_2b9f840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230968_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="818,-175 818,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b9ff70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="655,-177 655,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38503@0" ObjectIDZND0="38476@0" Pin0InfoVect0LinkObjId="g_2b9f840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230963_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="655,-177 655,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ba01d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1726,-177 1726,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38515@0" ObjectIDZND0="38477@0" Pin0InfoVect0LinkObjId="g_2b9f5e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230978_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1726,-177 1726,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ba0430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1874,-177 1874,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38519@0" ObjectIDZND0="38477@0" Pin0InfoVect0LinkObjId="g_2b9f5e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230983_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1874,-177 1874,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24374f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1321,-154 1321,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="38535@1" ObjectIDZND0="38550@1" Pin0InfoVect0LinkObjId="SW-231001_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231001_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1321,-154 1321,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2857270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-909 1336,-909 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="38491@x" ObjectIDND1="g_285c710@0" ObjectIDZND0="38494@0" Pin0InfoVect0LinkObjId="SW-230940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-230938_0" Pin1InfoVect1LinkObjId="g_285c710_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1312,-909 1336,-909 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_285d330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-842 1329,-842 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="38493@x" ObjectIDND1="g_285c710@0" ObjectIDND2="g_296ddb0@0" ObjectIDZND0="g_296f890@0" Pin0InfoVect0LinkObjId="g_296f890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-230939_0" Pin1InfoVect1LinkObjId="g_285c710_0" Pin1InfoVect2LinkObjId="g_296ddb0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1312,-842 1329,-842 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_285e040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-817 1312,-842 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="38493@0" ObjectIDZND0="g_285c710@0" ObjectIDZND1="g_296f890@0" ObjectIDZND2="g_296ddb0@0" Pin0InfoVect0LinkObjId="g_285c710_0" Pin0InfoVect1LinkObjId="g_296f890_0" Pin0InfoVect2LinkObjId="g_296ddb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230939_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1312,-817 1312,-842 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_285e2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-842 1272,-842 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="38493@x" ObjectIDND1="g_285c710@0" ObjectIDND2="g_296f890@0" ObjectIDZND0="g_296ddb0@0" Pin0InfoVect0LinkObjId="g_296ddb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-230939_0" Pin1InfoVect1LinkObjId="g_285c710_0" Pin1InfoVect2LinkObjId="g_296f890_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1312,-842 1272,-842 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_285e500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-842 1312,-859 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="38493@x" ObjectIDND1="g_296f890@0" ObjectIDND2="g_296ddb0@0" ObjectIDZND0="g_285c710@0" Pin0InfoVect0LinkObjId="g_285c710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-230939_0" Pin1InfoVect1LinkObjId="g_296f890_0" Pin1InfoVect2LinkObjId="g_296ddb0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1312,-842 1312,-859 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_296ad70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-919 1312,-909 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="38491@0" ObjectIDZND0="g_285c710@0" ObjectIDZND1="38494@x" Pin0InfoVect0LinkObjId="g_285c710_0" Pin0InfoVect1LinkObjId="SW-230940_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230938_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1312,-919 1312,-909 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_296afd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-909 1312,-898 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="38491@x" ObjectIDND1="38494@x" ObjectIDZND0="g_285c710@1" Pin0InfoVect0LinkObjId="g_285c710_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-230938_0" Pin1InfoVect1LinkObjId="SW-230940_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1312,-909 1312,-898 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_215ab20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1061,-288 1061,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38481@1" ObjectIDZND0="38483@1" Pin0InfoVect0LinkObjId="SW-230918_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230917_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1061,-288 1061,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_215ad80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1061,-251 1061,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38482@1" ObjectIDZND0="38481@0" Pin0InfoVect0LinkObjId="SW-230917_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230918_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1061,-251 1061,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b09930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1061,-646 1061,-657 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38478@1" ObjectIDZND0="38479@1" Pin0InfoVect0LinkObjId="SW-230916_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230915_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1061,-646 1061,-657 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b09b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1061,-609 1061,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38480@1" ObjectIDZND0="38478@0" Pin0InfoVect0LinkObjId="SW-230915_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230916_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1061,-609 1061,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b09df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1061,-674 1061,-708 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38479@0" ObjectIDZND0="38475@0" Pin0InfoVect0LinkObjId="g_2b9f380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230916_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1061,-674 1061,-708 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b0a050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1061,-233 1061,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38482@0" ObjectIDZND0="38476@0" Pin0InfoVect0LinkObjId="g_2b9f840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230918_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1061,-233 1061,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0b9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1260,-675 1260,-708 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38536@0" ObjectIDZND0="38475@0" Pin0InfoVect0LinkObjId="g_2b9f380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231004_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1260,-675 1260,-708 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0bc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1061,-571 1061,-591 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2970600@1" ObjectIDZND0="38480@0" Pin0InfoVect0LinkObjId="SW-230916_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2970600_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1061,-571 1061,-591 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0c8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1061,-518 1061,-501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2970600@0" ObjectIDZND0="38547@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2970600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1061,-518 1061,-501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b0cb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1061,-414 1061,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="38547@1" ObjectIDZND0="g_2b0be80@1" Pin0InfoVect0LinkObjId="g_2b0be80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b0c8a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1061,-414 1061,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b0cd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1061,-339 1061,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b0be80@0" ObjectIDZND0="38483@0" Pin0InfoVect0LinkObjId="SW-230918_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b0be80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1061,-339 1061,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29446b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1873,-646 1873,-657 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38484@1" ObjectIDZND0="38485@1" Pin0InfoVect0LinkObjId="SW-230927_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230926_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1873,-646 1873,-657 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2944910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1873,-609 1873,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38486@1" ObjectIDZND0="38484@0" Pin0InfoVect0LinkObjId="SW-230926_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230927_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1873,-609 1873,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2944b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1873,-674 1873,-708 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38485@0" ObjectIDZND0="38475@0" Pin0InfoVect0LinkObjId="g_2b9f380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230927_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1873,-674 1873,-708 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2944dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1873,-572 1873,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b0cfc0@1" ObjectIDZND0="38486@0" Pin0InfoVect0LinkObjId="SW-230927_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b0cfc0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1873,-572 1873,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2945a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1873,-519 1873,-502 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2b0cfc0@0" ObjectIDZND0="38548@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b0cfc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1873,-519 1873,-502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2945cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1873,-415 1873,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="38548@1" ObjectIDZND0="g_2945030@1" Pin0InfoVect0LinkObjId="g_2945030_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2945a50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1873,-415 1873,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2945f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1873,-340 1873,-317 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2945030@0" ObjectIDZND0="38489@0" Pin0InfoVect0LinkObjId="SW-230929_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2945030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1873,-340 1873,-317 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b02580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1260,-657 1260,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="38536@1" ObjectIDZND0="38537@1" Pin0InfoVect0LinkObjId="SW-231004_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231004_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1260,-657 1260,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b05460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1260,-583 1216,-583 1216,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="38537@x" ObjectIDND1="g_2b06ee0@0" ObjectIDZND0="g_2b061b0@0" Pin0InfoVect0LinkObjId="g_2b061b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-231004_0" Pin1InfoVect1LinkObjId="g_2b06ee0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1260,-583 1216,-583 1216,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b05f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1260,-614 1260,-583 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="38537@0" ObjectIDZND0="g_2b06ee0@0" ObjectIDZND1="g_2b061b0@0" Pin0InfoVect0LinkObjId="g_2b06ee0_0" Pin0InfoVect1LinkObjId="g_2b061b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231004_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1260,-614 1260,-583 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b07760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1260,-583 1260,-569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="38537@x" ObjectIDND1="g_2b061b0@0" ObjectIDZND0="g_2b06ee0@0" Pin0InfoVect0LinkObjId="g_2b06ee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-231004_0" Pin1InfoVect1LinkObjId="g_2b061b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1260,-583 1260,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b079c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1260,-538 1260,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2b06ee0@1" ObjectIDZND0="g_2b027e0@0" Pin0InfoVect0LinkObjId="g_2b027e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b06ee0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1260,-538 1260,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21a96b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="494,-149 494,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38496@1" ObjectIDZND0="38497@1" Pin0InfoVect0LinkObjId="SW-230957_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230956_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="494,-149 494,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21ad580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="528,-36 528,-44 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_21acb30@0" ObjectIDZND0="38499@0" Pin0InfoVect0LinkObjId="SW-230958_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21acb30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="528,-36 528,-44 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21ae510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="494,-177 494,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38497@0" ObjectIDZND0="38476@0" Pin0InfoVect0LinkObjId="g_2b9f840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230957_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="494,-177 494,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_297c310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="460,-80 528,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_21ad7e0@0" ObjectIDZND0="38499@1" Pin0InfoVect0LinkObjId="SW-230958_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21ad7e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="460,-80 528,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_297c570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="494,-112 494,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38498@1" ObjectIDZND0="38496@0" Pin0InfoVect0LinkObjId="SW-230956_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230957_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="494,-112 494,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_297c7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="494,-70 494,-95 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_21a9910@1" ObjectIDZND0="38498@0" Pin0InfoVect0LinkObjId="SW-230957_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21a9910_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="494,-70 494,-95 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2983ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="453,64 453,76 494,76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="38501@1" ObjectIDZND0="0@x" ObjectIDZND1="38500@x" ObjectIDZND2="g_2988020@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="SW-230959_0" Pin0InfoVect2LinkObjId="g_2988020_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230960_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="453,64 453,76 494,76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29870f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="425,64 425,216 494,216 494,197 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="38501@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="EC-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="425,64 425,216 494,216 494,197 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2987360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="494,86 494,77 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="38501@x" ObjectIDZND1="38500@x" ObjectIDZND2="g_2988020@0" Pin0InfoVect0LinkObjId="SW-230960_0" Pin0InfoVect1LinkObjId="SW-230959_0" Pin0InfoVect2LinkObjId="g_2988020_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="494,86 494,77 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2987900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="494,76 494,58 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="38501@x" ObjectIDND1="0@x" ObjectIDND2="g_2988020@0" ObjectIDZND0="38500@0" Pin0InfoVect0LinkObjId="SW-230959_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-230960_0" Pin1InfoVect1LinkObjId="EC-0_0" Pin1InfoVect2LinkObjId="g_2988020_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="494,76 494,58 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2987b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="494,22 494,-20 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="38500@1" ObjectIDZND0="g_21a9910@0" Pin0InfoVect0LinkObjId="g_21a9910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230959_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="494,22 494,-20 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2987dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="494,76 523,76 523,87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="38501@x" ObjectIDND1="0@x" ObjectIDND2="38500@x" ObjectIDZND0="g_2988020@0" Pin0InfoVect0LinkObjId="g_2988020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-230960_0" Pin1InfoVect1LinkObjId="EC-0_0" Pin1InfoVect2LinkObjId="SW-230959_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="494,76 523,76 523,87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2953b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="948,-91 948,-2 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="38543@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="EC-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231007_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="948,-91 948,-2 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2953db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="948,-205 948,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="38476@0" ObjectIDZND0="38542@0" Pin0InfoVect0LinkObjId="SW-231007_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b9f840_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="948,-205 948,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2955900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="948,-108 948,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="38543@1" ObjectIDZND0="g_29551e0@1" Pin0InfoVect0LinkObjId="g_29551e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231007_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="948,-108 948,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2955b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="948,-147 948,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_29551e0@0" ObjectIDZND0="38542@1" Pin0InfoVect0LinkObjId="SW-231007_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29551e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="948,-147 948,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_218e510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1061,-146 1061,-157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38510@1" ObjectIDZND0="38511@1" Pin0InfoVect0LinkObjId="SW-230973_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230972_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1061,-146 1061,-157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_218e770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1061,-109 1061,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38512@1" ObjectIDZND0="38510@0" Pin0InfoVect0LinkObjId="SW-230972_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230973_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1061,-109 1061,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2192640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1095,-23 1095,-31 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2191bf0@0" ObjectIDZND0="38513@0" Pin0InfoVect0LinkObjId="SW-230974_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2191bf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1095,-23 1095,-31 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21935d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1061,5 1061,184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_218e9d0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_218e9d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1061,5 1061,184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2193830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1027,-67 1061,-67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_21928a0@0" ObjectIDZND0="38512@x" ObjectIDZND1="38513@x" ObjectIDZND2="g_218e9d0@0" Pin0InfoVect0LinkObjId="SW-230973_0" Pin0InfoVect1LinkObjId="SW-230974_0" Pin0InfoVect2LinkObjId="g_218e9d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21928a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1027,-67 1061,-67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2193a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1061,-92 1061,-67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="38512@0" ObjectIDZND0="g_21928a0@0" ObjectIDZND1="38513@x" ObjectIDZND2="g_218e9d0@0" Pin0InfoVect0LinkObjId="g_21928a0_0" Pin0InfoVect1LinkObjId="SW-230974_0" Pin0InfoVect2LinkObjId="g_218e9d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230973_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1061,-92 1061,-67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2193cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1061,-67 1061,-48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="38512@x" ObjectIDND1="g_21928a0@0" ObjectIDND2="38513@x" ObjectIDZND0="g_218e9d0@1" Pin0InfoVect0LinkObjId="g_218e9d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-230973_0" Pin1InfoVect1LinkObjId="g_21928a0_0" Pin1InfoVect2LinkObjId="SW-230974_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1061,-67 1061,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2193f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1095,-67 1059,-67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="38513@1" ObjectIDZND0="38512@x" ObjectIDZND1="g_21928a0@0" ObjectIDZND2="g_218e9d0@0" Pin0InfoVect0LinkObjId="SW-230973_0" Pin0InfoVect1LinkObjId="g_21928a0_0" Pin0InfoVect2LinkObjId="g_218e9d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230974_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1095,-67 1059,-67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21941b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1061,-174 1061,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38511@0" ObjectIDZND0="38476@0" Pin0InfoVect0LinkObjId="g_2b9f840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230973_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1061,-174 1061,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2194ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1229,-171 1229,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38538@0" ObjectIDZND0="38476@0" Pin0InfoVect0LinkObjId="g_2b9f840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231005_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1229,-171 1229,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32c64d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1229,-153 1229,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="38538@1" ObjectIDZND0="38539@1" Pin0InfoVect0LinkObjId="SW-231005_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231005_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1229,-153 1229,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32c93b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1229,-79 1185,-79 1185,-64 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="38539@x" ObjectIDND1="g_32ca5a0@0" ObjectIDZND0="g_32c9870@0" Pin0InfoVect0LinkObjId="g_32c9870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-231005_0" Pin1InfoVect1LinkObjId="g_32ca5a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1229,-79 1185,-79 1185,-64 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32c9610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1229,-110 1229,-79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="38539@0" ObjectIDZND0="g_32c9870@0" ObjectIDZND1="g_32ca5a0@0" Pin0InfoVect0LinkObjId="g_32c9870_0" Pin0InfoVect1LinkObjId="g_32ca5a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231005_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1229,-110 1229,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32cae20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1229,-79 1229,-65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="38539@x" ObjectIDND1="g_32c9870@0" ObjectIDZND0="g_32ca5a0@0" Pin0InfoVect0LinkObjId="g_32ca5a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-231005_0" Pin1InfoVect1LinkObjId="g_32c9870_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1229,-79 1229,-65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32cb080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1229,-34 1229,-20 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_32ca5a0@1" ObjectIDZND0="g_32c6730@0" Pin0InfoVect0LinkObjId="g_32c6730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32ca5a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1229,-34 1229,-20 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32cedb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1600,-171 1600,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38540@0" ObjectIDZND0="38477@0" Pin0InfoVect0LinkObjId="g_2b9f5e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231006_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1600,-171 1600,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33850d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1600,-153 1600,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="38540@1" ObjectIDZND0="38541@1" Pin0InfoVect0LinkObjId="SW-231006_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231006_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1600,-153 1600,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3387fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1600,-79 1556,-79 1556,-64 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="38541@x" ObjectIDND1="g_33891a0@0" ObjectIDZND0="g_3388470@0" Pin0InfoVect0LinkObjId="g_3388470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-231006_0" Pin1InfoVect1LinkObjId="g_33891a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1600,-79 1556,-79 1556,-64 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3388210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1600,-110 1600,-79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="38541@0" ObjectIDZND0="g_3388470@0" ObjectIDZND1="g_33891a0@0" Pin0InfoVect0LinkObjId="g_3388470_0" Pin0InfoVect1LinkObjId="g_33891a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231006_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1600,-110 1600,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3389a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1600,-79 1600,-65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="38541@x" ObjectIDND1="g_3388470@0" ObjectIDZND0="g_33891a0@0" Pin0InfoVect0LinkObjId="g_33891a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-231006_0" Pin1InfoVect1LinkObjId="g_3388470_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1600,-79 1600,-65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3389c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1600,-34 1600,-20 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_33891a0@1" ObjectIDZND0="g_3385330@0" Pin0InfoVect0LinkObjId="g_3385330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33891a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1600,-34 1600,-20 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28611b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2042,-149 2042,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38522@1" ObjectIDZND0="38523@1" Pin0InfoVect0LinkObjId="SW-230988_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230987_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2042,-149 2042,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2861410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2042,-112 2042,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38524@1" ObjectIDZND0="38522@0" Pin0InfoVect0LinkObjId="SW-230987_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230988_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2042,-112 2042,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28652e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2076,-26 2076,-34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2864890@0" ObjectIDZND0="38525@0" Pin0InfoVect0LinkObjId="SW-230989_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2864890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2076,-26 2076,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2866270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2042,2 2042,189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2861670@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2861670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2042,2 2042,189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2866de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2008,-70 2042,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2865540@0" ObjectIDZND0="38524@x" ObjectIDZND1="38525@x" ObjectIDZND2="g_2861670@0" Pin0InfoVect0LinkObjId="SW-230988_0" Pin0InfoVect1LinkObjId="SW-230989_0" Pin0InfoVect2LinkObjId="g_2861670_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2865540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2008,-70 2042,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2866fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2042,-95 2042,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="38524@0" ObjectIDZND0="g_2865540@0" ObjectIDZND1="38525@x" ObjectIDZND2="g_2861670@0" Pin0InfoVect0LinkObjId="g_2865540_0" Pin0InfoVect1LinkObjId="SW-230989_0" Pin0InfoVect2LinkObjId="g_2861670_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230988_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2042,-95 2042,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28671c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2042,-70 2042,-51 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="38524@x" ObjectIDND1="g_2865540@0" ObjectIDND2="38525@x" ObjectIDZND0="g_2861670@1" Pin0InfoVect0LinkObjId="g_2861670_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-230988_0" Pin1InfoVect1LinkObjId="g_2865540_0" Pin1InfoVect2LinkObjId="SW-230989_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2042,-70 2042,-51 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28673b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2076,-70 2040,-70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="38525@1" ObjectIDZND0="38524@x" ObjectIDZND1="g_2865540@0" ObjectIDZND2="g_2861670@0" Pin0InfoVect0LinkObjId="SW-230988_0" Pin0InfoVect1LinkObjId="g_2865540_0" Pin0InfoVect2LinkObjId="g_2861670_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230989_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2076,-70 2040,-70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28675e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2042,-177 2042,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38523@0" ObjectIDZND0="38477@0" Pin0InfoVect0LinkObjId="g_2b9f5e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230988_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2042,-177 2042,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21411f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2252,-151 2252,-162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38526@1" ObjectIDZND0="38527@1" Pin0InfoVect0LinkObjId="SW-230994_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230993_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2252,-151 2252,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21450c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2286,-38 2286,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2144670@0" ObjectIDZND0="38529@0" Pin0InfoVect0LinkObjId="SW-230995_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2144670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2286,-38 2286,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2146050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2252,-179 2252,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38527@0" ObjectIDZND0="38477@0" Pin0InfoVect0LinkObjId="g_2b9f5e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230994_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2252,-179 2252,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2148ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2218,-82 2286,-82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2145320@0" ObjectIDZND0="38529@1" Pin0InfoVect0LinkObjId="SW-230995_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2145320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2218,-82 2286,-82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2148d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2252,-114 2252,-124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38528@1" ObjectIDZND0="38526@0" Pin0InfoVect0LinkObjId="SW-230993_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230994_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2252,-114 2252,-124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2148f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2252,-72 2252,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2141450@1" ObjectIDZND0="38528@0" Pin0InfoVect0LinkObjId="SW-230994_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2141450_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2252,-72 2252,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32aa980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2211,62 2211,74 2252,74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="38531@1" ObjectIDZND0="0@x" ObjectIDZND1="g_32ae300@0" ObjectIDZND2="38530@x" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="g_32ae300_0" Pin0InfoVect2LinkObjId="SW-230996_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230997_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2211,62 2211,74 2252,74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32ad3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2183,62 2183,214 2252,214 2252,195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="38531@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="EC-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230997_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2183,62 2183,214 2252,214 2252,195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32ad640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2252,84 2252,75 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="38531@x" ObjectIDZND1="g_32ae300@0" ObjectIDZND2="38530@x" Pin0InfoVect0LinkObjId="SW-230997_0" Pin0InfoVect1LinkObjId="g_32ae300_0" Pin0InfoVect2LinkObjId="SW-230996_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2252,84 2252,75 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32adbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2252,74 2252,56 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="38531@x" ObjectIDND2="g_32ae300@0" ObjectIDZND0="38530@0" Pin0InfoVect0LinkObjId="SW-230996_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="SW-230997_0" Pin1InfoVect2LinkObjId="g_32ae300_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2252,74 2252,56 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32ade40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2252,20 2252,-22 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="38530@1" ObjectIDZND0="g_2141450@0" Pin0InfoVect0LinkObjId="g_2141450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230996_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2252,20 2252,-22 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32ae0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2252,74 2281,74 2281,85 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="38531@x" ObjectIDND2="38530@x" ObjectIDZND0="g_32ae300@0" Pin0InfoVect0LinkObjId="g_32ae300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="SW-230997_0" Pin1InfoVect2LinkObjId="SW-230996_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2252,74 2281,74 2281,85 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32b5fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2416,-89 2416,0 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="38545@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="EC-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231008_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2416,-89 2416,0 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32b6210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2416,-205 2416,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="38477@0" ObjectIDZND0="38544@0" Pin0InfoVect0LinkObjId="SW-231008_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b9f5e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2416,-205 2416,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32b71c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2416,-106 2416,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="38545@1" ObjectIDZND0="g_32b6aa0@1" Pin0InfoVect0LinkObjId="g_32b6aa0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-231008_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2416,-106 2416,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32b7420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2416,-145 2416,-154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_32b6aa0@0" ObjectIDZND0="38544@1" Pin0InfoVect0LinkObjId="SW-231008_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32b6aa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2416,-145 2416,-154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32ba2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1873,-234 1873,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="38488@0" ObjectIDZND0="38477@0" Pin0InfoVect0LinkObjId="g_2b9f5e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230929_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1873,-234 1873,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32ba9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1873,-250 1873,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38488@1" ObjectIDZND0="38487@0" Pin0InfoVect0LinkObjId="SW-230928_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230929_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1873,-250 1873,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32babb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1873,-289 1873,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="38487@1" ObjectIDZND0="38489@1" Pin0InfoVect0LinkObjId="SW-230929_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230928_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1873,-289 1873,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b45c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-955 1312,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="38491@1" ObjectIDZND0="38495@x" ObjectIDZND1="g_296eb60@0" Pin0InfoVect0LinkObjId="SW-230941_0" Pin0InfoVect1LinkObjId="g_296eb60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-230938_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1312,-955 1312,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b45ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-982 1335,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="38491@x" ObjectIDND1="g_296eb60@0" ObjectIDZND0="38495@0" Pin0InfoVect0LinkObjId="SW-230941_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-230938_0" Pin1InfoVect1LinkObjId="g_296eb60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1312,-982 1335,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b46140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-1003 1277,-1003 1277,-994 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="38491@x" ObjectIDND1="38495@x" ObjectIDZND0="g_296eb60@0" Pin0InfoVect0LinkObjId="g_296eb60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-230938_0" Pin1InfoVect1LinkObjId="SW-230941_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1312,-1003 1277,-1003 1277,-994 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b46c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-982 1312,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="38491@x" ObjectIDND1="38495@x" ObjectIDZND0="g_296eb60@0" Pin0InfoVect0LinkObjId="g_296eb60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-230938_0" Pin1InfoVect1LinkObjId="SW-230941_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1312,-982 1312,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b46e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1312,-1003 1312,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_296eb60@0" ObjectIDND1="38491@x" ObjectIDND2="38495@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_296eb60_0" Pin1InfoVect1LinkObjId="SW-230938_0" Pin1InfoVect2LinkObjId="SW-230941_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1312,-1003 1312,-1030 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="38476" cx="1321" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38476" cx="818" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38476" cx="655" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38476" cx="1061" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38476" cx="494" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38476" cx="948" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38476" cx="1061" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38476" cx="1229" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38477" cx="1476" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38477" cx="1726" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38477" cx="1874" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38477" cx="1600" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38475" cx="1312" cy="-708" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38475" cx="1061" cy="-708" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38475" cx="1260" cy="-708" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38475" cx="1873" cy="-708" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38477" cx="2042" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38477" cx="2252" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38477" cx="2416" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="38477" cx="1873" cy="-205" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-230736" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 292.500000 -972.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38447" ObjectName="DYN-CX_LMQ"/>
     <cge:Meas_Ref ObjectId="230736"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28cbdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28cbdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28cbdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28cbdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28cbdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28cbdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28cbdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28cbdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28cbdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28cbdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28cbdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28cbdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28cbdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28cbdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28cbdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28cbdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28cbdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28cbdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b35090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b35090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b35090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b35090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b35090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b35090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b35090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2b35350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 152.000000 -1001.500000) translate(0,16)">龙马箐变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b16c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 873.000000 -475.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b16c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 873.000000 -475.000000) translate(0,33)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b16c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 873.000000 -475.000000) translate(0,51)">35±3x2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b16c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 873.000000 -475.000000) translate(0,69)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28194c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1194.500000 -1066.000000) translate(0,15)">35kV连厂线及弥兴T接线、龙马箐T接线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2888610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1681.000000 220.500000) translate(0,15)">万家隧洞进口线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ba0690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1689.000000 -467.000000) translate(0,15)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ba0690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1689.000000 -467.000000) translate(0,33)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ba0690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1689.000000 -467.000000) translate(0,51)">35±3x2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ba0690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1689.000000 -467.000000) translate(0,69)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b07c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1221.500000 -465.000000) translate(0,15)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2983d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 419.000000 229.000000) translate(0,15)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_294d190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 626.000000 229.000000) translate(0,15)">板凳山线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2954010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 793.000000 229.000000) translate(0,15)">龙马箐石料厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2954f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 908.000000 93.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32cbb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1188.500000 41.000000) translate(0,15)">10kVⅠ母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3389ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1559.500000 41.000000) translate(0,15)">10kVⅡ母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28664d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1838.000000 220.500000) translate(0,15)">万家四号隧洞线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28687d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2006.000000 220.500000) translate(0,15)">万家隧洞出口线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32aabe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2177.000000 222.000000) translate(0,15)">2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32b6470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2376.000000 95.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32b7c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 994.000000 229.000000) translate(0,15)">柳家村隧洞线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32b87a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1321.000000 -783.000000) translate(0,12)">375</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32b8a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1340.000000 -900.000000) translate(0,12)">37560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32b8c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1319.000000 -944.000000) translate(0,12)">3756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32b8ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1338.000000 -1008.000000) translate(0,12)">37567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32b90e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1073.000000 -734.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32b9320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1070.000000 -640.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32b9560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1070.000000 -282.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32b97a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1091.000000 -467.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32b99e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1903.000000 -468.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32b9c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1882.000000 -641.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32b9e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1272.000000 -651.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32ba0a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1882.000000 -283.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32badc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 421.000000 -225.000000) translate(0,12)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32bb210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1441.000000 -225.000000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32bb450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 503.000000 -143.000000) translate(0,12)">031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32bb690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 501.000000 32.000000) translate(0,12)">0316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32bb8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 376.000000 49.000000) translate(0,12)">03167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32bbb10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 535.000000 -69.000000) translate(0,12)">03160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32bbd50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 664.000000 -143.000000) translate(0,12)">032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32bbf90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 696.000000 -59.000000) translate(0,12)">03260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32bc1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 827.000000 -141.000000) translate(0,12)">033</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32bc410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 859.000000 -57.000000) translate(0,12)">03360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32bc650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 963.000000 -133.000000) translate(0,12)">0341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32bc890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1070.000000 -140.000000) translate(0,12)">035</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32bcad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1102.000000 -56.000000) translate(0,12)">03560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32bcd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1241.000000 -147.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32bcf50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1615.000000 -146.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32bd190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1485.000000 -144.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32bd3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1333.000000 -145.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32bd610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1735.000000 -143.000000) translate(0,12)">041</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32bd850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1767.000000 -59.000000) translate(0,12)">04160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32bda90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1883.000000 -143.000000) translate(0,12)">042</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32bdcd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1915.000000 -59.000000) translate(0,12)">04260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32bdf10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2051.000000 -143.000000) translate(0,12)">043</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32be150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2083.000000 -59.000000) translate(0,12)">04360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32be390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2261.000000 -145.000000) translate(0,12)">044</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32be5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2293.000000 -71.000000) translate(0,12)">04460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32be810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2259.000000 30.000000) translate(0,12)">0446</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32bea50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2125.000000 40.000000) translate(0,12)">04467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32bec90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2430.000000 -137.000000) translate(0,12)">0451</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b3c940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43.000000 -659.000000) translate(0,15)">公用信号</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-230937">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1303.000000 -754.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38490" ObjectName="SW-CX_LMQ.CX_LMQ_375BK"/>
     <cge:Meas_Ref ObjectId="230937"/>
    <cge:TPSR_Ref TObjectID="38490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230962">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 646.000000 -114.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38502" ObjectName="SW-CX_LMQ.CX_LMQ_032BK"/>
     <cge:Meas_Ref ObjectId="230962"/>
    <cge:TPSR_Ref TObjectID="38502"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230967">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 809.000000 -112.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38506" ObjectName="SW-CX_LMQ.CX_LMQ_033BK"/>
     <cge:Meas_Ref ObjectId="230967"/>
    <cge:TPSR_Ref TObjectID="38506"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230999">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1467.000000 -115.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38532" ObjectName="SW-CX_LMQ.CX_LMQ_012BK"/>
     <cge:Meas_Ref ObjectId="230999"/>
    <cge:TPSR_Ref TObjectID="38532"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230977">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1717.000000 -114.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38514" ObjectName="SW-CX_LMQ.CX_LMQ_041BK"/>
     <cge:Meas_Ref ObjectId="230977"/>
    <cge:TPSR_Ref TObjectID="38514"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230982">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1865.000000 -114.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38518" ObjectName="SW-CX_LMQ.CX_LMQ_042BK"/>
     <cge:Meas_Ref ObjectId="230982"/>
    <cge:TPSR_Ref TObjectID="38518"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230917">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1052.000000 -253.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38481" ObjectName="SW-CX_LMQ.CX_LMQ_001BK"/>
     <cge:Meas_Ref ObjectId="230917"/>
    <cge:TPSR_Ref TObjectID="38481"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230915">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1052.000000 -611.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38478" ObjectName="SW-CX_LMQ.CX_LMQ_301BK"/>
     <cge:Meas_Ref ObjectId="230915"/>
    <cge:TPSR_Ref TObjectID="38478"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230928">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1864.000000 -254.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38487" ObjectName="SW-CX_LMQ.CX_LMQ_002BK"/>
     <cge:Meas_Ref ObjectId="230928"/>
    <cge:TPSR_Ref TObjectID="38487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230926">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1864.000000 -611.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38484" ObjectName="SW-CX_LMQ.CX_LMQ_302BK"/>
     <cge:Meas_Ref ObjectId="230926"/>
    <cge:TPSR_Ref TObjectID="38484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230956">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 485.000000 -114.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38496" ObjectName="SW-CX_LMQ.CX_LMQ_031BK"/>
     <cge:Meas_Ref ObjectId="230956"/>
    <cge:TPSR_Ref TObjectID="38496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230972">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1052.000000 -111.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38510" ObjectName="SW-CX_LMQ.CX_LMQ_035BK"/>
     <cge:Meas_Ref ObjectId="230972"/>
    <cge:TPSR_Ref TObjectID="38510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230987">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2033.000000 -114.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38522" ObjectName="SW-CX_LMQ.CX_LMQ_043BK"/>
     <cge:Meas_Ref ObjectId="230987"/>
    <cge:TPSR_Ref TObjectID="38522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-230993">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2243.000000 -116.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38526" ObjectName="SW-CX_LMQ.CX_LMQ_044BK"/>
     <cge:Meas_Ref ObjectId="230993"/>
    <cge:TPSR_Ref TObjectID="38526"/></metadata>
   </g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 475.000000 202.000000)" xlink:href="#capacitor:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2233.000000 200.000000)" xlink:href="#capacitor:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2284aa0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 650.000000 7.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b1bba0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 614.000000 -16.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_213a540">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 813.000000 9.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28cd750">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 777.000000 -14.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21a0b50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1721.000000 7.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2887680">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1685.000000 -16.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2180f50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1869.000000 7.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2184e20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1833.000000 -16.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_285c710">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1302.000000 -854.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_296eb60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1270.000000 -940.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_296f890">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1325.000000 -834.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2970600">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1056.000000 -513.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b0be80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1056.000000 -334.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b0cfc0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1868.000000 -514.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2945030">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1868.000000 -335.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b061b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1209.000000 -514.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b06ee0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1251.000000 -533.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21a9910">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 489.000000 -12.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21ad7e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 453.000000 -26.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2988020">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 516.000000 141.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29551e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 939.000000 -110.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_218e9d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1056.000000 10.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21928a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1020.000000 -13.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32c9870">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1178.000000 -10.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32ca5a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1220.000000 -29.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3388470">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1549.000000 -10.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33891a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1591.000000 -29.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2861670">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2037.000000 7.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2865540">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2001.000000 -16.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2141450">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2247.000000 -17.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2145320">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2211.000000 -28.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32ae300">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2274.000000 139.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32b6aa0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2407.000000 -108.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="1" id="ME-230847" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 514.000000 -315.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230847" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38476"/>
     <cge:Term_Ref ObjectID="57618"/>
    <cge:TPSR_Ref TObjectID="38476"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="1" id="ME-230848" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 514.000000 -315.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230848" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38476"/>
     <cge:Term_Ref ObjectID="57618"/>
    <cge:TPSR_Ref TObjectID="38476"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="1" id="ME-230849" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 514.000000 -315.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230849" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38476"/>
     <cge:Term_Ref ObjectID="57618"/>
    <cge:TPSR_Ref TObjectID="38476"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-230853" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 514.000000 -315.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230853" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38476"/>
     <cge:Term_Ref ObjectID="57618"/>
    <cge:TPSR_Ref TObjectID="38476"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="1" id="ME-230850" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 514.000000 -315.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230850" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38476"/>
     <cge:Term_Ref ObjectID="57618"/>
    <cge:TPSR_Ref TObjectID="38476"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-230854" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2448.000000 -295.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230854" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38477"/>
     <cge:Term_Ref ObjectID="57619"/>
    <cge:TPSR_Ref TObjectID="38477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-230855" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2448.000000 -295.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230855" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38477"/>
     <cge:Term_Ref ObjectID="57619"/>
    <cge:TPSR_Ref TObjectID="38477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-230856" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2448.000000 -295.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230856" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38477"/>
     <cge:Term_Ref ObjectID="57619"/>
    <cge:TPSR_Ref TObjectID="38477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-230860" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2448.000000 -295.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230860" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38477"/>
     <cge:Term_Ref ObjectID="57619"/>
    <cge:TPSR_Ref TObjectID="38477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-230857" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2448.000000 -295.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230857" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38477"/>
     <cge:Term_Ref ObjectID="57619"/>
    <cge:TPSR_Ref TObjectID="38477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-230840" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 952.000000 -815.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230840" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38475"/>
     <cge:Term_Ref ObjectID="57617"/>
    <cge:TPSR_Ref TObjectID="38475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-230841" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 952.000000 -815.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230841" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38475"/>
     <cge:Term_Ref ObjectID="57617"/>
    <cge:TPSR_Ref TObjectID="38475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-230842" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 952.000000 -815.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230842" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38475"/>
     <cge:Term_Ref ObjectID="57617"/>
    <cge:TPSR_Ref TObjectID="38475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-230846" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 952.000000 -815.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230846" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38475"/>
     <cge:Term_Ref ObjectID="57617"/>
    <cge:TPSR_Ref TObjectID="38475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-230843" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 952.000000 -815.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230843" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38475"/>
     <cge:Term_Ref ObjectID="57617"/>
    <cge:TPSR_Ref TObjectID="38475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-230801" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1149.000000 -444.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230801" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38547"/>
     <cge:Term_Ref ObjectID="57761"/>
    <cge:TPSR_Ref TObjectID="38547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-230800" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1149.000000 -444.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230800" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38547"/>
     <cge:Term_Ref ObjectID="57761"/>
    <cge:TPSR_Ref TObjectID="38547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-230827" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1986.000000 -473.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230827" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38548"/>
     <cge:Term_Ref ObjectID="57765"/>
    <cge:TPSR_Ref TObjectID="38548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-230826" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1986.000000 -473.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230826" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38548"/>
     <cge:Term_Ref ObjectID="57765"/>
    <cge:TPSR_Ref TObjectID="38548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-230837" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1434.000000 -796.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230837" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38490"/>
     <cge:Term_Ref ObjectID="57644"/>
    <cge:TPSR_Ref TObjectID="38490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-230838" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1434.000000 -796.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230838" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38490"/>
     <cge:Term_Ref ObjectID="57644"/>
    <cge:TPSR_Ref TObjectID="38490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-230828" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1434.000000 -796.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230828" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38490"/>
     <cge:Term_Ref ObjectID="57644"/>
    <cge:TPSR_Ref TObjectID="38490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-230797" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 964.000000 -295.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230797" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38481"/>
     <cge:Term_Ref ObjectID="57626"/>
    <cge:TPSR_Ref TObjectID="38481"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-230798" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 964.000000 -295.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230798" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38481"/>
     <cge:Term_Ref ObjectID="57626"/>
    <cge:TPSR_Ref TObjectID="38481"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-230788" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 964.000000 -295.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230788" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38481"/>
     <cge:Term_Ref ObjectID="57626"/>
    <cge:TPSR_Ref TObjectID="38481"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-230823" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1750.000000 -291.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230823" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38487"/>
     <cge:Term_Ref ObjectID="57638"/>
    <cge:TPSR_Ref TObjectID="38487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-230824" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1750.000000 -291.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230824" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38487"/>
     <cge:Term_Ref ObjectID="57638"/>
    <cge:TPSR_Ref TObjectID="38487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-230814" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1750.000000 -291.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230814" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38487"/>
     <cge:Term_Ref ObjectID="57638"/>
    <cge:TPSR_Ref TObjectID="38487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-230864" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1409.000000 -68.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230864" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38532"/>
     <cge:Term_Ref ObjectID="57728"/>
    <cge:TPSR_Ref TObjectID="38532"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-230865" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1409.000000 -68.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230865" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38532"/>
     <cge:Term_Ref ObjectID="57728"/>
    <cge:TPSR_Ref TObjectID="38532"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-230861" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1409.000000 -68.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230861" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38532"/>
     <cge:Term_Ref ObjectID="57728"/>
    <cge:TPSR_Ref TObjectID="38532"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-0" prefix="" rightAlign="0">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 434.000000 259.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38496"/>
     <cge:Term_Ref ObjectID="57656"/>
    <cge:TPSR_Ref TObjectID="38496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-230906" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 434.000000 259.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230906" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38496"/>
     <cge:Term_Ref ObjectID="57656"/>
    <cge:TPSR_Ref TObjectID="38496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-230903" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 434.000000 259.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230903" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38496"/>
     <cge:Term_Ref ObjectID="57656"/>
    <cge:TPSR_Ref TObjectID="38496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-230870" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 633.000000 259.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230870" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38502"/>
     <cge:Term_Ref ObjectID="57668"/>
    <cge:TPSR_Ref TObjectID="38502"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-230871" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 633.000000 259.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230871" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38502"/>
     <cge:Term_Ref ObjectID="57668"/>
    <cge:TPSR_Ref TObjectID="38502"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-230867" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 633.000000 259.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230867" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38502"/>
     <cge:Term_Ref ObjectID="57668"/>
    <cge:TPSR_Ref TObjectID="38502"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-230876" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 792.000000 259.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230876" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38506"/>
     <cge:Term_Ref ObjectID="57676"/>
    <cge:TPSR_Ref TObjectID="38506"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-230877" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 792.000000 259.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230877" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38506"/>
     <cge:Term_Ref ObjectID="57676"/>
    <cge:TPSR_Ref TObjectID="38506"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-230873" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 792.000000 259.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230873" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38506"/>
     <cge:Term_Ref ObjectID="57676"/>
    <cge:TPSR_Ref TObjectID="38506"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-230882" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1026.000000 259.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230882" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38510"/>
     <cge:Term_Ref ObjectID="57684"/>
    <cge:TPSR_Ref TObjectID="38510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-230883" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1026.000000 259.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230883" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38510"/>
     <cge:Term_Ref ObjectID="57684"/>
    <cge:TPSR_Ref TObjectID="38510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-230879" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1026.000000 259.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230879" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38510"/>
     <cge:Term_Ref ObjectID="57684"/>
    <cge:TPSR_Ref TObjectID="38510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-230888" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1712.000000 252.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230888" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38514"/>
     <cge:Term_Ref ObjectID="57692"/>
    <cge:TPSR_Ref TObjectID="38514"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-230889" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1712.000000 252.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230889" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38514"/>
     <cge:Term_Ref ObjectID="57692"/>
    <cge:TPSR_Ref TObjectID="38514"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-230885" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1712.000000 252.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230885" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38514"/>
     <cge:Term_Ref ObjectID="57692"/>
    <cge:TPSR_Ref TObjectID="38514"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-230894" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1857.000000 252.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230894" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38518"/>
     <cge:Term_Ref ObjectID="57700"/>
    <cge:TPSR_Ref TObjectID="38518"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-230895" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1857.000000 252.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230895" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38518"/>
     <cge:Term_Ref ObjectID="57700"/>
    <cge:TPSR_Ref TObjectID="38518"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-230891" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1857.000000 252.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230891" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38518"/>
     <cge:Term_Ref ObjectID="57700"/>
    <cge:TPSR_Ref TObjectID="38518"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-230900" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2010.000000 252.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230900" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38522"/>
     <cge:Term_Ref ObjectID="57708"/>
    <cge:TPSR_Ref TObjectID="38522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-230901" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2010.000000 252.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230901" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38522"/>
     <cge:Term_Ref ObjectID="57708"/>
    <cge:TPSR_Ref TObjectID="38522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-230897" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2010.000000 252.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230897" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38522"/>
     <cge:Term_Ref ObjectID="57708"/>
    <cge:TPSR_Ref TObjectID="38522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-0" prefix="" rightAlign="0">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2203.000000 252.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38526"/>
     <cge:Term_Ref ObjectID="57716"/>
    <cge:TPSR_Ref TObjectID="38526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-230911" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2203.000000 252.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230911" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38526"/>
     <cge:Term_Ref ObjectID="57716"/>
    <cge:TPSR_Ref TObjectID="38526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-230908" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2203.000000 252.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230908" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38526"/>
     <cge:Term_Ref ObjectID="57716"/>
    <cge:TPSR_Ref TObjectID="38526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-230785" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 955.000000 -650.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230785" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38478"/>
     <cge:Term_Ref ObjectID="57620"/>
    <cge:TPSR_Ref TObjectID="38478"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-230786" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 955.000000 -650.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230786" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38478"/>
     <cge:Term_Ref ObjectID="57620"/>
    <cge:TPSR_Ref TObjectID="38478"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-230776" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 955.000000 -650.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230776" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38478"/>
     <cge:Term_Ref ObjectID="57620"/>
    <cge:TPSR_Ref TObjectID="38478"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-230811" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1774.000000 -652.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230811" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38484"/>
     <cge:Term_Ref ObjectID="57632"/>
    <cge:TPSR_Ref TObjectID="38484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-230812" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1774.000000 -652.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230812" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38484"/>
     <cge:Term_Ref ObjectID="57632"/>
    <cge:TPSR_Ref TObjectID="38484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-230802" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1774.000000 -652.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230802" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="38484"/>
     <cge:Term_Ref ObjectID="57632"/>
    <cge:TPSR_Ref TObjectID="38484"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变35.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="119" y="-1012"/></g>
   <g href="cx_索引_接线图_客户变35.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="70" y="-1029"/></g>
   <g href="35kV龙马箐变35kV龙马箐T接线间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1321" y="-783"/></g>
   <g href="35kV龙马箐变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="1091" y="-467"/></g>
   <g href="35kV龙马箐变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="1903" y="-468"/></g>
   <g href="35kV龙马箐变10kV1号电容器组031间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="503" y="-143"/></g>
   <g href="35kV龙马箐变10kV板凳山线032间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="664" y="-143"/></g>
   <g href="35kV龙马箐变10kV万家隧洞进口线033间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="827" y="-141"/></g>
   <g href="35kV龙马箐变10kV柳家村隧洞线035间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1070" y="-140"/></g>
   <g href="35kV龙马箐变10kV分段012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="1485" y="-144"/></g>
   <g href="35kV龙马箐变10kV龙马箐石料厂线041间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1735" y="-143"/></g>
   <g href="35kV龙马箐变10kV万家四号隧洞线042间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1883" y="-143"/></g>
   <g href="35kV龙马箐变10kV万家隧洞出口线043间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2051" y="-143"/></g>
   <g href="35kV龙马箐变10kV2号电容器组044间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2261" y="-145"/></g>
   <g href="35kV龙马箐变CX_LMQ_GG间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="72" x="43" y="-659"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2439610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 463.000000 271.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2439c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 447.000000 256.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2439e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 457.000000 317.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243a0c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 457.000000 302.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243a300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 457.000000 287.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243a630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1628.000000 -282.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243a890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1597.000000 -252.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243aad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1622.000000 -267.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243ae00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 341.000000 -256.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243b060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 355.000000 -286.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243b2a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 331.000000 -271.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243b5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 903.000000 296.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243b830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 917.000000 266.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243ba70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 893.000000 281.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243bda0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1688.000000 292.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243c000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1702.000000 262.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243c240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1678.000000 277.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243c570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1716.000000 652.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243c7d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1730.000000 622.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243ca10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1706.000000 637.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243cd40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 897.000000 650.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243cfa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 911.000000 620.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243d1e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 887.000000 635.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243d510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1375.000000 797.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243d770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1389.000000 767.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243d9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1365.000000 782.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243e860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2394.000000 251.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243eab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2378.000000 236.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243ecf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2388.000000 297.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_243ef30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2388.000000 282.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28556c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2388.000000 267.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28559f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1352.000000 69.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2855c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1366.000000 39.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2855e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1342.000000 54.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28561c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 899.000000 769.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2856430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 883.000000 754.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2856670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 893.000000 815.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28568b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 893.000000 800.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2856af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 893.000000 785.000000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_LMQ.CX_LMQ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="57760"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1023.000000 -409.000000)" xlink:href="#transformer2:shape22_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1023.000000 -409.000000)" xlink:href="#transformer2:shape22_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="38547" ObjectName="TF-CX_LMQ.CX_LMQ_1T"/>
    <cge:TPSR_Ref TObjectID="38547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_LMQ.CX_LMQ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="57764"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1835.000000 -410.000000)" xlink:href="#transformer2:shape22_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1835.000000 -410.000000)" xlink:href="#transformer2:shape22_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="38548" ObjectName="TF-CX_LMQ.CX_LMQ_2T"/>
    <cge:TPSR_Ref TObjectID="38548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 935.000000 73.000000)" xlink:href="#transformer2:shape95_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 935.000000 73.000000)" xlink:href="#transformer2:shape95_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2403.000000 75.000000)" xlink:href="#transformer2:shape95_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2403.000000 75.000000)" xlink:href="#transformer2:shape95_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="119" y="-1012"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="119" y="-1012"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="70" y="-1029"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="70" y="-1029"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1321" y="-783"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1321" y="-783"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="1091" y="-467"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="1091" y="-467"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="1903" y="-468"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="1903" y="-468"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="503" y="-143"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="503" y="-143"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="664" y="-143"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="664" y="-143"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="827" y="-141"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="827" y="-141"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1070" y="-140"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1070" y="-140"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="1485" y="-144"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="1485" y="-144"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1735" y="-143"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1735" y="-143"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1883" y="-143"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1883" y="-143"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2051" y="-143"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2051" y="-143"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2261" y="-145"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2261" y="-145"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="72" x="43" y="-659"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="72" x="43" y="-659"/></g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_296ddb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1264.000000 -776.000000)" xlink:href="#voltageTransformer:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b027e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1243.000000 -479.000000)" xlink:href="#voltageTransformer:shape144"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32c6730">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1212.000000 25.000000)" xlink:href="#voltageTransformer:shape144"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3385330">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1583.000000 25.000000)" xlink:href="#voltageTransformer:shape144"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 107.000000 -953.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_LMQ"/>
</svg>