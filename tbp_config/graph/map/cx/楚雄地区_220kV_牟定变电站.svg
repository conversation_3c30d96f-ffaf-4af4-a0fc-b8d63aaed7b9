<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-20" aopId="0" id="thSvg" viewBox="3025 -1240 2245 1260">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.208305" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.208305" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line stroke-width="0.5" x1="15" x2="1" y1="35" y2="10"/>
    <line stroke-width="0.5" x1="14" x2="14" y1="9" y2="9"/>
    <line stroke-width="0.5" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.208305" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line stroke-width="0.5" x1="15" x2="1" y1="35" y2="10"/>
    <line stroke-width="0.5" x1="14" x2="14" y1="9" y2="9"/>
    <line stroke-width="0.5" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.208305" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.208305" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.208305" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line stroke-width="0.5" x1="37" x2="10" y1="15" y2="4"/>
    <line stroke-width="0.5" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.208305" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line stroke-width="0.5" x1="37" x2="10" y1="15" y2="4"/>
    <line stroke-width="0.5" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.208305" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="earth:shape3">
    <line stroke-width="0.298089" x1="13" x2="13" y1="12" y2="0"/>
    <line stroke-width="0.125874" x1="13" x2="4" y1="6" y2="6"/>
    <line stroke-width="0.161499" x1="17" x2="17" y1="3" y2="9"/>
    <line stroke-width="0.114441" x1="20" x2="20" y1="5" y2="7"/>
   </symbol>
   <symbol id="earth:shape0">
    <line stroke-width="0.298089" x1="12" x2="0" y1="9" y2="9"/>
    <line stroke-width="0.114441" x1="5" x2="7" y1="2" y2="2"/>
    <line stroke-width="0.161499" x1="3" x2="9" y1="6" y2="6"/>
    <line stroke-width="0.125874" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <line stroke-width="1" x1="10" x2="10" y1="20" y2="23"/>
    <polyline points="19,44 10,32 1,44 19,44 " stroke-width="1"/>
    <line stroke-width="1" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.25" width="16" x="1" y="5"/>
    <line stroke-width="0.5" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape56">
    <line stroke-width="0.5" x1="42" x2="42" y1="17" y2="9"/>
    <line stroke-width="0.215981" x1="52" x2="52" y1="52" y2="18"/>
    <line stroke-width="0.5" x1="7" x2="7" y1="19" y2="28"/>
    <line stroke-width="0.305732" x1="13" x2="1" y1="19" y2="19"/>
    <line stroke-width="0.196875" x1="3" x2="11" y1="16" y2="16"/>
    <line stroke-width="0.125" x1="5" x2="8" y1="13" y2="13"/>
    <line stroke-width="0.144552" x1="16" x2="36" y1="24" y2="24"/>
    <line stroke-width="0.144552" x1="16" x2="36" y1="16" y2="16"/>
    <line stroke-width="0.25228" x1="26" x2="26" y1="16" y2="3"/>
    <line stroke-width="0.137731" x1="43" x2="43" y1="2" y2="6"/>
    <line stroke-width="0.137731" x1="8" x2="8" y1="2" y2="6"/>
    <line stroke-width="0.354167" x1="7" x2="7" y1="63" y2="33"/>
    <line stroke-width="0.169067" x1="8" x2="42" y1="64" y2="64"/>
    <rect height="26" stroke-width="0.19902" width="12" x="20" y="32"/>
    <rect height="28" stroke-width="0.19902" width="12" x="1" y="27"/>
    <line stroke-width="0.197576" x1="26" x2="41" y1="9" y2="9"/>
    <line stroke-width="0.18441" x1="9" x2="42" y1="2" y2="2"/>
    <line stroke-width="0.250746" x1="26" x2="26" y1="74" y2="24"/>
    <polyline fill="none" points="43,42 44,42 45,42 45,42 46,43 46,43 47,44 47,44 48,45 48,45 48,46 48,47 49,48 49,49 49,49 48,50 48,51 48,52 48,52 47,53 47,53 46,54 46,54 45,55 45,55 44,55 43,55 "/>
    <polyline fill="none" points="43,29 44,29 45,29 45,29 46,30 46,30 47,31 47,31 48,32 48,32 48,33 48,34 49,35 49,36 49,36 48,37 48,38 48,39 48,39 47,40 47,40 46,41 46,41 45,42 45,42 44,42 43,42 "/>
    <polyline fill="none" points="43,17 44,17 45,17 45,17 46,18 46,18 47,19 47,19 48,20 48,20 48,21 48,22 49,23 49,24 49,24 48,25 48,26 48,27 48,27 47,28 47,28 46,29 46,29 45,30 45,30 44,30 43,30 "/>
    <line stroke-width="0.5" x1="43" x2="43" y1="63" y2="55"/>
   </symbol>
   <symbol id="lightningRod:shape20">
    <polyline points="2,51 16,51 9,39 2,51 "/>
    <line stroke-width="0.999997" x1="7" x2="12" y1="2" y2="2"/>
    <line stroke-width="0.999996" x1="2" x2="17" y1="9" y2="9"/>
    <line stroke-width="0.999996" x1="5" x2="14" y1="6" y2="6"/>
    <line stroke-width="1" x1="10" x2="10" y1="9" y2="23"/>
    <polyline points="2,23 16,23 9,35 2,23 "/>
    <line stroke-width="1" x1="10" x2="10" y1="65" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="28" stroke-width="0.5" width="14" x="0" y="16"/>
    <line stroke-width="0.125" x1="5" x2="8" y1="57" y2="57"/>
    <line stroke-width="0.196875" x1="3" x2="11" y1="54" y2="54"/>
    <line stroke-width="0.111888" x1="7" x2="7" y1="51" y2="44"/>
    <line stroke-width="0.305732" x1="13" x2="1" y1="52" y2="51"/>
    <line stroke-width="0.5" x1="7" x2="7" y1="4" y2="39"/>
   </symbol>
   <symbol id="lightningRod:shape29">
    <ellipse cx="11" cy="15" rx="10.5" ry="11.5"/>
    <ellipse cx="11" cy="28" rx="10.5" ry="11.5"/>
   </symbol>
   <symbol id="lightningRod:shape87">
    <line stroke-width="0.125" x1="29" x2="32" y1="2" y2="2"/>
    <line stroke-width="0.5" x1="54" x2="54" y1="26" y2="16"/>
    <circle cx="54" cy="33" r="8.5"/>
    <circle cx="44" cy="32" r="8.5"/>
    <ellipse cx="47" cy="42" rx="8.5" ry="8"/>
    <line stroke-width="0.196875" x1="27" x2="35" y1="5" y2="5"/>
    <line stroke-width="0.125874" x1="31" x2="31" y1="8" y2="17"/>
    <line stroke-width="0.305732" x1="37" x2="25" y1="8" y2="8"/>
    <line stroke-width="0.5" x1="7" x2="53" y1="17" y2="17"/>
    <rect height="27" stroke-width="0.208333" width="14" x="0" y="43"/>
    <line stroke-width="0.5" x1="7" x2="7" y1="84" y2="47"/>
    <line stroke-width="0.5" x1="8" x2="47" y1="84" y2="84"/>
    <rect height="27" stroke-width="0.208333" width="14" x="41" y="54"/>
    <line stroke-width="0.5" x1="7" x2="7" y1="42" y2="17"/>
    <line stroke-width="0.5" x1="28" x2="28" y1="84" y2="94"/>
    <line stroke-width="0.5" x1="48" x2="48" y1="84" y2="50"/>
   </symbol>
   <symbol id="lightningRod:shape59">
    <line stroke-width="0.5" x1="10" x2="10" y1="72" y2="64"/>
    <line stroke-width="0.5" x1="6" x2="12" y1="8" y2="8"/>
    <line stroke-width="0.5" x1="6" x2="12" y1="23" y2="23"/>
    <circle cx="9" cy="9" r="8.5"/>
    <circle cx="9" cy="20" r="8.5"/>
    <line stroke-width="0.5" x1="7" x2="13" y1="63" y2="63"/>
    <polyline fill="none" points="31,61 9,39 9,30 "/>
    <rect height="4" stroke-width="0.5" width="19" x="11" y="48"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <line stroke-width="0.5" x1="6" x2="6" y1="50" y2="42"/>
    <line stroke-width="0.5" x1="3" x2="9" y1="41" y2="41"/>
    <polyline fill="none" points="27,39 5,17 5,5 "/>
    <rect height="4" stroke-width="0.5" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <ellipse cx="11" cy="12" rx="10.5" ry="11.5"/>
    <ellipse cx="11" cy="25" rx="10.5" ry="11.5"/>
   </symbol>
   <symbol id="lightningRod:shape96">
    <line stroke-width="0.5" x1="19" x2="19" y1="56" y2="40"/>
    <line stroke-width="0.387695" x1="51" x2="64" y1="18" y2="18"/>
    <line stroke-width="0.232617" x1="56" x2="59" y1="11" y2="11"/>
    <line stroke-width="0.251403" x1="57" x2="57" y1="18" y2="24"/>
    <line stroke-width="0.452311" x1="54" x2="61" y1="14" y2="14"/>
    <line stroke-width="0.174227" x1="2" x2="2" y1="64" y2="47"/>
    <line stroke-width="0.174227" x1="9" x2="9" y1="64" y2="48"/>
    <line stroke-width="0.338352" x1="27" x2="9" y1="56" y2="56"/>
    <line stroke-width="0.174227" x1="28" x2="28" y1="64" y2="47"/>
    <line stroke-width="0.174227" x1="35" x2="35" y1="64" y2="48"/>
    <line stroke-width="0.5" x1="47" x2="36" y1="56" y2="56"/>
    <line stroke-width="0.578462" x1="23" x2="19" y1="8" y2="11"/>
    <line stroke-width="0.578485" x1="19" x2="19" y1="11" y2="15"/>
    <line stroke-width="0.578462" x1="15" x2="19" y1="8" y2="11"/>
    <line stroke-width="0.592865" x1="38" x2="33" y1="19" y2="16"/>
    <line stroke-width="0.578477" x1="33" x2="33" y1="16" y2="25"/>
    <line stroke-width="0.596084" x1="38" x2="33" y1="21" y2="25"/>
    <line stroke-width="0.578462" x1="24" x2="19" y1="27" y2="30"/>
    <line stroke-width="0.578485" x1="19" x2="19" y1="30" y2="34"/>
    <line stroke-width="0.578462" x1="15" x2="19" y1="27" y2="30"/>
    <circle cx="19" cy="29" r="11" stroke-width="0.578489"/>
    <ellipse cx="33" cy="20" rx="11.5" ry="11" stroke-width="0.578489"/>
    <circle cx="19" cy="12" r="11" stroke-width="0.578489"/>
    <polyline fill="none" points="21,31 57,31 57,23 "/>
   </symbol>
   <symbol id="reactance:shape3">
    <polyline fill="none" points="13,39 11,39 9,38 8,38 6,37 5,36 3,35 2,33 1,31 1,30 0,28 0,26 0,24 1,22 1,21 2,19 3,18 5,16 6,15 8,14 9,14 11,13 13,13 15,13 17,14 18,14 20,15 21,16 23,18 24,19 25,21 25,22 26,24 26,26 "/>
    <line stroke-width="0.24" x1="26" x2="14" y1="26" y2="26"/>
    <line stroke-width="0.22" x1="13" x2="13" y1="5" y2="26"/>
    <line stroke-width="0.166154" x1="13" x2="13" y1="39" y2="47"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line stroke-width="0.162432" x1="17" x2="0" y1="34" y2="26"/>
    <line stroke-width="0.234885" x1="-9" x2="0" y1="26" y2="26"/>
    <line stroke-width="0.1875" x1="18" x2="18" y1="27" y2="25"/>
    <line stroke-width="0.234885" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line stroke-width="0.234885" x1="-8" x2="0" y1="26" y2="26"/>
    <line stroke-width="0.1875" x1="18" x2="18" y1="24" y2="27"/>
    <line stroke-width="0.234885" x1="19" x2="27" y1="26" y2="26"/>
    <line stroke-width="0.162432" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line stroke-width="0.162432" x1="17" x2="0" y1="34" y2="26"/>
    <line stroke-width="0.234885" x1="18" x2="27" y1="26" y2="26"/>
    <line stroke-width="0.1875" x1="18" x2="18" y1="27" y2="25"/>
    <line stroke-width="0.234885" x1="-9" x2="0" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line stroke-width="0.234885" x1="-8" x2="0" y1="26" y2="26"/>
    <line stroke-width="0.1875" x1="18" x2="18" y1="24" y2="27"/>
    <line stroke-width="0.234885" x1="19" x2="27" y1="26" y2="26"/>
    <line stroke-width="0.162432" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line stroke-width="0.162432" x1="7" x2="15" y1="48" y2="31"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="49" y2="58"/>
    <line stroke-width="0.1875" x1="14" x2="16" y1="49" y2="49"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line stroke-width="0.162432" x1="15" x2="15" y1="51" y2="31"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="49" y2="58"/>
    <line stroke-width="0.1875" x1="14" x2="16" y1="49" y2="49"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line stroke-width="0.162432" x1="7" x2="15" y1="48" y2="31"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="49" y2="58"/>
    <line stroke-width="0.1875" x1="14" x2="16" y1="49" y2="49"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line stroke-width="0.162432" x1="15" x2="15" y1="51" y2="31"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="49" y2="58"/>
    <line stroke-width="0.1875" x1="14" x2="16" y1="49" y2="49"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="transformer:shape4_0">
    <circle cx="68" cy="45" r="25"/>
    <line stroke-width="0.5" x1="60" x2="60" y1="87" y2="87"/>
    <line stroke-width="0.5" x1="60" x2="58" y1="90" y2="85"/>
    <line stroke-width="0.5" x1="55" x2="60" y1="90" y2="90"/>
    <line stroke-width="0.5" x1="0" x2="60" y1="56" y2="90"/>
    <line stroke-width="0.260204" x1="74" x2="67" y1="46" y2="53"/>
    <line stroke-width="0.260204" x1="82" x2="74" y1="53" y2="46"/>
    <line stroke-width="0.260204" x1="74" x2="74" y1="37" y2="46"/>
   </symbol>
   <symbol id="transformer:shape4_1">
    <circle cx="38" cy="61" r="25"/>
    <line stroke-width="0.260204" x1="37" x2="30" y1="66" y2="73"/>
    <line stroke-width="0.260204" x1="45" x2="37" y1="73" y2="66"/>
    <line stroke-width="0.260204" x1="37" x2="37" y1="57" y2="66"/>
   </symbol>
   <symbol id="transformer:shape4-2">
    <circle cx="38" cy="29" r="24.5"/>
    <line stroke-width="0.260204" x1="47" x2="32" y1="23" y2="32"/>
    <line stroke-width="0.260204" x1="47" x2="32" y1="23" y2="15"/>
    <line stroke-width="0.260204" x1="32" x2="32" y1="32" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape3_0">
    <ellipse cx="13" cy="17" rx="13" ry="12.5"/>
    <line stroke-width="0.132653" x1="13" x2="13" y1="11" y2="15"/>
    <line stroke-width="0.132653" x1="13" x2="17" y1="15" y2="19"/>
    <line stroke-width="0.132653" x1="9" x2="13" y1="19" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape3_1">
    <circle cx="13" cy="34" r="13"/>
    <line stroke-width="0.132653" x1="13" x2="9" y1="41" y2="33"/>
    <line stroke-width="0.132653" x1="13" x2="18" y1="41" y2="33"/>
    <line stroke-width="0.132653" x1="9" x2="18" y1="33" y2="33"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">开关检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    
   </symbol>
   <symbol id="Tag:shape27">
    
   </symbol>
   <symbol id="Tag:shape28">
    
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="0.5" width="111" x="0" y="0"/>
    <line stroke="rgb(50,205,50)" stroke-width="1.5" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" r="39.5" stroke="rgb(50,205,50)"/>
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(127,127,127);fill:none}
.BKBV-0KV { stroke:rgb(127,127,127);fill:rgb(127,127,127)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(0,72,216);fill:none}
.BKBV-10KV { stroke:rgb(0,72,216);fill:rgb(0,72,216)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(0,255,0);fill:none}
.BKBV-20KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(213,0,0);fill:none}
.BKBV-110KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-220KV { stroke:rgb(255,0,255);fill:none}
.BKBV-220KV { stroke:rgb(255,0,255);fill:rgb(255,0,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(255,0,0);fill:none}
.BKBV-500KV { stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.BV-750KV { stroke:rgb(255,0,0);fill:none}
.BKBV-750KV { stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.BV-22KV { stroke:rgb(255,255,255);fill:none}
.BKBV-22KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-380KV { stroke:rgb(255,255,255);fill:none}
.BKBV-380KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1270" width="2255" x="3020" y="-1245"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" stroke="rgb(255,0,0)" stroke-width="0.5" x1="3063" x2="3063" y1="-1202" y2="-2"/>
   <line DF8003:Layer="PUBLIC" stroke="rgb(255,0,0)" stroke-width="0.5" x1="3065" x2="3105" y1="-1201" y2="-1201"/>
   <line DF8003:Layer="PUBLIC" stroke="rgb(255,0,0)" stroke-width="0.5" x1="3060" x2="3108" y1="-1078" y2="-1078"/>
   <line DF8003:Layer="PUBLIC" stroke="rgb(255,0,0)" stroke-width="0.5" x1="3065" x2="3090" y1="-594" y2="-594"/>
   <line DF8003:Layer="PUBLIC" stroke="rgb(255,0,0)" stroke-width="0.5" x1="3120" x2="5267" y1="-1212" y2="-1212"/>
   <line DF8003:Layer="PUBLIC" stroke="rgb(255,0,0)" stroke-width="0.5" x1="3118" x2="3118" y1="-1240" y2="-1213"/>
   <line DF8003:Layer="PUBLIC" stroke="rgb(255,0,0)" stroke-width="0.5" x1="3478" x2="3478" y1="-1237" y2="-1213"/>
   <line DF8003:Layer="PUBLIC" stroke="rgb(255,0,0)" stroke-width="0.5" x1="5268" x2="5268" y1="-1236" y2="-1210"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-18271">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3994.000000 -533.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2790" ObjectName="SW-CX_MD.CX_MD_431BK"/>
     <cge:Meas_Ref ObjectId="18271"/>
    <cge:TPSR_Ref TObjectID="2790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18615">
    <use class="BV-38KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3753.000000 -709.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2836" ObjectName="SW-CX_MD.CX_MD_K03BK"/>
     <cge:Meas_Ref ObjectId="18615"/>
    <cge:TPSR_Ref TObjectID="2836"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18613">
    <use class="BV-38KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3735.000000 -667.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2835" ObjectName="SW-CX_MD.CX_MD_K01BK"/>
     <cge:Meas_Ref ObjectId="18613"/>
    <cge:TPSR_Ref TObjectID="2835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18626">
    <use class="BV-38KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3799.000000 -667.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2858" ObjectName="SW-CX_MD.CX_MD_K02BK"/>
     <cge:Meas_Ref ObjectId="18626"/>
    <cge:TPSR_Ref TObjectID="2858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18503">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3611.000000 -310.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2821" ObjectName="SW-CX_MD.CX_MD_467BK"/>
     <cge:Meas_Ref ObjectId="18503"/>
    <cge:TPSR_Ref TObjectID="2821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18531">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3611.000000 -549.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2825" ObjectName="SW-CX_MD.CX_MD_468BK"/>
     <cge:Meas_Ref ObjectId="18531"/>
    <cge:TPSR_Ref TObjectID="2825"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18738">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4586.000000 -313.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2841" ObjectName="SW-CX_MD.CX_MD_481BK"/>
     <cge:Meas_Ref ObjectId="18738"/>
    <cge:TPSR_Ref TObjectID="2841"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18475">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4481.000000 -313.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2818" ObjectName="SW-CX_MD.CX_MD_465BK"/>
     <cge:Meas_Ref ObjectId="18475"/>
    <cge:TPSR_Ref TObjectID="2818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18325">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4351.000000 -313.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2793" ObjectName="SW-CX_MD.CX_MD_461BK"/>
     <cge:Meas_Ref ObjectId="18325"/>
    <cge:TPSR_Ref TObjectID="2793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18355">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4201.000000 -313.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2798" ObjectName="SW-CX_MD.CX_MD_462BK"/>
     <cge:Meas_Ref ObjectId="18355"/>
    <cge:TPSR_Ref TObjectID="2798"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18385">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4062.000000 -313.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2803" ObjectName="SW-CX_MD.CX_MD_463BK"/>
     <cge:Meas_Ref ObjectId="18385"/>
    <cge:TPSR_Ref TObjectID="2803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18415">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 -313.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2808" ObjectName="SW-CX_MD.CX_MD_464BK"/>
     <cge:Meas_Ref ObjectId="18415"/>
    <cge:TPSR_Ref TObjectID="2808"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18445">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3770.000000 -313.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2813" ObjectName="SW-CX_MD.CX_MD_466BK"/>
     <cge:Meas_Ref ObjectId="18445"/>
    <cge:TPSR_Ref TObjectID="2813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18584">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4796.000000 -973.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2832" ObjectName="SW-CX_MD.CX_MD_385BK"/>
     <cge:Meas_Ref ObjectId="18584"/>
    <cge:TPSR_Ref TObjectID="2832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-17937">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4799.000000 -719.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2748" ObjectName="SW-CX_MD.CX_MD_381BK"/>
     <cge:Meas_Ref ObjectId="17937"/>
    <cge:TPSR_Ref TObjectID="2748"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-17967">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4799.000000 -807.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2752" ObjectName="SW-CX_MD.CX_MD_382BK"/>
     <cge:Meas_Ref ObjectId="17967"/>
    <cge:TPSR_Ref TObjectID="2752"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-17997">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4799.000000 -893.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2756" ObjectName="SW-CX_MD.CX_MD_383BK"/>
     <cge:Meas_Ref ObjectId="17997"/>
    <cge:TPSR_Ref TObjectID="2756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18878">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4610.000000 -892.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2860" ObjectName="SW-CX_MD.CX_MD_321BK"/>
     <cge:Meas_Ref ObjectId="18878"/>
    <cge:TPSR_Ref TObjectID="2860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18127">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4369.000000 -988.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2777" ObjectName="SW-CX_MD.CX_MD_186BK"/>
     <cge:Meas_Ref ObjectId="18127"/>
    <cge:TPSR_Ref TObjectID="2777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18086">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3995.000000 -992.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2765" ObjectName="SW-CX_MD.CX_MD_184BK"/>
     <cge:Meas_Ref ObjectId="18086"/>
    <cge:TPSR_Ref TObjectID="2765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18027">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4794.000000 -635.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2760" ObjectName="SW-CX_MD.CX_MD_380BK"/>
     <cge:Meas_Ref ObjectId="18027"/>
    <cge:TPSR_Ref TObjectID="2760"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18056">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4797.000000 -464.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2761" ObjectName="SW-CX_MD.CX_MD_384BK"/>
     <cge:Meas_Ref ObjectId="18056"/>
    <cge:TPSR_Ref TObjectID="2761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18870">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4797.000000 -562.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2849" ObjectName="SW-CX_MD.CX_MD_386BK"/>
     <cge:Meas_Ref ObjectId="18870"/>
    <cge:TPSR_Ref TObjectID="2849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18105">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4208.000000 -839.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2771" ObjectName="SW-CX_MD.CX_MD_185BK"/>
     <cge:Meas_Ref ObjectId="18105"/>
    <cge:TPSR_Ref TObjectID="2771"/></metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_MD.CX_MD_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="4138"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3966.000000 -647.000000)" xlink:href="#transformer:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="4140"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3966.000000 -647.000000)" xlink:href="#transformer:shape4_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="4142"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3966.000000 -647.000000)" xlink:href="#transformer:shape4-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="2859" ObjectName="TF-CX_MD.CX_MD_1T"/>
    <cge:TPSR_Ref TObjectID="2859"/></metadata>
   </g>
  </g><g id="Reactance_Layer">
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3607.000000 -162.000000)" xlink:href="#reactance:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3607.000000 -110.000000)" xlink:href="#reactance:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3607.000000 -696.000000)" xlink:href="#reactance:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3607.000000 -748.000000)" xlink:href="#reactance:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="1157de0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4076.000000 -769.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="1623840">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3692.000000 -293.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="16261f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3691.000000 -221.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="15ea320">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3692.000000 -565.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="15ebd90">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3691.000000 -637.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="1612fa0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3894.000000 -636.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="174a890">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4279.000000 -496.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="17a7f80">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4450.000000 -971.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="17ade90">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4449.000000 -1031.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="196dec0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4450.000000 -1109.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="197a040">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4076.000000 -972.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="197ff50">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4075.000000 -1031.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="19866c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4076.000000 -1110.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="19c9060">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4158.000000 -761.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="19c9ab0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4278.000000 -761.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="162a090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4004,-732 4004,-775 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="2859@1" ObjectIDZND0="2830@x" ObjectIDZND1="2829@x" Pin0InfoVect0LinkObjId="SW-18578_0" Pin0InfoVect1LinkObjId="SW-18577_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="19bc970_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4004,-732 4004,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="15a88f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4004,-775 4023,-775 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="2859@x" ObjectIDND1="2829@x" ObjectIDZND0="2830@0" Pin0InfoVect0LinkObjId="SW-18578_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="19bc970_0" Pin1InfoVect1LinkObjId="SW-18577_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4004,-775 4023,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="1159590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4059,-775 4081,-775 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2830@1" ObjectIDZND0="1157de0@0" Pin0InfoVect0LinkObjId="1157de0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18578_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4059,-775 4081,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="166ea50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4004,-775 4004,-811 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer" EndDevType0="switch" ObjectIDND0="2830@x" ObjectIDND1="2859@x" ObjectIDZND0="2829@0" Pin0InfoVect0LinkObjId="SW-18577_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18578_0" Pin1InfoVect1LinkObjId="19bc970_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4004,-775 4004,-811 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15f8a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4003,-652 4003,-627 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="2859@2" ObjectIDZND0="2792@1" Pin0InfoVect0LinkObjId="SW-18279_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="19bc970_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4003,-652 4003,-627 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15f9ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4003,-478 4003,-428 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2791@0" ObjectIDZND0="2746@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18278_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4003,-478 4003,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15e1c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3744,-428 3744,-440 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2746@0" ObjectIDZND0="2837@0" Pin0InfoVect0LinkObjId="SW-18620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="15f9ad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3744,-428 3744,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15e1e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3744,-488 3744,-476 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="15b84a0@1" ObjectIDZND0="2837@1" Pin0InfoVect0LinkObjId="SW-18620_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="15b84a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3744,-488 3744,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15e2000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3744,-527 3744,-539 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="15b84a0@0" ObjectIDZND0="15b8a90@0" Pin0InfoVect0LinkObjId="15b8a90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="15b84a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3744,-527 3744,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="15e3830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3744,-658 3744,-673 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2835@0" ObjectIDZND0="2838@0" Pin0InfoVect0LinkObjId="SW-18621_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18613_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3744,-658 3744,-673 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="15e3a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3744,-709 3744,-719 3762,-719 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2838@1" ObjectIDZND0="2836@1" Pin0InfoVect0LinkObjId="SW-18615_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18621_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3744,-709 3744,-719 3762,-719 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="16215b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3808,-631 3808,-608 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" ObjectIDND0="2858@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18626_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3808,-631 3808,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="16231e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3620,-366 3620,-345 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2822@0" ObjectIDZND0="2821@1" Pin0InfoVect0LinkObjId="SW-18503_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18509_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3620,-366 3620,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1623400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3620,-299 3639,-299 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="2821@x" ObjectIDND1="1625470@0" ObjectIDZND0="2823@0" Pin0InfoVect0LinkObjId="SW-18510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18503_0" Pin1InfoVect1LinkObjId="1625470_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3620,-299 3639,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1623620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3675,-299 3697,-299 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2823@1" ObjectIDZND0="1623840@0" Pin0InfoVect0LinkObjId="1623840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18510_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3675,-299 3697,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1625250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3620,-318 3620,-299 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="2821@0" ObjectIDZND0="2823@x" ObjectIDZND1="1625470@0" Pin0InfoVect0LinkObjId="SW-18510_0" Pin0InfoVect1LinkObjId="1625470_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18503_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3620,-318 3620,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1625db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3619,-227 3638,-227 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="reactance" EndDevType0="switch" ObjectIDND0="1625470@0" ObjectIDND1="0@x" ObjectIDZND0="2824@0" Pin0InfoVect0LinkObjId="SW-18511_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="1625470_0" Pin1InfoVect1LinkObjId="RB-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3619,-227 3638,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1625fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3674,-227 3696,-227 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2824@1" ObjectIDZND0="16261f0@0" Pin0InfoVect0LinkObjId="16261f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18511_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3674,-227 3696,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1626b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3620,-227 3620,-244 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="reactance" EndDevType0="lightningRod" ObjectIDND0="2824@x" ObjectIDND1="0@x" ObjectIDZND0="1625470@0" Pin0InfoVect0LinkObjId="1625470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18511_0" Pin1InfoVect1LinkObjId="RB-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3620,-227 3620,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1626d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3620,-283 3620,-299 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="1625470@1" ObjectIDZND0="2823@x" ObjectIDZND1="2821@x" Pin0InfoVect0LinkObjId="SW-18510_0" Pin0InfoVect1LinkObjId="SW-18503_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="1625470_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3620,-283 3620,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1627310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3620,-227 3620,-209 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="reactance" ObjectIDND0="2824@x" ObjectIDND1="1625470@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18511_0" Pin1InfoVect1LinkObjId="1625470_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3620,-227 3620,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="16278b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3620,-167 3620,-157 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="reactance" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3620,-167 3620,-157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="1616940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3620,-115 3620,-103 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="1616b60@0" Pin0InfoVect0LinkObjId="1616b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3620,-115 3620,-103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="16188a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3620,-428 3620,-402 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2746@0" ObjectIDZND0="2822@1" Pin0InfoVect0LinkObjId="SW-18509_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="15f9ad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3620,-428 3620,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15e9ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3620,-559 3639,-559 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="2825@x" ObjectIDND1="15eae70@0" ObjectIDZND0="2827@0" Pin0InfoVect0LinkObjId="SW-18538_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18531_0" Pin1InfoVect1LinkObjId="15eae70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3620,-559 3639,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15ea100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3675,-559 3697,-559 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2827@1" ObjectIDZND0="15ea320@0" Pin0InfoVect0LinkObjId="15ea320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18538_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3675,-559 3697,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15eac50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3620,-540 3620,-559 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="2825@0" ObjectIDZND0="2827@x" ObjectIDZND1="15eae70@0" Pin0InfoVect0LinkObjId="SW-18538_0" Pin0InfoVect1LinkObjId="15eae70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18531_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3620,-540 3620,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15eb950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3619,-631 3638,-631 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="reactance" EndDevType0="switch" ObjectIDND0="15eae70@0" ObjectIDND1="0@x" ObjectIDZND0="2828@0" Pin0InfoVect0LinkObjId="SW-18539_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="15eae70_0" Pin1InfoVect1LinkObjId="RB-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3619,-631 3638,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15ebb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3674,-631 3696,-631 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2828@1" ObjectIDZND0="15ebd90@0" Pin0InfoVect0LinkObjId="15ebd90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18539_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3674,-631 3696,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15ec6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3620,-631 3620,-614 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="reactance" EndDevType0="lightningRod" ObjectIDND0="2828@x" ObjectIDND1="0@x" ObjectIDZND0="15eae70@0" Pin0InfoVect0LinkObjId="15eae70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18539_0" Pin1InfoVect1LinkObjId="RB-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3620,-631 3620,-614 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15ec8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3620,-575 3620,-559 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="15eae70@1" ObjectIDZND0="2827@x" ObjectIDZND1="2825@x" Pin0InfoVect0LinkObjId="SW-18538_0" Pin0InfoVect1LinkObjId="SW-18531_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="15eae70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3620,-575 3620,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15fefc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3620,-631 3620,-649 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="reactance" ObjectIDND0="2828@x" ObjectIDND1="15eae70@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18539_0" Pin1InfoVect1LinkObjId="15eae70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3620,-631 3620,-649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="15ff660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3620,-691 3620,-701 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="reactance" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3620,-691 3620,-701 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="15ff880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3620,-743 3620,-760 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="15ffaa0@0" Pin0InfoVect0LinkObjId="15ffaa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3620,-743 3620,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="159e650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4005,-714 3942,-714 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="2859@x" ObjectIDZND0="2831@x" ObjectIDZND1="159f580@0" ObjectIDZND2="159e870@0" Pin0InfoVect0LinkObjId="SW-18579_0" Pin0InfoVect1LinkObjId="159f580_0" Pin0InfoVect2LinkObjId="159e870_0" Pin0Num="1" Pin1InfoVect0LinkObjId="19bc970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4005,-714 3942,-714 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="1612d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3900,-667 3900,-653 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2831@0" ObjectIDZND0="1612fa0@0" Pin0InfoVect0LinkObjId="1612fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18579_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3900,-667 3900,-653 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1613890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4003,-514 4003,-541 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2791@1" ObjectIDZND0="2790@0" Pin0InfoVect0LinkObjId="SW-18271_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18278_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4003,-514 4003,-541 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1613ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4003,-568 4003,-591 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2790@1" ObjectIDZND0="2792@0" Pin0InfoVect0LinkObjId="SW-18279_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18271_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4003,-568 4003,-591 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1635f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4595,-368 4595,-348 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2842@0" ObjectIDZND0="2841@1" Pin0InfoVect0LinkObjId="SW-18738_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18744_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4595,-368 4595,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="165da70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4595,-300 4595,-321 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2843@1" ObjectIDZND0="2841@0" Pin0InfoVect0LinkObjId="SW-18738_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18745_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4595,-300 4595,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="165dcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4595,-250 4595,-264 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="2845@x" ObjectIDND1="2844@x" ObjectIDZND0="2843@0" Pin0InfoVect0LinkObjId="SW-18745_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18747_0" Pin1InfoVect1LinkObjId="SW-18746_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4595,-250 4595,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="165e330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4647,-190 4647,-202 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="2845@0" Pin0InfoVect0LinkObjId="SW-18747_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4647,-190 4647,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="165e560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4647,-238 4647,-250 4595,-250 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="2845@1" ObjectIDZND0="2843@x" ObjectIDZND1="2844@x" Pin0InfoVect0LinkObjId="SW-18745_0" Pin0InfoVect1LinkObjId="SW-18746_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18747_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4647,-238 4647,-250 4595,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="16609c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4595,-126 4574,-126 4574,-112 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="2844@x" ObjectIDZND0="164cb80@0" Pin0InfoVect0LinkObjId="164cb80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18746_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4595,-126 4574,-126 4574,-112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1660c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4595,-50 4595,-126 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" ObjectIDZND0="164cb80@0" ObjectIDZND1="2844@x" Pin0InfoVect0LinkObjId="164cb80_0" Pin0InfoVect1LinkObjId="SW-18746_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4595,-50 4595,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="164c920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4595,-126 4595,-141 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="164cb80@0" ObjectIDZND0="2844@0" Pin0InfoVect0LinkObjId="SW-18746_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="164cb80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4595,-126 4595,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="164d7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4595,-177 4595,-250 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="2844@1" ObjectIDZND0="2843@x" ObjectIDZND1="2845@x" Pin0InfoVect0LinkObjId="SW-18745_0" Pin0InfoVect1LinkObjId="SW-18747_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18746_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4595,-177 4595,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="164da00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4595,-428 4595,-404 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2746@0" ObjectIDZND0="2842@1" Pin0InfoVect0LinkObjId="SW-18744_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="15f9ad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4595,-428 4595,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="161d2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4490,-368 4490,-348 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2819@0" ObjectIDZND0="2818@1" Pin0InfoVect0LinkObjId="SW-18475_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18481_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4490,-368 4490,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="163ee40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4490,-300 4490,-321 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2820@1" ObjectIDZND0="2818@0" Pin0InfoVect0LinkObjId="SW-18475_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18482_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4490,-300 4490,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="163f0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4490,-428 4490,-404 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2746@0" ObjectIDZND0="2819@1" Pin0InfoVect0LinkObjId="SW-18481_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="15f9ad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4490,-428 4490,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="163b510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-368 4360,-348 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2794@0" ObjectIDZND0="2793@1" Pin0InfoVect0LinkObjId="SW-18325_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18331_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-368 4360,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="158a480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-300 4360,-321 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2795@1" ObjectIDZND0="2793@0" Pin0InfoVect0LinkObjId="SW-18325_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18332_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-300 4360,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="158a6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-250 4360,-264 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="2797@x" ObjectIDND1="2796@x" ObjectIDZND0="2795@0" Pin0InfoVect0LinkObjId="SW-18332_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18334_0" Pin1InfoVect1LinkObjId="SW-18333_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-250 4360,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="158d1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4412,-190 4412,-202 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="2797@0" Pin0InfoVect0LinkObjId="SW-18334_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4412,-190 4412,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="158d450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4412,-238 4412,-250 4360,-250 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="2797@1" ObjectIDZND0="2795@x" ObjectIDZND1="2796@x" Pin0InfoVect0LinkObjId="SW-18332_0" Pin0InfoVect1LinkObjId="SW-18333_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18334_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4412,-238 4412,-250 4360,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15aa710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-126 4339,-126 4339,-112 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="2796@x" ObjectIDZND0="15aae30@0" Pin0InfoVect0LinkObjId="15aae30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18333_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-126 4339,-126 4339,-112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15aa970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-50 4360,-126 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" ObjectIDZND0="15aae30@0" ObjectIDZND1="2796@x" Pin0InfoVect0LinkObjId="15aae30_0" Pin0InfoVect1LinkObjId="SW-18333_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-50 4360,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15aabd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-126 4360,-141 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="15aae30@0" ObjectIDZND0="2796@0" Pin0InfoVect0LinkObjId="SW-18333_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="15aae30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-126 4360,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15abbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-177 4360,-250 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="2796@1" ObjectIDZND0="2795@x" ObjectIDZND1="2797@x" Pin0InfoVect0LinkObjId="SW-18332_0" Pin0InfoVect1LinkObjId="SW-18334_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18333_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-177 4360,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15abe40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4360,-428 4360,-404 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2746@0" ObjectIDZND0="2794@1" Pin0InfoVect0LinkObjId="SW-18331_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="15f9ad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4360,-428 4360,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="16d8aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4210,-368 4210,-348 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2799@0" ObjectIDZND0="2798@1" Pin0InfoVect0LinkObjId="SW-18355_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18361_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4210,-368 4210,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15ae520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4210,-300 4210,-321 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2800@1" ObjectIDZND0="2798@0" Pin0InfoVect0LinkObjId="SW-18355_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18362_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4210,-300 4210,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15ae780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4210,-250 4210,-264 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="2802@x" ObjectIDND1="2801@x" ObjectIDZND0="2800@0" Pin0InfoVect0LinkObjId="SW-18362_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18364_0" Pin1InfoVect1LinkObjId="SW-18363_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4210,-250 4210,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15b1290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4262,-190 4262,-202 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="2802@0" Pin0InfoVect0LinkObjId="SW-18364_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4262,-190 4262,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15b14f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4262,-238 4262,-250 4210,-250 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="2802@1" ObjectIDZND0="2800@x" ObjectIDZND1="2801@x" Pin0InfoVect0LinkObjId="SW-18362_0" Pin0InfoVect1LinkObjId="SW-18363_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18364_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4262,-238 4262,-250 4210,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15cc970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4210,-126 4189,-126 4189,-112 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="2801@x" ObjectIDZND0="15cd070@0" Pin0InfoVect0LinkObjId="15cd070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18363_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4210,-126 4189,-126 4189,-112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15ccbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4210,-50 4210,-126 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" ObjectIDZND0="15cd070@0" ObjectIDZND1="2801@x" Pin0InfoVect0LinkObjId="15cd070_0" Pin0InfoVect1LinkObjId="SW-18363_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4210,-50 4210,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15cce10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4210,-126 4210,-141 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="15cd070@0" ObjectIDZND0="2801@0" Pin0InfoVect0LinkObjId="SW-18363_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="15cd070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4210,-126 4210,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15cde20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4210,-177 4210,-250 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="2801@1" ObjectIDZND0="2800@x" ObjectIDZND1="2802@x" Pin0InfoVect0LinkObjId="SW-18362_0" Pin0InfoVect1LinkObjId="SW-18364_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18363_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4210,-177 4210,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15ce080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4210,-428 4210,-404 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2746@0" ObjectIDZND0="2799@1" Pin0InfoVect0LinkObjId="SW-18361_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="15f9ad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4210,-428 4210,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15d2be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4071,-368 4071,-348 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2804@0" ObjectIDZND0="2803@1" Pin0InfoVect0LinkObjId="SW-18385_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18391_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4071,-368 4071,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15d56f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4071,-300 4071,-321 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2805@1" ObjectIDZND0="2803@0" Pin0InfoVect0LinkObjId="SW-18385_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18392_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4071,-300 4071,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15d5950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4071,-250 4071,-264 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="2807@x" ObjectIDND1="2806@x" ObjectIDZND0="2805@0" Pin0InfoVect0LinkObjId="SW-18392_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18394_0" Pin1InfoVect1LinkObjId="SW-18393_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4071,-250 4071,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15bab60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4123,-190 4123,-202 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="2807@0" Pin0InfoVect0LinkObjId="SW-18394_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4123,-190 4123,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15badc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4123,-238 4123,-250 4071,-250 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="2807@1" ObjectIDZND0="2805@x" ObjectIDZND1="2806@x" Pin0InfoVect0LinkObjId="SW-18392_0" Pin0InfoVect1LinkObjId="SW-18393_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18394_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4123,-238 4123,-250 4071,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15bd8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4071,-126 4050,-126 4050,-112 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="2806@x" ObjectIDZND0="15bdff0@0" Pin0InfoVect0LinkObjId="15bdff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18393_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4071,-126 4050,-126 4050,-112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15bdb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4071,-50 4071,-126 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" ObjectIDZND0="15bdff0@0" ObjectIDZND1="2806@x" Pin0InfoVect0LinkObjId="15bdff0_0" Pin0InfoVect1LinkObjId="SW-18393_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4071,-50 4071,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15bdd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4071,-126 4071,-141 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="15bdff0@0" ObjectIDZND0="2806@0" Pin0InfoVect0LinkObjId="SW-18393_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="15bdff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4071,-126 4071,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15beda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4071,-177 4071,-250 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="2806@1" ObjectIDZND0="2805@x" ObjectIDZND1="2807@x" Pin0InfoVect0LinkObjId="SW-18392_0" Pin0InfoVect1LinkObjId="SW-18394_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18393_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4071,-177 4071,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15bf000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4071,-428 4071,-404 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2746@0" ObjectIDZND0="2804@1" Pin0InfoVect0LinkObjId="SW-18391_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="15f9ad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4071,-428 4071,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15c3b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-368 3921,-348 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2809@0" ObjectIDZND0="2808@1" Pin0InfoVect0LinkObjId="SW-18415_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18421_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-368 3921,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15c6670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-300 3921,-321 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2810@1" ObjectIDZND0="2808@0" Pin0InfoVect0LinkObjId="SW-18415_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18422_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-300 3921,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15c68d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-250 3921,-264 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="2812@x" ObjectIDND1="2811@x" ObjectIDZND0="2810@0" Pin0InfoVect0LinkObjId="SW-18422_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18424_0" Pin1InfoVect1LinkObjId="SW-18423_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-250 3921,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15c93e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3973,-190 3973,-202 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="2812@0" Pin0InfoVect0LinkObjId="SW-18424_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3973,-190 3973,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15c9640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3973,-238 3973,-250 3921,-250 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="2812@1" ObjectIDZND0="2810@x" ObjectIDZND1="2811@x" Pin0InfoVect0LinkObjId="SW-18422_0" Pin0InfoVect1LinkObjId="SW-18423_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18424_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3973,-238 3973,-250 3921,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1576fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-126 3900,-126 3900,-112 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="2811@x" ObjectIDZND0="15776a0@0" Pin0InfoVect0LinkObjId="15776a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18423_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-126 3900,-126 3900,-112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15771e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-50 3921,-126 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" ObjectIDZND0="15776a0@0" ObjectIDZND1="2811@x" Pin0InfoVect0LinkObjId="15776a0_0" Pin0InfoVect1LinkObjId="SW-18423_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-50 3921,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1577440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-126 3921,-141 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="15776a0@0" ObjectIDZND0="2811@0" Pin0InfoVect0LinkObjId="SW-18423_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="15776a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-126 3921,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1578390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-177 3921,-250 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="2811@1" ObjectIDZND0="2810@x" ObjectIDZND1="2812@x" Pin0InfoVect0LinkObjId="SW-18422_0" Pin0InfoVect1LinkObjId="SW-18424_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18423_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-177 3921,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15785f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-428 3921,-404 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2746@0" ObjectIDZND0="2809@1" Pin0InfoVect0LinkObjId="SW-18421_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="15f9ad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-428 3921,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="157c8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3779,-368 3779,-348 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2814@0" ObjectIDZND0="2813@1" Pin0InfoVect0LinkObjId="SW-18445_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18451_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3779,-368 3779,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="157f0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3779,-300 3779,-321 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2815@1" ObjectIDZND0="2813@0" Pin0InfoVect0LinkObjId="SW-18445_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18452_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3779,-300 3779,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="157f300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3779,-250 3779,-264 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="2817@x" ObjectIDND1="2816@x" ObjectIDZND0="2815@0" Pin0InfoVect0LinkObjId="SW-18452_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18454_0" Pin1InfoVect1LinkObjId="SW-18453_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3779,-250 3779,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1581c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3831,-190 3831,-202 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="2817@0" Pin0InfoVect0LinkObjId="SW-18454_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3831,-190 3831,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1581e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3831,-238 3831,-250 3779,-250 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="2817@1" ObjectIDZND0="2815@x" ObjectIDZND1="2816@x" Pin0InfoVect0LinkObjId="SW-18452_0" Pin0InfoVect1LinkObjId="SW-18453_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18454_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3831,-238 3831,-250 3779,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1584990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3779,-126 3758,-126 3758,-112 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="2816@x" ObjectIDZND0="15850b0@0" Pin0InfoVect0LinkObjId="15850b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18453_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3779,-126 3758,-126 3758,-112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1584bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3779,-50 3779,-126 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" ObjectIDZND0="15850b0@0" ObjectIDZND1="2816@x" Pin0InfoVect0LinkObjId="15850b0_0" Pin0InfoVect1LinkObjId="SW-18453_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3779,-50 3779,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1584e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3779,-126 3779,-141 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="15850b0@0" ObjectIDZND0="2816@0" Pin0InfoVect0LinkObjId="SW-18453_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="15850b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3779,-126 3779,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1585e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3779,-177 3779,-250 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="2816@1" ObjectIDZND0="2815@x" ObjectIDZND1="2817@x" Pin0InfoVect0LinkObjId="SW-18452_0" Pin0InfoVect1LinkObjId="SW-18454_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18453_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3779,-177 3779,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="15860c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3779,-428 3779,-404 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2746@0" ObjectIDZND0="2814@1" Pin0InfoVect0LinkObjId="SW-18451_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="15f9ad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3779,-428 3779,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1586320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4490,-264 4490,-190 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2820@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18482_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4490,-264 4490,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="174b320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4222,-502 4222,-523 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="2786@x" ObjectIDND1="2785@x" ObjectIDZND0="179a370@0" Pin0InfoVect0LinkObjId="179a370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18175_0" Pin1InfoVect1LinkObjId="SW-18174_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4222,-502 4222,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="174b580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4223,-502 4235,-502 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="179a370@0" ObjectIDND1="2785@x" ObjectIDZND0="2786@0" Pin0InfoVect0LinkObjId="SW-18175_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="179a370_0" Pin1InfoVect1LinkObjId="SW-18174_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4223,-502 4235,-502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="174b7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-502 4284,-502 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2786@1" ObjectIDZND0="174a890@0" Pin0InfoVect0LinkObjId="174a890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18175_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-502 4284,-502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="174eb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4349,-542 4349,-565 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="174e2f0@1" ObjectIDZND0="174edd0@0" Pin0InfoVect0LinkObjId="174edd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="174e2f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4349,-542 4349,-565 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1751d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-428 4453,-446 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2746@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="15f9ad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-428 4453,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="1751fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4453,-482 4453,-515 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4453,-482 4453,-515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="1754ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4725,-983 4745,-983 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2744@0" ObjectIDZND0="2833@0" Pin0InfoVect0LinkObjId="SW-18590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="177de50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4725,-983 4745,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="1756f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4781,-983 4805,-983 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2833@1" ObjectIDZND0="2832@1" Pin0InfoVect0LinkObjId="SW-18584_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18590_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4781,-983 4805,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="1759c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4832,-983 4858,-983 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2832@0" ObjectIDZND0="2834@0" Pin0InfoVect0LinkObjId="SW-18591_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18584_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4832,-983 4858,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="1759eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-983 4987,-983 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2834@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18591_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-983 4987,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="175cbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4725,-729 4749,-729 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2744@0" ObjectIDZND0="2749@0" Pin0InfoVect0LinkObjId="SW-17943_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="177de50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4725,-729 4749,-729 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="175ec30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4785,-729 4808,-729 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2749@1" ObjectIDZND0="2748@1" Pin0InfoVect0LinkObjId="SW-17937_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-17943_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4785,-729 4808,-729 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="1761940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-729 4857,-729 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2748@0" ObjectIDZND0="2750@0" Pin0InfoVect0LinkObjId="SW-17944_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-17937_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-729 4857,-729 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="1761ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4893,-729 4914,-729 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="2750@1" ObjectIDZND0="178a390@0" ObjectIDZND1="2751@x" Pin0InfoVect0LinkObjId="178a390_0" Pin0InfoVect1LinkObjId="SW-17945_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-17944_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4893,-729 4914,-729 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="1767360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4725,-817 4749,-817 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2744@0" ObjectIDZND0="2753@0" Pin0InfoVect0LinkObjId="SW-17973_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="177de50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4725,-817 4749,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="17693d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4785,-817 4808,-817 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2753@1" ObjectIDZND0="2752@1" Pin0InfoVect0LinkObjId="SW-17967_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-17973_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4785,-817 4808,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="176c0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-817 4857,-817 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2752@0" ObjectIDZND0="2754@0" Pin0InfoVect0LinkObjId="SW-17974_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-17967_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-817 4857,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="17718a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4725,-903 4749,-903 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2744@0" ObjectIDZND0="2757@0" Pin0InfoVect0LinkObjId="SW-18003_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="177de50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4725,-903 4749,-903 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="1773910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4785,-903 4808,-903 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2757@1" ObjectIDZND0="2756@1" Pin0InfoVect0LinkObjId="SW-17997_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18003_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4785,-903 4808,-903 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="1776620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-903 4857,-903 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2756@0" ObjectIDZND0="2758@0" Pin0InfoVect0LinkObjId="SW-18004_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-17997_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-903 4857,-903 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="177dbf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4646,-902 4665,-902 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2860@0" ObjectIDZND0="2788@0" Pin0InfoVect0LinkObjId="SW-18248_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18878_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4646,-902 4665,-902 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="177de50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4702,-902 4725,-902 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2788@1" ObjectIDZND0="2744@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18248_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4702,-902 4725,-902 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="1780b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4597,-902 4619,-902 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2789@1" ObjectIDZND0="2860@1" Pin0InfoVect0LinkObjId="SW-18878_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18249_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4597,-902 4619,-902 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="1783870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4725,-975 4701,-975 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2744@0" ObjectIDZND0="2783@1" Pin0InfoVect0LinkObjId="SW-18145_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="177de50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4725,-975 4701,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="1783ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4664,-975 4612,-975 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="2783@0" ObjectIDZND0="1783d30@0" Pin0InfoVect0LinkObjId="1783d30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18145_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4664,-975 4612,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="1788590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4725,-1057 4701,-1057 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2744@0" ObjectIDZND0="2784@1" Pin0InfoVect0LinkObjId="SW-18154_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="177de50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4725,-1057 4701,-1057 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="17897b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4664,-1057 4633,-1057 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="2784@0" ObjectIDZND0="17887f0@0" Pin0InfoVect0LinkObjId="17887f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18154_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4664,-1057 4633,-1057 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="1789a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4602,-1057 4568,-1057 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="17887f0@1" ObjectIDZND0="1789070@0" Pin0InfoVect0LinkObjId="1789070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="17887f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4602,-1057 4568,-1057 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="1789c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5030,-729 5030,-703 5046,-703 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="2750@x" ObjectIDND1="2751@x" ObjectIDZND0="178a390@0" Pin0InfoVect0LinkObjId="178a390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-17944_0" Pin1InfoVect1LinkObjId="SW-17945_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5030,-729 5030,-703 5046,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="1789ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4914,-729 5030,-729 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="2750@x" ObjectIDND1="2751@x" ObjectIDZND0="178a390@0" Pin0InfoVect0LinkObjId="178a390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-17944_0" Pin1InfoVect1LinkObjId="SW-17945_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4914,-729 5030,-729 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="178a130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5030,-729 5110,-729 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="2750@x" ObjectIDND1="2751@x" ObjectIDND2="178a390@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-17944_0" Pin1InfoVect1LinkObjId="SW-17945_0" Pin1InfoVect2LinkObjId="178a390_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5030,-729 5110,-729 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="178b140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5029,-817 5029,-791 5045,-791 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="2754@x" ObjectIDND1="2755@x" ObjectIDZND0="178b3a0@0" Pin0InfoVect0LinkObjId="178b3a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-17974_0" Pin1InfoVect1LinkObjId="SW-17975_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5029,-817 5029,-791 5045,-791 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="178c150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5029,-817 5110,-817 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="178b3a0@0" ObjectIDND1="2754@x" ObjectIDND2="2755@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="178b3a0_0" Pin1InfoVect1LinkObjId="SW-17974_0" Pin1InfoVect2LinkObjId="SW-17975_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5029,-817 5110,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="178c3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5029,-903 5029,-877 5045,-877 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="178dae0@0" ObjectIDND1="2758@x" ObjectIDND2="2759@x" ObjectIDZND0="178c610@0" Pin0InfoVect0LinkObjId="178c610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="178dae0_0" Pin1InfoVect1LinkObjId="SW-18004_0" Pin1InfoVect2LinkObjId="SW-18005_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5029,-903 5029,-877 5045,-877 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="178d3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5029,-903 5110,-903 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="178c610@0" ObjectIDND1="178dae0@0" ObjectIDND2="2758@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="178c610_0" Pin1InfoVect1LinkObjId="178dae0_0" Pin1InfoVect2LinkObjId="SW-18004_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5029,-903 5110,-903 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="178d620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5010,-903 5010,-941 5058,-941 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="178c610@0" ObjectIDND1="2758@x" ObjectIDND2="2759@x" ObjectIDZND0="178dae0@0" Pin0InfoVect0LinkObjId="178dae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="178c610_0" Pin1InfoVect1LinkObjId="SW-18004_0" Pin1InfoVect2LinkObjId="SW-18005_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5010,-903 5010,-941 5058,-941 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="178d880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5010,-903 5029,-903 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="178dae0@0" ObjectIDND1="2758@x" ObjectIDND2="2759@x" ObjectIDZND0="178c610@0" Pin0InfoVect0LinkObjId="178c610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="178dae0_0" Pin1InfoVect1LinkObjId="SW-18004_0" Pin1InfoVect2LinkObjId="SW-18005_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5010,-903 5029,-903 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="179c2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4914,-729 4914,-765 4931,-765 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="2750@x" ObjectIDND1="178a390@0" ObjectIDZND0="2751@0" Pin0InfoVect0LinkObjId="SW-17945_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-17944_0" Pin1InfoVect1LinkObjId="178a390_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4914,-729 4914,-765 4931,-765 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="179c4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4967,-765 4987,-765 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2751@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-17945_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4967,-765 4987,-765 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="179ce50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4893,-817 4911,-817 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="2754@1" ObjectIDZND0="178b3a0@0" ObjectIDZND1="2755@x" Pin0InfoVect0LinkObjId="178b3a0_0" Pin0InfoVect1LinkObjId="SW-17975_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-17974_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4893,-817 4911,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="179d060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4911,-817 5029,-817 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="2754@x" ObjectIDND1="2755@x" ObjectIDZND0="178b3a0@0" Pin0InfoVect0LinkObjId="178b3a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-17974_0" Pin1InfoVect1LinkObjId="SW-17975_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4911,-817 5029,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="179d290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4911,-817 4911,-848 4931,-848 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="2754@x" ObjectIDND1="178b3a0@0" ObjectIDZND0="2755@0" Pin0InfoVect0LinkObjId="SW-17975_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-17974_0" Pin1InfoVect1LinkObjId="178b3a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4911,-817 4911,-848 4931,-848 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="179d4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4967,-848 4987,-848 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2755@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-17975_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4967,-848 4987,-848 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="179dfc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4893,-903 4911,-903 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="2758@1" ObjectIDZND0="178dae0@0" ObjectIDZND1="178c610@0" ObjectIDZND2="2759@x" Pin0InfoVect0LinkObjId="178dae0_0" Pin0InfoVect1LinkObjId="178c610_0" Pin0InfoVect2LinkObjId="SW-18005_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18004_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4893,-903 4911,-903 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="179e200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4911,-903 5010,-903 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="2758@x" ObjectIDND1="2759@x" ObjectIDZND0="178dae0@0" ObjectIDZND1="178c610@0" Pin0InfoVect0LinkObjId="178dae0_0" Pin0InfoVect1LinkObjId="178c610_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18004_0" Pin1InfoVect1LinkObjId="SW-18005_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4911,-903 5010,-903 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="179e460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4911,-905 4911,-935 4931,-935 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="2758@x" ObjectIDND1="178dae0@0" ObjectIDND2="178c610@0" ObjectIDZND0="2759@0" Pin0InfoVect0LinkObjId="SW-18005_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-18004_0" Pin1InfoVect1LinkObjId="178dae0_0" Pin1InfoVect2LinkObjId="178c610_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4911,-905 4911,-935 4931,-935 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="179e6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4967,-935 4987,-935 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2759@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18005_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4967,-935 4987,-935 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="17a4db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4378,-911 4378,-926 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="2781@0" Pin0InfoVect0LinkObjId="SW-18139_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4378,-911 4378,-926 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="17a7ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4378,-977 4397,-977 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="2781@x" ObjectIDND1="2777@x" ObjectIDZND0="2782@0" Pin0InfoVect0LinkObjId="SW-18140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18139_0" Pin1InfoVect1LinkObjId="SW-18127_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4378,-977 4397,-977 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="17a7d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-977 4455,-977 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2782@1" ObjectIDZND0="17a7f80@0" Pin0InfoVect0LinkObjId="17a7f80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18140_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-977 4455,-977 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="17a8a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4378,-962 4378,-977 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="2781@1" ObjectIDZND0="2782@x" ObjectIDZND1="2777@x" Pin0InfoVect0LinkObjId="SW-18140_0" Pin0InfoVect1LinkObjId="SW-18127_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18139_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4378,-962 4378,-977 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="17aacc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4378,-977 4378,-996 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="2782@x" ObjectIDND1="2781@x" ObjectIDZND0="2777@0" Pin0InfoVect0LinkObjId="SW-18127_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18140_0" Pin1InfoVect1LinkObjId="SW-18139_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4378,-977 4378,-996 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="17ad9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4377,-1037 4396,-1037 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="2777@x" ObjectIDND1="2778@x" ObjectIDZND0="2780@0" Pin0InfoVect0LinkObjId="SW-18138_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18127_0" Pin1InfoVect1LinkObjId="SW-18136_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4377,-1037 4396,-1037 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="17adc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-1037 4454,-1037 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2780@1" ObjectIDZND0="17ade90@0" Pin0InfoVect0LinkObjId="17ade90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18138_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-1037 4454,-1037 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="17ae920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4378,-1023 4378,-1037 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="2777@1" ObjectIDZND0="2780@x" ObjectIDZND1="2778@x" Pin0InfoVect0LinkObjId="SW-18138_0" Pin0InfoVect1LinkObjId="SW-18136_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18127_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4378,-1023 4378,-1037 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="17b1430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4378,-1037 4378,-1050 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="2780@x" ObjectIDND1="2777@x" ObjectIDZND0="2778@0" Pin0InfoVect0LinkObjId="SW-18136_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18138_0" Pin1InfoVect1LinkObjId="SW-18127_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4378,-1037 4378,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="196da00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4378,-1115 4397,-1115 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="2778@x" ObjectIDND1="196f790@0" ObjectIDND2="1a0dea0@0" ObjectIDZND0="2779@0" Pin0InfoVect0LinkObjId="SW-18137_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-18136_0" Pin1InfoVect1LinkObjId="196f790_0" Pin1InfoVect2LinkObjId="1a0dea0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4378,-1115 4397,-1115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="196dc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-1115 4455,-1115 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2779@1" ObjectIDZND0="196dec0@0" Pin0InfoVect0LinkObjId="196dec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18137_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-1115 4455,-1115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="196e950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4378,-1086 4378,-1112 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="2778@1" ObjectIDZND0="2779@x" ObjectIDZND1="196f790@0" ObjectIDZND2="1a0dea0@0" Pin0InfoVect0LinkObjId="SW-18137_0" Pin0InfoVect1LinkObjId="196f790_0" Pin0InfoVect2LinkObjId="1a0dea0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18136_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4378,-1086 4378,-1112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="196ebb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4378,-1112 4378,-1158 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="2779@x" ObjectIDND1="2778@x" ObjectIDZND0="196f790@0" ObjectIDZND1="1a0dea0@0" Pin0InfoVect0LinkObjId="196f790_0" Pin0InfoVect1LinkObjId="1a0dea0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18137_0" Pin1InfoVect1LinkObjId="SW-18136_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4378,-1112 4378,-1158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="196ee10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4378,-1158 4378,-1181 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="2779@x" ObjectIDND1="2778@x" ObjectIDND2="196f790@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-18137_0" Pin1InfoVect1LinkObjId="SW-18136_0" Pin1InfoVect2LinkObjId="196f790_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4378,-1158 4378,-1181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="196f070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4358,-1158 4358,-1145 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="2779@x" ObjectIDND1="2778@x" ObjectIDND2="1a0dea0@0" ObjectIDZND0="196f790@0" Pin0InfoVect0LinkObjId="196f790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-18137_0" Pin1InfoVect1LinkObjId="SW-18136_0" Pin1InfoVect2LinkObjId="1a0dea0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4358,-1158 4358,-1145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="196f2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4378,-1158 4358,-1158 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="2779@x" ObjectIDND1="2778@x" ObjectIDZND0="196f790@0" ObjectIDZND1="1a0dea0@0" Pin0InfoVect0LinkObjId="196f790_0" Pin0InfoVect1LinkObjId="1a0dea0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18137_0" Pin1InfoVect1LinkObjId="SW-18136_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4378,-1158 4358,-1158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="196f530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4358,-1158 4316,-1158 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="196f790@0" ObjectIDND1="2779@x" ObjectIDND2="2778@x" ObjectIDZND0="1a0dea0@0" Pin0InfoVect0LinkObjId="1a0dea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="196f790_0" Pin1InfoVect1LinkObjId="SW-18137_0" Pin1InfoVect2LinkObjId="SW-18136_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4358,-1158 4316,-1158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="1979b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4004,-978 4023,-978 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="2769@x" ObjectIDND1="2765@x" ObjectIDZND0="2770@0" Pin0InfoVect0LinkObjId="SW-18099_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18098_0" Pin1InfoVect1LinkObjId="SW-18086_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4004,-978 4023,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="1979de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4059,-978 4081,-978 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2770@1" ObjectIDZND0="197a040@0" Pin0InfoVect0LinkObjId="197a040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18099_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4059,-978 4081,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="197aad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4004,-966 4004,-981 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="2769@1" ObjectIDZND0="2770@x" ObjectIDZND1="2765@x" Pin0InfoVect0LinkObjId="SW-18099_0" Pin0InfoVect1LinkObjId="SW-18086_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18098_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4004,-966 4004,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="197cd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4004,-981 4004,-1000 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="2770@x" ObjectIDND1="2769@x" ObjectIDZND0="2765@0" Pin0InfoVect0LinkObjId="SW-18086_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18099_0" Pin1InfoVect1LinkObjId="SW-18098_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4004,-981 4004,-1000 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="197fa90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4003,-1037 4022,-1037 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="2765@x" ObjectIDND1="2766@x" ObjectIDZND0="2768@0" Pin0InfoVect0LinkObjId="SW-18097_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18086_0" Pin1InfoVect1LinkObjId="SW-18095_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4003,-1037 4022,-1037 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="197fcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4058,-1037 4080,-1037 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2768@1" ObjectIDZND0="197ff50@0" Pin0InfoVect0LinkObjId="197ff50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18097_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4058,-1037 4080,-1037 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="19809e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4004,-1027 4004,-1041 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="2765@1" ObjectIDZND0="2768@x" ObjectIDZND1="2766@x" Pin0InfoVect0LinkObjId="SW-18097_0" Pin0InfoVect1LinkObjId="SW-18095_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18086_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4004,-1027 4004,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="19834f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4004,-1041 4004,-1054 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="2768@x" ObjectIDND1="2765@x" ObjectIDZND0="2766@0" Pin0InfoVect0LinkObjId="SW-18095_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18097_0" Pin1InfoVect1LinkObjId="SW-18086_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4004,-1041 4004,-1054 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="1986200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4004,-1116 4023,-1116 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="2766@x" ObjectIDND1="1987f90@0" ObjectIDND2="1a109c0@0" ObjectIDZND0="2767@0" Pin0InfoVect0LinkObjId="SW-18096_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-18095_0" Pin1InfoVect1LinkObjId="1987f90_0" Pin1InfoVect2LinkObjId="1a109c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4004,-1116 4023,-1116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="1986460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4059,-1116 4081,-1116 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2767@1" ObjectIDZND0="19866c0@0" Pin0InfoVect0LinkObjId="19866c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18096_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4059,-1116 4081,-1116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="1987150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4004,-1090 4004,-1116 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="2766@1" ObjectIDZND0="2767@x" ObjectIDZND1="1987f90@0" ObjectIDZND2="1a109c0@0" Pin0InfoVect0LinkObjId="SW-18096_0" Pin0InfoVect1LinkObjId="1987f90_0" Pin0InfoVect2LinkObjId="1a109c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18095_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4004,-1090 4004,-1116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="19873b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4004,-1116 4004,-1162 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="2767@x" ObjectIDND1="2766@x" ObjectIDZND0="1987f90@0" ObjectIDZND1="1a109c0@0" Pin0InfoVect0LinkObjId="1987f90_0" Pin0InfoVect1LinkObjId="1a109c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18096_0" Pin1InfoVect1LinkObjId="SW-18095_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4004,-1116 4004,-1162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="1987610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4004,-1162 4004,-1185 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="2767@x" ObjectIDND1="2766@x" ObjectIDND2="1987f90@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-18096_0" Pin1InfoVect1LinkObjId="SW-18095_0" Pin1InfoVect2LinkObjId="1987f90_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4004,-1162 4004,-1185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="1987870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3984,-1162 3984,-1149 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="2767@x" ObjectIDND1="2766@x" ObjectIDND2="1a109c0@0" ObjectIDZND0="1987f90@0" Pin0InfoVect0LinkObjId="1987f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-18096_0" Pin1InfoVect1LinkObjId="SW-18095_0" Pin1InfoVect2LinkObjId="1a109c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3984,-1162 3984,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="1987ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4004,-1162 3984,-1162 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="2767@x" ObjectIDND1="2766@x" ObjectIDZND0="1987f90@0" ObjectIDZND1="1a109c0@0" Pin0InfoVect0LinkObjId="1987f90_0" Pin0InfoVect1LinkObjId="1a109c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18096_0" Pin1InfoVect1LinkObjId="SW-18095_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4004,-1162 3984,-1162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="1987d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3984,-1162 3943,-1162 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="1987f90@0" ObjectIDND1="2767@x" ObjectIDND2="2766@x" ObjectIDZND0="1a109c0@0" Pin0InfoVect0LinkObjId="1a109c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="1987f90_0" Pin1InfoVect1LinkObjId="SW-18096_0" Pin1InfoVect2LinkObjId="SW-18095_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3984,-1162 3943,-1162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="198cdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4004,-930 4004,-911 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2769@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18098_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4004,-930 4004,-911 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="198d010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4004,-911 4004,-847 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="2829@1" Pin0InfoVect0LinkObjId="SW-18577_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4004,-911 4004,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="198fcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4725,-700 4749,-700 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2744@0" ObjectIDZND0="3022@0" Pin0InfoVect0LinkObjId="SW-18042_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="177de50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4725,-700 4749,-700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="1994a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4725,-530 4748,-530 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2745@0" ObjectIDZND0="2850@0" Pin0InfoVect0LinkObjId="SW-18875_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="19b5a30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4725,-530 4748,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="1994cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4784,-530 4817,-530 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="2850@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18875_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4784,-530 4817,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="19979c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4725,-474 4747,-474 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2745@0" ObjectIDZND0="2762@0" Pin0InfoVect0LinkObjId="SW-18062_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="19b5a30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4725,-474 4747,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="1999a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4783,-474 4806,-474 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2762@1" ObjectIDZND0="2761@1" Pin0InfoVect0LinkObjId="SW-18056_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18062_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4783,-474 4806,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="199c740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4833,-474 4855,-474 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2761@0" ObjectIDZND0="2763@0" Pin0InfoVect0LinkObjId="SW-18063_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18056_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4833,-474 4855,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="199c9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4891,-474 4912,-474 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="2763@1" ObjectIDZND0="2764@x" ObjectIDZND1="19aba40@0" ObjectIDZND2="19aec10@0" Pin0InfoVect0LinkObjId="SW-18064_0" Pin0InfoVect1LinkObjId="19aba40_0" Pin0InfoVect2LinkObjId="19aec10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18063_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4891,-474 4912,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="199f6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4912,-474 4912,-522 4931,-522 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="2763@x" ObjectIDND1="19aba40@0" ObjectIDND2="19aec10@0" ObjectIDZND0="2764@0" Pin0InfoVect0LinkObjId="SW-18064_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-18063_0" Pin1InfoVect1LinkObjId="19aba40_0" Pin1InfoVect2LinkObjId="19aec10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4912,-474 4912,-522 4931,-522 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="199f910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4967,-522 4987,-522 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2764@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18064_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4967,-522 4987,-522 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="19a2620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4725,-572 4747,-572 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2745@0" ObjectIDZND0="2846@0" Pin0InfoVect0LinkObjId="SW-18857_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="19b5a30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4725,-572 4747,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="19a4690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4783,-572 4806,-572 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2846@1" ObjectIDZND0="2849@1" Pin0InfoVect0LinkObjId="SW-18870_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18857_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4783,-572 4806,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="19a73a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4833,-572 4855,-572 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2849@0" ObjectIDZND0="2847@0" Pin0InfoVect0LinkObjId="SW-18858_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4833,-572 4855,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="19a7600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4891,-572 4912,-572 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="2847@1" ObjectIDZND0="2848@x" ObjectIDZND1="19aaa30@0" ObjectIDZND2="19afc20@0" Pin0InfoVect0LinkObjId="SW-18859_0" Pin0InfoVect1LinkObjId="19aaa30_0" Pin0InfoVect2LinkObjId="19afc20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18858_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4891,-572 4912,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="19aa310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4912,-572 4912,-620 4931,-620 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="2847@x" ObjectIDND1="19aaa30@0" ObjectIDND2="19afc20@0" ObjectIDZND0="2848@0" Pin0InfoVect0LinkObjId="SW-18859_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-18858_0" Pin1InfoVect1LinkObjId="19aaa30_0" Pin1InfoVect2LinkObjId="19afc20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4912,-572 4912,-620 4931,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="19aa570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4967,-620 4987,-620 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="2848@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18859_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4967,-620 4987,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="19aa7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5002,-572 5002,-546 5018,-546 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="2848@x" ObjectIDND1="2847@x" ObjectIDND2="19afc20@0" ObjectIDZND0="19aaa30@0" Pin0InfoVect0LinkObjId="19aaa30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-18859_0" Pin1InfoVect1LinkObjId="SW-18858_0" Pin1InfoVect2LinkObjId="19afc20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5002,-572 5002,-546 5018,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="19ab7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5008,-474 5008,-503 5056,-503 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="2764@x" ObjectIDND1="2763@x" ObjectIDND2="19aec10@0" ObjectIDZND0="19aba40@0" Pin0InfoVect0LinkObjId="19aba40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-18064_0" Pin1InfoVect1LinkObjId="SW-18063_0" Pin1InfoVect2LinkObjId="19aec10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5008,-474 5008,-503 5056,-503 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="19acb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4912,-474 5008,-474 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="2764@x" ObjectIDND1="2763@x" ObjectIDZND0="19aba40@0" ObjectIDZND1="19aec10@0" ObjectIDZND2="19acfe0@0" Pin0InfoVect0LinkObjId="19aba40_0" Pin0InfoVect1LinkObjId="19aec10_0" Pin0InfoVect2LinkObjId="19acfe0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18064_0" Pin1InfoVect1LinkObjId="SW-18063_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4912,-474 5008,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="19acd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5008,-474 5108,-474 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="2764@x" ObjectIDND1="2763@x" ObjectIDND2="19aba40@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-18064_0" Pin1InfoVect1LinkObjId="SW-18063_0" Pin1InfoVect2LinkObjId="19aba40_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5008,-474 5108,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="19adb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5067,-431 5091,-431 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="19acfe0@1" ObjectIDZND0="19addf0@0" Pin0InfoVect0LinkObjId="19addf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="19acfe0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5067,-431 5091,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="19ae4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5008,-431 5008,-415 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="2764@x" ObjectIDND1="2763@x" ObjectIDND2="19aba40@0" ObjectIDZND0="19aec10@0" Pin0InfoVect0LinkObjId="19aec10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-18064_0" Pin1InfoVect1LinkObjId="SW-18063_0" Pin1InfoVect2LinkObjId="19aba40_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5008,-431 5008,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="19ae750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5008,-474 5008,-431 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="2764@x" ObjectIDND1="2763@x" ObjectIDND2="19aba40@0" ObjectIDZND0="19aec10@0" ObjectIDZND1="19acfe0@0" Pin0InfoVect0LinkObjId="19aec10_0" Pin0InfoVect1LinkObjId="19acfe0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-18064_0" Pin1InfoVect1LinkObjId="SW-18063_0" Pin1InfoVect2LinkObjId="19aba40_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5008,-474 5008,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="19ae9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5008,-431 5022,-431 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="2764@x" ObjectIDND1="2763@x" ObjectIDND2="19aba40@0" ObjectIDZND0="19acfe0@0" Pin0InfoVect0LinkObjId="19acfe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-18064_0" Pin1InfoVect1LinkObjId="SW-18063_0" Pin1InfoVect2LinkObjId="19aba40_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5008,-431 5022,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="19af9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5027,-570 5027,-597 5075,-597 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="19b0d00@0" ObjectIDND1="19aaa30@0" ObjectIDND2="2848@x" ObjectIDZND0="19afc20@0" Pin0InfoVect0LinkObjId="19afc20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="19b0d00_0" Pin1InfoVect1LinkObjId="19aaa30_0" Pin1InfoVect2LinkObjId="SW-18859_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5027,-570 5027,-597 5075,-597 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="19b1920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5027,-572 5048,-572 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="19afc20@0" ObjectIDND1="19aaa30@0" ObjectIDND2="2848@x" ObjectIDZND0="19b0d00@1" Pin0InfoVect0LinkObjId="19b0d00_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="19afc20_0" Pin1InfoVect1LinkObjId="19aaa30_0" Pin1InfoVect2LinkObjId="SW-18859_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5027,-572 5048,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="19b1b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5087,-572 5108,-572 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="19b0d00@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="19b0d00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5087,-572 5108,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="19b5a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4747,-604 4725,-604 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3023@0" ObjectIDZND0="2745@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18043_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4747,-604 4725,-604 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="19bc970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-902 4500,-902 4500,-693 4057,-693 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="2789@0" ObjectIDZND0="2859@0" Pin0InfoVect0LinkObjId="1a06b80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18249_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-902 4500,-902 4500,-693 4057,-693 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="19beec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4284,-911 4284,-897 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="2774@1" Pin0InfoVect0LinkObjId="SW-18118_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4284,-911 4284,-897 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="19c19d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4164,-911 4164,-896 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="2772@1" Pin0InfoVect0LinkObjId="SW-18116_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4164,-911 4164,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="19c8ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4284,-797 4284,-778 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2775@0" ObjectIDZND0="19c9ab0@0" Pin0InfoVect0LinkObjId="19c9ab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18119_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4284,-797 4284,-778 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="19c8e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4164,-796 4164,-778 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="2773@0" ObjectIDZND0="19c9060@0" Pin0InfoVect0LinkObjId="19c9060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18117_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4164,-796 4164,-778 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="19f5dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4284,-861 4284,-849 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="2774@0" ObjectIDZND0="2775@x" ObjectIDZND1="2771@x" Pin0InfoVect0LinkObjId="SW-18119_0" Pin0InfoVect1LinkObjId="SW-18105_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18118_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4284,-861 4284,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="19f5fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4284,-849 4284,-833 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="2774@x" ObjectIDND1="2771@x" ObjectIDZND0="2775@1" Pin0InfoVect0LinkObjId="SW-18119_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18118_0" Pin1InfoVect1LinkObjId="SW-18105_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4284,-849 4284,-833 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="19f69c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4164,-860 4164,-849 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="2772@0" ObjectIDZND0="2773@x" ObjectIDZND1="2771@x" Pin0InfoVect0LinkObjId="SW-18117_0" Pin0InfoVect1LinkObjId="SW-18105_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18116_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4164,-860 4164,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="19f6c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4164,-849 4164,-832 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="2772@x" ObjectIDND1="2771@x" ObjectIDZND0="2773@1" Pin0InfoVect0LinkObjId="SW-18117_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18116_0" Pin1InfoVect1LinkObjId="SW-18105_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4164,-849 4164,-832 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="1a04f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4282,-849 4244,-849 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="2774@x" ObjectIDND1="2775@x" ObjectIDZND0="2771@0" Pin0InfoVect0LinkObjId="SW-18105_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18118_0" Pin1InfoVect1LinkObjId="SW-18119_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4282,-849 4244,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="1a05170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4217,-849 4164,-849 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="2771@1" ObjectIDZND0="2772@x" ObjectIDZND1="2773@x" Pin0InfoVect0LinkObjId="SW-18116_0" Pin0InfoVect1LinkObjId="SW-18117_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18105_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4217,-849 4164,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="1a053d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3928,-714 3900,-714 3900,-703 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="159f580@0" ObjectIDND1="2859@x" ObjectIDND2="159e870@0" ObjectIDZND0="2831@1" Pin0InfoVect0LinkObjId="SW-18579_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="159f580_0" Pin1InfoVect1LinkObjId="19bc970_0" Pin1InfoVect2LinkObjId="159e870_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3928,-714 3900,-714 3900,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="1a05ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3928,-714 3928,-697 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="2831@x" ObjectIDND1="2859@x" ObjectIDND2="159e870@0" ObjectIDZND0="159f580@0" Pin0InfoVect0LinkObjId="159f580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-18579_0" Pin1InfoVect1LinkObjId="19bc970_0" Pin1InfoVect2LinkObjId="159e870_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3928,-714 3928,-697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="1a06920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3942,-714 3928,-714 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="2859@x" ObjectIDND1="159e870@0" ObjectIDZND0="2831@x" ObjectIDZND1="159f580@0" Pin0InfoVect0LinkObjId="SW-18579_0" Pin0InfoVect1LinkObjId="159f580_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="19bc970_0" Pin1InfoVect1LinkObjId="159e870_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3942,-714 3928,-714 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="1a06b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3950,-696 3950,-714 3942,-714 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="159e870@0" ObjectIDZND0="2859@x" ObjectIDZND1="2831@x" ObjectIDZND2="159f580@0" Pin0InfoVect0LinkObjId="19bc970_0" Pin0InfoVect1LinkObjId="SW-18579_0" Pin0InfoVect2LinkObjId="159f580_0" Pin0Num="1" Pin1InfoVect0LinkObjId="159e870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3950,-696 3950,-714 3942,-714 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1a06de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3620,-428 3620,-441 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2746@0" ObjectIDZND0="2826@0" Pin0InfoVect0LinkObjId="SW-18537_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="15f9ad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3620,-428 3620,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1a07040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3620,-477 3620,-513 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2826@1" ObjectIDZND0="2825@1" Pin0InfoVect0LinkObjId="SW-18531_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18537_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3620,-477 3620,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1a072a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4349,-428 4349,-443 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2746@0" ObjectIDZND0="2787@0" Pin0InfoVect0LinkObjId="SW-18185_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="15f9ad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4349,-428 4349,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1a07500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4349,-479 4349,-511 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="2787@1" ObjectIDZND0="174e2f0@0" Pin0InfoVect0LinkObjId="174e2f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18185_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4349,-479 4349,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1a07760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4222,-428 4222,-442 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="2746@0" ObjectIDZND0="2785@0" Pin0InfoVect0LinkObjId="SW-18174_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="15f9ad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4222,-428 4222,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1a079c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4222,-478 4222,-502 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="2785@1" ObjectIDZND0="2786@x" ObjectIDZND1="179a370@0" Pin0InfoVect0LinkObjId="SW-18175_0" Pin0InfoVect1LinkObjId="179a370_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18174_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4222,-478 4222,-502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="1a0cc90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4783,-604 4803,-604 4803,-643 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3023@1" ObjectIDZND0="2760@0" Pin0InfoVect0LinkObjId="SW-18027_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18043_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4783,-604 4803,-604 4803,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="1a0cef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4803,-670 4803,-700 4784,-700 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2760@1" ObjectIDZND0="3022@1" Pin0InfoVect0LinkObjId="SW-18042_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18027_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4803,-670 4803,-700 4784,-700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="1a0d9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4912,-572 5002,-572 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="2848@x" ObjectIDND1="2847@x" ObjectIDZND0="19aaa30@0" ObjectIDZND1="19afc20@0" ObjectIDZND2="19b0d00@0" Pin0InfoVect0LinkObjId="19aaa30_0" Pin0InfoVect1LinkObjId="19afc20_0" Pin0InfoVect2LinkObjId="19b0d00_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-18859_0" Pin1InfoVect1LinkObjId="SW-18858_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4912,-572 5002,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="1a0dc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5002,-572 5027,-572 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="19aaa30@0" ObjectIDND1="2848@x" ObjectIDND2="2847@x" ObjectIDZND0="19afc20@0" ObjectIDZND1="19b0d00@0" Pin0InfoVect0LinkObjId="19afc20_0" Pin0InfoVect1LinkObjId="19b0d00_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="19aaa30_0" Pin1InfoVect1LinkObjId="SW-18859_0" Pin1InfoVect2LinkObjId="SW-18858_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5002,-572 5027,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="1a14890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3744,-570 3744,-580 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="15b8a90@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="RB-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="15b8a90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3744,-570 3744,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="1a14ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3744,-622 3744,-631 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="2835@1" Pin0InfoVect0LinkObjId="SW-18613_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="RB-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3744,-622 3744,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="1a14d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3808,-658 3808,-675 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="2858@0" ObjectIDZND0="2840@0" Pin0InfoVect0LinkObjId="SW-18629_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18626_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3808,-658 3808,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="1a14f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3808,-711 3808,-719 3789,-719 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="2840@1" ObjectIDZND0="2836@0" Pin0InfoVect0LinkObjId="SW-18615_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-18629_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3808,-711 3808,-719 3789,-719 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer"/><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3119.000000 -1155.000000) translate(0,17)">加南网标志（288＊90）：禄丰变电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3030.000000 -1139.000000) translate(0,12)">0.1h</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3025.000000 -925.000000) translate(0,12)">0.4h</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3032.000000 -500.000000) translate(0,12)">0.5h</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3167.000000 -1120.000000) translate(0,12)">系统时间（180＊36）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3117.000000 -977.000000) translate(0,17)">频率</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3117.000000 -977.000000) translate(0,38)">全站有功</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3117.000000 -977.000000) translate(0,59)">全站无功</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3117.000000 -977.000000) translate(0,80)">并网联络点的电压和交换功率</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3118.000000 -589.000000) translate(0,17)">危险点说明</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3118.000000 -589.000000) translate(0,38)">联系方式</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3289.000000 -1231.000000) translate(0,12)">0.3h</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4521.000000 -1023.000000) translate(0,15)">I段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4521.000000 -1092.000000) translate(0,15)">I段计量TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3660.000000 -833.000000) translate(0,15)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3660.000000 -833.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3660.000000 -833.000000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3660.000000 -833.000000) translate(0,69)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3660.000000 -833.000000) translate(0,87)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3660.000000 -833.000000) translate(0,105)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3764.000000 -606.000000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3764.000000 -606.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3764.000000 -606.000000) translate(0,51)">所</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3764.000000 -606.000000) translate(0,69)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3764.000000 -606.000000) translate(0,87)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3817.000000 -606.000000) translate(0,15)">至</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3817.000000 -606.000000) translate(0,33)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3817.000000 -606.000000) translate(0,51)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3817.000000 -606.000000) translate(0,69)">所</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3817.000000 -606.000000) translate(0,87)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3817.000000 -606.000000) translate(0,105)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4322.000000 -636.000000) translate(0,15)">I段计量TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3653.000000 -134.000000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3653.000000 -134.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3653.000000 -134.000000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3653.000000 -134.000000) translate(0,69)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3653.000000 -134.000000) translate(0,87)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3653.000000 -134.000000) translate(0,105)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3786.000000 -134.000000) translate(0,15)">散</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3786.000000 -134.000000) translate(0,33)">花</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3786.000000 -134.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3928.000000 -134.000000) translate(0,15)">牟</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3928.000000 -134.000000) translate(0,33)">城</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3928.000000 -134.000000) translate(0,51)">II</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3928.000000 -134.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3928.000000 -134.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4078.000000 -134.000000) translate(0,15)">牟</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4078.000000 -134.000000) translate(0,33)">城</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4078.000000 -134.000000) translate(0,51)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4078.000000 -134.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4078.000000 -134.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4218.000000 -134.000000) translate(0,15)">新</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4218.000000 -134.000000) translate(0,33)">甸</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4218.000000 -134.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4368.000000 -134.000000) translate(0,15)">高</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4368.000000 -134.000000) translate(0,33)">平</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4368.000000 -134.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4604.000000 -134.000000) translate(0,15)">牟</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4604.000000 -134.000000) translate(0,33)">城</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4604.000000 -134.000000) translate(0,51)">III</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4604.000000 -134.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4604.000000 -134.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4188.000000 -636.000000) translate(0,15)">I段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5001.000000 -980.000000) translate(0,15)">旁路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5133.000000 -950.000000) translate(0,15)">AB相</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5116.000000 -913.000000) translate(0,15)">红坡线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5098.000000 -837.000000) translate(0,15)">飒马场线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5089.000000 -750.000000) translate(0,15)">龙排I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4008.000000 -838.000000) translate(0,12)">1843</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4026.000000 -797.000000) translate(0,12)">1745</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4385.000000 -1201.000000) translate(0,15)">元</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4385.000000 -1201.000000) translate(0,33)">牟</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4385.000000 -1201.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4386.000000 -1026.000000) translate(0,12)">186</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4386.000000 -1086.000000) translate(0,12)">1861</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4386.000000 -956.000000) translate(0,12)">1862</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4401.000000 -1000.000000) translate(0,12)">1764</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4401.000000 -1060.000000) translate(0,12)">1763</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4401.000000 -1139.000000) translate(0,12)">1762</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4010.000000 -1201.000000) translate(0,15)">楚</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4010.000000 -1201.000000) translate(0,33)">牟</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4010.000000 -1201.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4012.000000 -1026.000000) translate(0,12)">184</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4012.000000 -1086.000000) translate(0,12)">1841</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4012.000000 -956.000000) translate(0,12)">1842</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4026.000000 -1139.000000) translate(0,12)">1742</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4026.000000 -1060.000000) translate(0,12)">1743</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4026.000000 -1000.000000) translate(0,12)">1744</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5116.000000 -630.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5080.000000 -560.000000) translate(0,15)">龙排II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5133.000000 -511.000000) translate(0,15)">AB相</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5116.000000 -481.000000) translate(0,15)">新桥线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5090.000000 -412.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4285.000000 -824.000000) translate(0,12)">1752</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4168.000000 -824.000000) translate(0,12)">1751</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4218.000000 -871.000000) translate(0,12)">185</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4166.000000 -888.000000) translate(0,12)">1851</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4285.000000 -888.000000) translate(0,12)">1852</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4735.000000 -428.000000) translate(0,15)">35kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4735.000000 -1085.000000) translate(0,15)">35kV I段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3499.000000 -450.000000) translate(0,15)">10kV I段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4012.000000 -761.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4012.000000 -564.000000) translate(0,12)">431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4012.000000 -505.000000) translate(0,12)">4312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4012.000000 -618.000000) translate(0,12)">4311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4619.000000 -926.000000) translate(0,15)">321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4665.000000 -926.000000) translate(0,15)">3212</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4561.000000 -926.000000) translate(0,15)">3211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4665.000000 -1000.000000) translate(0,15)">3601</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4665.000000 -1082.000000) translate(0,15)">3602</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4229.000000 -467.000000) translate(0,12)">4601</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4356.000000 -467.000000) translate(0,12)">4602</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4234.000000 -527.000000) translate(0,12)">4701</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3638.000000 -582.000000) translate(0,12)">4781</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3638.000000 -654.000000) translate(0,12)">4782</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3629.000000 -536.000000) translate(0,12)">468</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3629.000000 -467.000000) translate(0,12)">4681</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3753.000000 -467.000000) translate(0,12)">4001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3753.000000 -652.000000) translate(0,12)">K01</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3753.000000 -701.000000) translate(0,12)">K011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3761.000000 -746.000000) translate(0,12)">K03</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3860.000000 -702.000000) translate(0,12)">1701</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3817.000000 -702.000000) translate(0,12)">K021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3629.000000 -342.000000) translate(0,12)">467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3629.000000 -393.000000) translate(0,12)">4671</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3648.000000 -321.000000) translate(0,12)">4771</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3648.000000 -249.000000) translate(0,12)">4772</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3788.000000 -342.000000) translate(0,12)">466</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3788.000000 -393.000000) translate(0,12)">4661</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3788.000000 -289.000000) translate(0,12)">4662</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3788.000000 -166.000000) translate(0,12)">4663</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3834.000000 -227.000000) translate(0,12)">4664</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3930.000000 -342.000000) translate(0,12)">464</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3930.000000 -393.000000) translate(0,12)">4641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3930.000000 -289.000000) translate(0,12)">4642</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3930.000000 -166.000000) translate(0,12)">4643</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3976.000000 -227.000000) translate(0,12)">4644</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4080.000000 -342.000000) translate(0,12)">463</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4080.000000 -393.000000) translate(0,12)">4631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4080.000000 -289.000000) translate(0,12)">4632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4078.000000 -166.000000) translate(0,12)">4633</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4126.000000 -227.000000) translate(0,12)">4634</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4219.000000 -342.000000) translate(0,12)">462</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4219.000000 -393.000000) translate(0,12)">4621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4219.000000 -289.000000) translate(0,12)">4622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4219.000000 -166.000000) translate(0,12)">4623</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4265.000000 -227.000000) translate(0,12)">4624</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4369.000000 -342.000000) translate(0,12)">461</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4369.000000 -393.000000) translate(0,12)">4611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4369.000000 -289.000000) translate(0,12)">4612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4369.000000 -166.000000) translate(0,12)">4613</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4415.000000 -227.000000) translate(0,12)">4614</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4499.000000 -342.000000) translate(0,12)">465</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4499.000000 -393.000000) translate(0,12)">4651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4499.000000 -289.000000) translate(0,12)">4652</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4604.000000 -342.000000) translate(0,12)">481</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4604.000000 -393.000000) translate(0,12)">4811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4604.000000 -289.000000) translate(0,12)">4812</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4604.000000 -166.000000) translate(0,12)">4813</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4650.000000 -227.000000) translate(0,12)">4814</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4814.000000 -594.000000) translate(0,12)">386</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4749.000000 -594.000000) translate(0,12)">3861</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4857.000000 -594.000000) translate(0,12)">3862</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4933.000000 -646.000000) translate(0,12)">3864</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4749.000000 -556.000000) translate(0,12)">3222</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4749.000000 -496.000000) translate(0,12)">3841</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4857.000000 -496.000000) translate(0,12)">3842</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4933.000000 -548.000000) translate(0,12)">3844</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4814.000000 -496.000000) translate(0,12)">384</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4814.000000 -666.000000) translate(0,12)">380</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4814.000000 -1007.000000) translate(0,12)">385</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4749.000000 -1007.000000) translate(0,12)">3851</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4857.000000 -1007.000000) translate(0,12)">3852</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4814.000000 -925.000000) translate(0,12)">383</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4749.000000 -925.000000) translate(0,12)">3831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4857.000000 -925.000000) translate(0,12)">3832</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4933.000000 -961.000000) translate(0,12)">3834</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4749.000000 -839.000000) translate(0,12)">3821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4857.000000 -839.000000) translate(0,12)">3822</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4933.000000 -874.000000) translate(0,12)">3824</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4814.000000 -839.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4749.000000 -751.000000) translate(0,12)">3811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4857.000000 -751.000000) translate(0,12)">3812</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4933.000000 -791.000000) translate(0,12)">3814</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4814.000000 -751.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4735.000000 -1108.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4735.000000 -401.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3499.000000 -469.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4749.000000 -720.000000) translate(0,12)">3801</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4749.000000 -625.000000) translate(0,12)">3802</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3817.000000 -652.000000) translate(0,12)">K02</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(255,0,0)" stroke-width="0.5" width="360" x="3118" y="-1199"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="1201" stroke="rgb(255,0,0)" stroke-width="0.5" width="2150" x="3118" y="-1199"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(255,0,0)" stroke-width="0.5" width="360" x="3117" y="-1080"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(255,0,0)" stroke-width="0.5" width="360" x="3117" y="-598"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="AC-CX_MD.CX_MD_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3501,-428 4692,-428 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="2746" ObjectName="BS-CX_MD.CX_MD_9IM"/>
    <cge:TPSR_Ref TObjectID="2746"/></metadata>
   <polyline fill="none" opacity="0" points="3501,-428 4692,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-CX_MD.CX_MD_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4725,-1085 4725,-676 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="2744" ObjectName="BS-CX_MD.CX_MD_3IM"/>
    <cge:TPSR_Ref TObjectID="2744"/></metadata>
   <polyline fill="none" opacity="0" points="4725,-1085 4725,-676 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3744,-190 4688,-190 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3744,-190 4688,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4987,-1097 4987,-349 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4987,-1097 4987,-349 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4246,-911 4451,-911 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4246,-911 4451,-911 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3946,-911 4192,-911 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3946,-911 4192,-911 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-CX_MD.CX_MD_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4725,-630 4725,-412 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="2745" ObjectName="BS-CX_MD.CX_MD_3IIM"/>
    <cge:TPSR_Ref TObjectID="2745"/></metadata>
   <polyline fill="none" opacity="0" points="4725,-630 4725,-412 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="15b84a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3734.000000 -532.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="15b8a90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3735.000000 -534.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="1625470">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3610.000000 -239.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="1616b60">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3646.000000 -30.000000)" xlink:href="#lightningRod:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="15eae70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3610.000000 -619.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="15ffaa0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 1.000000 3646.000000 -833.000000)" xlink:href="#lightningRod:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="159e870">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3941.000000 -633.000000)" xlink:href="#lightningRod:shape20"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="159f580">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3921.000000 -702.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="164cb80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4567.000000 -117.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="15aae30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4332.000000 -117.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="15cd070">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4182.000000 -117.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="15bdff0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4043.000000 -117.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="15776a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3893.000000 -117.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="15850b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3751.000000 -117.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="174e2f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4340.000000 -506.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="174edd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4337.000000 -560.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="1783d30">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4518.000000 -1003.000000)" xlink:href="#lightningRod:shape87"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="17887f0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4638.500000 -1048.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="1789070">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4573.000000 -1045.000000)" xlink:href="#lightningRod:shape29"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="178a390">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 5041.500000 -696.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="178b3a0">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 5040.500000 -784.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="178c610">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 5040.500000 -870.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="178dae0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5130.000000 -931.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="179a370">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4195.000000 -617.000000)" xlink:href="#lightningRod:shape87"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="196f790">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4351.000000 -1150.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="1987f90">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3977.000000 -1154.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="19aaa30">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 5013.500000 -539.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="19aba40">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5128.000000 -493.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="19acfe0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5072.000000 -425.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="19addf0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5127.000000 -419.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="19aec10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5001.500000 -420.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="19afc20">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5147.000000 -587.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="19b0d00">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5092.500000 -562.500000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="1a0dea0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4269.000000 -1102.000000)" xlink:href="#lightningRod:shape96"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="1a109c0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3896.000000 -1106.000000)" xlink:href="#lightningRod:shape96"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-17805" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4106.000000 -1201.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17805" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2765"/>
     <cge:Term_Ref ObjectID="3954"/>
    <cge:TPSR_Ref TObjectID="2765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-17806" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4106.000000 -1201.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17806" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2765"/>
     <cge:Term_Ref ObjectID="3954"/>
    <cge:TPSR_Ref TObjectID="2765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-17800" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4106.000000 -1201.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17800" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2765"/>
     <cge:Term_Ref ObjectID="3954"/>
    <cge:TPSR_Ref TObjectID="2765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-17826" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4487.000000 -1201.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17826" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2777"/>
     <cge:Term_Ref ObjectID="3978"/>
    <cge:TPSR_Ref TObjectID="2777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-17827" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4487.000000 -1201.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17827" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2777"/>
     <cge:Term_Ref ObjectID="3978"/>
    <cge:TPSR_Ref TObjectID="2777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-17821" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4487.000000 -1201.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17821" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2777"/>
     <cge:Term_Ref ObjectID="3978"/>
    <cge:TPSR_Ref TObjectID="2777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-18889" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4650.000000 -886.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="18889" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2860"/>
     <cge:Term_Ref ObjectID="4144"/>
    <cge:TPSR_Ref TObjectID="2860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-18890" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4650.000000 -886.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="18890" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2860"/>
     <cge:Term_Ref ObjectID="4144"/>
    <cge:TPSR_Ref TObjectID="2860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-18886" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4650.000000 -886.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="18886" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2860"/>
     <cge:Term_Ref ObjectID="4144"/>
    <cge:TPSR_Ref TObjectID="2860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-17903" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5239.000000 -1005.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17903" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2832"/>
     <cge:Term_Ref ObjectID="4088"/>
    <cge:TPSR_Ref TObjectID="2832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-17904" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5239.000000 -1005.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17904" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2832"/>
     <cge:Term_Ref ObjectID="4088"/>
    <cge:TPSR_Ref TObjectID="2832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-17901" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5239.000000 -1005.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17901" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2832"/>
     <cge:Term_Ref ObjectID="4088"/>
    <cge:TPSR_Ref TObjectID="2832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-17784" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5239.000000 -921.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17784" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2756"/>
     <cge:Term_Ref ObjectID="3936"/>
    <cge:TPSR_Ref TObjectID="2756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-17785" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5239.000000 -921.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17785" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2756"/>
     <cge:Term_Ref ObjectID="3936"/>
    <cge:TPSR_Ref TObjectID="2756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-17782" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5239.000000 -921.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17782" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2756"/>
     <cge:Term_Ref ObjectID="3936"/>
    <cge:TPSR_Ref TObjectID="2756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-17780" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5239.000000 -844.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17780" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2752"/>
     <cge:Term_Ref ObjectID="3928"/>
    <cge:TPSR_Ref TObjectID="2752"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-17781" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5239.000000 -844.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17781" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2752"/>
     <cge:Term_Ref ObjectID="3928"/>
    <cge:TPSR_Ref TObjectID="2752"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-17778" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5239.000000 -844.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17778" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2752"/>
     <cge:Term_Ref ObjectID="3928"/>
    <cge:TPSR_Ref TObjectID="2752"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-17776" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5239.000000 -757.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17776" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2748"/>
     <cge:Term_Ref ObjectID="3920"/>
    <cge:TPSR_Ref TObjectID="2748"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-17777" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5239.000000 -757.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17777" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2748"/>
     <cge:Term_Ref ObjectID="3920"/>
    <cge:TPSR_Ref TObjectID="2748"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-17774" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5239.000000 -757.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17774" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2748"/>
     <cge:Term_Ref ObjectID="3920"/>
    <cge:TPSR_Ref TObjectID="2748"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-17788" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4914.000000 -697.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17788" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2760"/>
     <cge:Term_Ref ObjectID="3944"/>
    <cge:TPSR_Ref TObjectID="2760"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-17789" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4914.000000 -697.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17789" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2760"/>
     <cge:Term_Ref ObjectID="3944"/>
    <cge:TPSR_Ref TObjectID="2760"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-17786" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4914.000000 -697.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17786" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2760"/>
     <cge:Term_Ref ObjectID="3944"/>
    <cge:TPSR_Ref TObjectID="2760"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-17933" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5239.000000 -580.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17933" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2849"/>
     <cge:Term_Ref ObjectID="4122"/>
    <cge:TPSR_Ref TObjectID="2849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-17934" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5239.000000 -580.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17934" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2849"/>
     <cge:Term_Ref ObjectID="4122"/>
    <cge:TPSR_Ref TObjectID="2849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-17931" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5239.000000 -580.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17931" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2849"/>
     <cge:Term_Ref ObjectID="4122"/>
    <cge:TPSR_Ref TObjectID="2849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-17792" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5239.000000 -496.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17792" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2761"/>
     <cge:Term_Ref ObjectID="3946"/>
    <cge:TPSR_Ref TObjectID="2761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-17793" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5239.000000 -496.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17793" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2761"/>
     <cge:Term_Ref ObjectID="3946"/>
    <cge:TPSR_Ref TObjectID="2761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-17790" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5239.000000 -496.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17790" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2761"/>
     <cge:Term_Ref ObjectID="3946"/>
    <cge:TPSR_Ref TObjectID="2761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-17812" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4241.000000 -963.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17812" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2771"/>
     <cge:Term_Ref ObjectID="3966"/>
    <cge:TPSR_Ref TObjectID="2771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-17813" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4241.000000 -963.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17813" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2771"/>
     <cge:Term_Ref ObjectID="3966"/>
    <cge:TPSR_Ref TObjectID="2771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-17808" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4241.000000 -963.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17808" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2771"/>
     <cge:Term_Ref ObjectID="3966"/>
    <cge:TPSR_Ref TObjectID="2771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-18902" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3952.000000 -576.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="18902" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2790"/>
     <cge:Term_Ref ObjectID="4004"/>
    <cge:TPSR_Ref TObjectID="2790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-18903" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3952.000000 -576.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="18903" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2790"/>
     <cge:Term_Ref ObjectID="4004"/>
    <cge:TPSR_Ref TObjectID="2790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-18899" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3952.000000 -576.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="18899" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2790"/>
     <cge:Term_Ref ObjectID="4004"/>
    <cge:TPSR_Ref TObjectID="2790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-17885" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3805.000000 -25.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17885" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2813"/>
     <cge:Term_Ref ObjectID="4050"/>
    <cge:TPSR_Ref TObjectID="2813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-17886" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3805.000000 -25.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17886" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2813"/>
     <cge:Term_Ref ObjectID="4050"/>
    <cge:TPSR_Ref TObjectID="2813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-17883" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3805.000000 -25.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17883" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2813"/>
     <cge:Term_Ref ObjectID="4050"/>
    <cge:TPSR_Ref TObjectID="2813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-17881" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3950.000000 -25.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17881" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2808"/>
     <cge:Term_Ref ObjectID="4040"/>
    <cge:TPSR_Ref TObjectID="2808"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-17882" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3950.000000 -25.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17882" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2808"/>
     <cge:Term_Ref ObjectID="4040"/>
    <cge:TPSR_Ref TObjectID="2808"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-17879" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3950.000000 -25.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17879" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2808"/>
     <cge:Term_Ref ObjectID="4040"/>
    <cge:TPSR_Ref TObjectID="2808"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-17877" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4105.000000 -25.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17877" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2803"/>
     <cge:Term_Ref ObjectID="4030"/>
    <cge:TPSR_Ref TObjectID="2803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-17878" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4105.000000 -25.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17878" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2803"/>
     <cge:Term_Ref ObjectID="4030"/>
    <cge:TPSR_Ref TObjectID="2803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-17875" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4105.000000 -25.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17875" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2803"/>
     <cge:Term_Ref ObjectID="4030"/>
    <cge:TPSR_Ref TObjectID="2803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-18960" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4235.000000 -25.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="18960" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2798"/>
     <cge:Term_Ref ObjectID="4020"/>
    <cge:TPSR_Ref TObjectID="2798"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-17874" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4235.000000 -25.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17874" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2798"/>
     <cge:Term_Ref ObjectID="4020"/>
    <cge:TPSR_Ref TObjectID="2798"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-18958" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4235.000000 -25.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="18958" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2798"/>
     <cge:Term_Ref ObjectID="4020"/>
    <cge:TPSR_Ref TObjectID="2798"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-17872" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4385.000000 -25.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17872" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2793"/>
     <cge:Term_Ref ObjectID="4010"/>
    <cge:TPSR_Ref TObjectID="2793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-17873" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4385.000000 -25.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17873" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2793"/>
     <cge:Term_Ref ObjectID="4010"/>
    <cge:TPSR_Ref TObjectID="2793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-17870" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4385.000000 -25.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17870" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2793"/>
     <cge:Term_Ref ObjectID="4010"/>
    <cge:TPSR_Ref TObjectID="2793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-17889" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4523.000000 -25.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17889" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2818"/>
     <cge:Term_Ref ObjectID="4060"/>
    <cge:TPSR_Ref TObjectID="2818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-17890" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4523.000000 -25.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17890" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2818"/>
     <cge:Term_Ref ObjectID="4060"/>
    <cge:TPSR_Ref TObjectID="2818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-17887" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4523.000000 -25.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17887" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2818"/>
     <cge:Term_Ref ObjectID="4060"/>
    <cge:TPSR_Ref TObjectID="2818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-17907" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4626.000000 -25.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17907" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2841"/>
     <cge:Term_Ref ObjectID="4106"/>
    <cge:TPSR_Ref TObjectID="2841"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-17908" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4626.000000 -25.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17908" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2841"/>
     <cge:Term_Ref ObjectID="4106"/>
    <cge:TPSR_Ref TObjectID="2841"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-17905" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4626.000000 -25.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17905" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2841"/>
     <cge:Term_Ref ObjectID="4106"/>
    <cge:TPSR_Ref TObjectID="2841"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-17900" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4171.000000 -754.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17900" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2859"/>
     <cge:Term_Ref ObjectID="4139"/>
    <cge:TPSR_Ref TObjectID="2859"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-17840" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4799.000000 -401.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17840" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2745"/>
     <cge:Term_Ref ObjectID="3917"/>
    <cge:TPSR_Ref TObjectID="2745"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-17851" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3559.000000 -469.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17851" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2746"/>
     <cge:Term_Ref ObjectID="3918"/>
    <cge:TPSR_Ref TObjectID="2746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-17837" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4796.000000 -1108.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17837" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2744"/>
     <cge:Term_Ref ObjectID="3916"/>
    <cge:TPSR_Ref TObjectID="2744"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-17898" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3665.000000 -871.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17898" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2825"/>
     <cge:Term_Ref ObjectID="4074"/>
    <cge:TPSR_Ref TObjectID="2825"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-17895" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3665.000000 -871.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17895" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2825"/>
     <cge:Term_Ref ObjectID="4074"/>
    <cge:TPSR_Ref TObjectID="2825"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-17894" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3647.000000 -25.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17894" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2821"/>
     <cge:Term_Ref ObjectID="4066"/>
    <cge:TPSR_Ref TObjectID="2821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-17891" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3647.000000 -25.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17891" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="2821"/>
     <cge:Term_Ref ObjectID="4066"/>
    <cge:TPSR_Ref TObjectID="2821"/></metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4054.000000 1201.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4043.000000 1186.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4068.000000 1171.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4087.000000 754.000000) translate(0,12)">档位(档)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4087.000000 739.000000) translate(0,12)">油温(oC)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4057.000000 724.000000) translate(0,12)">绕组温度(oC)：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3603.000000 871.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3628.000000 856.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4187.000000 963.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4176.000000 948.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4201.000000 933.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4433.000000 1201.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4422.000000 1186.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4447.000000 1171.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5186.000000 1005.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5175.000000 990.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5200.000000 975.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3900.000000 576.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3889.000000 561.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3914.000000 546.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4597.000000 886.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4586.000000 871.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4611.000000 856.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3754.000000 25.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3743.000000 10.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3768.000000 -5.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3898.000000 25.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3887.000000 10.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 -5.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4052.000000 25.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4041.000000 10.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4066.000000 -5.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4184.000000 25.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4173.000000 10.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4198.000000 -5.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4332.000000 25.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4321.000000 10.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4346.000000 -5.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4471.000000 25.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4460.000000 10.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4485.000000 -5.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4576.000000 25.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4565.000000 10.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4590.000000 -5.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3585.000000 25.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3610.000000 10.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5186.000000 921.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5175.000000 906.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5200.000000 891.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5186.000000 844.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5175.000000 829.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5200.000000 814.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5186.000000 757.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5175.000000 742.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5200.000000 727.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5186.000000 580.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5175.000000 565.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5200.000000 550.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5186.000000 496.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5175.000000 481.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5200.000000 466.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4855.000000 697.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4844.000000 682.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4869.000000 667.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3731.000000 -575.000000)" xlink:href="#transformer2:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3731.000000 -575.000000)" xlink:href="#transformer2:shape3_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-18578">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4032.000000 -749.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2830" ObjectName="SW-CX_MD.CX_MD_1745SW"/>
     <cge:Meas_Ref ObjectId="18578"/>
    <cge:TPSR_Ref TObjectID="2830"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18577">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3989.000000 -789.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2829" ObjectName="SW-CX_MD.CX_MD_1843SW"/>
     <cge:Meas_Ref ObjectId="18577"/>
    <cge:TPSR_Ref TObjectID="2829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18279">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3988.000000 -569.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2792" ObjectName="SW-CX_MD.CX_MD_4311SW"/>
     <cge:Meas_Ref ObjectId="18279"/>
    <cge:TPSR_Ref TObjectID="2792"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18278">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3988.000000 -456.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2791" ObjectName="SW-CX_MD.CX_MD_4312SW"/>
     <cge:Meas_Ref ObjectId="18278"/>
    <cge:TPSR_Ref TObjectID="2791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18620">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3729.000000 -418.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2837" ObjectName="SW-CX_MD.CX_MD_4001SW"/>
     <cge:Meas_Ref ObjectId="18620"/>
    <cge:TPSR_Ref TObjectID="2837"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18621">
    <use class="BV-38KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3729.000000 -651.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2838" ObjectName="SW-CX_MD.CX_MD_K011SW"/>
     <cge:Meas_Ref ObjectId="18621"/>
    <cge:TPSR_Ref TObjectID="2838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18629">
    <use class="BV-38KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3793.000000 -653.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2840" ObjectName="SW-CX_MD.CX_MD_K021SW"/>
     <cge:Meas_Ref ObjectId="18629"/>
    <cge:TPSR_Ref TObjectID="2840"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18537">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3605.000000 -419.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2826" ObjectName="SW-CX_MD.CX_MD_4681SW"/>
     <cge:Meas_Ref ObjectId="18537"/>
    <cge:TPSR_Ref TObjectID="2826"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18539">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3647.000000 -605.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2828" ObjectName="SW-CX_MD.CX_MD_4782SW"/>
     <cge:Meas_Ref ObjectId="18539"/>
    <cge:TPSR_Ref TObjectID="2828"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18538">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3648.000000 -533.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2827" ObjectName="SW-CX_MD.CX_MD_4781SW"/>
     <cge:Meas_Ref ObjectId="18538"/>
    <cge:TPSR_Ref TObjectID="2827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18510">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3648.000000 -273.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2823" ObjectName="SW-CX_MD.CX_MD_4771SW"/>
     <cge:Meas_Ref ObjectId="18510"/>
    <cge:TPSR_Ref TObjectID="2823"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18511">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3647.000000 -201.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2824" ObjectName="SW-CX_MD.CX_MD_4772SW"/>
     <cge:Meas_Ref ObjectId="18511"/>
    <cge:TPSR_Ref TObjectID="2824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18509">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3605.000000 -344.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2822" ObjectName="SW-CX_MD.CX_MD_4671SW"/>
     <cge:Meas_Ref ObjectId="18509"/>
    <cge:TPSR_Ref TObjectID="2822"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18579">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3885.000000 -645.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2831" ObjectName="SW-CX_MD.CX_MD_1701SW"/>
     <cge:Meas_Ref ObjectId="18579"/>
    <cge:TPSR_Ref TObjectID="2831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18744">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4580.000000 -346.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2842" ObjectName="SW-CX_MD.CX_MD_4811SW"/>
     <cge:Meas_Ref ObjectId="18744"/>
    <cge:TPSR_Ref TObjectID="2842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18745">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4580.000000 -242.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2843" ObjectName="SW-CX_MD.CX_MD_4812SW"/>
     <cge:Meas_Ref ObjectId="18745"/>
    <cge:TPSR_Ref TObjectID="2843"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18747">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4632.000000 -180.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2845" ObjectName="SW-CX_MD.CX_MD_4814SW"/>
     <cge:Meas_Ref ObjectId="18747"/>
    <cge:TPSR_Ref TObjectID="2845"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18746">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4580.000000 -119.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2844" ObjectName="SW-CX_MD.CX_MD_4813SW"/>
     <cge:Meas_Ref ObjectId="18746"/>
    <cge:TPSR_Ref TObjectID="2844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18481">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4475.000000 -346.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2819" ObjectName="SW-CX_MD.CX_MD_4651SW"/>
     <cge:Meas_Ref ObjectId="18481"/>
    <cge:TPSR_Ref TObjectID="2819"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18482">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4475.000000 -242.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2820" ObjectName="SW-CX_MD.CX_MD_4652SW"/>
     <cge:Meas_Ref ObjectId="18482"/>
    <cge:TPSR_Ref TObjectID="2820"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18331">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4345.000000 -346.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2794" ObjectName="SW-CX_MD.CX_MD_4611SW"/>
     <cge:Meas_Ref ObjectId="18331"/>
    <cge:TPSR_Ref TObjectID="2794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18332">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4345.000000 -242.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2795" ObjectName="SW-CX_MD.CX_MD_4612SW"/>
     <cge:Meas_Ref ObjectId="18332"/>
    <cge:TPSR_Ref TObjectID="2795"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18334">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4397.000000 -180.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2797" ObjectName="SW-CX_MD.CX_MD_4614SW"/>
     <cge:Meas_Ref ObjectId="18334"/>
    <cge:TPSR_Ref TObjectID="2797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18333">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4345.000000 -119.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2796" ObjectName="SW-CX_MD.CX_MD_4613SW"/>
     <cge:Meas_Ref ObjectId="18333"/>
    <cge:TPSR_Ref TObjectID="2796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18361">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4195.000000 -346.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2799" ObjectName="SW-CX_MD.CX_MD_4621SW"/>
     <cge:Meas_Ref ObjectId="18361"/>
    <cge:TPSR_Ref TObjectID="2799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18362">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4195.000000 -242.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2800" ObjectName="SW-CX_MD.CX_MD_4622SW"/>
     <cge:Meas_Ref ObjectId="18362"/>
    <cge:TPSR_Ref TObjectID="2800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18364">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4247.000000 -180.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2802" ObjectName="SW-CX_MD.CX_MD_4624SW"/>
     <cge:Meas_Ref ObjectId="18364"/>
    <cge:TPSR_Ref TObjectID="2802"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18363">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4195.000000 -119.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2801" ObjectName="SW-CX_MD.CX_MD_4623SW"/>
     <cge:Meas_Ref ObjectId="18363"/>
    <cge:TPSR_Ref TObjectID="2801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18391">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4056.000000 -346.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2804" ObjectName="SW-CX_MD.CX_MD_4631SW"/>
     <cge:Meas_Ref ObjectId="18391"/>
    <cge:TPSR_Ref TObjectID="2804"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18392">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4056.000000 -242.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2805" ObjectName="SW-CX_MD.CX_MD_4632SW"/>
     <cge:Meas_Ref ObjectId="18392"/>
    <cge:TPSR_Ref TObjectID="2805"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18394">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4108.000000 -180.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2807" ObjectName="SW-CX_MD.CX_MD_4634SW"/>
     <cge:Meas_Ref ObjectId="18394"/>
    <cge:TPSR_Ref TObjectID="2807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18393">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4056.000000 -119.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2806" ObjectName="SW-CX_MD.CX_MD_4633SW"/>
     <cge:Meas_Ref ObjectId="18393"/>
    <cge:TPSR_Ref TObjectID="2806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18421">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3906.000000 -346.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2809" ObjectName="SW-CX_MD.CX_MD_4641SW"/>
     <cge:Meas_Ref ObjectId="18421"/>
    <cge:TPSR_Ref TObjectID="2809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18422">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3906.000000 -242.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2810" ObjectName="SW-CX_MD.CX_MD_4642SW"/>
     <cge:Meas_Ref ObjectId="18422"/>
    <cge:TPSR_Ref TObjectID="2810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18424">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3958.000000 -180.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2812" ObjectName="SW-CX_MD.CX_MD_4644SW"/>
     <cge:Meas_Ref ObjectId="18424"/>
    <cge:TPSR_Ref TObjectID="2812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18423">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3906.000000 -119.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2811" ObjectName="SW-CX_MD.CX_MD_4643SW"/>
     <cge:Meas_Ref ObjectId="18423"/>
    <cge:TPSR_Ref TObjectID="2811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18451">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3764.000000 -346.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2814" ObjectName="SW-CX_MD.CX_MD_4661SW"/>
     <cge:Meas_Ref ObjectId="18451"/>
    <cge:TPSR_Ref TObjectID="2814"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18452">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3764.000000 -242.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2815" ObjectName="SW-CX_MD.CX_MD_4662SW"/>
     <cge:Meas_Ref ObjectId="18452"/>
    <cge:TPSR_Ref TObjectID="2815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18454">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3816.000000 -180.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2817" ObjectName="SW-CX_MD.CX_MD_4664SW"/>
     <cge:Meas_Ref ObjectId="18454"/>
    <cge:TPSR_Ref TObjectID="2817"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18453">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3764.000000 -119.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2816" ObjectName="SW-CX_MD.CX_MD_4663SW"/>
     <cge:Meas_Ref ObjectId="18453"/>
    <cge:TPSR_Ref TObjectID="2816"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18174">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4207.000000 -420.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2785" ObjectName="SW-CX_MD.CX_MD_4601SW"/>
     <cge:Meas_Ref ObjectId="18174"/>
    <cge:TPSR_Ref TObjectID="2785"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18175">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4244.000000 -476.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2786" ObjectName="SW-CX_MD.CX_MD_4701SW"/>
     <cge:Meas_Ref ObjectId="18175"/>
    <cge:TPSR_Ref TObjectID="2786"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18185">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4334.000000 -421.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2787" ObjectName="SW-CX_MD.CX_MD_4602SW"/>
     <cge:Meas_Ref ObjectId="18185"/>
    <cge:TPSR_Ref TObjectID="2787"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4438.000000 -424.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18590">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4754.000000 -957.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2833" ObjectName="SW-CX_MD.CX_MD_3851SW"/>
     <cge:Meas_Ref ObjectId="18590"/>
    <cge:TPSR_Ref TObjectID="2833"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18591">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4867.000000 -957.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2834" ObjectName="SW-CX_MD.CX_MD_3852SW"/>
     <cge:Meas_Ref ObjectId="18591"/>
    <cge:TPSR_Ref TObjectID="2834"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-17943">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4758.000000 -703.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2749" ObjectName="SW-CX_MD.CX_MD_3811SW"/>
     <cge:Meas_Ref ObjectId="17943"/>
    <cge:TPSR_Ref TObjectID="2749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-17944">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4866.000000 -703.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2750" ObjectName="SW-CX_MD.CX_MD_3812SW"/>
     <cge:Meas_Ref ObjectId="17944"/>
    <cge:TPSR_Ref TObjectID="2750"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-17945">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4940.000000 -739.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2751" ObjectName="SW-CX_MD.CX_MD_3814SW"/>
     <cge:Meas_Ref ObjectId="17945"/>
    <cge:TPSR_Ref TObjectID="2751"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-17973">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4758.000000 -791.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2753" ObjectName="SW-CX_MD.CX_MD_3821SW"/>
     <cge:Meas_Ref ObjectId="17973"/>
    <cge:TPSR_Ref TObjectID="2753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-17974">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4866.000000 -791.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2754" ObjectName="SW-CX_MD.CX_MD_3822SW"/>
     <cge:Meas_Ref ObjectId="17974"/>
    <cge:TPSR_Ref TObjectID="2754"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-17975">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4940.000000 -822.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2755" ObjectName="SW-CX_MD.CX_MD_3824SW"/>
     <cge:Meas_Ref ObjectId="17975"/>
    <cge:TPSR_Ref TObjectID="2755"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18003">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4758.000000 -877.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2757" ObjectName="SW-CX_MD.CX_MD_3831SW"/>
     <cge:Meas_Ref ObjectId="18003"/>
    <cge:TPSR_Ref TObjectID="2757"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18004">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4866.000000 -877.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2758" ObjectName="SW-CX_MD.CX_MD_3832SW"/>
     <cge:Meas_Ref ObjectId="18004"/>
    <cge:TPSR_Ref TObjectID="2758"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18005">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4940.000000 -909.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2759" ObjectName="SW-CX_MD.CX_MD_3834SW"/>
     <cge:Meas_Ref ObjectId="18005"/>
    <cge:TPSR_Ref TObjectID="2759"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18248">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4675.000000 -876.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2788" ObjectName="SW-CX_MD.CX_MD_3212SW"/>
     <cge:Meas_Ref ObjectId="18248"/>
    <cge:TPSR_Ref TObjectID="2788"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18249">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4570.000000 -876.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2789" ObjectName="SW-CX_MD.CX_MD_3211SW"/>
     <cge:Meas_Ref ObjectId="18249"/>
    <cge:TPSR_Ref TObjectID="2789"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18145">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4673.000000 -949.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2783" ObjectName="SW-CX_MD.CX_MD_3601SW"/>
     <cge:Meas_Ref ObjectId="18145"/>
    <cge:TPSR_Ref TObjectID="2783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18154">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4673.000000 -1031.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2784" ObjectName="SW-CX_MD.CX_MD_3602SW"/>
     <cge:Meas_Ref ObjectId="18154"/>
    <cge:TPSR_Ref TObjectID="2784"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18139">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4363.000000 -904.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2781" ObjectName="SW-CX_MD.CX_MD_1862SW"/>
     <cge:Meas_Ref ObjectId="18139"/>
    <cge:TPSR_Ref TObjectID="2781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18140">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4406.000000 -951.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2782" ObjectName="SW-CX_MD.CX_MD_1764SW"/>
     <cge:Meas_Ref ObjectId="18140"/>
    <cge:TPSR_Ref TObjectID="2782"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18138">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4405.000000 -1011.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2780" ObjectName="SW-CX_MD.CX_MD_1763SW"/>
     <cge:Meas_Ref ObjectId="18138"/>
    <cge:TPSR_Ref TObjectID="2780"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18136">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4363.000000 -1028.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2778" ObjectName="SW-CX_MD.CX_MD_1861SW"/>
     <cge:Meas_Ref ObjectId="18136"/>
    <cge:TPSR_Ref TObjectID="2778"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18137">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4406.000000 -1089.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2779" ObjectName="SW-CX_MD.CX_MD_1762SW"/>
     <cge:Meas_Ref ObjectId="18137"/>
    <cge:TPSR_Ref TObjectID="2779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18098">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3989.000000 -908.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2769" ObjectName="SW-CX_MD.CX_MD_1842SW"/>
     <cge:Meas_Ref ObjectId="18098"/>
    <cge:TPSR_Ref TObjectID="2769"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18099">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4032.000000 -952.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2770" ObjectName="SW-CX_MD.CX_MD_1744SW"/>
     <cge:Meas_Ref ObjectId="18099"/>
    <cge:TPSR_Ref TObjectID="2770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18097">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4031.000000 -1011.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2768" ObjectName="SW-CX_MD.CX_MD_1743SW"/>
     <cge:Meas_Ref ObjectId="18097"/>
    <cge:TPSR_Ref TObjectID="2768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18095">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3989.000000 -1032.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2766" ObjectName="SW-CX_MD.CX_MD_1841SW"/>
     <cge:Meas_Ref ObjectId="18095"/>
    <cge:TPSR_Ref TObjectID="2766"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18096">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4032.000000 -1090.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2767" ObjectName="SW-CX_MD.CX_MD_1742SW"/>
     <cge:Meas_Ref ObjectId="18096"/>
    <cge:TPSR_Ref TObjectID="2767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18042">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4758.000000 -674.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3022" ObjectName="SW-CX_MD.CX_MD_3801SW"/>
     <cge:Meas_Ref ObjectId="18042"/>
    <cge:TPSR_Ref TObjectID="3022"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18875">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4757.000000 -504.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2850" ObjectName="SW-CX_MD.CX_MD_3222SW"/>
     <cge:Meas_Ref ObjectId="18875"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18062">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4756.000000 -448.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2762" ObjectName="SW-CX_MD.CX_MD_3841SW"/>
     <cge:Meas_Ref ObjectId="18062"/>
    <cge:TPSR_Ref TObjectID="2762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18063">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4864.000000 -448.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2763" ObjectName="SW-CX_MD.CX_MD_3842SW"/>
     <cge:Meas_Ref ObjectId="18063"/>
    <cge:TPSR_Ref TObjectID="2763"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18064">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4940.000000 -496.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2764" ObjectName="SW-CX_MD.CX_MD_3844SW"/>
     <cge:Meas_Ref ObjectId="18064"/>
    <cge:TPSR_Ref TObjectID="2764"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18857">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4756.000000 -546.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2846" ObjectName="SW-CX_MD.CX_MD_3861SW"/>
     <cge:Meas_Ref ObjectId="18857"/>
    <cge:TPSR_Ref TObjectID="2846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18858">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4864.000000 -546.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2847" ObjectName="SW-CX_MD.CX_MD_3862SW"/>
     <cge:Meas_Ref ObjectId="18858"/>
    <cge:TPSR_Ref TObjectID="2847"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18859">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4940.000000 -594.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2848" ObjectName="SW-CX_MD.CX_MD_3864SW"/>
     <cge:Meas_Ref ObjectId="18859"/>
    <cge:TPSR_Ref TObjectID="2848"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18043">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4756.000000 -578.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3023" ObjectName="SW-CX_MD.CX_MD_3802SW"/>
     <cge:Meas_Ref ObjectId="18043"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18116">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4149.000000 -838.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2772" ObjectName="SW-CX_MD.CX_MD_1851SW"/>
     <cge:Meas_Ref ObjectId="18116"/>
    <cge:TPSR_Ref TObjectID="2772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18117">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4149.000000 -774.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2773" ObjectName="SW-CX_MD.CX_MD_1751SW"/>
     <cge:Meas_Ref ObjectId="18117"/>
    <cge:TPSR_Ref TObjectID="2773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18119">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4269.000000 -775.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2775" ObjectName="SW-CX_MD.CX_MD_1752SW"/>
     <cge:Meas_Ref ObjectId="18119"/>
    <cge:TPSR_Ref TObjectID="2775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-18118">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4269.000000 -839.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="2774" ObjectName="SW-CX_MD.CX_MD_1852SW"/>
     <cge:Meas_Ref ObjectId="18118"/>
    <cge:TPSR_Ref TObjectID="2774"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-17899" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4163.000000 -739.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="17899" ObjectName="CX_MD:CX_MD_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4171.000000 -724.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4"/>
</svg>