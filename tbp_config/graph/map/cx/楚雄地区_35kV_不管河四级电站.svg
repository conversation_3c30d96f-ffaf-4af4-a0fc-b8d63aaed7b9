<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-45" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3116 -1200 1757 1201">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.890909" x1="29" x2="29" y1="6" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.583333" x1="26" x2="26" y1="4" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="22" x2="22" y1="0" y2="18"/>
   </symbol>
   <symbol id="hydroGenerator:shape2">
    <polyline points="12,27 11,28 12,28 12,29 12,30 13,31 13,32 14,33 15,33 15,34 16,34 17,35 18,35 19,35 20,35 21,35 22,34 23,34 24,33 25,33 25,32 26,31 26,30 27,29 27,28 27,28 27,27 " stroke-width="0.06"/>
    <circle cx="27" cy="27" fillStyle="0" r="26.5" stroke-width="0.55102"/>
    <polyline arcFlag="1" points="28,27 28,26 28,25 28,24 29,23 29,22 30,21 30,21 31,20 32,20 33,19 34,19 35,19 36,18 37,19 38,19 39,19 40,20 40,20 41,21 42,21 42,22 43,23 43,24 43,25 44,26 43,27 " stroke-width="0.06"/>
   </symbol>
   <symbol id="lightningRod:shape116">
    <circle cx="8" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="14" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="18" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape164">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="60" x2="60" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="57" x2="57" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="48" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="20" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="25" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="20" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="25" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
   </symbol>
   <symbol id="switch2:shape21_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="28" x2="36" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="25" x2="25" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="5" x2="5" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="25" x2="9" y1="23" y2="23"/>
   </symbol>
   <symbol id="switch2:shape21_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="26" x2="26" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="6" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="10" x2="10" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="26" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="3" x2="3" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="36" x2="36" y1="34" y2="14"/>
   </symbol>
   <symbol id="switch2:shape21-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="28" x2="36" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="25" x2="25" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="5" x2="5" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="25" x2="9" y1="23" y2="23"/>
   </symbol>
   <symbol id="switch2:shape21-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="26" x2="26" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="6" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="10" x2="10" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="26" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="3" x2="3" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="36" x2="36" y1="34" y2="14"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape3_0">
    <ellipse cx="13" cy="17" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="11" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="15" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="19" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape3_1">
    <circle cx="13" cy="34" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="9" y1="41" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="18" y1="41" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="18" y1="33" y2="33"/>
   </symbol>
   <symbol id="transformer2:shape25_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="58" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape25_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="12,76 19,76 16,83 12,76 "/>
   </symbol>
   <symbol id="voltageTransformer:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="3" x2="9" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="3" y1="14" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="14" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="24" y1="13" y2="11"/>
    <circle cx="7" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="15" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="24" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="15" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="6" y2="4"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_277b830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_277c980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_277d330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_277e320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_277f260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_277fe80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27808e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_27813a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2338dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2338dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2784680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2784680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2786410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2786410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2787430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2789030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2789c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_278a9e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_278b100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_278c8c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_278d5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_278de80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_278e640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_278f720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27900a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2790b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2791550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_27929d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2793570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_27945a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_27951e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_27a39b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2796ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_27980c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_27995f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1211" width="1767" x="3111" y="-1205"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3117" y="-599"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3117" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3117" y="-1199"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-46199">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.978261 -0.000000 0.000000 -0.928571 4162.000000 -751.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8045" ObjectName="SW-CX_BGHSJ.CX_BGHSJ_36160SW"/>
     <cge:Meas_Ref ObjectId="46199"/>
    <cge:TPSR_Ref TObjectID="8045"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46197">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4116.000000 -761.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8043" ObjectName="SW-CX_BGHSJ.CX_BGHSJ_3616SW"/>
     <cge:Meas_Ref ObjectId="46197"/>
    <cge:TPSR_Ref TObjectID="8043"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46198">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.978261 -0.000000 0.000000 -0.928571 4162.000000 -813.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8044" ObjectName="SW-CX_BGHSJ.CX_BGHSJ_36167SW"/>
     <cge:Meas_Ref ObjectId="46198"/>
    <cge:TPSR_Ref TObjectID="8044"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46200">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4294.000000 -843.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8046" ObjectName="SW-CX_BGHSJ.CX_BGHSJ_3619SW"/>
     <cge:Meas_Ref ObjectId="46200"/>
    <cge:TPSR_Ref TObjectID="8046"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46201">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.978261 -0.000000 0.000000 -0.928571 4341.000000 -830.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8047" ObjectName="SW-CX_BGHSJ.CX_BGHSJ_36197SW"/>
     <cge:Meas_Ref ObjectId="46201"/>
    <cge:TPSR_Ref TObjectID="8047"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46207">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4681.000000 -437.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8053" ObjectName="SW-CX_BGHSJ.CX_BGHSJ_6901SW"/>
     <cge:Meas_Ref ObjectId="46207"/>
    <cge:TPSR_Ref TObjectID="8053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46208">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(0.978261 -0.000000 0.000000 -0.928571 4725.000000 -493.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8054" ObjectName="SW-CX_BGHSJ.CX_BGHSJ_69017SW"/>
     <cge:Meas_Ref ObjectId="46208"/>
    <cge:TPSR_Ref TObjectID="8054"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46202">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4094.000000 -446.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8048" ObjectName="SW-CX_BGHSJ.CX_BGHSJ_6011SW"/>
     <cge:Meas_Ref ObjectId="46202"/>
    <cge:TPSR_Ref TObjectID="8048"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46203">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3856.000000 -372.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8049" ObjectName="SW-CX_BGHSJ.CX_BGHSJ_6611SW"/>
     <cge:Meas_Ref ObjectId="46203"/>
    <cge:TPSR_Ref TObjectID="8049"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46205">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3937.000000 -194.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8051" ObjectName="SW-CX_BGHSJ.CX_BGHSJ_6619SW"/>
     <cge:Meas_Ref ObjectId="46205"/>
    <cge:TPSR_Ref TObjectID="8051"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46206">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4017.000000 -194.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8052" ObjectName="SW-CX_BGHSJ.CX_BGHSJ_6618SW"/>
     <cge:Meas_Ref ObjectId="46206"/>
    <cge:TPSR_Ref TObjectID="8052"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46209">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4396.000000 -372.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8055" ObjectName="SW-CX_BGHSJ.CX_BGHSJ_6621SW"/>
     <cge:Meas_Ref ObjectId="46209"/>
    <cge:TPSR_Ref TObjectID="8055"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46211">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4478.000000 -187.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8057" ObjectName="SW-CX_BGHSJ.CX_BGHSJ_6629SW"/>
     <cge:Meas_Ref ObjectId="46211"/>
    <cge:TPSR_Ref TObjectID="8057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46212">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4558.000000 -187.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8058" ObjectName="SW-CX_BGHSJ.CX_BGHSJ_6628SW"/>
     <cge:Meas_Ref ObjectId="46212"/>
    <cge:TPSR_Ref TObjectID="8058"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4104.000000 -966.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315171">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4116.000000 -639.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48666" ObjectName="SW-CX_BGHSJ.XB"/>
     <cge:Meas_Ref ObjectId="315171"/>
    <cge:TPSR_Ref TObjectID="48666"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_BGHSJ.CX_BGHSJ_6IM">
    <g class="BV-6KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3788,-437 4872,-437 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="10463" ObjectName="BS-CX_BGHSJ.CX_BGHSJ_6IM"/>
    <cge:TPSR_Ref TObjectID="10463"/></metadata>
   <polyline fill="none" opacity="0" points="3788,-437 4872,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_BGHSJ.XM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4123,-712 4137,-712 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48667" ObjectName="BS-CX_BGHSJ.XM"/>
    <cge:TPSR_Ref TObjectID="48667"/></metadata>
   <polyline fill="none" opacity="0" points="4123,-712 4137,-712 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_BGHSJ.CX_BGHSJ_T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="15120"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4106.000000 -564.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4106.000000 -564.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="10848" ObjectName="TF-CX_BGHSJ.CX_BGHSJ_T"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4628.000000 -64.000000)" xlink:href="#transformer2:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4628.000000 -64.000000)" xlink:href="#transformer2:shape3_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4087.000000 -67.000000)" xlink:href="#transformer2:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4087.000000 -67.000000)" xlink:href="#transformer2:shape3_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4481.000000 -716.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4481.000000 -716.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="HydroGenerator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3864.000000 -120.000000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4404.000000 -115.000000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-6KV" id="g_1ffa2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3893,-291 3926,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="8051@x" ObjectIDND1="8052@x" ObjectIDND2="g_234c1d0@0" ObjectIDZND0="g_1fc30c0@0" Pin0InfoVect0LinkObjId="g_1fc30c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46205_0" Pin1InfoVect1LinkObjId="SW-46206_0" Pin1InfoVect2LinkObjId="g_234c1d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3893,-291 3926,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_238df60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4188,-775 4203,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8045@1" ObjectIDZND0="g_23bf9f0@0" Pin0InfoVect0LinkObjId="g_23bf9f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46199_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4188,-775 4203,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f72760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4188,-837 4198,-837 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8044@1" ObjectIDZND0="g_2365630@0" Pin0InfoVect0LinkObjId="g_2365630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46198_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4188,-837 4198,-837 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2018300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4309,-934 4309,-901 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1e94e10@0" ObjectIDZND0="8046@1" Pin0InfoVect0LinkObjId="SW-46200_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e94e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4309,-934 4309,-901 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23f9170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4309,-854 4332,-854 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_24e0010@0" ObjectIDND1="g_1ea4060@0" ObjectIDND2="8046@x" ObjectIDZND0="8047@0" Pin0InfoVect0LinkObjId="SW-46201_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_24e0010_0" Pin1InfoVect1LinkObjId="g_1ea4060_0" Pin1InfoVect2LinkObjId="SW-46200_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4309,-854 4332,-854 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ff8e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4367,-854 4378,-854 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8047@1" ObjectIDZND0="g_2779760@0" Pin0InfoVect0LinkObjId="g_2779760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46201_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4367,-854 4378,-854 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fcee00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4309,-865 4309,-854 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="8046@0" ObjectIDZND0="g_24e0010@0" ObjectIDZND1="g_1ea4060@0" ObjectIDZND2="8047@x" Pin0InfoVect0LinkObjId="g_24e0010_0" Pin0InfoVect1LinkObjId="g_1ea4060_0" Pin0InfoVect2LinkObjId="SW-46201_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4309,-865 4309,-854 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23a19a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4309,-839 4275,-839 4275,-807 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="8046@x" ObjectIDND1="8047@x" ObjectIDND2="g_1ea4060@0" ObjectIDZND0="g_24e0010@0" Pin0InfoVect0LinkObjId="g_24e0010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46200_0" Pin1InfoVect1LinkObjId="SW-46201_0" Pin1InfoVect2LinkObjId="g_1ea4060_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4309,-839 4275,-839 4275,-807 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fd7d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4309,-854 4309,-839 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="8046@x" ObjectIDND1="8047@x" ObjectIDZND0="g_24e0010@0" ObjectIDZND1="g_1ea4060@0" Pin0InfoVect0LinkObjId="g_24e0010_0" Pin0InfoVect1LinkObjId="g_1ea4060_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46200_0" Pin1InfoVect1LinkObjId="SW-46201_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4309,-854 4309,-839 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24f4590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4309,-934 4497,-934 4497,-901 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="8046@x" ObjectIDZND0="g_1e94e10@1" Pin0InfoVect0LinkObjId="g_1e94e10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4309,-934 4497,-934 4497,-901 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2503600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3892,-377 3892,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8049@1" ObjectIDZND0="8050@1" Pin0InfoVect0LinkObjId="SW-46204_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46203_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3892,-377 3892,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2362990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3892,-260 3973,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1fc30c0@0" ObjectIDND1="8050@x" ObjectIDND2="g_234c1d0@0" ObjectIDZND0="8051@x" ObjectIDZND1="8052@x" Pin0InfoVect0LinkObjId="SW-46205_0" Pin0InfoVect1LinkObjId="SW-46206_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1fc30c0_0" Pin1InfoVect1LinkObjId="SW-46204_0" Pin1InfoVect2LinkObjId="g_234c1d0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3892,-260 3973,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1fdad80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4053,-199 4053,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="8052@1" ObjectIDZND0="g_23c1710@0" ObjectIDZND1="g_2344d10@0" Pin0InfoVect0LinkObjId="g_23c1710_0" Pin0InfoVect1LinkObjId="g_2344d10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46206_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4053,-199 4053,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f781d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-303 4466,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="8058@x" ObjectIDND1="8057@x" ObjectIDND2="g_250bb90@0" ObjectIDZND0="g_23c1f10@0" Pin0InfoVect0LinkObjId="g_23c1f10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46212_0" Pin1InfoVect1LinkObjId="SW-46211_0" Pin1InfoVect2LinkObjId="g_250bb90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-303 4466,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2360690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-377 4432,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8055@1" ObjectIDZND0="8056@1" Pin0InfoVect0LinkObjId="SW-46210_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46209_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-377 4432,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1fd82a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-326 4432,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="8056@0" ObjectIDZND0="8058@x" ObjectIDZND1="8057@x" ObjectIDZND2="g_250bb90@0" Pin0InfoVect0LinkObjId="SW-46212_0" Pin0InfoVect1LinkObjId="SW-46211_0" Pin0InfoVect2LinkObjId="g_250bb90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-326 4432,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1fadb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-303 4432,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_23c1f10@0" ObjectIDND1="8056@x" ObjectIDZND0="8058@x" ObjectIDZND1="8057@x" ObjectIDZND2="g_250bb90@0" Pin0InfoVect0LinkObjId="SW-46212_0" Pin0InfoVect1LinkObjId="SW-46211_0" Pin0InfoVect2LinkObjId="g_250bb90_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_23c1f10_0" Pin1InfoVect1LinkObjId="SW-46210_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-303 4432,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1ffe1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-255 4514,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_23c1f10@0" ObjectIDND1="8056@x" ObjectIDND2="g_250bb90@0" ObjectIDZND0="8058@x" ObjectIDZND1="8057@x" Pin0InfoVect0LinkObjId="SW-46212_0" Pin0InfoVect1LinkObjId="SW-46211_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_23c1f10_0" Pin1InfoVect1LinkObjId="SW-46210_0" Pin1InfoVect2LinkObjId="g_250bb90_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-255 4514,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_261f640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4594,-192 4594,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="8058@1" ObjectIDZND0="g_1c85da0@0" ObjectIDZND1="g_23e3390@0" Pin0InfoVect0LinkObjId="g_1c85da0_0" Pin0InfoVect1LinkObjId="g_23e3390_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46212_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4594,-192 4594,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1db1e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4696,-437 4696,-459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10463@0" ObjectIDZND0="8053@0" Pin0InfoVect0LinkObjId="SW-46207_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d4ae60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4696,-437 4696,-459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1fb72c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-517 4719,-517 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1f8b180@0" ObjectIDND1="g_2386b00@0" ObjectIDND2="8053@x" ObjectIDZND0="8054@0" Pin0InfoVect0LinkObjId="SW-46208_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1f8b180_0" Pin1InfoVect1LinkObjId="g_2386b00_0" Pin1InfoVect2LinkObjId="SW-46207_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-517 4719,-517 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1ca51c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4751,-517 4764,-517 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8054@1" ObjectIDZND0="g_238ba80@0" Pin0InfoVect0LinkObjId="g_238ba80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46208_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4751,-517 4764,-517 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_23e9ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4696,-495 4696,-517 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="8053@1" ObjectIDZND0="g_1f8b180@0" ObjectIDZND1="g_2386b00@0" ObjectIDZND2="8054@x" Pin0InfoVect0LinkObjId="g_1f8b180_0" Pin0InfoVect1LinkObjId="g_2386b00_0" Pin0InfoVect2LinkObjId="SW-46208_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46207_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4696,-495 4696,-517 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1e3df90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4696,-549 4661,-549 4661,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="8053@x" ObjectIDND1="8054@x" ObjectIDND2="g_2386b00@0" ObjectIDZND0="g_1f8b180@0" Pin0InfoVect0LinkObjId="g_1f8b180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46207_0" Pin1InfoVect1LinkObjId="SW-46208_0" Pin1InfoVect2LinkObjId="g_2386b00_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4696,-549 4661,-549 4661,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1cf2780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4696,-517 4696,-549 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="8053@x" ObjectIDND1="8054@x" ObjectIDZND0="g_1f8b180@0" ObjectIDZND1="g_2386b00@0" Pin0InfoVect0LinkObjId="g_1f8b180_0" Pin0InfoVect1LinkObjId="g_2386b00_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46207_0" Pin1InfoVect1LinkObjId="SW-46208_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4696,-517 4696,-549 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e61cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4496,-809 4496,-856 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1e94e10@0" Pin0InfoVect0LinkObjId="g_1e94e10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4496,-809 4496,-856 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1daaf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4496,-661 4496,-758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4496,-661 4496,-758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f6a8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3892,-173 3892,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_234c1d0@0" Pin0InfoVect0LinkObjId="g_234c1d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3892,-173 3892,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_24d04c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3892,-244 3892,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_234c1d0@1" ObjectIDZND0="8051@x" ObjectIDZND1="8052@x" ObjectIDZND2="g_1fc30c0@0" Pin0InfoVect0LinkObjId="SW-46205_0" Pin0InfoVect1LinkObjId="SW-46206_0" Pin0InfoVect2LinkObjId="g_1fc30c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_234c1d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3892,-244 3892,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2781990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-168 4432,-185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_250bb90@0" Pin0InfoVect0LinkObjId="g_250bb90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-168 4432,-185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1fa3e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-238 4432,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="g_250bb90@1" ObjectIDZND0="g_23c1f10@0" ObjectIDZND1="8056@x" ObjectIDZND2="8058@x" Pin0InfoVect0LinkObjId="g_23c1f10_0" Pin0InfoVect1LinkObjId="SW-46210_0" Pin0InfoVect2LinkObjId="SW-46212_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_250bb90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-238 4432,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cd42e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4309,-839 4309,-815 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_24e0010@0" ObjectIDND1="8046@x" ObjectIDND2="8047@x" ObjectIDZND0="g_1ea4060@1" Pin0InfoVect0LinkObjId="g_1ea4060_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_24e0010_0" Pin1InfoVect1LinkObjId="SW-46200_0" Pin1InfoVect2LinkObjId="SW-46201_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4309,-839 4309,-815 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2347720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4309,-784 4309,-761 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_1ea4060@0" ObjectIDZND0="g_238a2d0@0" Pin0InfoVect0LinkObjId="g_238a2d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ea4060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4309,-784 4309,-761 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2508750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4696,-549 4696,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="8053@x" ObjectIDND1="8054@x" ObjectIDND2="g_1f8b180@0" ObjectIDZND0="g_2386b00@0" Pin0InfoVect0LinkObjId="g_2386b00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-46207_0" Pin1InfoVect1LinkObjId="SW-46208_0" Pin1InfoVect2LinkObjId="g_1f8b180_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4696,-549 4696,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1e02640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4696,-604 4696,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2386b00@1" ObjectIDZND0="g_1c78f20@0" Pin0InfoVect0LinkObjId="g_1c78f20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2386b00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4696,-604 4696,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_20011e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4053,-188 4053,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2344d10@0" ObjectIDND1="8052@x" ObjectIDZND0="g_23c1710@1" Pin0InfoVect0LinkObjId="g_23c1710_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2344d10_0" Pin1InfoVect1LinkObjId="SW-46206_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4053,-188 4053,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c74ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4053,-130 4053,-99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_23c1710@0" ObjectIDZND0="g_2337120@0" Pin0InfoVect0LinkObjId="g_2337120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23c1710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4053,-130 4053,-99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f818c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4514,-92 4514,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2512560@0" ObjectIDZND0="g_261bc50@0" Pin0InfoVect0LinkObjId="g_261bc50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2512560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4514,-92 4514,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2378a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4514,-158 4514,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_261bc50@1" ObjectIDZND0="8057@1" Pin0InfoVect0LinkObjId="SW-46211_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_261bc50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4514,-158 4514,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2781bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4594,-181 4594,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_23e3390@0" ObjectIDND1="8058@x" ObjectIDZND0="g_1c85da0@1" Pin0InfoVect0LinkObjId="g_1c85da0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_23e3390_0" Pin1InfoVect1LinkObjId="SW-46212_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4594,-181 4594,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_23706e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4594,-127 4594,-92 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1c85da0@0" ObjectIDZND0="g_23c02b0@0" Pin0InfoVect0LinkObjId="g_23c02b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c85da0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4594,-127 4594,-92 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f5fc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4641,-110 4641,-118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_23e3390@0" Pin0InfoVect0LinkObjId="g_23e3390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4641,-110 4641,-118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c5f460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4641,-172 4641,-181 4594,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_23e3390@1" ObjectIDZND0="g_1c85da0@0" ObjectIDZND1="8058@x" Pin0InfoVect0LinkObjId="g_1c85da0_0" Pin0InfoVect1LinkObjId="SW-46212_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23e3390_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4641,-172 4641,-181 4594,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_24ca730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4100,-113 4100,-124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2344d10@0" Pin0InfoVect0LinkObjId="g_2344d10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4100,-113 4100,-124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2345b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4100,-180 4100,-188 4053,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2344d10@1" ObjectIDZND0="g_23c1710@0" ObjectIDZND1="8052@x" Pin0InfoVect0LinkObjId="g_23c1710_0" Pin0InfoVect1LinkObjId="SW-46206_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2344d10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4100,-180 4100,-188 4053,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f5f330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3973,-99 3973,-129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_1c5d340@0" ObjectIDZND0="g_200a920@0" Pin0InfoVect0LinkObjId="g_200a920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c5d340_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3973,-99 3973,-129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_238a9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3973,-160 3973,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_200a920@1" ObjectIDZND0="8051@1" Pin0InfoVect0LinkObjId="SW-46205_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_200a920_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3973,-160 3973,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1cd3990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4130,-569 4130,-556 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="10848@0" ObjectIDZND0="g_264a420@1" Pin0InfoVect0LinkObjId="g_264a420_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2818ed0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4130,-569 4130,-556 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_23a0600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4130,-503 4130,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_264a420@0" ObjectIDZND0="8048@0" Pin0InfoVect0LinkObjId="SW-46202_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_264a420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4130,-503 4130,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1fe4980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3892,-291 3892,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1fc30c0@0" ObjectIDND1="8050@x" ObjectIDZND0="8051@x" ObjectIDZND1="8052@x" ObjectIDZND2="g_234c1d0@0" Pin0InfoVect0LinkObjId="SW-46205_0" Pin0InfoVect1LinkObjId="SW-46206_0" Pin0InfoVect2LinkObjId="g_234c1d0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1fc30c0_0" Pin1InfoVect1LinkObjId="SW-46204_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3892,-291 3892,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2381810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3892,-326 3892,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="8050@0" ObjectIDZND0="g_1fc30c0@0" ObjectIDZND1="8051@x" ObjectIDZND2="8052@x" Pin0InfoVect0LinkObjId="g_1fc30c0_0" Pin0InfoVect1LinkObjId="SW-46205_0" Pin0InfoVect2LinkObjId="SW-46206_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46204_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3892,-326 3892,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1d4ae60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3892,-413 3892,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8049@0" ObjectIDZND0="10463@0" Pin0InfoVect0LinkObjId="g_27858f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46203_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3892,-413 3892,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_27858f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4432,-413 4432,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8055@0" ObjectIDZND0="10463@0" Pin0InfoVect0LinkObjId="g_1d4ae60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46209_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4432,-413 4432,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1da2aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4130,-451 4130,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8048@1" ObjectIDZND0="10463@0" Pin0InfoVect0LinkObjId="g_1d4ae60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46202_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4130,-451 4130,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_277bcd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3973,-235 3973,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="8051@0" ObjectIDZND0="g_1fc30c0@0" ObjectIDZND1="8050@x" ObjectIDZND2="g_234c1d0@0" Pin0InfoVect0LinkObjId="g_1fc30c0_0" Pin0InfoVect1LinkObjId="SW-46204_0" Pin0InfoVect2LinkObjId="g_234c1d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46205_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3973,-235 3973,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2508bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3973,-260 4053,-260 4053,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_1fc30c0@0" ObjectIDND1="8050@x" ObjectIDND2="g_234c1d0@0" ObjectIDZND0="8052@0" Pin0InfoVect0LinkObjId="SW-46206_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1fc30c0_0" Pin1InfoVect1LinkObjId="SW-46204_0" Pin1InfoVect2LinkObjId="g_234c1d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3973,-260 4053,-260 4053,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c553f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4514,-228 4514,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="8057@0" ObjectIDZND0="g_23c1f10@0" ObjectIDZND1="8056@x" ObjectIDZND2="g_250bb90@0" Pin0InfoVect0LinkObjId="g_23c1f10_0" Pin0InfoVect1LinkObjId="SW-46210_0" Pin0InfoVect2LinkObjId="g_250bb90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46211_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4514,-228 4514,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_277f7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4514,-255 4594,-255 4594,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_23c1f10@0" ObjectIDND1="8056@x" ObjectIDND2="g_250bb90@0" ObjectIDZND0="8058@0" Pin0InfoVect0LinkObjId="SW-46212_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_23c1f10_0" Pin1InfoVect1LinkObjId="SW-46210_0" Pin1InfoVect2LinkObjId="g_250bb90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4514,-255 4594,-255 4594,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ccaee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4309,-934 4166,-934 4132,-934 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" ObjectIDND0="g_1e94e10@0" ObjectIDND1="8046@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1e94e10_0" Pin1InfoVect1LinkObjId="SW-46200_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4309,-934 4166,-934 4132,-934 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23ca130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4131,-837 4131,-909 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="powerLine" ObjectIDND0="8044@x" ObjectIDND1="8043@x" ObjectIDZND0="47298@1" Pin0InfoVect0LinkObjId="g_20137f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46198_0" Pin1InfoVect1LinkObjId="SW-46197_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4131,-837 4131,-909 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20137f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4131,-819 4131,-837 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" EndDevType1="switch" ObjectIDND0="8043@1" ObjectIDZND0="47298@1" ObjectIDZND1="8044@x" Pin0InfoVect0LinkObjId="g_23ca130_1" Pin0InfoVect1LinkObjId="SW-46198_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46197_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4131,-819 4131,-837 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23ef190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4131,-837 4153,-837 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="47298@1" ObjectIDND1="8043@x" ObjectIDZND0="8044@0" Pin0InfoVect0LinkObjId="SW-46198_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_23ca130_1" Pin1InfoVect1LinkObjId="SW-46197_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4131,-837 4153,-837 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2818ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4131,-729 4131,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="8042@0" ObjectIDZND0="48667@0" Pin0InfoVect0LinkObjId="g_1f77620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46196_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4131,-729 4131,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2818ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4131,-661 4131,-649 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="48666@0" ObjectIDZND0="10848@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315171_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4131,-661 4131,-649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_280f080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-775 4153,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="8042@x" ObjectIDND1="8043@x" ObjectIDZND0="8045@0" Pin0InfoVect0LinkObjId="SW-46199_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46196_0" Pin1InfoVect1LinkObjId="SW-46197_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-775 4153,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26f8560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-757 4132,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="8042@1" ObjectIDZND0="8043@x" ObjectIDZND1="8045@x" Pin0InfoVect0LinkObjId="SW-46197_0" Pin0InfoVect1LinkObjId="SW-46199_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-46196_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-757 4132,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2689910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-775 4131,-781 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="8042@x" ObjectIDND1="8045@x" ObjectIDZND0="8043@0" Pin0InfoVect0LinkObjId="SW-46197_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-46196_0" Pin1InfoVect1LinkObjId="SW-46199_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-775 4131,-781 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f77620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4131,-697 4131,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48666@1" ObjectIDZND0="48667@0" Pin0InfoVect0LinkObjId="g_2818ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315171_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4131,-697 4131,-712 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="10463" cx="4696" cy="-437" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10463" cx="4432" cy="-437" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10463" cx="3892" cy="-437" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10463" cx="4130" cy="-437" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48667" cx="4131" cy="-712" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48667" cx="4131" cy="-712" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-37314" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3422.000000 -1088.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5896" ObjectName="DYN-CX_BGHSJ"/>
     <cge:Meas_Ref ObjectId="37314"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23c8f10" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3865.000000 -106.000000) translate(0,15)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2377ea0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4105.000000 -997.000000) translate(0,15)">不</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2377ea0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4105.000000 -997.000000) translate(0,33)">管</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2377ea0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4105.000000 -997.000000) translate(0,51)">河</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2377ea0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4105.000000 -997.000000) translate(0,69)">四</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2377ea0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4105.000000 -997.000000) translate(0,87)">级</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2377ea0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4105.000000 -997.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c773e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4073.000000 -62.000000) translate(0,15)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fd80e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3790.000000 -428.000000) translate(0,15)">6kV段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1cd3e50" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4455.000000 -655.000000) translate(0,15)">1号厂用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2369b30" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4328.000000 -781.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1cd6fe0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4390.000000 -104.000000) translate(0,15)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c7f3a0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4614.000000 -60.000000) translate(0,15)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1cc3340" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4614.000000 -669.000000) translate(0,15)">6kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26147d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4091.000000 -749.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fbb7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4084.000000 -808.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fd0d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4146.000000 -801.000000) translate(0,12)">36160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23943c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4146.000000 -863.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c81570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4264.000000 -890.000000) translate(0,12)">3619</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fce360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4326.000000 -877.000000) translate(0,12)">36197</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23c18e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4167.000000 -618.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ccfe80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4137.000000 -497.000000) translate(0,12)">6011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26395d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3901.000000 -347.000000) translate(0,12)">661</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c812d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3899.000000 -402.000000) translate(0,12)">6611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23a2c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4060.000000 -224.000000) translate(0,12)">6618</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f8d4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3980.000000 -224.000000) translate(0,12)">6619</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cfafb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4441.000000 -347.000000) translate(0,12)">662</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d65670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4521.000000 -217.000000) translate(0,12)">6629</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d9c990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4601.000000 -217.000000) translate(0,12)">6628</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2341c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4439.000000 -402.000000) translate(0,12)">6621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23415f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4703.000000 -484.000000) translate(0,12)">6901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb8350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4713.000000 -543.000000) translate(0,12)">69017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f6ab00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -578.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f6ab00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -578.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f6ab00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -578.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f6ab00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -578.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f6ab00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -578.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f6ab00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -578.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f6ab00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -578.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f6ab00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -578.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f6ab00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -578.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f6ab00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -578.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f6ab00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -578.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f6ab00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -578.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f6ab00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -578.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f6ab00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -578.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f6ab00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -578.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f6ab00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -578.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f6ab00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -578.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f6ab00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -578.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cca0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1057.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cca0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1057.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cca0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1057.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cca0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1057.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cca0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1057.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cca0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1057.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1cca0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -1057.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_24d4170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3266.000000 -1168.500000) translate(0,16)">不管河四级站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fb9db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3944.000000 -651.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fb9db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3944.000000 -651.000000) translate(0,33)">SF11-8000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fb9db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3944.000000 -651.000000) translate(0,51)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fb9db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3944.000000 -651.000000) translate(0,69)">38.5±2х2.5%/6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2009cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3754.000000 -217.000000) translate(0,15)">1号发电机参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2009cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3754.000000 -217.000000) translate(0,33)">SFW3000-6/1730</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2009cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3754.000000 -217.000000) translate(0,51)">Pe=3000kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2009cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3754.000000 -217.000000) translate(0,69)">Ue=6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2009cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3754.000000 -217.000000) translate(0,87)">Ie=344A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2009cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3754.000000 -217.000000) translate(0,105)">COS=0.8</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db26d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4283.000000 -216.000000) translate(0,15)">2号发电机参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db26d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4283.000000 -216.000000) translate(0,33)">SFW3000-6/1730</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db26d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4283.000000 -216.000000) translate(0,51)">Pe=3000kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db26d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4283.000000 -216.000000) translate(0,69)">Ue=6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db26d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4283.000000 -216.000000) translate(0,87)">Ie=344A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1db26d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4283.000000 -216.000000) translate(0,105)">COS=0.8</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fb0a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4137.000000 -476.000000) translate(0,12)">6011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e62620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3259.000000 -224.000000) translate(0,12)">7822010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23e7ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3259.000000 -198.000000) translate(0,12)">13368781192</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2395b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3243.000000 -172.000000) translate(0,12)">黄磊 17387874970</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-46196">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4122.000000 -721.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8042" ObjectName="SW-CX_BGHSJ.CX_BGHSJ_361BK"/>
     <cge:Meas_Ref ObjectId="46196"/>
    <cge:TPSR_Ref TObjectID="8042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46204">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3883.000000 -318.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8050" ObjectName="SW-CX_BGHSJ.CX_BGHSJ_661BK"/>
     <cge:Meas_Ref ObjectId="46204"/>
    <cge:TPSR_Ref TObjectID="8050"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-46210">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4423.000000 -318.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8056" ObjectName="SW-CX_BGHSJ.CX_BGHSJ_662BK"/>
     <cge:Meas_Ref ObjectId="46210"/>
    <cge:TPSR_Ref TObjectID="8056"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_BGHSJ" endPointId="0" endStationName="CX_BGSJ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_buGuanHe4" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4131,-1026 4131,-907 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="47298" ObjectName="AC-35kV.LN_buGuanHe4"/>
    <cge:TPSR_Ref TObjectID="47298_SS-45"/></metadata>
   <polyline fill="none" opacity="0" points="4131,-1026 4131,-907 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2337120">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4039.000000 -75.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e94e10">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4502.000000 -906.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23c02b0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4580.000000 -68.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24e0010">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4268.000000 -749.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f8b180">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4654.000000 -575.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_234c1d0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3887.000000 -185.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_250bb90">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4427.000000 -180.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ea4060">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4300.000000 -779.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2386b00">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4687.000000 -568.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_200a920">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3964.000000 -124.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23c1710">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4044.000000 -125.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_261bc50">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4505.000000 -122.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c85da0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4585.000000 -122.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23e3390">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4636.000000 -113.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2344d10">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4095.000000 -119.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fc30c0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3922.000000 -283.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23c1f10">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4462.000000 -295.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_264a420">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4125.000000 -498.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-46176" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4217.000000 -744.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46176" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8042"/>
     <cge:Term_Ref ObjectID="11507"/>
    <cge:TPSR_Ref TObjectID="8042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46177" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4217.000000 -744.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46177" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8042"/>
     <cge:Term_Ref ObjectID="11507"/>
    <cge:TPSR_Ref TObjectID="8042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-56847" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4217.000000 -744.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56847" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8042"/>
     <cge:Term_Ref ObjectID="11507"/>
    <cge:TPSR_Ref TObjectID="8042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-46183" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4012.000000 -366.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46183" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8050"/>
     <cge:Term_Ref ObjectID="11523"/>
    <cge:TPSR_Ref TObjectID="8050"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46184" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4012.000000 -366.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46184" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8050"/>
     <cge:Term_Ref ObjectID="11523"/>
    <cge:TPSR_Ref TObjectID="8050"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46182" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4012.000000 -366.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46182" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8050"/>
     <cge:Term_Ref ObjectID="11523"/>
    <cge:TPSR_Ref TObjectID="8050"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-46188" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4559.000000 -366.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46188" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8056"/>
     <cge:Term_Ref ObjectID="11535"/>
    <cge:TPSR_Ref TObjectID="8056"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-46185" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4559.000000 -366.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46185" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8056"/>
     <cge:Term_Ref ObjectID="11535"/>
    <cge:TPSR_Ref TObjectID="8056"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-46187" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4559.000000 -366.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="46187" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8056"/>
     <cge:Term_Ref ObjectID="11535"/>
    <cge:TPSR_Ref TObjectID="8056"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3246" y="-1179"/></g>
   <g href="cx_索引_接线图_地调直调.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3197" y="-1196"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24fbae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3954.000000 367.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c859b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3943.000000 352.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ea3130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3968.000000 337.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fd13f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4499.000000 367.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25070d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4488.000000 352.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f88890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4513.000000 337.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2380780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4161.000000 722.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a78a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4150.000000 707.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c10a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4175.000000 692.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2365630" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4193.000000 -828.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23bf9f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4198.000000 -766.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2779760" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4373.000000 -845.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_238ba80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4759.000000 -508.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3246" y="-1179"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3246" y="-1179"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3197" y="-1196"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3197" y="-1196"/></g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_238a2d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4294.000000 -739.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c5d340">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3958.000000 -77.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2512560">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4499.000000 -70.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c78f20">
    <use class="BV-6KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4711.000000 -648.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3234.000000 -1120.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116494" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3240.538462 -1012.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116494" ObjectName="CX_BGHSJ:CX_BGHSJ_ZJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116495" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3239.538462 -972.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116495" ObjectName="CX_BGHSJ:CX_BGHSJ_ZJ_sumQ"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_BGHSJ"/>
</svg>