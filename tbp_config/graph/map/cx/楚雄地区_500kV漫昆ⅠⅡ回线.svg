<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" LineID="楚雄地区_500kV漫昆ⅠⅡ回线.svg" MapType="line" StationID="SS-118" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="2811 -1925 1643 946">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18df040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18dfa80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_18e03e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_18e1530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_18e2850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_18e3410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18e3ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18e4950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18e52e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18e5c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18e67d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_18e70b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18e8c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18e98d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18ea0f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_18eaae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18ec010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18ecd10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_148d360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_18ee310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18ef4c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18efe40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18f0930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_153ec30" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_153fb00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_15403a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_18f6060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18f6e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_18fc4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_147ee20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="956" width="1653" x="2806" y="-1930"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-196127">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3717.000000 -1493.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29854" ObjectName="SW-CX_TASE2JS.YN_MKⅡ_5031BK"/>
     <cge:Meas_Ref ObjectId="196127"/>
    <cge:TPSR_Ref TObjectID="29854"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196128">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3717.000000 -1297.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29855" ObjectName="SW-CX_TASE2JS.YN_MKⅡ_5032BK"/>
     <cge:Meas_Ref ObjectId="196128"/>
    <cge:TPSR_Ref TObjectID="29855"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213950">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3717.000000 -1106.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31855" ObjectName="SW-CX_TASE2JS.YN_MKⅡ_5033BK"/>
     <cge:Meas_Ref ObjectId="213950"/>
    <cge:TPSR_Ref TObjectID="31855"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213929">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3476.000000 -1496.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31834" ObjectName="SW-CX_TASE2JS.YN_MKⅠ_5021BK"/>
     <cge:Meas_Ref ObjectId="213929"/>
    <cge:TPSR_Ref TObjectID="31834"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196125">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3476.000000 -1300.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29850" ObjectName="SW-CX_TASE2JS.YN_MKⅠ_5022BK"/>
     <cge:Meas_Ref ObjectId="196125"/>
    <cge:TPSR_Ref TObjectID="29850"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196126">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3476.000000 -1109.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29851" ObjectName="SW-CX_TASE2JS.YN_MKⅠ_5023BK"/>
     <cge:Meas_Ref ObjectId="196126"/>
    <cge:TPSR_Ref TObjectID="29851"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66379">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4336.000000 -1497.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20240" ObjectName="SW-CX_TASE2JS.YN_MKⅠ_5031BK"/>
     <cge:Meas_Ref ObjectId="66379"/>
    <cge:TPSR_Ref TObjectID="20240"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66380">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4336.000000 -1301.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20241" ObjectName="SW-CX_TASE2JS.YN_MKⅠ_5032BK"/>
     <cge:Meas_Ref ObjectId="66380"/>
    <cge:TPSR_Ref TObjectID="20241"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66381">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4336.000000 -1110.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20242" ObjectName="SW-CX_TASE2JS.YN_MKⅠ_5033BK"/>
     <cge:Meas_Ref ObjectId="66381"/>
    <cge:TPSR_Ref TObjectID="20242"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66388">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4095.000000 -1500.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20258" ObjectName="SW-CX_TASE2JS.YN_MKⅡ_5051BK"/>
     <cge:Meas_Ref ObjectId="66388"/>
    <cge:TPSR_Ref TObjectID="20258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66389">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4095.000000 -1304.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20259" ObjectName="SW-CX_TASE2JS.YN_MKⅡ_5052BK"/>
     <cge:Meas_Ref ObjectId="66389"/>
    <cge:TPSR_Ref TObjectID="20259"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66390">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4095.000000 -1113.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20260" ObjectName="SW-CX_TASE2JS.YN_MKⅡ_5053BK"/>
     <cge:Meas_Ref ObjectId="66390"/>
    <cge:TPSR_Ref TObjectID="20260"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_TASE2JS.CX_TASE2JS_CP_5ⅠM">
    <g class="BV-500KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3407,-1612 3840,-1612 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="31794" ObjectName="BS-CX_TASE2JS.CX_TASE2JS_CP_5ⅠM"/>
    <cge:TPSR_Ref TObjectID="31794"/></metadata>
   <polyline fill="none" opacity="0" points="3407,-1612 3840,-1612 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_TASE2JS.CX_TASE2JS_CP_5ⅡM">
    <g class="BV-500KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3398,-1017 3857,-1017 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="31797" ObjectName="BS-CX_TASE2JS.CX_TASE2JS_CP_5ⅡM"/>
    <cge:TPSR_Ref TObjectID="31797"/></metadata>
   <polyline fill="none" opacity="0" points="3398,-1017 3857,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_TASE2JS.CX_TASE2JS_MW_5ⅡM">
    <g class="BV-500KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4013,-1021 4453,-1021 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="31796" ObjectName="BS-CX_TASE2JS.CX_TASE2JS_MW_5ⅡM"/>
    <cge:TPSR_Ref TObjectID="31796"/></metadata>
   <polyline fill="none" opacity="0" points="4013,-1021 4453,-1021 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_TASE2JS.CX_TASE2JS_MW_5ⅠM">
    <g class="BV-500KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4020,-1616 4454,-1616 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="31795" ObjectName="BS-CX_TASE2JS.CX_TASE2JS_MW_5ⅠM"/>
    <cge:TPSR_Ref TObjectID="31795"/></metadata>
   <polyline fill="none" opacity="0" points="4020,-1616 4454,-1616 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Link_Layer">
   <g class="BV-500KV" id="g_18ede50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3726,-1612 3726,-1590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="31794@0" ObjectIDZND0="31857@1" Pin0InfoVect0LinkObjId="SW-213960_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3726,-1612 3726,-1590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_15baee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3726,-1044 3726,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31863@0" ObjectIDZND0="31797@0" Pin0InfoVect0LinkObjId="g_1472bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213966_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3726,-1044 3726,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_15bb140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3726,-1235 3726,-1203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="31860@0" ObjectIDZND0="31856@1" Pin0InfoVect0LinkObjId="SW-213959_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213962_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3726,-1235 3726,-1203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1696230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3485,-1612 3485,-1593 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="31794@0" ObjectIDZND0="31835@1" Pin0InfoVect0LinkObjId="SW-213951_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3485,-1612 3485,-1593 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1472bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3485,-1047 3485,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31838@0" ObjectIDZND0="31797@0" Pin0InfoVect0LinkObjId="g_15baee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213955_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3485,-1047 3485,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a1a4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3318,-1227 3285,-1227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31840@0" ObjectIDZND0="g_1a19a50@0" Pin0InfoVect0LinkObjId="g_1a19a50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213956_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3318,-1227 3285,-1227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a1a740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3485,-1557 3485,-1531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31835@0" ObjectIDZND0="31834@1" Pin0InfoVect0LinkObjId="SW-213929_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213951_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3485,-1557 3485,-1531 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a1a9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3485,-1361 3485,-1335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31836@0" ObjectIDZND0="29850@1" Pin0InfoVect0LinkObjId="SW-196125_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213952_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3485,-1361 3485,-1335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a1ac00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3485,-1308 3485,-1274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29850@0" ObjectIDZND0="31841@1" Pin0InfoVect0LinkObjId="SW-213957_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196125_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3485,-1308 3485,-1274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a1ae60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3485,-1144 3485,-1170 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29851@1" ObjectIDZND0="31837@0" Pin0InfoVect0LinkObjId="SW-213954_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196126_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3485,-1144 3485,-1170 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a1b0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3485,-1083 3485,-1117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31838@1" ObjectIDZND0="29851@0" Pin0InfoVect0LinkObjId="SW-196126_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213955_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3485,-1083 3485,-1117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a1b320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3726,-1554 3726,-1528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31857@0" ObjectIDZND0="29854@1" Pin0InfoVect0LinkObjId="SW-196127_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3726,-1554 3726,-1528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a1b580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3726,-1501 3726,-1480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29854@0" ObjectIDZND0="31862@1" Pin0InfoVect0LinkObjId="SW-213965_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196127_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3726,-1501 3726,-1480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a1b7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3726,-1358 3726,-1332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31859@0" ObjectIDZND0="29855@1" Pin0InfoVect0LinkObjId="SW-196128_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213961_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3726,-1358 3726,-1332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a1ba40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3726,-1305 3726,-1271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29855@0" ObjectIDZND0="31860@1" Pin0InfoVect0LinkObjId="SW-213962_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196128_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3726,-1305 3726,-1271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a1bca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3726,-1141 3726,-1167 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="31855@1" ObjectIDZND0="31856@0" Pin0InfoVect0LinkObjId="SW-213959_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213950_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3726,-1141 3726,-1167 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a1bf00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3726,-1080 3726,-1114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31863@1" ObjectIDZND0="31855@0" Pin0InfoVect0LinkObjId="SW-213950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213966_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3726,-1080 3726,-1114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a1e6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3485,-1227 3448,-1227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="31841@x" ObjectIDND1="31837@x" ObjectIDZND0="31839@1" Pin0InfoVect0LinkObjId="SW-213953_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-213957_0" Pin1InfoVect1LinkObjId="SW-213954_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3485,-1227 3448,-1227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a1e900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3380,-1227 3353,-1227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31839@x" ObjectIDND1="31843@x" ObjectIDND2="31844@x" ObjectIDZND0="31840@1" Pin0InfoVect0LinkObjId="SW-213956_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-213953_0" Pin1InfoVect1LinkObjId="SW-213930_0" Pin1InfoVect2LinkObjId="SW-213931_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3380,-1227 3353,-1227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a1f3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3412,-1227 3380,-1227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31839@0" ObjectIDZND0="31840@x" ObjectIDZND1="31843@x" ObjectIDZND2="31844@x" Pin0InfoVect0LinkObjId="SW-213956_0" Pin0InfoVect1LinkObjId="SW-213930_0" Pin0InfoVect2LinkObjId="SW-213931_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213953_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3412,-1227 3380,-1227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a1fee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3485,-1238 3485,-1227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="31841@0" ObjectIDZND0="31839@x" ObjectIDZND1="31837@x" Pin0InfoVect0LinkObjId="SW-213953_0" Pin0InfoVect1LinkObjId="SW-213954_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213957_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3485,-1238 3485,-1227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a20140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3485,-1227 3485,-1206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="31839@x" ObjectIDND1="31841@x" ObjectIDZND0="31837@1" Pin0InfoVect0LinkObjId="SW-213954_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-213953_0" Pin1InfoVect1LinkObjId="SW-213957_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3485,-1227 3485,-1206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a20c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3726,-1444 3726,-1412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="31862@0" ObjectIDZND0="31859@x" ObjectIDZND1="31858@x" Pin0InfoVect0LinkObjId="SW-213961_0" Pin0InfoVect1LinkObjId="SW-213963_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213965_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3726,-1444 3726,-1412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a20e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3726,-1412 3726,-1394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="31858@x" ObjectIDND1="31862@x" ObjectIDZND0="31859@1" Pin0InfoVect0LinkObjId="SW-213961_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-213963_0" Pin1InfoVect1LinkObjId="SW-213965_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3726,-1412 3726,-1394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a21320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4345,-1616 4345,-1594 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="31795@0" ObjectIDZND0="20243@1" Pin0InfoVect0LinkObjId="SW-66382_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4345,-1616 4345,-1594 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18b6610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4345,-1048 4345,-1021 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20248@0" ObjectIDZND0="31796@0" Pin0InfoVect0LinkObjId="g_18ce920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66387_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4345,-1048 4345,-1021 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18b6870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4345,-1239 4345,-1207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="20246@0" ObjectIDZND0="20247@1" Pin0InfoVect0LinkObjId="SW-66386_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66385_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4345,-1239 4345,-1207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18bbaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4104,-1616 4104,-1597 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="31795@0" ObjectIDZND0="20261@1" Pin0InfoVect0LinkObjId="SW-66391_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4104,-1616 4104,-1597 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18c7640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4104,-1452 4104,-1401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="20262@0" ObjectIDZND0="20263@1" Pin0InfoVect0LinkObjId="SW-66393_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66392_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4104,-1452 4104,-1401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18ce920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4104,-1051 4104,-1021 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="20266@0" ObjectIDZND0="31796@0" Pin0InfoVect0LinkObjId="g_18b6610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66396_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4104,-1051 4104,-1021 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d2940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3937,-1231 3904,-1231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31865@0" ObjectIDZND0="g_18d1eb0@0" Pin0InfoVect0LinkObjId="g_18d1eb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213933_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3937,-1231 3904,-1231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d2ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4104,-1561 4104,-1535 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20261@0" ObjectIDZND0="20258@1" Pin0InfoVect0LinkObjId="SW-66388_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66391_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4104,-1561 4104,-1535 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d2e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4104,-1508 4104,-1488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20258@0" ObjectIDZND0="20262@1" Pin0InfoVect0LinkObjId="SW-66392_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66388_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4104,-1508 4104,-1488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d3060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4104,-1365 4104,-1339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20263@0" ObjectIDZND0="20259@1" Pin0InfoVect0LinkObjId="SW-66389_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66393_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4104,-1365 4104,-1339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d32c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4104,-1312 4104,-1278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20259@0" ObjectIDZND0="20264@1" Pin0InfoVect0LinkObjId="SW-66394_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66389_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4104,-1312 4104,-1278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d3520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4104,-1148 4104,-1174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20260@1" ObjectIDZND0="20265@0" Pin0InfoVect0LinkObjId="SW-66395_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66390_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4104,-1148 4104,-1174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d3780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4104,-1087 4104,-1121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20266@1" ObjectIDZND0="20260@0" Pin0InfoVect0LinkObjId="SW-66390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66396_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4104,-1087 4104,-1121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d39e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4345,-1558 4345,-1532 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20243@0" ObjectIDZND0="20240@1" Pin0InfoVect0LinkObjId="SW-66379_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66382_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4345,-1558 4345,-1532 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d3c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4345,-1505 4345,-1471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20240@0" ObjectIDZND0="20244@1" Pin0InfoVect0LinkObjId="SW-66383_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66379_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4345,-1505 4345,-1471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d3ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4345,-1362 4345,-1336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20245@0" ObjectIDZND0="20241@1" Pin0InfoVect0LinkObjId="SW-66380_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66384_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4345,-1362 4345,-1336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d4100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4345,-1309 4345,-1275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20241@0" ObjectIDZND0="20246@1" Pin0InfoVect0LinkObjId="SW-66385_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4345,-1309 4345,-1275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d4360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4345,-1145 4345,-1171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="20242@1" ObjectIDZND0="20247@0" Pin0InfoVect0LinkObjId="SW-66386_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66381_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4345,-1145 4345,-1171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d45c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4345,-1084 4345,-1118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="20248@1" ObjectIDZND0="20242@0" Pin0InfoVect0LinkObjId="SW-66381_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66387_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4345,-1084 4345,-1118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d6d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4104,-1231 4067,-1231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20264@x" ObjectIDND1="20265@x" ObjectIDZND0="31864@1" Pin0InfoVect0LinkObjId="SW-213932_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-66394_0" Pin1InfoVect1LinkObjId="SW-66395_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4104,-1231 4067,-1231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d6fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3999,-1231 3972,-1231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31864@x" ObjectIDND1="31861@x" ObjectIDND2="31858@x" ObjectIDZND0="31865@1" Pin0InfoVect0LinkObjId="SW-213933_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-213932_0" Pin1InfoVect1LinkObjId="SW-213964_0" Pin1InfoVect2LinkObjId="SW-213963_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3999,-1231 3972,-1231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d7220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4031,-1231 3999,-1231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31864@0" ObjectIDZND0="31865@x" ObjectIDZND1="31861@x" ObjectIDZND2="31858@x" Pin0InfoVect0LinkObjId="SW-213933_0" Pin0InfoVect1LinkObjId="SW-213964_0" Pin0InfoVect2LinkObjId="SW-213963_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213932_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4031,-1231 3999,-1231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d7480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4104,-1242 4104,-1231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20264@0" ObjectIDZND0="31864@x" ObjectIDZND1="20265@x" Pin0InfoVect0LinkObjId="SW-213932_0" Pin0InfoVect1LinkObjId="SW-66395_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66394_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4104,-1242 4104,-1231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d76e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4104,-1231 4104,-1210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20264@x" ObjectIDND1="31864@x" ObjectIDZND0="20265@1" Pin0InfoVect0LinkObjId="SW-66395_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-66394_0" Pin1InfoVect1LinkObjId="SW-213932_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4104,-1231 4104,-1210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18d9e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4345,-1417 4327,-1417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20244@x" ObjectIDND1="20245@x" ObjectIDZND0="31843@1" Pin0InfoVect0LinkObjId="SW-213930_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-66383_0" Pin1InfoVect1LinkObjId="SW-66384_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4345,-1417 4327,-1417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18da0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4345,-1435 4345,-1416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="20244@0" ObjectIDZND0="31843@x" ObjectIDZND1="20245@x" Pin0InfoVect0LinkObjId="SW-213930_0" Pin0InfoVect1LinkObjId="SW-66384_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-66383_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4345,-1435 4345,-1416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_18da340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4345,-1416 4345,-1398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="20244@x" ObjectIDND1="31843@x" ObjectIDZND0="20245@1" Pin0InfoVect0LinkObjId="SW-66384_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-66383_0" Pin1InfoVect1LinkObjId="SW-213930_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4345,-1416 4345,-1398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a25510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4209,-1417 4176,-1417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31844@0" ObjectIDZND0="g_18dcae0@0" Pin0InfoVect0LinkObjId="g_18dcae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213931_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4209,-1417 4176,-1417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a25770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-1417 4244,-1417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31843@x" ObjectIDND1="31840@x" ObjectIDND2="31839@x" ObjectIDZND0="31844@1" Pin0InfoVect0LinkObjId="SW-213931_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-213930_0" Pin1InfoVect1LinkObjId="SW-213956_0" Pin1InfoVect2LinkObjId="SW-213953_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-1417 4244,-1417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a259d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4291,-1417 4271,-1417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31843@0" ObjectIDZND0="31844@x" ObjectIDZND1="31840@x" ObjectIDZND2="31839@x" Pin0InfoVect0LinkObjId="SW-213931_0" Pin0InfoVect1LinkObjId="SW-213956_0" Pin0InfoVect2LinkObjId="SW-213953_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4291,-1417 4271,-1417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a28020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3999,-1231 3999,-1663 3847,-1663 3847,-1413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="31864@x" ObjectIDND1="31865@x" ObjectIDZND0="31861@x" ObjectIDZND1="31858@x" Pin0InfoVect0LinkObjId="SW-213964_0" Pin0InfoVect1LinkObjId="SW-213963_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-213932_0" Pin1InfoVect1LinkObjId="SW-213933_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3999,-1231 3999,-1663 3847,-1663 3847,-1413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a28270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-1417 4271,-1742 3380,-1742 3380,-1227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="31843@x" ObjectIDND1="31844@x" ObjectIDZND0="31840@x" ObjectIDZND1="31839@x" Pin0InfoVect0LinkObjId="SW-213956_0" Pin0InfoVect1LinkObjId="SW-213953_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-213930_0" Pin1InfoVect1LinkObjId="SW-213931_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-1417 4271,-1742 3380,-1742 3380,-1227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a284c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3485,-1397 3485,-1449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="31836@1" ObjectIDZND0="31842@0" Pin0InfoVect0LinkObjId="SW-213958_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213952_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3485,-1397 3485,-1449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a28720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3485,-1485 3485,-1504 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="31842@1" ObjectIDZND0="31834@0" Pin0InfoVect0LinkObjId="SW-213929_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213958_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3485,-1485 3485,-1504 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a3bb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3847,-1413 3870,-1413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31864@x" ObjectIDND1="31865@x" ObjectIDND2="31858@x" ObjectIDZND0="31861@0" Pin0InfoVect0LinkObjId="SW-213964_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-213932_0" Pin1InfoVect1LinkObjId="SW-213933_0" Pin1InfoVect2LinkObjId="SW-213963_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3847,-1413 3870,-1413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a3bde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3906,-1413 3925,-1413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31861@1" ObjectIDZND0="g_1a3a860@0" Pin0InfoVect0LinkObjId="g_1a3a860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213964_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3906,-1413 3925,-1413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a3c040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3726,-1413 3770,-1413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="31859@x" ObjectIDND1="31862@x" ObjectIDZND0="31858@0" Pin0InfoVect0LinkObjId="SW-213963_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-213961_0" Pin1InfoVect1LinkObjId="SW-213965_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3726,-1413 3770,-1413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-500KV" id="g_1a3c2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3806,-1413 3847,-1413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31858@1" ObjectIDZND0="31864@x" ObjectIDZND1="31865@x" ObjectIDZND2="31861@x" Pin0InfoVect0LinkObjId="SW-213932_0" Pin0InfoVect1LinkObjId="SW-213933_0" Pin0InfoVect2LinkObjId="SW-213964_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-213963_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3806,-1413 3847,-1413 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="31794" cx="3726" cy="-1612" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31794" cx="3485" cy="-1612" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31797" cx="3726" cy="-1017" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31797" cx="3485" cy="-1017" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31795" cx="4345" cy="-1616" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31795" cx="4104" cy="-1616" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31796" cx="4345" cy="-1021" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31796" cx="4104" cy="-1021" fill="rgb(213,0,0)" r="4" stroke="rgb(213,0,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1472e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3401.000000 -1647.000000) translate(0,16)">500kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1473370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3827.000000 -1693.000000) translate(0,16)">漫昆Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_14ecec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3560.000000 -999.000000) translate(0,16)">500kV草铺变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_14ed110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3379.000000 -1049.000000) translate(0,16)">500kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimSun" font-size="30" graphid="g_1a164e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2982.500000 -1871.500000) translate(0,24)">漫昆Ⅰ回线、漫昆Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a16c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3739.000000 -1524.000000) translate(0,16)">5031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a16ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3735.000000 -1332.000000) translate(0,16)">5032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a17430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3494.000000 -1329.000000) translate(0,16)">5022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a17800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3497.000000 -1137.000000) translate(0,16)">5023</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18ceb80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4012.000000 -1645.000000) translate(0,16)">500kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18cf1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4179.000000 -1003.000000) translate(0,16)">500kV漫湾电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18cf790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3986.000000 -1052.000000) translate(0,16)">500kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_18cf9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3849.000000 -1770.000000) translate(0,16)">漫昆Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a28980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4352.000000 -1583.000000) translate(0,16)">50311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a28fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4354.000000 -1526.000000) translate(0,16)">5031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a291f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4352.000000 -1460.000000) translate(0,16)">50312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a29430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4352.000000 -1387.000000) translate(0,16)">50321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a29670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4354.000000 -1330.000000) translate(0,16)">5032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a298b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4352.000000 -1264.000000) translate(0,16)">50322</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a29af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4352.000000 -1196.000000) translate(0,16)">50331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a29d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4354.000000 -1139.000000) translate(0,16)">5033</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a29f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4352.000000 -1073.000000) translate(0,16)">50332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a2a1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4111.000000 -1586.000000) translate(0,16)">50511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a2a3f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4113.000000 -1529.000000) translate(0,16)">5051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a2a630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4111.000000 -1477.000000) translate(0,16)">50512</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a2a870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4111.000000 -1390.000000) translate(0,16)">50521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a2aab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4113.000000 -1333.000000) translate(0,16)">5052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a2acf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4111.000000 -1267.000000) translate(0,16)">50522</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a2af30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4111.000000 -1199.000000) translate(0,16)">50531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a2b170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4113.000000 -1142.000000) translate(0,16)">5053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a2b3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4111.000000 -1076.000000) translate(0,16)">50532</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a33510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3492.000000 -1582.000000) translate(0,16)">50211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a33a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3494.000000 -1525.000000) translate(0,16)">5021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a33c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3492.000000 -1474.000000) translate(0,16)">50212</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a33e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3492.000000 -1386.000000) translate(0,16)">50221</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a340c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3492.000000 -1263.000000) translate(0,16)">50222</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a34300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3400.000000 -1261.000000) translate(0,16)">50236</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a34680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3285.000000 -1259.000000) translate(0,16)">5023617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a34c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3492.000000 -1195.000000) translate(0,16)">50231</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a350d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3492.000000 -1072.000000) translate(0,16)">50232</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a35310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3733.000000 -1579.000000) translate(0,16)">50311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a35550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3733.000000 -1469.000000) translate(0,16)">50312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a35790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3733.000000 -1383.000000) translate(0,16)">50321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a359d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3733.000000 -1260.000000) translate(0,16)">50322</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a35c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3733.000000 -1192.000000) translate(0,16)">50331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a35e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3739.000000 -1135.000000) translate(0,16)">5033</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a36090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3733.000000 -1069.000000) translate(0,16)">50332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a3c500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3768.000000 -1439.000000) translate(0,16)">50316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a3cb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3861.000000 -1439.000000) translate(0,16)">5031617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a3cd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4022.000000 -1267.000000) translate(0,16)">50536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a3cfb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3910.000000 -1264.000000) translate(0,16)">5053617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a3d1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4278.000000 -1452.000000) translate(0,16)">50336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1a3d430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4174.000000 -1452.000000) translate(0,16)">5033617</text>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-213965">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3717.000000 -1439.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31862" ObjectName="SW-CX_TASE2JS.YN_MKⅡ_50312SW"/>
     <cge:Meas_Ref ObjectId="213965"/>
    <cge:TPSR_Ref TObjectID="31862"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213961">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3717.000000 -1353.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31859" ObjectName="SW-CX_TASE2JS.YN_MKⅡ_50321SW"/>
     <cge:Meas_Ref ObjectId="213961"/>
    <cge:TPSR_Ref TObjectID="31859"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213962">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3717.000000 -1230.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31860" ObjectName="SW-CX_TASE2JS.YN_MKⅡ_50322SW"/>
     <cge:Meas_Ref ObjectId="213962"/>
    <cge:TPSR_Ref TObjectID="31860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213966">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3717.000000 -1039.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31863" ObjectName="SW-CX_TASE2JS.YN_MKⅡ_50332SW"/>
     <cge:Meas_Ref ObjectId="213966"/>
    <cge:TPSR_Ref TObjectID="31863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213959">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3717.000000 -1162.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31856" ObjectName="SW-CX_TASE2JS.YN_MKⅡ_50331SW"/>
     <cge:Meas_Ref ObjectId="213959"/>
    <cge:TPSR_Ref TObjectID="31856"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213960">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3717.000000 -1549.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31857" ObjectName="SW-CX_TASE2JS.YN_MKⅡ_50311SW"/>
     <cge:Meas_Ref ObjectId="213960"/>
    <cge:TPSR_Ref TObjectID="31857"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213951">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3476.000000 -1552.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31835" ObjectName="SW-CX_TASE2JS.YN_MKⅠ_50211SW"/>
     <cge:Meas_Ref ObjectId="213951"/>
    <cge:TPSR_Ref TObjectID="31835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213958">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3476.000000 -1444.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31842" ObjectName="SW-CX_TASE2JS.YN_MKⅠ_50212SW"/>
     <cge:Meas_Ref ObjectId="213958"/>
    <cge:TPSR_Ref TObjectID="31842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213952">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3476.000000 -1356.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31836" ObjectName="SW-CX_TASE2JS.YN_MKⅠ_50221SW"/>
     <cge:Meas_Ref ObjectId="213952"/>
    <cge:TPSR_Ref TObjectID="31836"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213957">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3476.000000 -1233.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31841" ObjectName="SW-CX_TASE2JS.YN_MKⅠ_50222SW"/>
     <cge:Meas_Ref ObjectId="213957"/>
    <cge:TPSR_Ref TObjectID="31841"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213955">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3476.000000 -1042.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31838" ObjectName="SW-CX_TASE2JS.YN_MKⅠ_50232SW"/>
     <cge:Meas_Ref ObjectId="213955"/>
    <cge:TPSR_Ref TObjectID="31838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213954">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3476.000000 -1165.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31837" ObjectName="SW-CX_TASE2JS.YN_MKⅠ_50231SW"/>
     <cge:Meas_Ref ObjectId="213954"/>
    <cge:TPSR_Ref TObjectID="31837"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213956">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3312.000000 -1222.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31840" ObjectName="SW-CX_TASE2JS.YN_MKⅠ_5023617SW"/>
     <cge:Meas_Ref ObjectId="213956"/>
    <cge:TPSR_Ref TObjectID="31840"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213953">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3407.000000 -1222.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31839" ObjectName="SW-CX_TASE2JS.YN_MKⅠ_50236SW"/>
     <cge:Meas_Ref ObjectId="213953"/>
    <cge:TPSR_Ref TObjectID="31839"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66383">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4336.000000 -1430.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20244" ObjectName="SW-CX_TASE2JS.YN_MKⅠ_50312SW"/>
     <cge:Meas_Ref ObjectId="66383"/>
    <cge:TPSR_Ref TObjectID="20244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66384">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4336.000000 -1357.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20245" ObjectName="SW-CX_TASE2JS.YN_MKⅠ_50321SW"/>
     <cge:Meas_Ref ObjectId="66384"/>
    <cge:TPSR_Ref TObjectID="20245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66385">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4336.000000 -1234.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20246" ObjectName="SW-CX_TASE2JS.YN_MKⅠ_50322SW"/>
     <cge:Meas_Ref ObjectId="66385"/>
    <cge:TPSR_Ref TObjectID="20246"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66387">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4336.000000 -1043.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20248" ObjectName="SW-CX_TASE2JS.YN_MKⅠ_50332SW"/>
     <cge:Meas_Ref ObjectId="66387"/>
    <cge:TPSR_Ref TObjectID="20248"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66386">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4336.000000 -1166.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20247" ObjectName="SW-CX_TASE2JS.YN_MKⅠ_50331SW"/>
     <cge:Meas_Ref ObjectId="66386"/>
    <cge:TPSR_Ref TObjectID="20247"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66382">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4336.000000 -1553.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20243" ObjectName="SW-CX_TASE2JS.YN_MKⅠ_50311SW"/>
     <cge:Meas_Ref ObjectId="66382"/>
    <cge:TPSR_Ref TObjectID="20243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66391">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4095.000000 -1556.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20261" ObjectName="SW-CX_TASE2JS.YN_MKⅡ_50511SW"/>
     <cge:Meas_Ref ObjectId="66391"/>
    <cge:TPSR_Ref TObjectID="20261"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66392">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4095.000000 -1447.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20262" ObjectName="SW-CX_TASE2JS.YN_MKⅡ_50512SW"/>
     <cge:Meas_Ref ObjectId="66392"/>
    <cge:TPSR_Ref TObjectID="20262"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66393">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4095.000000 -1360.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20263" ObjectName="SW-CX_TASE2JS.YN_MKⅡ_50521SW"/>
     <cge:Meas_Ref ObjectId="66393"/>
    <cge:TPSR_Ref TObjectID="20263"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66394">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4095.000000 -1237.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20264" ObjectName="SW-CX_TASE2JS.YN_MKⅡ_50522SW"/>
     <cge:Meas_Ref ObjectId="66394"/>
    <cge:TPSR_Ref TObjectID="20264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66396">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4095.000000 -1046.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20266" ObjectName="SW-CX_TASE2JS.YN_MKⅡ_50532SW"/>
     <cge:Meas_Ref ObjectId="66396"/>
    <cge:TPSR_Ref TObjectID="20266"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-66395">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4095.000000 -1169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20265" ObjectName="SW-CX_TASE2JS.YN_MKⅡ_50531SW"/>
     <cge:Meas_Ref ObjectId="66395"/>
    <cge:TPSR_Ref TObjectID="20265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213933">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3931.000000 -1226.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31865" ObjectName="SW-CX_TASE2JS.YN_MKⅡ_5053617SW"/>
     <cge:Meas_Ref ObjectId="213933"/>
    <cge:TPSR_Ref TObjectID="31865"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213932">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4026.000000 -1226.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31864" ObjectName="SW-CX_TASE2JS.YN_MKⅡ_50536SW"/>
     <cge:Meas_Ref ObjectId="213932"/>
    <cge:TPSR_Ref TObjectID="31864"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213930">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4286.000000 -1412.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31843" ObjectName="SW-CX_TASE2JS.YN_MKⅠ_50336SW"/>
     <cge:Meas_Ref ObjectId="213930"/>
    <cge:TPSR_Ref TObjectID="31843"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213931">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4203.000000 -1412.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31844" ObjectName="SW-CX_TASE2JS.YN_MKⅠ_5033617SW"/>
     <cge:Meas_Ref ObjectId="213931"/>
    <cge:TPSR_Ref TObjectID="31844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213963">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3765.000000 -1408.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31858" ObjectName="SW-CX_TASE2JS.YN_MKⅡ_50316SW"/>
     <cge:Meas_Ref ObjectId="213963"/>
    <cge:TPSR_Ref TObjectID="31858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-213964">
    <use class="BV-500KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3865.000000 -1408.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31861" ObjectName="SW-CX_TASE2JS.YN_MKⅡ_5031617SW"/>
     <cge:Meas_Ref ObjectId="213964"/>
    <cge:TPSR_Ref TObjectID="31861"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 2956.500000 -1786.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-66355" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4344.000000 -1704.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66355" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-66356" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4344.000000 -1689.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66356" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-66358" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4344.000000 -1674.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66358" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-66357" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4020.000000 -1712.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66357" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-66359" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4020.000000 -1697.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66359" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-66360" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4020.000000 -1682.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="66360" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_局属变.svg" style="fill-opacity:0"><rect height="84" qtmmishow="hidden" width="410" x="2921" y="-1896"/></g>
   <g href="cx_索引_接线图_局属变.svg" style="fill-opacity:0"><rect height="131" qtmmishow="hidden" width="173" x="2811" y="-1924"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a2c9c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4291.000000 1703.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a2dbd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4280.000000 1688.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a2ebf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4305.000000 1673.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a30200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3955.000000 1713.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a304c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3944.000000 1698.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a30700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3969.000000 1683.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="84" qtmmishow="hidden" width="410" x="2921" y="-1896"/>
    </a>
   <metadata/><rect fill="white" height="84" opacity="0" stroke="white" transform="" width="410" x="2921" y="-1896"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="131" qtmmishow="hidden" width="173" x="2811" y="-1924"/>
    </a>
   <metadata/><rect fill="white" height="131" opacity="0" stroke="white" transform="" width="173" x="2811" y="-1924"/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1a19a50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3267.000000 -1221.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    <cge:TPSR_Ref TObjectID="0"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18d1eb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3886.000000 -1225.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    <cge:TPSR_Ref TObjectID="0"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18dcae0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4158.000000 -1411.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    <cge:TPSR_Ref TObjectID="0"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a3a860" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3921.000000 -1407.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    <cge:TPSR_Ref TObjectID="0"/></metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1"/>
</svg>