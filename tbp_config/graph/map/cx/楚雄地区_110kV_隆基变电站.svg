<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-253" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-138 -986 2932 1429">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape36">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="2" x2="2" y1="45" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="47" x2="47" y1="63" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="47" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="41" x2="53" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="51" x2="43" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="48" x2="45" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="36" x2="21" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="28" x2="28" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="12" x2="12" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="45" x2="45" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="28" x2="11" y1="58" y2="58"/>
    <rect height="23" stroke-width="0.369608" width="12" x="22" y="30"/>
    <rect height="23" stroke-width="0.369608" width="12" x="41" y="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="45" x2="12" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="28" x2="28" y1="79" y2="25"/>
    <polyline arcFlag="1" points="11,39 10,39 9,39 9,40 8,40 8,40 7,41 7,41 6,42 6,42 6,43 6,43 5,44 5,45 5,45 6,46 6,47 6,47 6,48 7,48 7,49 8,49 8,50 9,50 9,50 10,50 11,50 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,28 10,28 9,28 9,28 8,29 8,29 7,29 7,30 6,30 6,31 6,32 6,32 5,33 5,34 5,34 6,35 6,36 6,36 6,37 7,37 7,38 8,38 8,38 9,39 9,39 10,39 11,39 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,18 10,18 9,18 9,18 8,18 8,19 7,19 7,20 6,20 6,21 6,21 6,22 5,23 5,23 5,24 6,25 6,25 6,26 6,26 7,27 7,27 8,28 8,28 9,28 9,29 10,29 11,29 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="58" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="18" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="29" x2="29" y1="92" y2="100"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="16" x2="28" y1="79" y2="79"/>
    <polyline points="29,92 31,92 33,91 34,91 36,90 37,89 39,88 40,86 41,84 41,83 42,81 42,79 42,77 41,75 41,74 40,72 39,71 37,69 36,68 34,67 33,67 31,66 29,66 27,66 25,67 24,67 22,68 21,69 19,71 18,72 17,74 17,75 16,77 16,79 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="47" x2="28" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="36" x2="21" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="28" x2="11" y1="11" y2="11"/>
   </symbol>
   <symbol id="capacitor:shape12">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="5" x2="15" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="15" x2="15" y1="20" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="24" x2="34" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="24" x2="24" y1="20" y2="0"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="13" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="9" y1="60" y2="60"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="58" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="8" y2="37"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape25_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="58" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape25_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="12,76 19,76 16,83 12,76 "/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape71">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="53" y1="12" y2="12"/>
    <ellipse cx="39" cy="19" fillStyle="0" rx="15" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.36031" x1="30" x2="34" y1="55" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.39762" x1="25" x2="21" y1="55" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.77579" x1="21" x2="35" y1="62" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="33" x2="41" y1="16" y2="20"/>
    <ellipse cx="16" cy="20" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="40" x2="41" y1="20" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="47" x2="41" y1="16" y2="20"/>
    <ellipse cx="28" cy="56" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="53" y1="32" y2="32"/>
    <ellipse cx="40" cy="39" fillStyle="0" rx="15" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="32" y2="32"/>
    <ellipse cx="15" cy="40" fillStyle="0" rx="14.5" ry="12" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="15" x2="16" y1="20" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="22" x2="16" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="16" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="33" x2="41" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="40" x2="41" y1="40" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="47" x2="41" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="15" x2="16" y1="40" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="21" x2="15" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="16" y1="36" y2="40"/>
   </symbol>
   <symbol id="voltageTransformer:shape75">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649573" x1="6" x2="28" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="22" x2="22" y1="25" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="22" x2="18" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="26" x2="22" y1="23" y2="25"/>
    <circle cx="22" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="35" x2="35" y1="25" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="35" x2="31" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="35" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="49" x2="49" y1="12" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="49" x2="45" y1="12" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="53" x2="49" y1="10" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.605383" x1="35" x2="35" y1="12" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="35" x2="31" y1="12" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="39" x2="35" y1="10" y2="12"/>
    <circle cx="35" cy="12" r="7.5" stroke-width="0.804311"/>
    <circle cx="35" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="46" x2="51" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="47" x2="46" y1="24" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="50" x2="51" y1="24" y2="28"/>
    <circle cx="48" cy="12" r="7.5" stroke-width="0.804311"/>
    <circle cx="48" cy="25" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.475524" x1="6" x2="6" y1="27" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="33" y2="33"/>
   </symbol>
   <symbol id="voltageTransformer:shape5">
    <circle cx="7" cy="9" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="6" cy="18" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="13" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3842830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3843730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_38440f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3845330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3846620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_38472c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3847e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3848860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_30cd810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_30cd810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_384b8c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_384b8c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_384d6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_384d6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_384e790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3850390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3850f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3851d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3852680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3853d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3854a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_38552e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3855aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3856b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3857500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3857ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3858b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3859da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_385a990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_385b9c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_385c600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_386add0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_385def0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_385f4e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3860a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1439" width="2942" x="-143" y="-991"/>
  </g><g id="ArcTwoPoints_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="475,269 475,269 476,269 476,269 477,268 477,268 478,267 478,267 479,266 479,265 479,265 479,264 480,263 480,262 480,261 480,261 479,260 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="465,266 465,266 465,267 466,267 466,268 467,268 467,269 468,269 469,269 469,269 470,270 471,270 471,270 472,270 473,270 474,269 475,269 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="476,269 476,269 476,270 476,271 476,272 476,272 476,273 476,274 477,274 477,275 478,275 478,276 479,277 479,277 480,277 481,278 481,278 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,267 1114,267 1115,267 1115,267 1116,266 1116,266 1117,265 1117,265 1118,264 1118,263 1118,263 1118,262 1119,261 1119,260 1119,259 1119,259 1118,258 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1104,264 1104,264 1104,265 1105,265 1105,266 1106,266 1106,267 1107,267 1108,267 1108,267 1109,268 1110,268 1110,268 1111,268 1112,268 1113,267 1114,267 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1115,267 1115,267 1115,268 1115,269 1115,270 1115,270 1115,271 1115,272 1116,272 1116,273 1117,273 1117,274 1118,275 1118,275 1119,275 1120,276 1120,276 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1735,268 1735,268 1736,268 1736,268 1737,267 1737,267 1738,266 1738,266 1739,265 1739,264 1739,264 1739,263 1740,262 1740,261 1740,260 1740,260 1739,259 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1725,265 1725,265 1725,266 1726,266 1726,267 1727,267 1727,268 1728,268 1729,268 1729,268 1730,269 1731,269 1731,269 1732,269 1733,269 1734,268 1735,268 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1736,268 1736,268 1736,269 1736,270 1736,271 1736,271 1736,272 1736,273 1737,273 1737,274 1738,274 1738,275 1739,276 1739,276 1740,276 1741,277 1741,277 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="2734,250 2734,250 2735,250 2735,250 2736,249 2736,249 2737,248 2737,248 2738,247 2738,246 2738,246 2738,245 2739,244 2739,243 2739,242 2739,242 2738,241 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="2724,247 2724,247 2724,248 2725,248 2725,249 2726,249 2726,250 2727,250 2728,250 2728,250 2729,251 2730,251 2730,251 2731,251 2732,251 2733,250 2734,250 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="2735,250 2735,250 2735,251 2735,252 2735,253 2735,253 2735,254 2735,255 2736,255 2736,256 2737,256 2737,257 2738,258 2738,258 2739,258 2740,259 2740,259 " stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cdb2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 279.000000 94.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cdbdb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 285.000000 78.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cdc020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 269.000000 63.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cdc920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 279.000000 124.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cdcaf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 285.000000 47.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cdd3c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 279.000000 109.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cddc70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1326.000000 87.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cddf00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1332.000000 71.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cde140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1316.000000 56.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cde380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1326.000000 117.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cde5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1332.000000 40.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cde800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1326.000000 102.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cdf130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 513.000000 681.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cdf3c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 519.000000 665.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cdf600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 503.000000 650.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cdf840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 513.000000 711.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cdfa80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 519.000000 634.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cdfcc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 513.000000 696.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ce05f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 665.000000 189.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ce1990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 665.000000 204.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ce3a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 674.000000 467.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ce4410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 688.000000 437.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ce4970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 664.000000 452.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ce52e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1271.000000 463.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ce5580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1285.000000 433.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ce57c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1261.000000 448.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ce5f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 652.000000 89.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ce61e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 662.000000 104.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ce6420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 677.000000 74.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ce6660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 683.000000 59.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ce73f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1040.000000 103.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ce76a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1050.000000 118.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ce78e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1065.000000 88.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ce7b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1071.000000 73.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cec770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 707.000000 766.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cec9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 721.000000 736.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cecc10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 697.000000 751.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3895d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1813.000000 461.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3895fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1827.000000 431.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3896200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1803.000000 446.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3896530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1582.000000 101.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38967a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1592.000000 116.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38969e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1607.000000 86.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3896c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1613.000000 71.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38ba0e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2520.000000 465.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38ba340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2534.000000 435.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38ba580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2510.000000 450.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38ba8b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2289.000000 105.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38bab20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2299.000000 120.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38bad60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2314.000000 90.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38bafa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2320.000000 75.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b6650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1971.000000 92.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b68c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1977.000000 76.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b6b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1961.000000 61.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b6d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1971.000000 122.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b6f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1977.000000 45.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b71c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1971.000000 107.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b7b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2678.000000 92.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b7d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2684.000000 76.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b7fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2668.000000 61.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b8200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2678.000000 122.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b8440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2684.000000 45.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b8680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2678.000000 107.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="31" stroke="rgb(60,120,255)" stroke-width="1" width="17" x="1399" y="95"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="31" stroke="rgb(60,120,255)" stroke-width="1" width="17" x="760" y="97"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="36" stroke="rgb(60,120,255)" stroke-width="1" width="16" x="454" y="317"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="36" stroke="rgb(60,120,255)" stroke-width="1" width="16" x="1096" y="315"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="36" stroke="rgb(60,120,255)" stroke-width="1" width="16" x="1714" y="316"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="31" stroke="rgb(60,120,255)" stroke-width="1" width="17" x="1862" y="106"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="31" stroke="rgb(60,120,255)" stroke-width="1" width="17" x="2291" y="109"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="36" stroke="rgb(60,120,255)" stroke-width="1" width="16" x="2716" y="298"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-195317">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1282.000000 -673.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29691" ObjectName="SW-CX_LJ.CX_LJ_1901SW"/>
     <cge:Meas_Ref ObjectId="195317"/>
    <cge:TPSR_Ref TObjectID="29691"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195318">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1222.000000 -723.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29692" ObjectName="SW-CX_LJ.CX_LJ_19017SW"/>
     <cge:Meas_Ref ObjectId="195318"/>
    <cge:TPSR_Ref TObjectID="29692"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195319">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1224.000000 -657.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29693" ObjectName="SW-CX_LJ.CX_LJ_19010SW"/>
     <cge:Meas_Ref ObjectId="195319"/>
    <cge:TPSR_Ref TObjectID="29693"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195236">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1210.000000 -506.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29685" ObjectName="SW-CX_LJ.CX_LJ_1021SW"/>
     <cge:Meas_Ref ObjectId="195236"/>
    <cge:TPSR_Ref TObjectID="29685"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195237">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1210.000000 -313.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29686" ObjectName="SW-CX_LJ.CX_LJ_1026SW"/>
     <cge:Meas_Ref ObjectId="195237"/>
    <cge:TPSR_Ref TObjectID="29686"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195238">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1150.000000 -473.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29687" ObjectName="SW-CX_LJ.CX_LJ_10217SW"/>
     <cge:Meas_Ref ObjectId="195238"/>
    <cge:TPSR_Ref TObjectID="29687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195239">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1149.000000 -383.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29688" ObjectName="SW-CX_LJ.CX_LJ_10260SW"/>
     <cge:Meas_Ref ObjectId="195239"/>
    <cge:TPSR_Ref TObjectID="29688"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195240">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1148.000000 -274.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29689" ObjectName="SW-CX_LJ.CX_LJ_10267SW"/>
     <cge:Meas_Ref ObjectId="195240"/>
    <cge:TPSR_Ref TObjectID="29689"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195251">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1209.000000 -111.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29706" ObjectName="SW-CX_LJ.CX_LJ_002XC1"/>
     <cge:Meas_Ref ObjectId="195251"/>
    <cge:TPSR_Ref TObjectID="29706"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195251">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1209.000000 -41.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29705" ObjectName="SW-CX_LJ.CX_LJ_002XC"/>
     <cge:Meas_Ref ObjectId="195251"/>
    <cge:TPSR_Ref TObjectID="29705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195172">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 603.000000 -314.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29675" ObjectName="SW-CX_LJ.CX_LJ_1016SW"/>
     <cge:Meas_Ref ObjectId="195172"/>
    <cge:TPSR_Ref TObjectID="29675"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195173">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 543.000000 -474.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29676" ObjectName="SW-CX_LJ.CX_LJ_10117SW"/>
     <cge:Meas_Ref ObjectId="195173"/>
    <cge:TPSR_Ref TObjectID="29676"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195174">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 542.000000 -384.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29677" ObjectName="SW-CX_LJ.CX_LJ_10160SW"/>
     <cge:Meas_Ref ObjectId="195174"/>
    <cge:TPSR_Ref TObjectID="29677"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195175">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 541.000000 -275.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29678" ObjectName="SW-CX_LJ.CX_LJ_10167SW"/>
     <cge:Meas_Ref ObjectId="195175"/>
    <cge:TPSR_Ref TObjectID="29678"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195176">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 504.533537 -192.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29679" ObjectName="SW-CX_LJ.CX_LJ_1010SW"/>
     <cge:Meas_Ref ObjectId="195176"/>
    <cge:TPSR_Ref TObjectID="29679"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195186">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 602.000000 -111.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29682" ObjectName="SW-CX_LJ.CX_LJ_001XC1"/>
     <cge:Meas_Ref ObjectId="195186"/>
    <cge:TPSR_Ref TObjectID="29682"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195186">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 602.000000 -41.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29681" ObjectName="SW-CX_LJ.CX_LJ_001XC"/>
     <cge:Meas_Ref ObjectId="195186"/>
    <cge:TPSR_Ref TObjectID="29681"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195171">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 603.000000 -507.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29674" ObjectName="SW-CX_LJ.CX_LJ_1011"/>
     <cge:Meas_Ref ObjectId="195171"/>
    <cge:TPSR_Ref TObjectID="29674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 991.000000 199.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 991.000000 117.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 816.000000 35.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 816.000000 102.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 924.000000 33.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 924.000000 100.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 936.000000 210.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1261.000000 57.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1211.000000 183.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1261.000000 139.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1105.000000 58.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1059.000000 184.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1105.000000 140.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1398.000000 44.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 352.000000 176.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 352.000000 109.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 622.000000 59.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 572.000000 185.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 622.000000 141.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 466.000000 60.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 420.000000 186.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 466.000000 142.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 759.000000 46.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 302.000000 186.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195241">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1111.533537 -194.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29690" ObjectName="SW-CX_LJ.CX_LJ_1020SW"/>
     <cge:Meas_Ref ObjectId="195241"/>
    <cge:TPSR_Ref TObjectID="29690"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195119">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 849.000000 -851.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29672" ObjectName="SW-CX_LJ.CX_LJ_18167SW"/>
     <cge:Meas_Ref ObjectId="195119"/>
    <cge:TPSR_Ref TObjectID="29672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195117">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 848.000000 -693.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29670" ObjectName="SW-CX_LJ.CX_LJ_18117SW"/>
     <cge:Meas_Ref ObjectId="195117"/>
    <cge:TPSR_Ref TObjectID="29670"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195115">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 909.000000 -620.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29668" ObjectName="SW-CX_LJ.CX_LJ_1811SW"/>
     <cge:Meas_Ref ObjectId="195115"/>
    <cge:TPSR_Ref TObjectID="29668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195118">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 849.000000 -772.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29671" ObjectName="SW-CX_LJ.CX_LJ_18160SW"/>
     <cge:Meas_Ref ObjectId="195118"/>
    <cge:TPSR_Ref TObjectID="29671"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195116">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 909.000000 -793.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29669" ObjectName="SW-CX_LJ.CX_LJ_1816SW"/>
     <cge:Meas_Ref ObjectId="195116"/>
    <cge:TPSR_Ref TObjectID="29669"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1294.000000 256.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1263.000000 304.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 624.000000 316.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 655.000000 268.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236584">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1752.000000 -503.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39411" ObjectName="SW-CX_LJ.CX_LJ_1031SW"/>
     <cge:Meas_Ref ObjectId="236584"/>
    <cge:TPSR_Ref TObjectID="39411"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236585">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1752.000000 -310.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39412" ObjectName="SW-CX_LJ.CX_LJ_1036SW"/>
     <cge:Meas_Ref ObjectId="236585"/>
    <cge:TPSR_Ref TObjectID="39412"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236586">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1692.000000 -471.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39413" ObjectName="SW-CX_LJ.CX_LJ_10317SW"/>
     <cge:Meas_Ref ObjectId="236586"/>
    <cge:TPSR_Ref TObjectID="39413"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236587">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1691.000000 -381.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39415" ObjectName="SW-CX_LJ.CX_LJ_10360SW"/>
     <cge:Meas_Ref ObjectId="236587"/>
    <cge:TPSR_Ref TObjectID="39415"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236588">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1690.000000 -272.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39414" ObjectName="SW-CX_LJ.CX_LJ_10367SW"/>
     <cge:Meas_Ref ObjectId="236588"/>
    <cge:TPSR_Ref TObjectID="39414"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236591">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1751.000000 -109.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39417" ObjectName="SW-CX_LJ.CX_LJ_003XC1"/>
     <cge:Meas_Ref ObjectId="236591"/>
    <cge:TPSR_Ref TObjectID="39417"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236591">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1751.000000 -39.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39416" ObjectName="SW-CX_LJ.CX_LJ_003XC"/>
     <cge:Meas_Ref ObjectId="236591"/>
    <cge:TPSR_Ref TObjectID="39416"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236592">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1653.533537 -192.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39418" ObjectName="SW-CX_LJ.CX_LJ_1030SW"/>
     <cge:Meas_Ref ObjectId="236592"/>
    <cge:TPSR_Ref TObjectID="39418"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236611">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2459.000000 -508.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39421" ObjectName="SW-CX_LJ.CX_LJ_1041SW"/>
     <cge:Meas_Ref ObjectId="236611"/>
    <cge:TPSR_Ref TObjectID="39421"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236612">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2459.000000 -315.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39422" ObjectName="SW-CX_LJ.CX_LJ_1046SW"/>
     <cge:Meas_Ref ObjectId="236612"/>
    <cge:TPSR_Ref TObjectID="39422"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236613">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2399.000000 -475.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39423" ObjectName="SW-CX_LJ.CX_LJ_10417SW"/>
     <cge:Meas_Ref ObjectId="236613"/>
    <cge:TPSR_Ref TObjectID="39423"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236614">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2398.000000 -385.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39425" ObjectName="SW-CX_LJ.CX_LJ_10460SW"/>
     <cge:Meas_Ref ObjectId="236614"/>
    <cge:TPSR_Ref TObjectID="39425"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236615">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2397.000000 -276.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39424" ObjectName="SW-CX_LJ.CX_LJ_10467SW"/>
     <cge:Meas_Ref ObjectId="236615"/>
    <cge:TPSR_Ref TObjectID="39424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236617">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2458.000000 -113.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39427" ObjectName="SW-CX_LJ.CX_LJ_004XC1"/>
     <cge:Meas_Ref ObjectId="236617"/>
    <cge:TPSR_Ref TObjectID="39427"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236617">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2458.000000 -43.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39426" ObjectName="SW-CX_LJ.CX_LJ_004XC"/>
     <cge:Meas_Ref ObjectId="236617"/>
    <cge:TPSR_Ref TObjectID="39426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236618">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2360.533537 -196.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39428" ObjectName="SW-CX_LJ.CX_LJ_1040SW"/>
     <cge:Meas_Ref ObjectId="236618"/>
    <cge:TPSR_Ref TObjectID="39428"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2092.032331 33.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2092.032331 100.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2200.032331 31.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2200.032331 98.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1551.000000 59.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1501.000000 185.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1551.000000 141.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1553.000000 316.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1584.000000 268.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1726.000000 59.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1680.000000 185.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1726.000000 141.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1861.000000 55.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1977.000000 195.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1976.000000 116.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1927.000000 239.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2290.000000 58.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2404.000000 183.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2404.000000 101.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2354.000000 227.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2545.000000 53.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2495.000000 179.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2545.000000 135.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2547.000000 310.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2578.000000 262.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2725.000000 41.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2679.000000 167.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2725.000000 123.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-318634">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 353.000000 48.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49192" ObjectName="SW-CX_LJ.CX_LJ_XN1SW"/>
     <cge:Meas_Ref ObjectId="318634"/>
    <cge:TPSR_Ref TObjectID="49192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-318635">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 992.000000 45.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49193" ObjectName="SW-CX_LJ.CX_LJ_XN2SW"/>
     <cge:Meas_Ref ObjectId="318635"/>
    <cge:TPSR_Ref TObjectID="49193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-318636">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1977.000000 44.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49194" ObjectName="SW-CX_LJ.CX_LJ_XN3SW"/>
     <cge:Meas_Ref ObjectId="318636"/>
    <cge:TPSR_Ref TObjectID="49194"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-318637">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2405.000000 44.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49195" ObjectName="SW-CX_LJ.CX_LJ_XN4SW"/>
     <cge:Meas_Ref ObjectId="318637"/>
    <cge:TPSR_Ref TObjectID="49195"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_LJ.CX_LJ_1IM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="410,-600 2510,-600 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="29663" ObjectName="BS-CX_LJ.CX_LJ_1IM"/>
    <cge:TPSR_Ref TObjectID="29663"/></metadata>
   <polyline fill="none" opacity="0" points="410,-600 2510,-600 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_LJ.CX_LJ_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="336,-5 844,-5 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="29664" ObjectName="BS-CX_LJ.CX_LJ_9IM"/>
    <cge:TPSR_Ref TObjectID="29664"/></metadata>
   <polyline fill="none" opacity="0" points="336,-5 844,-5 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_LJ.CX_LJ_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="920,-7 1418,-7 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="29665" ObjectName="BS-CX_LJ.CX_LJ_9IIM"/>
    <cge:TPSR_Ref TObjectID="29665"/></metadata>
   <polyline fill="none" opacity="0" points="920,-7 1418,-7 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_LJ.CX_LJ_9ⅢM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1535,-7 2138,-7 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="39431" ObjectName="BS-CX_LJ.CX_LJ_9ⅢM"/>
    <cge:TPSR_Ref TObjectID="39431"/></metadata>
   <polyline fill="none" opacity="0" points="1535,-7 2138,-7 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_LJ.CX_LJ_9ⅣM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2172,-9 2794,-9 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="39432" ObjectName="BS-CX_LJ.CX_LJ_9ⅣM"/>
    <cge:TPSR_Ref TObjectID="39432"/></metadata>
   <polyline fill="none" opacity="0" points="2172,-9 2794,-9 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1243.000000 425.000000)" xlink:href="#capacitor:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 604.000000 427.000000)" xlink:href="#capacitor:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 832.000000 -886.000000)" xlink:href="#capacitor:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 778.000000 -886.000000)" xlink:href="#capacitor:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1533.000000 426.000000)" xlink:href="#capacitor:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2527.000000 420.000000)" xlink:href="#capacitor:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 986.000000 368.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 986.000000 368.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 347.000000 345.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 347.000000 345.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_LJ.CX_LJ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="42360"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 587.000000 -175.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 587.000000 -175.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="29683" ObjectName="TF-CX_LJ.CX_LJ_1T"/>
    <cge:TPSR_Ref TObjectID="29683"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_LJ.CX_LJ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="42354"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1194.000000 -177.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1194.000000 -177.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="29697" ObjectName="TF-CX_LJ.CX_LJ_2T"/>
    <cge:TPSR_Ref TObjectID="29697"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_LJ.CX_LJ_3T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="59275"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1736.000000 -172.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1736.000000 -172.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="39429" ObjectName="TF-CX_LJ.CX_LJ_3T"/>
    <cge:TPSR_Ref TObjectID="39429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_LJ.CX_LJ_4T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="59279"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2444.000000 -179.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2444.000000 -179.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="39430" ObjectName="TF-CX_LJ.CX_LJ_4T"/>
    <cge:TPSR_Ref TObjectID="39430"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1972.000000 398.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1972.000000 398.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2399.000000 386.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2399.000000 386.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_34e0170">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1335.000000 -733.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3657280">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1252.000000 -108.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34ec300">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1170.533537 -167.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36fe070">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 645.000000 -109.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36feda0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 563.533537 -165.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4c93770">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1147.000000 233.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4cffca0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1347.000000 135.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4d009d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1302.000000 232.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4d14a30">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 508.000000 235.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4cae170">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 708.000000 137.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4caf5f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 663.000000 234.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3805330">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 534.000000 -171.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3808bc0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1141.000000 -173.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_381fa30">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 952.000000 -859.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3886fc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1794.000000 -106.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3887cf0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1712.533537 -165.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3894040">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1683.000000 -171.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38ab110">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2501.000000 -110.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38abe40">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2419.533537 -169.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38b8180">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2390.000000 -175.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3734280">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1592.000000 234.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37402d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1768.000000 234.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3750430">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1810.000000 146.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_376fcd0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2239.000000 149.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37920e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2586.000000 228.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_379f420">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2767.000000 216.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -57.000000 -903.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-226243" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 15.000000 -784.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226243" ObjectName="CX_LJ:CX_LJ_sumP"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-195096" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 336.000000 -122.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195096" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29664"/>
     <cge:Term_Ref ObjectID="42288"/>
    <cge:TPSR_Ref TObjectID="29664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-195097" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 336.000000 -122.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195097" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29664"/>
     <cge:Term_Ref ObjectID="42288"/>
    <cge:TPSR_Ref TObjectID="29664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-195098" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 336.000000 -122.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195098" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29664"/>
     <cge:Term_Ref ObjectID="42288"/>
    <cge:TPSR_Ref TObjectID="29664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-195102" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 336.000000 -122.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195102" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29664"/>
     <cge:Term_Ref ObjectID="42288"/>
    <cge:TPSR_Ref TObjectID="29664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-195099" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 336.000000 -122.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195099" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29664"/>
     <cge:Term_Ref ObjectID="42288"/>
    <cge:TPSR_Ref TObjectID="29664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-195103" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 336.000000 -122.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195103" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29664"/>
     <cge:Term_Ref ObjectID="42288"/>
    <cge:TPSR_Ref TObjectID="29664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-195104" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1381.000000 -114.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195104" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29665"/>
     <cge:Term_Ref ObjectID="42289"/>
    <cge:TPSR_Ref TObjectID="29665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-195105" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1381.000000 -114.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195105" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29665"/>
     <cge:Term_Ref ObjectID="42289"/>
    <cge:TPSR_Ref TObjectID="29665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-195106" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1381.000000 -114.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195106" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29665"/>
     <cge:Term_Ref ObjectID="42289"/>
    <cge:TPSR_Ref TObjectID="29665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-195110" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1381.000000 -114.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195110" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29665"/>
     <cge:Term_Ref ObjectID="42289"/>
    <cge:TPSR_Ref TObjectID="29665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-195107" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1381.000000 -114.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195107" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29665"/>
     <cge:Term_Ref ObjectID="42289"/>
    <cge:TPSR_Ref TObjectID="29665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-195111" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1381.000000 -114.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195111" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29665"/>
     <cge:Term_Ref ObjectID="42289"/>
    <cge:TPSR_Ref TObjectID="29665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-195088" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 571.000000 -710.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195088" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29663"/>
     <cge:Term_Ref ObjectID="42287"/>
    <cge:TPSR_Ref TObjectID="29663"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-195089" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 571.000000 -710.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195089" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29663"/>
     <cge:Term_Ref ObjectID="42287"/>
    <cge:TPSR_Ref TObjectID="29663"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-195090" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 571.000000 -710.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195090" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29663"/>
     <cge:Term_Ref ObjectID="42287"/>
    <cge:TPSR_Ref TObjectID="29663"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-195094" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 571.000000 -710.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195094" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29663"/>
     <cge:Term_Ref ObjectID="42287"/>
    <cge:TPSR_Ref TObjectID="29663"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-195091" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 571.000000 -710.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195091" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29663"/>
     <cge:Term_Ref ObjectID="42287"/>
    <cge:TPSR_Ref TObjectID="29663"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-195095" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 571.000000 -710.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195095" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29663"/>
     <cge:Term_Ref ObjectID="42287"/>
    <cge:TPSR_Ref TObjectID="29663"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-195086" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1365.000000 -195.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195086" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29697"/>
     <cge:Term_Ref ObjectID="42355"/>
    <cge:TPSR_Ref TObjectID="29697"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-195087" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1365.000000 -195.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195087" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29697"/>
     <cge:Term_Ref ObjectID="42355"/>
    <cge:TPSR_Ref TObjectID="29697"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-195043" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 730.000000 -466.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195043" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29673"/>
     <cge:Term_Ref ObjectID="42304"/>
    <cge:TPSR_Ref TObjectID="29673"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-195044" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 730.000000 -466.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195044" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29673"/>
     <cge:Term_Ref ObjectID="42304"/>
    <cge:TPSR_Ref TObjectID="29673"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-195034" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 730.000000 -466.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195034" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29673"/>
     <cge:Term_Ref ObjectID="42304"/>
    <cge:TPSR_Ref TObjectID="29673"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-195070" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1327.000000 -463.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195070" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29684"/>
     <cge:Term_Ref ObjectID="42326"/>
    <cge:TPSR_Ref TObjectID="29684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-195071" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1327.000000 -463.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195071" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29684"/>
     <cge:Term_Ref ObjectID="42326"/>
    <cge:TPSR_Ref TObjectID="29684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-195061" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1327.000000 -463.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195061" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29684"/>
     <cge:Term_Ref ObjectID="42326"/>
    <cge:TPSR_Ref TObjectID="29684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-195056" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 719.000000 -103.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195056" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29680"/>
     <cge:Term_Ref ObjectID="42318"/>
    <cge:TPSR_Ref TObjectID="29680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-195057" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 719.000000 -103.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195057" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29680"/>
     <cge:Term_Ref ObjectID="42318"/>
    <cge:TPSR_Ref TObjectID="29680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-195046" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 719.000000 -103.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195046" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29680"/>
     <cge:Term_Ref ObjectID="42318"/>
    <cge:TPSR_Ref TObjectID="29680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-195058" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 719.000000 -103.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195058" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29680"/>
     <cge:Term_Ref ObjectID="42318"/>
    <cge:TPSR_Ref TObjectID="29680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-195083" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1109.000000 -117.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195083" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29696"/>
     <cge:Term_Ref ObjectID="42350"/>
    <cge:TPSR_Ref TObjectID="29696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-195084" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1109.000000 -117.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195084" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29696"/>
     <cge:Term_Ref ObjectID="42350"/>
    <cge:TPSR_Ref TObjectID="29696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-195073" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1109.000000 -117.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195073" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29696"/>
     <cge:Term_Ref ObjectID="42350"/>
    <cge:TPSR_Ref TObjectID="29696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-195085" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1109.000000 -117.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195085" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29696"/>
     <cge:Term_Ref ObjectID="42350"/>
    <cge:TPSR_Ref TObjectID="29696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-195059" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 766.000000 -204.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195059" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29683"/>
     <cge:Term_Ref ObjectID="42324"/>
    <cge:TPSR_Ref TObjectID="29683"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-195060" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 766.000000 -204.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195060" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29683"/>
     <cge:Term_Ref ObjectID="42324"/>
    <cge:TPSR_Ref TObjectID="29683"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-195031" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 764.000000 -766.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195031" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29667"/>
     <cge:Term_Ref ObjectID="42292"/>
    <cge:TPSR_Ref TObjectID="29667"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-195032" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 764.000000 -766.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195032" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29667"/>
     <cge:Term_Ref ObjectID="42292"/>
    <cge:TPSR_Ref TObjectID="29667"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-195022" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 764.000000 -766.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195022" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29667"/>
     <cge:Term_Ref ObjectID="42292"/>
    <cge:TPSR_Ref TObjectID="29667"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-236111" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1874.000000 -459.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236111" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39409"/>
     <cge:Term_Ref ObjectID="59233"/>
    <cge:TPSR_Ref TObjectID="39409"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-236112" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1874.000000 -459.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236112" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39409"/>
     <cge:Term_Ref ObjectID="59233"/>
    <cge:TPSR_Ref TObjectID="39409"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-236102" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1874.000000 -459.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236102" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39409"/>
     <cge:Term_Ref ObjectID="59233"/>
    <cge:TPSR_Ref TObjectID="39409"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-236138" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2578.000000 -460.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236138" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39419"/>
     <cge:Term_Ref ObjectID="59253"/>
    <cge:TPSR_Ref TObjectID="39419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-236139" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2578.000000 -460.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236139" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39419"/>
     <cge:Term_Ref ObjectID="59253"/>
    <cge:TPSR_Ref TObjectID="39419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-236129" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2578.000000 -460.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236129" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39419"/>
     <cge:Term_Ref ObjectID="59253"/>
    <cge:TPSR_Ref TObjectID="39419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-236124" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1649.000000 -117.000000) translate(0,12)">236124.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236124" ObjectName="CX_LJ.CX_LJ_003BK:F"/>
     <cge:PSR_Ref ObjectID="39410"/>
     <cge:Term_Ref ObjectID="59235"/>
    <cge:TPSR_Ref TObjectID="39410"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-236125" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1649.000000 -117.000000) translate(0,27)">236125.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236125" ObjectName="CX_LJ.CX_LJ_003BK:F"/>
     <cge:PSR_Ref ObjectID="39410"/>
     <cge:Term_Ref ObjectID="59235"/>
    <cge:TPSR_Ref TObjectID="39410"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-236114" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1649.000000 -117.000000) translate(0,42)">236114.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236114" ObjectName="CX_LJ.CX_LJ_003BK:F"/>
     <cge:PSR_Ref ObjectID="39410"/>
     <cge:Term_Ref ObjectID="59235"/>
    <cge:TPSR_Ref TObjectID="39410"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-236126" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1649.000000 -117.000000) translate(0,57)">236126.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236126" ObjectName="CX_LJ.CX_LJ_003BK:F"/>
     <cge:PSR_Ref ObjectID="39410"/>
     <cge:Term_Ref ObjectID="59235"/>
    <cge:TPSR_Ref TObjectID="39410"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-236151" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2355.000000 -119.000000) translate(0,12)">236151.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236151" ObjectName="CX_LJ.CX_LJ_004BK:F"/>
     <cge:PSR_Ref ObjectID="39420"/>
     <cge:Term_Ref ObjectID="59255"/>
    <cge:TPSR_Ref TObjectID="39420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-236152" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2355.000000 -119.000000) translate(0,27)">236152.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236152" ObjectName="CX_LJ.CX_LJ_004BK:F"/>
     <cge:PSR_Ref ObjectID="39420"/>
     <cge:Term_Ref ObjectID="59255"/>
    <cge:TPSR_Ref TObjectID="39420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-236141" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2355.000000 -119.000000) translate(0,42)">236141.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236141" ObjectName="CX_LJ.CX_LJ_004BK:F"/>
     <cge:PSR_Ref ObjectID="39420"/>
     <cge:Term_Ref ObjectID="59255"/>
    <cge:TPSR_Ref TObjectID="39420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-236153" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2355.000000 -119.000000) translate(0,57)">236153.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236153" ObjectName="CX_LJ.CX_LJ_004BK:F"/>
     <cge:PSR_Ref ObjectID="39420"/>
     <cge:Term_Ref ObjectID="59255"/>
    <cge:TPSR_Ref TObjectID="39420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-236156" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2026.000000 -120.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236156" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39431"/>
     <cge:Term_Ref ObjectID="59281"/>
    <cge:TPSR_Ref TObjectID="39431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-236157" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2026.000000 -120.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236157" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39431"/>
     <cge:Term_Ref ObjectID="59281"/>
    <cge:TPSR_Ref TObjectID="39431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-236158" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2026.000000 -120.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236158" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39431"/>
     <cge:Term_Ref ObjectID="59281"/>
    <cge:TPSR_Ref TObjectID="39431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-236162" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2026.000000 -120.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236162" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39431"/>
     <cge:Term_Ref ObjectID="59281"/>
    <cge:TPSR_Ref TObjectID="39431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-236159" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2026.000000 -120.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236159" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39431"/>
     <cge:Term_Ref ObjectID="59281"/>
    <cge:TPSR_Ref TObjectID="39431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-236163" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2026.000000 -120.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236163" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39431"/>
     <cge:Term_Ref ObjectID="59281"/>
    <cge:TPSR_Ref TObjectID="39431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-236164" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2732.000000 -120.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236164" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39432"/>
     <cge:Term_Ref ObjectID="59282"/>
    <cge:TPSR_Ref TObjectID="39432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-236165" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2732.000000 -120.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236165" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39432"/>
     <cge:Term_Ref ObjectID="59282"/>
    <cge:TPSR_Ref TObjectID="39432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-236166" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2732.000000 -120.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236166" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39432"/>
     <cge:Term_Ref ObjectID="59282"/>
    <cge:TPSR_Ref TObjectID="39432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-236170" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2732.000000 -120.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236170" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39432"/>
     <cge:Term_Ref ObjectID="59282"/>
    <cge:TPSR_Ref TObjectID="39432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-236167" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2732.000000 -120.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236167" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39432"/>
     <cge:Term_Ref ObjectID="59282"/>
    <cge:TPSR_Ref TObjectID="39432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-236171" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2732.000000 -120.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236171" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39432"/>
     <cge:Term_Ref ObjectID="59282"/>
    <cge:TPSR_Ref TObjectID="39432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-236127" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1905.000000 -189.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236127" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39429"/>
     <cge:Term_Ref ObjectID="59273"/>
    <cge:TPSR_Ref TObjectID="39429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="0" id="ME-236128" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1905.000000 -189.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236128" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39429"/>
     <cge:Term_Ref ObjectID="59273"/>
    <cge:TPSR_Ref TObjectID="39429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-236154" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2616.000000 -197.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236154" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39430"/>
     <cge:Term_Ref ObjectID="59280"/>
    <cge:TPSR_Ref TObjectID="39430"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="0" id="ME-236155" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2616.000000 -197.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="236155" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39430"/>
     <cge:Term_Ref ObjectID="59280"/>
    <cge:TPSR_Ref TObjectID="39430"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-45" y="-962"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-45" y="-962"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-94" y="-979"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-94" y="-979"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="1789" y="-227"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="1789" y="-227"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="2496" y="-231"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="2496" y="-231"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="653" y="-241"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="653" y="-241"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="1263" y="-238"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="1263" y="-238"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="927" y="-749"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="927" y="-749"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="21" qtmmishow="hidden" width="64" x="-77" y="-616"/>
    </a>
   <metadata/><rect fill="white" height="21" opacity="0" stroke="white" transform="" width="64" x="-77" y="-616"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-45" y="-962"/></g>
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-94" y="-979"/></g>
   <g href="110kV隆基变电站3号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="1789" y="-227"/></g>
   <g href="110kV隆基变电站4号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="2496" y="-231"/></g>
   <g href="110kV隆基变电站1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="653" y="-241"/></g>
   <g href="110kV隆基变电站2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="1263" y="-238"/></g>
   <g href="110kV隆基变电站CX_LJ_181间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="927" y="-749"/></g>
   <g href="110kV隆基变电站GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="21" qtmmishow="hidden" width="64" x="-77" y="-616"/></g>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1072,330 1071,330 1070,330 1069,330 1069,331 1068,331 1067,332 1067,332 1066,333 1066,333 1066,334 1066,334 1066,335 1066,335 1066,336 1066,337 1066,337 1067,338 1067,338 1068,339 1068,339 1069,339 1070,340 1071,340 1071,340 1072,340 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1072,340 1071,340 1071,340 1070,340 1069,341 1068,341 1068,341 1067,342 1067,342 1066,343 1066,344 1066,344 1066,345 1066,345 1066,346 1066,347 1067,347 1067,348 1067,348 1068,349 1069,349 1069,349 1070,350 1071,350 1072,350 1072,350 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1071,320 1071,321 1070,321 1069,321 1068,321 1068,322 1067,322 1066,322 1066,323 1066,323 1065,324 1065,325 1065,325 1065,326 1065,326 1066,327 1066,328 1066,328 1067,329 1067,329 1068,329 1069,330 1069,330 1070,330 1071,330 1072,330 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="430,332 429,332 428,332 427,332 427,333 426,333 425,334 425,334 424,335 424,335 424,336 424,336 424,337 424,337 424,338 424,339 424,339 425,340 425,340 426,341 426,341 427,341 428,342 429,342 429,342 430,342 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="430,342 429,342 429,342 428,342 427,343 426,343 426,343 425,344 425,344 424,345 424,346 424,346 424,347 424,347 424,348 424,349 425,349 425,350 425,350 426,351 427,351 427,351 428,352 429,352 430,352 430,352 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="429,322 429,323 428,323 427,323 426,323 426,324 425,324 424,324 424,325 424,325 423,326 423,327 423,327 423,328 423,328 424,329 424,330 424,330 425,331 425,331 426,331 427,332 427,332 428,332 429,332 430,332 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1690,331 1689,331 1688,331 1687,331 1687,332 1686,332 1685,333 1685,333 1684,334 1684,334 1684,335 1684,335 1684,336 1684,336 1684,337 1684,338 1684,338 1685,339 1685,339 1686,340 1686,340 1687,340 1688,341 1689,341 1689,341 1690,341 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1690,341 1689,341 1689,341 1688,341 1687,342 1686,342 1686,342 1685,343 1685,343 1684,344 1684,345 1684,345 1684,346 1684,346 1684,347 1684,348 1685,348 1685,349 1685,349 1686,350 1687,350 1687,350 1688,351 1689,351 1690,351 1690,351 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="1689,321 1689,322 1688,322 1687,322 1686,322 1686,323 1685,323 1684,323 1684,324 1684,324 1683,325 1683,326 1683,326 1683,327 1683,327 1684,328 1684,329 1684,329 1685,330 1685,330 1686,330 1687,331 1687,331 1688,331 1689,331 1690,331 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="2692,313 2691,313 2690,313 2689,313 2689,314 2688,314 2687,315 2687,315 2686,316 2686,316 2686,317 2686,317 2686,318 2686,318 2686,319 2686,320 2686,320 2687,321 2687,321 2688,322 2688,322 2689,322 2690,323 2691,323 2691,323 2692,323 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="2692,323 2691,323 2691,323 2690,323 2689,324 2688,324 2688,324 2687,325 2687,325 2686,326 2686,327 2686,327 2686,328 2686,328 2686,329 2686,330 2687,330 2687,331 2687,331 2688,332 2689,332 2689,332 2690,333 2691,333 2692,333 2692,333 " stroke="rgb(60,120,255)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arcFlag="1" fill="none" points="2691,303 2691,304 2690,304 2689,304 2688,304 2688,305 2687,305 2686,305 2686,306 2686,306 2685,307 2685,308 2685,308 2685,309 2685,309 2686,310 2686,311 2686,311 2687,312 2687,312 2688,312 2689,313 2689,313 2690,313 2691,313 2692,313 " stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-195235">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1210.000000 -410.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29684" ObjectName="SW-CX_LJ.CX_LJ_102BK"/>
     <cge:Meas_Ref ObjectId="195235"/>
    <cge:TPSR_Ref TObjectID="29684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195250">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1210.000000 -70.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29696" ObjectName="SW-CX_LJ.CX_LJ_002BK"/>
     <cge:Meas_Ref ObjectId="195250"/>
    <cge:TPSR_Ref TObjectID="29696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195185">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 603.000000 -70.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29680" ObjectName="SW-CX_LJ.CX_LJ_001BK"/>
     <cge:Meas_Ref ObjectId="195185"/>
    <cge:TPSR_Ref TObjectID="29680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195170">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 603.000000 -411.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29673" ObjectName="SW-CX_LJ.CX_LJ_101BK"/>
     <cge:Meas_Ref ObjectId="195170"/>
    <cge:TPSR_Ref TObjectID="29673"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 992.000000 164.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 817.000000 74.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1262.000000 104.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1106.000000 105.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 353.000000 148.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 623.000000 106.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 467.000000 107.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195113">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 909.000000 -720.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29667" ObjectName="SW-CX_LJ.CX_LJ_181BK"/>
     <cge:Meas_Ref ObjectId="195113"/>
    <cge:TPSR_Ref TObjectID="29667"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236583">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1752.000000 -405.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39409" ObjectName="SW-CX_LJ.CX_LJ_103BK"/>
     <cge:Meas_Ref ObjectId="236583"/>
    <cge:TPSR_Ref TObjectID="39409"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236590">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1752.000000 -69.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39410" ObjectName="SW-CX_LJ.CX_LJ_003BK"/>
     <cge:Meas_Ref ObjectId="236590"/>
    <cge:TPSR_Ref TObjectID="39410"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236610">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2458.000000 -409.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39419" ObjectName="SW-CX_LJ.CX_LJ_104BK"/>
     <cge:Meas_Ref ObjectId="236610"/>
    <cge:TPSR_Ref TObjectID="39419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-236645">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2459.000000 -72.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39420" ObjectName="SW-CX_LJ.CX_LJ_004BK"/>
     <cge:Meas_Ref ObjectId="236645"/>
    <cge:TPSR_Ref TObjectID="39420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2093.032331 72.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1552.000000 106.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1727.000000 106.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1978.000000 160.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2405.000000 148.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2546.000000 100.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2726.000000 88.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_LJ" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-110kV.lushangIhuiTlj_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="918,-943 918,-984 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37736" ObjectName="AC-110kV.lushangIhuiTlj_line"/>
    <cge:TPSR_Ref TObjectID="37736_SS-253"/></metadata>
   <polyline fill="none" opacity="0" points="918,-943 918,-984 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_298db00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1214.000000 -757.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34c6160" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1198.000000 -722.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36f93a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1200.000000 -656.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36e1190" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1124.000000 -472.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34c34c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1123.000000 -382.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34f3940" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1122.000000 -273.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30d59c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 517.000000 -473.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3702af0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 516.000000 -383.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3706170" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 515.000000 -274.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_382ed20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 910.000000 211.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38385d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1185.000000 184.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4cf6bb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1033.000000 185.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4c9c280" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 546.000000 186.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4ca6100" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 394.000000 187.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4cba0d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 276.000000 187.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_380c8b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 823.000000 -850.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_380fad0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 822.000000 -692.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3818e00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 823.000000 -771.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3823270" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 744.000000 -890.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4ccc840" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1340.000000 257.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4cd04b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 701.000000 269.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_387c8c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1666.000000 -470.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_387fae0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1665.000000 -380.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3886070" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1664.000000 -271.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38a0a10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2373.000000 -474.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38a3c30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2372.000000 -384.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38aa1c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2371.000000 -275.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38d6f50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1475.000000 186.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_373b9f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1630.000000 269.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3744a90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1654.000000 186.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3761d40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1901.000000 240.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3781e70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2328.000000 228.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_378c0e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2469.000000 180.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3799850" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2624.000000 263.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_37a3c40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2653.000000 168.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="29664" cx="612" cy="-5" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29665" cx="1219" cy="-7" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29664" cx="826" cy="-5" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29665" cx="934" cy="-7" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29665" cx="1115" cy="-7" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29665" cx="1271" cy="-7" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29665" cx="1408" cy="-7" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29664" cx="632" cy="-5" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29664" cx="476" cy="-5" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29664" cx="769" cy="-5" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29663" cx="612" cy="-599" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29663" cx="918" cy="-599" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29663" cx="1219" cy="-599" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29663" cx="1291" cy="-599" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29663" cx="2468" cy="-599" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39431" cx="2102" cy="-7" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39431" cx="1761" cy="-7" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39431" cx="1736" cy="-7" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39431" cx="1561" cy="-7" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39431" cx="1871" cy="-7" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39432" cx="2299" cy="-9" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39432" cx="2210" cy="-9" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39432" cx="2554" cy="-9" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39432" cx="2468" cy="-9" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39432" cx="2734" cy="-9" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29663" cx="1761" cy="-599" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29664" cx="362" cy="-5" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29665" cx="1001" cy="-7" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39431" cx="1986" cy="-7" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39432" cx="2414" cy="-9" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39432" cx="2414" cy="-9" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39432" cx="2414" cy="-9" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_344c8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -138.000000 -825.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_344c8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -138.000000 -825.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_344c8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -138.000000 -825.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_344c8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -138.000000 -825.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_344c8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -138.000000 -825.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_344c8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -138.000000 -825.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_344c8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -138.000000 -825.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ca920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ca920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ca920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ca920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ca920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ca920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ca920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ca920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ca920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ca920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ca920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ca920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ca920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ca920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ca920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ca920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34ca920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,353)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_29e95b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -5.000000 -951.500000) translate(0,16)">隆基变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3825ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 991.000000 -975.000000) translate(0,12)">禄</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3825ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 991.000000 -975.000000) translate(0,27)">上</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3825ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 991.000000 -975.000000) translate(0,42)">Ⅰ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3825ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 991.000000 -975.000000) translate(0,57)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3825ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 991.000000 -975.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3825ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 991.000000 -975.000000) translate(0,87)">T</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3825ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 991.000000 -975.000000) translate(0,102)">隆</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3825ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 991.000000 -975.000000) translate(0,117)">基</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3825ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 991.000000 -975.000000) translate(0,132)">支</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3825ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 991.000000 -975.000000) translate(0,147)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3828220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1244.000000 -848.000000) translate(0,12)">110kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3828220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1244.000000 -848.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3829c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 733.000000 -311.000000) translate(0,12)">1号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3829c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 733.000000 -311.000000) translate(0,27)">SFZ11-25000/110GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3829c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 733.000000 -311.000000) translate(0,42)">110±8×1.25%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3829c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 733.000000 -311.000000) translate(0,57)">25MVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3829c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 733.000000 -311.000000) translate(0,72)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3829c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 733.000000 -311.000000) translate(0,87)">Ud%=10.31</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cc2720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1354.000000 -308.000000) translate(0,12)">2号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cc2720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1354.000000 -308.000000) translate(0,27)">SFZ11-25000/110GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cc2720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1354.000000 -308.000000) translate(0,42)">110±8×1.25%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cc2720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1354.000000 -308.000000) translate(0,57)">25MVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cc2720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1354.000000 -308.000000) translate(0,72)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cc2720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1354.000000 -308.000000) translate(0,87)">Ud%=10.27</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cc2ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 527.000000 -137.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cc2f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 325.000000 354.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cc36e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 414.000000 376.000000) translate(0,12)">10kV1号接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cc36e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 414.000000 376.000000) translate(0,27)">及消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cc4af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 582.000000 428.000000) translate(0,12)">10kV1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cc5430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 718.000000 188.000000) translate(0,12)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cc5430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 718.000000 188.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cc5ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1226.000000 423.000000) translate(0,12)">10kV2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cc5cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1362.000000 183.000000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cc5cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1362.000000 183.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cc6280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 969.000000 374.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cc6490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1058.000000 371.000000) translate(0,12)">10kV2号接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cc6490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1058.000000 371.000000) translate(0,27)">及消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd3fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 927.000000 -749.000000) translate(0,12)">181</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd45f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 853.000000 -879.000000) translate(0,12)">18167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd4830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 925.000000 -823.000000) translate(0,12)">1816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd4a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 852.000000 -803.000000) translate(0,12)">18160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd4cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 852.000000 -724.000000) translate(0,12)">18117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd4ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 925.000000 -650.000000) translate(0,12)">1811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd5130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 410.000000 -620.000000) translate(0,12)">110kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd5370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1227.000000 -688.000000) translate(0,12)">19010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd59c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1298.000000 -703.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd5c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1225.000000 -754.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd5e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 619.000000 -537.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd60c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 547.000000 -505.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd6300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 621.000000 -440.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd6540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 545.000000 -415.000000) translate(0,12)">10160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd6780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 619.000000 -344.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd69c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 544.000000 -306.000000) translate(0,12)">10167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd6c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 621.000000 -99.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd6e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1226.000000 -536.000000) translate(0,12)">1021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd7080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1153.000000 -504.000000) translate(0,12)">10217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd72c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1228.000000 -439.000000) translate(0,12)">102</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd7500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1152.000000 -414.000000) translate(0,12)">10260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd7740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1226.000000 -343.000000) translate(0,12)">1026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd7980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1151.000000 -305.000000) translate(0,12)">10267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd7bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 471.000000 -223.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd7e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1077.000000 -219.000000) translate(0,12)">1020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd8040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 336.000000 -25.000000) translate(0,12)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd8280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 920.000000 -27.000000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd84c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 375.000000 117.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd8700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 306.000000 187.000000) translate(0,12)">08167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd8940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 486.000000 78.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd8b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 418.000000 186.000000) translate(0,12)">08267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd8dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 572.000000 185.000000) translate(0,12)">08360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd9000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 640.000000 77.000000) translate(0,12)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd9240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 938.000000 209.000000) translate(0,12)">08467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd9480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1014.000000 132.000000) translate(0,12)">084</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd96c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1059.000000 184.000000) translate(0,12)">08567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd9900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1127.000000 76.000000) translate(0,12)">085</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd9b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1212.000000 184.000000) translate(0,12)">08660</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd9d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1284.000000 75.000000) translate(0,12)">086</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd9fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 724.000000 29.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cda200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 836.000000 45.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cda440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 900.000000 44.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cda680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1316.000000 256.000000) translate(0,12)">08667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cda8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1230.000000 276.000000) translate(0,12)">0866</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cdab00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1422.000000 28.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cdad40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 680.000000 269.000000) translate(0,12)">08367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cdaf80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 588.000000 285.000000) translate(0,12)">0836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ce24b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1275.000000 -196.000000) translate(0,12)">档位（档）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ce2710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1275.000000 -181.000000) translate(0,12)">油温（℃）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ce7d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1228.000000 -99.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3873540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1263.000000 -238.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3873b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 653.000000 -241.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3873db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 10.000000 -43.000000) translate(0,18)">15987870017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38950d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1887.000000 -303.000000) translate(0,12)">3号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38950d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1887.000000 -303.000000) translate(0,27)">SFZ11-20000/110GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38950d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1887.000000 -303.000000) translate(0,42)">110±8×1.25%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38950d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1887.000000 -303.000000) translate(0,57)">20MVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38950d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1887.000000 -303.000000) translate(0,72)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38950d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1887.000000 -303.000000) translate(0,87)">Ud%=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38957b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1817.000000 -194.000000) translate(0,12)">档位（档）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3895a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1817.000000 -179.000000) translate(0,12)">油温（℃）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38b9210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2605.000000 -310.000000) translate(0,12)">4号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38b9210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2605.000000 -310.000000) translate(0,27)">SFZ11-20000/110GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38b9210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2605.000000 -310.000000) translate(0,42)">110±8×1.25%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38b9210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2605.000000 -310.000000) translate(0,57)">20MVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38b9210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2605.000000 -310.000000) translate(0,72)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38b9210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2605.000000 -310.000000) translate(0,87)">Ud%=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38b98f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2475.000000 -345.000000) translate(0,12)">1046</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38b9b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2524.000000 -198.000000) translate(0,12)">档位（档）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38b9db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2524.000000 -183.000000) translate(0,12)">油温（℃）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38ccfb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1535.000000 -27.000000) translate(0,12)">10kVⅢ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38cd920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2172.000000 -29.000000) translate(0,12)">10kVⅣ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38cde80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2069.000000 44.000000) translate(0,12)">034</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38ce100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2171.000000 43.000000) translate(0,12)">0344</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3738420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1511.000000 428.000000) translate(0,12)">10kV3号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_373ec70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1501.000000 185.000000) translate(0,12)">07160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_373f2a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1572.000000 77.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_373f4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1609.000000 269.000000) translate(0,12)">07167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_373f720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1517.000000 285.000000) translate(0,12)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_374de30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1674.000000 375.000000) translate(0,12)">10kV3号接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_374de30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1674.000000 375.000000) translate(0,27)">及消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_374e4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1678.000000 185.000000) translate(0,12)">07267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3757380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1820.000000 197.000000) translate(0,12)">10kVⅢ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3757380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1820.000000 197.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37579b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1826.000000 38.000000) translate(0,12)">0903</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3764f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1950.000000 407.000000) translate(0,12)">3号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3765530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1931.000000 240.000000) translate(0,12)">07367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3776c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2249.000000 200.000000) translate(0,12)">10kVⅣ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3776c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2249.000000 200.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3777250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2255.000000 41.000000) translate(0,12)">0904</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3785030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2377.000000 395.000000) translate(0,12)">4号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3785660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2358.000000 228.000000) translate(0,12)">07467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3796280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2505.000000 422.000000) translate(0,12)">10kV4号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_379cad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2495.000000 179.000000) translate(0,12)">07560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_379d100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2566.000000 71.000000) translate(0,12)">075</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_379d340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2603.000000 263.000000) translate(0,12)">07567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_379d580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2511.000000 279.000000) translate(0,12)">0756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37acfe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2678.000000 354.000000) translate(0,12)">10kV4号接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37acfe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2678.000000 354.000000) translate(0,27)">及消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37ad660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2679.000000 167.000000) translate(0,12)">07667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37ad890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2747.000000 59.000000) translate(0,12)">076</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b1fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1698.000000 77.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b2610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1945.000000 131.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b2850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2374.000000 120.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b2a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1770.000000 -434.000000) translate(0,12)">103</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b2cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1768.000000 -533.000000) translate(0,12)">1031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b2f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1695.000000 -502.000000) translate(0,12)">10317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b3150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1694.000000 -412.000000) translate(0,12)">10360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b3390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1768.000000 -340.000000) translate(0,12)">1036</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b35d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1693.000000 -303.000000) translate(0,12)">10367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b3810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1617.000000 -225.000000) translate(0,12)">1030</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b3a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1770.000000 -98.000000) translate(0,12)">003</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b3c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2476.000000 -438.000000) translate(0,12)">104</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b3ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2403.000000 -506.000000) translate(0,12)">10417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b4110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2401.000000 -416.000000) translate(0,12)">10460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b4350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2400.000000 -307.000000) translate(0,12)">10467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b4590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2324.000000 -223.000000) translate(0,12)">1040</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b47d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2477.000000 -101.000000) translate(0,12)">004</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b4a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2496.000000 -231.000000) translate(0,12)">4号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37b4c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1788.000000 -224.000000) translate(0,12)">3号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37ba410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -72.000000 -610.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37bae30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2475.000000 -538.000000) translate(0,12)">1041</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37ca960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 381.000000 19.000000) translate(0,12)">XN1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37cb340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1016.000000 14.000000) translate(0,12)">XN2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37cb5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2005.000000 12.000000) translate(0,12)">XN3</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37cc760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2421.000000 13.000000) translate(0,12)">XN4</text>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="475" cy="267" fill="none" fillStyle="0" r="15.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1114" cy="265" fill="none" fillStyle="0" r="15.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1735" cy="266" fill="none" fillStyle="0" r="15.5" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="2734" cy="248" fill="none" fillStyle="0" r="15.5" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_28010d0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1250.000000 -746.000000)" xlink:href="#voltageTransformer:shape71"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4d05140">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1324.000000 178.000000)" xlink:href="#voltageTransformer:shape75"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4cb0580">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 686.000000 180.000000)" xlink:href="#voltageTransformer:shape75"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3821250">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 818.000000 -916.000000)" xlink:href="#voltageTransformer:shape5"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3751160">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1788.000000 189.000000)" xlink:href="#voltageTransformer:shape75"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3770a00">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2217.000000 192.000000)" xlink:href="#voltageTransformer:shape75"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-0KV" id="g_29548e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1232,-763 1255,-763 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" ObjectIDND0="g_298db00@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_298db00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1232,-763 1255,-763 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2954b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1341,-737 1291,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_34e0170@0" ObjectIDZND0="g_28010d0@0" ObjectIDZND1="29692@x" ObjectIDZND2="29691@x" Pin0InfoVect0LinkObjId="g_28010d0_0" Pin0InfoVect1LinkObjId="SW-195318_0" Pin0InfoVect2LinkObjId="SW-195317_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34e0170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1341,-737 1291,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2954da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1291,-737 1291,-753 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_34e0170@0" ObjectIDND1="29692@x" ObjectIDND2="29691@x" ObjectIDZND0="g_28010d0@0" Pin0InfoVect0LinkObjId="g_28010d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_34e0170_0" Pin1InfoVect1LinkObjId="SW-195318_0" Pin1InfoVect2LinkObjId="SW-195317_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1291,-737 1291,-753 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2955000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1216,-728 1229,-728 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_34c6160@0" ObjectIDZND0="29692@0" Pin0InfoVect0LinkObjId="SW-195318_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34c6160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1216,-728 1229,-728 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34c5f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1292,-728 1263,-728 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="29691@x" ObjectIDND1="g_34e0170@0" ObjectIDND2="g_28010d0@0" ObjectIDZND0="29692@1" Pin0InfoVect0LinkObjId="SW-195318_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-195317_0" Pin1InfoVect1LinkObjId="g_34e0170_0" Pin1InfoVect2LinkObjId="g_28010d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1292,-728 1263,-728 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38637c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1291,-714 1291,-728 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="29691@1" ObjectIDZND0="29692@x" ObjectIDZND1="g_34e0170@0" ObjectIDZND2="g_28010d0@0" Pin0InfoVect0LinkObjId="SW-195318_0" Pin0InfoVect1LinkObjId="g_34e0170_0" Pin0InfoVect2LinkObjId="g_28010d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195317_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1291,-714 1291,-728 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3863a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1291,-728 1291,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="29692@x" ObjectIDND1="29691@x" ObjectIDZND0="g_34e0170@0" ObjectIDZND1="g_28010d0@0" Pin0InfoVect0LinkObjId="g_34e0170_0" Pin0InfoVect1LinkObjId="g_28010d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-195318_0" Pin1InfoVect1LinkObjId="SW-195317_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1291,-728 1291,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36f9140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1218,-662 1231,-662 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_36f93a0@0" ObjectIDZND0="29693@0" Pin0InfoVect0LinkObjId="SW-195319_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36f93a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1218,-662 1231,-662 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36e0cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1142,-478 1155,-478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_36e1190@0" ObjectIDZND0="29687@0" Pin0InfoVect0LinkObjId="SW-195238_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36e1190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1142,-478 1155,-478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36e0f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1219,-478 1191,-478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="29685@x" ObjectIDND1="29684@x" ObjectIDZND0="29687@1" Pin0InfoVect0LinkObjId="SW-195238_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-195236_0" Pin1InfoVect1LinkObjId="SW-195235_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1219,-478 1191,-478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34c3260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1219,-388 1190,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="29684@x" ObjectIDND1="29686@x" ObjectIDZND0="29688@1" Pin0InfoVect0LinkObjId="SW-195239_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-195235_0" Pin1InfoVect1LinkObjId="SW-195237_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1219,-388 1190,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34b9780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1141,-388 1154,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_34c34c0@0" ObjectIDZND0="29688@0" Pin0InfoVect0LinkObjId="SW-195239_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34c34c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1141,-388 1154,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34b99e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1219,-511 1219,-478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="29685@0" ObjectIDZND0="29687@x" ObjectIDZND1="29684@x" Pin0InfoVect0LinkObjId="SW-195238_0" Pin0InfoVect1LinkObjId="SW-195235_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195236_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1219,-511 1219,-478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34b9c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1219,-478 1219,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="29687@x" ObjectIDND1="29685@x" ObjectIDZND0="29684@1" Pin0InfoVect0LinkObjId="SW-195235_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-195238_0" Pin1InfoVect1LinkObjId="SW-195236_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1219,-478 1219,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34e4a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1219,-418 1219,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29684@0" ObjectIDZND0="29688@x" ObjectIDZND1="29686@x" Pin0InfoVect0LinkObjId="SW-195239_0" Pin0InfoVect1LinkObjId="SW-195237_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195235_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1219,-418 1219,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34e4ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1219,-388 1219,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="29688@x" ObjectIDND1="29684@x" ObjectIDZND0="29686@1" Pin0InfoVect0LinkObjId="SW-195237_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-195239_0" Pin1InfoVect1LinkObjId="SW-195235_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1219,-388 1219,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34f3480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1140,-279 1153,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_34f3940@0" ObjectIDZND0="29689@0" Pin0InfoVect0LinkObjId="SW-195240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34f3940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1140,-279 1153,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34f36e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1219,-279 1189,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="29686@x" ObjectIDND1="29697@x" ObjectIDZND0="29689@1" Pin0InfoVect0LinkObjId="SW-195240_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-195237_0" Pin1InfoVect1LinkObjId="g_3657020_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1219,-279 1189,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3656dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1219,-318 1219,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="29686@0" ObjectIDZND0="29689@x" ObjectIDZND1="29697@x" Pin0InfoVect0LinkObjId="SW-195240_0" Pin0InfoVect1LinkObjId="g_3657020_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195237_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1219,-318 1219,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3657020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1219,-279 1219,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="29689@x" ObjectIDND1="29686@x" ObjectIDZND0="29697@1" Pin0InfoVect0LinkObjId="g_30dbd00_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-195240_0" Pin1InfoVect1LinkObjId="SW-195237_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1219,-279 1219,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3464d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1178,-221 1178,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="g_34ec300@0" ObjectIDZND0="29690@x" ObjectIDZND1="g_3808bc0@0" ObjectIDZND2="29697@x" Pin0InfoVect0LinkObjId="SW-195241_0" Pin0InfoVect1LinkObjId="g_3808bc0_0" Pin0InfoVect2LinkObjId="g_3657020_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34ec300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1178,-221 1178,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_30dbaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1147,-242 1178,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="29690@x" ObjectIDND1="g_3808bc0@0" ObjectIDZND0="g_34ec300@0" ObjectIDZND1="29697@x" Pin0InfoVect0LinkObjId="g_34ec300_0" Pin0InfoVect1LinkObjId="g_3657020_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-195241_0" Pin1InfoVect1LinkObjId="g_3808bc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1147,-242 1178,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_30dbd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1178,-242 1218,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_34ec300@0" ObjectIDND1="29690@x" ObjectIDND2="g_3808bc0@0" ObjectIDZND0="29697@x" Pin0InfoVect0LinkObjId="g_3657020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_34ec300_0" Pin1InfoVect1LinkObjId="SW-195241_0" Pin1InfoVect2LinkObjId="g_3808bc0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1178,-242 1218,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_370cb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1219,-78 1219,-65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29696@0" ObjectIDZND0="29705@1" Pin0InfoVect0LinkObjId="SW-195251_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1219,-78 1219,-65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_370cde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1219,-48 1219,-7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29705@0" ObjectIDZND0="29665@0" Pin0InfoVect0LinkObjId="g_36f1c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195251_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1219,-48 1219,-7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_370d040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1219,-118 1219,-105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29706@1" ObjectIDZND0="29696@1" Pin0InfoVect0LinkObjId="SW-195250_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195251_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1219,-118 1219,-105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_30d5500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="535,-479 548,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_30d59c0@0" ObjectIDZND0="29676@0" Pin0InfoVect0LinkObjId="SW-195173_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30d59c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="535,-479 548,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_30d5760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="612,-479 584,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="29673@x" ObjectIDND1="29674@x" ObjectIDZND0="29676@1" Pin0InfoVect0LinkObjId="SW-195173_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-195170_0" Pin1InfoVect1LinkObjId="SW-195171_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="612,-479 584,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3702890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="612,-389 583,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="29675@x" ObjectIDND1="29673@x" ObjectIDZND0="29677@1" Pin0InfoVect0LinkObjId="SW-195174_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-195172_0" Pin1InfoVect1LinkObjId="SW-195170_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="612,-389 583,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3468130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="534,-389 547,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3702af0@0" ObjectIDZND0="29677@0" Pin0InfoVect0LinkObjId="SW-195174_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3702af0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="534,-389 547,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3468390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="612,-512 612,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29674@0" ObjectIDZND0="29673@x" ObjectIDZND1="29676@x" Pin0InfoVect0LinkObjId="SW-195170_0" Pin0InfoVect1LinkObjId="SW-195173_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195171_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="612,-512 612,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34685f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="612,-479 612,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="29674@x" ObjectIDND1="29676@x" ObjectIDZND0="29673@1" Pin0InfoVect0LinkObjId="SW-195170_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-195171_0" Pin1InfoVect1LinkObjId="SW-195173_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="612,-479 612,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3468850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="612,-419 612,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29673@0" ObjectIDZND0="29675@x" ObjectIDZND1="29677@x" Pin0InfoVect0LinkObjId="SW-195172_0" Pin0InfoVect1LinkObjId="SW-195174_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="612,-419 612,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3468ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="612,-389 612,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="29673@x" ObjectIDND1="29677@x" ObjectIDZND0="29675@1" Pin0InfoVect0LinkObjId="SW-195172_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-195170_0" Pin1InfoVect1LinkObjId="SW-195174_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="612,-389 612,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3705cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="533,-280 546,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3706170@0" ObjectIDZND0="29678@0" Pin0InfoVect0LinkObjId="SW-195175_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3706170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="533,-280 546,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3705f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="612,-280 582,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="29675@x" ObjectIDND1="29683@x" ObjectIDZND0="29678@1" Pin0InfoVect0LinkObjId="SW-195175_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-195172_0" Pin1InfoVect1LinkObjId="g_36fde10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="612,-280 582,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36fdbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="612,-319 612,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="29675@0" ObjectIDZND0="29678@x" ObjectIDZND1="29683@x" Pin0InfoVect0LinkObjId="SW-195175_0" Pin0InfoVect1LinkObjId="g_36fde10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195172_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="612,-319 612,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36fde10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="612,-280 612,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="29675@x" ObjectIDND1="29678@x" ObjectIDZND0="29683@1" Pin0InfoVect0LinkObjId="g_30cc650_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-195172_0" Pin1InfoVect1LinkObjId="SW-195175_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="612,-280 612,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36ffad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="571,-219 571,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="transformer2" ObjectIDND0="g_36feda0@0" ObjectIDZND0="g_3805330@0" ObjectIDZND1="29679@x" ObjectIDZND2="29683@x" Pin0InfoVect0LinkObjId="g_3805330_0" Pin0InfoVect1LinkObjId="SW-195176_0" Pin0InfoVect2LinkObjId="g_36fde10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36feda0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="571,-219 571,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36ffd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="540,-228 540,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="g_3805330@0" ObjectIDZND0="29679@x" ObjectIDZND1="g_36feda0@0" ObjectIDZND2="29683@x" Pin0InfoVect0LinkObjId="SW-195176_0" Pin0InfoVect1LinkObjId="g_36feda0_0" Pin0InfoVect2LinkObjId="g_36fde10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3805330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="540,-228 540,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_36fff90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="540,-240 514,-240 514,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="transformer2" EndDevType0="switch" ObjectIDND0="g_3805330@0" ObjectIDND1="g_36feda0@0" ObjectIDND2="29683@x" ObjectIDZND0="29679@1" Pin0InfoVect0LinkObjId="SW-195176_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3805330_0" Pin1InfoVect1LinkObjId="g_36feda0_0" Pin1InfoVect2LinkObjId="g_36fde10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="540,-240 514,-240 514,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_30cc3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="540,-240 571,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="g_3805330@0" ObjectIDND1="29679@x" ObjectIDZND0="g_36feda0@0" ObjectIDZND1="29683@x" Pin0InfoVect0LinkObjId="g_36feda0_0" Pin0InfoVect1LinkObjId="g_36fde10_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3805330_0" Pin1InfoVect1LinkObjId="SW-195176_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="540,-240 571,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_30cc650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="571,-240 611,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="transformer2" ObjectIDND0="g_36feda0@0" ObjectIDND1="g_3805330@0" ObjectIDND2="29679@x" ObjectIDZND0="29683@x" Pin0InfoVect0LinkObjId="g_36fde10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_36feda0_0" Pin1InfoVect1LinkObjId="g_3805330_0" Pin1InfoVect2LinkObjId="SW-195176_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="571,-240 611,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_370efa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="612,-78 612,-65 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29680@0" ObjectIDZND0="29681@1" Pin0InfoVect0LinkObjId="SW-195186_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195185_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="612,-78 612,-65 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_370f200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="612,-48 612,-5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29681@0" ObjectIDZND0="29664@0" Pin0InfoVect0LinkObjId="g_36eb2f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195186_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="612,-48 612,-5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_370f460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="612,-118 612,-105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29682@1" ObjectIDZND0="29680@1" Pin0InfoVect0LinkObjId="SW-195185_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195186_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="612,-118 612,-105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3719bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1001,155 1001,175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1001,155 1001,175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4c90620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1001,110 1001,128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1001,110 1001,128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4c93510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1154,179 1114,179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_4c93770@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4c93770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1154,179 1114,179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36e8050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="826,65 826,78 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="826,65 826,78 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36e82b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="826,28 826,38 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="826,28 826,38 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36eb2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="826,11 826,-5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="29664@0" Pin0InfoVect0LinkObjId="g_370f200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="826,11 826,-5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36eebc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="934,24 934,76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="934,24 934,76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_36f1c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="934,9 934,-7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="29665@0" Pin0InfoVect0LinkObjId="g_370cde0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="934,9 934,-7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_382e610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="934,93 934,114 826,114 826,95 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="934,93 934,114 826,114 826,95 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_382e860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="928,205 941,205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_382ed20@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_382ed20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="928,205 941,205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_382eac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1002,203 979,204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1002,203 979,204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3832480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1001,192 1001,204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1001,192 1001,204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_38326e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1001,204 1001,275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1001,204 1001,275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_38349b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1271,95 1271,115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1271,95 1271,115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3834c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1271,50 1271,68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1271,50 1271,68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3837c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1271,33 1271,-7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="29665@0" Pin0InfoVect0LinkObjId="g_370cde0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1271,33 1271,-7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3837eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1272,178 1252,178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_4d009d0@0" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_4d009d0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1272,178 1252,178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3838110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1271,132 1271,178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_4d009d0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_4d009d0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1271,132 1271,178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3838370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1203,178 1216,178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_38385d0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38385d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1203,178 1216,178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_383f080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1115,96 1115,116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1115,96 1115,116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_383f2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1115,51 1115,69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1115,51 1115,69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4cf5fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1115,34 1115,-7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="29665@0" Pin0InfoVect0LinkObjId="g_370cde0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1115,34 1115,-7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4cf6230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1116,179 1100,179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_4c93770@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_4c93770_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1116,179 1100,179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4cf6490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1115,133 1115,179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_4c93770@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_4c93770_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1115,133 1115,179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4cf66f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1115,179 1115,250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="g_4c93770@0" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_4c93770_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1115,179 1115,250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4cf6950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1051,179 1064,179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_4cf6bb0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4cf6bb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1051,179 1064,179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4d01700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1309,178 1269,178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_4d009d0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4d009d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1309,178 1269,178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4d082c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1354,81 1354,68 1408,68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="g_4cffca0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_4d05140@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_4d05140_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4cffca0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1354,81 1354,68 1408,68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4d08db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1408,68 1408,179 1359,179 1360,173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_4cffca0@0" ObjectIDND1="0@x" ObjectIDZND0="g_4d05140@0" Pin0InfoVect0LinkObjId="g_4d05140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4cffca0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1408,68 1408,179 1359,179 1360,173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d0be00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1408,-7 1408,20 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29665@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_370cde0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1408,-7 1408,20 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4d0c060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1408,37 1408,68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="g_4cffca0@0" ObjectIDZND1="g_4d05140@0" Pin0InfoVect0LinkObjId="g_4cffca0_0" Pin0InfoVect1LinkObjId="g_4d05140_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1408,37 1408,68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4d11530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="362,139 362,152 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="362,139 362,152 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4d11790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="362,102 362,112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="362,102 362,112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4d147d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="515,181 475,181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_4d14a30@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4d14a30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="515,181 475,181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4d171c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="362,181 362,252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="362,181 362,252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4c98660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,97 632,117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="632,97 632,117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4c988c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,52 632,70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="632,52 632,70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4c9b900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,35 632,-5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="29664@0" Pin0InfoVect0LinkObjId="g_370f200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="632,35 632,-5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4c9bb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="633,180 613,180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_4caf5f0@0" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_4caf5f0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="633,180 613,180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4c9bdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,134 632,180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_4caf5f0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_4caf5f0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="632,134 632,180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4c9c020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="564,180 577,180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_4c9c280@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4c9c280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="564,180 577,180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4ca2280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="476,98 476,118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="476,98 476,118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4ca24e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="476,53 476,71 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="476,53 476,71 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4ca5520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="476,36 476,-5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="29664@0" Pin0InfoVect0LinkObjId="g_370f200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="476,36 476,-5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4ca5780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="477,181 461,181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_4d14a30@0" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4d14a30_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="477,181 461,181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4ca59e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="476,135 476,181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_4d14a30@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_4d14a30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="476,135 476,181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4ca5c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="475,181 475,252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="g_4d14a30@0" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_4d14a30_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="475,181 475,252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4ca5ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="412,181 425,181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_4ca6100@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4ca6100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="412,181 425,181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4cb0320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="670,180 630,180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_4caf5f0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4caf5f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="670,180 630,180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4cb3130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="715,83 715,70 769,70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" ObjectIDND0="g_4cae170@0" ObjectIDZND0="g_4cb0580@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_4cb0580_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4cae170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="715,83 715,70 769,70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4cb3390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="769,70 769,181 720,181 721,175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_4cae170@0" ObjectIDND1="0@x" ObjectIDZND0="g_4cb0580@0" Pin0InfoVect0LinkObjId="g_4cb0580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4cae170_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="769,70 769,181 720,181 721,175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4cb63e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="769,-5 769,22 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29664@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_370f200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="769,-5 769,22 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4cb6640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="769,39 769,70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_4cb0580@0" ObjectIDZND1="g_4cae170@0" Pin0InfoVect0LinkObjId="g_4cb0580_0" Pin0InfoVect1LinkObjId="g_4cae170_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="769,39 769,70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4cb9c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="294,181 307,181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_4cba0d0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4cba0d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="294,181 307,181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4cb9e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="363,181 343,181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="363,181 343,181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_38050d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="362,169 362,181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="362,169 362,181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3805f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="514,-197 514,-188 540,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="29679@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195176_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="514,-197 514,-188 540,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3806160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1147,-242 1121,-242 1121,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_34ec300@0" ObjectIDND1="29697@x" ObjectIDND2="g_3808bc0@0" ObjectIDZND0="29690@1" Pin0InfoVect0LinkObjId="SW-195241_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_34ec300_0" Pin1InfoVect1LinkObjId="g_3657020_0" Pin1InfoVect2LinkObjId="g_3808bc0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1147,-242 1121,-242 1121,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3809790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1121,-199 1121,-190 1147,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="29690@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195241_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1121,-199 1121,-190 1147,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38099f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1147,-230 1147,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer2" EndDevType2="switch" ObjectIDND0="g_3808bc0@0" ObjectIDZND0="g_34ec300@0" ObjectIDZND1="29697@x" ObjectIDZND2="29690@x" Pin0InfoVect0LinkObjId="g_34ec300_0" Pin0InfoVect1LinkObjId="g_3657020_0" Pin0InfoVect2LinkObjId="SW-195241_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3808bc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1147,-230 1147,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_380c3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="841,-856 854,-856 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_380c8b0@0" ObjectIDZND0="29672@0" Pin0InfoVect0LinkObjId="SW-195119_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_380c8b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="841,-856 854,-856 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_380c650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,-856 890,-856 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="29669@x" ObjectIDND1="0@x" ObjectIDND2="g_381fa30@0" ObjectIDZND0="29672@1" Pin0InfoVect0LinkObjId="SW-195119_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-195116_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_381fa30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="918,-856 890,-856 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_380f870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,-698 889,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="29667@x" ObjectIDND1="29668@x" ObjectIDZND0="29670@1" Pin0InfoVect0LinkObjId="SW-195117_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-195113_0" Pin1InfoVect1LinkObjId="SW-195115_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="918,-698 889,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3812a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="840,-698 853,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_380fad0@0" ObjectIDZND0="29670@0" Pin0InfoVect0LinkObjId="SW-195117_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_380fad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="840,-698 853,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3812cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,-728 918,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29667@0" ObjectIDZND0="29670@x" ObjectIDZND1="29668@x" Pin0InfoVect0LinkObjId="SW-195117_0" Pin0InfoVect1LinkObjId="SW-195115_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195113_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="918,-728 918,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3818940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,-698 918,-661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="29670@x" ObjectIDND1="29667@x" ObjectIDZND0="29668@1" Pin0InfoVect0LinkObjId="SW-195115_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-195117_0" Pin1InfoVect1LinkObjId="SW-195113_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="918,-698 918,-661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3818ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,-777 890,-777 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="29667@x" ObjectIDND1="29669@x" ObjectIDZND0="29671@1" Pin0InfoVect0LinkObjId="SW-195118_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-195113_0" Pin1InfoVect1LinkObjId="SW-195116_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="918,-777 890,-777 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_381bdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="841,-777 854,-777 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3818e00@0" ObjectIDZND0="29671@0" Pin0InfoVect0LinkObjId="SW-195118_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3818e00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="841,-777 854,-777 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_381c8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,-777 918,-755 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="29671@x" ObjectIDND1="29669@x" ObjectIDZND0="29667@1" Pin0InfoVect0LinkObjId="SW-195113_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-195118_0" Pin1InfoVect1LinkObjId="SW-195116_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="918,-777 918,-755 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_381f310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,-858 918,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="29672@x" ObjectIDND1="0@x" ObjectIDND2="g_381fa30@0" ObjectIDZND0="29669@1" Pin0InfoVect0LinkObjId="SW-195116_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-195119_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_381fa30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="918,-858 918,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_381f570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,-798 918,-777 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="29669@0" ObjectIDZND0="29671@x" ObjectIDZND1="29667@x" Pin0InfoVect0LinkObjId="SW-195118_0" Pin0InfoVect1LinkObjId="SW-195113_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195116_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="918,-798 918,-777 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_381f7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="959,-913 919,-913 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_381fa30@0" ObjectIDZND0="0@x" ObjectIDZND1="29672@x" ObjectIDZND2="29669@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-195119_0" Pin0InfoVect2LinkObjId="SW-195116_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_381fa30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="959,-913 919,-913 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3820ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,-946 918,-913 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="capacitor" EndDevType2="switch" ObjectIDND0="37736@1" ObjectIDZND0="g_381fa30@0" ObjectIDZND1="0@x" ObjectIDZND2="29672@x" Pin0InfoVect0LinkObjId="g_381fa30_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-195119_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="918,-946 918,-913 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3823d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="866,-896 918,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_381fa30@0" ObjectIDZND1="37736@1" ObjectIDZND2="29672@x" Pin0InfoVect0LinkObjId="g_381fa30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="SW-195119_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="866,-896 918,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3823f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="762,-896 783,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="capacitor" ObjectIDND0="g_3823270@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3823270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="762,-896 783,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3824a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,-913 918,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="capacitor" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_381fa30@0" ObjectIDND1="37736@1" ObjectIDZND0="0@x" ObjectIDZND1="29672@x" ObjectIDZND2="29669@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-195119_0" Pin0InfoVect2LinkObjId="SW-195116_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_381fa30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="918,-913 918,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3824cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,-896 918,-856 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_381fa30@0" ObjectIDND2="37736@1" ObjectIDZND0="29672@x" ObjectIDZND1="29669@x" Pin0InfoVect0LinkObjId="SW-195119_0" Pin0InfoVect1LinkObjId="SW-195116_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_381fa30_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="918,-896 918,-856 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3824f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="825,-919 825,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="capacitor" EndDevType1="capacitor" ObjectIDND0="g_3821250@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3821250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="825,-919 825,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3825a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="812,-896 825,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="voltageTransformer" EndDevType1="capacitor" ObjectIDND0="0@1" ObjectIDZND0="g_3821250@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_3821250_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="812,-896 825,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3825c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="825,-896 837,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="capacitor" EndDevType0="capacitor" ObjectIDND0="g_3821250@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3821250_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="825,-896 837,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4cc6720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="633,326 633,300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="633,326 633,300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4cc6910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="633,264 633,180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_4caf5f0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_4caf5f0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="633,264 633,180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4cc6b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1272,325 1272,299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1272,325 1272,299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4ccb630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1299,251 1272,251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1299,251 1272,251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4ccc120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1272,263 1272,251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1272,263 1272,251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4ccc380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1272,251 1272,178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_4d009d0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_4d009d0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1272,251 1272,178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4ccc5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1335,251 1344,251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_4ccc840@0" Pin0InfoVect0LinkObjId="g_4ccc840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1335,251 1344,251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4ccfd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="660,263 633,263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_4caf5f0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_4caf5f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="660,263 633,263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4ccfff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="633,275 633,263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="633,275 633,263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4cd0250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="696,263 705,263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_4cd04b0@0" Pin0InfoVect0LinkObjId="g_4cd04b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="696,263 705,263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4ce9f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1219,-182 1219,-162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="29697@0" ObjectIDZND0="29706@x" ObjectIDZND1="g_3657280@0" Pin0InfoVect0LinkObjId="SW-195251_0" Pin0InfoVect1LinkObjId="g_3657280_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3657020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1219,-182 1219,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4cea9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1219,-135 1219,-162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="29706@0" ObjectIDZND0="g_3657280@0" ObjectIDZND1="29697@x" Pin0InfoVect0LinkObjId="g_3657280_0" Pin0InfoVect1LinkObjId="g_3657020_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195251_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1219,-135 1219,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4ceac50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1219,-162 1259,-162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="29706@x" ObjectIDND1="29697@x" ObjectIDZND0="g_3657280@0" Pin0InfoVect0LinkObjId="g_3657280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-195251_0" Pin1InfoVect1LinkObjId="g_3657020_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1219,-162 1259,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4ceaeb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="612,-163 612,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_36fe070@0" ObjectIDND1="29682@x" ObjectIDZND0="29683@0" Pin0InfoVect0LinkObjId="g_36fde10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_36fe070_0" Pin1InfoVect1LinkObjId="SW-195186_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="612,-163 612,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4ceb9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="652,-163 612,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="g_36fe070@0" ObjectIDZND0="29682@x" ObjectIDZND1="29683@x" Pin0InfoVect0LinkObjId="SW-195186_0" Pin0InfoVect1LinkObjId="g_36fde10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36fe070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="652,-163 612,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4cebc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="612,-163 612,-135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_36fe070@0" ObjectIDND1="29683@x" ObjectIDZND0="29682@0" Pin0InfoVect0LinkObjId="SW-195186_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_36fe070_0" Pin1InfoVect1LinkObjId="g_36fde10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="612,-163 612,-135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_387c400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1684,-476 1697,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_387c8c0@0" ObjectIDZND0="39413@0" Pin0InfoVect0LinkObjId="SW-236586_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_387c8c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1684,-476 1697,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_387c660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1761,-476 1733,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="39411@x" ObjectIDND1="39409@x" ObjectIDZND0="39413@1" Pin0InfoVect0LinkObjId="SW-236586_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-236584_0" Pin1InfoVect1LinkObjId="SW-236583_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1761,-476 1733,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_387f880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1761,-386 1732,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="39409@x" ObjectIDND1="39412@x" ObjectIDZND0="39415@1" Pin0InfoVect0LinkObjId="SW-236587_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-236583_0" Pin1InfoVect1LinkObjId="SW-236585_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1761,-386 1732,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3882aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1683,-386 1696,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_387fae0@0" ObjectIDZND0="39415@0" Pin0InfoVect0LinkObjId="SW-236587_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_387fae0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1683,-386 1696,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3882d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1761,-508 1761,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="39411@0" ObjectIDZND0="39413@x" ObjectIDZND1="39409@x" Pin0InfoVect0LinkObjId="SW-236586_0" Pin0InfoVect1LinkObjId="SW-236583_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236584_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1761,-508 1761,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3882f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1761,-476 1761,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="39413@x" ObjectIDND1="39411@x" ObjectIDZND0="39409@1" Pin0InfoVect0LinkObjId="SW-236583_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-236586_0" Pin1InfoVect1LinkObjId="SW-236584_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1761,-476 1761,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38831c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1761,-413 1761,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="39409@0" ObjectIDZND0="39415@x" ObjectIDZND1="39412@x" Pin0InfoVect0LinkObjId="SW-236587_0" Pin0InfoVect1LinkObjId="SW-236585_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236583_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1761,-413 1761,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3883420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1761,-386 1761,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="39415@x" ObjectIDND1="39409@x" ObjectIDZND0="39412@1" Pin0InfoVect0LinkObjId="SW-236585_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-236587_0" Pin1InfoVect1LinkObjId="SW-236583_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1761,-386 1761,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3885bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1682,-277 1695,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3886070@0" ObjectIDZND0="39414@0" Pin0InfoVect0LinkObjId="SW-236588_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3886070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1682,-277 1695,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3885e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1761,-277 1731,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="39412@x" ObjectIDND1="39429@x" ObjectIDZND0="39414@1" Pin0InfoVect0LinkObjId="SW-236588_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-236585_0" Pin1InfoVect1LinkObjId="g_3886d60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1761,-277 1731,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3886b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1761,-315 1761,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="39412@0" ObjectIDZND0="39414@x" ObjectIDZND1="39429@x" Pin0InfoVect0LinkObjId="SW-236588_0" Pin0InfoVect1LinkObjId="g_3886d60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236585_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1761,-315 1761,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3886d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1761,-277 1760,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="39414@x" ObjectIDND1="39412@x" ObjectIDZND0="39429@1" Pin0InfoVect0LinkObjId="g_3888ee0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-236588_0" Pin1InfoVect1LinkObjId="SW-236585_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1761,-277 1760,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3888a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1720,-219 1720,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="g_3887cf0@0" ObjectIDZND0="39418@x" ObjectIDZND1="g_3894040@0" ObjectIDZND2="39429@x" Pin0InfoVect0LinkObjId="SW-236592_0" Pin0InfoVect1LinkObjId="g_3894040_0" Pin0InfoVect2LinkObjId="g_3886d60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3887cf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1720,-219 1720,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3888c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1689,-240 1720,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="39418@x" ObjectIDND1="g_3894040@0" ObjectIDZND0="g_3887cf0@0" ObjectIDZND1="39429@x" Pin0InfoVect0LinkObjId="g_3887cf0_0" Pin0InfoVect1LinkObjId="g_3886d60_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-236592_0" Pin1InfoVect1LinkObjId="g_3894040_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1689,-240 1720,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3888ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1720,-240 1760,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="transformer2" ObjectIDND0="39418@x" ObjectIDND1="g_3894040@0" ObjectIDND2="g_3887cf0@0" ObjectIDZND0="39429@x" Pin0InfoVect0LinkObjId="g_3886d60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-236592_0" Pin1InfoVect1LinkObjId="g_3894040_0" Pin1InfoVect2LinkObjId="g_3887cf0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1720,-240 1760,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3891120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1761,-77 1761,-63 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39410@0" ObjectIDZND0="39416@1" Pin0InfoVect0LinkObjId="SW-236591_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1761,-77 1761,-63 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3891380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1761,-116 1761,-104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39417@1" ObjectIDZND0="39410@1" Pin0InfoVect0LinkObjId="SW-236590_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236591_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1761,-116 1761,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38915e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1689,-240 1663,-240 1663,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_3887cf0@0" ObjectIDND1="39429@x" ObjectIDND2="g_3894040@0" ObjectIDZND0="39418@1" Pin0InfoVect0LinkObjId="SW-236592_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3887cf0_0" Pin1InfoVect1LinkObjId="g_3886d60_0" Pin1InfoVect2LinkObjId="g_3894040_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1689,-240 1663,-240 1663,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3894c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1663,-197 1663,-188 1689,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="39418@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236592_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1663,-197 1663,-188 1689,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3894e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1689,-228 1689,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="g_3894040@0" ObjectIDZND0="39418@x" ObjectIDZND1="g_3887cf0@0" ObjectIDZND2="39429@x" Pin0InfoVect0LinkObjId="SW-236592_0" Pin0InfoVect1LinkObjId="g_3887cf0_0" Pin0InfoVect2LinkObjId="g_3886d60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3894040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1689,-228 1689,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3896e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1760,-177 1761,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="39429@0" ObjectIDZND0="39417@x" ObjectIDZND1="g_3886fc0@0" Pin0InfoVect0LinkObjId="SW-236591_0" Pin0InfoVect1LinkObjId="g_3886fc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3886d60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1760,-177 1761,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3897050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1761,-133 1761,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="39417@0" ObjectIDZND0="g_3886fc0@0" ObjectIDZND1="39429@x" Pin0InfoVect0LinkObjId="g_3886fc0_0" Pin0InfoVect1LinkObjId="g_3886d60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236591_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1761,-133 1761,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3897240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1761,-160 1801,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="39417@x" ObjectIDND1="39429@x" ObjectIDZND0="g_3886fc0@0" Pin0InfoVect0LinkObjId="g_3886fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-236591_0" Pin1InfoVect1LinkObjId="g_3886d60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1761,-160 1801,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38a0550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2391,-480 2404,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_38a0a10@0" ObjectIDZND0="39423@0" Pin0InfoVect0LinkObjId="SW-236613_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38a0a10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2391,-480 2404,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38a07b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2468,-480 2440,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="39421@x" ObjectIDND1="39419@x" ObjectIDZND0="39423@1" Pin0InfoVect0LinkObjId="SW-236613_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-236611_0" Pin1InfoVect1LinkObjId="SW-236610_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2468,-480 2440,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38a39d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2468,-390 2439,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="39422@x" ObjectIDND1="39419@x" ObjectIDZND0="39425@1" Pin0InfoVect0LinkObjId="SW-236614_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-236612_0" Pin1InfoVect1LinkObjId="SW-236610_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2468,-390 2439,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38a6bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2390,-390 2403,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_38a3c30@0" ObjectIDZND0="39425@0" Pin0InfoVect0LinkObjId="SW-236614_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38a3c30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2390,-390 2403,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38a6e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2468,-513 2468,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="39421@0" ObjectIDZND0="39423@x" ObjectIDZND1="39419@x" Pin0InfoVect0LinkObjId="SW-236613_0" Pin0InfoVect1LinkObjId="SW-236610_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236611_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2468,-513 2468,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38a70b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2468,-480 2467,-444 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="39421@x" ObjectIDND1="39423@x" ObjectIDZND0="39419@1" Pin0InfoVect0LinkObjId="SW-236610_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-236611_0" Pin1InfoVect1LinkObjId="SW-236613_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2468,-480 2467,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38a7310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2467,-417 2468,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="39419@0" ObjectIDZND0="39425@x" ObjectIDZND1="39422@x" Pin0InfoVect0LinkObjId="SW-236614_0" Pin0InfoVect1LinkObjId="SW-236612_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2467,-417 2468,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38a7570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2468,-390 2468,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="39425@x" ObjectIDND1="39419@x" ObjectIDZND0="39422@1" Pin0InfoVect0LinkObjId="SW-236612_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-236614_0" Pin1InfoVect1LinkObjId="SW-236610_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2468,-390 2468,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38a9d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2389,-281 2402,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_38aa1c0@0" ObjectIDZND0="39424@0" Pin0InfoVect0LinkObjId="SW-236615_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38aa1c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2389,-281 2402,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38a9f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2468,-281 2438,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="39422@x" ObjectIDND1="39430@x" ObjectIDZND0="39424@1" Pin0InfoVect0LinkObjId="SW-236615_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-236612_0" Pin1InfoVect1LinkObjId="g_38aaeb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2468,-281 2438,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38aac50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2468,-320 2468,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="39422@0" ObjectIDZND0="39424@x" ObjectIDZND1="39430@x" Pin0InfoVect0LinkObjId="SW-236615_0" Pin0InfoVect1LinkObjId="g_38aaeb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236612_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2468,-320 2468,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38aaeb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2468,-281 2468,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="39422@x" ObjectIDND1="39424@x" ObjectIDZND0="39430@1" Pin0InfoVect0LinkObjId="g_38ad030_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-236612_0" Pin1InfoVect1LinkObjId="SW-236615_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2468,-281 2468,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38acb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2427,-223 2427,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="g_38abe40@0" ObjectIDZND0="39428@x" ObjectIDZND1="g_38b8180@0" ObjectIDZND2="39430@x" Pin0InfoVect0LinkObjId="SW-236618_0" Pin0InfoVect1LinkObjId="g_38b8180_0" Pin0InfoVect2LinkObjId="g_38aaeb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38abe40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2427,-223 2427,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38acdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2396,-244 2427,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="39428@x" ObjectIDND1="g_38b8180@0" ObjectIDZND0="g_38abe40@0" ObjectIDZND1="39430@x" Pin0InfoVect0LinkObjId="g_38abe40_0" Pin0InfoVect1LinkObjId="g_38aaeb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-236618_0" Pin1InfoVect1LinkObjId="g_38b8180_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2396,-244 2427,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38ad030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2427,-244 2467,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_38abe40@0" ObjectIDND1="39428@x" ObjectIDND2="g_38b8180@0" ObjectIDZND0="39430@x" Pin0InfoVect0LinkObjId="g_38aaeb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_38abe40_0" Pin1InfoVect1LinkObjId="SW-236618_0" Pin1InfoVect2LinkObjId="g_38b8180_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2427,-244 2467,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38b5260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2468,-80 2468,-67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39420@0" ObjectIDZND0="39426@1" Pin0InfoVect0LinkObjId="SW-236617_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236645_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2468,-80 2468,-67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38b54c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2468,-120 2468,-107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39427@1" ObjectIDZND0="39420@1" Pin0InfoVect0LinkObjId="SW-236645_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236617_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2468,-120 2468,-107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38b5720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2396,-244 2370,-244 2370,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_38abe40@0" ObjectIDND1="39430@x" ObjectIDND2="g_38b8180@0" ObjectIDZND0="39428@1" Pin0InfoVect0LinkObjId="SW-236618_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_38abe40_0" Pin1InfoVect1LinkObjId="g_38aaeb0_0" Pin1InfoVect2LinkObjId="g_38b8180_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2396,-244 2370,-244 2370,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38b8d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2370,-201 2370,-192 2396,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="39428@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236618_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2370,-201 2370,-192 2396,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38b8fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2396,-232 2396,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="transformer2" EndDevType2="switch" ObjectIDND0="g_38b8180@0" ObjectIDZND0="g_38abe40@0" ObjectIDZND1="39430@x" ObjectIDZND2="39428@x" Pin0InfoVect0LinkObjId="g_38abe40_0" Pin0InfoVect1LinkObjId="g_38aaeb0_0" Pin0InfoVect2LinkObjId="SW-236618_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38b8180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2396,-232 2396,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38bb1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2468,-184 2468,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="39430@0" ObjectIDZND0="39427@x" ObjectIDZND1="g_38ab110@0" Pin0InfoVect0LinkObjId="SW-236617_0" Pin0InfoVect1LinkObjId="g_38ab110_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38aaeb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2468,-184 2468,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38bb3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2468,-137 2468,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="39427@0" ObjectIDZND0="39430@x" ObjectIDZND1="g_38ab110@0" Pin0InfoVect0LinkObjId="g_38aaeb0_0" Pin0InfoVect1LinkObjId="g_38ab110_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236617_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2468,-137 2468,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38bb5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2468,-164 2508,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="39430@x" ObjectIDND1="39427@x" ObjectIDZND0="g_38ab110@0" Pin0InfoVect0LinkObjId="g_38ab110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_38aaeb0_0" Pin1InfoVect1LinkObjId="SW-236617_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2468,-164 2508,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38bdcd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2299,32 2299,-9 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="39432@0" Pin0InfoVect0LinkObjId="g_38c9580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2299,32 2299,-9 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_38bffa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2102,63 2102,76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2102,63 2102,76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_38c0200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2102,26 2102,36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2102,26 2102,36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38c3240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2102,9 2102,-7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="39431@0" Pin0InfoVect0LinkObjId="g_376ae30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2102,9 2102,-7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_38c6540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2210,22 2210,74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2210,22 2210,74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38c9580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2210,7 2210,-9 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="39432@0" Pin0InfoVect0LinkObjId="g_38bdcd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2210,7 2210,-9 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_38cc880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2210,91 2210,112 2102,112 2102,93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2210,91 2210,112 2102,112 2102,93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38ccaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2554,31 2554,-9 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="39432@0" Pin0InfoVect0LinkObjId="g_38bdcd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2554,31 2554,-9 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38ccd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2734,-9 2734,17 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="39432@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38bdcd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2734,-9 2734,17 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38ce340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="612,-548 612,-599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29674@1" ObjectIDZND0="29663@0" Pin0InfoVect0LinkObjId="g_38ce530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195171_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="612,-548 612,-599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38ce530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="918,-625 918,-599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29668@0" ObjectIDZND0="29663@0" Pin0InfoVect0LinkObjId="g_38ce340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195115_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="918,-625 918,-599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38ce720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1219,-547 1219,-599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29685@1" ObjectIDZND0="29663@0" Pin0InfoVect0LinkObjId="g_38ce340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195236_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1219,-547 1219,-599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38ce910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1291,-662 1291,-599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="29693@x" ObjectIDND1="29691@x" ObjectIDZND0="29663@0" Pin0InfoVect0LinkObjId="g_38ce340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-195319_0" Pin1InfoVect1LinkObjId="SW-195317_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1291,-662 1291,-599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38cf310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1265,-662 1291,-662 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="29693@1" ObjectIDZND0="29663@0" ObjectIDZND1="29691@x" Pin0InfoVect0LinkObjId="g_38ce340_0" Pin0InfoVect1LinkObjId="SW-195317_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195319_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1265,-662 1291,-662 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38cf540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1291,-662 1291,-678 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="29663@0" ObjectIDND1="29693@x" ObjectIDZND0="29691@0" Pin0InfoVect0LinkObjId="SW-195317_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_38ce340_0" Pin1InfoVect1LinkObjId="SW-195319_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1291,-662 1291,-678 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38cf7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1761,-544 1761,-599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39411@1" ObjectIDZND0="29663@0" Pin0InfoVect0LinkObjId="g_38ce340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236584_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1761,-544 1761,-599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_38cfa00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2468,-549 2468,-599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39421@1" ObjectIDZND0="29663@0" Pin0InfoVect0LinkObjId="g_38ce340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236611_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2468,-549 2468,-599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38d13a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2468,-50 2468,-9 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39426@0" ObjectIDZND0="39432@0" Pin0InfoVect0LinkObjId="g_38bdcd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236617_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2468,-50 2468,-9 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_38d3630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1561,97 1561,117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1561,97 1561,117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_38d3890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1561,52 1561,70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1561,52 1561,70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_38d6830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1562,180 1542,180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1562,180 1542,180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_38d6a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1561,134 1561,180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1561,134 1561,180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_38d6cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1493,180 1506,180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_38d6f50@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38d6f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1493,180 1506,180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3734fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1599,180 1559,180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3734280@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3734280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1599,180 1559,180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3738a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1562,326 1562,300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1562,326 1562,300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3738c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1562,264 1562,180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_3734280@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_3734280_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1562,264 1562,180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_373b2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1589,263 1562,263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_3734280@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_3734280_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1589,263 1562,263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_373b530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1562,275 1562,263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1562,275 1562,263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_373b790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1625,263 1634,263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_373b9f0@0" Pin0InfoVect0LinkObjId="g_373b9f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1625,263 1634,263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37400a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1775,180 1735,180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_37402d0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37402d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1775,180 1735,180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3740e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1736,97 1736,117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1736,97 1736,117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37410d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1736,52 1736,70 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1736,52 1736,70 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3744110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1737,180 1721,180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_37402d0@0" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_37402d0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1737,180 1721,180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3744370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1736,134 1736,180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_37402d0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_37402d0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1736,134 1736,180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37445d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1737,180 1737,251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_37402d0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_37402d0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1737,180 1737,251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3744830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1672,180 1685,180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3744a90@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3744a90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1672,180 1685,180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3753d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1817,92 1817,79 1871,79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" ObjectIDND0="g_3750430@0" ObjectIDZND0="g_3751160@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_3751160_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3750430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1817,92 1817,79 1871,79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3753f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1871,79 1871,190 1822,190 1823,184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_3750430@0" ObjectIDND1="0@x" ObjectIDZND0="g_3751160@0" Pin0InfoVect0LinkObjId="g_3751160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3750430_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1871,79 1871,190 1822,190 1823,184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3756fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1871,48 1871,79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_3751160@0" ObjectIDZND1="g_3750430@0" Pin0InfoVect0LinkObjId="g_3751160_0" Pin0InfoVect1LinkObjId="g_3750430_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1871,48 1871,79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_375c920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1987,151 1987,171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1987,151 1987,171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_375cb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1987,106 1987,124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1987,106 1987,124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3761620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1987,234 1987,305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1987,234 1987,305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3761880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1919,234 1932,234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3761d40@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3761d40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1919,234 1932,234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3761ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1988,234 1968,234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1988,234 1968,234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3764ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1987,188 1987,234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1987,188 1987,234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_376ae30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1761,-46 1761,-7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39416@0" ObjectIDZND0="39431@0" Pin0InfoVect0LinkObjId="g_38c3240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-236591_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1761,-46 1761,-7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_376b090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1736,35 1736,-7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="39431@0" Pin0InfoVect0LinkObjId="g_38c3240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1736,35 1736,-7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_376b2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1561,35 1561,-7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="39431@0" Pin0InfoVect0LinkObjId="g_38c3240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1561,35 1561,-7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_376b550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1871,31 1871,-7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="39431@0" Pin0InfoVect0LinkObjId="g_38c3240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1871,31 1871,-7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37735b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2246,95 2246,82 2300,82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" ObjectIDND0="g_376fcd0@0" ObjectIDZND0="g_3770a00@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_3770a00_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_376fcd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2246,95 2246,82 2300,82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3773810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2300,82 2300,193 2251,193 2252,187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_376fcd0@0" ObjectIDND1="0@x" ObjectIDZND0="g_3770a00@0" Pin0InfoVect0LinkObjId="g_3770a00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_376fcd0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2300,82 2300,193 2251,193 2252,187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3776860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2300,51 2300,82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="g_376fcd0@0" ObjectIDZND1="g_3770a00@0" Pin0InfoVect0LinkObjId="g_376fcd0_0" Pin0InfoVect1LinkObjId="g_3770a00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2300,51 2300,82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_377ca50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2414,139 2414,159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2414,139 2414,159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_377ccb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2414,94 2414,112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2414,94 2414,112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3781750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2414,222 2414,293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2414,222 2414,293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37819b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2346,222 2359,222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3781e70@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3781e70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2346,222 2359,222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3781c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2415,222 2395,222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2415,222 2395,222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3784dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2414,176 2414,222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2414,176 2414,222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3788720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2555,91 2555,111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2555,91 2555,111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3788980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2555,46 2555,64 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2555,46 2555,64 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_378b9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2556,174 2536,174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2556,174 2536,174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_378bc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2555,128 2555,174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2555,128 2555,174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_378be80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2487,174 2500,174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_378c0e0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_378c0e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2487,174 2500,174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3792e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2593,174 2553,174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_37920e0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37920e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2593,174 2553,174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37968c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2556,320 2556,294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2556,320 2556,294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3796ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2556,258 2556,174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_37920e0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_37920e0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2556,258 2556,174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3799130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2583,257 2556,257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_37920e0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_37920e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2583,257 2556,257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3799390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2556,269 2556,257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2556,269 2556,257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37995f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2619,257 2628,257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_3799850@0" Pin0InfoVect0LinkObjId="g_3799850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2619,257 2628,257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_379f1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2774,162 2734,162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_379f420@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_379f420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2774,162 2734,162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37a0020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2735,79 2735,99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2735,79 2735,99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37a0280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2735,34 2735,52 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2735,34 2735,52 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37a32c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2736,162 2720,162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_379f420@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_379f420_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2736,162 2720,162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37a3520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2735,116 2735,162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_379f420@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_379f420_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2735,116 2735,162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37a3780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2736,162 2736,233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_379f420@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_379f420_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2736,162 2736,233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37a39e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2671,162 2684,162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_37a3c40@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_37a3c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2671,162 2684,162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37be9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="362,85 362,71 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="362,85 362,71 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37bec00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="362,7 362,-5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49192@1" ObjectIDZND0="29664@0" Pin0InfoVect0LinkObjId="g_370f200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-318634_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="362,7 362,-5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37bf580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="362,52 362,43 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" ObjectIDND0="49196@0" ObjectIDZND0="49192@0" Pin0InfoVect0LinkObjId="SW-318634_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_LJ.CX_LJ_XN1_LD_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="362,52 362,43 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37c1fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1001,4 1001,-7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49193@1" ObjectIDZND0="29665@0" Pin0InfoVect0LinkObjId="g_370cde0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-318635_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1001,4 1001,-7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37c29a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1001,49 1001,40 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" ObjectIDND0="49197@0" ObjectIDZND0="49193@0" Pin0InfoVect0LinkObjId="SW-318635_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_LJ.CX_LJ_XN2_LD_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1001,49 1001,40 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37c31d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1001,93 1001,67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1001,93 1001,67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37c5c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1986,3 1986,-7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49194@1" ObjectIDZND0="39431@0" Pin0InfoVect0LinkObjId="g_38c3240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-318636_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1986,3 1986,-7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37c65f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1986,48 1986,39 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" ObjectIDND0="49198@0" ObjectIDZND0="49194@0" Pin0InfoVect0LinkObjId="SW-318636_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_LJ.CX_LJ_XN3_LD_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1986,48 1986,39 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37c6850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1986,92 1986,66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1986,92 1986,66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37c9880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2414,-9 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="busSection" ObjectIDND0="39432@0" ObjectIDZND0="39432@0" Pin0InfoVect0LinkObjId="g_38bdcd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38bdcd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2414,-9 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37ca240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2414,50 2414,39 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" ObjectIDND0="49199@0" ObjectIDZND0="49195@0" Pin0InfoVect0LinkObjId="SW-318637_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_LJ.CX_LJ_XN4_LD_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2414,50 2414,39 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_37ca4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2414,77 2414,63 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="2414,77 2414,63 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37ca700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2414,3 2414,-9 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49195@1" ObjectIDZND0="39432@0" Pin0InfoVect0LinkObjId="g_38bdcd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-318637_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2414,3 2414,-9 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="460" x2="429" y1="268" y2="268"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="429" x2="429" y1="268" y2="313"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="462" x2="429" y1="292" y2="292"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="462" x2="462" y1="292" y2="311"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="462" x2="462" y1="330" y2="304"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="429" x2="429" y1="322" y2="309"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="415" x2="437" y1="354" y2="320"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1099" x2="1071" y1="266" y2="266"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1071" x2="1071" y1="266" y2="311"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1104" x2="1071" y1="290" y2="290"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1104" x2="1104" y1="290" y2="309"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1104" x2="1104" y1="328" y2="302"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1071" x2="1071" y1="320" y2="307"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1057" x2="1079" y1="352" y2="318"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1270" x2="1315" y1="285" y2="285"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.973684" x1="1315" x2="1315" y1="248" y2="285"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="631" x2="676" y1="297" y2="297"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.973684" x1="676" x2="676" y1="260" y2="297"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="362" x2="369" y1="222" y2="235"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="353" x2="368" y1="236" y2="236"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="361" x2="354" y1="222" y2="235"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="476" x2="483" y1="209" y2="196"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="467" x2="482" y1="195" y2="195"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="475" x2="468" y1="209" y2="196"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="475" x2="482" y1="224" y2="237"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="466" x2="481" y1="238" y2="238"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="474" x2="467" y1="224" y2="237"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="634" x2="641" y1="208" y2="195"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="625" x2="640" y1="194" y2="194"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="633" x2="626" y1="208" y2="195"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="633" x2="640" y1="219" y2="232"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="624" x2="639" y1="233" y2="233"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="632" x2="625" y1="219" y2="232"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1002" x2="1009" y1="232" y2="219"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="993" x2="1008" y1="218" y2="218"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1001" x2="994" y1="232" y2="219"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1001" x2="1008" y1="243" y2="256"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="992" x2="1007" y1="257" y2="257"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1000" x2="993" y1="243" y2="256"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1116" x2="1123" y1="207" y2="194"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1107" x2="1122" y1="193" y2="193"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1115" x2="1108" y1="207" y2="194"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1115" x2="1122" y1="220" y2="233"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1106" x2="1121" y1="234" y2="234"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1114" x2="1107" y1="220" y2="233"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="363" x2="370" y1="209" y2="196"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="354" x2="369" y1="195" y2="195"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="362" x2="355" y1="209" y2="196"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1105" x2="1108" y1="343" y2="328"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1100" x2="1108" y1="328" y2="328"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1104" x2="1101" y1="343" y2="328"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1067" x2="1075" y1="365" y2="365"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1069" x2="1072" y1="367" y2="367"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1071" x2="1071" y1="362" y2="351"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1065" x2="1078" y1="362" y2="362"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="457" x2="465" y1="367" y2="367"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="459" x2="462" y1="369" y2="369"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="461" x2="461" y1="364" y2="353"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="455" x2="468" y1="364" y2="364"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="463" x2="466" y1="345" y2="330"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="458" x2="466" y1="330" y2="330"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="462" x2="459" y1="345" y2="330"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="425" x2="433" y1="367" y2="367"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="427" x2="430" y1="369" y2="369"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="429" x2="429" y1="364" y2="353"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="423" x2="436" y1="364" y2="364"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1099" x2="1107" y1="365" y2="365"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1101" x2="1104" y1="367" y2="367"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1103" x2="1103" y1="362" y2="351"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1097" x2="1110" y1="362" y2="362"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1273" x2="1280" y1="206" y2="193"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1264" x2="1279" y1="192" y2="192"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1272" x2="1265" y1="206" y2="193"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1272" x2="1279" y1="218" y2="231"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1263" x2="1278" y1="232" y2="232"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1271" x2="1264" y1="218" y2="231"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1560" x2="1605" y1="297" y2="297"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.973684" x1="1605" x2="1605" y1="260" y2="297"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1563" x2="1570" y1="208" y2="195"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1554" x2="1569" y1="194" y2="194"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1562" x2="1555" y1="208" y2="195"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1562" x2="1569" y1="219" y2="232"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1553" x2="1568" y1="233" y2="233"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1561" x2="1554" y1="219" y2="232"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1720" x2="1689" y1="267" y2="267"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1689" x2="1689" y1="267" y2="312"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1722" x2="1689" y1="291" y2="291"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1722" x2="1722" y1="291" y2="310"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1722" x2="1722" y1="329" y2="303"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1689" x2="1689" y1="321" y2="308"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1675" x2="1697" y1="353" y2="319"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1736" x2="1743" y1="208" y2="195"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1727" x2="1742" y1="194" y2="194"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1735" x2="1728" y1="208" y2="195"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1735" x2="1742" y1="223" y2="236"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1726" x2="1741" y1="237" y2="237"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1734" x2="1727" y1="223" y2="236"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1717" x2="1725" y1="366" y2="366"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1719" x2="1722" y1="368" y2="368"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1721" x2="1721" y1="363" y2="352"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1715" x2="1728" y1="363" y2="363"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1723" x2="1726" y1="344" y2="329"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1718" x2="1726" y1="329" y2="329"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1722" x2="1719" y1="344" y2="329"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1685" x2="1693" y1="366" y2="366"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1687" x2="1690" y1="368" y2="368"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1689" x2="1689" y1="363" y2="352"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1683" x2="1696" y1="363" y2="363"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1987" x2="1994" y1="275" y2="288"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1978" x2="1993" y1="289" y2="289"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1986" x2="1979" y1="275" y2="288"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1988" x2="1995" y1="262" y2="249"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1979" x2="1994" y1="248" y2="248"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1987" x2="1980" y1="262" y2="249"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2414" x2="2421" y1="263" y2="276"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2405" x2="2420" y1="277" y2="277"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2413" x2="2406" y1="263" y2="276"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2415" x2="2422" y1="250" y2="237"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2406" x2="2421" y1="236" y2="236"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2414" x2="2407" y1="250" y2="237"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2554" x2="2599" y1="291" y2="291"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.973684" x1="2599" x2="2599" y1="254" y2="291"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2557" x2="2564" y1="202" y2="189"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2548" x2="2563" y1="188" y2="188"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2556" x2="2549" y1="202" y2="189"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2556" x2="2563" y1="213" y2="226"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2547" x2="2562" y1="227" y2="227"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2555" x2="2548" y1="213" y2="226"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2719" x2="2691" y1="249" y2="249"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2691" x2="2691" y1="249" y2="294"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2724" x2="2691" y1="273" y2="273"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2724" x2="2724" y1="273" y2="292"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2724" x2="2724" y1="311" y2="285"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2691" x2="2691" y1="303" y2="290"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2677" x2="2699" y1="335" y2="301"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2736" x2="2743" y1="190" y2="177"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2727" x2="2742" y1="176" y2="176"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2735" x2="2728" y1="190" y2="177"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2735" x2="2742" y1="203" y2="216"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2726" x2="2741" y1="217" y2="217"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2734" x2="2727" y1="203" y2="216"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2725" x2="2728" y1="326" y2="311"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2720" x2="2728" y1="311" y2="311"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2724" x2="2721" y1="326" y2="311"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2687" x2="2695" y1="348" y2="348"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2689" x2="2692" y1="350" y2="350"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2691" x2="2691" y1="345" y2="334"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2685" x2="2698" y1="345" y2="345"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2719" x2="2727" y1="348" y2="348"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2721" x2="2724" y1="350" y2="350"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2723" x2="2723" y1="345" y2="334"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2717" x2="2730" y1="345" y2="345"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-194887" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 109.000000 -874.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29579" ObjectName="DYN-CX_LJ"/>
     <cge:Meas_Ref ObjectId="194887"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_LJ.CX_LJ_XN1_LD">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.750000 353.000000 72.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49196" ObjectName="EC-CX_LJ.CX_LJ_XN1_LD"/>
    <cge:TPSR_Ref TObjectID="49196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_LJ.CX_LJ_XN2_LD">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.750000 992.000000 69.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49197" ObjectName="EC-CX_LJ.CX_LJ_XN2_LD"/>
    <cge:TPSR_Ref TObjectID="49197"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_LJ.CX_LJ_XN3_LD">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.750000 1977.000000 68.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49198" ObjectName="EC-CX_LJ.CX_LJ_XN3_LD"/>
    <cge:TPSR_Ref TObjectID="49198"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_LJ.CX_LJ_XN4_LD">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.750000 2405.000000 70.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49199" ObjectName="EC-CX_LJ.CX_LJ_XN4_LD"/>
    <cge:TPSR_Ref TObjectID="49199"/></metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_LJ"/>
</svg>