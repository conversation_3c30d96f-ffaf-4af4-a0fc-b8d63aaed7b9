<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-159" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="2890 -1193 2287 1341">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape28">
    <polyline arcFlag="1" points="19,105 17,105 15,104 14,104 12,103 11,102 9,101 8,99 7,97 7,96 6,94 6,92 6,90 7,88 7,87 8,85 9,84 11,82 12,81 14,80 15,80 17,79 19,79 21,79 23,80 24,80 26,81 27,82 29,84 30,85 31,87 31,88 32,90 32,92 " stroke-width="0.0972"/>
    <polyline arcFlag="1" points="36,30 37,30 38,30 38,30 39,31 39,31 40,31 40,32 41,32 41,33 41,34 41,34 42,35 42,36 42,36 41,37 41,38 41,38 41,39 40,39 40,40 39,40 39,40 38,41 38,41 37,41 36,41 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="11" x2="26" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="10" x2="26" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="19" x2="19" y1="19" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="35" x2="35" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="2" x2="2" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.356919" x1="19" x2="36" y1="60" y2="60"/>
    <rect height="23" stroke-width="0.369608" width="12" x="13" y="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44164" x1="19" x2="36" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="2" x2="35" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.423529" x1="19" x2="19" y1="92" y2="26"/>
    <polyline arcFlag="1" points="36,41 37,41 38,41 38,42 39,42 39,42 40,43 40,43 41,44 41,44 41,45 41,45 42,46 42,47 42,47 41,48 41,49 41,49 41,50 40,50 40,51 39,51 39,52 38,52 38,52 37,52 36,52 " stroke-width="1"/>
    <polyline arcFlag="1" points="36,19 37,19 38,19 38,19 39,19 39,20 40,20 40,21 41,21 41,22 41,22 41,23 42,24 42,24 42,25 41,26 41,26 41,27 41,27 40,28 40,28 39,29 39,29 38,29 38,30 37,30 36,30 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="60" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="19" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="19" x2="19" y1="105" y2="116"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="32" x2="19" y1="92" y2="92"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="0" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="9" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="7" y2="5"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="13" x2="4" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="13" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="20" y2="20"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape164">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="60" x2="60" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="57" x2="57" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="48" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape114">
    <ellipse cx="25" cy="38" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="63" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="26" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="51" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="52" x2="52" y1="62" y2="32"/>
    <rect height="26" stroke-width="0.416667" width="13" x="45" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="53" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="40" x2="29" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="35" x2="35" y1="8" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="31" x2="38" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="32" x2="35" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="62" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="53" y1="27" y2="17"/>
    <ellipse cx="15" cy="34" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <ellipse cx="8" cy="38" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <circle cx="15" cy="43" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="load:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="5" y2="29"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,19 9,31 17,19 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape21_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="28" x2="36" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="25" x2="25" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="5" x2="5" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="25" x2="9" y1="23" y2="23"/>
   </symbol>
   <symbol id="switch2:shape21_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="26" x2="26" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="6" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="10" x2="10" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="26" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="3" x2="3" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="36" x2="36" y1="34" y2="14"/>
   </symbol>
   <symbol id="switch2:shape21-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="28" x2="36" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="25" x2="25" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="5" x2="5" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="25" x2="9" y1="23" y2="23"/>
   </symbol>
   <symbol id="switch2:shape21-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="26" x2="26" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="6" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="10" x2="10" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="26" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="3" x2="3" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="36" x2="36" y1="34" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape57_0">
    <circle cx="16" cy="80" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="16" y1="50" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="45" x2="37" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="35" x2="47" y1="26" y2="26"/>
    <polyline DF8003:Layer="PUBLIC" points="16,12 22,25 10,25 16,12 16,13 16,12 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="43" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="79" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="79" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="79" y2="74"/>
   </symbol>
   <symbol id="transformer2:shape57_1">
    <circle cx="16" cy="58" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="16,55 41,55 41,26 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="55" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="55" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="55" y2="60"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape40_0">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="32" y1="53" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,56 6,56 6,28 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="57" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape40_1">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="87" y2="87"/>
    <polyline DF8003:Layer="PUBLIC" points="30,87 26,78 36,78 30,87 "/>
   </symbol>
   <symbol id="voltageTransformer:shape104">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="39" y1="41" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="36" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="38" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="37" y1="43" y2="43"/>
    <ellipse cx="8" cy="12" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="8" cy="24" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="10" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="4" y1="10" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="10" y2="14"/>
    <ellipse cx="19" cy="12" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <ellipse cx="19" cy="24" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="44" y1="19" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="44" y1="28" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="39" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="39" y1="12" y2="18"/>
    <rect height="13" stroke-width="1" width="5" x="37" y="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape3">
    <circle cx="7" cy="19" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="7" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3040f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30420b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3042a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3043a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3044670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3044f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30456b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3046170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_25f2730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_25f2730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30492a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30492a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_304b030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_304b030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_304c050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_304dc50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_304e610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_304f4f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_304fdd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3051330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3051ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30527b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3052f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3054050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30549d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30554c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3055e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3057330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3057ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3058f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3059b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3067fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_305b0c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_305c6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_305dbe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1351" width="2297" x="2885" y="-1198"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(65,105,225)" stroke-width="1" x1="4082" x2="4091" y1="-1142" y2="-1142"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(65,105,225)" stroke-width="1" x1="4082" x2="4091" y1="-1150" y2="-1150"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(65,105,225)" stroke-width="1" x1="3824" x2="3833" y1="-1144" y2="-1144"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(65,105,225)" stroke-width="1" x1="3824" x2="3833" y1="-1153" y2="-1153"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3859" x2="3945" y1="-478" y2="-478"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3859" x2="3859" y1="-478" y2="-511"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3614" x2="3614" y1="-501" y2="-534"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.662791" x1="3614" x2="3671" y1="-501" y2="-501"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3694" x2="3694" y1="-607" y2="-604"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3706" x2="3706" y1="-599" y2="-596"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3695" x2="3695" y1="-590" y2="-587"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4155" x2="4155" y1="-489" y2="-523"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.686047" x1="4155" x2="4214" y1="-489" y2="-489"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.909091" x1="4384" x2="4384" y1="-493" y2="-523"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.663178" x1="4327" x2="4384" y1="-493" y2="-493"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.959904" x1="3303" x2="3344" y1="-409" y2="-409"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.757576" x1="3303" x2="3303" y1="-384" y2="-409"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3307" x2="3307" y1="-124" y2="17"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="3307" x2="3344" y1="17" y2="17"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.84563" x1="3465" x2="3501" y1="-409" y2="-409"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.815851" x1="3465" x2="3465" y1="-382" y2="-409"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.815851" x1="3621" x2="3621" y1="-382" y2="-409"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.84563" x1="3621" x2="3657" y1="-409" y2="-409"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.815851" x1="3782" x2="3782" y1="-382" y2="-409"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.84563" x1="3782" x2="3818" y1="-409" y2="-409"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.815851" x1="4073" x2="4073" y1="-387" y2="-414"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.84563" x1="4073" x2="4109" y1="-414" y2="-414"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4530" x2="4530" y1="-609" y2="-606"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4542" x2="4542" y1="-601" y2="-598"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4531" x2="4531" y1="-592" y2="-589"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="4619" x2="4619" y1="-482" y2="-515"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.709302" x1="4619" x2="4680" y1="-482" y2="-482"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.815851" x1="4309" x2="4309" y1="-396" y2="-423"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.84563" x1="4309" x2="4345" y1="-423" y2="-423"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.815851" x1="4500" x2="4500" y1="-395" y2="-422"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.84563" x1="4500" x2="4536" y1="-422" y2="-422"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.815851" x1="4688" x2="4688" y1="-391" y2="-418"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.84563" x1="4688" x2="4724" y1="-418" y2="-418"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.815851" x1="4871" x2="4871" y1="-395" y2="-422"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.84563" x1="4871" x2="4907" y1="-422" y2="-422"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.815851" x1="5060" x2="5060" y1="-400" y2="-427"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="0.84563" x1="5060" x2="5096" y1="-427" y2="-427"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="5062" x2="5099" y1="-12" y2="-12"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="5062" x2="5062" y1="-153" y2="-12"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3093" y="-1192"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="2891" y="-1043"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-108656">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3941.464567 -790.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21133" ObjectName="SW-LF_QSH.LF_QSH_3011SW"/>
     <cge:Meas_Ref ObjectId="108656"/>
    <cge:TPSR_Ref TObjectID="21133"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108657">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3891.464567 -771.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21134" ObjectName="SW-LF_QSH.LF_QSH_30117SW"/>
     <cge:Meas_Ref ObjectId="108657"/>
    <cge:TPSR_Ref TObjectID="21134"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108665">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3940.464567 -455.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21135" ObjectName="SW-LF_QSH.LF_QSH_0011SW"/>
     <cge:Meas_Ref ObjectId="108665"/>
    <cge:TPSR_Ref TObjectID="21135"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108666">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3864.464567 -506.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21136" ObjectName="SW-LF_QSH.LF_QSH_00117SW"/>
     <cge:Meas_Ref ObjectId="108666"/>
    <cge:TPSR_Ref TObjectID="21136"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108759">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4674.000000 -779.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21141" ObjectName="SW-LF_QSH.LF_QSH_3021SW"/>
     <cge:Meas_Ref ObjectId="108759"/>
    <cge:TPSR_Ref TObjectID="21141"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108760">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4624.000000 -761.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21142" ObjectName="SW-LF_QSH.LF_QSH_30217SW"/>
     <cge:Meas_Ref ObjectId="108760"/>
    <cge:TPSR_Ref TObjectID="21142"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108768">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4673.000000 -463.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21143" ObjectName="SW-LF_QSH.LF_QSH_0021SW"/>
     <cge:Meas_Ref ObjectId="108768"/>
    <cge:TPSR_Ref TObjectID="21143"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108769">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4623.000000 -510.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21144" ObjectName="SW-LF_QSH.LF_QSH_00217SW"/>
     <cge:Meas_Ref ObjectId="108769"/>
    <cge:TPSR_Ref TObjectID="21144"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108865">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4510.000000 -865.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21167" ObjectName="SW-LF_QSH.LF_QSH_39020SW"/>
     <cge:Meas_Ref ObjectId="108865"/>
    <cge:TPSR_Ref TObjectID="21167"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108864">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4490.000000 -883.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21166" ObjectName="SW-LF_QSH.LF_QSH_3902SW"/>
     <cge:Meas_Ref ObjectId="108864"/>
    <cge:TPSR_Ref TObjectID="21166"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108866">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4439.000000 -934.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21168" ObjectName="SW-LF_QSH.LF_QSH_39027SW"/>
     <cge:Meas_Ref ObjectId="108866"/>
    <cge:TPSR_Ref TObjectID="21168"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108841">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4203.000000 -860.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21158" ObjectName="SW-LF_QSH.LF_QSH_3121SW"/>
     <cge:Meas_Ref ObjectId="108841"/>
    <cge:TPSR_Ref TObjectID="21158"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108843">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4309.000000 -860.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21160" ObjectName="SW-LF_QSH.LF_QSH_3122W"/>
     <cge:Meas_Ref ObjectId="108843"/>
    <cge:TPSR_Ref TObjectID="21160"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108844">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4309.000000 -925.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21161" ObjectName="SW-LF_QSH.LF_QSH_31227SW"/>
     <cge:Meas_Ref ObjectId="108844"/>
    <cge:TPSR_Ref TObjectID="21161"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108842">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4203.000000 -925.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21159" ObjectName="SW-LF_QSH.LF_QSH_31217SW"/>
     <cge:Meas_Ref ObjectId="108842"/>
    <cge:TPSR_Ref TObjectID="21159"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108804">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4045.000000 -860.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21152" ObjectName="SW-LF_QSH.LF_QSH_3621SW"/>
     <cge:Meas_Ref ObjectId="108804"/>
    <cge:TPSR_Ref TObjectID="21152"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108805">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3998.000000 -913.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21153" ObjectName="SW-LF_QSH.LF_QSH_3617SW"/>
     <cge:Meas_Ref ObjectId="108805"/>
    <cge:TPSR_Ref TObjectID="21153"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108807">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3999.000000 -968.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21155" ObjectName="SW-LF_QSH.LF_QSH_36260SW"/>
     <cge:Meas_Ref ObjectId="108807"/>
    <cge:TPSR_Ref TObjectID="21155"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108812">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3998.000000 -1037.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21156" ObjectName="SW-LF_QSH.LF_QSH_36267SW"/>
     <cge:Meas_Ref ObjectId="108812"/>
    <cge:TPSR_Ref TObjectID="21156"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108806">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4045.000000 -984.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21154" ObjectName="SW-LF_QSH.LF_QSH_3626SW"/>
     <cge:Meas_Ref ObjectId="108806"/>
    <cge:TPSR_Ref TObjectID="21154"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4077.000000 -1082.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108773">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3794.000000 -863.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21147" ObjectName="SW-LF_QSH.LF_QSH_3611SW"/>
     <cge:Meas_Ref ObjectId="108773"/>
    <cge:TPSR_Ref TObjectID="21147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108774">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3747.000000 -916.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21148" ObjectName="SW-LF_QSH.LF_QSH_36117SW"/>
     <cge:Meas_Ref ObjectId="108774"/>
    <cge:TPSR_Ref TObjectID="21148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108776">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3748.000000 -971.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21150" ObjectName="SW-LF_QSH.LF_QSH_36160SW"/>
     <cge:Meas_Ref ObjectId="108776"/>
    <cge:TPSR_Ref TObjectID="21150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108781">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3747.000000 -1041.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32004" ObjectName="SW-LF_QSH.LF_QSH_36167SW"/>
     <cge:Meas_Ref ObjectId="108781"/>
    <cge:TPSR_Ref TObjectID="32004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108775">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3794.000000 -988.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21149" ObjectName="SW-LF_QSH.LF_QSH_3616SW"/>
     <cge:Meas_Ref ObjectId="108775"/>
    <cge:TPSR_Ref TObjectID="21149"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108862">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3634.000000 -871.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21164" ObjectName="SW-LF_QSH.LF_QSH_39010SW"/>
     <cge:Meas_Ref ObjectId="108862"/>
    <cge:TPSR_Ref TObjectID="21164"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108861">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3614.000000 -906.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21163" ObjectName="SW-LF_QSH.LF_QSH_3901SW"/>
     <cge:Meas_Ref ObjectId="108861"/>
    <cge:TPSR_Ref TObjectID="21163"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108863">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3563.000000 -954.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21165" ObjectName="SW-LF_QSH.LF_QSH_39017SW"/>
     <cge:Meas_Ref ObjectId="108863"/>
    <cge:TPSR_Ref TObjectID="21165"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109030">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3811.607874 -390.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21209" ObjectName="SW-LF_QSH.LF_QSH_0671SW"/>
     <cge:Meas_Ref ObjectId="109030"/>
    <cge:TPSR_Ref TObjectID="21209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109031">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3760.607874 -373.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21210" ObjectName="SW-LF_QSH.LF_QSH_06717SW"/>
     <cge:Meas_Ref ObjectId="109031"/>
    <cge:TPSR_Ref TObjectID="21210"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109034">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3810.607874 -156.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21212" ObjectName="SW-LF_QSH.LF_QSH_0676SW"/>
     <cge:Meas_Ref ObjectId="109034"/>
    <cge:TPSR_Ref TObjectID="21212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109036">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3831.607874 -145.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21213" ObjectName="SW-LF_QSH.LF_QSH_06767SW"/>
     <cge:Meas_Ref ObjectId="109036"/>
    <cge:TPSR_Ref TObjectID="21213"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108871">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3666.818898 -477.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21169" ObjectName="SW-LF_QSH.LF_QSH_0901SW"/>
     <cge:Meas_Ref ObjectId="108871"/>
    <cge:TPSR_Ref TObjectID="21169"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108872">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3615.818898 -529.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21170" ObjectName="SW-LF_QSH.LF_QSH_09017SW"/>
     <cge:Meas_Ref ObjectId="108872"/>
    <cge:TPSR_Ref TObjectID="21170"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109005">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3652.176378 -389.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21203" ObjectName="SW-LF_QSH.LF_QSH_0651SW"/>
     <cge:Meas_Ref ObjectId="109005"/>
    <cge:TPSR_Ref TObjectID="21203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109006">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3602.176378 -374.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21204" ObjectName="SW-LF_QSH.LF_QSH_06517SW"/>
     <cge:Meas_Ref ObjectId="109006"/>
    <cge:TPSR_Ref TObjectID="21204"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109009">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3652.176378 -156.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21206" ObjectName="SW-LF_QSH.LF_QSH_0656SW"/>
     <cge:Meas_Ref ObjectId="109009"/>
    <cge:TPSR_Ref TObjectID="21206"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109011">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3673.176378 -145.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21207" ObjectName="SW-LF_QSH.LF_QSH_06567SW"/>
     <cge:Meas_Ref ObjectId="109011"/>
    <cge:TPSR_Ref TObjectID="21207"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108980">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3494.470866 -390.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21197" ObjectName="SW-LF_QSH.LF_QSH_0631SW"/>
     <cge:Meas_Ref ObjectId="108980"/>
    <cge:TPSR_Ref TObjectID="21197"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108981">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3444.470866 -374.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21198" ObjectName="SW-LF_QSH.LF_QSH_06317SW"/>
     <cge:Meas_Ref ObjectId="108981"/>
    <cge:TPSR_Ref TObjectID="21198"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108984">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3494.470866 -156.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21200" ObjectName="SW-LF_QSH.LF_QSH_0636SW"/>
     <cge:Meas_Ref ObjectId="108984"/>
    <cge:TPSR_Ref TObjectID="21200"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108986">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3515.470866 -145.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21201" ObjectName="SW-LF_QSH.LF_QSH_06367SW"/>
     <cge:Meas_Ref ObjectId="108986"/>
    <cge:TPSR_Ref TObjectID="21201"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109055">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3337.943307 -389.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21215" ObjectName="SW-LF_QSH.LF_QSH_0611SW"/>
     <cge:Meas_Ref ObjectId="109055"/>
    <cge:TPSR_Ref TObjectID="21215"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109056">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3279.943307 -375.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21216" ObjectName="SW-LF_QSH.LF_QSH_06117SW"/>
     <cge:Meas_Ref ObjectId="109056"/>
    <cge:TPSR_Ref TObjectID="21216"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109059">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3338.000000 -140.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21219" ObjectName="SW-LF_QSH.LF_QSH_0616SW"/>
     <cge:Meas_Ref ObjectId="109059"/>
    <cge:TPSR_Ref TObjectID="21219"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109058">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3288.000000 -194.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21218" ObjectName="SW-LF_QSH.LF_QSH_06160SW"/>
     <cge:Meas_Ref ObjectId="109058"/>
    <cge:TPSR_Ref TObjectID="21218"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109060">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3287.000000 -117.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21220" ObjectName="SW-LF_QSH.LF_QSH_06167SW"/>
     <cge:Meas_Ref ObjectId="109060"/>
    <cge:TPSR_Ref TObjectID="21220"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108881">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4719.000000 -398.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21174" ObjectName="SW-LF_QSH.LF_QSH_0661SW"/>
     <cge:Meas_Ref ObjectId="108881"/>
    <cge:TPSR_Ref TObjectID="21174"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108882">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4668.000000 -382.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21175" ObjectName="SW-LF_QSH.LF_QSH_06617SW"/>
     <cge:Meas_Ref ObjectId="108882"/>
    <cge:TPSR_Ref TObjectID="21175"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108885">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4719.000000 -168.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21177" ObjectName="SW-LF_QSH.LF_QSH_0666SW"/>
     <cge:Meas_Ref ObjectId="108885"/>
    <cge:TPSR_Ref TObjectID="21177"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108887">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4740.000000 -157.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21178" ObjectName="SW-LF_QSH.LF_QSH_0667SW"/>
     <cge:Meas_Ref ObjectId="108887"/>
    <cge:TPSR_Ref TObjectID="21178"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108955">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4341.000000 -399.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21191" ObjectName="SW-LF_QSH.LF_QSH_0621SW"/>
     <cge:Meas_Ref ObjectId="108955"/>
    <cge:TPSR_Ref TObjectID="21191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108956">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4291.000000 -388.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21192" ObjectName="SW-LF_QSH.LF_QSH_06217SW"/>
     <cge:Meas_Ref ObjectId="108956"/>
    <cge:TPSR_Ref TObjectID="21192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108959">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4341.000000 -169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21194" ObjectName="SW-LF_QSH.LF_QSH_0626SW"/>
     <cge:Meas_Ref ObjectId="108959"/>
    <cge:TPSR_Ref TObjectID="21194"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108961">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4362.000000 -158.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21195" ObjectName="SW-LF_QSH.LF_QSH_06267SW"/>
     <cge:Meas_Ref ObjectId="108961"/>
    <cge:TPSR_Ref TObjectID="21195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108906">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4530.000000 -400.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21180" ObjectName="SW-LF_QSH.LF_QSH_0641SW"/>
     <cge:Meas_Ref ObjectId="108906"/>
    <cge:TPSR_Ref TObjectID="21180"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-219565">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4530.000000 -175.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34311" ObjectName="SW-LF_QSH.LF_QSH_0646SW"/>
     <cge:Meas_Ref ObjectId="219565"/>
    <cge:TPSR_Ref TObjectID="34311"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108911">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4551.000000 -159.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21183" ObjectName="SW-LF_QSH.LF_QSH_06467SW"/>
     <cge:Meas_Ref ObjectId="108911"/>
    <cge:TPSR_Ref TObjectID="21183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108930">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4904.000000 -399.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21185" ObjectName="SW-LF_QSH.LF_QSH_0721SW"/>
     <cge:Meas_Ref ObjectId="108930"/>
    <cge:TPSR_Ref TObjectID="21185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108931">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4853.000000 -388.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21186" ObjectName="SW-LF_QSH.LF_QSH_07217SW"/>
     <cge:Meas_Ref ObjectId="108931"/>
    <cge:TPSR_Ref TObjectID="21186"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108934">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4904.000000 -168.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21188" ObjectName="SW-LF_QSH.LF_QSH_0726SW"/>
     <cge:Meas_Ref ObjectId="108934"/>
    <cge:TPSR_Ref TObjectID="21188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108936">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4925.000000 -158.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21189" ObjectName="SW-LF_QSH.LF_QSH_07267SW"/>
     <cge:Meas_Ref ObjectId="108936"/>
    <cge:TPSR_Ref TObjectID="21189"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109078">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5093.000000 -404.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21222" ObjectName="SW-LF_QSH.LF_QSH_0681SW"/>
     <cge:Meas_Ref ObjectId="109078"/>
    <cge:TPSR_Ref TObjectID="21222"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109079">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5042.000000 -392.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21223" ObjectName="SW-LF_QSH.LF_QSH_06817SW"/>
     <cge:Meas_Ref ObjectId="109079"/>
    <cge:TPSR_Ref TObjectID="21223"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109082">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5093.000000 -158.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21226" ObjectName="SW-LF_QSH.LF_QSH_0686SW"/>
     <cge:Meas_Ref ObjectId="109082"/>
    <cge:TPSR_Ref TObjectID="21226"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109081">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5043.000000 -212.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21225" ObjectName="SW-LF_QSH.LF_QSH_06860SW"/>
     <cge:Meas_Ref ObjectId="109081"/>
    <cge:TPSR_Ref TObjectID="21225"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109083">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5042.000000 -146.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21227" ObjectName="SW-LF_QSH.LF_QSH_06867SW"/>
     <cge:Meas_Ref ObjectId="109083"/>
    <cge:TPSR_Ref TObjectID="21227"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108873">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4503.000000 -480.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21171" ObjectName="SW-LF_QSH.LF_QSH_0902SW"/>
     <cge:Meas_Ref ObjectId="108873"/>
    <cge:TPSR_Ref TObjectID="21171"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108874">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4452.000000 -532.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21172" ObjectName="SW-LF_QSH.LF_QSH_09027SW"/>
     <cge:Meas_Ref ObjectId="108874"/>
    <cge:TPSR_Ref TObjectID="21172"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185506">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4210.480315 -467.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28116" ObjectName="SW-LF_QSH.LF_QSH_0121W"/>
     <cge:Meas_Ref ObjectId="185506"/>
    <cge:TPSR_Ref TObjectID="28116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185508">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4331.519685 -514.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28118" ObjectName="SW-LF_QSH.LF_QSH_0122W"/>
     <cge:Meas_Ref ObjectId="185508"/>
    <cge:TPSR_Ref TObjectID="28118"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-198684">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4106.176378 -390.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30385" ObjectName="SW-LF_QSH.LF_QSH_0691SW"/>
     <cge:Meas_Ref ObjectId="198684"/>
    <cge:TPSR_Ref TObjectID="30385"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-198687">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4056.176378 -381.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30386" ObjectName="SW-LF_QSH.LF_QSH_06917SW"/>
     <cge:Meas_Ref ObjectId="198687"/>
    <cge:TPSR_Ref TObjectID="30386"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-198686">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4106.176378 -157.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30388" ObjectName="SW-LF_QSH.LF_QSH_0696SW"/>
     <cge:Meas_Ref ObjectId="198686"/>
    <cge:TPSR_Ref TObjectID="30388"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-198688">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4127.176378 -146.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30387" ObjectName="SW-LF_QSH.LF_QSH_06967SW"/>
     <cge:Meas_Ref ObjectId="198688"/>
    <cge:TPSR_Ref TObjectID="30387"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4413.000000 -782.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3819.000000 -1085.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108667">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3913.000000 -561.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21137" ObjectName="SW-LF_QSH.LF_QSH_0016SW"/>
     <cge:Meas_Ref ObjectId="108667"/>
    <cge:TPSR_Ref TObjectID="21137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185507">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4334.000000 -518.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28119" ObjectName="SW-LF_QSH.LF_QSH_01227W"/>
     <cge:Meas_Ref ObjectId="185507"/>
    <cge:TPSR_Ref TObjectID="28119"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109057">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3311.000000 -270.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21217" ObjectName="SW-LF_QSH.LF_QSH_0612SW"/>
     <cge:Meas_Ref ObjectId="109057"/>
    <cge:TPSR_Ref TObjectID="21217"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3338.000000 35.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108983">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3467.000000 -269.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21199" ObjectName="SW-LF_QSH.LF_QSH_0632SW"/>
     <cge:Meas_Ref ObjectId="108983"/>
    <cge:TPSR_Ref TObjectID="21199"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109008">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3625.000000 -269.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21205" ObjectName="SW-LF_QSH.LF_QSH_0652SW"/>
     <cge:Meas_Ref ObjectId="109008"/>
    <cge:TPSR_Ref TObjectID="21205"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109033">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3784.000000 -269.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21211" ObjectName="SW-LF_QSH.LF_QSH_0672SW"/>
     <cge:Meas_Ref ObjectId="109033"/>
    <cge:TPSR_Ref TObjectID="21211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3954.000000 -216.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-198685">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4079.000000 -270.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30389" ObjectName="SW-LF_QSH.LF_QSH_0692SW"/>
     <cge:Meas_Ref ObjectId="198685"/>
    <cge:TPSR_Ref TObjectID="30389"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108770">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4646.000000 -559.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21145" ObjectName="SW-LF_QSH.LF_QSH_0026SW"/>
     <cge:Meas_Ref ObjectId="108770"/>
    <cge:TPSR_Ref TObjectID="21145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108958">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4314.000000 -283.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21193" ObjectName="SW-LF_QSH.LF_QSH_0622SW"/>
     <cge:Meas_Ref ObjectId="108958"/>
    <cge:TPSR_Ref TObjectID="21193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108907">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4480.000000 -387.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21181" ObjectName="SW-LF_QSH.LF_QSH_06417SW"/>
     <cge:Meas_Ref ObjectId="108907"/>
    <cge:TPSR_Ref TObjectID="21181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108909">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4503.000000 -285.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21182" ObjectName="SW-LF_QSH.LF_QSH_0642SW"/>
     <cge:Meas_Ref ObjectId="108909"/>
    <cge:TPSR_Ref TObjectID="21182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108884">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4692.000000 -282.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21176" ObjectName="SW-LF_QSH.LF_QSH_0662SW"/>
     <cge:Meas_Ref ObjectId="108884"/>
    <cge:TPSR_Ref TObjectID="21176"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108933">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4877.000000 -284.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21187" ObjectName="SW-LF_QSH.LF_QSH_0722SW"/>
     <cge:Meas_Ref ObjectId="108933"/>
    <cge:TPSR_Ref TObjectID="21187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109080">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5066.000000 -289.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21224" ObjectName="SW-LF_QSH.LF_QSH_0682SW"/>
     <cge:Meas_Ref ObjectId="109080"/>
    <cge:TPSR_Ref TObjectID="21224"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5093.000000 6.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4161.000000 -518.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-LF_QSH.LF_QSH_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4308,-845 4722,-845 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="29430" ObjectName="BS-LF_QSH.LF_QSH_3IIM"/>
    <cge:TPSR_Ref TObjectID="29430"/></metadata>
   <polyline fill="none" opacity="0" points="4308,-845 4722,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_QSH.LF_QSH_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3528,-845 4229,-845 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="29429" ObjectName="BS-LF_QSH.LF_QSH_3IM"/>
    <cge:TPSR_Ref TObjectID="29429"/></metadata>
   <polyline fill="none" opacity="0" points="3528,-845 4229,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_QSH.LF_QSH_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4306,-458 5156,-458 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="29432" ObjectName="BS-LF_QSH.LF_QSH_9IIM"/>
    <cge:TPSR_Ref TObjectID="29432"/></metadata>
   <polyline fill="none" opacity="0" points="4306,-458 5156,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_QSH.LF_QSH_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3337,-447 4230,-447 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="29431" ObjectName="BS-LF_QSH.LF_QSH_9IM"/>
    <cge:TPSR_Ref TObjectID="29431"/></metadata>
   <polyline fill="none" opacity="0" points="3337,-447 4230,-447 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.859504 3328.000000 -9.000000)" xlink:href="#capacitor:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.859504 5083.000000 -38.000000)" xlink:href="#capacitor:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4403.000000 -679.000000)" xlink:href="#transformer2:shape57_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4403.000000 -679.000000)" xlink:href="#transformer2:shape57_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-LF_QSH.LF_QSH_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="29475"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3910.000000 -615.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3910.000000 -615.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="21130" ObjectName="TF-LF_QSH.LF_QSH_1T"/>
    <cge:TPSR_Ref TObjectID="21130"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3929.000000 -117.000000)" xlink:href="#transformer2:shape40_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3929.000000 -117.000000)" xlink:href="#transformer2:shape40_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-LF_QSH.LF_QSH_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="32590"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4643.000000 -615.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4643.000000 -615.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="21138" ObjectName="TF-LF_QSH.LF_QSH_2T"/>
    <cge:TPSR_Ref TObjectID="21138"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_27faba0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4523.000000 -982.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27fb190">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4454.000000 -1047.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2762960">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 3592.676378 -142.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_271d450">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 3434.970866 -142.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_273a0c0">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 3752.107874 -142.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2745c80">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4660.500000 -154.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26fa2c0">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4282.500000 -155.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26f42e0">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 5030.500000 -279.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26ca390">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4845.500000 -155.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26df930">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.873016 3342.000000 -204.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2693ff0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.920635 3498.470866 -205.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2694cd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.904762 3656.176378 -203.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2695a20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.841270 3814.607874 -206.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2696770">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4908.000000 -217.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26974c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5097.000000 -222.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2698210">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.873016 4723.000000 -218.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2698f60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.920635 4534.000000 -221.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2699cb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.888889 4345.000000 -212.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_268ba50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3355.000000 -114.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_268c520">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5113.000000 -143.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_268d260">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4707.000000 -602.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_283b320">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4046.676378 -143.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25d4510">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.873016 4110.176378 -207.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2602ba0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4022.000000 -1142.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2607fa0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3764.000000 -1145.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2609dd0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3651.000000 -995.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_260a650">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3582.000000 -1060.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28f2860">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3865.000000 -604.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28fcb20">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3711.000000 -633.000000)" xlink:href="#lightningRod:shape114"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2905940">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4298.000000 -526.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_290fdb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3271.000000 -257.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2926f70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3426.000000 -259.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2928900">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3584.000000 -258.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_292cfc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3744.000000 -261.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_293b050">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4039.000000 -259.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_293c9d0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4547.000000 -637.000000)" xlink:href="#lightningRod:shape114"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2951900">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4273.000000 -268.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_295bb40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4463.000000 -274.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_295c510">
    <use class="BV-10KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4471.500000 -156.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29646b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4640.000000 -268.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_296d4a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4825.000000 -275.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 2953.500000 -1074.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217874" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.290323 -0.000000 -0.000000 1.333333 3042.500000 -928.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217874" ObjectName="LF_QSH:LF_QSH_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-224612" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.290323 -0.000000 -0.000000 1.333333 3042.500000 -897.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="224612" ObjectName="LF_QSH:LF_QSH_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217874" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.290323 -0.000000 -0.000000 1.333333 3040.500000 -1010.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217874" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217874" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.290323 -0.000000 -0.000000 1.333333 3040.500000 -972.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217874" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-108413" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4144.000000 -1069.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108413" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21151"/>
     <cge:Term_Ref ObjectID="29501"/>
    <cge:TPSR_Ref TObjectID="21151"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-108414" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4144.000000 -1069.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108414" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21151"/>
     <cge:Term_Ref ObjectID="29501"/>
    <cge:TPSR_Ref TObjectID="21151"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-108403" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4144.000000 -1069.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108403" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21151"/>
     <cge:Term_Ref ObjectID="29501"/>
    <cge:TPSR_Ref TObjectID="21151"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-108399" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3924.000000 -960.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108399" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21146"/>
     <cge:Term_Ref ObjectID="29491"/>
    <cge:TPSR_Ref TObjectID="21146"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-108400" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3924.000000 -960.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108400" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21146"/>
     <cge:Term_Ref ObjectID="29491"/>
    <cge:TPSR_Ref TObjectID="21146"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-108389" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3924.000000 -960.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108389" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21146"/>
     <cge:Term_Ref ObjectID="29491"/>
    <cge:TPSR_Ref TObjectID="21146"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-108426" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4294.000000 -835.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108426" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21157"/>
     <cge:Term_Ref ObjectID="29513"/>
    <cge:TPSR_Ref TObjectID="21157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-108427" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4294.000000 -835.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108427" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21157"/>
     <cge:Term_Ref ObjectID="29513"/>
    <cge:TPSR_Ref TObjectID="21157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-108417" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4294.000000 -835.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108417" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21157"/>
     <cge:Term_Ref ObjectID="29513"/>
    <cge:TPSR_Ref TObjectID="21157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-108349" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4069.464567 -786.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108349" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21131"/>
     <cge:Term_Ref ObjectID="29461"/>
    <cge:TPSR_Ref TObjectID="21131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-108350" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4069.464567 -786.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108350" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21131"/>
     <cge:Term_Ref ObjectID="29461"/>
    <cge:TPSR_Ref TObjectID="21131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-108344" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4069.464567 -786.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108344" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21131"/>
     <cge:Term_Ref ObjectID="29461"/>
    <cge:TPSR_Ref TObjectID="21131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-108360" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4070.464567 -562.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108360" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21132"/>
     <cge:Term_Ref ObjectID="29463"/>
    <cge:TPSR_Ref TObjectID="21132"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-108361" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4070.464567 -562.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108361" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21132"/>
     <cge:Term_Ref ObjectID="29463"/>
    <cge:TPSR_Ref TObjectID="21132"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-108355" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4070.464567 -562.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108355" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21132"/>
     <cge:Term_Ref ObjectID="29463"/>
    <cge:TPSR_Ref TObjectID="21132"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-108373" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4808.000000 -785.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108373" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21139"/>
     <cge:Term_Ref ObjectID="29477"/>
    <cge:TPSR_Ref TObjectID="21139"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-108374" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4808.000000 -785.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108374" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21139"/>
     <cge:Term_Ref ObjectID="29477"/>
    <cge:TPSR_Ref TObjectID="21139"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-108368" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4808.000000 -785.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108368" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21139"/>
     <cge:Term_Ref ObjectID="29477"/>
    <cge:TPSR_Ref TObjectID="21139"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-108384" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4850.000000 -567.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108384" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21140"/>
     <cge:Term_Ref ObjectID="29479"/>
    <cge:TPSR_Ref TObjectID="21140"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-108385" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4850.000000 -567.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108385" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21140"/>
     <cge:Term_Ref ObjectID="29479"/>
    <cge:TPSR_Ref TObjectID="21140"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-108379" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4850.000000 -567.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108379" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21140"/>
     <cge:Term_Ref ObjectID="29479"/>
    <cge:TPSR_Ref TObjectID="21140"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-108484" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3504.470866 -26.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108484" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21196"/>
     <cge:Term_Ref ObjectID="29591"/>
    <cge:TPSR_Ref TObjectID="21196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-108485" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3504.470866 -26.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108485" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21196"/>
     <cge:Term_Ref ObjectID="29591"/>
    <cge:TPSR_Ref TObjectID="21196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-108481" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3504.470866 -26.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108481" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21196"/>
     <cge:Term_Ref ObjectID="29591"/>
    <cge:TPSR_Ref TObjectID="21196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-108490" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3669.176378 -28.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108490" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21202"/>
     <cge:Term_Ref ObjectID="29603"/>
    <cge:TPSR_Ref TObjectID="21202"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-108491" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3669.176378 -28.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108491" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21202"/>
     <cge:Term_Ref ObjectID="29603"/>
    <cge:TPSR_Ref TObjectID="21202"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-108487" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3669.176378 -28.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108487" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21202"/>
     <cge:Term_Ref ObjectID="29603"/>
    <cge:TPSR_Ref TObjectID="21202"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-108496" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3827.607874 -26.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108496" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21208"/>
     <cge:Term_Ref ObjectID="29615"/>
    <cge:TPSR_Ref TObjectID="21208"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-108497" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3827.607874 -26.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108497" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21208"/>
     <cge:Term_Ref ObjectID="29615"/>
    <cge:TPSR_Ref TObjectID="21208"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-108493" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3827.607874 -26.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108493" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21208"/>
     <cge:Term_Ref ObjectID="29615"/>
    <cge:TPSR_Ref TObjectID="21208"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-108472" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4927.000000 -29.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108472" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21184"/>
     <cge:Term_Ref ObjectID="29567"/>
    <cge:TPSR_Ref TObjectID="21184"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-108473" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4927.000000 -29.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108473" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21184"/>
     <cge:Term_Ref ObjectID="29567"/>
    <cge:TPSR_Ref TObjectID="21184"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-108469" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4927.000000 -29.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108469" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21184"/>
     <cge:Term_Ref ObjectID="29567"/>
    <cge:TPSR_Ref TObjectID="21184"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-108460" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4739.000000 -28.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108460" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21173"/>
     <cge:Term_Ref ObjectID="29545"/>
    <cge:TPSR_Ref TObjectID="21173"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-108461" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4739.000000 -28.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108461" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21173"/>
     <cge:Term_Ref ObjectID="29545"/>
    <cge:TPSR_Ref TObjectID="21173"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-108457" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4739.000000 -28.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108457" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21173"/>
     <cge:Term_Ref ObjectID="29545"/>
    <cge:TPSR_Ref TObjectID="21173"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-108466" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4552.000000 -27.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108466" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21179"/>
     <cge:Term_Ref ObjectID="29557"/>
    <cge:TPSR_Ref TObjectID="21179"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-108467" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4552.000000 -27.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108467" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21179"/>
     <cge:Term_Ref ObjectID="29557"/>
    <cge:TPSR_Ref TObjectID="21179"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-108463" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4552.000000 -27.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108463" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21179"/>
     <cge:Term_Ref ObjectID="29557"/>
    <cge:TPSR_Ref TObjectID="21179"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-108478" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4360.000000 -28.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108478" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21190"/>
     <cge:Term_Ref ObjectID="29579"/>
    <cge:TPSR_Ref TObjectID="21190"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-108479" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4360.000000 -28.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108479" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21190"/>
     <cge:Term_Ref ObjectID="29579"/>
    <cge:TPSR_Ref TObjectID="21190"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-108475" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4360.000000 -28.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108475" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21190"/>
     <cge:Term_Ref ObjectID="29579"/>
    <cge:TPSR_Ref TObjectID="21190"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-108364" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4091.000000 -665.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108364" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21130"/>
     <cge:Term_Ref ObjectID="29476"/>
    <cge:TPSR_Ref TObjectID="21130"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-108363" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4091.000000 -665.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108363" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21130"/>
     <cge:Term_Ref ObjectID="29476"/>
    <cge:TPSR_Ref TObjectID="21130"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-108388" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4820.000000 -659.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108388" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21138"/>
     <cge:Term_Ref ObjectID="32588"/>
    <cge:TPSR_Ref TObjectID="21138"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-108387" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4820.000000 -659.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108387" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21138"/>
     <cge:Term_Ref ObjectID="32588"/>
    <cge:TPSR_Ref TObjectID="21138"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-108429" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3495.000000 -930.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108429" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29429"/>
     <cge:Term_Ref ObjectID="41916"/>
    <cge:TPSR_Ref TObjectID="29429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-108430" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3495.000000 -930.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108430" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29429"/>
     <cge:Term_Ref ObjectID="41916"/>
    <cge:TPSR_Ref TObjectID="29429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-108431" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3495.000000 -930.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108431" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29429"/>
     <cge:Term_Ref ObjectID="41916"/>
    <cge:TPSR_Ref TObjectID="29429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-108432" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3495.000000 -930.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108432" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29429"/>
     <cge:Term_Ref ObjectID="41916"/>
    <cge:TPSR_Ref TObjectID="29429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-108445" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3495.000000 -930.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108445" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29429"/>
     <cge:Term_Ref ObjectID="41916"/>
    <cge:TPSR_Ref TObjectID="29429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-108433" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4733.000000 -934.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108433" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29430"/>
     <cge:Term_Ref ObjectID="41917"/>
    <cge:TPSR_Ref TObjectID="29430"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-108434" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4733.000000 -934.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108434" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29430"/>
     <cge:Term_Ref ObjectID="41917"/>
    <cge:TPSR_Ref TObjectID="29430"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-108435" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4733.000000 -934.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108435" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29430"/>
     <cge:Term_Ref ObjectID="41917"/>
    <cge:TPSR_Ref TObjectID="29430"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-108436" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4733.000000 -934.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108436" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29430"/>
     <cge:Term_Ref ObjectID="41917"/>
    <cge:TPSR_Ref TObjectID="29430"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-108448" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4733.000000 -934.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108448" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29430"/>
     <cge:Term_Ref ObjectID="41917"/>
    <cge:TPSR_Ref TObjectID="29430"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-108505" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4259.000000 -605.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108505" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21228"/>
     <cge:Term_Ref ObjectID="29655"/>
    <cge:TPSR_Ref TObjectID="21228"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-108506" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4259.000000 -605.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108506" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21228"/>
     <cge:Term_Ref ObjectID="29655"/>
    <cge:TPSR_Ref TObjectID="21228"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-108499" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4259.000000 -605.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108499" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21228"/>
     <cge:Term_Ref ObjectID="29655"/>
    <cge:TPSR_Ref TObjectID="21228"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-108511" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3340.000000 101.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108511" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21214"/>
     <cge:Term_Ref ObjectID="29627"/>
    <cge:TPSR_Ref TObjectID="21214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-108512" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3340.000000 101.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108512" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21214"/>
     <cge:Term_Ref ObjectID="29627"/>
    <cge:TPSR_Ref TObjectID="21214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-108508" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3340.000000 101.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108508" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21214"/>
     <cge:Term_Ref ObjectID="29627"/>
    <cge:TPSR_Ref TObjectID="21214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-198710" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4119.000000 -28.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198710" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30384"/>
     <cge:Term_Ref ObjectID="43213"/>
    <cge:TPSR_Ref TObjectID="30384"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-198711" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4119.000000 -28.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198711" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30384"/>
     <cge:Term_Ref ObjectID="43213"/>
    <cge:TPSR_Ref TObjectID="30384"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-198706" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4119.000000 -28.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="198706" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30384"/>
     <cge:Term_Ref ObjectID="43213"/>
    <cge:TPSR_Ref TObjectID="30384"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-108517" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5107.000000 69.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108517" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21221"/>
     <cge:Term_Ref ObjectID="29641"/>
    <cge:TPSR_Ref TObjectID="21221"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-108518" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5107.000000 69.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108518" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21221"/>
     <cge:Term_Ref ObjectID="29641"/>
    <cge:TPSR_Ref TObjectID="21221"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-108514" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5107.000000 69.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108514" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21221"/>
     <cge:Term_Ref ObjectID="29641"/>
    <cge:TPSR_Ref TObjectID="21221"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-108437" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3435.000000 -546.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108437" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29431"/>
     <cge:Term_Ref ObjectID="41918"/>
    <cge:TPSR_Ref TObjectID="29431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-108438" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3435.000000 -546.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108438" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29431"/>
     <cge:Term_Ref ObjectID="41918"/>
    <cge:TPSR_Ref TObjectID="29431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-108439" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3435.000000 -546.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108439" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29431"/>
     <cge:Term_Ref ObjectID="41918"/>
    <cge:TPSR_Ref TObjectID="29431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-108440" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3435.000000 -546.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108440" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29431"/>
     <cge:Term_Ref ObjectID="41918"/>
    <cge:TPSR_Ref TObjectID="29431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="1" id="ME-108451" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3435.000000 -546.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108451" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29431"/>
     <cge:Term_Ref ObjectID="41918"/>
    <cge:TPSR_Ref TObjectID="29431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-108453" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3435.000000 -546.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108453" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29431"/>
     <cge:Term_Ref ObjectID="41918"/>
    <cge:TPSR_Ref TObjectID="29431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-108441" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4989.000000 -550.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108441" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29432"/>
     <cge:Term_Ref ObjectID="41919"/>
    <cge:TPSR_Ref TObjectID="29432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-108442" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4989.000000 -550.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108442" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29432"/>
     <cge:Term_Ref ObjectID="41919"/>
    <cge:TPSR_Ref TObjectID="29432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-108443" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4989.000000 -550.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108443" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29432"/>
     <cge:Term_Ref ObjectID="41919"/>
    <cge:TPSR_Ref TObjectID="29432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-108444" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4989.000000 -550.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108444" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29432"/>
     <cge:Term_Ref ObjectID="41919"/>
    <cge:TPSR_Ref TObjectID="29432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-108454" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4989.000000 -550.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108454" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29432"/>
     <cge:Term_Ref ObjectID="41919"/>
    <cge:TPSR_Ref TObjectID="29432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-108456" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4989.000000 -550.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="108456" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29432"/>
     <cge:Term_Ref ObjectID="41919"/>
    <cge:TPSR_Ref TObjectID="29432"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3174" y="-1124"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3174" y="-1124"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3173" y="-1160"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3173" y="-1160"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="3014" y="-1142"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="3014" y="-1142"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="2966" y="-1159"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="2966" y="-1159"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4360" y="-378"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4360" y="-378"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4557" y="-379"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4557" y="-379"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4737" y="-369"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4737" y="-369"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5111" y="-375"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5111" y="-375"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4922" y="-370"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4922" y="-370"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3669" y="-357"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3669" y="-357"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3511" y="-357"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3511" y="-357"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3356" y="-357"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3356" y="-357"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="33" qtmmishow="hidden" width="83" x="2923" y="-803"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="83" x="2923" y="-803"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4068" y="-952"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4068" y="-952"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3815" y="-955"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3815" y="-955"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="62" x="4721" y="-694"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="62" x="4721" y="-694"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="63" x="3994" y="-694"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="63" x="3994" y="-694"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4254" y="-938"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4254" y="-938"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="28" x="3828" y="-357"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="28" x="3828" y="-357"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="4127" y="-360"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="4127" y="-360"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="4245" y="-549"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="4245" y="-549"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_配调_配网接线图35_禄丰.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3174" y="-1124"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3173" y="-1160"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="3014" y="-1142"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="2966" y="-1159"/></g>
   <g href="35kV清水河变电站10kV工业园区Ⅱ回线062间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4360" y="-378"/></g>
   <g href="35kV清水河变电站10kV工业园区Ⅰ回线064间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4557" y="-379"/></g>
   <g href="35kV清水河变电站10kV金泰焊材厂线066间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4737" y="-369"/></g>
   <g href="35kV清水河变电站10kV2号电容器068间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5111" y="-375"/></g>
   <g href="35kV清水河变电站10kV大窝线072间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4922" y="-370"/></g>
   <g href="35kV清水河变电站10kV张武庄煤矿Ⅰ回线065间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3669" y="-357"/></g>
   <g href="35kV清水河变电站LF_QSH_063间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3511" y="-357"/></g>
   <g href="35kV清水河变电站LF_QSH_061间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3356" y="-357"/></g>
   <g href="35kV清水河变电站GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="83" x="2923" y="-803"/></g>
   <g href="35kV清水河变电站LF_QSH_362间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4068" y="-952"/></g>
   <g href="35kV清水河变电站LF_QSH_361间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3815" y="-955"/></g>
   <g href="35kV清水河变电站2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="62" x="4721" y="-694"/></g>
   <g href="35kV清水河变电站1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="63" x="3994" y="-694"/></g>
   <g href="35kV清水河变电站LF_QSH_312间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4254" y="-938"/></g>
   <g href="35kV清水河变电站10kV干海资集镇线067间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="28" x="3828" y="-357"/></g>
   <g href="35kV清水河变电站10kV煤矿小区Ⅰ回线069间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="4127" y="-360"/></g>
   <g href="35kV清水河变电站10kV分段Ia间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="4245" y="-549"/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3692,-601 3694,-604 3697,-602 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3704,-593 3706,-596 3709,-594 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3693,-584 3695,-587 3698,-585 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3682,-597 3679,-594 3686,-594 3684,-597 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3856,-135 3856,-125 3863,-129 3856,-134 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3943,-281 3943,-271 3936,-275 3943,-280 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4528,-603 4530,-606 4533,-604 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4540,-595 4542,-598 4545,-596 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4529,-586 4531,-589 4534,-587 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4518,-599 4515,-596 4522,-596 4520,-599 " stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_25ff770">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4513.000000 -1034.000000)" xlink:href="#voltageTransformer:shape104"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2603910">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4079.000000 -1130.000000)" xlink:href="#voltageTransformer:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2608d10">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3821.000000 -1132.000000)" xlink:href="#voltageTransformer:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_260bae0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3641.000000 -1047.000000)" xlink:href="#voltageTransformer:shape104"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-LF_QSH.LF_QSH_069Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4107.000000 -72.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34443" ObjectName="EC-LF_QSH.LF_QSH_069Ld"/>
    <cge:TPSR_Ref TObjectID="34443"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_QSH.LF_QSH_064Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4530.000000 -83.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34439" ObjectName="EC-LF_QSH.LF_QSH_064Ld"/>
    <cge:TPSR_Ref TObjectID="34439"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_QSH.LF_QSH_072Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4905.000000 -75.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34444" ObjectName="EC-LF_QSH.LF_QSH_072Ld"/>
    <cge:TPSR_Ref TObjectID="34444"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_QSH.LF_QSH_062Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4342.000000 -77.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34438" ObjectName="EC-LF_QSH.LF_QSH_062Ld"/>
    <cge:TPSR_Ref TObjectID="34438"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_QSH.LF_QSH_063Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3495.000000 -70.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34441" ObjectName="EC-LF_QSH.LF_QSH_063Ld"/>
    <cge:TPSR_Ref TObjectID="34441"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_QSH.LF_QSH_065Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3653.000000 -69.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34440" ObjectName="EC-LF_QSH.LF_QSH_065Ld"/>
    <cge:TPSR_Ref TObjectID="34440"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_QSH.LF_QSH_067Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3811.000000 -67.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34442" ObjectName="EC-LF_QSH.LF_QSH_067Ld"/>
    <cge:TPSR_Ref TObjectID="34442"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_QSH.LF_QSH_066Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4720.000000 -88.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34445" ObjectName="EC-LF_QSH.LF_QSH_066Ld"/>
    <cge:TPSR_Ref TObjectID="34445"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_QSH.LF_QSH_362Ld">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4045.000000 -1105.000000)" xlink:href="#load:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41855" ObjectName="EC-LF_QSH.LF_QSH_362Ld"/>
    <cge:TPSR_Ref TObjectID="41855"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_271f590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3883,-776 3896,-776 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_271f970@0" ObjectIDZND0="21134@0" Pin0InfoVect0LinkObjId="SW-108657_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_271f970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3883,-776 3896,-776 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_271f780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3932,-776 3950,-776 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21134@1" ObjectIDZND0="21131@x" ObjectIDZND1="21133@x" Pin0InfoVect0LinkObjId="SW-108588_0" Pin0InfoVect1LinkObjId="SW-108656_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108657_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3932,-776 3950,-776 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27b2f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3950,-713 3950,-733 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="21130@1" ObjectIDZND0="21131@0" Pin0InfoVect0LinkObjId="SW-108588_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29816c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3950,-713 3950,-733 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27b3160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3950,-760 3950,-776 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="21131@1" ObjectIDZND0="21134@x" ObjectIDZND1="21133@x" Pin0InfoVect0LinkObjId="SW-108657_0" Pin0InfoVect1LinkObjId="SW-108656_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108588_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3950,-760 3950,-776 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27b3350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3950,-776 3950,-796 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="21134@x" ObjectIDND1="21131@x" ObjectIDZND0="21133@1" Pin0InfoVect0LinkObjId="SW-108656_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108657_0" Pin1InfoVect1LinkObjId="SW-108588_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3950,-776 3950,-796 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27b3540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3950,-831 3950,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21133@0" ObjectIDZND0="29429@0" Pin0InfoVect0LinkObjId="g_2609910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108656_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3950,-831 3950,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2751530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4616,-766 4629,-766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2751910@0" ObjectIDZND0="21142@0" Pin0InfoVect0LinkObjId="SW-108760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2751910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4616,-766 4629,-766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2751720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4665,-766 4683,-766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21142@1" ObjectIDZND0="21139@x" ObjectIDZND1="21141@x" Pin0InfoVect0LinkObjId="SW-108691_0" Pin0InfoVect1LinkObjId="SW-108759_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108760_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4665,-766 4683,-766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27d7900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4681,-610 4711,-610 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="21138@x" ObjectIDND1="21145@x" ObjectIDZND0="g_268d260@0" Pin0InfoVect0LinkObjId="g_268d260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-108770_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4681,-610 4711,-610 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27d91a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4612,-515 4628,-515 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_29482e0@0" ObjectIDZND0="21144@0" Pin0InfoVect0LinkObjId="SW-108769_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29482e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4612,-515 4628,-515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27d9390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4664,-515 4682,-515 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21144@1" ObjectIDZND0="21140@x" ObjectIDZND1="21143@x" Pin0InfoVect0LinkObjId="SW-108723_0" Pin0InfoVect1LinkObjId="SW-108768_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108769_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4664,-515 4682,-515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27d9580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4682,-504 4682,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21143@0" ObjectIDZND0="21140@x" ObjectIDZND1="21144@x" Pin0InfoVect0LinkObjId="SW-108723_0" Pin0InfoVect1LinkObjId="SW-108769_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108768_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4682,-504 4682,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27d9770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4682,-513 4682,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21143@x" ObjectIDND1="21144@x" ObjectIDZND0="21140@0" Pin0InfoVect0LinkObjId="SW-108723_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108768_0" Pin1InfoVect1LinkObjId="SW-108769_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4682,-513 4682,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27d9960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4683,-710 4683,-723 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="21138@1" ObjectIDZND0="21139@0" Pin0InfoVect0LinkObjId="SW-108691_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4683,-710 4683,-723 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27de790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4683,-750 4683,-766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="21139@1" ObjectIDZND0="21142@x" ObjectIDZND1="21141@x" Pin0InfoVect0LinkObjId="SW-108760_0" Pin0InfoVect1LinkObjId="SW-108759_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108691_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4683,-750 4683,-766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27de980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4683,-766 4684,-788 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21139@x" ObjectIDND1="21142@x" ObjectIDZND0="21141@1" Pin0InfoVect0LinkObjId="SW-108759_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108691_0" Pin1InfoVect1LinkObjId="SW-108760_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4683,-766 4684,-788 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27deb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4683,-820 4683,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21141@0" ObjectIDZND0="29430@0" Pin0InfoVect0LinkObjId="g_25fcfe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108759_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4683,-820 4683,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27df900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4499,-845 4499,-870 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29430@0" ObjectIDZND0="21167@x" ObjectIDZND1="21166@x" Pin0InfoVect0LinkObjId="SW-108865_0" Pin0InfoVect1LinkObjId="SW-108864_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27deb70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4499,-845 4499,-870 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_277f9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4499,-870 4515,-870 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="29430@0" ObjectIDND1="21166@x" ObjectIDZND0="21167@0" Pin0InfoVect0LinkObjId="SW-108865_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_27deb70_0" Pin1InfoVect1LinkObjId="SW-108864_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4499,-870 4515,-870 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_277fba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4551,-870 4569,-870 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21167@1" ObjectIDZND0="g_277f200@0" Pin0InfoVect0LinkObjId="g_277f200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108865_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4551,-870 4569,-870 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26ed850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4431,-939 4444,-939 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_26eda40@0" ObjectIDZND0="21168@0" Pin0InfoVect0LinkObjId="SW-108866_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26eda40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4431,-939 4444,-939 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27c4f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4212,-845 4212,-870 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29429@0" ObjectIDZND0="21158@1" Pin0InfoVect0LinkObjId="SW-108841_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27b3540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4212,-845 4212,-870 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27c6a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4318,-845 4318,-870 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29430@0" ObjectIDZND0="21160@1" Pin0InfoVect0LinkObjId="SW-108843_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27deb70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4318,-845 4318,-870 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27c6c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4212,-901 4212,-914 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21158@0" ObjectIDZND0="21157@x" ObjectIDZND1="21159@x" Pin0InfoVect0LinkObjId="SW-108834_0" Pin0InfoVect1LinkObjId="SW-108842_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108841_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4212,-901 4212,-914 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27c6e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4318,-901 4318,-914 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21160@0" ObjectIDZND0="21157@x" ObjectIDZND1="21161@x" Pin0InfoVect0LinkObjId="SW-108834_0" Pin0InfoVect1LinkObjId="SW-108844_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108843_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4318,-901 4318,-914 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2799e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4212,-914 4253,-914 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21158@x" ObjectIDND1="21159@x" ObjectIDZND0="21157@1" Pin0InfoVect0LinkObjId="SW-108834_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108841_0" Pin1InfoVect1LinkObjId="SW-108842_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4212,-914 4253,-914 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2799ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4280,-914 4318,-914 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="21157@0" ObjectIDZND0="21160@x" ObjectIDZND1="21161@x" Pin0InfoVect0LinkObjId="SW-108843_0" Pin0InfoVect1LinkObjId="SW-108844_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108834_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4280,-914 4318,-914 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_274d7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4212,-914 4212,-935 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21157@x" ObjectIDND1="21158@x" ObjectIDZND0="21159@1" Pin0InfoVect0LinkObjId="SW-108842_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108834_0" Pin1InfoVect1LinkObjId="SW-108841_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4212,-914 4212,-935 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_274d9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4212,-966 4212,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21159@0" ObjectIDZND0="g_274e030@0" Pin0InfoVect0LinkObjId="g_274e030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108842_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4212,-966 4212,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_274dbf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4318,-914 4318,-934 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21157@x" ObjectIDND1="21160@x" ObjectIDZND0="21161@1" Pin0InfoVect0LinkObjId="SW-108844_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108834_0" Pin1InfoVect1LinkObjId="SW-108843_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4318,-914 4318,-934 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_274de10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4318,-966 4318,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21161@0" ObjectIDZND0="g_274e960@0" Pin0InfoVect0LinkObjId="g_274e960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108844_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4318,-966 4318,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2796ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4054,-845 4054,-865 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29429@0" ObjectIDZND0="21152@1" Pin0InfoVect0LinkObjId="SW-108804_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27b3540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4054,-845 4054,-865 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2796d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4054,-901 4054,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="21152@0" ObjectIDZND0="21153@x" ObjectIDZND1="21151@x" Pin0InfoVect0LinkObjId="SW-108805_0" Pin0InfoVect1LinkObjId="SW-108803_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108804_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4054,-901 4054,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2798d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4054,-918 4039,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="21152@x" ObjectIDND1="21151@x" ObjectIDZND0="21153@1" Pin0InfoVect0LinkObjId="SW-108805_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108804_0" Pin1InfoVect1LinkObjId="SW-108803_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4054,-918 4039,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2798f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4003,-918 3989,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21153@0" ObjectIDZND0="g_274fc60@0" Pin0InfoVect0LinkObjId="g_274fc60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108805_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4003,-918 3989,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2617040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4054,-918 4054,-933 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21152@x" ObjectIDND1="21153@x" ObjectIDZND0="21151@0" Pin0InfoVect0LinkObjId="SW-108803_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108804_0" Pin1InfoVect1LinkObjId="SW-108805_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4054,-918 4054,-933 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26191f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4054,-973 4040,-973 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21151@x" ObjectIDND1="21154@x" ObjectIDZND0="21155@1" Pin0InfoVect0LinkObjId="SW-108807_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108803_0" Pin1InfoVect1LinkObjId="SW-108806_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4054,-973 4040,-973 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2619410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4004,-973 3990,-973 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21155@0" ObjectIDZND0="g_27dbc30@0" Pin0InfoVect0LinkObjId="g_27dbc30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108807_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4004,-973 3990,-973 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27dc560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4054,-960 4054,-973 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="21151@1" ObjectIDZND0="21155@x" ObjectIDZND1="21154@x" Pin0InfoVect0LinkObjId="SW-108807_0" Pin0InfoVect1LinkObjId="SW-108806_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108803_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4054,-960 4054,-973 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_276bbf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4054,-1042 4039,-1042 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="21154@x" ObjectIDND1="g_2602ba0@0" ObjectIDND2="0@x" ObjectIDZND0="21156@1" Pin0InfoVect0LinkObjId="SW-108812_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-108806_0" Pin1InfoVect1LinkObjId="g_2602ba0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4054,-1042 4039,-1042 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_276be10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4003,-1042 3989,-1042 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21156@0" ObjectIDZND0="g_276c030@0" Pin0InfoVect0LinkObjId="g_276c030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108812_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4003,-1042 3989,-1042 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2782cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4054,-973 4054,-989 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="21155@x" ObjectIDND1="21151@x" ObjectIDZND0="21154@1" Pin0InfoVect0LinkObjId="SW-108806_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108807_0" Pin1InfoVect1LinkObjId="SW-108803_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4054,-973 4054,-989 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2782ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4054,-1025 4054,-1042 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="21154@0" ObjectIDZND0="21156@x" ObjectIDZND1="g_2602ba0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-108812_0" Pin0InfoVect1LinkObjId="g_2602ba0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108806_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4054,-1025 4054,-1042 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_270d6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3803,-845 3803,-868 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29429@0" ObjectIDZND0="21147@1" Pin0InfoVect0LinkObjId="SW-108773_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27b3540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3803,-845 3803,-868 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_270d890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3803,-904 3803,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21147@0" ObjectIDZND0="21146@x" ObjectIDZND1="21148@x" Pin0InfoVect0LinkObjId="SW-108772_0" Pin0InfoVect1LinkObjId="SW-108774_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108773_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3803,-904 3803,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_270fa40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3803,-921 3788,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21146@x" ObjectIDND1="21147@x" ObjectIDZND0="21148@1" Pin0InfoVect0LinkObjId="SW-108774_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108772_0" Pin1InfoVect1LinkObjId="SW-108773_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3803,-921 3788,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_270fc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3752,-921 3738,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21148@0" ObjectIDZND0="g_270fe80@0" Pin0InfoVect0LinkObjId="g_270fe80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108774_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3752,-921 3738,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_274c4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3803,-921 3803,-936 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21147@x" ObjectIDND1="21148@x" ObjectIDZND0="21146@0" Pin0InfoVect0LinkObjId="SW-108772_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108773_0" Pin1InfoVect1LinkObjId="SW-108774_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3803,-921 3803,-936 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26cefd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3803,-976 3789,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21146@x" ObjectIDND1="21149@x" ObjectIDZND0="21150@1" Pin0InfoVect0LinkObjId="SW-108776_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108772_0" Pin1InfoVect1LinkObjId="SW-108775_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3803,-976 3789,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26cf1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3753,-976 3739,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21150@0" ObjectIDZND0="g_26cf3e0@0" Pin0InfoVect0LinkObjId="g_26cf3e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108776_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3753,-976 3739,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26cfd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3803,-963 3803,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="21146@1" ObjectIDZND0="21150@x" ObjectIDZND1="21149@x" Pin0InfoVect0LinkObjId="SW-108776_0" Pin0InfoVect1LinkObjId="SW-108775_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108772_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3803,-963 3803,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2704fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3803,-1046 3788,-1046 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="21149@x" ObjectIDND1="g_2607fa0@0" ObjectIDND2="0@x" ObjectIDZND0="32004@1" Pin0InfoVect0LinkObjId="SW-108781_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-108775_0" Pin1InfoVect1LinkObjId="g_2607fa0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3803,-1046 3788,-1046 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2705200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3752,-1046 3738,-1046 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32004@0" ObjectIDZND0="g_2705420@0" Pin0InfoVect0LinkObjId="g_2705420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108781_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3752,-1046 3738,-1046 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2707df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3803,-976 3803,-993 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21146@x" ObjectIDND1="21150@x" ObjectIDZND0="21149@1" Pin0InfoVect0LinkObjId="SW-108775_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108772_0" Pin1InfoVect1LinkObjId="SW-108776_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3803,-976 3803,-993 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_272d290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3803,-1029 3803,-1046 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="21149@0" ObjectIDZND0="32004@x" ObjectIDZND1="g_2607fa0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-108781_0" Pin0InfoVect1LinkObjId="g_2607fa0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108775_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3803,-1029 3803,-1046 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_272ed10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3623,-876 3639,-876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="busSection" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="29429@0" ObjectIDND1="29429@0" ObjectIDND2="21164@x" ObjectIDZND0="21164@0" Pin0InfoVect0LinkObjId="SW-108862_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_27b3540_0" Pin1InfoVect1LinkObjId="g_27b3540_0" Pin1InfoVect2LinkObjId="SW-108862_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3623,-876 3639,-876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_272ef40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3675,-876 3693,-876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21164@1" ObjectIDZND0="g_272e790@0" Pin0InfoVect0LinkObjId="g_272e790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108862_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3675,-876 3693,-876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_272f750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3623,-893 3623,-916 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="21163@1" Pin0InfoVect0LinkObjId="SW-108861_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3623,-893 3623,-916 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_272fd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3555,-960 3568,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2730250@0" ObjectIDZND0="21165@0" Pin0InfoVect0LinkObjId="SW-108863_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2730250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3555,-960 3568,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_272fff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3604,-960 3623,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="21165@1" ObjectIDZND0="21163@x" ObjectIDZND1="g_2609dd0@0" ObjectIDZND2="g_260a650@0" Pin0InfoVect0LinkObjId="SW-108861_0" Pin0InfoVect1LinkObjId="g_2609dd0_0" Pin0InfoVect2LinkObjId="g_260a650_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108863_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3604,-960 3623,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26c5660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3821,-446 3821,-431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29431@0" ObjectIDZND0="21209@0" Pin0InfoVect0LinkObjId="SW-109030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29837d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3821,-446 3821,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26c5ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3749,-378 3766,-378 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_292c530@0" ObjectIDZND0="21210@0" Pin0InfoVect0LinkObjId="SW-109031_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_292c530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3749,-378 3766,-378 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26c5ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3802,-378 3820,-378 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21210@1" ObjectIDZND0="21208@x" ObjectIDZND1="21209@x" Pin0InfoVect0LinkObjId="SW-109027_0" Pin0InfoVect1LinkObjId="SW-109030_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109031_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3802,-378 3820,-378 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26c6140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3820,-395 3820,-379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="21209@1" ObjectIDZND0="21210@x" ObjectIDZND1="21208@x" Pin0InfoVect0LinkObjId="SW-109031_0" Pin0InfoVect1LinkObjId="SW-109027_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109030_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3820,-395 3820,-379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26c67a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3820,-379 3820,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21210@x" ObjectIDND1="21209@x" ObjectIDZND0="21208@1" Pin0InfoVect0LinkObjId="SW-109027_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-109031_0" Pin1InfoVect1LinkObjId="SW-109030_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3820,-379 3820,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26c6f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3821,-150 3806,-150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="21213@x" ObjectIDND1="21212@x" ObjectIDND2="0@x" ObjectIDZND0="g_273a0c0@0" Pin0InfoVect0LinkObjId="g_273a0c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-109036_0" Pin1InfoVect1LinkObjId="SW-109034_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3821,-150 3806,-150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26c7b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3821,-150 3837,-150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_273a0c0@0" ObjectIDND1="21212@x" ObjectIDND2="0@x" ObjectIDZND0="21213@0" Pin0InfoVect0LinkObjId="SW-109036_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_273a0c0_0" Pin1InfoVect1LinkObjId="SW-109034_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3821,-150 3837,-150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2669560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3873,-150 3891,-150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21213@1" ObjectIDZND0="g_26c7600@0" Pin0InfoVect0LinkObjId="g_26c7600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109036_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3873,-150 3891,-150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2669d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3676,-447 3676,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29431@0" ObjectIDZND0="21169@1" Pin0InfoVect0LinkObjId="SW-108871_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29837d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3676,-447 3676,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_266a380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3657,-534 3676,-534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="21170@1" ObjectIDZND0="21169@x" ObjectIDZND1="g_28fcb20@0" Pin0InfoVect0LinkObjId="SW-108871_0" Pin0InfoVect1LinkObjId="g_28fcb20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108872_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3657,-534 3676,-534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_266a5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3676,-518 3676,-534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="21169@0" ObjectIDZND0="g_28fcb20@0" ObjectIDZND1="21170@x" Pin0InfoVect0LinkObjId="g_28fcb20_0" Pin0InfoVect1LinkObjId="SW-108872_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108871_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3676,-518 3676,-534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_266a820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3676,-534 3676,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="21169@x" ObjectIDND1="21170@x" ObjectIDZND0="g_28fcb20@0" Pin0InfoVect0LinkObjId="g_28fcb20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108871_0" Pin1InfoVect1LinkObjId="SW-108872_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3676,-534 3676,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_266aa80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3608,-534 3621,-534 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_266ace0@0" ObjectIDZND0="21170@0" Pin0InfoVect0LinkObjId="SW-108872_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_266ace0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3608,-534 3621,-534 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2774180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3661,-446 3661,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29431@0" ObjectIDZND0="21203@0" Pin0InfoVect0LinkObjId="SW-109005_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29837d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3661,-446 3661,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2776910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3590,-379 3607,-379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_292aef0@0" ObjectIDZND0="21204@0" Pin0InfoVect0LinkObjId="SW-109006_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_292aef0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3590,-379 3607,-379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2776b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3643,-379 3661,-379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="21204@1" ObjectIDZND0="21203@x" ObjectIDZND1="21202@x" Pin0InfoVect0LinkObjId="SW-109005_0" Pin0InfoVect1LinkObjId="SW-109002_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109006_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3643,-379 3661,-379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2776dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3661,-394 3661,-379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="21203@1" ObjectIDZND0="21204@x" ObjectIDZND1="21202@x" Pin0InfoVect0LinkObjId="SW-109006_0" Pin0InfoVect1LinkObjId="SW-109002_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109005_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3661,-394 3661,-379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2637400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3661,-379 3661,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21204@x" ObjectIDND1="21203@x" ObjectIDZND0="21202@1" Pin0InfoVect0LinkObjId="SW-109002_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-109006_0" Pin1InfoVect1LinkObjId="SW-109005_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3661,-379 3661,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27636d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3662,-150 3647,-150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="21207@x" ObjectIDND1="34440@x" ObjectIDND2="21206@x" ObjectIDZND0="g_2762960@0" Pin0InfoVect0LinkObjId="g_2762960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-109011_0" Pin1InfoVect1LinkObjId="EC-LF_QSH.LF_QSH_065Ld_0" Pin1InfoVect2LinkObjId="SW-109009_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3662,-150 3647,-150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26134c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3662,-150 3678,-150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2762960@0" ObjectIDND1="34440@x" ObjectIDND2="21206@x" ObjectIDZND0="21207@0" Pin0InfoVect0LinkObjId="SW-109011_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2762960_0" Pin1InfoVect1LinkObjId="EC-LF_QSH.LF_QSH_065Ld_0" Pin1InfoVect2LinkObjId="SW-109009_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3662,-150 3678,-150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2613720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3714,-150 3732,-150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21207@1" ObjectIDZND0="g_2765e60@0" Pin0InfoVect0LinkObjId="g_2765e60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109011_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3714,-150 3732,-150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2616040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3503,-446 3503,-431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29431@0" ObjectIDZND0="21197@0" Pin0InfoVect0LinkObjId="SW-108980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29837d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3503,-446 3503,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_278bed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3432,-379 3449,-379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2917dc0@0" ObjectIDZND0="21198@0" Pin0InfoVect0LinkObjId="SW-108981_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2917dc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3432,-379 3449,-379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_278c130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3485,-379 3503,-379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="21198@1" ObjectIDZND0="21197@x" ObjectIDZND1="21196@x" Pin0InfoVect0LinkObjId="SW-108980_0" Pin0InfoVect1LinkObjId="SW-108977_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108981_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3485,-379 3503,-379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_278c390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3503,-395 3503,-379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="21197@1" ObjectIDZND0="21198@x" ObjectIDZND1="21196@x" Pin0InfoVect0LinkObjId="SW-108981_0" Pin0InfoVect1LinkObjId="SW-108977_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108980_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3503,-395 3503,-379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_278e640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3503,-379 3503,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21198@x" ObjectIDND1="21197@x" ObjectIDZND0="21196@1" Pin0InfoVect0LinkObjId="SW-108977_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108981_0" Pin1InfoVect1LinkObjId="SW-108980_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3503,-379 3503,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_271e1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3504,-150 3489,-150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="21201@x" ObjectIDND1="21200@x" ObjectIDND2="g_271d450@0" ObjectIDZND0="g_271d450@0" Pin0InfoVect0LinkObjId="g_271d450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-108986_0" Pin1InfoVect1LinkObjId="SW-108984_0" Pin1InfoVect2LinkObjId="g_271d450_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3504,-150 3489,-150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27542c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3504,-150 3520,-150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_271d450@0" ObjectIDND1="21200@x" ObjectIDND2="g_271d450@0" ObjectIDZND0="21201@0" Pin0InfoVect0LinkObjId="SW-108986_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_271d450_0" Pin1InfoVect1LinkObjId="SW-108984_0" Pin1InfoVect2LinkObjId="g_271d450_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3504,-150 3520,-150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2754520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3556,-150 3574,-150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21201@1" ObjectIDZND0="g_2753830@0" Pin0InfoVect0LinkObjId="g_2753830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108986_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3556,-150 3574,-150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_279cb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3347,-447 3347,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29431@0" ObjectIDZND0="21215@0" Pin0InfoVect0LinkObjId="SW-109055_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29837d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3347,-447 3347,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_273f9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3329,-199 3347,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="21218@1" ObjectIDZND0="21219@x" ObjectIDZND1="g_26df930@0" Pin0InfoVect0LinkObjId="SW-109059_0" Pin0InfoVect1LinkObjId="g_26df930_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109058_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3329,-199 3347,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27406d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3347,-181 3347,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="21219@0" ObjectIDZND0="21218@x" ObjectIDZND1="g_26df930@0" Pin0InfoVect0LinkObjId="SW-109058_0" Pin0InfoVect1LinkObjId="g_26df930_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109059_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3347,-181 3347,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2740930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3347,-122 3359,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="21220@x" ObjectIDND1="0@x" ObjectIDND2="21219@x" ObjectIDZND0="g_268ba50@0" Pin0InfoVect0LinkObjId="g_268ba50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-109060_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-109059_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3347,-122 3359,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27430c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3328,-122 3347,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="21220@1" ObjectIDZND0="0@x" ObjectIDZND1="21219@x" ObjectIDZND2="g_268ba50@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-109059_0" Pin0InfoVect2LinkObjId="g_268ba50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109060_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3328,-122 3347,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_273d4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4728,-458 4728,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29432@0" ObjectIDZND0="21174@0" Pin0InfoVect0LinkObjId="SW-108881_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_296de70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4728,-458 4728,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2714d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4656,-387 4673,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_295e800@0" ObjectIDZND0="21175@0" Pin0InfoVect0LinkObjId="SW-108882_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_295e800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4656,-387 4673,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27469f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4729,-162 4714,-162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="21178@x" ObjectIDND1="21177@x" ObjectIDND2="21177@x" ObjectIDZND0="g_2745c80@0" Pin0InfoVect0LinkObjId="g_2745c80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-108887_0" Pin1InfoVect1LinkObjId="SW-108885_0" Pin1InfoVect2LinkObjId="SW-108885_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4729,-162 4714,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2749c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4729,-162 4745,-162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2745c80@0" ObjectIDND1="21177@x" ObjectIDND2="21177@x" ObjectIDZND0="21178@0" Pin0InfoVect0LinkObjId="SW-108887_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2745c80_0" Pin1InfoVect1LinkObjId="SW-108885_0" Pin1InfoVect2LinkObjId="SW-108885_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4729,-162 4745,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2749e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4781,-162 4799,-162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21178@1" ObjectIDZND0="g_2749180@0" Pin0InfoVect0LinkObjId="g_2749180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108887_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4781,-162 4799,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_274a0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4728,-162 4728,-117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_2745c80@0" ObjectIDND1="21178@x" ObjectIDND2="21177@x" ObjectIDZND0="34445@0" Pin0InfoVect0LinkObjId="EC-LF_QSH.LF_QSH_066Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2745c80_0" Pin1InfoVect1LinkObjId="SW-108887_0" Pin1InfoVect2LinkObjId="SW-108885_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4728,-162 4728,-117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2675340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4350,-458 4350,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29432@0" ObjectIDZND0="21191@0" Pin0InfoVect0LinkObjId="SW-108955_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_296de70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4350,-458 4350,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2677ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-393 4350,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="21192@1" ObjectIDZND0="21191@x" ObjectIDZND1="21190@x" Pin0InfoVect0LinkObjId="SW-108955_0" Pin0InfoVect1LinkObjId="SW-108952_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108956_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-393 4350,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26fb030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4351,-163 4336,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="34438@x" ObjectIDND1="21195@x" ObjectIDND2="21194@x" ObjectIDZND0="g_26fa2c0@0" Pin0InfoVect0LinkObjId="g_26fa2c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-LF_QSH.LF_QSH_062Ld_0" Pin1InfoVect1LinkObjId="SW-108961_0" Pin1InfoVect2LinkObjId="SW-108959_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4351,-163 4336,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26fb290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4351,-163 4351,-104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_26fa2c0@0" ObjectIDND1="21195@x" ObjectIDND2="21194@x" ObjectIDZND0="34438@0" Pin0InfoVect0LinkObjId="EC-LF_QSH.LF_QSH_062Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_26fa2c0_0" Pin1InfoVect1LinkObjId="SW-108961_0" Pin1InfoVect2LinkObjId="SW-108959_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4351,-163 4351,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26e2ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4351,-163 4367,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_26fa2c0@0" ObjectIDND1="34438@x" ObjectIDND2="21194@x" ObjectIDZND0="21195@0" Pin0InfoVect0LinkObjId="SW-108961_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_26fa2c0_0" Pin1InfoVect1LinkObjId="EC-LF_QSH.LF_QSH_062Ld_0" Pin1InfoVect2LinkObjId="SW-108959_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4351,-163 4367,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26e2f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4403,-163 4421,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21195@1" ObjectIDZND0="g_26e2210@0" Pin0InfoVect0LinkObjId="g_26e2210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108961_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4403,-163 4421,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26e5820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4539,-458 4539,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29432@0" ObjectIDZND0="21180@0" Pin0InfoVect0LinkObjId="SW-108906_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_296de70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4539,-458 4539,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26f5050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4540,-164 4525,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="21183@x" ObjectIDND1="34311@x" ObjectIDND2="34439@x" ObjectIDZND0="g_295c510@0" Pin0InfoVect0LinkObjId="g_295c510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-108911_0" Pin1InfoVect1LinkObjId="SW-219565_0" Pin1InfoVect2LinkObjId="EC-LF_QSH.LF_QSH_064Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4540,-164 4525,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26f52b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4539,-163 4539,-110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="21183@x" ObjectIDND1="34311@x" ObjectIDND2="g_295c510@0" ObjectIDZND0="34439@0" Pin0InfoVect0LinkObjId="EC-LF_QSH.LF_QSH_064Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-108911_0" Pin1InfoVect1LinkObjId="SW-219565_0" Pin1InfoVect2LinkObjId="g_295c510_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4539,-163 4539,-110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26fe680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4540,-164 4556,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="34311@x" ObjectIDND1="g_295c510@0" ObjectIDND2="34439@x" ObjectIDZND0="21183@0" Pin0InfoVect0LinkObjId="SW-108911_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-219565_0" Pin1InfoVect1LinkObjId="g_295c510_0" Pin1InfoVect2LinkObjId="EC-LF_QSH.LF_QSH_064Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4540,-164 4556,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26fe8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4592,-164 4610,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21183@1" ObjectIDZND0="g_26fdbf0@0" Pin0InfoVect0LinkObjId="g_26fdbf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108911_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4592,-164 4610,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2701200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4913,-458 4913,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29432@0" ObjectIDZND0="21185@0" Pin0InfoVect0LinkObjId="SW-108930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_296de70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4913,-458 4913,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27587e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4841,-393 4858,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2967850@0" ObjectIDZND0="21186@0" Pin0InfoVect0LinkObjId="SW-108931_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2967850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4841,-393 4858,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2758a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-393 4912,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21186@1" ObjectIDZND0="21184@x" ObjectIDZND1="21185@x" Pin0InfoVect0LinkObjId="SW-108927_0" Pin0InfoVect1LinkObjId="SW-108930_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108931_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-393 4912,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2758ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4913,-401 4913,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="21185@1" ObjectIDZND0="21186@x" ObjectIDZND1="21184@x" Pin0InfoVect0LinkObjId="SW-108931_0" Pin0InfoVect1LinkObjId="SW-108927_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108930_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4913,-401 4913,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_275af50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4913,-392 4913,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21186@x" ObjectIDND1="21185@x" ObjectIDZND0="21184@1" Pin0InfoVect0LinkObjId="SW-108927_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108931_0" Pin1InfoVect1LinkObjId="SW-108930_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4913,-392 4913,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_275b1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4913,-349 4913,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21184@0" ObjectIDZND0="21187@0" Pin0InfoVect0LinkObjId="SW-108933_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108927_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4913,-349 4913,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26cb100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4914,-163 4899,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="34444@x" ObjectIDND1="21189@x" ObjectIDND2="21188@x" ObjectIDZND0="g_26ca390@0" Pin0InfoVect0LinkObjId="g_26ca390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-LF_QSH.LF_QSH_072Ld_0" Pin1InfoVect1LinkObjId="SW-108936_0" Pin1InfoVect2LinkObjId="SW-108934_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4914,-163 4899,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26cb360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4914,-163 4914,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_26ca390@0" ObjectIDND1="21189@x" ObjectIDND2="21188@x" ObjectIDZND0="34444@0" Pin0InfoVect0LinkObjId="EC-LF_QSH.LF_QSH_072Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_26ca390_0" Pin1InfoVect1LinkObjId="SW-108936_0" Pin1InfoVect2LinkObjId="SW-108934_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4914,-163 4914,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26ce580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4914,-163 4930,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_26ca390@0" ObjectIDND1="34444@x" ObjectIDND2="21188@x" ObjectIDZND0="21189@0" Pin0InfoVect0LinkObjId="SW-108936_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_26ca390_0" Pin1InfoVect1LinkObjId="EC-LF_QSH.LF_QSH_072Ld_0" Pin1InfoVect2LinkObjId="SW-108934_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4914,-163 4930,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26ce7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4966,-163 4984,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21189@1" ObjectIDZND0="g_26cdaf0@0" Pin0InfoVect0LinkObjId="g_26cdaf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108936_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4966,-163 4984,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2671050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5030,-397 5047,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_296f150@0" ObjectIDZND0="21223@0" Pin0InfoVect0LinkObjId="SW-109079_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_296f150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5030,-397 5047,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26712b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5083,-397 5101,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="21223@1" ObjectIDZND0="21222@x" ObjectIDZND1="21221@x" Pin0InfoVect0LinkObjId="SW-109078_0" Pin0InfoVect1LinkObjId="SW-109074_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109079_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5083,-397 5101,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2671510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5102,-409 5102,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="21222@1" ObjectIDZND0="21223@x" ObjectIDZND1="21221@x" Pin0InfoVect0LinkObjId="SW-109079_0" Pin0InfoVect1LinkObjId="SW-109074_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109078_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5102,-409 5102,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26629b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5102,-397 5102,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21223@x" ObjectIDND1="21222@x" ObjectIDZND0="21221@1" Pin0InfoVect0LinkObjId="SW-109074_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-109079_0" Pin1InfoVect1LinkObjId="SW-109078_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5102,-397 5102,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2667800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5084,-217 5102,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="21225@1" ObjectIDZND0="21226@x" ObjectIDZND1="g_26974c0@0" Pin0InfoVect0LinkObjId="SW-109082_0" Pin0InfoVect1LinkObjId="g_26974c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109081_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5084,-217 5102,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27b8400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5102,-199 5102,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="21226@0" ObjectIDZND0="21225@x" ObjectIDZND1="g_26974c0@0" Pin0InfoVect0LinkObjId="SW-109081_0" Pin0InfoVect1LinkObjId="g_26974c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109082_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5102,-199 5102,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27b8660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5102,-151 5117,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="21226@x" ObjectIDND1="21227@x" ObjectIDND2="g_268c520@0" ObjectIDZND0="g_268c520@0" Pin0InfoVect0LinkObjId="g_268c520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-109082_0" Pin1InfoVect1LinkObjId="SW-109083_0" Pin1InfoVect2LinkObjId="g_268c520_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5102,-151 5117,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27b88c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5102,-151 5102,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_268c520@0" ObjectIDND1="21227@x" ObjectIDND2="g_268c520@0" ObjectIDZND0="21226@1" Pin0InfoVect0LinkObjId="SW-109082_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_268c520_0" Pin1InfoVect1LinkObjId="SW-109083_0" Pin1InfoVect2LinkObjId="g_268c520_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5102,-151 5102,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27baf90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5034,-151 5047,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_27bb450@0" ObjectIDZND0="21227@0" Pin0InfoVect0LinkObjId="SW-109083_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27bb450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5034,-151 5047,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27bb1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5083,-151 5102,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="21227@1" ObjectIDZND0="g_268c520@0" ObjectIDZND1="21226@x" ObjectIDZND2="g_268c520@0" Pin0InfoVect0LinkObjId="g_268c520_0" Pin0InfoVect1LinkObjId="SW-109082_0" Pin0InfoVect2LinkObjId="g_268c520_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109083_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5083,-151 5102,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27be800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-458 4512,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29432@0" ObjectIDZND0="21171@1" Pin0InfoVect0LinkObjId="SW-108873_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_296de70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-458 4512,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26a4cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4444,-538 4457,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_26a4f10@0" ObjectIDZND0="21172@0" Pin0InfoVect0LinkObjId="SW-108874_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26a4f10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4444,-538 4457,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2679d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4219,-447 4219,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29431@0" ObjectIDZND0="28116@1" Pin0InfoVect0LinkObjId="SW-185506_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29837d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4219,-447 4219,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_269aa00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4350,-163 4350,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_26fa2c0@0" ObjectIDND1="34438@x" ObjectIDND2="21195@x" ObjectIDZND0="21194@1" Pin0InfoVect0LinkObjId="SW-108959_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_26fa2c0_0" Pin1InfoVect1LinkObjId="EC-LF_QSH.LF_QSH_062Ld_0" Pin1InfoVect2LinkObjId="SW-108961_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4350,-163 4350,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_269ac60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4539,-164 4539,-180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="21183@x" ObjectIDND1="g_295c510@0" ObjectIDND2="34439@x" ObjectIDZND0="34311@1" Pin0InfoVect0LinkObjId="SW-219565_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-108911_0" Pin1InfoVect1LinkObjId="g_295c510_0" Pin1InfoVect2LinkObjId="EC-LF_QSH.LF_QSH_064Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4539,-164 4539,-180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_269aec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4539,-216 4539,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="34311@0" ObjectIDZND0="g_2698f60@1" Pin0InfoVect0LinkObjId="g_2698f60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-219565_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4539,-216 4539,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_269b120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4728,-162 4728,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2745c80@0" ObjectIDND1="21178@x" ObjectIDND2="21177@x" ObjectIDZND0="21177@1" Pin0InfoVect0LinkObjId="SW-108885_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2745c80_0" Pin1InfoVect1LinkObjId="SW-108887_0" Pin1InfoVect2LinkObjId="SW-108885_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4728,-162 4728,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_269b380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4728,-209 4728,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="21177@0" ObjectIDZND0="g_2698210@1" Pin0InfoVect0LinkObjId="g_2698210_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108885_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4728,-209 4728,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_269b5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5102,-217 5102,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="21225@x" ObjectIDND1="21226@x" ObjectIDZND0="g_26974c0@1" Pin0InfoVect0LinkObjId="g_26974c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-109081_0" Pin1InfoVect1LinkObjId="SW-109082_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5102,-217 5102,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_269b840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4913,-163 4913,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_26ca390@0" ObjectIDND1="34444@x" ObjectIDND2="21189@x" ObjectIDZND0="21188@1" Pin0InfoVect0LinkObjId="SW-108934_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_26ca390_0" Pin1InfoVect1LinkObjId="EC-LF_QSH.LF_QSH_072Ld_0" Pin1InfoVect2LinkObjId="SW-108936_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4913,-163 4913,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_269baa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4913,-209 4913,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="21188@0" ObjectIDZND0="g_2696770@1" Pin0InfoVect0LinkObjId="g_2696770_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108934_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4913,-209 4913,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_269bd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3820,-150 3820,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_273a0c0@0" ObjectIDND1="21213@x" ObjectIDND2="0@x" ObjectIDZND0="21212@1" Pin0InfoVect0LinkObjId="SW-109034_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_273a0c0_0" Pin1InfoVect1LinkObjId="SW-109036_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3820,-150 3820,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_269bf60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3820,-197 3820,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="21212@0" ObjectIDZND0="g_2695a20@1" Pin0InfoVect0LinkObjId="g_2695a20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109034_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3820,-197 3820,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_269c1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3661,-197 3661,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="21206@0" ObjectIDZND0="g_2694cd0@1" Pin0InfoVect0LinkObjId="g_2694cd0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109009_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3661,-197 3661,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_269c420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3503,-150 3503,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_271d450@0" ObjectIDND1="21201@x" ObjectIDND2="g_271d450@0" ObjectIDZND0="21200@1" Pin0InfoVect0LinkObjId="SW-108984_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_271d450_0" Pin1InfoVect1LinkObjId="SW-108986_0" Pin1InfoVect2LinkObjId="g_271d450_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3503,-150 3503,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_269c680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3503,-197 3503,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="21200@0" ObjectIDZND0="g_2693ff0@1" Pin0InfoVect0LinkObjId="g_2693ff0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108984_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3503,-197 3503,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2688880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3270,-122 3292,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2743320@0" ObjectIDZND0="21220@0" Pin0InfoVect0LinkObjId="SW-109060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2743320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3270,-122 3292,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_268dfd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3347,-338 3347,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21214@0" ObjectIDZND0="21217@0" Pin0InfoVect0LinkObjId="SW-109057_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109052_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3347,-338 3347,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_268e230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3503,-336 3503,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21196@0" ObjectIDZND0="21199@0" Pin0InfoVect0LinkObjId="SW-108983_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108977_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3503,-336 3503,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_268e490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3661,-336 3661,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21202@0" ObjectIDZND0="21205@0" Pin0InfoVect0LinkObjId="SW-109008_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109002_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3661,-336 3661,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_268e6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3820,-336 3820,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21208@0" ObjectIDZND0="21211@0" Pin0InfoVect0LinkObjId="SW-109033_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109027_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3820,-336 3820,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_268ef20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-537 4512,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="21171@x" ObjectIDND1="21172@x" ObjectIDZND0="g_293c9d0@0" Pin0InfoVect0LinkObjId="g_293c9d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108873_0" Pin1InfoVect1LinkObjId="SW-108874_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-537 4512,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_268fa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-521 4512,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="21171@0" ObjectIDZND0="21172@x" ObjectIDZND1="g_293c9d0@0" Pin0InfoVect0LinkObjId="SW-108874_0" Pin0InfoVect1LinkObjId="g_293c9d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108873_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-521 4512,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_268fc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-537 4493,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="21171@x" ObjectIDND1="g_293c9d0@0" ObjectIDZND0="21172@1" Pin0InfoVect0LinkObjId="SW-108874_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108873_0" Pin1InfoVect1LinkObjId="g_293c9d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-537 4493,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_282b5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4682,-458 4682,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29432@0" ObjectIDZND0="21143@1" Pin0InfoVect0LinkObjId="SW-108768_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_296de70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4682,-458 4682,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_282b810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4914,-163 4913,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_26ca390@0" ObjectIDND1="34444@x" ObjectIDND2="21189@x" ObjectIDZND0="21188@1" Pin0InfoVect0LinkObjId="SW-108934_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_26ca390_0" Pin1InfoVect1LinkObjId="EC-LF_QSH.LF_QSH_072Ld_0" Pin1InfoVect2LinkObjId="SW-108936_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4914,-163 4913,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_282ba50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5101,-409 5102,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDZND0="21223@x" ObjectIDZND1="21222@x" ObjectIDZND2="21221@x" Pin0InfoVect0LinkObjId="SW-109079_0" Pin0InfoVect1LinkObjId="SW-109078_0" Pin0InfoVect2LinkObjId="SW-109074_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5101,-409 5102,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_282bcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5102,-163 5102,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDZND0="g_268c520@0" ObjectIDZND1="21226@x" ObjectIDZND2="21227@x" Pin0InfoVect0LinkObjId="g_268c520_0" Pin0InfoVect1LinkObjId="SW-109082_0" Pin0InfoVect2LinkObjId="SW-109083_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5102,-163 5102,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_282bf10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4728,-173 4729,-162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="21177@1" ObjectIDZND0="g_2745c80@0" ObjectIDZND1="21178@x" ObjectIDZND2="21177@x" Pin0InfoVect0LinkObjId="g_2745c80_0" Pin0InfoVect1LinkObjId="SW-108887_0" Pin0InfoVect2LinkObjId="SW-108885_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108885_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4728,-173 4729,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_282c170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4539,-405 4541,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="21180@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108906_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4539,-405 4541,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_282c3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4350,-404 4350,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="21191@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108955_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4350,-404 4350,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_282c630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3661,-160 3662,-150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="21206@1" ObjectIDZND0="g_2762960@0" ObjectIDZND1="21207@x" ObjectIDZND2="34440@x" Pin0InfoVect0LinkObjId="g_2762960_0" Pin0InfoVect1LinkObjId="SW-109011_0" Pin0InfoVect2LinkObjId="EC-LF_QSH.LF_QSH_065Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109009_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3661,-160 3662,-150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_282c890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3662,-150 3662,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_2762960@0" ObjectIDND1="21207@x" ObjectIDND2="21206@x" ObjectIDZND0="34440@0" Pin0InfoVect0LinkObjId="EC-LF_QSH.LF_QSH_065Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2762960_0" Pin1InfoVect1LinkObjId="SW-109011_0" Pin1InfoVect2LinkObjId="SW-109009_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3662,-150 3662,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_282caf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3504,-150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_271d450@0" ObjectIDND1="21201@x" ObjectIDND2="21200@x" ObjectIDZND0="g_271d450@0" ObjectIDZND1="21201@x" ObjectIDZND2="21200@x" Pin0InfoVect0LinkObjId="g_271d450_0" Pin0InfoVect1LinkObjId="SW-108986_0" Pin0InfoVect2LinkObjId="SW-108984_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_271d450_0" Pin1InfoVect1LinkObjId="SW-108986_0" Pin1InfoVect2LinkObjId="SW-108984_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3504,-150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28336f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3504,-150 3504,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_271d450@0" ObjectIDND1="21201@x" ObjectIDND2="21200@x" ObjectIDZND0="34441@0" Pin0InfoVect0LinkObjId="EC-LF_QSH.LF_QSH_063Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_271d450_0" Pin1InfoVect1LinkObjId="SW-108986_0" Pin1InfoVect2LinkObjId="SW-108984_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3504,-150 3504,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2836010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4115,-446 4115,-431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29431@0" ObjectIDZND0="30385@0" Pin0InfoVect0LinkObjId="SW-198684_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29837d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4115,-446 4115,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28387a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4044,-386 4061,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_29350e0@0" ObjectIDZND0="30386@0" Pin0InfoVect0LinkObjId="SW-198687_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29350e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4044,-386 4061,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2838a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4097,-386 4115,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="30386@1" ObjectIDZND0="30385@x" ObjectIDZND1="30384@x" Pin0InfoVect0LinkObjId="SW-198684_0" Pin0InfoVect1LinkObjId="SW-185504_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-198687_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4097,-386 4115,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_283c090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-151 4101,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="30387@x" ObjectIDND1="34443@x" ObjectIDND2="30388@x" ObjectIDZND0="g_283b320@0" Pin0InfoVect0LinkObjId="g_283b320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-198688_0" Pin1InfoVect1LinkObjId="EC-LF_QSH.LF_QSH_069Ld_0" Pin1InfoVect2LinkObjId="SW-198686_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-151 4101,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25d2b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-151 4132,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_283b320@0" ObjectIDND1="34443@x" ObjectIDND2="30388@x" ObjectIDZND0="30387@0" Pin0InfoVect0LinkObjId="SW-198688_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_283b320_0" Pin1InfoVect1LinkObjId="EC-LF_QSH.LF_QSH_069Ld_0" Pin1InfoVect2LinkObjId="SW-198686_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-151 4132,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25d2d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4168,-151 4186,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="30387@1" ObjectIDZND0="g_25d20e0@0" Pin0InfoVect0LinkObjId="g_25d20e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-198688_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4168,-151 4186,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25d4e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4115,-198 4115,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="30388@0" ObjectIDZND0="g_25d4510@1" Pin0InfoVect0LinkObjId="g_25d4510_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-198686_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4115,-198 4115,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25d5090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4115,-337 4115,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="30384@0" ObjectIDZND0="30389@0" Pin0InfoVect0LinkObjId="SW-198685_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185504_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4115,-337 4115,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25d52f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-161 4116,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="30388@1" ObjectIDZND0="g_283b320@0" ObjectIDZND1="30387@x" ObjectIDZND2="34443@x" Pin0InfoVect0LinkObjId="g_283b320_0" Pin0InfoVect1LinkObjId="SW-198688_0" Pin0InfoVect2LinkObjId="EC-LF_QSH.LF_QSH_069Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-198686_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-161 4116,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25d5550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-151 4116,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_283b320@0" ObjectIDND1="30387@x" ObjectIDND2="30388@x" ObjectIDZND0="34443@0" Pin0InfoVect0LinkObjId="EC-LF_QSH.LF_QSH_069Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_283b320_0" Pin1InfoVect1LinkObjId="SW-198688_0" Pin1InfoVect2LinkObjId="SW-198686_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-151 4116,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25fcd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4418,-773 4418,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4418,-773 4418,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25fcfe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4418,-832 4418,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="29430@0" Pin0InfoVect0LinkObjId="g_27deb70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4418,-832 4418,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25fd810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4499,-870 4499,-893 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="21167@x" ObjectIDND1="29430@0" ObjectIDZND0="21166@1" Pin0InfoVect0LinkObjId="SW-108864_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108865_0" Pin1InfoVect1LinkObjId="g_27deb70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4499,-870 4499,-893 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25fda70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4480,-939 4499,-939 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="21168@1" ObjectIDZND0="21166@x" ObjectIDZND1="g_27faba0@0" ObjectIDZND2="g_27fb190@0" Pin0InfoVect0LinkObjId="SW-108864_0" Pin0InfoVect1LinkObjId="g_27faba0_0" Pin0InfoVect2LinkObjId="g_27fb190_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108866_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4480,-939 4499,-939 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25fe560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4499,-924 4499,-939 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="21166@0" ObjectIDZND0="21168@x" ObjectIDZND1="g_27faba0@0" ObjectIDZND2="g_27fb190@0" Pin0InfoVect0LinkObjId="SW-108866_0" Pin0InfoVect1LinkObjId="g_27faba0_0" Pin0InfoVect2LinkObjId="g_27fb190_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108864_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4499,-924 4499,-939 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25fe7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4499,-939 4499,-974 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="21168@x" ObjectIDND1="21166@x" ObjectIDZND0="g_27faba0@0" ObjectIDZND1="g_27fb190@0" Pin0InfoVect0LinkObjId="g_27faba0_0" Pin0InfoVect1LinkObjId="g_27fb190_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108866_0" Pin1InfoVect1LinkObjId="SW-108864_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4499,-939 4499,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25ff2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4499,-974 4532,-974 4532,-987 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="21168@x" ObjectIDND1="21166@x" ObjectIDND2="g_27fb190@0" ObjectIDZND0="g_27faba0@0" Pin0InfoVect0LinkObjId="g_27faba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-108866_0" Pin1InfoVect1LinkObjId="SW-108864_0" Pin1InfoVect2LinkObjId="g_27fb190_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4499,-974 4532,-974 4532,-987 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25ff510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4461,-993 4461,-974 4499,-974 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_27fb190@0" ObjectIDZND0="21168@x" ObjectIDZND1="21166@x" ObjectIDZND2="g_27faba0@0" Pin0InfoVect0LinkObjId="SW-108866_0" Pin0InfoVect1LinkObjId="SW-108864_0" Pin0InfoVect2LinkObjId="g_27faba0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27fb190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4461,-993 4461,-974 4499,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2602370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4532,-1018 4532,-1039 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_27faba0@1" ObjectIDZND0="g_25ff770@0" Pin0InfoVect0LinkObjId="g_25ff770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27faba0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4532,-1018 4532,-1039 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2604010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4086,-1123 4086,-1135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_2603910@0" Pin0InfoVect0LinkObjId="g_2603910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4086,-1123 4086,-1135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2604270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4029,-1088 4029,-1079 4054,-1079 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2602ba0@0" ObjectIDZND0="21156@x" ObjectIDZND1="21154@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-108812_0" Pin0InfoVect1LinkObjId="SW-108806_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2602ba0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4029,-1088 4029,-1079 4054,-1079 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2604f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4054,-1110 4054,-1079 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="41855@0" ObjectIDZND0="g_2602ba0@0" ObjectIDZND1="21156@x" ObjectIDZND2="21154@x" Pin0InfoVect0LinkObjId="g_2602ba0_0" Pin0InfoVect1LinkObjId="SW-108812_0" Pin0InfoVect2LinkObjId="SW-108806_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-LF_QSH.LF_QSH_362Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4054,-1110 4054,-1079 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26051e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4054,-1079 4054,-1042 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="load" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2602ba0@0" ObjectIDND1="0@x" ObjectIDND2="41855@x" ObjectIDZND0="21156@x" ObjectIDZND1="21154@x" Pin0InfoVect0LinkObjId="SW-108812_0" Pin0InfoVect1LinkObjId="SW-108806_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2602ba0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="EC-LF_QSH.LF_QSH_362Ld_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4054,-1079 4054,-1042 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2605440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4054,-1079 4086,-1079 4086,-1087 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2602ba0@0" ObjectIDND1="21156@x" ObjectIDND2="21154@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2602ba0_0" Pin1InfoVect1LinkObjId="SW-108812_0" Pin1InfoVect2LinkObjId="SW-108806_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4054,-1079 4086,-1079 4086,-1087 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2609410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3828,-1126 3828,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_2608d10@0" Pin0InfoVect0LinkObjId="g_2608d10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3828,-1126 3828,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2609910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3623,-845 3623,-876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="busSection" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="29429@0" ObjectIDZND0="29429@0" ObjectIDZND1="21164@x" ObjectIDZND2="21164@x" Pin0InfoVect0LinkObjId="g_27b3540_0" Pin0InfoVect1LinkObjId="SW-108862_0" Pin0InfoVect2LinkObjId="SW-108862_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27b3540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3623,-845 3623,-876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2609b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3623,-876 3623,-893 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="29429@0" ObjectIDND1="21164@x" ObjectIDZND0="29429@0" ObjectIDZND1="21164@x" Pin0InfoVect0LinkObjId="g_27b3540_0" Pin0InfoVect1LinkObjId="SW-108862_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_27b3540_0" Pin1InfoVect1LinkObjId="SW-108862_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3623,-876 3623,-893 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_260b3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3627,-987 3660,-987 3660,-1000 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_260a650@0" ObjectIDND1="21165@x" ObjectIDND2="21163@x" ObjectIDZND0="g_2609dd0@0" Pin0InfoVect0LinkObjId="g_2609dd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_260a650_0" Pin1InfoVect1LinkObjId="SW-108863_0" Pin1InfoVect2LinkObjId="SW-108861_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3627,-987 3660,-987 3660,-1000 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_260b620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3589,-1006 3589,-987 3627,-987 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_260a650@0" ObjectIDZND0="g_2609dd0@0" ObjectIDZND1="21165@x" ObjectIDZND2="21163@x" Pin0InfoVect0LinkObjId="g_2609dd0_0" Pin0InfoVect1LinkObjId="SW-108863_0" Pin0InfoVect2LinkObjId="SW-108861_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_260a650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3589,-1006 3589,-987 3627,-987 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_260b880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3660,-1031 3660,-1052 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2609dd0@1" ObjectIDZND0="g_260bae0@0" Pin0InfoVect0LinkObjId="g_260bae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2609dd0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3660,-1031 3660,-1052 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_260f800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3623,-947 3623,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="21163@0" ObjectIDZND0="21165@x" ObjectIDZND1="g_2609dd0@0" ObjectIDZND2="g_260a650@0" Pin0InfoVect0LinkObjId="SW-108863_0" Pin0InfoVect1LinkObjId="g_2609dd0_0" Pin0InfoVect2LinkObjId="g_260a650_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108861_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3623,-947 3623,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_260fa60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3623,-987 3623,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2609dd0@0" ObjectIDND1="g_260a650@0" ObjectIDZND0="21165@x" ObjectIDZND1="21163@x" Pin0InfoVect0LinkObjId="SW-108863_0" Pin0InfoVect1LinkObjId="SW-108861_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2609dd0_0" Pin1InfoVect1LinkObjId="g_260a650_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3623,-987 3623,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28f8ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-511 3869,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_28f8250@0" ObjectIDZND0="21136@0" Pin0InfoVect0LinkObjId="SW-108666_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28f8250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-511 3869,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28fa0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3771,-1091 3771,-1084 3803,-1084 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2607fa0@0" ObjectIDZND0="21149@x" ObjectIDZND1="32004@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-108775_0" Pin0InfoVect1LinkObjId="SW-108781_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2607fa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3771,-1091 3771,-1084 3803,-1084 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28fac00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3803,-1116 3803,-1084 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="18139@1" ObjectIDZND0="g_2607fa0@0" ObjectIDZND1="21149@x" ObjectIDZND2="32004@x" Pin0InfoVect0LinkObjId="g_2607fa0_0" Pin0InfoVect1LinkObjId="SW-108775_0" Pin0InfoVect2LinkObjId="SW-108781_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3803,-1116 3803,-1084 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28fadf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3803,-1084 3803,-1046 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2607fa0@0" ObjectIDND1="0@x" ObjectIDND2="18139@1" ObjectIDZND0="21149@x" ObjectIDZND1="32004@x" Pin0InfoVect0LinkObjId="SW-108775_0" Pin0InfoVect1LinkObjId="SW-108781_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2607fa0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3803,-1084 3803,-1046 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28fb000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3803,-1084 3828,-1084 3828,-1090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2607fa0@0" ObjectIDND1="21149@x" ObjectIDND2="32004@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2607fa0_0" Pin1InfoVect1LinkObjId="SW-108775_0" Pin1InfoVect2LinkObjId="SW-108781_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3803,-1084 3828,-1084 3828,-1090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28fffb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4219,-523 4202,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="28116@x" ObjectIDND1="21228@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-185506_0" Pin1InfoVect1LinkObjId="SW-209598_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4219,-523 4202,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2900aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4219,-508 4219,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="28116@0" ObjectIDZND0="21228@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-209598_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185506_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4219,-508 4219,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2900d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4219,-523 4245,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="28116@x" ObjectIDND1="0@x" ObjectIDZND0="21228@1" Pin0InfoVect0LinkObjId="SW-209598_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-185506_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4219,-523 4245,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2900f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4166,-523 4142,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_28ff520@0" Pin0InfoVect0LinkObjId="g_28ff520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4166,-523 4142,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29044a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4375,-523 4391,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28119@1" ObjectIDZND0="g_2903a10@0" Pin0InfoVect0LinkObjId="g_2903a10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185507_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4375,-523 4391,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2906450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-530 4305,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2905940@0" ObjectIDZND0="21228@x" ObjectIDZND1="28118@x" ObjectIDZND2="28119@x" Pin0InfoVect0LinkObjId="SW-209598_0" Pin0InfoVect1LinkObjId="SW-185508_0" Pin0InfoVect2LinkObjId="SW-185507_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2905940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4305,-530 4305,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2906f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4272,-523 4305,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="21228@0" ObjectIDZND0="g_2905940@0" ObjectIDZND1="28118@x" ObjectIDZND2="28119@x" Pin0InfoVect0LinkObjId="g_2905940_0" Pin0InfoVect1LinkObjId="SW-185508_0" Pin0InfoVect2LinkObjId="SW-185507_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-209598_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4272,-523 4305,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29071a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4323,-458 4323,-473 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29432@0" ObjectIDZND0="28118@0" Pin0InfoVect0LinkObjId="SW-185508_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_296de70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4323,-458 4323,-473 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29079d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4323,-509 4323,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="28118@1" ObjectIDZND0="28119@x" ObjectIDZND1="g_2905940@0" ObjectIDZND2="21228@x" Pin0InfoVect0LinkObjId="SW-185507_0" Pin0InfoVect1LinkObjId="g_2905940_0" Pin0InfoVect2LinkObjId="SW-209598_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185508_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4323,-509 4323,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29084c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4339,-523 4323,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="28119@0" ObjectIDZND0="28118@x" ObjectIDZND1="g_2905940@0" ObjectIDZND2="21228@x" Pin0InfoVect0LinkObjId="SW-185508_0" Pin0InfoVect1LinkObjId="g_2905940_0" Pin0InfoVect2LinkObjId="SW-209598_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185507_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4339,-523 4323,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2908720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4323,-523 4305,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="28118@x" ObjectIDND1="28119@x" ObjectIDZND0="g_2905940@0" ObjectIDZND1="21228@x" Pin0InfoVect0LinkObjId="g_2905940_0" Pin0InfoVect1LinkObjId="SW-209598_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-185508_0" Pin1InfoVect1LinkObjId="SW-185507_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4323,-523 4305,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29099e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3261,-380 3285,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2908f50@0" ObjectIDZND0="21216@0" Pin0InfoVect0LinkObjId="SW-109056_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2908f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3261,-380 3285,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2909c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3321,-380 3347,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="21216@1" ObjectIDZND0="21215@x" ObjectIDZND1="21214@x" Pin0InfoVect0LinkObjId="SW-109055_0" Pin0InfoVect1LinkObjId="SW-109052_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109056_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3321,-380 3347,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_290a730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3347,-394 3347,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="21215@1" ObjectIDZND0="21216@x" ObjectIDZND1="21214@x" Pin0InfoVect0LinkObjId="SW-109056_0" Pin0InfoVect1LinkObjId="SW-109052_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109055_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3347,-394 3347,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_290a990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3347,-380 3347,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21216@x" ObjectIDND1="21215@x" ObjectIDZND0="21214@1" Pin0InfoVect0LinkObjId="SW-109052_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-109056_0" Pin1InfoVect1LinkObjId="SW-109055_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3347,-380 3347,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2910780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3347,-208 3347,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_26df930@1" ObjectIDZND0="21218@x" ObjectIDZND1="21219@x" Pin0InfoVect0LinkObjId="SW-109058_0" Pin0InfoVect1LinkObjId="SW-109059_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26df930_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3347,-208 3347,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29109e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3328,-264 3347,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_290fdb0@0" ObjectIDZND0="21217@x" ObjectIDZND1="g_26df930@0" Pin0InfoVect0LinkObjId="SW-109057_0" Pin0InfoVect1LinkObjId="g_26df930_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_290fdb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3328,-264 3347,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29114d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3347,-275 3347,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="21217@1" ObjectIDZND0="g_290fdb0@0" ObjectIDZND1="g_26df930@0" Pin0InfoVect0LinkObjId="g_290fdb0_0" Pin0InfoVect1LinkObjId="g_26df930_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109057_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3347,-275 3347,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2911730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3347,-264 3347,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_290fdb0@0" ObjectIDND1="21217@x" ObjectIDZND0="g_26df930@0" Pin0InfoVect0LinkObjId="g_26df930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_290fdb0_0" Pin1InfoVect1LinkObjId="SW-109057_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3347,-264 3347,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2911990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3293,-199 3270,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21218@0" ObjectIDZND0="g_273fc40@0" Pin0InfoVect0LinkObjId="g_273fc40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109058_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3293,-199 3270,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2914170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3347,-122 3347,-108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="capacitor" ObjectIDND0="21220@x" ObjectIDND1="21219@x" ObjectIDND2="g_268ba50@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-109060_0" Pin1InfoVect1LinkObjId="SW-109059_0" Pin1InfoVect2LinkObjId="g_268ba50_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3347,-122 3347,-108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2916b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3347,-13 3347,-6 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3347,-13 3347,-6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2916dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3347,30 3347,39 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2688150@0" Pin0InfoVect0LinkObjId="g_2688150_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3347,30 3347,39 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2917030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3347,-145 3347,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="lightningRod" ObjectIDND0="21219@1" ObjectIDZND0="21220@x" ObjectIDZND1="0@x" ObjectIDZND2="g_268ba50@0" Pin0InfoVect0LinkObjId="SW-109060_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_268ba50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109059_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3347,-145 3347,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2927950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3483,-266 3502,-266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2926f70@0" ObjectIDZND0="g_2693ff0@0" ObjectIDZND1="21199@x" Pin0InfoVect0LinkObjId="g_2693ff0_0" Pin0InfoVect1LinkObjId="SW-108983_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2926f70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3483,-266 3502,-266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2928440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3503,-258 3503,-266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2693ff0@0" ObjectIDZND0="g_2926f70@0" ObjectIDZND1="21199@x" Pin0InfoVect0LinkObjId="g_2926f70_0" Pin0InfoVect1LinkObjId="SW-108983_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2693ff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3503,-258 3503,-266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29286a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3503,-266 3503,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2926f70@0" ObjectIDND1="g_2693ff0@0" ObjectIDZND0="21199@1" Pin0InfoVect0LinkObjId="SW-108983_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2926f70_0" Pin1InfoVect1LinkObjId="g_2693ff0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3503,-266 3503,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29296b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3641,-265 3660,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2928900@0" ObjectIDZND0="g_2694cd0@0" ObjectIDZND1="21205@x" Pin0InfoVect0LinkObjId="g_2694cd0_0" Pin0InfoVect1LinkObjId="SW-109008_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2928900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3641,-265 3660,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_292a1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3661,-255 3661,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2694cd0@0" ObjectIDZND0="g_2928900@0" ObjectIDZND1="21205@x" Pin0InfoVect0LinkObjId="g_2928900_0" Pin0InfoVect1LinkObjId="SW-109008_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2694cd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3661,-255 3661,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_292a400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3661,-265 3661,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2928900@0" ObjectIDND1="g_2694cd0@0" ObjectIDZND0="21205@1" Pin0InfoVect0LinkObjId="SW-109008_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2928900_0" Pin1InfoVect1LinkObjId="g_2694cd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3661,-265 3661,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_292dd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3820,-254 3820,-268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2695a20@0" ObjectIDZND0="21211@x" ObjectIDZND1="g_292cfc0@0" Pin0InfoVect0LinkObjId="SW-109033_0" Pin0InfoVect1LinkObjId="g_292cfc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2695a20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3820,-254 3820,-268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_292e860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3820,-272 3820,-268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="21211@1" ObjectIDZND0="g_2695a20@0" ObjectIDZND1="g_292cfc0@0" Pin0InfoVect0LinkObjId="g_2695a20_0" Pin0InfoVect1LinkObjId="g_292cfc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109033_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3820,-272 3820,-268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_292eac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3820,-268 3801,-268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2695a20@0" ObjectIDND1="21211@x" ObjectIDZND0="g_292cfc0@0" Pin0InfoVect0LinkObjId="g_292cfc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2695a20_0" Pin1InfoVect1LinkObjId="SW-109033_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3820,-268 3801,-268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2933bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3960,-210 3959,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3960,-210 3959,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2933e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3959,-266 3959,-275 3916,-275 3916,-129 3820,-129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_273a0c0@0" ObjectIDZND1="21213@x" ObjectIDZND2="21212@x" Pin0InfoVect0LinkObjId="g_273a0c0_0" Pin0InfoVect1LinkObjId="SW-109036_0" Pin0InfoVect2LinkObjId="SW-109034_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3959,-266 3959,-275 3916,-275 3916,-129 3820,-129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2934c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4115,-395 4115,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="30385@1" ObjectIDZND0="30386@x" ObjectIDZND1="30384@x" Pin0InfoVect0LinkObjId="SW-198687_0" Pin0InfoVect1LinkObjId="SW-185504_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-198684_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4115,-395 4115,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2934e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4115,-364 4115,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="30384@1" ObjectIDZND0="30386@x" ObjectIDZND1="30385@x" Pin0InfoVect0LinkObjId="SW-198687_0" Pin0InfoVect1LinkObjId="SW-198684_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185504_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4115,-364 4115,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_293ba20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4115,-266 4096,-266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_25d4510@0" ObjectIDND1="30389@x" ObjectIDZND0="g_293b050@0" Pin0InfoVect0LinkObjId="g_293b050_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_25d4510_0" Pin1InfoVect1LinkObjId="SW-198685_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4115,-266 4096,-266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_293c510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4115,-259 4115,-266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_25d4510@0" ObjectIDZND0="g_293b050@0" ObjectIDZND1="30389@x" Pin0InfoVect0LinkObjId="g_293b050_0" Pin0InfoVect1LinkObjId="SW-198685_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25d4510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4115,-259 4115,-266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_293c770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4115,-266 4115,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_293b050@0" ObjectIDND1="g_25d4510@0" ObjectIDZND0="30389@1" Pin0InfoVect0LinkObjId="SW-198685_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_293b050_0" Pin1InfoVect1LinkObjId="g_25d4510_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4115,-266 4115,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2942b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4682,-621 4682,-610 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="21138@0" ObjectIDZND0="g_268d260@0" ObjectIDZND1="21145@x" Pin0InfoVect0LinkObjId="g_268d260_0" Pin0InfoVect1LinkObjId="SW-108770_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4682,-621 4682,-610 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2947300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4682,-610 4682,-600 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_268d260@0" ObjectIDND1="21138@x" ObjectIDZND0="21145@0" Pin0InfoVect0LinkObjId="SW-108770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_268d260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4682,-610 4682,-600 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2947560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4682,-564 4682,-554 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21145@1" ObjectIDZND0="21140@1" Pin0InfoVect0LinkObjId="SW-108723_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108770_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4682,-564 4682,-554 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_294b330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4350,-404 4350,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="21191@1" ObjectIDZND0="21192@x" ObjectIDZND1="21190@x" Pin0InfoVect0LinkObjId="SW-108956_0" Pin0InfoVect1LinkObjId="SW-108952_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108955_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4350,-404 4350,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_294b590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4350,-393 4350,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21192@x" ObjectIDND1="21191@x" ObjectIDZND0="21190@1" Pin0InfoVect0LinkObjId="SW-108952_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108956_0" Pin1InfoVect1LinkObjId="SW-108955_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4350,-393 4350,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_294c280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4273,-393 4296,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_294b7f0@0" ObjectIDZND0="21192@0" Pin0InfoVect0LinkObjId="SW-108956_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_294b7f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4273,-393 4296,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_294c800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4350,-349 4350,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21190@0" ObjectIDZND0="21193@0" Pin0InfoVect0LinkObjId="SW-108958_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108952_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4350,-349 4350,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2954800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4521,-392 4539,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21181@1" ObjectIDZND0="21179@x" ObjectIDZND1="21180@x" Pin0InfoVect0LinkObjId="SW-108903_0" Pin0InfoVect1LinkObjId="SW-108906_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108907_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4521,-392 4539,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29552f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4539,-404 4539,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="21180@1" ObjectIDZND0="21181@x" ObjectIDZND1="21179@x" Pin0InfoVect0LinkObjId="SW-108907_0" Pin0InfoVect1LinkObjId="SW-108903_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108906_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4539,-404 4539,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2955550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4539,-392 4539,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21181@x" ObjectIDND1="21180@x" ObjectIDZND0="21179@1" Pin0InfoVect0LinkObjId="SW-108903_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108907_0" Pin1InfoVect1LinkObjId="SW-108906_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4539,-392 4539,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2956240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4466,-392 4485,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_29557b0@0" ObjectIDZND0="21181@0" Pin0InfoVect0LinkObjId="SW-108907_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29557b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4466,-392 4485,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2956cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4539,-350 4539,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21179@0" ObjectIDZND0="21182@0" Pin0InfoVect0LinkObjId="SW-108909_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108903_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4539,-350 4539,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_295d850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4709,-387 4728,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="21175@1" ObjectIDZND0="21174@x" ObjectIDZND1="21173@x" Pin0InfoVect0LinkObjId="SW-108881_0" Pin0InfoVect1LinkObjId="SW-108878_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108882_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4709,-387 4728,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_295e340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4728,-403 4728,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="21174@1" ObjectIDZND0="21175@x" ObjectIDZND1="21173@x" Pin0InfoVect0LinkObjId="SW-108882_0" Pin0InfoVect1LinkObjId="SW-108878_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108881_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4728,-403 4728,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_295e5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4728,-387 4728,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21175@x" ObjectIDND1="21174@x" ObjectIDZND0="21173@1" Pin0InfoVect0LinkObjId="SW-108878_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108882_0" Pin1InfoVect1LinkObjId="SW-108881_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4728,-387 4728,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_295f5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4728,-348 4728,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21173@0" ObjectIDZND0="21176@0" Pin0InfoVect0LinkObjId="SW-108884_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108878_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4728,-348 4728,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2965080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4520,-281 4539,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_295bb40@0" ObjectIDZND0="g_2698f60@0" ObjectIDZND1="21182@x" Pin0InfoVect0LinkObjId="g_2698f60_0" Pin0InfoVect1LinkObjId="SW-108909_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_295bb40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4520,-281 4539,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29652e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-275 4728,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_29646b0@0" ObjectIDZND0="g_2698210@0" ObjectIDZND1="21176@x" Pin0InfoVect0LinkObjId="g_2698210_0" Pin0InfoVect1LinkObjId="SW-108884_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29646b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-275 4728,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2965dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4728,-268 4728,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2698210@0" ObjectIDZND0="g_29646b0@0" ObjectIDZND1="21176@x" Pin0InfoVect0LinkObjId="g_29646b0_0" Pin0InfoVect1LinkObjId="SW-108884_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2698210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4728,-268 4728,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2966030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4728,-275 4728,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_29646b0@0" ObjectIDND1="g_2698210@0" ObjectIDZND0="21176@1" Pin0InfoVect0LinkObjId="SW-108884_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29646b0_0" Pin1InfoVect1LinkObjId="g_2698210_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4728,-275 4728,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_296de70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5102,-445 5102,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21222@0" ObjectIDZND0="29432@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109078_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5102,-445 5102,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_296ff00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5102,-354 5102,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21221@0" ObjectIDZND0="21224@0" Pin0InfoVect0LinkObjId="SW-109080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109074_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5102,-354 5102,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2975000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5084,-286 5102,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_26f42e0@0" ObjectIDZND0="g_26974c0@0" ObjectIDZND1="21224@x" Pin0InfoVect0LinkObjId="g_26974c0_0" Pin0InfoVect1LinkObjId="SW-109080_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26f42e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5084,-286 5102,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2975970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5102,-280 5102,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_26974c0@0" ObjectIDZND0="g_26f42e0@0" ObjectIDZND1="21224@x" Pin0InfoVect0LinkObjId="g_26f42e0_0" Pin0InfoVect1LinkObjId="SW-109080_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26974c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5102,-280 5102,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2975b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5102,-286 5102,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_26f42e0@0" ObjectIDND1="g_26974c0@0" ObjectIDZND0="21224@1" Pin0InfoVect0LinkObjId="SW-109080_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_26f42e0_0" Pin1InfoVect1LinkObjId="g_26974c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5102,-286 5102,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2975d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5035,-217 5048,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2667a60@0" ObjectIDZND0="21225@0" Pin0InfoVect0LinkObjId="SW-109081_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2667a60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5035,-217 5048,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2975fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5102,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_268c520@0" ObjectIDND1="21226@x" ObjectIDND2="21227@x" ObjectIDZND0="g_268c520@0" ObjectIDZND1="21226@x" ObjectIDZND2="21227@x" Pin0InfoVect0LinkObjId="g_268c520_0" Pin0InfoVect1LinkObjId="SW-109082_0" Pin0InfoVect2LinkObjId="SW-109083_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_268c520_0" Pin1InfoVect1LinkObjId="SW-109082_0" Pin1InfoVect2LinkObjId="SW-109083_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5102,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2979280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5102,-42 5102,-35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5102,-42 5102,-35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29794e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5102,1 5102,10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2976200@0" Pin0InfoVect0LinkObjId="g_2976200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5102,1 5102,10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_297bf60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5102,-151 5102,-137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="capacitor" ObjectIDND0="g_268c520@0" ObjectIDND1="21226@x" ObjectIDND2="21227@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_268c520_0" Pin1InfoVect1LinkObjId="SW-109082_0" Pin1InfoVect2LinkObjId="SW-109083_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5102,-151 5102,-137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_297ca50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3820,-150 3820,-129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_273a0c0@0" ObjectIDND1="21213@x" ObjectIDND2="21212@x" ObjectIDZND0="0@x" ObjectIDZND1="34442@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="EC-LF_QSH.LF_QSH_067Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_273a0c0_0" Pin1InfoVect1LinkObjId="SW-109036_0" Pin1InfoVect2LinkObjId="SW-109034_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3820,-150 3820,-129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_297ccb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3820,-129 3820,-94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="0@x" ObjectIDND1="g_273a0c0@0" ObjectIDND2="21213@x" ObjectIDZND0="34442@0" Pin0InfoVect0LinkObjId="EC-LF_QSH.LF_QSH_067Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_273a0c0_0" Pin1InfoVect2LinkObjId="SW-109036_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3820,-129 3820,-94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_297cf10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4350,-216 4350,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2699cb0@1" ObjectIDZND0="21194@0" Pin0InfoVect0LinkObjId="SW-108959_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2699cb0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4350,-216 4350,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_297d170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4330,-275 4350,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2951900@0" ObjectIDZND0="21193@x" ObjectIDZND1="g_2699cb0@0" Pin0InfoVect0LinkObjId="SW-108958_0" Pin0InfoVect1LinkObjId="g_2699cb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2951900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4330,-275 4350,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_297dc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4350,-288 4350,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="21193@1" ObjectIDZND0="g_2951900@0" ObjectIDZND1="g_2699cb0@0" Pin0InfoVect0LinkObjId="g_2951900_0" Pin0InfoVect1LinkObjId="g_2699cb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108958_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4350,-288 4350,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_297dec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4350,-275 4350,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2951900@0" ObjectIDND1="21193@x" ObjectIDZND0="g_2699cb0@0" Pin0InfoVect0LinkObjId="g_2699cb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2951900_0" Pin1InfoVect1LinkObjId="SW-108958_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4350,-275 4350,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_297e9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4539,-274 4539,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2698f60@0" ObjectIDZND0="g_295bb40@0" ObjectIDZND1="21182@x" Pin0InfoVect0LinkObjId="g_295bb40_0" Pin0InfoVect1LinkObjId="SW-108909_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2698f60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4539,-274 4539,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_297ec10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4539,-281 4539,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_295bb40@0" ObjectIDND1="g_2698f60@0" ObjectIDZND0="21182@1" Pin0InfoVect0LinkObjId="SW-108909_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_295bb40_0" Pin1InfoVect1LinkObjId="g_2698f60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4539,-281 4539,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_297f440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4882,-282 4913,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_296d4a0@0" ObjectIDZND0="21187@x" ObjectIDZND1="g_2696770@0" Pin0InfoVect0LinkObjId="SW-108933_0" Pin0InfoVect1LinkObjId="g_2696770_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_296d4a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4882,-282 4913,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_297ff30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4913,-289 4913,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="21187@1" ObjectIDZND0="g_296d4a0@0" ObjectIDZND1="g_2696770@0" Pin0InfoVect0LinkObjId="g_296d4a0_0" Pin0InfoVect1LinkObjId="g_2696770_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108933_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4913,-289 4913,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2980190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4913,-282 4913,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_296d4a0@0" ObjectIDND1="21187@x" ObjectIDZND0="g_2696770@0" Pin0InfoVect0LinkObjId="g_2696770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_296d4a0_0" Pin1InfoVect1LinkObjId="SW-108933_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4913,-282 4913,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29816c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3922,-611 3949,-611 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="g_28f2860@0" ObjectIDZND0="21130@x" ObjectIDZND1="21137@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="SW-108667_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28f2860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3922,-611 3949,-611 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2982130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3949,-620 3949,-611 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="21130@0" ObjectIDZND0="g_28f2860@0" ObjectIDZND1="21137@x" Pin0InfoVect0LinkObjId="g_28f2860_0" Pin0InfoVect1LinkObjId="SW-108667_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29816c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3949,-620 3949,-611 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2982380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3949,-611 3949,-602 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_28f2860@0" ObjectIDND1="21130@x" ObjectIDZND0="21137@0" Pin0InfoVect0LinkObjId="SW-108667_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28f2860_0" Pin1InfoVect1LinkObjId="g_29816c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3949,-611 3949,-602 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29825e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3949,-551 3949,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21132@1" ObjectIDZND0="21137@1" Pin0InfoVect0LinkObjId="SW-108667_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108620_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3949,-551 3949,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2982840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3905,-511 3949,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21136@1" ObjectIDZND0="21132@x" ObjectIDZND1="21135@x" Pin0InfoVect0LinkObjId="SW-108620_0" Pin0InfoVect1LinkObjId="SW-108665_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108666_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3905,-511 3949,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2983310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3949,-524 3949,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="21132@0" ObjectIDZND0="21136@x" ObjectIDZND1="21135@x" Pin0InfoVect0LinkObjId="SW-108666_0" Pin0InfoVect1LinkObjId="SW-108665_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3949,-524 3949,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2983570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3949,-511 3949,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="21136@x" ObjectIDND1="21132@x" ObjectIDZND0="21135@0" Pin0InfoVect0LinkObjId="SW-108665_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-108666_0" Pin1InfoVect1LinkObjId="SW-108620_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3949,-511 3949,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29837d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3949,-460 3949,-447 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21135@1" ObjectIDZND0="29431@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-108665_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3949,-460 3949,-447 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-107503" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3205.000000 -1046.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="20950" ObjectName="DYN-LF_QSH"/>
     <cge:Meas_Ref ObjectId="107503"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25e7340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3434.000000 929.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25e82c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3434.000000 914.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25e8860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3434.000000 898.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25e8db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3440.000000 882.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25e9030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3426.000000 866.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25e9360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4671.000000 934.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25e95d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4671.000000 919.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25e9810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4671.000000 903.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25e9a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4677.000000 887.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25e9c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4663.000000 871.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ea5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4225.000000 835.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25eb170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4214.000000 820.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25eba20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4239.000000 805.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ec3a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3269.000000 -103.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ec600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3258.000000 -118.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ec840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3283.000000 -133.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28f09f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3992.000000 648.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28f1d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3992.000000 663.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28f8e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4003.000000 785.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28f9450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3992.000000 770.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28f9690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4017.000000 755.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28f99c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4009.000000 563.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28f9c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3998.000000 548.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28f9e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4023.000000 533.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28fb350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3861.000000 959.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28fb820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3850.000000 944.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28fba60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3875.000000 929.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28fbd90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4085.000000 1068.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28fbff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4074.000000 1053.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28fc230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4099.000000 1038.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2905060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4190.000000 605.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29052c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4179.000000 590.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2905500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4204.000000 575.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_293f1a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4744.000000 786.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_293f7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4733.000000 771.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_293fa30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4758.000000 756.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_293fd60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4723.000000 646.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_293ffc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4723.000000 661.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2941740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4783.000000 562.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2941d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4772.000000 547.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2941f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4797.000000 532.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2983fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3448.000000 29.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29845f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3437.000000 14.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2984830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3462.000000 -1.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2984b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3609.000000 29.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2984dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3598.000000 14.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2985000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3623.000000 -1.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2985330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3767.000000 28.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2985590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3756.000000 13.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29857d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3781.000000 -2.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2985b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4060.000000 28.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2985d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4049.000000 13.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2985fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4074.000000 -2.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29862d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4301.000000 30.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2986530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4290.000000 15.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2986770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4315.000000 0.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2986aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4488.000000 29.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2986d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4477.000000 14.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2986f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4502.000000 -1.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2987270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4680.000000 30.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29874d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4669.000000 15.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2987710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4694.000000 0.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2987a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4867.000000 30.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2987ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4856.000000 15.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2987ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4881.000000 0.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2988210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5048.000000 -67.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2988470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5037.000000 -82.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29886b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5062.000000 -97.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_298d200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3380.000000 515.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_298d7a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3386.000000 501.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_298d9e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3372.000000 486.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_298dc20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3372.000000 471.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_298de60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3380.000000 546.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_298e0a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3380.000000 531.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_298e3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4940.000000 520.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_298e650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4946.000000 506.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_298e890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4932.000000 491.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_298ead0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4932.000000 476.000000) translate(0,12)">Uca(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_298ed10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4940.000000 551.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_298ef50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4940.000000 536.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_SZ" endPointId="0" endStationName="LF_QSH" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_SheQing" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3803,-1169 3803,-1112 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18139" ObjectName="AC-35kV.LN_SheQing"/>
    <cge:TPSR_Ref TObjectID="18139_SS-159"/></metadata>
   <polyline fill="none" opacity="0" points="3803,-1169 3803,-1112 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="29430" cx="4683" cy="-845" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29429" cx="4212" cy="-845" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29430" cx="4318" cy="-845" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29432" cx="4512" cy="-458" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29431" cx="3821" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29431" cx="3661" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29431" cx="3503" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29431" cx="4115" cy="-446" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29430" cx="4418" cy="-845" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29430" cx="4499" cy="-845" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29429" cx="4054" cy="-845" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29429" cx="3950" cy="-845" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29431" cx="3676" cy="-447" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29431" cx="4219" cy="-447" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29432" cx="4323" cy="-458" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29431" cx="3347" cy="-447" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29432" cx="4682" cy="-458" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29432" cx="4350" cy="-458" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29432" cx="4728" cy="-458" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29432" cx="4913" cy="-458" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29432" cx="5102" cy="-458" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29432" cx="4539" cy="-458" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29431" cx="3949" cy="-447" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-108803">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4045.000000 -925.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21151" ObjectName="SW-LF_QSH.LF_QSH_362BK"/>
     <cge:Meas_Ref ObjectId="108803"/>
    <cge:TPSR_Ref TObjectID="21151"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108588">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3941.464567 -725.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21131" ObjectName="SW-LF_QSH.LF_QSH_301BK"/>
     <cge:Meas_Ref ObjectId="108588"/>
    <cge:TPSR_Ref TObjectID="21131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108620">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3940.464567 -516.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21132" ObjectName="SW-LF_QSH.LF_QSH_001BK"/>
     <cge:Meas_Ref ObjectId="108620"/>
    <cge:TPSR_Ref TObjectID="21132"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108691">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4674.000000 -715.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21139" ObjectName="SW-LF_QSH.LF_QSH_302BK"/>
     <cge:Meas_Ref ObjectId="108691"/>
    <cge:TPSR_Ref TObjectID="21139"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108723">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4673.000000 -516.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21140" ObjectName="SW-LF_QSH.LF_QSH_002BK"/>
     <cge:Meas_Ref ObjectId="108723"/>
    <cge:TPSR_Ref TObjectID="21140"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108834">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4244.000000 -904.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21157" ObjectName="SW-LF_QSH.LF_QSH_312BK"/>
     <cge:Meas_Ref ObjectId="108834"/>
    <cge:TPSR_Ref TObjectID="21157"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108772">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3794.000000 -928.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21146" ObjectName="SW-LF_QSH.LF_QSH_361BK"/>
     <cge:Meas_Ref ObjectId="108772"/>
    <cge:TPSR_Ref TObjectID="21146"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109027">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3810.607874 -328.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21208" ObjectName="SW-LF_QSH.LF_QSH_067BK"/>
     <cge:Meas_Ref ObjectId="109027"/>
    <cge:TPSR_Ref TObjectID="21208"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109002">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3652.176378 -328.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21202" ObjectName="SW-LF_QSH.LF_QSH_065BK"/>
     <cge:Meas_Ref ObjectId="109002"/>
    <cge:TPSR_Ref TObjectID="21202"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108977">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3493.470866 -328.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21196" ObjectName="SW-LF_QSH.LF_QSH_063BK"/>
     <cge:Meas_Ref ObjectId="108977"/>
    <cge:TPSR_Ref TObjectID="21196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109052">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3338.000000 -330.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21214" ObjectName="SW-LF_QSH.LF_QSH_061BK"/>
     <cge:Meas_Ref ObjectId="109052"/>
    <cge:TPSR_Ref TObjectID="21214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108878">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4719.000000 -340.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21173" ObjectName="SW-LF_QSH.LF_QSH_066BK"/>
     <cge:Meas_Ref ObjectId="108878"/>
    <cge:TPSR_Ref TObjectID="21173"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108952">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4341.000000 -341.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21190" ObjectName="SW-LF_QSH.LF_QSH_062BK"/>
     <cge:Meas_Ref ObjectId="108952"/>
    <cge:TPSR_Ref TObjectID="21190"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108903">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4530.000000 -342.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21179" ObjectName="SW-LF_QSH.LF_QSH_064BK"/>
     <cge:Meas_Ref ObjectId="108903"/>
    <cge:TPSR_Ref TObjectID="21179"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-108927">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4904.000000 -341.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21184" ObjectName="SW-LF_QSH.LF_QSH_072BK"/>
     <cge:Meas_Ref ObjectId="108927"/>
    <cge:TPSR_Ref TObjectID="21184"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109074">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5093.000000 -346.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21221" ObjectName="SW-LF_QSH.LF_QSH_068BK"/>
     <cge:Meas_Ref ObjectId="109074"/>
    <cge:TPSR_Ref TObjectID="21221"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-209598">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4236.000000 -513.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21228" ObjectName="SW-LF_QSH.LF_QSH_012BK"/>
     <cge:Meas_Ref ObjectId="209598"/>
    <cge:TPSR_Ref TObjectID="21228"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185504">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4106.000000 -329.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30384" ObjectName="SW-LF_QSH.LF_QSH_069BK"/>
     <cge:Meas_Ref ObjectId="185504"/>
    <cge:TPSR_Ref TObjectID="30384"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_267c660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4881.000000 -67.000000) translate(0,15)">大窝线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_267e1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5061.000000 38.000000) translate(0,15)">2号电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2682c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3292.000000 68.000000) translate(0,15)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26b1690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3462.000000 -57.000000) translate(0,15)">冶炼厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26b2330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3592.000000 -57.000000) translate(0,15)">张武庄煤矿Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26b3b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3764.000000 -57.000000) translate(0,15)">干海资集镇线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26b4d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3919.000000 -114.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26b57d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3295.000000 -477.000000) translate(0,15)">10kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2681550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5100.000000 -477.000000) translate(0,15)">10kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26b7590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4410.000000 -655.000000) translate(0,15)">10kVII段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26b7dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3609.000000 -651.000000) translate(0,15)">10kVI段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26b80a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4327.000000 -729.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26b82d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4410.000000 -1094.000000) translate(0,15)">35kVⅡ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26b66a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3553.000000 -1106.000000) translate(0,15)">35kVI段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26b6920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3773.000000 -1178.000000) translate(0,15)">舍清线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26b7150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4251.000000 -904.000000) translate(0,15)">分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26d3750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4014.000000 -1179.000000) translate(0,15)">煤矿I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26d39d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4241.000000 -513.000000) translate(0,15)">分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d3c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4060.000000 -890.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d4910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4060.000000 -1014.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d4d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3991.000000 -1065.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d50b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3809.000000 -893.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d5510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3809.000000 -1017.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d5750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3736.000000 -1072.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d5990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3630.000000 -936.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d61d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3641.000000 -900.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d6450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4507.000000 -916.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d6690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4517.000000 -893.000000) translate(0,12)">39020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d68d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4254.000000 -938.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d6b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4166.000000 -889.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d6d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4325.000000 -890.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26d6f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3514.000000 -832.000000) translate(0,15)">35kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d71d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3959.464567 -754.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d7410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3957.464567 -819.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d7650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4692.000000 -744.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d7890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4690.000000 -809.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d7ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4176.480315 -488.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d7d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4333.480315 -490.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d7f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3682.818898 -507.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d8190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3618.818898 -560.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d83d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4519.000000 -510.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d8610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4454.000000 -563.000000) translate(0,12)">09027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d8850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3356.000000 -357.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d8a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3364.943307 -421.000000) translate(0,12)">0611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d8cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3278.943307 -374.000000) translate(0,12)">06117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d8f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3354.000000 -170.000000) translate(0,12)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d9150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3291.000000 -225.000000) translate(0,12)">06160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d9390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3282.000000 -152.000000) translate(0,12)">06167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d95d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3512.470866 -357.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d9810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3510.470866 -416.000000) translate(0,12)">0631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d9a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3510.470866 -191.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d9c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3519.470866 -140.000000) translate(0,12)">06367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d9ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3442.470866 -373.000000) translate(0,12)">06317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26da110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3670.176378 -357.000000) translate(0,12)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26da450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3668.176378 -416.000000) translate(0,12)">0651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26da8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3668.176378 -191.000000) translate(0,12)">0656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26daaf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3680.176378 -140.000000) translate(0,12)">06567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26dad30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3607.176378 -372.000000) translate(0,12)">06517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26daf70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3828.607874 -357.000000) translate(0,12)">067</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26db1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3826.607874 -416.000000) translate(0,12)">0671</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26db3f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3826.607874 -191.000000) translate(0,12)">0676</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26db630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3840.607874 -174.000000) translate(0,12)">06767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26db870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3760.607874 -369.000000) translate(0,12)">06717</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26dbab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4922.000000 -370.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26dbcf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4920.000000 -429.000000) translate(0,12)">0721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26dbf30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4852.000000 -383.000000) translate(0,12)">07217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26dc170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4920.000000 -204.000000) translate(0,12)">0726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26dc3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4931.000000 -156.000000) translate(0,12)">07267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26dc5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5111.000000 -375.000000) translate(0,12)">068</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26dc930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5109.000000 -434.000000) translate(0,12)">0681</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26dcd90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5045.000000 -388.000000) translate(0,12)">06817</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26dcfd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5109.000000 -188.000000) translate(0,12)">0686</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26dd210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5045.000000 -243.000000) translate(0,12)">06860</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26dd450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5050.000000 -146.000000) translate(0,12)">06867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26dd690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4737.000000 -369.000000) translate(0,12)">066</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26dd8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4735.000000 -428.000000) translate(0,12)">0661</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26ddb10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4666.000000 -380.000000) translate(0,12)">06617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26ddd50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4735.000000 -203.000000) translate(0,12)">0666</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26ddf90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4744.000000 -149.000000) translate(0,12)">06667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26de1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4557.000000 -379.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26de510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4546.000000 -430.000000) translate(0,12)">0641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26de970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4548.000000 -213.000000) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26debb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4560.000000 -157.000000) translate(0,12)">06467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26dedf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4360.000000 -378.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26df030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4357.000000 -429.000000) translate(0,12)">0621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26df270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4294.000000 -388.000000) translate(0,12)">06217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26df4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4360.000000 -153.000000) translate(0,12)">06267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26df6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4357.000000 -204.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_269cc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2914.000000 -1013.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_269cc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2914.000000 -1013.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_269cc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2914.000000 -1013.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_269cc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2914.000000 -1013.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_269cc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2914.000000 -1013.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_269cc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2914.000000 -1013.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_269cc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2914.000000 -1013.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_269cc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2914.000000 -1013.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_269cc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2914.000000 -1013.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26a0640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2912.000000 -553.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26a0640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2912.000000 -553.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26a0640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2912.000000 -553.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26a0640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2912.000000 -553.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26a0640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2912.000000 -553.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26a0640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2912.000000 -553.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26a0640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2912.000000 -553.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26a0640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2912.000000 -553.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26a0640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2912.000000 -553.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26a0640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2912.000000 -553.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26a0640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2912.000000 -553.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26a0640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2912.000000 -553.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26a0640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2912.000000 -553.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26a0640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2912.000000 -553.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26a0640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2912.000000 -553.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26a0640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2912.000000 -553.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26a0640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2912.000000 -553.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26a0640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2912.000000 -553.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2627100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3051.500000 -1131.500000) translate(0,16)">清水河变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2628060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3834.000000 -1116.000000) translate(0,12)">3610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_261fc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3750.000000 -1000.000000) translate(0,12)">36160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_261fe60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3736.000000 -947.000000) translate(0,12)">36117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26200a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4098.000000 -1113.000000) translate(0,12)">3620</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26202e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4002.000000 -998.000000) translate(0,12)">36260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2620520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3994.000000 -942.000000) translate(0,12)">36217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2620760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3559.000000 -956.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26209a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4155.000000 -971.000000) translate(0,12)">31217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2620be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4325.000000 -955.000000) translate(0,12)">31227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2620e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4438.000000 -966.000000) translate(0,12)">39027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2621060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3894.464567 -802.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26212a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3961.464567 -553.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26214e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3963.464567 -476.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2621720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3868.464567 -538.000000) translate(0,12)">00117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2621960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4615.000000 -792.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2621ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4691.000000 -545.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2621de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4621.000000 -541.000000) translate(0,12)">00217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2622020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4690.000000 -487.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2622260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3982.000000 -249.000000) translate(0,12)">0670</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26224a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4692.000000 -82.000000) translate(0,15)">金泰焊材厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2688a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4072.000000 -731.000000) translate(0,12)">SFZ11-10000/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2688a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4072.000000 -731.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_268b6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4797.000000 -706.000000) translate(0,12)">SFZ11-10000/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_268b6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4797.000000 -706.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28296d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3513.000000 -338.000000) translate(0,12)">600/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2829910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3672.000000 -337.000000) translate(0,12)">300/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2829b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3829.000000 -332.000000) translate(0,12)">300/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2829d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4924.000000 -349.000000) translate(0,12)">300/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2829fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4735.000000 -342.000000) translate(0,12)">400/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_282a210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4554.000000 -362.000000) translate(0,12)">400/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_282a450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4362.000000 -360.000000) translate(0,12)">300/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25d2fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4046.000000 -58.000000) translate(0,15)">煤矿小区Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25d3bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4122.176378 -417.000000) translate(0,12)">0691</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25d3e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4122.176378 -192.000000) translate(0,12)">0696</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25d4090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4132.176378 -145.000000) translate(0,12)">06967</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25d42d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4061.176378 -379.000000) translate(0,12)">06917</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_25da3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2923.000000 -795.000000) translate(0,16)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_25dbcf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3184.000000 -1117.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_25dd880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3184.000000 -1152.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ddf60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4068.000000 -952.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25de280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3815.000000 -955.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25debd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4245.000000 -549.000000) translate(0,15)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="1" graphid="g_25df290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4124.000000 -358.000000) translate(0,1)">069</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25e1ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4127.000000 -360.000000) translate(0,15)">069</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25e6620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4765.000000 -841.000000) translate(0,15)">35kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_25eec30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2908.000000 -507.000000) translate(0,16)">10kV煤矿小区Ⅰ回线069断路器未做过</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_25eec30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2908.000000 -507.000000) translate(0,36)">遥控试验，不可遥控分合，待现场有</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_25eec30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2908.000000 -507.000000) translate(0,56)">相关工作再进行验证。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25f7b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4449.000000 -813.000000) translate(0,12)">3903</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28eff70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3994.000000 -695.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28f7a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3956.000000 -591.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2904700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4164.000000 -549.000000) translate(0,12)">01217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2904d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4337.000000 -553.000000) translate(0,12)">01227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_290f780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3354.000000 -300.000000) translate(0,12)">0612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29264c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3512.000000 -301.000000) translate(0,12)">0632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2926af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3668.000000 -299.000000) translate(0,12)">0652</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2926d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3831.000000 -300.000000) translate(0,12)">0672</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_293aa20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4122.000000 -300.000000) translate(0,12)">0692</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29421d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4721.000000 -692.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29477c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4689.000000 -589.000000) translate(0,12)">0026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29512d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4357.000000 -313.000000) translate(0,12)">0622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29564a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4481.000000 -384.000000) translate(0,12)">06417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_295b510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4553.000000 -316.000000) translate(0,12)">0642</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2964080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4735.000000 -312.000000) translate(0,12)">0662</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_296ce70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4920.000000 -314.000000) translate(0,12)">0722</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29749d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5109.000000 -319.000000) translate(0,12)">0682</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_29888f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2898.000000 -161.000000) translate(0,17)">禄丰巡维中心</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_29888f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2898.000000 -161.000000) translate(0,38)">禄丰变值班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_298a9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3049.000000 -150.500000) translate(0,17)">13908784381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_298c410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3049.000000 -195.000000) translate(0,17)">15758580336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29911d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4288.000000 -73.000000) translate(0,15)">工业园区Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_2992230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4488.000000 -76.000000) translate(0,14)">工业园区Ⅰ回线</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_271f970" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3865.464567 -770.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2751910" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4598.000000 -760.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_277f200" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4564.000000 -864.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26eda40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4413.000000 -933.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_274e030" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4206.000000 -976.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_274e960" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4312.000000 -976.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_274fc60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3971.000000 -912.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27dbc30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3972.000000 -967.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_276c030" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3971.000000 -1036.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_270fe80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3720.000000 -915.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26cf3e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3721.000000 -970.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2705420" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3720.000000 -1040.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_272e790" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3688.000000 -870.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2730250" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3537.000000 -954.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26c7600" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3885.607874 -144.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_266ace0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3589.818898 -528.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2765e60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3727.176378 -144.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2753830" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3569.470866 -144.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_273fc40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3252.000000 -193.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2743320" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3252.000000 -116.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2749180" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4794.000000 -156.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26e2210" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4416.000000 -157.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26fdbf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4605.000000 -158.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26cdaf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4979.000000 -157.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2667a60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5017.000000 -211.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27bb450" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5016.000000 -145.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26a4f10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4426.000000 -531.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2688150" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3338.000000 65.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25d20e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4181.176378 -145.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28f8250" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3826.000000 -505.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28ff520" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4124.000000 -517.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2903a10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4387.000000 -517.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2908f50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3243.000000 -374.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2917dc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3414.000000 -373.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_292aef0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3572.000000 -373.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_292c530" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3731.000000 -372.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29350e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4026.000000 -380.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29482e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4594.000000 -509.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_294b7f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4255.000000 -387.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29557b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4448.000000 -386.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_295e800" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4638.000000 -381.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2967850" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4823.000000 -387.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_296f150" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5012.000000 -391.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2976200" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5093.000000 36.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="LF_QSH"/>
</svg>