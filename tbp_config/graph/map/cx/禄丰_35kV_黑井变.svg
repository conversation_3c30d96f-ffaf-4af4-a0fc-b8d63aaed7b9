<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-94" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="2963 -1294 2868 1350">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="16" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.7" x1="19" x2="10" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="25" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="16" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="20" x2="11" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="11" x2="2" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="11" x2="11" y1="9" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="66"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="16" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="17" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="25" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="16" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="11" x2="11" y1="9" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="11" x2="2" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="20" x2="11" y1="17" y2="8"/>
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="40"/>
   </symbol>
   <symbol id="capacitor:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="37" x2="37" y1="122" y2="130"/>
    <polyline arcFlag="1" points="37,122 35,122 33,121 32,121 30,120 29,119 27,118 26,116 25,114 25,113 24,111 24,109 24,107 25,105 25,104 26,102 27,101 29,99 30,98 32,97 33,97 35,96 37,96 39,96 41,97 42,97 44,98 45,99 47,101 48,102 49,104 49,105 50,107 50,109 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="50" x2="38" y1="109" y2="109"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.915724" x1="37" x2="37" y1="109" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="23" y2="14"/>
    <polyline arcFlag="1" points="13,23 12,23 12,23 11,23 10,23 10,24 9,24 8,25 8,25 8,26 7,27 7,27 7,28 7,29 7,30 7,30 8,31 8,32 8,32 9,33 10,33 10,34 11,34 12,34 12,34 13,34 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,34 12,34 12,34 11,34 10,34 10,35 9,35 8,36 8,36 8,37 7,38 7,38 7,39 7,40 7,41 7,41 8,42 8,43 8,43 9,44 10,44 10,45 11,45 12,45 12,45 13,45 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,45 12,45 12,45 11,45 10,45 10,46 9,46 8,47 8,47 8,48 7,49 7,49 7,50 7,51 7,52 7,52 8,53 8,54 8,54 9,55 10,55 10,56 11,56 12,56 12,56 13,56 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="65" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="65" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.393258" x1="21" x2="56" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="13" y2="13"/>
    <rect height="26" stroke-width="0.398039" width="12" x="31" y="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="20" x2="20" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="2" x2="2" y1="56" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="57" x2="57" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.504561" x1="37" x2="37" y1="16" y2="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="59" x2="24" y1="7" y2="7"/>
    <rect height="12" stroke-width="1" width="26" x="18" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="5" x2="5" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="17" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="8" x2="8" y1="12" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape185">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="48" x2="45" y1="55" y2="53"/>
    <ellipse cx="34" cy="41" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="15" x2="15" y1="9" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="21" y1="9" y2="9"/>
    <g class="BV-0KV" stationId="-1">
     <rect height="13" stroke-width="1" width="6" x="12" y="18"/>
    </g>
    <polyline points="27,29 11,21 3,21 1,21 " stroke-width="1"/>
    <polyline points="38,47 37,49 15,49 15,31 " stroke-width="1"/>
    <ellipse cx="45" cy="41" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="45" x2="43" y1="44" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="46" x2="48" y1="44" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="48" x2="43" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="34" x2="34" y1="41" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="31" x2="34" y1="43" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="37" x2="34" y1="43" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="18" x2="12" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="16" x2="14" y1="2" y2="2"/>
    <ellipse cx="34" cy="52" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="45" cy="52" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="32" x2="34" y1="55" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="35" x2="35" y1="53" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="37" x2="34" y1="55" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="46" x2="46" y1="53" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="43" x2="45" y1="55" y2="53"/>
   </symbol>
   <symbol id="lightningRod:shape186">
    <circle cx="15" cy="80" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="41" x2="39" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="43" x2="37" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="34" x2="46" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="59" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="19" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="57" y2="53"/>
    <circle cx="15" cy="58" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="43" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="15,16 21,28 9,28 15,16 15,16 15,16 "/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,59 40,59 40,30 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="53" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="10" y1="87" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="18" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="14" y1="81" y2="87"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="64" x2="64" y1="6" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape13_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape20_0">
    <circle cx="20" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="13" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="11" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="19" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape20_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="41,15 41,40 70,40 " stroke-width="1"/>
    <circle cx="42" cy="16" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <ellipse cx="35" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="88" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="88" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="85" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="57" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="42" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="34" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <circle cx="35" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="33" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="46" x2="30" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="46" x2="30" y1="24" y2="33"/>
   </symbol>
   <symbol id="voltageTransformer:shape64">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="29" y1="17" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="6" y1="69" y2="69"/>
    <ellipse cx="8" cy="59" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="67" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="38" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="8" y1="16" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="17" y1="24" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="37" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="10" y1="17" y2="30"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1afc150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ae14e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a20bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1afe060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1413c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d64b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_13f6ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_10ea2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1bcec60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1bcec60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c85a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c85a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cc7430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cc7430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1c25720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cc59c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_13d10a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_17d5610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c2b810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18e9860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_10f4880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d75e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c459a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_177bb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19596d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_13d6e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19e36f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1c1c220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_178b640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1b4b060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1af0400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c07230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_17e87b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1b55b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1be8a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1360" width="2878" x="2958" y="-1299"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.444444" x1="4007" x2="4007" y1="-356" y2="-364"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.444444" x1="4374" x2="4374" y1="-346" y2="-354"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.444444" x1="4583" x2="4583" y1="-344" y2="-352"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.444444" x1="4776" x2="4776" y1="-342" y2="-350"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.444444" x1="5547" x2="5547" y1="-342" y2="-350"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-81230">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4799.887352 -1006.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17936" ObjectName="SW-LF_HJ.LF_HJ_361BK"/>
     <cge:Meas_Ref ObjectId="81230"/>
    <cge:TPSR_Ref TObjectID="17936"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3788.000000 -440.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81527">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4365.000000 -819.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17930" ObjectName="SW-LF_HJ.LF_HJ_301BK"/>
     <cge:Meas_Ref ObjectId="81527"/>
    <cge:TPSR_Ref TObjectID="17930"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84138">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5345.000000 -815.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18561" ObjectName="SW-LF_HJ.LF_HJ_302BK"/>
     <cge:Meas_Ref ObjectId="84138"/>
    <cge:TPSR_Ref TObjectID="18561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81267">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3998.000000 -472.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17943" ObjectName="SW-LF_HJ.LF_HJ_062BK"/>
     <cge:Meas_Ref ObjectId="81267"/>
    <cge:TPSR_Ref TObjectID="17943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81293">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4171.000000 -475.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17948" ObjectName="SW-LF_HJ.LF_HJ_063BK"/>
     <cge:Meas_Ref ObjectId="81293"/>
    <cge:TPSR_Ref TObjectID="17948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81320">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4364.000000 -477.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17952" ObjectName="SW-LF_HJ.LF_HJ_064BK"/>
     <cge:Meas_Ref ObjectId="81320"/>
    <cge:TPSR_Ref TObjectID="17952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81347">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4574.000000 -474.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17956" ObjectName="SW-LF_HJ.LF_HJ_065BK"/>
     <cge:Meas_Ref ObjectId="81347"/>
    <cge:TPSR_Ref TObjectID="17956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81374">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4767.000000 -474.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17960" ObjectName="SW-LF_HJ.LF_HJ_066BK"/>
     <cge:Meas_Ref ObjectId="81374"/>
    <cge:TPSR_Ref TObjectID="17960"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84228">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4920.000000 -470.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18569" ObjectName="SW-LF_HJ.LF_HJ_012BK"/>
     <cge:Meas_Ref ObjectId="84228"/>
    <cge:TPSR_Ref TObjectID="18569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84284">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5538.000000 -472.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18571" ObjectName="SW-LF_HJ.LF_HJ_071BK"/>
     <cge:Meas_Ref ObjectId="84284"/>
    <cge:TPSR_Ref TObjectID="18571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84163">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5345.000000 -582.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18565" ObjectName="SW-LF_HJ.LF_HJ_002BK"/>
     <cge:Meas_Ref ObjectId="84163"/>
    <cge:TPSR_Ref TObjectID="18565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81191">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4364.000000 -588.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17935" ObjectName="SW-LF_HJ.LF_HJ_001BK"/>
     <cge:Meas_Ref ObjectId="81191"/>
    <cge:TPSR_Ref TObjectID="17935"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_24cd900">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4828.000000 -1132.000000)" xlink:href="#voltageTransformer:shape64"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="LF_HJ" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_Dianheia_hj" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4809,-1265 4809,-1232 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48673" ObjectName="AC-35kV.LN_Dianheia_hj"/>
    <cge:TPSR_Ref TObjectID="48673_SS-94"/></metadata>
   <polyline fill="none" opacity="0" points="4809,-1265 4809,-1232 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-LF_HJ.HJ_063Ld">
    <use class="BKBV-10KV" transform="matrix(0.700000 -0.000000 0.000000 -2.115385 4177.092050 -143.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34010" ObjectName="EC-LF_HJ.HJ_063Ld"/>
    <cge:TPSR_Ref TObjectID="34010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_HJ.HJ_064Ld">
    <use class="BKBV-10KV" transform="matrix(0.700000 -0.000000 0.000000 -2.115385 4371.092050 -145.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34011" ObjectName="EC-LF_HJ.HJ_064Ld"/>
    <cge:TPSR_Ref TObjectID="34011"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_HJ.HJ_065Ld">
    <use class="BKBV-10KV" transform="matrix(0.700000 -0.000000 0.000000 -2.115385 4580.092050 -143.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34012" ObjectName="EC-LF_HJ.HJ_065Ld"/>
    <cge:TPSR_Ref TObjectID="34012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_HJ.HJ_066Ld">
    <use class="BKBV-10KV" transform="matrix(0.700000 -0.000000 0.000000 -2.115385 4773.092050 -141.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34013" ObjectName="EC-LF_HJ.HJ_066Ld"/>
    <cge:TPSR_Ref TObjectID="34013"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_HJ.HJ_071Ld">
    <use class="BKBV-10KV" transform="matrix(0.700000 -0.000000 0.000000 -2.115385 5544.092050 -141.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34014" ObjectName="EC-LF_HJ.HJ_071Ld"/>
    <cge:TPSR_Ref TObjectID="34014"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_21657b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5089.582170 -943.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ef6d20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4001.000000 -82.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a4f2b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3970.000000 -352.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e3afc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4145.000000 -363.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d36f40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4339.000000 -365.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_208e240" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4549.000000 -354.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d13bf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4554.000000 -222.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e596d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4346.000000 -224.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a50b90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.000000 -223.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d26990" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3960.000000 -225.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d133d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4741.000000 -353.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2479770" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4747.000000 -224.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c87170" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4157.000000 -773.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d17aa0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4087.000000 -847.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e3a090" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4847.000000 -1093.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f12dc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4776.000000 -944.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f05520" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4776.000000 -1049.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_249bfe0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5512.000000 -353.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1099aa0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5518.000000 -224.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24ca220" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5324.000000 -906.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24cac60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4344.000000 -911.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_204b080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4809,-934 4809,-953 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17928@0" ObjectIDZND0="17937@0" Pin0InfoVect0LinkObjId="SW-81232_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24993c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4809,-934 4809,-953 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fecb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5096,-973 5096,-952 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="17932@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81417_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5096,-973 5096,-952 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e87740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5127,-1058 5127,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1d0a5e0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d0a5e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5127,-1058 5127,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1feb010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4374,-815 4374,-827 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="17971@0" ObjectIDZND0="17930@0" Pin0InfoVect0LinkObjId="SW-81527_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4374,-815 4374,-827 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13fcbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4781,-1115 4809,-1115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="17941@1" ObjectIDZND0="17939@x" ObjectIDZND1="g_1e6eb80@0" ObjectIDZND2="g_24cd900@0" Pin0InfoVect0LinkObjId="SW-81236_0" Pin0InfoVect1LinkObjId="g_1e6eb80_0" Pin0InfoVect2LinkObjId="g_24cd900_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81239_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4781,-1115 4809,-1115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fe0730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4809,-1095 4809,-1116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="17939@1" ObjectIDZND0="17941@x" ObjectIDZND1="g_1e6eb80@0" ObjectIDZND2="g_24cd900@0" Pin0InfoVect0LinkObjId="SW-81239_0" Pin0InfoVect1LinkObjId="g_1e6eb80_0" Pin0InfoVect2LinkObjId="g_24cd900_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81236_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4809,-1095 4809,-1116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d0aef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4809,-1116 4809,-1138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="powerLine" ObjectIDND0="17941@x" ObjectIDND1="17939@x" ObjectIDZND0="g_1e6eb80@0" ObjectIDZND1="g_24cd900@0" ObjectIDZND2="48673@1" Pin0InfoVect0LinkObjId="g_1e6eb80_0" Pin0InfoVect1LinkObjId="g_24cd900_0" Pin0InfoVect2LinkObjId="g_1e39cb0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-81239_0" Pin1InfoVect1LinkObjId="SW-81236_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4809,-1116 4809,-1138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1413ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4809,-1052 4853,-1052 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="17936@x" ObjectIDND1="17939@x" ObjectIDZND0="17940@0" Pin0InfoVect0LinkObjId="SW-81238_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-81230_0" Pin1InfoVect1LinkObjId="SW-81236_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4809,-1052 4853,-1052 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e6c720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4809,-1041 4809,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="17936@1" ObjectIDZND0="17940@x" ObjectIDZND1="17939@x" Pin0InfoVect0LinkObjId="SW-81238_0" Pin0InfoVect1LinkObjId="SW-81236_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81230_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4809,-1041 4809,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e50a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4809,-1051 4809,-1059 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="17940@x" ObjectIDND1="17936@x" ObjectIDZND0="17939@0" Pin0InfoVect0LinkObjId="SW-81236_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-81238_0" Pin1InfoVect1LinkObjId="SW-81230_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4809,-1051 4809,-1059 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2029050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4782,-1007 4809,-1007 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="17938@1" ObjectIDZND0="17936@x" ObjectIDZND1="17937@x" Pin0InfoVect0LinkObjId="SW-81230_0" Pin0InfoVect1LinkObjId="SW-81232_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81234_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4782,-1007 4809,-1007 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_206aec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4793,-1137 4809,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_1e6eb80@0" ObjectIDZND0="17941@x" ObjectIDZND1="17939@x" ObjectIDZND2="g_24cd900@0" Pin0InfoVect0LinkObjId="SW-81239_0" Pin0InfoVect1LinkObjId="SW-81236_0" Pin0InfoVect2LinkObjId="g_24cd900_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e6eb80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4793,-1137 4809,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2072cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3637,-377 3637,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_247c520@1" ObjectIDZND0="g_24a6680@0" Pin0InfoVect0LinkObjId="g_24a6680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_247c520_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3637,-377 3637,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_206a6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3637,-553 3637,-532 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="18579@0" ObjectIDZND0="17968@0" Pin0InfoVect0LinkObjId="SW-81450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f0eb30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3637,-553 3637,-532 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2486fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3637,-448 3685,-448 3685,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_247c520@0" ObjectIDND1="17968@x" ObjectIDZND0="g_2014f60@0" Pin0InfoVect0LinkObjId="g_2014f60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_247c520_0" Pin1InfoVect1LinkObjId="SW-81450_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3637,-448 3685,-448 3685,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f0ed70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3637,-409 3637,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_247c520@0" ObjectIDZND0="g_2014f60@0" ObjectIDZND1="17968@x" Pin0InfoVect0LinkObjId="g_2014f60_0" Pin0InfoVect1LinkObjId="SW-81450_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_247c520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3637,-409 3637,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20950a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3637,-448 3637,-464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2014f60@0" ObjectIDND1="g_247c520@0" ObjectIDZND0="17968@1" Pin0InfoVect0LinkObjId="SW-81450_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2014f60_0" Pin1InfoVect1LinkObjId="g_247c520_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3637,-448 3637,-464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_208f080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-431 4180,-431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="17949@1" ObjectIDZND0="g_24bf700@0" ObjectIDZND1="g_2094da0@0" ObjectIDZND2="32050@x" Pin0InfoVect0LinkObjId="g_24bf700_0" Pin0InfoVect1LinkObjId="g_2094da0_0" Pin0InfoVect2LinkObjId="SW-81295_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81297_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-431 4180,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24993c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4374,-914 4374,-934 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="17931@1" ObjectIDZND0="17928@0" Pin0InfoVect0LinkObjId="g_1f09560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81167_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4374,-914 4374,-934 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d37fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3798,-373 3798,-350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_249dcf0@1" ObjectIDZND0="g_1cf8be0@1" Pin0InfoVect0LinkObjId="g_1cf8be0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_249dcf0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3798,-373 3798,-350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24abde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-347 4007,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="17945@1" ObjectIDZND0="g_2494ae0@1" Pin0InfoVect0LinkObjId="g_2494ae0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81272_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-347 4007,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24b69c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4180,-345 4180,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="17951@1" ObjectIDZND0="g_2094da0@1" Pin0InfoVect0LinkObjId="g_2094da0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81299_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4180,-345 4180,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a82be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4554,-431 4583,-431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="17957@1" ObjectIDZND0="g_1a85310@0" ObjectIDZND1="g_1d20e40@0" ObjectIDZND2="32054@x" Pin0InfoVect0LinkObjId="g_1a85310_0" Pin0InfoVect1LinkObjId="g_1d20e40_0" Pin0InfoVect2LinkObjId="SW-81349_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81351_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4554,-431 4583,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1136ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4747,-430 4776,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="17961@1" ObjectIDZND0="g_1e6d7d0@0" ObjectIDZND1="g_1d38a70@0" ObjectIDZND2="32055@x" Pin0InfoVect0LinkObjId="g_1e6d7d0_0" Pin0InfoVect1LinkObjId="g_1d38a70_0" Pin0InfoVect2LinkObjId="SW-81376_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81378_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4747,-430 4776,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e510c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4374,-360 4374,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1d253d0@1" ObjectIDZND0="17955@1" Pin0InfoVect0LinkObjId="SW-81326_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d253d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4374,-360 4374,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2049fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4583,-357 4583,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1d20e40@1" ObjectIDZND0="17959@1" Pin0InfoVect0LinkObjId="SW-81353_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d20e40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4583,-357 4583,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2493d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-355 4776,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1d38a70@1" ObjectIDZND0="17963@1" Pin0InfoVect0LinkObjId="SW-81380_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d38a70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-355 4776,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2488100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4374,-868 4350,-868 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="17931@x" ObjectIDND1="17930@x" ObjectIDZND0="17933@0" Pin0InfoVect0LinkObjId="SW-81169_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-81167_0" Pin1InfoVect1LinkObjId="SW-81527_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4374,-868 4350,-868 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f0eb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4373,-564 4373,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32046@0" ObjectIDZND0="18579@0" Pin0InfoVect0LinkObjId="g_1e415d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81193_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4373,-564 4373,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f03e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4373,-735 4373,-725 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="17971@1" ObjectIDZND0="g_24b1f40@0" Pin0InfoVect0LinkObjId="g_24b1f40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4373,-735 4373,-725 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1eff5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4374,-311 4374,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="17955@0" ObjectIDZND0="17954@x" ObjectIDZND1="34011@x" ObjectIDZND2="g_24b66c0@0" Pin0InfoVect0LinkObjId="SW-81325_0" Pin0InfoVect1LinkObjId="EC-LF_HJ.HJ_064Ld_0" Pin0InfoVect2LinkObjId="g_24b66c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81326_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4374,-311 4374,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_247be50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4351,-292 4374,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="17954@1" ObjectIDZND0="17955@x" ObjectIDZND1="34011@x" ObjectIDZND2="g_24b66c0@0" Pin0InfoVect0LinkObjId="SW-81326_0" Pin0InfoVect1LinkObjId="EC-LF_HJ.HJ_064Ld_0" Pin0InfoVect2LinkObjId="g_24b66c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81325_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4351,-292 4374,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ef8500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4374,-291 4374,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="17955@x" ObjectIDND1="17954@x" ObjectIDND2="g_24b66c0@0" ObjectIDZND0="34011@0" Pin0InfoVect0LinkObjId="EC-LF_HJ.HJ_064Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-81326_0" Pin1InfoVect1LinkObjId="SW-81325_0" Pin1InfoVect2LinkObjId="g_24b66c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4374,-291 4374,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2467ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4583,-309 4583,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="17959@0" ObjectIDZND0="g_24ae640@0" ObjectIDZND1="34012@x" ObjectIDZND2="17958@x" Pin0InfoVect0LinkObjId="g_24ae640_0" Pin0InfoVect1LinkObjId="EC-LF_HJ.HJ_065Ld_0" Pin0InfoVect2LinkObjId="SW-81352_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81353_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4583,-309 4583,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ce75c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4560,-291 4583,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="17958@1" ObjectIDZND0="g_24ae640@0" ObjectIDZND1="34012@x" ObjectIDZND2="17959@x" Pin0InfoVect0LinkObjId="g_24ae640_0" Pin0InfoVect1LinkObjId="EC-LF_HJ.HJ_065Ld_0" Pin0InfoVect2LinkObjId="SW-81353_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81352_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4560,-291 4583,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d19f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4583,-291 4600,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="34012@x" ObjectIDND1="17959@x" ObjectIDND2="17958@x" ObjectIDZND0="g_24ae640@0" Pin0InfoVect0LinkObjId="g_24ae640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-LF_HJ.HJ_065Ld_0" Pin1InfoVect1LinkObjId="SW-81353_0" Pin1InfoVect2LinkObjId="SW-81352_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4583,-291 4600,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24ab110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4583,-290 4583,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_24ae640@0" ObjectIDND1="17959@x" ObjectIDND2="17958@x" ObjectIDZND0="34012@0" Pin0InfoVect0LinkObjId="EC-LF_HJ.HJ_065Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_24ae640_0" Pin1InfoVect1LinkObjId="SW-81353_0" Pin1InfoVect2LinkObjId="SW-81352_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4583,-290 4583,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d21010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-307 4776,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="17963@0" ObjectIDZND0="g_1e3b6d0@0" ObjectIDZND1="34013@x" ObjectIDZND2="17962@x" Pin0InfoVect0LinkObjId="g_1e3b6d0_0" Pin0InfoVect1LinkObjId="EC-LF_HJ.HJ_066Ld_0" Pin0InfoVect2LinkObjId="SW-81379_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-307 4776,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e47e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4753,-291 4776,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="17962@1" ObjectIDZND0="g_1e3b6d0@0" ObjectIDZND1="34013@x" ObjectIDZND2="17963@x" Pin0InfoVect0LinkObjId="g_1e3b6d0_0" Pin0InfoVect1LinkObjId="EC-LF_HJ.HJ_066Ld_0" Pin0InfoVect2LinkObjId="SW-81380_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81379_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4753,-291 4776,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e45a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-291 4796,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="34013@x" ObjectIDND1="17963@x" ObjectIDND2="17962@x" ObjectIDZND0="g_1e3b6d0@0" Pin0InfoVect0LinkObjId="g_1e3b6d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-LF_HJ.HJ_066Ld_0" Pin1InfoVect1LinkObjId="SW-81380_0" Pin1InfoVect2LinkObjId="SW-81379_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-291 4796,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1febe10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-290 4776,-185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_1e3b6d0@0" ObjectIDND1="17963@x" ObjectIDND2="17962@x" ObjectIDZND0="34013@0" Pin0InfoVect0LinkObjId="EC-LF_HJ.HJ_066Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e3b6d0_0" Pin1InfoVect1LinkObjId="SW-81380_0" Pin1InfoVect2LinkObjId="SW-81379_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-290 4776,-185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2084910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4180,-309 4180,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="17951@0" ObjectIDZND0="g_1e44db0@0" ObjectIDZND1="34010@x" ObjectIDZND2="17950@x" Pin0InfoVect0LinkObjId="g_1e44db0_0" Pin0InfoVect1LinkObjId="EC-LF_HJ.HJ_063Ld_0" Pin0InfoVect2LinkObjId="SW-81298_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81299_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4180,-309 4180,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aa46a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4157,-291 4180,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="17950@1" ObjectIDZND0="g_1e44db0@0" ObjectIDZND1="34010@x" ObjectIDZND2="17951@x" Pin0InfoVect0LinkObjId="g_1e44db0_0" Pin0InfoVect1LinkObjId="EC-LF_HJ.HJ_063Ld_0" Pin0InfoVect2LinkObjId="SW-81299_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81298_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4157,-291 4180,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fc9730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4180,-291 4195,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="34010@x" ObjectIDND1="17951@x" ObjectIDND2="17950@x" ObjectIDZND0="g_1e44db0@0" Pin0InfoVect0LinkObjId="g_1e44db0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-LF_HJ.HJ_063Ld_0" Pin1InfoVect1LinkObjId="SW-81299_0" Pin1InfoVect2LinkObjId="SW-81298_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4180,-291 4195,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dff800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4180,-291 4180,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_1e44db0@0" ObjectIDND1="17951@x" ObjectIDND2="17950@x" ObjectIDZND0="34010@0" Pin0InfoVect0LinkObjId="EC-LF_HJ.HJ_063Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e44db0_0" Pin1InfoVect1LinkObjId="SW-81299_0" Pin1InfoVect2LinkObjId="SW-81298_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4180,-291 4180,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24e8200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4345,-433 4374,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="17953@1" ObjectIDZND0="g_201d2f0@0" ObjectIDZND1="g_1d253d0@0" ObjectIDZND2="32051@x" Pin0InfoVect0LinkObjId="g_201d2f0_0" Pin0InfoVect1LinkObjId="g_1d253d0_0" Pin0InfoVect2LinkObjId="SW-81322_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81324_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4345,-433 4374,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a100f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4373,-672 4373,-662 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_24b1f40@1" ObjectIDZND0="g_1d0d2f0@0" ObjectIDZND1="32041@x" Pin0InfoVect0LinkObjId="g_1d0d2f0_0" Pin0InfoVect1LinkObjId="SW-81193_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24b1f40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4373,-672 4373,-662 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e3e5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4384,-663 4373,-663 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1d0d2f0@0" ObjectIDZND0="g_24b1f40@0" ObjectIDZND1="32041@x" Pin0InfoVect0LinkObjId="g_24b1f40_0" Pin0InfoVect1LinkObjId="SW-81193_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d0d2f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4384,-663 4373,-663 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fc6f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4373,-662 4373,-655 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_24b1f40@0" ObjectIDND1="g_1d0d2f0@0" ObjectIDZND0="32041@0" Pin0InfoVect0LinkObjId="SW-81193_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24b1f40_0" Pin1InfoVect1LinkObjId="g_1d0d2f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4373,-662 4373,-655 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e415d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3798,-539 3798,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="18579@0" Pin0InfoVect0LinkObjId="g_1f0eb30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3798,-539 3798,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc8f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4076,-840 4116,-840 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1e6bc90@0" ObjectIDZND0="g_1a40d00@0" ObjectIDZND1="17964@x" ObjectIDZND2="17966@x" Pin0InfoVect0LinkObjId="g_1a40d00_0" Pin0InfoVect1LinkObjId="SW-81406_0" Pin0InfoVect2LinkObjId="SW-81410_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e6bc90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4076,-840 4116,-840 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20799f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-855 4116,-839 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="17964@0" ObjectIDZND0="g_1e6bc90@0" ObjectIDZND1="g_1a40d00@0" ObjectIDZND2="17966@x" Pin0InfoVect0LinkObjId="g_1e6bc90_0" Pin0InfoVect1LinkObjId="g_1a40d00_0" Pin0InfoVect2LinkObjId="SW-81410_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81406_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-855 4116,-839 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2479300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-840 4116,-831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1e6bc90@0" ObjectIDND1="17964@x" ObjectIDND2="17966@x" ObjectIDZND0="g_1a40d00@0" Pin0InfoVect0LinkObjId="g_1a40d00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e6bc90_0" Pin1InfoVect1LinkObjId="SW-81406_0" Pin1InfoVect2LinkObjId="SW-81410_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-840 4116,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1fc5210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5127,-934 5127,-959 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17928@0" ObjectIDZND0="17967@0" Pin0InfoVect0LinkObjId="SW-81415_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24993c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5127,-934 5127,-959 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1dcfb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5096,-1009 5127,-1009 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="17932@1" ObjectIDZND0="17967@x" ObjectIDZND1="g_1d0a5e0@0" Pin0InfoVect0LinkObjId="SW-81415_0" Pin0InfoVect1LinkObjId="g_1d0a5e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81417_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5096,-1009 5127,-1009 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c19b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5127,-995 5127,-1009 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="17967@1" ObjectIDZND0="17932@x" ObjectIDZND1="g_1d0a5e0@0" Pin0InfoVect0LinkObjId="SW-81417_0" Pin0InfoVect1LinkObjId="g_1d0a5e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81415_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5127,-995 5127,-1009 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_246afb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5127,-1009 5127,-1027 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="17932@x" ObjectIDND1="17967@x" ObjectIDZND0="g_1d0a5e0@0" Pin0InfoVect0LinkObjId="g_1d0a5e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-81417_0" Pin1InfoVect1LinkObjId="SW-81415_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5127,-1009 5127,-1027 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a73dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-799 4116,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1a40d00@1" ObjectIDZND0="g_1a9d7c0@0" Pin0InfoVect0LinkObjId="g_1a9d7c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a40d00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-799 4116,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d262e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-113 4007,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="17947@0" ObjectIDZND0="g_1ef6d20@0" Pin0InfoVect0LinkObjId="g_1ef6d20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81275_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-113 4007,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e39cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4809,-1137 4809,-1232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="17941@x" ObjectIDND1="17939@x" ObjectIDND2="g_1e6eb80@0" ObjectIDZND0="48673@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-81239_0" Pin1InfoVect1LinkObjId="SW-81236_0" Pin1InfoVect2LinkObjId="g_1e6eb80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4809,-1137 4809,-1232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e457f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4809,-1137 4836,-1137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="17941@x" ObjectIDND1="17939@x" ObjectIDND2="g_1e6eb80@0" ObjectIDZND0="g_24cd900@0" Pin0InfoVect0LinkObjId="g_24cd900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-81239_0" Pin1InfoVect1LinkObjId="SW-81236_0" Pin1InfoVect2LinkObjId="g_1e6eb80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4809,-1137 4836,-1137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d294c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-840 4163,-840 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1e6bc90@0" ObjectIDND1="g_1a40d00@0" ObjectIDND2="17964@x" ObjectIDZND0="17966@1" Pin0InfoVect0LinkObjId="SW-81410_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e6bc90_0" Pin1InfoVect1LinkObjId="g_1a40d00_0" Pin1InfoVect2LinkObjId="SW-81406_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-840 4163,-840 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2467e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3976,-383 3976,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="17944@0" ObjectIDZND0="g_1a4f2b0@0" Pin0InfoVect0LinkObjId="g_1a4f2b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81271_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3976,-383 3976,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20199d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-394 4151,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="17949@0" ObjectIDZND0="g_1e3afc0@0" Pin0InfoVect0LinkObjId="g_1e3afc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81297_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-394 4151,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f853b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4345,-396 4345,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="17953@0" ObjectIDZND0="g_1d36f40@0" Pin0InfoVect0LinkObjId="g_1d36f40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81324_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4345,-396 4345,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f090f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4555,-394 4555,-372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="17957@0" ObjectIDZND0="g_208e240@0" Pin0InfoVect0LinkObjId="g_208e240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81351_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4555,-394 4555,-372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e63a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4560,-253 4560,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="17958@0" ObjectIDZND0="g_1d13bf0@0" Pin0InfoVect0LinkObjId="g_1d13bf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81352_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4560,-253 4560,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d1f0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-255 4352,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="17954@0" ObjectIDZND0="g_1e596d0@0" Pin0InfoVect0LinkObjId="g_1e596d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81325_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-255 4352,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14145e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4158,-254 4158,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="17950@0" ObjectIDZND0="g_1a50b90@0" Pin0InfoVect0LinkObjId="g_1a50b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81298_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4158,-254 4158,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24a2c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3966,-256 3966,-243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="17946@0" ObjectIDZND0="g_1d26990@0" Pin0InfoVect0LinkObjId="g_1d26990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81274_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3966,-256 3966,-243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e79b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4747,-394 4747,-371 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="17961@0" ObjectIDZND0="g_1d133d0@0" Pin0InfoVect0LinkObjId="g_1d133d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81378_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4747,-394 4747,-371 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a49a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4753,-255 4753,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="17962@0" ObjectIDZND0="g_2479770@0" Pin0InfoVect0LinkObjId="g_2479770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81379_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4753,-255 4753,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d214c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4163,-804 4163,-791 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="17966@0" ObjectIDZND0="g_1c87170@0" Pin0InfoVect0LinkObjId="g_1c87170_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4163,-804 4163,-791 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f09560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-913 4116,-913 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="17965@1" ObjectIDZND0="17928@0" ObjectIDZND1="17964@x" Pin0InfoVect0LinkObjId="g_24993c0_0" Pin0InfoVect1LinkObjId="SW-81406_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81408_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-913 4116,-913 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20122e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-934 4116,-913 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="17928@0" ObjectIDZND0="17965@x" ObjectIDZND1="17964@x" Pin0InfoVect0LinkObjId="SW-81408_0" Pin0InfoVect1LinkObjId="SW-81406_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24993c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-934 4116,-913 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17b1340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-913 4116,-892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="17965@x" ObjectIDND1="17928@0" ObjectIDZND0="17964@1" Pin0InfoVect0LinkObjId="SW-81406_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-81408_0" Pin1InfoVect1LinkObjId="g_24993c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-913 4116,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24aecf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-876 4093,-865 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="17965@0" ObjectIDZND0="g_1d17aa0@0" Pin0InfoVect0LinkObjId="g_1d17aa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81408_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-876 4093,-865 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e41870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4853,-1098 4853,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1e3a090@0" ObjectIDZND0="17940@1" Pin0InfoVect0LinkObjId="SW-81238_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e3a090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4853,-1098 4853,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d2cdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4809,-1007 4809,-1014 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="17938@x" ObjectIDND1="17937@x" ObjectIDZND0="17936@0" Pin0InfoVect0LinkObjId="SW-81230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-81234_0" Pin1InfoVect1LinkObjId="SW-81232_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4809,-1007 4809,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2475a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4809,-988 4809,-1007 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="17937@1" ObjectIDZND0="17938@x" ObjectIDZND1="17936@x" Pin0InfoVect0LinkObjId="SW-81234_0" Pin0InfoVect1LinkObjId="SW-81230_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81232_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4809,-988 4809,-1007 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2475cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4782,-971 4782,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="17938@0" ObjectIDZND0="g_1f12dc0@0" Pin0InfoVect0LinkObjId="g_1f12dc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81234_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4782,-971 4782,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2494070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4782,-1078 4782,-1067 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="17941@0" ObjectIDZND0="g_1f05520@0" Pin0InfoVect0LinkObjId="g_1f05520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81239_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4782,-1078 4782,-1067 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17dc950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4374,-878 4374,-869 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="17931@0" ObjectIDZND0="17933@x" ObjectIDZND1="17930@x" Pin0InfoVect0LinkObjId="SW-81169_0" Pin0InfoVect1LinkObjId="SW-81527_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81167_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4374,-878 4374,-869 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d12ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4374,-854 4374,-869 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="17930@1" ObjectIDZND0="17933@x" ObjectIDZND1="17931@x" Pin0InfoVect0LinkObjId="SW-81169_0" Pin0InfoVect1LinkObjId="SW-81167_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81527_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4374,-854 4374,-869 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e5b9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3976,-419 3976,-432 4007,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="17944@1" ObjectIDZND0="g_2494ae0@0" ObjectIDZND1="g_1d130d0@0" ObjectIDZND2="32047@x" Pin0InfoVect0LinkObjId="g_2494ae0_0" Pin0InfoVect1LinkObjId="g_1d130d0_0" Pin0InfoVect2LinkObjId="SW-81269_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81271_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3976,-419 3976,-432 4007,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24a5290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-413 4007,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2494ae0@0" ObjectIDZND0="17944@x" ObjectIDZND1="g_1d130d0@0" ObjectIDZND2="32047@x" Pin0InfoVect0LinkObjId="SW-81271_0" Pin0InfoVect1LinkObjId="g_1d130d0_0" Pin0InfoVect2LinkObjId="SW-81269_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2494ae0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-413 4007,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24a54f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-432 4044,-432 4044,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="17944@x" ObjectIDND1="g_2494ae0@0" ObjectIDND2="32047@x" ObjectIDZND0="g_1d130d0@0" Pin0InfoVect0LinkObjId="g_1d130d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-81271_0" Pin1InfoVect1LinkObjId="g_2494ae0_0" Pin1InfoVect2LinkObjId="SW-81269_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-432 4044,-432 4044,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24bf4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4180,-431 4217,-431 4217,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="17949@x" ObjectIDND1="g_2094da0@0" ObjectIDND2="32050@x" ObjectIDZND0="g_24bf700@0" Pin0InfoVect0LinkObjId="g_24bf700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-81297_0" Pin1InfoVect1LinkObjId="g_2094da0_0" Pin1InfoVect2LinkObjId="SW-81295_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4180,-431 4217,-431 4217,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1743560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4374,-433 4411,-433 4411,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="17953@x" ObjectIDND1="g_1d253d0@0" ObjectIDND2="32051@x" ObjectIDZND0="g_201d2f0@0" Pin0InfoVect0LinkObjId="g_201d2f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-81324_0" Pin1InfoVect1LinkObjId="g_1d253d0_0" Pin1InfoVect2LinkObjId="SW-81322_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4374,-433 4411,-433 4411,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d21a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4374,-433 4374,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="17953@x" ObjectIDND1="g_201d2f0@0" ObjectIDND2="32051@x" ObjectIDZND0="g_1d253d0@0" Pin0InfoVect0LinkObjId="g_1d253d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-81324_0" Pin1InfoVect1LinkObjId="g_201d2f0_0" Pin1InfoVect2LinkObjId="SW-81322_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4374,-433 4374,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a850b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4583,-431 4620,-431 4620,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="17957@x" ObjectIDND1="g_1d20e40@0" ObjectIDND2="32054@x" ObjectIDZND0="g_1a85310@0" Pin0InfoVect0LinkObjId="g_1a85310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-81351_0" Pin1InfoVect1LinkObjId="g_1d20e40_0" Pin1InfoVect2LinkObjId="SW-81349_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4583,-431 4620,-431 4620,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2028c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4583,-431 4583,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="17957@x" ObjectIDND1="g_1a85310@0" ObjectIDND2="32054@x" ObjectIDZND0="g_1d20e40@0" Pin0InfoVect0LinkObjId="g_1d20e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-81351_0" Pin1InfoVect1LinkObjId="g_1a85310_0" Pin1InfoVect2LinkObjId="SW-81349_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4583,-431 4583,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e6d570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-430 4813,-430 4813,-418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="17961@x" ObjectIDND1="g_1d38a70@0" ObjectIDND2="32055@x" ObjectIDZND0="g_1e6d7d0@0" Pin0InfoVect0LinkObjId="g_1e6d7d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-81378_0" Pin1InfoVect1LinkObjId="g_1d38a70_0" Pin1InfoVect2LinkObjId="SW-81376_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-430 4813,-430 4813,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e391b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4180,-411 4180,-431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2094da0@0" ObjectIDZND0="g_24bf700@0" ObjectIDZND1="17949@x" ObjectIDZND2="32050@x" Pin0InfoVect0LinkObjId="g_24bf700_0" Pin0InfoVect1LinkObjId="SW-81297_0" Pin0InfoVect2LinkObjId="SW-81295_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2094da0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4180,-411 4180,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e39410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-408 4776,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1d38a70@0" ObjectIDZND0="g_1e6d7d0@0" ObjectIDZND1="17961@x" ObjectIDZND2="32055@x" Pin0InfoVect0LinkObjId="g_1e6d7d0_0" Pin0InfoVect1LinkObjId="SW-81378_0" Pin0InfoVect2LinkObjId="SW-81376_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d38a70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-408 4776,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cf7d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5354,-811 5354,-823 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="18590@0" ObjectIDZND0="18561@0" Pin0InfoVect0LinkObjId="SW-84138_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5354,-811 5354,-823 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2465fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5354,-910 5354,-934 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="18562@1" ObjectIDZND0="17928@0" Pin0InfoVect0LinkObjId="g_24993c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84139_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5354,-910 5354,-934 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d19a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5354,-864 5330,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="18562@x" ObjectIDND1="18561@x" ObjectIDZND0="18563@0" Pin0InfoVect0LinkObjId="SW-84141_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-84139_0" Pin1InfoVect1LinkObjId="SW-84138_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5354,-864 5330,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d3ac60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5353,-731 5353,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="18590@1" ObjectIDZND0="g_1d19c80@0" Pin0InfoVect0LinkObjId="g_1d19c80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5353,-731 5353,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d676d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5353,-668 5353,-658 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1d19c80@1" ObjectIDZND0="g_1d3aea0@0" ObjectIDZND1="32042@x" Pin0InfoVect0LinkObjId="g_1d3aea0_0" Pin0InfoVect1LinkObjId="SW-84165_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d19c80_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5353,-668 5353,-658 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aafcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5364,-659 5353,-659 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1d3aea0@0" ObjectIDZND0="g_1d19c80@0" ObjectIDZND1="32042@x" Pin0InfoVect0LinkObjId="g_1d19c80_0" Pin0InfoVect1LinkObjId="SW-84165_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d3aea0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5364,-659 5353,-659 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1aaff00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5353,-658 5353,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_1d3aea0@0" ObjectIDND1="g_1d19c80@0" ObjectIDZND0="32042@0" Pin0InfoVect0LinkObjId="SW-84165_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d3aea0_0" Pin1InfoVect1LinkObjId="g_1d19c80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5353,-658 5353,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18ecbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5354,-874 5354,-865 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="18562@0" ObjectIDZND0="18563@x" ObjectIDZND1="18561@x" Pin0InfoVect0LinkObjId="SW-84141_0" Pin0InfoVect1LinkObjId="SW-84138_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84139_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5354,-874 5354,-865 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_18ecdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5354,-850 5354,-865 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="18561@1" ObjectIDZND0="18562@x" ObjectIDZND1="18563@x" Pin0InfoVect0LinkObjId="SW-84139_0" Pin0InfoVect1LinkObjId="SW-84141_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84138_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5354,-850 5354,-865 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2482200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5518,-430 5547,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="18580@1" ObjectIDZND0="g_2011570@0" ObjectIDZND1="g_24a8570@0" ObjectIDZND2="32044@x" Pin0InfoVect0LinkObjId="g_2011570_0" Pin0InfoVect1LinkObjId="g_24a8570_0" Pin0InfoVect2LinkObjId="SW-84286_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84288_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5518,-430 5547,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2482430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5547,-355 5547,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_24a8570@1" ObjectIDZND0="18581@1" Pin0InfoVect0LinkObjId="SW-84289_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24a8570_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5547,-355 5547,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2480580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5547,-307 5547,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="18581@0" ObjectIDZND0="0@x" ObjectIDZND1="34014@x" ObjectIDZND2="g_2482690@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="EC-LF_HJ.HJ_071Ld_0" Pin0InfoVect2LinkObjId="g_2482690_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84289_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5547,-307 5547,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_201c650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5524,-291 5547,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="18581@x" ObjectIDZND1="34014@x" ObjectIDZND2="g_2482690@0" Pin0InfoVect0LinkObjId="SW-84289_0" Pin0InfoVect1LinkObjId="EC-LF_HJ.HJ_071Ld_0" Pin0InfoVect2LinkObjId="g_2482690_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5524,-291 5547,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_201c8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5547,-291 5567,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="18581@x" ObjectIDND2="34014@x" ObjectIDZND0="g_2482690@0" Pin0InfoVect0LinkObjId="g_2482690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-84289_0" Pin1InfoVect2LinkObjId="EC-LF_HJ.HJ_071Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5547,-291 5567,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_201cb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5547,-290 5547,-185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="0@x" ObjectIDND1="18581@x" ObjectIDND2="g_2482690@0" ObjectIDZND0="34014@0" Pin0InfoVect0LinkObjId="EC-LF_HJ.HJ_071Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-84289_0" Pin1InfoVect2LinkObjId="g_2482690_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5547,-290 5547,-185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1b4bc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5518,-394 5518,-371 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18580@0" ObjectIDZND0="g_249bfe0@0" Pin0InfoVect0LinkObjId="g_249bfe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84288_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5518,-394 5518,-371 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2013ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5524,-255 5524,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_1099aa0@0" Pin0InfoVect0LinkObjId="g_1099aa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5524,-255 5524,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2011310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5547,-430 5584,-430 5584,-418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="18580@x" ObjectIDND1="g_24a8570@0" ObjectIDND2="32044@x" ObjectIDZND0="g_2011570@0" Pin0InfoVect0LinkObjId="g_2011570_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-84288_0" Pin1InfoVect1LinkObjId="g_24a8570_0" Pin1InfoVect2LinkObjId="SW-84286_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5547,-430 5584,-430 5584,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a41f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5547,-408 5547,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_24a8570@0" ObjectIDZND0="g_2011570@0" ObjectIDZND1="18580@x" ObjectIDZND2="32044@x" Pin0InfoVect0LinkObjId="g_2011570_0" Pin0InfoVect1LinkObjId="SW-84288_0" Pin0InfoVect2LinkObjId="SW-84286_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24a8570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5547,-408 5547,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d1fb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5320,-371 5320,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1e4f5b0@1" ObjectIDZND0="g_1e67ff0@0" Pin0InfoVect0LinkObjId="g_1e67ff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e4f5b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5320,-371 5320,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f0e040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5320,-552 5320,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17929@0" ObjectIDZND0="18570@0" Pin0InfoVect0LinkObjId="SW-84259_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2454650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5320,-552 5320,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f0e2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5320,-442 5368,-442 5368,-428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1e4f5b0@0" ObjectIDND1="18570@x" ObjectIDZND0="g_1f0e500@0" Pin0InfoVect0LinkObjId="g_1f0e500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1e4f5b0_0" Pin1InfoVect1LinkObjId="SW-84259_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5320,-442 5368,-442 5368,-428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11346e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5320,-403 5320,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1e4f5b0@0" ObjectIDZND0="g_1f0e500@0" ObjectIDZND1="18570@x" Pin0InfoVect0LinkObjId="g_1f0e500_0" Pin0InfoVect1LinkObjId="SW-84259_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e4f5b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5320,-403 5320,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e678a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5320,-442 5320,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_1f0e500@0" ObjectIDND1="g_1e4f5b0@0" ObjectIDZND0="18570@1" Pin0InfoVect0LinkObjId="SW-84259_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f0e500_0" Pin1InfoVect1LinkObjId="g_1e4f5b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5320,-442 5320,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e2f0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4929,-411 4929,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="18582@x" ObjectIDND1="32057@x" ObjectIDZND0="g_1d0dad0@0" Pin0InfoVect0LinkObjId="g_1d0dad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-84232_0" Pin1InfoVect1LinkObjId="SW-84230_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4929,-411 4929,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_141aa00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4929,-411 5179,-411 5179,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1d0dad0@0" ObjectIDND1="32057@x" ObjectIDZND0="18582@1" Pin0InfoVect0LinkObjId="SW-84232_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d0dad0_0" Pin1InfoVect1LinkObjId="SW-84230_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4929,-411 5179,-411 5179,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2454650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5179,-531 5179,-552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="18582@0" ObjectIDZND0="17929@0" Pin0InfoVect0LinkObjId="g_28f02c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84232_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5179,-531 5179,-552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2483740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-507 4007,-521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17943@1" ObjectIDZND0="32040@1" Pin0InfoVect0LinkObjId="SW-81269_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81267_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-507 4007,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24839a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-538 4007,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32040@0" ObjectIDZND0="18579@0" Pin0InfoVect0LinkObjId="g_1f0eb30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81269_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-538 4007,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2483c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-432 4007,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="17944@x" ObjectIDND1="g_2494ae0@0" ObjectIDND2="g_1d130d0@0" ObjectIDZND0="32047@0" Pin0InfoVect0LinkObjId="SW-81269_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-81271_0" Pin1InfoVect1LinkObjId="g_2494ae0_0" Pin1InfoVect2LinkObjId="g_1d130d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-432 4007,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2483e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-459 4007,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32047@1" ObjectIDZND0="17943@0" Pin0InfoVect0LinkObjId="SW-81267_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81269_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-459 4007,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f06c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4180,-510 4180,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17948@1" ObjectIDZND0="32049@1" Pin0InfoVect0LinkObjId="SW-81295_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81293_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4180,-510 4180,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f06ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4180,-462 4180,-483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32050@1" ObjectIDZND0="17948@0" Pin0InfoVect0LinkObjId="SW-81293_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81295_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4180,-462 4180,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21197f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4373,-512 4373,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17952@1" ObjectIDZND0="32048@1" Pin0InfoVect0LinkObjId="SW-81322_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81320_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4373,-512 4373,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2119a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4373,-464 4373,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32051@1" ObjectIDZND0="17952@0" Pin0InfoVect0LinkObjId="SW-81320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81322_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4373,-464 4373,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2119cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4180,-431 4180,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_24bf700@0" ObjectIDND1="17949@x" ObjectIDND2="g_2094da0@0" ObjectIDZND0="32050@0" Pin0InfoVect0LinkObjId="SW-81295_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_24bf700_0" Pin1InfoVect1LinkObjId="SW-81297_0" Pin1InfoVect2LinkObjId="g_2094da0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4180,-431 4180,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2119f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4180,-541 4180,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32049@0" ObjectIDZND0="18579@0" Pin0InfoVect0LinkObjId="g_1f0eb30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81295_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4180,-541 4180,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_211a740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4373,-553 4373,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="18579@0" ObjectIDZND0="32048@0" Pin0InfoVect0LinkObjId="SW-81322_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f0eb30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4373,-553 4373,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ef8b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4373,-449 4373,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="32051@0" ObjectIDZND0="17953@x" ObjectIDZND1="g_201d2f0@0" ObjectIDZND2="g_1d253d0@0" Pin0InfoVect0LinkObjId="SW-81324_0" Pin0InfoVect1LinkObjId="g_201d2f0_0" Pin0InfoVect2LinkObjId="g_1d253d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81322_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4373,-449 4373,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1efe110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4583,-509 4583,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17956@1" ObjectIDZND0="32053@1" Pin0InfoVect0LinkObjId="SW-81349_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81347_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4583,-509 4583,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1efe370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4583,-461 4583,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32054@1" ObjectIDZND0="17956@0" Pin0InfoVect0LinkObjId="SW-81347_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81349_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4583,-461 4583,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1efe5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4583,-552 4583,-540 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="18579@0" ObjectIDZND0="32053@0" Pin0InfoVect0LinkObjId="SW-81349_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f0eb30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4583,-552 4583,-540 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1efe830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4583,-444 4583,-431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="32054@0" ObjectIDZND0="17957@x" ObjectIDZND1="g_1a85310@0" ObjectIDZND2="g_1d20e40@0" Pin0InfoVect0LinkObjId="SW-81351_0" Pin0InfoVect1LinkObjId="g_1a85310_0" Pin0InfoVect2LinkObjId="g_1d20e40_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81349_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4583,-444 4583,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20a4070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-509 4776,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17960@1" ObjectIDZND0="32052@1" Pin0InfoVect0LinkObjId="SW-81376_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81374_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-509 4776,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20a42d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-461 4776,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32055@1" ObjectIDZND0="17960@0" Pin0InfoVect0LinkObjId="SW-81374_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81376_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-461 4776,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20a4530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-552 4776,-540 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="18579@0" ObjectIDZND0="32052@0" Pin0InfoVect0LinkObjId="SW-81376_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f0eb30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-552 4776,-540 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20a4790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-445 4776,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="32055@0" ObjectIDZND0="g_1e6d7d0@0" ObjectIDZND1="17961@x" ObjectIDZND2="g_1d38a70@0" Pin0InfoVect0LinkObjId="g_1e6d7d0_0" Pin0InfoVect1LinkObjId="SW-81378_0" Pin0InfoVect2LinkObjId="g_1d38a70_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81376_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-445 4776,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e1b4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4929,-505 4929,-519 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="18569@1" ObjectIDZND0="32056@1" Pin0InfoVect0LinkObjId="SW-84230_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84228_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4929,-505 4929,-519 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e1b730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4929,-457 4929,-478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32057@1" ObjectIDZND0="18569@0" Pin0InfoVect0LinkObjId="SW-84228_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84230_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4929,-457 4929,-478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e1b990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4929,-411 4929,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1d0dad0@0" ObjectIDND1="18582@x" ObjectIDZND0="32057@0" Pin0InfoVect0LinkObjId="SW-84230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d0dad0_0" Pin1InfoVect1LinkObjId="SW-84232_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4929,-411 4929,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e1bbf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4929,-536 4929,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32056@0" ObjectIDZND0="18579@0" Pin0InfoVect0LinkObjId="g_1f0eb30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4929,-536 4929,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e23de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5547,-507 5547,-521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="18571@1" ObjectIDZND0="32043@1" Pin0InfoVect0LinkObjId="SW-84286_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84284_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5547,-507 5547,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e24040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5547,-459 5547,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32044@1" ObjectIDZND0="18571@0" Pin0InfoVect0LinkObjId="SW-84284_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84286_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5547,-459 5547,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e242a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5547,-553 5547,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17929@0" ObjectIDZND0="32043@0" Pin0InfoVect0LinkObjId="SW-84286_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2454650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5547,-553 5547,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e24500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5547,-443 5547,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="32044@0" ObjectIDZND0="g_2011570@0" ObjectIDZND1="18580@x" ObjectIDZND2="g_24a8570@0" Pin0InfoVect0LinkObjId="g_2011570_0" Pin0InfoVect1LinkObjId="SW-84288_0" Pin0InfoVect2LinkObjId="g_24a8570_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84286_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5547,-443 5547,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28efe00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5354,-617 5354,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="18565@1" ObjectIDZND0="32042@1" Pin0InfoVect0LinkObjId="SW-84165_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84163_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5354,-617 5354,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28f0060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5355,-581 5354,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32045@1" ObjectIDZND0="18565@0" Pin0InfoVect0LinkObjId="SW-84163_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84165_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5355,-581 5354,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28f02c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5355,-564 5355,-552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32045@0" ObjectIDZND0="17929@0" Pin0InfoVect0LinkObjId="g_2454650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84165_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5355,-564 5355,-552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28f8ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4373,-623 4373,-637 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17935@1" ObjectIDZND0="32041@1" Pin0InfoVect0LinkObjId="SW-81193_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81191_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4373,-623 4373,-637 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28f8d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4373,-582 4373,-596 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32046@1" ObjectIDZND0="17935@0" Pin0InfoVect0LinkObjId="SW-81191_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81193_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4373,-582 4373,-596 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24caa30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5330,-900 5330,-911 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="18563@1" ObjectIDZND0="g_24ca220@0" Pin0InfoVect0LinkObjId="g_24ca220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-84141_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5330,-900 5330,-911 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24cb500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4350,-905 4350,-916 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="17933@1" ObjectIDZND0="g_24cac60@0" Pin0InfoVect0LinkObjId="g_24cac60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81169_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4350,-905 4350,-916 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24cb760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3787,-438 3798,-438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_24cc6f0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_249dcf0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_249dcf0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24cc6f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3787,-438 3798,-438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24cc230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3798,-448 3798,-438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_249dcf0@0" ObjectIDZND1="g_24cc6f0@0" Pin0InfoVect0LinkObjId="g_249dcf0_0" Pin0InfoVect1LinkObjId="g_24cc6f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3798,-448 3798,-438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24cc490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3798,-438 3798,-426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_24cc6f0@0" ObjectIDZND0="g_249dcf0@0" Pin0InfoVect0LinkObjId="g_249dcf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_24cc6f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3798,-438 3798,-426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24cd440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-231 4030,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="17947@x" ObjectIDZND0="g_246da70@0" Pin0InfoVect0LinkObjId="g_246da70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81275_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-231 4030,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24cd6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4374,-292 4389,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="17955@x" ObjectIDND1="17954@x" ObjectIDND2="34011@x" ObjectIDZND0="g_24b66c0@0" Pin0InfoVect0LinkObjId="g_24b66c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-81326_0" Pin1InfoVect1LinkObjId="SW-81325_0" Pin1InfoVect2LinkObjId="EC-LF_HJ.HJ_064Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4374,-292 4389,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24d9c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-311 4007,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="17945@0" ObjectIDZND0="17946@x" ObjectIDZND1="17970@x" Pin0InfoVect0LinkObjId="SW-81274_0" Pin0InfoVect1LinkObjId="CB-LF_HJ.LF_HJ_C_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-81272_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-311 4007,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24d9ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-302 4007,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="17945@x" ObjectIDND1="17946@x" ObjectIDZND0="17970@0" Pin0InfoVect0LinkObjId="CB-LF_HJ.LF_HJ_C_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-81272_0" Pin1InfoVect1LinkObjId="SW-81274_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-302 4007,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24da120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-302 3966,-302 3966,-297 3966,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="17945@x" ObjectIDND1="17970@x" ObjectIDZND0="17946@1" Pin0InfoVect0LinkObjId="SW-81274_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-81272_0" Pin1InfoVect1LinkObjId="CB-LF_HJ.LF_HJ_C_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-302 3966,-302 3966,-297 3966,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24dac20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-288 4007,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="lightningRod" ObjectIDZND0="17947@x" ObjectIDZND1="g_246da70@0" Pin0InfoVect0LinkObjId="SW-81275_0" Pin0InfoVect1LinkObjId="g_246da70_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-288 4007,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24dae80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-231 4007,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_246da70@0" ObjectIDZND0="17947@1" Pin0InfoVect0LinkObjId="SW-81275_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_246da70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-231 4007,-149 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectPoint_Layer"/><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-81066" prefix="Ua  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4063.000000 -1105.000000) translate(0,21)">Ua   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81066" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17928"/>
     <cge:Term_Ref ObjectID="24741"/>
    <cge:TPSR_Ref TObjectID="17928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-81067" prefix="Ub " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4063.000000 -1105.000000) translate(0,47)">Ub  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81067" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17928"/>
     <cge:Term_Ref ObjectID="24741"/>
    <cge:TPSR_Ref TObjectID="17928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-81068" prefix="Uc " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4063.000000 -1105.000000) translate(0,73)">Uc  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81068" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17928"/>
     <cge:Term_Ref ObjectID="24741"/>
    <cge:TPSR_Ref TObjectID="17928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-81454" prefix="Uab " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4063.000000 -1105.000000) translate(0,99)">Uab  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81454" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17928"/>
     <cge:Term_Ref ObjectID="24741"/>
    <cge:TPSR_Ref TObjectID="17928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-81069" prefix="Ubc " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4063.000000 -1105.000000) translate(0,125)">Ubc  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81069" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17928"/>
     <cge:Term_Ref ObjectID="24741"/>
    <cge:TPSR_Ref TObjectID="17928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-81070" prefix="Uca " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4063.000000 -1105.000000) translate(0,151)">Uca  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81070" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17928"/>
     <cge:Term_Ref ObjectID="24741"/>
    <cge:TPSR_Ref TObjectID="17928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-81095" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4457.000000 -692.000000) translate(0,21)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81095" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17935"/>
     <cge:Term_Ref ObjectID="24753"/>
    <cge:TPSR_Ref TObjectID="17935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-81096" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4457.000000 -692.000000) translate(0,47)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81096" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17935"/>
     <cge:Term_Ref ObjectID="24753"/>
    <cge:TPSR_Ref TObjectID="17935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-81087" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4457.000000 -692.000000) translate(0,73)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81087" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17935"/>
     <cge:Term_Ref ObjectID="24753"/>
    <cge:TPSR_Ref TObjectID="17935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-81089" prefix="Ic  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4457.000000 -692.000000) translate(0,99)">Ic   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81089" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17935"/>
     <cge:Term_Ref ObjectID="24753"/>
    <cge:TPSR_Ref TObjectID="17935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-81097" prefix="Cos " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4457.000000 -692.000000) translate(0,125)">Cos  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81097" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17935"/>
     <cge:Term_Ref ObjectID="24753"/>
    <cge:TPSR_Ref TObjectID="17935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-81162" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4751.000000 -74.000000) translate(0,21)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81162" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17960"/>
     <cge:Term_Ref ObjectID="24807"/>
    <cge:TPSR_Ref TObjectID="17960"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-81163" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4751.000000 -74.000000) translate(0,47)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81163" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17960"/>
     <cge:Term_Ref ObjectID="24807"/>
    <cge:TPSR_Ref TObjectID="17960"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-81154" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4751.000000 -74.000000) translate(0,73)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81154" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17960"/>
     <cge:Term_Ref ObjectID="24807"/>
    <cge:TPSR_Ref TObjectID="17960"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-81156" prefix="Ic  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4751.000000 -74.000000) translate(0,99)">Ic   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81156" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17960"/>
     <cge:Term_Ref ObjectID="24807"/>
    <cge:TPSR_Ref TObjectID="17960"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-81164" prefix="Cos " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4751.000000 -74.000000) translate(0,125)">Cos  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81164" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17960"/>
     <cge:Term_Ref ObjectID="24807"/>
    <cge:TPSR_Ref TObjectID="17960"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-81151" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4558.000000 -74.000000) translate(0,21)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81151" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17956"/>
     <cge:Term_Ref ObjectID="24799"/>
    <cge:TPSR_Ref TObjectID="17956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-81152" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4558.000000 -74.000000) translate(0,47)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81152" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17956"/>
     <cge:Term_Ref ObjectID="24799"/>
    <cge:TPSR_Ref TObjectID="17956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-81143" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4558.000000 -74.000000) translate(0,73)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81143" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17956"/>
     <cge:Term_Ref ObjectID="24799"/>
    <cge:TPSR_Ref TObjectID="17956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-81145" prefix="Ic  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4558.000000 -74.000000) translate(0,99)">Ic   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81145" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17956"/>
     <cge:Term_Ref ObjectID="24799"/>
    <cge:TPSR_Ref TObjectID="17956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-81153" prefix="Cos " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4558.000000 -74.000000) translate(0,125)">Cos  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81153" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17956"/>
     <cge:Term_Ref ObjectID="24799"/>
    <cge:TPSR_Ref TObjectID="17956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-81140" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4345.000000 -74.000000) translate(0,21)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81140" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17952"/>
     <cge:Term_Ref ObjectID="24791"/>
    <cge:TPSR_Ref TObjectID="17952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-81141" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4345.000000 -74.000000) translate(0,47)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81141" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17952"/>
     <cge:Term_Ref ObjectID="24791"/>
    <cge:TPSR_Ref TObjectID="17952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-81132" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4345.000000 -74.000000) translate(0,73)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81132" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17952"/>
     <cge:Term_Ref ObjectID="24791"/>
    <cge:TPSR_Ref TObjectID="17952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-81134" prefix="Ic  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4345.000000 -74.000000) translate(0,99)">Ic   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81134" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17952"/>
     <cge:Term_Ref ObjectID="24791"/>
    <cge:TPSR_Ref TObjectID="17952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-81142" prefix="Cos " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4345.000000 -74.000000) translate(0,125)">Cos  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81142" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17952"/>
     <cge:Term_Ref ObjectID="24791"/>
    <cge:TPSR_Ref TObjectID="17952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-81129" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4155.000000 -74.000000) translate(0,21)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81129" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17948"/>
     <cge:Term_Ref ObjectID="24783"/>
    <cge:TPSR_Ref TObjectID="17948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-81130" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4155.000000 -74.000000) translate(0,47)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81130" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17948"/>
     <cge:Term_Ref ObjectID="24783"/>
    <cge:TPSR_Ref TObjectID="17948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-81121" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4155.000000 -74.000000) translate(0,73)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81121" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17948"/>
     <cge:Term_Ref ObjectID="24783"/>
    <cge:TPSR_Ref TObjectID="17948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-81123" prefix="Ic  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4155.000000 -74.000000) translate(0,99)">Ic   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81123" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17948"/>
     <cge:Term_Ref ObjectID="24783"/>
    <cge:TPSR_Ref TObjectID="17948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-81131" prefix="Cos " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4155.000000 -74.000000) translate(0,125)">Cos  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81131" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17948"/>
     <cge:Term_Ref ObjectID="24783"/>
    <cge:TPSR_Ref TObjectID="17948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-81118" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3968.000000 -74.000000) translate(0,21)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81118" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17943"/>
     <cge:Term_Ref ObjectID="24773"/>
    <cge:TPSR_Ref TObjectID="17943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-81119" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3968.000000 -74.000000) translate(0,47)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81119" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17943"/>
     <cge:Term_Ref ObjectID="24773"/>
    <cge:TPSR_Ref TObjectID="17943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-81110" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3968.000000 -74.000000) translate(0,73)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81110" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17943"/>
     <cge:Term_Ref ObjectID="24773"/>
    <cge:TPSR_Ref TObjectID="17943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-81112" prefix="Ic  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3968.000000 -74.000000) translate(0,99)">Ic   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81112" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17943"/>
     <cge:Term_Ref ObjectID="24773"/>
    <cge:TPSR_Ref TObjectID="17943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-81120" prefix="Cos " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3968.000000 -74.000000) translate(0,125)">Cos  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81120" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17943"/>
     <cge:Term_Ref ObjectID="24773"/>
    <cge:TPSR_Ref TObjectID="17943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-81107" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4911.000000 -1079.000000) translate(0,21)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81107" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17936"/>
     <cge:Term_Ref ObjectID="24755"/>
    <cge:TPSR_Ref TObjectID="17936"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-81108" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4911.000000 -1079.000000) translate(0,47)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81108" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17936"/>
     <cge:Term_Ref ObjectID="24755"/>
    <cge:TPSR_Ref TObjectID="17936"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-81099" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4911.000000 -1079.000000) translate(0,73)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81099" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17936"/>
     <cge:Term_Ref ObjectID="24755"/>
    <cge:TPSR_Ref TObjectID="17936"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-81101" prefix="Ic  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4911.000000 -1079.000000) translate(0,99)">Ic   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81101" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17936"/>
     <cge:Term_Ref ObjectID="24755"/>
    <cge:TPSR_Ref TObjectID="17936"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-81109" prefix="Cos " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4911.000000 -1079.000000) translate(0,125)">Cos  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81109" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17936"/>
     <cge:Term_Ref ObjectID="24755"/>
    <cge:TPSR_Ref TObjectID="17936"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-81084" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4434.000000 -920.000000) translate(0,21)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81084" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17930"/>
     <cge:Term_Ref ObjectID="24743"/>
    <cge:TPSR_Ref TObjectID="17930"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-81085" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4434.000000 -920.000000) translate(0,47)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81085" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17930"/>
     <cge:Term_Ref ObjectID="24743"/>
    <cge:TPSR_Ref TObjectID="17930"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-81076" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4434.000000 -920.000000) translate(0,73)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81076" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17930"/>
     <cge:Term_Ref ObjectID="24743"/>
    <cge:TPSR_Ref TObjectID="17930"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-81078" prefix="Ic  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4434.000000 -920.000000) translate(0,99)">Ic   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81078" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17930"/>
     <cge:Term_Ref ObjectID="24743"/>
    <cge:TPSR_Ref TObjectID="17930"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-81086" prefix="Cos " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4434.000000 -920.000000) translate(0,125)">Cos  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81086" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17930"/>
     <cge:Term_Ref ObjectID="24743"/>
    <cge:TPSR_Ref TObjectID="17930"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-84110" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5455.000000 -681.000000) translate(0,20)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84110" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18565"/>
     <cge:Term_Ref ObjectID="25896"/>
    <cge:TPSR_Ref TObjectID="18565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-84111" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5455.000000 -681.000000) translate(0,45)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84111" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18565"/>
     <cge:Term_Ref ObjectID="25896"/>
    <cge:TPSR_Ref TObjectID="18565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-84101" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5455.000000 -681.000000) translate(0,70)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84101" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18565"/>
     <cge:Term_Ref ObjectID="25896"/>
    <cge:TPSR_Ref TObjectID="18565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-84103" prefix="Ic  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5455.000000 -681.000000) translate(0,95)">Ic   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84103" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18565"/>
     <cge:Term_Ref ObjectID="25896"/>
    <cge:TPSR_Ref TObjectID="18565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-84112" prefix="Cos " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5455.000000 -681.000000) translate(0,120)">Cos  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84112" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18565"/>
     <cge:Term_Ref ObjectID="25896"/>
    <cge:TPSR_Ref TObjectID="18565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-84275" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5492.000000 -69.000000) translate(0,20)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84275" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18571"/>
     <cge:Term_Ref ObjectID="25898"/>
    <cge:TPSR_Ref TObjectID="18571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-84276" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5492.000000 -69.000000) translate(0,45)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84276" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18571"/>
     <cge:Term_Ref ObjectID="25898"/>
    <cge:TPSR_Ref TObjectID="18571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-84266" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5492.000000 -69.000000) translate(0,70)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84266" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18571"/>
     <cge:Term_Ref ObjectID="25898"/>
    <cge:TPSR_Ref TObjectID="18571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-84268" prefix="Ic  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5492.000000 -69.000000) translate(0,95)">Ic   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84268" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18571"/>
     <cge:Term_Ref ObjectID="25898"/>
    <cge:TPSR_Ref TObjectID="18571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-84277" prefix="Cos " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5492.000000 -69.000000) translate(0,120)">Cos  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84277" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18571"/>
     <cge:Term_Ref ObjectID="25898"/>
    <cge:TPSR_Ref TObjectID="18571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-84135" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5003.000000 -375.000000) translate(0,20)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84135" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18569"/>
     <cge:Term_Ref ObjectID="25904"/>
    <cge:TPSR_Ref TObjectID="18569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-84136" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5003.000000 -375.000000) translate(0,45)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84136" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18569"/>
     <cge:Term_Ref ObjectID="25904"/>
    <cge:TPSR_Ref TObjectID="18569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-84126" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5003.000000 -375.000000) translate(0,70)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84126" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18569"/>
     <cge:Term_Ref ObjectID="25904"/>
    <cge:TPSR_Ref TObjectID="18569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-84128" prefix="Ic  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5003.000000 -375.000000) translate(0,95)">Ic   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84128" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18569"/>
     <cge:Term_Ref ObjectID="25904"/>
    <cge:TPSR_Ref TObjectID="18569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-84137" prefix="Cos " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5003.000000 -375.000000) translate(0,120)">Cos  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84137" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18569"/>
     <cge:Term_Ref ObjectID="25904"/>
    <cge:TPSR_Ref TObjectID="18569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-84098" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5439.000000 -904.000000) translate(0,20)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84098" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18561"/>
     <cge:Term_Ref ObjectID="25888"/>
    <cge:TPSR_Ref TObjectID="18561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-84099" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5439.000000 -904.000000) translate(0,45)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84099" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18561"/>
     <cge:Term_Ref ObjectID="25888"/>
    <cge:TPSR_Ref TObjectID="18561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-84089" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5439.000000 -904.000000) translate(0,70)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84089" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18561"/>
     <cge:Term_Ref ObjectID="25888"/>
    <cge:TPSR_Ref TObjectID="18561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-84091" prefix="Ic  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5439.000000 -904.000000) translate(0,95)">Ic   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84091" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18561"/>
     <cge:Term_Ref ObjectID="25888"/>
    <cge:TPSR_Ref TObjectID="18561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-84100" prefix="Cos " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5439.000000 -904.000000) translate(0,120)">Cos  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84100" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18561"/>
     <cge:Term_Ref ObjectID="25888"/>
    <cge:TPSR_Ref TObjectID="18561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-84113" prefix="Tap  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5229.000000 -773.000000) translate(0,16)">Tap   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84113" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18590"/>
     <cge:Term_Ref ObjectID="25922"/>
    <cge:TPSR_Ref TObjectID="18590"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-81525" prefix="Tap  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4254.000000 -767.000000) translate(0,12)">Tap   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81525" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17971"/>
     <cge:Term_Ref ObjectID="24831"/>
    <cge:TPSR_Ref TObjectID="17971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-81071" prefix="Ua  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4891.000000 -669.000000) translate(0,15)">Ua   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81071" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18579"/>
     <cge:Term_Ref ObjectID="25887"/>
    <cge:TPSR_Ref TObjectID="18579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-81072" prefix="Ub " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4891.000000 -669.000000) translate(0,33)">Ub  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81072" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18579"/>
     <cge:Term_Ref ObjectID="25887"/>
    <cge:TPSR_Ref TObjectID="18579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-81073" prefix="Uc " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4891.000000 -669.000000) translate(0,51)">Uc  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81073" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18579"/>
     <cge:Term_Ref ObjectID="25887"/>
    <cge:TPSR_Ref TObjectID="18579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-81455" prefix="Uab " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4891.000000 -669.000000) translate(0,69)">Uab  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81455" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18579"/>
     <cge:Term_Ref ObjectID="25887"/>
    <cge:TPSR_Ref TObjectID="18579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-81074" prefix="Ubc " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4891.000000 -669.000000) translate(0,87)">Ubc  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81074" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18579"/>
     <cge:Term_Ref ObjectID="25887"/>
    <cge:TPSR_Ref TObjectID="18579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-81075" prefix="Uca " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4891.000000 -669.000000) translate(0,105)">Uca  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="81075" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="18579"/>
     <cge:Term_Ref ObjectID="25887"/>
    <cge:TPSR_Ref TObjectID="18579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-84278" prefix="Ua  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5106.000000 -671.000000) translate(0,15)">Ua   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84278" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17929"/>
     <cge:Term_Ref ObjectID="24742"/>
    <cge:TPSR_Ref TObjectID="17929"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-84279" prefix="Ub " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5106.000000 -671.000000) translate(0,33)">Ub  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84279" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17929"/>
     <cge:Term_Ref ObjectID="24742"/>
    <cge:TPSR_Ref TObjectID="17929"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-84280" prefix="Uc " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5106.000000 -671.000000) translate(0,51)">Uc  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84280" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17929"/>
     <cge:Term_Ref ObjectID="24742"/>
    <cge:TPSR_Ref TObjectID="17929"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-84281" prefix="Uab " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5106.000000 -671.000000) translate(0,69)">Uab  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84281" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17929"/>
     <cge:Term_Ref ObjectID="24742"/>
    <cge:TPSR_Ref TObjectID="17929"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-84282" prefix="Ubc " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5106.000000 -671.000000) translate(0,87)">Ubc  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84282" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17929"/>
     <cge:Term_Ref ObjectID="24742"/>
    <cge:TPSR_Ref TObjectID="17929"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-84283" prefix="Uca " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5106.000000 -671.000000) translate(0,105)">Uca  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="84283" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17929"/>
     <cge:Term_Ref ObjectID="24742"/>
    <cge:TPSR_Ref TObjectID="17929"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3466" y="-1166"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3466" y="-1166"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3248" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3199" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="72" x="3151" y="-826"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="72" x="3151" y="-826"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3466" y="-1201"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3466" y="-1201"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4827" y="-1032"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4827" y="-1032"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="63" x="4409" y="-774"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="63" x="4409" y="-774"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="62" x="5389" y="-765"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="62" x="5389" y="-765"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="4023" y="-500"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="4023" y="-500"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="4199" y="-500"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="4199" y="-500"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="31" x="4389" y="-505"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="31" x="4389" y="-505"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="4602" y="-504"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="4602" y="-504"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="4793" y="-503"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="4793" y="-503"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="4947" y="-499"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="4947" y="-499"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="5556" y="-501"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="5556" y="-501"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="3610" y="-1190"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="3610" y="-1190"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_配调_配网接线图35_禄丰.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3466" y="-1166"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/></g>
   <g href="35kV黑井变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="72" x="3151" y="-826"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3466" y="-1201"/></g>
   <g href="35kV黑井变35kV甸黑阿线361断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4827" y="-1032"/></g>
   <g href="35kV黑井变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="63" x="4409" y="-774"/></g>
   <g href="35kV黑井变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="62" x="5389" y="-765"/></g>
   <g href="35kV黑井变1号电容器062断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="4023" y="-500"/></g>
   <g href="35kV黑井变10kV青龙线063断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="4199" y="-500"/></g>
   <g href="35kV黑井变10kV赵园线064断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="31" x="4389" y="-505"/></g>
   <g href="35kV黑井变10kV红石岩线065断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="4602" y="-504"/></g>
   <g href="35kV黑井变10kV城区线066断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="4793" y="-503"/></g>
   <g href="35kV黑井变10kV分段保护测控012断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="4947" y="-499"/></g>
   <g href="35kV黑井变10kV轿子山隧道专线071断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="5556" y="-501"/></g>
   <g href="AVC黑井站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="3610" y="-1190"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="3609" y="-1190"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-LF_HJ.LF_HJ_1M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4031,-934 5682,-934 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17928" ObjectName="BS-LF_HJ.LF_HJ_1M"/>
    <cge:TPSR_Ref TObjectID="17928"/></metadata>
   <polyline fill="none" opacity="0" points="4031,-934 5682,-934 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_HJ.LF_HJ_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3567,-553 5010,-553 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18579" ObjectName="BS-LF_HJ.LF_HJ_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="18579"/></metadata>
   <polyline fill="none" opacity="0" points="3567,-553 5010,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_HJ.LF_HJ_9ⅡM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5105,-552 5812,-552 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17929" ObjectName="BS-LF_HJ.LF_HJ_9ⅡM"/>
    <cge:TPSR_Ref TObjectID="17929"/></metadata>
   <polyline fill="none" opacity="0" points="5105,-552 5812,-552 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5141.582170 -1075.000000)" xlink:href="#transformer2:shape20_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5141.582170 -1075.000000)" xlink:href="#transformer2:shape20_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-LF_HJ.LF_HJ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="24833"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4339.000000 -730.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4339.000000 -730.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="17971" ObjectName="TF-LF_HJ.LF_HJ_1T"/>
    <cge:TPSR_Ref TObjectID="17971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-LF_HJ.LF_HJ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="25924"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5319.000000 -726.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5319.000000 -726.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="18590" ObjectName="TF-LF_HJ.LF_HJ_2T"/>
    <cge:TPSR_Ref TObjectID="18590"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3236.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-215279" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3260.000000 -951.000000) translate(0,21)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215279" ObjectName="LF_HJ:LF_HJ_sum_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-215278" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3262.000000 -911.000000) translate(0,21)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215278" ObjectName="LF_HJ:LF_HJ_sum_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217873" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3258.000000 -1030.000000) translate(0,21)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217873" ObjectName="LF_HJ:LF_HJ_sum_P1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217873" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3259.000000 -991.000000) translate(0,21)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217873" ObjectName="LF_HJ:LF_HJ_sum_P1"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-52543" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3506.000000 -1089.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9253" ObjectName="DYN-LF_HJ"/>
     <cge:Meas_Ref ObjectId="52543"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_211cb70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4769.000000 1230.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="4776" cy="1223" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-LF_HJ.LF_HJ_C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3970.000000 -159.000000)" xlink:href="#capacitor:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17970" ObjectName="CB-LF_HJ.LF_HJ_C"/>
    <cge:TPSR_Ref TObjectID="17970"/></metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="18579" cx="3798" cy="-553" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17928" cx="4809" cy="-934" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17928" cx="5127" cy="-934" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17928" cx="5354" cy="-934" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17929" cx="5179" cy="-552" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18579" cx="3637" cy="-553" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18579" cx="4007" cy="-553" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18579" cx="4929" cy="-553" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18579" cx="4180" cy="-553" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17929" cx="5355" cy="-552" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="18579" cx="4373" cy="-553" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-81232">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4800.213685 -947.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17937" ObjectName="SW-LF_HJ.LF_HJ_3611SW"/>
     <cge:Meas_Ref ObjectId="81232"/>
    <cge:TPSR_Ref TObjectID="17937"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81236">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4800.213685 -1054.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17939" ObjectName="SW-LF_HJ.LF_HJ_3616SW"/>
     <cge:Meas_Ref ObjectId="81236"/>
    <cge:TPSR_Ref TObjectID="17939"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81417">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5086.582170 -968.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17932" ObjectName="SW-LF_HJ.LF_HJ_36217SW"/>
     <cge:Meas_Ref ObjectId="81417"/>
    <cge:TPSR_Ref TObjectID="17932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81406">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4106.947935 -850.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17964" ObjectName="SW-LF_HJ.LF_HJ_3901SW"/>
     <cge:Meas_Ref ObjectId="81406"/>
    <cge:TPSR_Ref TObjectID="17964"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81272">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3998.000000 -306.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17945" ObjectName="SW-LF_HJ.LF_HJ_0626SW"/>
     <cge:Meas_Ref ObjectId="81272"/>
    <cge:TPSR_Ref TObjectID="17945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81299">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4171.000000 -304.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17951" ObjectName="SW-LF_HJ.LF_HJ_0636SW"/>
     <cge:Meas_Ref ObjectId="81299"/>
    <cge:TPSR_Ref TObjectID="17951"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81326">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4365.000000 -306.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17955" ObjectName="SW-LF_HJ.LF_HJ_0646SW"/>
     <cge:Meas_Ref ObjectId="81326"/>
    <cge:TPSR_Ref TObjectID="17955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81353">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4574.000000 -304.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17959" ObjectName="SW-LF_HJ.LF_HJ_0656SW"/>
     <cge:Meas_Ref ObjectId="81353"/>
    <cge:TPSR_Ref TObjectID="17959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81380">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4767.000000 -302.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17963" ObjectName="SW-LF_HJ.LF_HJ_0666SW"/>
     <cge:Meas_Ref ObjectId="81380"/>
    <cge:TPSR_Ref TObjectID="17963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81167">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4365.000000 -873.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17931" ObjectName="SW-LF_HJ.LF_HJ_3011SW"/>
     <cge:Meas_Ref ObjectId="81167"/>
    <cge:TPSR_Ref TObjectID="17931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81415">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5118.000000 -954.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17967" ObjectName="SW-LF_HJ.LF_HJ_3621SW"/>
     <cge:Meas_Ref ObjectId="81415"/>
    <cge:TPSR_Ref TObjectID="17967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81275">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3998.000000 -108.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17947" ObjectName="SW-LF_HJ.LF_HJ_06200SW"/>
     <cge:Meas_Ref ObjectId="81275"/>
    <cge:TPSR_Ref TObjectID="17947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81271">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3967.000000 -378.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17944" ObjectName="SW-LF_HJ.LF_HJ_06260SW"/>
     <cge:Meas_Ref ObjectId="81271"/>
    <cge:TPSR_Ref TObjectID="17944"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81297">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4142.000000 -389.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17949" ObjectName="SW-LF_HJ.LF_HJ_06360SW"/>
     <cge:Meas_Ref ObjectId="81297"/>
    <cge:TPSR_Ref TObjectID="17949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81324">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4336.000000 -391.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17953" ObjectName="SW-LF_HJ.LF_HJ_06460SW"/>
     <cge:Meas_Ref ObjectId="81324"/>
    <cge:TPSR_Ref TObjectID="17953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81351">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4546.000000 -389.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17957" ObjectName="SW-LF_HJ.LF_HJ_06560SW"/>
     <cge:Meas_Ref ObjectId="81351"/>
    <cge:TPSR_Ref TObjectID="17957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81352">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4551.000000 -248.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17958" ObjectName="SW-LF_HJ.LF_HJ_06567SW"/>
     <cge:Meas_Ref ObjectId="81352"/>
    <cge:TPSR_Ref TObjectID="17958"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81325">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4343.000000 -250.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17954" ObjectName="SW-LF_HJ.LF_HJ_06467SW"/>
     <cge:Meas_Ref ObjectId="81325"/>
    <cge:TPSR_Ref TObjectID="17954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81298">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4149.000000 -249.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17950" ObjectName="SW-LF_HJ.LF_HJ_06367SW"/>
     <cge:Meas_Ref ObjectId="81298"/>
    <cge:TPSR_Ref TObjectID="17950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81274">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3957.000000 -251.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17946" ObjectName="SW-LF_HJ.LF_HJ_06267SW"/>
     <cge:Meas_Ref ObjectId="81274"/>
    <cge:TPSR_Ref TObjectID="17946"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81378">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4738.000000 -389.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17961" ObjectName="SW-LF_HJ.LF_HJ_06660SW"/>
     <cge:Meas_Ref ObjectId="81378"/>
    <cge:TPSR_Ref TObjectID="17961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81379">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4744.000000 -250.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17962" ObjectName="SW-LF_HJ.LF_HJ_06667SW"/>
     <cge:Meas_Ref ObjectId="81379"/>
    <cge:TPSR_Ref TObjectID="17962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81410">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4154.000000 -799.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17966" ObjectName="SW-LF_HJ.LF_HJ_39017SW"/>
     <cge:Meas_Ref ObjectId="81410"/>
    <cge:TPSR_Ref TObjectID="17966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81408">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4084.000000 -871.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17965" ObjectName="SW-LF_HJ.LF_HJ_39010SW"/>
     <cge:Meas_Ref ObjectId="81408"/>
    <cge:TPSR_Ref TObjectID="17965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81169">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4341.000000 -864.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17933" ObjectName="SW-LF_HJ.LF_HJ_30117SW"/>
     <cge:Meas_Ref ObjectId="81169"/>
    <cge:TPSR_Ref TObjectID="17933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81238">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4844.000000 -1047.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17940" ObjectName="SW-LF_HJ.LF_HJ_36160SW"/>
     <cge:Meas_Ref ObjectId="81238"/>
    <cge:TPSR_Ref TObjectID="17940"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81234">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4773.000000 -966.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17938" ObjectName="SW-LF_HJ.LF_HJ_36117SW"/>
     <cge:Meas_Ref ObjectId="81234"/>
    <cge:TPSR_Ref TObjectID="17938"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81239">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4773.000000 -1073.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17941" ObjectName="SW-LF_HJ.LF_HJ_36167SW"/>
     <cge:Meas_Ref ObjectId="81239"/>
    <cge:TPSR_Ref TObjectID="17941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84139">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5345.000000 -869.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18562" ObjectName="SW-LF_HJ.LF_HJ_3021SW"/>
     <cge:Meas_Ref ObjectId="84139"/>
    <cge:TPSR_Ref TObjectID="18562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84141">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5321.000000 -859.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18563" ObjectName="SW-LF_HJ.LF_HJ_30217SW"/>
     <cge:Meas_Ref ObjectId="84141"/>
    <cge:TPSR_Ref TObjectID="18563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84289">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5538.000000 -302.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18581" ObjectName="SW-LF_HJ.LF_HJ_0716SW"/>
     <cge:Meas_Ref ObjectId="84289"/>
    <cge:TPSR_Ref TObjectID="18581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84288">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5509.000000 -389.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18580" ObjectName="SW-LF_HJ.LF_HJ_07160SW"/>
     <cge:Meas_Ref ObjectId="84288"/>
    <cge:TPSR_Ref TObjectID="18580"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5515.000000 -250.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84259">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5313.000000 -464.000000)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18570" ObjectName="SW-LF_HJ.LF_HJ_0902SW"/>
     <cge:Meas_Ref ObjectId="84259"/>
    <cge:TPSR_Ref TObjectID="18570"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84232">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5172.000000 -468.000000)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18582" ObjectName="SW-LF_HJ.LF_HJ_0122SW"/>
     <cge:Meas_Ref ObjectId="84232"/>
    <cge:TPSR_Ref TObjectID="18582"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81450">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3630.000000 -470.000000)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17968" ObjectName="SW-LF_HJ.LF_HJ_0901XC"/>
     <cge:Meas_Ref ObjectId="81450"/>
    <cge:TPSR_Ref TObjectID="17968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81269">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3997.000000 -514.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32040" ObjectName="SW-LF_HJ.LF_HJ_062XC"/>
     <cge:Meas_Ref ObjectId="81269"/>
    <cge:TPSR_Ref TObjectID="32040"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81269">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3997.000000 -435.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32047" ObjectName="SW-LF_HJ.LF_HJ_062XC1"/>
     <cge:Meas_Ref ObjectId="81269"/>
    <cge:TPSR_Ref TObjectID="32047"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81295">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4170.000000 -517.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32049" ObjectName="SW-LF_HJ.LF_HJ_063XC"/>
     <cge:Meas_Ref ObjectId="81295"/>
    <cge:TPSR_Ref TObjectID="32049"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81295">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4170.000000 -438.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32050" ObjectName="SW-LF_HJ.LF_HJ_063XC1"/>
     <cge:Meas_Ref ObjectId="81295"/>
    <cge:TPSR_Ref TObjectID="32050"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81322">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4363.000000 -519.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32048" ObjectName="SW-LF_HJ.LF_HJ_064XC"/>
     <cge:Meas_Ref ObjectId="81322"/>
    <cge:TPSR_Ref TObjectID="32048"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81322">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4363.000000 -440.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32051" ObjectName="SW-LF_HJ.LF_HJ_064XC1"/>
     <cge:Meas_Ref ObjectId="81322"/>
    <cge:TPSR_Ref TObjectID="32051"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81349">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4573.000000 -516.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32053" ObjectName="SW-LF_HJ.LF_HJ_065XC"/>
     <cge:Meas_Ref ObjectId="81349"/>
    <cge:TPSR_Ref TObjectID="32053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81349">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4573.000000 -437.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32054" ObjectName="SW-LF_HJ.LF_HJ_065XC1"/>
     <cge:Meas_Ref ObjectId="81349"/>
    <cge:TPSR_Ref TObjectID="32054"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81376">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4766.000000 -516.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32052" ObjectName="SW-LF_HJ.LF_HJ_066XC"/>
     <cge:Meas_Ref ObjectId="81376"/>
    <cge:TPSR_Ref TObjectID="32052"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81376">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4766.000000 -437.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32055" ObjectName="SW-LF_HJ.LF_HJ_066XC1"/>
     <cge:Meas_Ref ObjectId="81376"/>
    <cge:TPSR_Ref TObjectID="32055"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84230">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4919.000000 -512.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32056" ObjectName="SW-LF_HJ.LF_HJ_012XC"/>
     <cge:Meas_Ref ObjectId="84230"/>
    <cge:TPSR_Ref TObjectID="32056"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84230">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4919.000000 -433.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32057" ObjectName="SW-LF_HJ.LF_HJ_012XC1"/>
     <cge:Meas_Ref ObjectId="84230"/>
    <cge:TPSR_Ref TObjectID="32057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84286">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5537.000000 -514.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32043" ObjectName="SW-LF_HJ.LF_HJ_071XC"/>
     <cge:Meas_Ref ObjectId="84286"/>
    <cge:TPSR_Ref TObjectID="32043"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84286">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5537.000000 -435.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32044" ObjectName="SW-LF_HJ.LF_HJ_071XC1"/>
     <cge:Meas_Ref ObjectId="84286"/>
    <cge:TPSR_Ref TObjectID="32044"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84165">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5344.000000 -624.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32042" ObjectName="SW-LF_HJ.LF_HJ_002XC"/>
     <cge:Meas_Ref ObjectId="84165"/>
    <cge:TPSR_Ref TObjectID="32042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-84165">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5345.000000 -557.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32045" ObjectName="SW-LF_HJ.LF_HJ_002XC1"/>
     <cge:Meas_Ref ObjectId="84165"/>
    <cge:TPSR_Ref TObjectID="32045"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81193">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4363.000000 -630.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32041" ObjectName="SW-LF_HJ.LF_HJ_001XC"/>
     <cge:Meas_Ref ObjectId="81193"/>
    <cge:TPSR_Ref TObjectID="32041"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-81193">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4363.000000 -558.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32046" ObjectName="SW-LF_HJ.LF_HJ_001XC1"/>
     <cge:Meas_Ref ObjectId="81193"/>
    <cge:TPSR_Ref TObjectID="32046"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20986b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20986b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20986b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20986b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20986b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20986b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20986b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20986b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_20986b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1136920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1136920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1136920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1136920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1136920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1136920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1136920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1136920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1136920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1136920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1136920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1136920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1136920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1136920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1136920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1136920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1136920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1136920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1e77a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3283.000000 -1166.500000) translate(0,16)">黑井变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1e4df00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5100.000000 -1202.000000) translate(0,18)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a99ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5030.582170 -998.000000) translate(0,15)">36217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d1de80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4381.481796 -901.000000) translate(0,15)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1cf2620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5577.000000 -957.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1cef3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3572.000000 -595.000000) translate(0,15)">10kV I 段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e788f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4856.000000 -1070.000000) translate(0,15)">36160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2472570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4720.000000 -1113.000000) translate(0,15)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13fe530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4827.213685 -1032.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24871d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4816.213685 -978.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_246a5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4724.000000 -991.000000) translate(0,15)">36117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2464c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4811.000000 -1085.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24ac7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4025.000000 -713.000000) translate(0,15)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_24a5a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4399.000000 -748.000000) translate(0,16)">SZ11—5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_246e520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4397.000000 -725.000000) translate(0,16)">35±3*2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_205e430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3574.000000 -292.000000) translate(0,15)">10kVI段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1e54e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3765.000000 -254.000000) translate(0,18)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1e3a750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4150.333333 -141.000000) translate(0,18)">青龙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1e50270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4344.333333 -143.000000) translate(0,18)">赵园线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1cbd320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4553.333333 -141.000000) translate(0,18)">红石岩线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1fc61b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4746.333333 -139.000000) translate(0,18)">城区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fc69c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3909.000000 -409.000000) translate(0,15)">06260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fc6710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -277.000000) translate(0,15)">06267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fc79c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4089.000000 -420.000000) translate(0,15)">06360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e50060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4092.000000 -280.000000) translate(0,15)">06367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a7a9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4285.000000 -423.000000) translate(0,15)">06460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1fc99e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4283.000000 -265.000000) translate(0,15)">06467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2078a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4493.000000 -417.000000) translate(0,15)">06560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2075360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4493.000000 -279.000000) translate(0,15)">06567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a84f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4686.000000 -416.000000) translate(0,15)">06660</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_113dcf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4684.000000 -275.000000) translate(0,15)">06667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a84940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4015.000000 -336.000000) translate(0,15)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ab1480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4381.000000 -335.000000) translate(0,15)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1777270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4589.000000 -334.000000) translate(0,15)">0656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_201a550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4784.000000 -328.000000) translate(0,15)">0666</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ef7a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4037.213685 -903.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_247d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4119.213685 -890.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2468210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4173.213685 -824.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2467cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4287.481796 -895.000000) translate(0,15)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d0b730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3608.000000 -333.000000) translate(0,12)">u</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_1efbe50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4084.000000 -745.000000) translate(0,10)">U</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2098b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5134.000000 -987.000000) translate(0,16)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1d2f400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4014.000000 -141.000000) translate(0,16)">06200</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1d06030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4025.000000 -209.000000) translate(0,16)">#1电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ab1c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4187.000000 -334.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_11361b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4382.481796 -849.000000) translate(0,15)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1c1f680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3259.000000 -230.000000) translate(0,16)">15758580351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d25110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4120.000000 -497.000000) translate(0,15)">400/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ef6320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4312.000000 -496.000000) translate(0,15)">150/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e55f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4520.000000 -496.000000) translate(0,15)">400/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e4e310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4715.000000 -498.000000) translate(0,15)">150/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1cf7f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5372.481796 -893.000000) translate(0,15)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_17fc090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5381.000000 -734.000000) translate(0,16)">SZ11—10000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_17fc2e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5379.000000 -713.000000) translate(0,16)">35±3*2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24661c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5267.481796 -891.000000) translate(0,15)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21646c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5379.481796 -845.000000) translate(0,15)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_13e8fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5480.333333 -138.000000) translate(0,18)">骄子山隧道线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_11f73c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5457.000000 -416.000000) translate(0,15)">07160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_11f7640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5455.000000 -275.000000) translate(0,15)">07167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24a8330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5555.000000 -328.000000) translate(0,15)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1cf2c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5486.000000 -498.000000) translate(0,15)">150/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e4f150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5705.000000 -576.000000) translate(0,15)">10kV II 段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2012840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5213.000000 -276.000000) translate(0,15)">10kV II 段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e67b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5291.000000 -327.000000) translate(0,12)">u</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2470a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5334.000000 -495.000000) translate(0,16)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2454400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5195.000000 -508.000000) translate(0,15)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d16dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3151.000000 -826.000000) translate(0,15)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_246ec00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3659.000000 -511.000000) translate(0,15)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_246f580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3477.000000 -1158.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2485310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3477.000000 -1193.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2485b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4409.000000 -774.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f048f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5389.000000 -765.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28f9550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4023.000000 -500.000000) translate(0,15)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28f9b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4199.000000 -500.000000) translate(0,15)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28f9dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4389.000000 -505.000000) translate(0,15)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28fa000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4389.000000 -618.000000) translate(0,15)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28fa240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4602.000000 -504.000000) translate(0,15)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28fa480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4793.000000 -503.000000) translate(0,15)">066</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28fa6c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4947.000000 -499.000000) translate(0,15)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28fa900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5556.000000 -501.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28fcb10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5368.000000 -609.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="18" graphid="g_24cfc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2963.000000 -508.000000) translate(0,15)">1、10kV出线量测与主变低侧不平衡，现场有自动化人员进站时，自动化组联系核实。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="18" graphid="g_24cfc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2963.000000 -508.000000) translate(0,33)">2、1号主变低压侧001断路器现场上送的数值不对，主站侧放大1.43倍。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_24db0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3132.000000 -189.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_24db0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3132.000000 -189.000000) translate(0,38)">心变运二班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_24dd730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3259.000000 -199.500000) translate(0,17)">13508785260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_24dd730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3259.000000 -199.500000) translate(0,38)">18787879001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_24dd730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3259.000000 -199.500000) translate(0,59)">18787879002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="18" graphid="g_24df7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2972.000000 -665.000000) translate(0,15)">1号电容器组0626、06200刀闸和012手车工作位置上送地调信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="18" graphid="g_24df7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2972.000000 -665.000000) translate(0,33)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="18" graphid="g_24df7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2972.000000 -665.000000) translate(0,51)">与实际位置不对应，地调已取反，现场已报缺陷。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_211b360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3625.500000 -1178.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20ac120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4762.000000 -1294.000000) translate(0,12)">35kV甸黑阿线</text>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1e6eb80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4734.887352 -1131.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d0a5e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5117.582170 -1022.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_247c520">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3628.000000 -372.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2014f60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3677.464327 -376.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24a6680">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3603.000000 -298.000000)" xlink:href="#lightningRod:shape185"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cf8be0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3784.000000 -256.000000)" xlink:href="#lightningRod:shape186"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_249dcf0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3793.000000 -368.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2494ae0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4002.000000 -355.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2094da0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4175.000000 -353.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d253d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4369.000000 -355.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d20e40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4578.000000 -352.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d38a70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4771.000000 -350.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24b1f40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4368.000000 -667.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24ae640">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4596.000000 -283.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e3b6d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4792.000000 -283.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24b66c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4385.000000 -284.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e44db0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4191.000000 -283.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_246da70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4026.000000 -224.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d0d2f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4380.000000 -655.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e6bc90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4069.000000 -782.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a40d00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4107.000000 -794.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a9d7c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4082.000000 -726.000000)" xlink:href="#lightningRod:shape185"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d130d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4037.000000 -362.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24bf700">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4210.000000 -361.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_201d2f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4404.000000 -363.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a85310">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4613.000000 -361.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e6d7d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4806.000000 -360.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d19c80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5348.000000 -663.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d3aea0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5360.000000 -652.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24a8570">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5542.000000 -350.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2482690">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5563.000000 -283.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2011570">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5577.000000 -360.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e4f5b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5311.000000 -366.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f0e500">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5360.464327 -370.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e67ff0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5286.000000 -292.000000)" xlink:href="#lightningRod:shape185"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d0dad0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4921.464327 -324.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24cc6f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3730.000000 -431.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="LF_HJ"/>
</svg>