<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-175" aopId="16398" id="thSvg" viewBox="3115 -1295 2213 1232">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.592016" x1="11" x2="60" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.294304" x1="46" x2="27" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.294304" x1="46" x2="27" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.607595" x1="11" x2="11" y1="15" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.607595" x1="36" x2="36" y1="15" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.607595" x1="60" x2="60" y1="15" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.294304" x1="69" x2="50" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.294304" x1="69" x2="50" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.294304" x1="20" x2="1" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.294304" x1="20" x2="1" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="60" y1="24" y2="32"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape51_0">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline fill="none" points="64,100 1,37 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643357" x1="97" x2="39" y1="75" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643357" x1="97" x2="97" y1="54" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="100" x2="94" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="98" x2="96" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="103" x2="91" y1="54" y2="54"/>
    <polyline fill="none" points="64,93 64,100 " stroke-width="1"/>
    <polyline fill="none" points="58,100 64,100 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
   </symbol>
   <symbol id="transformer2:shape51_1">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape66_0">
    <circle cx="31" cy="80" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="31" y1="50" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <polyline DF8003:Layer="PUBLIC" points="31,12 25,25 37,25 31,12 31,13 31,12 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="43" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="79" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="79" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="79" y2="74"/>
   </symbol>
   <symbol id="transformer2:shape66_1">
    <circle cx="31" cy="58" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="31,55 6,55 6,26 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="55" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="55" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="55" y2="60"/>
   </symbol>
   <symbol id="voltageTransformer:shape93">
    <rect height="24" stroke-width="0.379884" width="14" x="14" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="9" y1="14" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="5" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="21" x2="21" y1="23" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="21" x2="24" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="21" x2="21" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="18" x2="21" y1="11" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="21" x2="24" y1="8" y2="11"/>
    <ellipse cx="8" cy="16" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <circle cx="20" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="20" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="18" x2="21" y1="26" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="5" y1="18" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="21" y1="75" y2="31"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1712690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_15e53f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_15dbb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_16914e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_15e4b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1640d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_15e8150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1674610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_170fc10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_17145e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_16422c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_16472d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_162e8f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_163e480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_16413a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1640580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_14efa00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_15e8b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_15dfd00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_170f360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_14f7c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_163ee80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1719c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1640190" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_163fd40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_14ed1d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_15e9a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    
   </symbol>
   <symbol id="Tag:shape33">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1678340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline fill="none" points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape34">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline fill="none" points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_14f8ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape36">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
   </symbol>
   <symbol id="Tag:shape37">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1242" width="2223" x="3110" y="-1300"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3117" x2="5326" y1="-1291" y2="-1291"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="5326" x2="5326" y1="-1289" y2="-68"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="5326" x2="3116" y1="-68" y2="-68"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="3117" x2="3117" y1="-68" y2="-1295"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="3478" x2="3478" y1="-1290" y2="-63"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(213,0,0)" stroke-width="1" x1="3479" x2="5327" y1="-160" y2="-160"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(213,0,0)" stroke-width="1" width="361" x="3116" y="-1198"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(213,0,0)" stroke-width="1" width="360" x="3118" y="-1079"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-120889">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 -885.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22527" ObjectName="SW-WD_GQ.WD_GQ_3011SW"/>
     <cge:Meas_Ref ObjectId="120889"/>
    <cge:TPSR_Ref TObjectID="22527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120915">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 -603.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22533" ObjectName="SW-WD_GQ.WD_GQ_0016SW"/>
     <cge:Meas_Ref ObjectId="120915"/>
    <cge:TPSR_Ref TObjectID="22533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120916">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 -516.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22534" ObjectName="SW-WD_GQ.WD_GQ_0011SW"/>
     <cge:Meas_Ref ObjectId="120916"/>
    <cge:TPSR_Ref TObjectID="22534"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120905">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4183.000000 -884.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22529" ObjectName="SW-WD_GQ.WD_GQ_3901SW"/>
     <cge:Meas_Ref ObjectId="120905"/>
    <cge:TPSR_Ref TObjectID="22529"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120960">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3704.000000 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22540" ObjectName="SW-WD_GQ.WD_GQ_0612SW"/>
     <cge:Meas_Ref ObjectId="120960"/>
    <cge:TPSR_Ref TObjectID="22540"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120962">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3704.000000 -254.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22542" ObjectName="SW-WD_GQ.WD_GQ_0616SW"/>
     <cge:Meas_Ref ObjectId="120962"/>
    <cge:TPSR_Ref TObjectID="22542"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120961">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3704.000000 -453.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22541" ObjectName="SW-WD_GQ.WD_GQ_0611SW"/>
     <cge:Meas_Ref ObjectId="120961"/>
    <cge:TPSR_Ref TObjectID="22541"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121066">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4952.000000 -356.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22564" ObjectName="SW-WD_GQ.WD_GQ_0676SW"/>
     <cge:Meas_Ref ObjectId="121066"/>
    <cge:TPSR_Ref TObjectID="22564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121070">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4952.000000 -450.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22566" ObjectName="SW-WD_GQ.WD_GQ_0671SW"/>
     <cge:Meas_Ref ObjectId="121070"/>
    <cge:TPSR_Ref TObjectID="22566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120906">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5160.000000 -450.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22530" ObjectName="SW-WD_GQ.WD_GQ_0901SW"/>
     <cge:Meas_Ref ObjectId="120906"/>
    <cge:TPSR_Ref TObjectID="22530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120928">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4952.000000 -886.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22536" ObjectName="SW-WD_GQ.WD_GQ_3021SW"/>
     <cge:Meas_Ref ObjectId="120928"/>
    <cge:TPSR_Ref TObjectID="22536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120948">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4952.000000 -604.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22538" ObjectName="SW-WD_GQ.WD_GQ_0026SW"/>
     <cge:Meas_Ref ObjectId="120948"/>
    <cge:TPSR_Ref TObjectID="22538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120949">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4952.000000 -517.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22539" ObjectName="SW-WD_GQ.WD_GQ_0021SW"/>
     <cge:Meas_Ref ObjectId="120949"/>
    <cge:TPSR_Ref TObjectID="22539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120978">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 -358.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22544" ObjectName="SW-WD_GQ.WD_GQ_0622SW"/>
     <cge:Meas_Ref ObjectId="120978"/>
    <cge:TPSR_Ref TObjectID="22544"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120980">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 -253.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22546" ObjectName="SW-WD_GQ.WD_GQ_0626SW"/>
     <cge:Meas_Ref ObjectId="120980"/>
    <cge:TPSR_Ref TObjectID="22546"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120979">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 -452.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22545" ObjectName="SW-WD_GQ.WD_GQ_0621SW"/>
     <cge:Meas_Ref ObjectId="120979"/>
    <cge:TPSR_Ref TObjectID="22545"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120996">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4120.000000 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22548" ObjectName="SW-WD_GQ.WD_GQ_0632SW"/>
     <cge:Meas_Ref ObjectId="120996"/>
    <cge:TPSR_Ref TObjectID="22548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120998">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4120.000000 -254.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22550" ObjectName="SW-WD_GQ.WD_GQ_0636SW"/>
     <cge:Meas_Ref ObjectId="120998"/>
    <cge:TPSR_Ref TObjectID="22550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120997">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4120.000000 -453.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22549" ObjectName="SW-WD_GQ.WD_GQ_0631SW"/>
     <cge:Meas_Ref ObjectId="120997"/>
    <cge:TPSR_Ref TObjectID="22549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121014">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4328.000000 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22552" ObjectName="SW-WD_GQ.WD_GQ_0642SW"/>
     <cge:Meas_Ref ObjectId="121014"/>
    <cge:TPSR_Ref TObjectID="22552"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121016">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4328.000000 -254.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22554" ObjectName="SW-WD_GQ.WD_GQ_0646SW"/>
     <cge:Meas_Ref ObjectId="121016"/>
    <cge:TPSR_Ref TObjectID="22554"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121015">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4328.000000 -451.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22553" ObjectName="SW-WD_GQ.WD_GQ_0641SW"/>
     <cge:Meas_Ref ObjectId="121015"/>
    <cge:TPSR_Ref TObjectID="22553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121032">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4536.000000 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22556" ObjectName="SW-WD_GQ.WD_GQ_0652SW"/>
     <cge:Meas_Ref ObjectId="121032"/>
    <cge:TPSR_Ref TObjectID="22556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121034">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4536.000000 -254.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22558" ObjectName="SW-WD_GQ.WD_GQ_0656SW"/>
     <cge:Meas_Ref ObjectId="121034"/>
    <cge:TPSR_Ref TObjectID="22558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121033">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4536.000000 -451.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22557" ObjectName="SW-WD_GQ.WD_GQ_0651SW"/>
     <cge:Meas_Ref ObjectId="121033"/>
    <cge:TPSR_Ref TObjectID="22557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121050">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4744.000000 -358.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22560" ObjectName="SW-WD_GQ.WD_GQ_0662SW"/>
     <cge:Meas_Ref ObjectId="121050"/>
    <cge:TPSR_Ref TObjectID="22560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121052">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4744.000000 -253.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22562" ObjectName="SW-WD_GQ.WD_GQ_0666SW"/>
     <cge:Meas_Ref ObjectId="121052"/>
    <cge:TPSR_Ref TObjectID="22562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121051">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4744.000000 -450.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22561" ObjectName="SW-WD_GQ.WD_GQ_0661SW"/>
     <cge:Meas_Ref ObjectId="121051"/>
    <cge:TPSR_Ref TObjectID="22561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120904">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4503.000000 -884.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22528" ObjectName="SW-WD_GQ.WD_GQ_3902SW"/>
     <cge:Meas_Ref ObjectId="120904"/>
    <cge:TPSR_Ref TObjectID="22528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120908">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4439.000000 -997.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22531" ObjectName="SW-WD_GQ.WD_GQ_3616SW"/>
     <cge:Meas_Ref ObjectId="120908"/>
    <cge:TPSR_Ref TObjectID="22531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120885">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4360.000000 -998.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22525" ObjectName="SW-WD_GQ.WD_GQ_36167SW"/>
     <cge:Meas_Ref ObjectId="120885"/>
    <cge:TPSR_Ref TObjectID="22525"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-WD_GQ.WD_GQ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3834,-941 5090,-941 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="22523" ObjectName="BS-WD_GQ.WD_GQ_3IM"/>
    <cge:TPSR_Ref TObjectID="22523"/></metadata>
   <polyline fill="none" opacity="0" points="3834,-941 5090,-941 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-WD_GQ.WD_GQ_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3584,-508 5327,-508 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="22524" ObjectName="BS-WD_GQ.WD_GQ_9IM"/>
    <cge:TPSR_Ref TObjectID="22524"/></metadata>
   <polyline fill="none" opacity="0" points="3584,-508 5327,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3841,-1062 3999,-1062 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3841,-1062 3999,-1062 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4337,-1242 4558,-1242 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4337,-1242 4558,-1242 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3704.000000 -184.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 -183.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4120.000000 -184.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4328.000000 -184.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4536.000000 -184.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4744.000000 -183.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_19b65d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4363.000000 -970.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_17d1610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-926 3921,-940 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22527@1" ObjectIDZND0="22523@0" Pin0InfoVect0LinkObjId="g_19e85d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120889_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-926 3921,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_17d1870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-874 3921,-890 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22526@1" ObjectIDZND0="22527@0" Pin0InfoVect0LinkObjId="SW-120889_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120888_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-874 3921,-890 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18b9aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-608 3921,-596 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22533@0" ObjectIDZND0="22532@1" Pin0InfoVect0LinkObjId="SW-120914_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120915_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-608 3921,-596 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_181ccc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-569 3921,-557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22532@0" ObjectIDZND0="22534@1" Pin0InfoVect0LinkObjId="SW-120916_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120914_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-569 3921,-557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_181cf20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-521 3921,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22534@0" ObjectIDZND0="22524@0" Pin0InfoVect0LinkObjId="g_18be850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120916_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-521 3921,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19c3f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-847 3921,-812 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="22526@0" ObjectIDZND0="22567@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120888_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-847 3921,-812 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19c41a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-720 3921,-644 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="22567@1" ObjectIDZND0="22533@1" Pin0InfoVect0LinkObjId="SW-120915_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19c3f40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-720 3921,-644 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_180e560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-810 4152,-876 4192,-876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" ObjectIDND0="g_19c4400@0" ObjectIDZND0="g_1c438e0@0" ObjectIDZND1="22529@x" Pin0InfoVect0LinkObjId="g_1c438e0_0" Pin0InfoVect1LinkObjId="SW-120905_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19c4400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-810 4152,-876 4192,-876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18be850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3713,-494 3713,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22541@1" ObjectIDZND0="22524@0" Pin0InfoVect0LinkObjId="g_181cf20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120961_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3713,-494 3713,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18c0a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3681,-236 3681,-248 3713,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_18bf7e0@0" ObjectIDZND0="22542@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-120962_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18bf7e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3681,-236 3681,-248 3713,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18c0bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3747,-342 3747,-355 3714,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_18beab0@0" ObjectIDZND0="22542@x" ObjectIDZND1="22540@x" Pin0InfoVect0LinkObjId="SW-120962_0" Pin0InfoVect1LinkObjId="SW-120960_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18beab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3747,-342 3747,-355 3714,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18c0de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3713,-400 3713,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22540@1" ObjectIDZND0="22543@0" Pin0InfoVect0LinkObjId="SW-120963_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120960_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3713,-400 3713,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19f4090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3713,-211 3713,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_18bf7e0@0" ObjectIDZND1="22542@x" Pin0InfoVect0LinkObjId="g_18bf7e0_0" Pin0InfoVect1LinkObjId="SW-120962_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3713,-211 3713,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19f42f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3713,-248 3713,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_18bf7e0@0" ObjectIDND1="0@x" ObjectIDZND0="22542@0" Pin0InfoVect0LinkObjId="SW-120962_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_18bf7e0_0" Pin1InfoVect1LinkObjId="EC-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3713,-248 3713,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18d6b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-491 4961,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22566@1" ObjectIDZND0="22524@0" Pin0InfoVect0LinkObjId="g_181cf20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121070_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4961,-491 4961,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18bb790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-397 4961,-408 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22564@1" ObjectIDZND0="22565@0" Pin0InfoVect0LinkObjId="SW-121069_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121066_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4961,-397 4961,-408 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17d07d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5129,-365 5129,-437 5169,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="g_17cd620@0" ObjectIDZND0="22530@x" ObjectIDZND1="g_1c41e70@0" Pin0InfoVect0LinkObjId="SW-120906_0" Pin0InfoVect1LinkObjId="g_1c41e70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17cd620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5129,-365 5129,-437 5169,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19e85d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-927 4961,-940 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22536@1" ObjectIDZND0="22523@0" Pin0InfoVect0LinkObjId="g_17d1610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120928_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4961,-927 4961,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19e8830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-875 4961,-891 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22535@1" ObjectIDZND0="22536@0" Pin0InfoVect0LinkObjId="SW-120928_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120927_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4961,-875 4961,-891 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17c8590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-609 4961,-597 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22538@0" ObjectIDZND0="22537@1" Pin0InfoVect0LinkObjId="SW-120947_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120948_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4961,-609 4961,-597 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1826870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-570 4961,-558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22537@0" ObjectIDZND0="22539@1" Pin0InfoVect0LinkObjId="SW-120949_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120947_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4961,-570 4961,-558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1826ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-522 4961,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22539@0" ObjectIDZND0="22524@0" Pin0InfoVect0LinkObjId="g_181cf20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120949_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4961,-522 4961,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c68d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-848 4961,-813 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="22535@0" ObjectIDZND0="22568@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120927_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4961,-848 4961,-813 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c68fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-721 4961,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="22568@1" ObjectIDZND0="22538@1" Pin0InfoVect0LinkObjId="SW-120948_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c68d60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4961,-721 4961,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c6a730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3713,-438 3713,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22543@1" ObjectIDZND0="22541@0" Pin0InfoVect0LinkObjId="SW-120961_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120963_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3713,-438 3713,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c6b230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4234,-798 4234,-876 4192,-876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1c438e0@0" ObjectIDZND0="g_19c4400@0" ObjectIDZND1="22529@x" Pin0InfoVect0LinkObjId="g_19c4400_0" Pin0InfoVect1LinkObjId="SW-120905_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c438e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4234,-798 4234,-876 4192,-876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c6bcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3713,-295 3713,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22542@1" ObjectIDZND0="g_18beab0@0" ObjectIDZND1="22540@x" Pin0InfoVect0LinkObjId="g_18beab0_0" Pin0InfoVect1LinkObjId="SW-120960_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120962_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3713,-295 3713,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c6bf20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3713,-355 3713,-364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_18beab0@0" ObjectIDND1="22542@x" ObjectIDZND0="22540@0" Pin0InfoVect0LinkObjId="SW-120960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_18beab0_0" Pin1InfoVect1LinkObjId="SW-120962_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3713,-355 3713,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18b6d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-493 3921,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22545@1" ObjectIDZND0="22524@0" Pin0InfoVect0LinkObjId="g_181cf20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120979_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-493 3921,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17e4a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-235 3889,-247 3921,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_17e38b0@0" ObjectIDZND0="22546@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-120980_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17e38b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-235 3889,-247 3921,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17e4c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3955,-341 3955,-354 3922,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_18b6fe0@0" ObjectIDZND0="22546@x" ObjectIDZND1="22544@x" Pin0InfoVect0LinkObjId="SW-120980_0" Pin0InfoVect1LinkObjId="SW-120978_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_18b6fe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3955,-341 3955,-354 3922,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17e4e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-399 3921,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22544@1" ObjectIDZND0="22547@0" Pin0InfoVect0LinkObjId="SW-120981_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120978_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-399 3921,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1828ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-210 3921,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_17e38b0@0" ObjectIDZND1="22546@x" Pin0InfoVect0LinkObjId="g_17e38b0_0" Pin0InfoVect1LinkObjId="SW-120980_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-210 3921,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1829150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-247 3921,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_17e38b0@0" ObjectIDND1="0@x" ObjectIDZND0="22546@0" Pin0InfoVect0LinkObjId="SW-120980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_17e38b0_0" Pin1InfoVect1LinkObjId="EC-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-247 3921,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18293b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-437 3921,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22547@1" ObjectIDZND0="22545@0" Pin0InfoVect0LinkObjId="SW-120979_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120981_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-437 3921,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1829610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-294 3921,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22546@1" ObjectIDZND0="g_18b6fe0@0" ObjectIDZND1="22544@x" Pin0InfoVect0LinkObjId="g_18b6fe0_0" Pin0InfoVect1LinkObjId="SW-120978_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120980_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-294 3921,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1829870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-354 3921,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_18b6fe0@0" ObjectIDND1="22546@x" ObjectIDZND0="22544@0" Pin0InfoVect0LinkObjId="SW-120978_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_18b6fe0_0" Pin1InfoVect1LinkObjId="SW-120980_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3921,-354 3921,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18119a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-494 4129,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22549@1" ObjectIDZND0="22524@0" Pin0InfoVect0LinkObjId="g_181cf20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120997_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-494 4129,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1813b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4097,-236 4097,-248 4129,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1812930@0" ObjectIDZND0="22550@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-120998_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1812930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4097,-236 4097,-248 4129,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1813d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4163,-342 4163,-355 4130,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1811c00@0" ObjectIDZND0="22550@x" ObjectIDZND1="22548@x" Pin0InfoVect0LinkObjId="SW-120998_0" Pin0InfoVect1LinkObjId="SW-120996_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1811c00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4163,-342 4163,-355 4130,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1813f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-400 4129,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22548@1" ObjectIDZND0="22551@0" Pin0InfoVect0LinkObjId="SW-120999_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120996_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-400 4129,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19289b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-211 4129,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_1812930@0" ObjectIDZND1="22550@x" Pin0InfoVect0LinkObjId="g_1812930_0" Pin0InfoVect1LinkObjId="SW-120998_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-211 4129,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1928c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-248 4129,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_1812930@0" ObjectIDND1="0@x" ObjectIDZND0="22550@0" Pin0InfoVect0LinkObjId="SW-120998_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1812930_0" Pin1InfoVect1LinkObjId="EC-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-248 4129,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1928e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-438 4129,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22551@1" ObjectIDZND0="22549@0" Pin0InfoVect0LinkObjId="SW-120997_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120999_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-438 4129,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19290d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-295 4129,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22550@1" ObjectIDZND0="g_1811c00@0" ObjectIDZND1="22548@x" Pin0InfoVect0LinkObjId="g_1811c00_0" Pin0InfoVect1LinkObjId="SW-120996_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120998_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-295 4129,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1929330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-355 4129,-364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="22550@x" ObjectIDND1="g_1811c00@0" ObjectIDZND0="22548@0" Pin0InfoVect0LinkObjId="SW-120996_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120998_0" Pin1InfoVect1LinkObjId="g_1811c00_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-355 4129,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1817c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-236 4305,-248 4337,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1816a70@0" ObjectIDZND0="22554@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-121016_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1816a70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4305,-236 4305,-248 4337,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1817e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4371,-342 4371,-355 4338,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1815d40@0" ObjectIDZND0="22554@x" ObjectIDZND1="22552@x" Pin0InfoVect0LinkObjId="SW-121016_0" Pin0InfoVect1LinkObjId="SW-121014_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1815d40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4371,-342 4371,-355 4338,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1818070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4337,-400 4337,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22552@1" ObjectIDZND0="22555@0" Pin0InfoVect0LinkObjId="SW-121017_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121014_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4337,-400 4337,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_181a900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4337,-211 4337,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_1816a70@0" ObjectIDZND1="22554@x" Pin0InfoVect0LinkObjId="g_1816a70_0" Pin0InfoVect1LinkObjId="SW-121016_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4337,-211 4337,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_181ab60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4337,-248 4337,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_1816a70@0" ObjectIDND1="0@x" ObjectIDZND0="22554@0" Pin0InfoVect0LinkObjId="SW-121016_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1816a70_0" Pin1InfoVect1LinkObjId="EC-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4337,-248 4337,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_181adc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4337,-438 4337,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22555@1" ObjectIDZND0="22553@0" Pin0InfoVect0LinkObjId="SW-121015_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121017_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4337,-438 4337,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_181b020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4337,-295 4337,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22554@1" ObjectIDZND0="g_1815d40@0" ObjectIDZND1="22552@x" Pin0InfoVect0LinkObjId="g_1815d40_0" Pin0InfoVect1LinkObjId="SW-121014_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121016_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4337,-295 4337,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_181b280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4337,-355 4337,-364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1815d40@0" ObjectIDND1="22554@x" ObjectIDZND0="22552@0" Pin0InfoVect0LinkObjId="SW-121014_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1815d40_0" Pin1InfoVect1LinkObjId="SW-121016_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4337,-355 4337,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17ddc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4337,-492 4337,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22553@1" ObjectIDZND0="22524@0" Pin0InfoVect0LinkObjId="g_181cf20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121015_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4337,-492 4337,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19c1760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4513,-236 4513,-248 4545,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_19c0540@0" ObjectIDZND0="22558@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-121034_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19c0540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4513,-236 4513,-248 4545,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19c1950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4579,-342 4579,-355 4546,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_19bf810@0" ObjectIDZND0="22558@x" ObjectIDZND1="22556@x" Pin0InfoVect0LinkObjId="SW-121034_0" Pin0InfoVect1LinkObjId="SW-121032_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19bf810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4579,-342 4579,-355 4546,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19c1b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-400 4545,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22556@1" ObjectIDZND0="22559@0" Pin0InfoVect0LinkObjId="SW-121035_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121032_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-400 4545,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17f87d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-211 4545,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="22558@x" ObjectIDZND1="g_19c0540@0" Pin0InfoVect0LinkObjId="SW-121034_0" Pin0InfoVect1LinkObjId="g_19c0540_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-211 4545,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17f8a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-248 4545,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_19c0540@0" ObjectIDND1="0@x" ObjectIDZND0="22558@0" Pin0InfoVect0LinkObjId="SW-121034_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_19c0540_0" Pin1InfoVect1LinkObjId="EC-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-248 4545,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17f8c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-438 4545,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22559@1" ObjectIDZND0="22557@0" Pin0InfoVect0LinkObjId="SW-121033_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121035_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-438 4545,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17f8ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-295 4545,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22558@1" ObjectIDZND0="g_19bf810@0" ObjectIDZND1="22556@x" Pin0InfoVect0LinkObjId="g_19bf810_0" Pin0InfoVect1LinkObjId="SW-121032_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121034_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-295 4545,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17f9150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-355 4545,-364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="22558@x" ObjectIDND1="g_19bf810@0" ObjectIDZND0="22556@0" Pin0InfoVect0LinkObjId="SW-121032_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-121034_0" Pin1InfoVect1LinkObjId="g_19bf810_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-355 4545,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17f93b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-492 4545,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22557@1" ObjectIDZND0="22524@0" Pin0InfoVect0LinkObjId="g_181cf20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121033_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-492 4545,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17da110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4721,-235 4721,-247 4753,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_17d8ef0@0" ObjectIDZND0="22562@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-121052_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17d8ef0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4721,-235 4721,-247 4753,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17da300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4787,-341 4787,-354 4754,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_17d81c0@0" ObjectIDZND0="22562@x" ObjectIDZND1="22560@x" Pin0InfoVect0LinkObjId="SW-121052_0" Pin0InfoVect1LinkObjId="SW-121050_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17d81c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4787,-341 4787,-354 4754,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17e7b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4753,-399 4753,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22560@1" ObjectIDZND0="22563@0" Pin0InfoVect0LinkObjId="SW-121053_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121050_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4753,-399 4753,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17ea410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4753,-210 4753,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_17d8ef0@0" ObjectIDZND1="22562@x" Pin0InfoVect0LinkObjId="g_17d8ef0_0" Pin0InfoVect1LinkObjId="SW-121052_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4753,-210 4753,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17ea670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4753,-247 4753,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_17d8ef0@0" ObjectIDND1="0@x" ObjectIDZND0="22562@0" Pin0InfoVect0LinkObjId="SW-121052_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_17d8ef0_0" Pin1InfoVect1LinkObjId="EC-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4753,-247 4753,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17ea8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4753,-437 4753,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22563@1" ObjectIDZND0="22561@0" Pin0InfoVect0LinkObjId="SW-121051_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121053_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4753,-437 4753,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17eab30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4753,-294 4753,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="22562@1" ObjectIDZND0="g_17d81c0@0" ObjectIDZND1="22560@x" Pin0InfoVect0LinkObjId="g_17d81c0_0" Pin0InfoVect1LinkObjId="SW-121050_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121052_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4753,-294 4753,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17ead90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4753,-354 4753,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="22562@x" ObjectIDND1="g_17d81c0@0" ObjectIDZND0="22560@0" Pin0InfoVect0LinkObjId="SW-121050_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-121052_0" Pin1InfoVect1LinkObjId="g_17d81c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4753,-354 4753,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17eaff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4753,-491 4753,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22561@1" ObjectIDZND0="22524@0" Pin0InfoVect0LinkObjId="g_181cf20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121051_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4753,-491 4753,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17ed4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-268 4961,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="22564@0" Pin0InfoVect0LinkObjId="SW-121066_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4961,-268 4961,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17ed740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-455 4961,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="22566@0" ObjectIDZND0="22565@1" Pin0InfoVect0LinkObjId="SW-121069_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4961,-455 4961,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c40ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5169,-437 5169,-455 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_17cd620@0" ObjectIDND1="g_1c41e70@0" ObjectIDZND0="22530@0" Pin0InfoVect0LinkObjId="SW-120906_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_17cd620_0" Pin1InfoVect1LinkObjId="g_1c41e70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5169,-437 5169,-455 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c41120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5169,-491 5169,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22530@1" ObjectIDZND0="22524@0" Pin0InfoVect0LinkObjId="g_181cf20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120906_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5169,-491 5169,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c41c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5205,-355 5205,-437 5169,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1c41e70@0" ObjectIDZND0="g_17cd620@0" ObjectIDZND1="22530@x" Pin0InfoVect0LinkObjId="g_17cd620_0" Pin0InfoVect1LinkObjId="SW-120906_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c41e70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5205,-355 5205,-437 5169,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c45350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4192,-876 4192,-889 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_19c4400@0" ObjectIDND1="g_1c438e0@0" ObjectIDZND0="22529@0" Pin0InfoVect0LinkObjId="SW-120905_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_19c4400_0" Pin1InfoVect1LinkObjId="g_1c438e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4192,-876 4192,-889 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c455b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4192,-925 4192,-940 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22529@1" ObjectIDZND0="22523@0" Pin0InfoVect0LinkObjId="g_17d1610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120905_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4192,-925 4192,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c485e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-838 4512,-889 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="22528@0" Pin0InfoVect0LinkObjId="SW-120904_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-838 4512,-889 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c48840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-925 4512,-940 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22528@1" ObjectIDZND0="22523@0" Pin0InfoVect0LinkObjId="g_17d1610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120904_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-925 4512,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19b30e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4448,-940 4448,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22523@0" ObjectIDZND0="22531@0" Pin0InfoVect0LinkObjId="SW-120908_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17d1610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4448,-940 4448,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19b6110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4448,-1069 4369,-1069 4369,-1039 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="22531@x" ObjectIDND1="g_19b7280@0" ObjectIDND2="0@x" ObjectIDZND0="22525@1" Pin0InfoVect0LinkObjId="SW-120885_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-120908_0" Pin1InfoVect1LinkObjId="g_19b7280_0" Pin1InfoVect2LinkObjId="EC-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4448,-1069 4369,-1069 4369,-1039 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19b6370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4369,-1003 4369,-988 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22525@0" ObjectIDZND0="g_19b65d0@0" Pin0InfoVect0LinkObjId="g_19b65d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120885_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4369,-1003 4369,-988 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19b7020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4449,-1103 4502,-1103 4502,-1090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="22525@x" ObjectIDZND0="g_19b7280@0" Pin0InfoVect0LinkObjId="g_19b7280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="EC-0_0" Pin1InfoVect2LinkObjId="SW-120885_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4449,-1103 4502,-1103 4502,-1090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ba8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4448,-1157 3920,-1157 3920,-1116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_19b7280@0" ObjectIDND2="22525@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="EC-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="g_19b7280_0" Pin1InfoVect2LinkObjId="SW-120885_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4448,-1157 3920,-1157 3920,-1116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19bab10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3920,-1089 3920,-1062 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3920,-1089 3920,-1062 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19c72e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4448,-1157 4448,-1189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_19b7280@0" ObjectIDND2="22525@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="g_19b7280_0" Pin1InfoVect2LinkObjId="SW-120885_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4448,-1157 4448,-1189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19c7540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4448,-1216 4448,-1242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4448,-1216 4448,-1242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19c95d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4448,-1103 4448,-1157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" EndDevType1="breaker" ObjectIDND0="g_19b7280@0" ObjectIDND1="22525@x" ObjectIDND2="22531@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_19b7280_0" Pin1InfoVect1LinkObjId="SW-120885_0" Pin1InfoVect2LinkObjId="SW-120908_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4448,-1103 4448,-1157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19c97f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4448,-1038 4448,-1069 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="22531@1" ObjectIDZND0="22525@x" ObjectIDZND1="g_19b7280@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-120885_0" Pin0InfoVect1LinkObjId="g_19b7280_0" Pin0InfoVect2LinkObjId="EC-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-120908_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4448,-1038 4448,-1069 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19c9a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4448,-1069 4448,-1103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="breaker" ObjectIDND0="22525@x" ObjectIDND1="22531@x" ObjectIDZND0="g_19b7280@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_19b7280_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="EC-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-120885_0" Pin1InfoVect1LinkObjId="SW-120908_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4448,-1069 4448,-1103 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="22524" cx="3921" cy="-508" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22524" cx="3713" cy="-508" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22523" cx="3921" cy="-940" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22524" cx="3921" cy="-508" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22524" cx="4129" cy="-508" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22524" cx="4337" cy="-508" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22524" cx="4545" cy="-508" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22524" cx="4753" cy="-508" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22523" cx="4961" cy="-940" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22524" cx="4961" cy="-508" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22524" cx="4961" cy="-508" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22524" cx="5169" cy="-508" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22523" cx="4192" cy="-940" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22523" cx="4512" cy="-940" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22523" cx="4448" cy="-940" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3920" cy="-1062" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4448" cy="-1242" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" id="DYN-119711" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3416.500000 -1084.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22308" ObjectName="DYN-WD_GQ"/>
     <cge:Meas_Ref ObjectId="119711"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18c7b30" transform="matrix(1.000000 0.000000 0.000000 1.000000 3162.000000 -1028.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18c7b30" transform="matrix(1.000000 0.000000 0.000000 1.000000 3162.000000 -1028.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18c7b30" transform="matrix(1.000000 0.000000 0.000000 1.000000 3162.000000 -1028.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18c7b30" transform="matrix(1.000000 0.000000 0.000000 1.000000 3162.000000 -1028.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18c7b30" transform="matrix(1.000000 0.000000 0.000000 1.000000 3162.000000 -1028.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18c7b30" transform="matrix(1.000000 0.000000 0.000000 1.000000 3162.000000 -1028.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_18c7b30" transform="matrix(1.000000 0.000000 0.000000 1.000000 3162.000000 -1028.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c80b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3163.000000 -589.000000) translate(0,15)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c80b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3163.000000 -589.000000) translate(0,33)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c80b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3163.000000 -589.000000) translate(0,51)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c80b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3163.000000 -589.000000) translate(0,69)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c80b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3163.000000 -589.000000) translate(0,87)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c80b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3163.000000 -589.000000) translate(0,105)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c80b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3163.000000 -589.000000) translate(0,123)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c80b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3163.000000 -589.000000) translate(0,141)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c80b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3163.000000 -589.000000) translate(0,159)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c80b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3163.000000 -589.000000) translate(0,177)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c80b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3163.000000 -589.000000) translate(0,195)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c80b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3163.000000 -589.000000) translate(0,213)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c80b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3163.000000 -589.000000) translate(0,231)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c80b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3163.000000 -589.000000) translate(0,249)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c80b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3163.000000 -589.000000) translate(0,267)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c80b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3163.000000 -589.000000) translate(0,285)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c80b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3163.000000 -589.000000) translate(0,303)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_17db580" transform="matrix(1.000000 0.000000 0.000000 1.000000 3275.000000 -1167.500000) translate(0,16)">高桥变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17dc120" transform="matrix(1.000000 0.000000 0.000000 1.000000 3713.000000 -812.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17dc120" transform="matrix(1.000000 0.000000 0.000000 1.000000 3713.000000 -812.000000) translate(0,33)">SZ11-2000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17dc120" transform="matrix(1.000000 0.000000 0.000000 1.000000 3713.000000 -812.000000) translate(0,51)">2000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17dc120" transform="matrix(1.000000 0.000000 0.000000 1.000000 3713.000000 -812.000000) translate(0,69)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17dc120" transform="matrix(1.000000 0.000000 0.000000 1.000000 3713.000000 -812.000000) translate(0,87)">Ud=7.05%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c0510" transform="matrix(0.987013 0.000000 0.000000 0.938462 3723.142857 -220.800000) translate(0,15)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18c0510" transform="matrix(0.987013 0.000000 0.000000 0.938462 3723.142857 -220.800000) translate(0,33)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18be050" transform="matrix(0.987013 0.000000 0.000000 0.938462 4939.142857 -236.800000) translate(0,15)">电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c69220" transform="matrix(1.000000 0.000000 0.000000 1.000000 4753.000000 -812.000000) translate(0,15)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c69220" transform="matrix(1.000000 0.000000 0.000000 1.000000 4753.000000 -812.000000) translate(0,33)">SZ9-3150/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c69220" transform="matrix(1.000000 0.000000 0.000000 1.000000 4753.000000 -812.000000) translate(0,51)">3150kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c69220" transform="matrix(1.000000 0.000000 0.000000 1.000000 4753.000000 -812.000000) translate(0,69)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c69220" transform="matrix(1.000000 0.000000 0.000000 1.000000 4753.000000 -812.000000) translate(0,87)">Ud=7.38%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17e4590" transform="matrix(0.987013 0.000000 0.000000 0.938462 3938.142857 -229.300000) translate(0,15)">机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17e4590" transform="matrix(0.987013 0.000000 0.000000 0.938462 3938.142857 -229.300000) translate(0,33)">关</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17e4590" transform="matrix(0.987013 0.000000 0.000000 0.938462 3938.142857 -229.300000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1813660" transform="matrix(0.987013 0.000000 0.000000 0.938462 4146.142857 -229.300000) translate(0,15)">马</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1813660" transform="matrix(0.987013 0.000000 0.000000 0.938462 4146.142857 -229.300000) translate(0,33)">鞍</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1813660" transform="matrix(0.987013 0.000000 0.000000 0.938462 4146.142857 -229.300000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18177a0" transform="matrix(0.987013 0.000000 0.000000 0.938462 4355.142857 -229.300000) translate(0,15)">花</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18177a0" transform="matrix(0.987013 0.000000 0.000000 0.938462 4355.142857 -229.300000) translate(0,33)">桥</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18177a0" transform="matrix(0.987013 0.000000 0.000000 0.938462 4355.142857 -229.300000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19c1270" transform="matrix(0.987013 0.000000 0.000000 0.938462 4564.142857 -229.300000) translate(0,15)">勒</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19c1270" transform="matrix(0.987013 0.000000 0.000000 0.938462 4564.142857 -229.300000) translate(0,33)">外</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19c1270" transform="matrix(0.987013 0.000000 0.000000 0.938462 4564.142857 -229.300000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17d9c20" transform="matrix(0.987013 0.000000 0.000000 0.938462 4772.142857 -220.800000) translate(0,15)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17d9c20" transform="matrix(0.987013 0.000000 0.000000 0.938462 4772.142857 -220.800000) translate(0,33)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c4ae00" transform="matrix(0.987013 0.000000 0.000000 0.938462 4119.142857 -712.800000) translate(0,15)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19b09c0" transform="matrix(0.987013 0.000000 0.000000 0.938462 4481.142857 -725.800000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19c7f70" transform="matrix(1.000000 0.000000 0.000000 1.000000 4403.000000 -1263.000000) translate(0,15)">大响水电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19c8460" transform="matrix(1.000000 0.000000 0.000000 1.000000 3873.000000 -1052.000000) translate(0,15)">35kV猫街变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19c9c70" transform="matrix(0.987013 0.000000 0.000000 0.938462 5127.142857 -270.800000) translate(0,15)">10kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19ca130" transform="matrix(1.000000 0.000000 0.000000 1.000000 5230.000000 -534.000000) translate(0,15)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19ca360" transform="matrix(1.000000 0.000000 0.000000 1.000000 4996.000000 -968.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19ca5a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4760.000000 -482.000000) translate(0,15)">0661</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cab20" transform="matrix(1.000000 0.000000 0.000000 1.000000 4760.000000 -390.000000) translate(0,15)">0662</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cada0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4760.000000 -285.000000) translate(0,15)">0666</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cafe0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4762.000000 -433.000000) translate(0,15)">066</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cb220" transform="matrix(1.000000 0.000000 0.000000 1.000000 3720.000000 -485.000000) translate(0,15)">0611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cb460" transform="matrix(1.000000 0.000000 0.000000 1.000000 3720.000000 -391.000000) translate(0,15)">0612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cb6a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3720.000000 -286.000000) translate(0,15)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cb8e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3722.000000 -434.000000) translate(0,15)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cbb20" transform="matrix(1.000000 0.000000 0.000000 1.000000 4968.000000 -482.000000) translate(0,15)">0671</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cbd60" transform="matrix(1.000000 0.000000 0.000000 1.000000 4968.000000 -388.000000) translate(0,15)">0676</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cbfa0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4970.000000 -431.000000) translate(0,15)">067</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cc1e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 5176.000000 -482.000000) translate(0,15)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cc420" transform="matrix(1.000000 0.000000 0.000000 1.000000 4344.000000 -483.000000) translate(0,15)">0641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cc950" transform="matrix(1.000000 0.000000 0.000000 1.000000 4344.000000 -391.000000) translate(0,15)">0642</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19ccc20" transform="matrix(1.000000 0.000000 0.000000 1.000000 4344.000000 -286.000000) translate(0,15)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cce60" transform="matrix(1.000000 0.000000 0.000000 1.000000 4346.000000 -434.000000) translate(0,15)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cd0a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3928.000000 -484.000000) translate(0,15)">0621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cd2e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3928.000000 -390.000000) translate(0,15)">0622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cd520" transform="matrix(1.000000 0.000000 0.000000 1.000000 3928.000000 -285.000000) translate(0,15)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cd760" transform="matrix(1.000000 0.000000 0.000000 1.000000 3930.000000 -433.000000) translate(0,15)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cd9a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4552.000000 -483.000000) translate(0,15)">0651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cdbe0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4552.000000 -391.000000) translate(0,15)">0652</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cde20" transform="matrix(1.000000 0.000000 0.000000 1.000000 4552.000000 -286.000000) translate(0,15)">0656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19ce060" transform="matrix(1.000000 0.000000 0.000000 1.000000 4554.000000 -434.000000) translate(0,15)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19ce2a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4136.000000 -485.000000) translate(0,15)">0631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19ce4e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4136.000000 -391.000000) translate(0,15)">0632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19ce720" transform="matrix(1.000000 0.000000 0.000000 1.000000 4136.000000 -286.000000) translate(0,15)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19ce960" transform="matrix(1.000000 0.000000 0.000000 1.000000 4138.000000 -434.000000) translate(0,15)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19ceba0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4519.000000 -916.000000) translate(0,15)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cede0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3928.000000 -548.000000) translate(0,15)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cf020" transform="matrix(1.000000 0.000000 0.000000 1.000000 3928.000000 -635.000000) translate(0,15)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cf260" transform="matrix(1.000000 0.000000 0.000000 1.000000 3930.000000 -592.000000) translate(0,15)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cf4a0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3928.000000 -917.000000) translate(0,15)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cf6e0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3930.000000 -870.000000) translate(0,15)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cf920" transform="matrix(1.000000 0.000000 0.000000 1.000000 4970.000000 -593.000000) translate(0,15)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cfb60" transform="matrix(1.000000 0.000000 0.000000 1.000000 4968.000000 -918.000000) translate(0,15)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1979250" transform="matrix(1.000000 0.000000 0.000000 1.000000 4970.000000 -871.000000) translate(0,15)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1979490" transform="matrix(1.000000 0.000000 0.000000 1.000000 4968.000000 -549.000000) translate(0,15)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19796d0" transform="matrix(1.000000 0.000000 0.000000 1.000000 4968.000000 -636.000000) translate(0,15)">0026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1979910" transform="matrix(1.000000 0.000000 0.000000 1.000000 4376.000000 -1030.000000) translate(0,15)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1979b50" transform="matrix(1.000000 0.000000 0.000000 1.000000 4455.000000 -1029.000000) translate(0,15)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1979d90" transform="matrix(1.000000 0.000000 0.000000 1.000000 4199.000000 -916.000000) translate(0,15)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1979fd0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3950.000000 -752.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_197a210" transform="matrix(1.000000 0.000000 0.000000 1.000000 4999.000000 -752.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_197f5c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3940.000000 -695.000000) translate(0,15)">YJV22-8.7/10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_197f5c0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3940.000000 -695.000000) translate(0,33)">3×95mm2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_197f820" transform="matrix(1.000000 0.000000 0.000000 1.000000 4975.000000 -695.000000) translate(0,15)">YJV22-8.7/10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_197f820" transform="matrix(1.000000 0.000000 0.000000 1.000000 4975.000000 -695.000000) translate(0,33)">3×95mm2</text>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-674 3914,-659 3929,-659 3921,-674 3921,-674 3921,-674 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-686 3914,-701 3929,-701 3921,-686 3921,-686 3921,-686 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3713,-320 3706,-305 3721,-305 3713,-320 3713,-320 3713,-320 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3713,-332 3706,-347 3721,-347 3713,-332 3713,-332 3713,-332 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-317 4954,-302 4969,-302 4961,-317 4961,-317 4961,-317 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-329 4954,-344 4969,-344 4961,-329 4961,-329 4961,-329 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-675 4954,-660 4969,-660 4961,-675 4961,-675 4961,-675 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4961,-687 4954,-702 4969,-702 4961,-687 4961,-687 4961,-687 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-319 3914,-304 3929,-304 3921,-319 3921,-319 3921,-319 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3921,-331 3914,-346 3929,-346 3921,-331 3921,-331 3921,-331 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-320 4122,-305 4137,-305 4129,-320 4129,-320 4129,-320 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-332 4122,-347 4137,-347 4129,-332 4129,-332 4129,-332 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4337,-320 4330,-305 4345,-305 4337,-320 4337,-320 4337,-320 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4337,-332 4330,-347 4345,-347 4337,-332 4337,-332 4337,-332 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-320 4538,-305 4553,-305 4545,-320 4545,-320 4545,-320 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-332 4538,-347 4553,-347 4545,-332 4545,-332 4545,-332 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4753,-319 4746,-304 4761,-304 4753,-319 4753,-319 4753,-319 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4753,-331 4746,-346 4761,-346 4753,-331 4753,-331 4753,-331 " stroke="rgb(0,255,0)"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1c41e70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5184.000000 -281.000000)" xlink:href="#voltageTransformer:shape93"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c438e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4213.000000 -724.000000)" xlink:href="#voltageTransformer:shape93"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-WD_GQ.WD_GQ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="31704"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3882.000000 -715.000000)" xlink:href="#transformer2:shape51_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3882.000000 -715.000000)" xlink:href="#transformer2:shape51_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="22567" ObjectName="TF-WD_GQ.WD_GQ_1T"/>
    <cge:TPSR_Ref TObjectID="22567"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-WD_GQ.WD_BL_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="31708"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4922.000000 -716.000000)" xlink:href="#transformer2:shape51_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4922.000000 -716.000000)" xlink:href="#transformer2:shape51_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="22568" ObjectName="TF-WD_GQ.WD_BL_2T"/>
    <cge:TPSR_Ref TObjectID="22568"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4480.000000 -744.000000)" xlink:href="#transformer2:shape66_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4480.000000 -744.000000)" xlink:href="#transformer2:shape66_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 0.000000 0.000000 2.335135 3215.000000 -1116.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 0.000000 0.000000 1.395515 3292.538462 -987.966362) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 0.000000 0.000000 1.395515 3292.538462 -945.966362) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-120829" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3815.000000 -684.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120829" ObjectName="WD_GQ:WD_GQ_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-121093" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4871.000000 -687.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121093" ObjectName="WD_GQ:WD_GQ_2T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-125932" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3815.000000 -700.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125932" ObjectName="WD_GQ:WD_GQ_1T_Tp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-125933" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4871.000000 -705.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125933" ObjectName="WD_GQ:WD_GQ_2Tap"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3235" y="-1178"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3235" y="-1178"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3186" y="-1195"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3186" y="-1195"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19ad940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3636.000000 151.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19ae380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3625.000000 136.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ca8ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3650.000000 121.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c69850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3599.000000 532.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c69cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3607.000000 547.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6a270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3607.000000 562.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c6a4f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3607.000000 576.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_197e6d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3694.000000 935.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_197e930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3702.000000 950.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_197eb70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3702.000000 965.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_197edb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3702.000000 979.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1980380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3745.000000 685.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19805e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3745.000000 701.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1981170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4797.000000 688.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19813d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4797.000000 704.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4925.000000 -244.000000)" xlink:href="#capacitor:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120825" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3973.000000 -885.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120825" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22526"/>
     <cge:Term_Ref ObjectID="31620"/>
    <cge:TPSR_Ref TObjectID="22526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120826" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3973.000000 -885.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120826" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22526"/>
     <cge:Term_Ref ObjectID="31620"/>
    <cge:TPSR_Ref TObjectID="22526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120823" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3973.000000 -885.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120823" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22526"/>
     <cge:Term_Ref ObjectID="31620"/>
    <cge:TPSR_Ref TObjectID="22526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120838" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3972.000000 -604.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120838" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22532"/>
     <cge:Term_Ref ObjectID="31632"/>
    <cge:TPSR_Ref TObjectID="22532"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-125918" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3972.000000 -604.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="125918" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22532"/>
     <cge:Term_Ref ObjectID="31632"/>
    <cge:TPSR_Ref TObjectID="22532"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120836" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3972.000000 -604.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120836" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22532"/>
     <cge:Term_Ref ObjectID="31632"/>
    <cge:TPSR_Ref TObjectID="22532"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120842" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5018.000000 -881.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120842" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22535"/>
     <cge:Term_Ref ObjectID="31638"/>
    <cge:TPSR_Ref TObjectID="22535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120843" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5018.000000 -881.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120843" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22535"/>
     <cge:Term_Ref ObjectID="31638"/>
    <cge:TPSR_Ref TObjectID="22535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120840" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5018.000000 -881.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120840" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22535"/>
     <cge:Term_Ref ObjectID="31638"/>
    <cge:TPSR_Ref TObjectID="22535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120848" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5018.000000 -603.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120848" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22537"/>
     <cge:Term_Ref ObjectID="31642"/>
    <cge:TPSR_Ref TObjectID="22537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120849" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5018.000000 -603.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120849" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22537"/>
     <cge:Term_Ref ObjectID="31642"/>
    <cge:TPSR_Ref TObjectID="22537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120846" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5018.000000 -603.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120846" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22537"/>
     <cge:Term_Ref ObjectID="31642"/>
    <cge:TPSR_Ref TObjectID="22537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-0" prefix="" rightAlign="0">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4950.000000 -148.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22565"/>
     <cge:Term_Ref ObjectID="31698"/>
    <cge:TPSR_Ref TObjectID="22565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120883" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4950.000000 -148.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120883" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22565"/>
     <cge:Term_Ref ObjectID="31698"/>
    <cge:TPSR_Ref TObjectID="22565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120881" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4950.000000 -148.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120881" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22565"/>
     <cge:Term_Ref ObjectID="31698"/>
    <cge:TPSR_Ref TObjectID="22565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120878" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4740.000000 -148.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120878" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22563"/>
     <cge:Term_Ref ObjectID="31694"/>
    <cge:TPSR_Ref TObjectID="22563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120879" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4740.000000 -148.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120879" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22563"/>
     <cge:Term_Ref ObjectID="31694"/>
    <cge:TPSR_Ref TObjectID="22563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120876" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4740.000000 -148.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120876" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22563"/>
     <cge:Term_Ref ObjectID="31694"/>
    <cge:TPSR_Ref TObjectID="22563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120873" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4532.000000 -148.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120873" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22559"/>
     <cge:Term_Ref ObjectID="31686"/>
    <cge:TPSR_Ref TObjectID="22559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120874" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4532.000000 -148.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120874" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22559"/>
     <cge:Term_Ref ObjectID="31686"/>
    <cge:TPSR_Ref TObjectID="22559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120871" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4532.000000 -148.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120871" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22559"/>
     <cge:Term_Ref ObjectID="31686"/>
    <cge:TPSR_Ref TObjectID="22559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120868" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4324.000000 -148.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120868" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22555"/>
     <cge:Term_Ref ObjectID="31678"/>
    <cge:TPSR_Ref TObjectID="22555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120869" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4324.000000 -148.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120869" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22555"/>
     <cge:Term_Ref ObjectID="31678"/>
    <cge:TPSR_Ref TObjectID="22555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120866" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4324.000000 -148.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120866" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22555"/>
     <cge:Term_Ref ObjectID="31678"/>
    <cge:TPSR_Ref TObjectID="22555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120863" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4116.000000 -148.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120863" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22551"/>
     <cge:Term_Ref ObjectID="31670"/>
    <cge:TPSR_Ref TObjectID="22551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120864" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4116.000000 -148.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120864" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22551"/>
     <cge:Term_Ref ObjectID="31670"/>
    <cge:TPSR_Ref TObjectID="22551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120861" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4116.000000 -148.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120861" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22551"/>
     <cge:Term_Ref ObjectID="31670"/>
    <cge:TPSR_Ref TObjectID="22551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120858" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3908.000000 -148.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120858" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22547"/>
     <cge:Term_Ref ObjectID="31662"/>
    <cge:TPSR_Ref TObjectID="22547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120859" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3908.000000 -148.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120859" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22547"/>
     <cge:Term_Ref ObjectID="31662"/>
    <cge:TPSR_Ref TObjectID="22547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120856" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3908.000000 -148.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120856" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22547"/>
     <cge:Term_Ref ObjectID="31662"/>
    <cge:TPSR_Ref TObjectID="22547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-120853" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3700.000000 -148.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120853" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22543"/>
     <cge:Term_Ref ObjectID="31654"/>
    <cge:TPSR_Ref TObjectID="22543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-120854" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3700.000000 -148.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120854" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22543"/>
     <cge:Term_Ref ObjectID="31654"/>
    <cge:TPSR_Ref TObjectID="22543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-120851" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3700.000000 -148.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120851" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22543"/>
     <cge:Term_Ref ObjectID="31654"/>
    <cge:TPSR_Ref TObjectID="22543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-120820" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3761.000000 -979.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120820" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22523"/>
     <cge:Term_Ref ObjectID="31616"/>
    <cge:TPSR_Ref TObjectID="22523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-120821" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3761.000000 -979.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120821" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22523"/>
     <cge:Term_Ref ObjectID="31616"/>
    <cge:TPSR_Ref TObjectID="22523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-120822" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3761.000000 -979.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120822" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22523"/>
     <cge:Term_Ref ObjectID="31616"/>
    <cge:TPSR_Ref TObjectID="22523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-120817" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3761.000000 -979.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120817" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22523"/>
     <cge:Term_Ref ObjectID="31616"/>
    <cge:TPSR_Ref TObjectID="22523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-120833" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3667.000000 -576.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120833" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22524"/>
     <cge:Term_Ref ObjectID="31617"/>
    <cge:TPSR_Ref TObjectID="22524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-120834" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3667.000000 -576.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120834" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22524"/>
     <cge:Term_Ref ObjectID="31617"/>
    <cge:TPSR_Ref TObjectID="22524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-120835" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3667.000000 -576.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120835" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22524"/>
     <cge:Term_Ref ObjectID="31617"/>
    <cge:TPSR_Ref TObjectID="22524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-120830" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3667.000000 -576.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="120830" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22524"/>
     <cge:Term_Ref ObjectID="31617"/>
    <cge:TPSR_Ref TObjectID="22524"/></metadata>
   </g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-120888">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 -839.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22526" ObjectName="SW-WD_GQ.WD_GQ_301BK"/>
     <cge:Meas_Ref ObjectId="120888"/>
    <cge:TPSR_Ref TObjectID="22526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120914">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 -561.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22532" ObjectName="SW-WD_GQ.WD_GQ_001BK"/>
     <cge:Meas_Ref ObjectId="120914"/>
    <cge:TPSR_Ref TObjectID="22532"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120963">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3704.000000 -403.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22543" ObjectName="SW-WD_GQ.WD_GQ_061BK"/>
     <cge:Meas_Ref ObjectId="120963"/>
    <cge:TPSR_Ref TObjectID="22543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121069">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4952.000000 -400.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22565" ObjectName="SW-WD_GQ.WD_GQ_067BK"/>
     <cge:Meas_Ref ObjectId="121069"/>
    <cge:TPSR_Ref TObjectID="22565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120927">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4952.000000 -840.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22535" ObjectName="SW-WD_GQ.WD_GQ_302BK"/>
     <cge:Meas_Ref ObjectId="120927"/>
    <cge:TPSR_Ref TObjectID="22535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120947">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4952.000000 -562.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22537" ObjectName="SW-WD_GQ.WD_GQ_002BK"/>
     <cge:Meas_Ref ObjectId="120947"/>
    <cge:TPSR_Ref TObjectID="22537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120981">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 -402.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22547" ObjectName="SW-WD_GQ.WD_GQ_062BK"/>
     <cge:Meas_Ref ObjectId="120981"/>
    <cge:TPSR_Ref TObjectID="22547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-120999">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4120.000000 -403.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22551" ObjectName="SW-WD_GQ.WD_GQ_063BK"/>
     <cge:Meas_Ref ObjectId="120999"/>
    <cge:TPSR_Ref TObjectID="22551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121017">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4328.000000 -403.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22555" ObjectName="SW-WD_GQ.WD_GQ_064BK"/>
     <cge:Meas_Ref ObjectId="121017"/>
    <cge:TPSR_Ref TObjectID="22555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121035">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4536.000000 -403.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22559" ObjectName="SW-WD_GQ.WD_GQ_065BK"/>
     <cge:Meas_Ref ObjectId="121035"/>
    <cge:TPSR_Ref TObjectID="22559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121053">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4744.000000 -402.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22563" ObjectName="SW-WD_GQ.WD_GQ_066BK"/>
     <cge:Meas_Ref ObjectId="121053"/>
    <cge:TPSR_Ref TObjectID="22563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3911.000000 -1081.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4439.000000 -1181.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="wd_索引_接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3235" y="-1178"/></g>
   <g href="wd_索引_接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3186" y="-1195"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_19c4400">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4145.000000 -756.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18beab0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3740.000000 -288.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18bf7e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3674.000000 -182.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17cd620">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5122.000000 -311.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18b6fe0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3948.000000 -287.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17e38b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3882.000000 -181.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1811c00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4156.000000 -288.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1812930">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4090.000000 -182.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1815d40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4364.000000 -288.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1816a70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4298.000000 -182.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19bf810">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4572.000000 -288.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19c0540">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4506.000000 -182.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17d81c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4780.000000 -287.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_17d8ef0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4714.000000 -181.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19b7280">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4495.000000 -1036.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="WD_GQ"/>
</svg>