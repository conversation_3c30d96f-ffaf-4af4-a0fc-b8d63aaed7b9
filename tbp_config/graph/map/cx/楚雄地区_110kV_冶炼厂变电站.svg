<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-255" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-138 -979 2007 1107">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="58" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="8" y2="37"/>
   </symbol>
   <symbol id="lightningRod:shape98">
    <polyline DF8003:Layer="PUBLIC" points="0,17 4,32 8,17 0,17 " stroke-width="0.4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="4" y1="5" y2="17"/>
    <polyline DF8003:Layer="PUBLIC" points="0,51 4,36 8,51 0,51 " stroke-width="0.4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="4" y1="63" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape85">
    <circle cx="8" cy="17" fillStyle="0" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="11" y1="20" y2="20"/>
    <rect height="27" stroke-width="0.416667" width="14" x="1" y="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="73" y2="25"/>
    <circle cx="8" cy="8" fillStyle="0" r="7.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape94">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="12" y1="50" y2="37"/>
    <ellipse cx="11" cy="25" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="12" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape183">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.466741" x1="20" x2="20" y1="37" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="3" x2="3" y1="25" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="20" x2="3" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.387695" x1="6" x2="0" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.232617" x1="4" x2="2" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.452311" x1="5" x2="1" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="11" x2="11" y1="41" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="15" x2="15" y1="42" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338352" x1="24" x2="14" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="24" x2="24" y1="41" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="28" x2="28" y1="42" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="28" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.592865" x1="30" x2="27" y1="17" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.578477" x1="27" x2="27" y1="15" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596084" x1="30" x2="27" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.578462" x1="19" x2="21" y1="26" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.578485" x1="21" x2="21" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.578462" x1="23" x2="21" y1="26" y2="25"/>
    <ellipse cx="21" cy="24" rx="6" ry="5.5" stroke-width="0.578489"/>
    <circle cx="28" cy="16" r="5.5" stroke-width="0.578489"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.498625" x1="11" x2="4" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.578485" x1="17" x2="17" y1="14" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.578462" x1="19" x2="17" y1="16" y2="14"/>
    <ellipse cx="17" cy="14" rx="6" ry="5" stroke-width="0.578489"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.578462" x1="15" x2="17" y1="16" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.578485" x1="24" x2="24" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.578462" x1="26" x2="24" y1="8" y2="6"/>
    <ellipse cx="24" cy="6" rx="6" ry="5" stroke-width="0.578489"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.578462" x1="22" x2="24" y1="8" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape137">
    <ellipse cx="18" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="25" x2="28" y1="15" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="31" x2="28" y1="15" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="28" x2="28" y1="17" y2="19"/>
    <ellipse cx="28" cy="16" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="14" x2="20" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="16" x2="14" y1="19" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="17" x2="20" y1="19" y2="22"/>
    <ellipse cx="17" cy="21" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="5" x2="8" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="11" x2="8" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="8" x2="8" y1="13" y2="16"/>
    <ellipse cx="8" cy="14" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="15" x2="18" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="18" x2="18" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="21" x2="18" y1="6" y2="8"/>
   </symbol>
   <symbol id="load:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="5" y2="29"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,19 9,31 17,19 " stroke-width="2"/>
   </symbol>
   <symbol id="load:shape16">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,48 5,1 " stroke-width="3"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape28_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape28_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape28-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape28-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape18_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="13" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape18_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="5" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor1">
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="14" y2="65"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="66"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape13_0">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="38" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape13_1">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="46" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="29" y1="34" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape21_0">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline points="64,100 64,93 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
   </symbol>
   <symbol id="transformer2:shape21_1">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_29a4850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29a54a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29a5d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29a6510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29a7000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29a7b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29a8330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_29a8d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2150a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2150a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29ab7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29ab7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29ad590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29ad590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_29ae5a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29aff10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_29b0b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_29b1a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29b2320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29b32b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29b3c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29b4540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29b4d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29b5de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29b6760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29b7250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_29b7c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_29b9300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_29b9df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_29bae40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29bba90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29c9e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29c1ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_29c31c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_29bd6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1117" width="2017" x="-143" y="-984"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-195933">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1695.000000 -143.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29802" ObjectName="SW-CX_YLC.CX_YLC_304BK"/>
     <cge:Meas_Ref ObjectId="195933"/>
    <cge:TPSR_Ref TObjectID="29802"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195927">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1532.000000 -142.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29796" ObjectName="SW-CX_YLC.CX_YLC_302BK"/>
     <cge:Meas_Ref ObjectId="195927"/>
    <cge:TPSR_Ref TObjectID="29796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195941">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 362.000000 -141.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29810" ObjectName="SW-CX_YLC.CX_YLC_352BK"/>
     <cge:Meas_Ref ObjectId="195941"/>
    <cge:TPSR_Ref TObjectID="29810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195930">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 528.000000 -145.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29799" ObjectName="SW-CX_YLC.CX_YLC_303BK"/>
     <cge:Meas_Ref ObjectId="195930"/>
    <cge:TPSR_Ref TObjectID="29799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195924">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 693.000000 -139.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29793" ObjectName="SW-CX_YLC.CX_YLC_301BK"/>
     <cge:Meas_Ref ObjectId="195924"/>
    <cge:TPSR_Ref TObjectID="29793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195936">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 940.000000 -153.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29805" ObjectName="SW-CX_YLC.CX_YLC_312BK"/>
     <cge:Meas_Ref ObjectId="195936"/>
    <cge:TPSR_Ref TObjectID="29805"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195922">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 642.000000 -301.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29791" ObjectName="SW-CX_YLC.CX_YLC_351BK"/>
     <cge:Meas_Ref ObjectId="195922"/>
    <cge:TPSR_Ref TObjectID="29791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195910">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1408.000000 -629.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29784" ObjectName="SW-CX_YLC.CX_YLC_151BK"/>
     <cge:Meas_Ref ObjectId="195910"/>
    <cge:TPSR_Ref TObjectID="29784"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195920">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1408.000000 -297.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29789" ObjectName="SW-CX_YLC.CX_YLC_305BK"/>
     <cge:Meas_Ref ObjectId="195920"/>
    <cge:TPSR_Ref TObjectID="29789"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_YLC.CX_YLC_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="303,-247 992,-247 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="29782" ObjectName="BS-CX_YLC.CX_YLC_3IM"/>
    <cge:TPSR_Ref TObjectID="29782"/></metadata>
   <polyline fill="none" opacity="0" points="303,-247 992,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YLC.CX_YLC_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1098,-248 1762,-248 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="29783" ObjectName="BS-CX_YLC.CX_YLC_3IIM"/>
    <cge:TPSR_Ref TObjectID="29783"/></metadata>
   <polyline fill="none" opacity="0" points="1098,-248 1762,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YLC.XM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1410,-625 1423,-625 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48325" ObjectName="BS-CX_YLC.XM"/>
    <cge:TPSR_Ref TObjectID="48325"/></metadata>
   <polyline fill="none" opacity="0" points="1410,-625 1423,-625 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_YLC.CX_YLC_351_LD">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 642.000000 -552.000000)" xlink:href="#load:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49698" ObjectName="EC-CX_YLC.CX_YLC_351_LD"/>
    <cge:TPSR_Ref TObjectID="49698"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YLC.CX_YLC_352_LD">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 366.000000 8.000000)" xlink:href="#load:shape16"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49699" ObjectName="EC-CX_YLC.CX_YLC_352_LD"/>
    <cge:TPSR_Ref TObjectID="49699"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YLC.CX_YLC_303_LD">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 532.000000 4.000000)" xlink:href="#load:shape16"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49702" ObjectName="EC-CX_YLC.CX_YLC_303_LD"/>
    <cge:TPSR_Ref TObjectID="49702"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YLC.CX_YLC_301_LD">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 696.000000 6.000000)" xlink:href="#load:shape16"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49700" ObjectName="EC-CX_YLC.CX_YLC_301_LD"/>
    <cge:TPSR_Ref TObjectID="49700"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YLC.CX_YLC_302_LD">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1535.000000 14.000000)" xlink:href="#load:shape16"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49701" ObjectName="EC-CX_YLC.CX_YLC_302_LD"/>
    <cge:TPSR_Ref TObjectID="49701"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_YLC.CX_YLC_304_LD">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1699.000000 13.000000)" xlink:href="#load:shape16"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49703" ObjectName="EC-CX_YLC.CX_YLC_304_LD"/>
    <cge:TPSR_Ref TObjectID="49703"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_20bd090" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 288.000000 -60.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_211ae90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 454.000000 -60.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26b6d90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 618.000000 -60.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26c1840" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1330.000000 -668.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29b56f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1328.000000 -572.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_267dfb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1293.000000 -462.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_266eb70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1457.000000 -60.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e17720" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1621.000000 -60.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26ab7d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1316.000000 -767.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_26b82c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="371,-247 371,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29782@0" ObjectIDZND0="29811@0" Pin0InfoVect0LinkObjId="SW-195942_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2902260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="371,-247 371,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_267e770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="306,-66 325,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_20bd090@0" ObjectIDZND0="29812@0" Pin0InfoVect0LinkObjId="SW-195943_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20bd090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="306,-66 325,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2687b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="537,-247 537,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29782@0" ObjectIDZND0="29800@0" Pin0InfoVect0LinkObjId="SW-195931_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2902260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="537,-247 537,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26b60a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="537,-118 537,-67 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="33752@0" ObjectIDZND0="29801@x" ObjectIDZND1="g_26b4f20@0" ObjectIDZND2="49702@x" Pin0InfoVect0LinkObjId="SW-195932_0" Pin0InfoVect1LinkObjId="g_26b4f20_0" Pin0InfoVect2LinkObjId="EC-CX_YLC.CX_YLC_303_LD_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195931_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="537,-118 537,-67 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_267e4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="472,-66 491,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_211ae90@0" ObjectIDZND0="29801@0" Pin0InfoVect0LinkObjId="SW-195932_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_211ae90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="472,-66 491,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_268a710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="701,-247 701,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29782@0" ObjectIDZND0="29794@0" Pin0InfoVect0LinkObjId="SW-195925_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2902260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="701,-247 701,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_269f890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="701,-118 701,-64 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="33750@0" ObjectIDZND0="29795@x" ObjectIDZND1="g_266d3e0@0" ObjectIDZND2="49700@x" Pin0InfoVect0LinkObjId="SW-195926_0" Pin0InfoVect1LinkObjId="g_266d3e0_0" Pin0InfoVect2LinkObjId="EC-CX_YLC.CX_YLC_301_LD_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195925_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="701,-118 701,-64 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2689ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="636,-66 655,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_26b6d90@0" ObjectIDZND0="29795@0" Pin0InfoVect0LinkObjId="SW-195926_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26b6d90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="636,-66 655,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2159800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1394,-820 1417,-820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="g_2151560@0" ObjectIDZND0="g_264c070@0" ObjectIDZND1="11556@1" ObjectIDZND2="29785@x" Pin0InfoVect0LinkObjId="g_264c070_0" Pin0InfoVect1LinkObjId="g_26d1710_1" Pin0InfoVect2LinkObjId="SW-195911_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2151560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1394,-820 1417,-820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_267b120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1367,-674 1348,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29786@0" ObjectIDZND0="g_26c1840@0" Pin0InfoVect0LinkObjId="g_26c1840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195912_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1367,-674 1348,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26549a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1367,-578 1346,-578 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29787@0" ObjectIDZND0="g_29b56f0@0" Pin0InfoVect0LinkObjId="g_29b56f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195913_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1367,-578 1346,-578 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_263e0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1299,-541 1270,-541 1270,-516 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer2" EndDevType0="lightningRod" ObjectIDND0="29815@x" ObjectIDND1="g_26554b0@0" ObjectIDND2="29814@x" ObjectIDZND0="g_267b780@0" Pin0InfoVect0LinkObjId="g_267b780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-195947_0" Pin1InfoVect1LinkObjId="g_26554b0_0" Pin1InfoVect2LinkObjId="g_26d1500_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1299,-541 1270,-541 1270,-516 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2689800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1299,-541 1299,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="transformer2" EndDevType0="switch" ObjectIDND0="g_267b780@0" ObjectIDND1="g_26554b0@0" ObjectIDND2="29814@x" ObjectIDZND0="29815@1" Pin0InfoVect0LinkObjId="SW-195947_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_267b780_0" Pin1InfoVect1LinkObjId="g_26554b0_0" Pin1InfoVect2LinkObjId="g_26d1500_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1299,-541 1299,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2689a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1299,-494 1299,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29815@0" ObjectIDZND0="g_267dfb0@0" Pin0InfoVect0LinkObjId="g_267dfb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195947_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1299,-494 1299,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_266c4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1359,-519 1359,-541 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="transformer2" ObjectIDND0="g_26554b0@0" ObjectIDZND0="g_267b780@0" ObjectIDZND1="29815@x" ObjectIDZND2="29814@x" Pin0InfoVect0LinkObjId="g_267b780_0" Pin0InfoVect1LinkObjId="SW-195947_0" Pin0InfoVect2LinkObjId="g_26d1500_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26554b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1359,-519 1359,-541 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2639350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1233,-248 1233,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29783@0" ObjectIDZND0="29809@1" Pin0InfoVect0LinkObjId="SW-195940_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28612c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1233,-248 1233,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26918d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1234,-110 1249,-110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="29809@x" ObjectIDND1="g_2651370@0" ObjectIDZND0="g_2691b30@0" Pin0InfoVect0LinkObjId="g_2691b30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-195940_0" Pin1InfoVect1LinkObjId="g_2651370_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1234,-110 1249,-110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24d4350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1540,-248 1540,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29783@0" ObjectIDZND0="29797@0" Pin0InfoVect0LinkObjId="SW-195928_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28612c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1540,-248 1540,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24d45b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1540,-118 1540,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="33751@0" ObjectIDZND0="29798@x" ObjectIDZND1="g_26a0ec0@0" ObjectIDZND2="49701@x" Pin0InfoVect0LinkObjId="SW-195929_0" Pin0InfoVect1LinkObjId="g_26a0ec0_0" Pin0InfoVect2LinkObjId="EC-CX_YLC.CX_YLC_302_LD_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195928_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1540,-118 1540,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f4d750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1475,-66 1494,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_266eb70@0" ObjectIDZND0="29798@0" Pin0InfoVect0LinkObjId="SW-195929_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_266eb70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1475,-66 1494,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f33c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1704,-248 1704,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29783@0" ObjectIDZND0="29803@0" Pin0InfoVect0LinkObjId="SW-195934_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28612c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1704,-248 1704,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f33ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1704,-118 1704,-63 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="33753@0" ObjectIDZND0="29804@x" ObjectIDZND1="g_266e460@0" ObjectIDZND2="49703@x" Pin0InfoVect0LinkObjId="SW-195935_0" Pin0InfoVect1LinkObjId="g_266e460_0" Pin0InfoVect2LinkObjId="EC-CX_YLC.CX_YLC_304_LD_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195934_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1704,-118 1704,-63 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2688eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1639,-66 1658,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1e17720@0" ObjectIDZND0="29804@0" Pin0InfoVect0LinkObjId="SW-195935_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e17720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1639,-66 1658,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a95a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1384,-248 1384,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29783@0" ObjectIDZND0="29813@1" Pin0InfoVect0LinkObjId="SW-195944_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28612c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1384,-248 1384,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a9800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1384,-130 1384,-18 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="29813@0" ObjectIDZND0="g_2687920@0" Pin0InfoVect0LinkObjId="g_2687920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195944_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1384,-130 1384,-18 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2648320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1233,-137 1233,-110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="29809@0" ObjectIDZND0="g_2691b30@0" ObjectIDZND1="g_2651370@0" Pin0InfoVect0LinkObjId="g_2691b30_0" Pin0InfoVect1LinkObjId="g_2651370_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1233,-137 1233,-110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_267f290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1233,-16 1233,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_267f750@0" ObjectIDZND0="g_2651370@0" Pin0InfoVect0LinkObjId="g_2651370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_267f750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1233,-16 1233,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_267f4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1233,-89 1233,-110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2651370@1" ObjectIDZND0="g_2691b30@0" ObjectIDZND1="29809@x" Pin0InfoVect0LinkObjId="g_2691b30_0" Pin0InfoVect1LinkObjId="SW-195940_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2651370_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1233,-89 1233,-110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26b6550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1244,3 1264,3 1264,-13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_26b67b0@0" Pin0InfoVect0LinkObjId="g_26b67b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1244,3 1264,3 1264,-13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_263cc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="852,-247 852,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29782@0" ObjectIDZND0="29808@1" Pin0InfoVect0LinkObjId="SW-195939_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2902260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="852,-247 852,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26b1020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="853,-111 868,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="29808@x" ObjectIDND1="g_2646d50@0" ObjectIDZND0="g_26b1280@0" Pin0InfoVect0LinkObjId="g_26b1280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-195939_0" Pin1InfoVect1LinkObjId="g_2646d50_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="853,-111 868,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2646af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="852,-138 852,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="29808@0" ObjectIDZND0="g_26b1280@0" ObjectIDZND1="g_2646d50@0" Pin0InfoVect0LinkObjId="g_26b1280_0" Pin0InfoVect1LinkObjId="g_2646d50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195939_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="852,-138 852,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_269dc90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="852,-17 852,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_269e150@0" ObjectIDZND0="g_2646d50@0" Pin0InfoVect0LinkObjId="g_2646d50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_269e150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="852,-17 852,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_269def0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="852,-90 852,-111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2646d50@1" ObjectIDZND0="g_26b1280@0" ObjectIDZND1="29808@x" Pin0InfoVect0LinkObjId="g_26b1280_0" Pin0InfoVect1LinkObjId="SW-195939_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2646d50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="852,-90 852,-111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26c77a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="863,2 883,2 883,-14 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_26c7a00@0" Pin0InfoVect0LinkObjId="g_26c7a00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="863,2 883,2 883,-14 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2860df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="949,-247 949,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29782@0" ObjectIDZND0="29806@0" Pin0InfoVect0LinkObjId="SW-195937_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2902260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="949,-247 949,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2861050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="949,-132 949,-109 1143,-109 1143,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="33747@0" ObjectIDZND0="29807@0" Pin0InfoVect0LinkObjId="SW-195938_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195937_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="949,-132 949,-109 1143,-109 1143,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28612c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1143,-217 1143,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29807@1" ObjectIDZND0="29783@0" Pin0InfoVect0LinkObjId="g_26aaf40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195938_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1143,-217 1143,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_262d010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1704,-194 1704,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29803@1" ObjectIDZND0="29802@1" Pin0InfoVect0LinkObjId="SW-195933_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195934_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1704,-194 1704,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_262d270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1704,-151 1704,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29802@0" ObjectIDZND0="33753@1" Pin0InfoVect0LinkObjId="SW-195934_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195933_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1704,-151 1704,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2629220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1541,-193 1541,-177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29797@1" ObjectIDZND0="29796@1" Pin0InfoVect0LinkObjId="SW-195927_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195928_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1541,-193 1541,-177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2629480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1541,-150 1541,-137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29796@0" ObjectIDZND0="33751@1" Pin0InfoVect0LinkObjId="SW-195928_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195927_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1541,-150 1541,-137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2678390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="371,-192 371,-176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29811@1" ObjectIDZND0="29810@1" Pin0InfoVect0LinkObjId="SW-195941_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195942_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="371,-192 371,-176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26785f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="371,-149 371,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29810@0" ObjectIDZND0="33748@1" Pin0InfoVect0LinkObjId="SW-195942_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195941_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="371,-149 371,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b91f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="537,-196 537,-180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29800@1" ObjectIDZND0="29799@1" Pin0InfoVect0LinkObjId="SW-195930_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195931_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="537,-196 537,-180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20b9450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="537,-153 537,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29799@0" ObjectIDZND0="33752@1" Pin0InfoVect0LinkObjId="SW-195931_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="537,-153 537,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2634a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="702,-190 702,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29794@1" ObjectIDZND0="29793@1" Pin0InfoVect0LinkObjId="SW-195924_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195925_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="702,-190 702,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2634ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="702,-147 702,-134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29793@0" ObjectIDZND0="33750@1" Pin0InfoVect0LinkObjId="SW-195925_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195924_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="702,-147 702,-134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_269bc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="949,-204 949,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29806@1" ObjectIDZND0="29805@1" Pin0InfoVect0LinkObjId="SW-195936_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195937_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="949,-204 949,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_269be80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="949,-161 949,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29805@0" ObjectIDZND0="33747@1" Pin0InfoVect0LinkObjId="SW-195937_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195936_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="949,-161 949,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26d1310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1299,-541 1359,-541 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="g_267b780@0" ObjectIDND1="29815@x" ObjectIDZND0="g_26554b0@0" ObjectIDZND1="29814@x" Pin0InfoVect0LinkObjId="g_26554b0_0" Pin0InfoVect1LinkObjId="g_26d1500_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_267b780_0" Pin1InfoVect1LinkObjId="SW-195947_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1299,-541 1359,-541 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26d1500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1359,-541 1416,-541 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="transformer2" ObjectIDND0="g_26554b0@0" ObjectIDND1="g_267b780@0" ObjectIDND2="29815@x" ObjectIDZND0="29814@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_26554b0_0" Pin1InfoVect1LinkObjId="g_267b780_0" Pin1InfoVect2LinkObjId="SW-195947_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1359,-541 1416,-541 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26d1710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1417,-858 1417,-874 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_264c070@0" ObjectIDND1="g_2151560@0" ObjectIDND2="29785@x" ObjectIDZND0="11556@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_264c070_0" Pin1InfoVect1LinkObjId="g_2151560_0" Pin1InfoVect2LinkObjId="SW-195911_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1417,-858 1417,-874 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28fa480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1457,-842 1457,-861 1417,-861 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_264c070@0" ObjectIDZND0="g_2151560@0" ObjectIDZND1="29785@x" ObjectIDZND2="29788@x" Pin0InfoVect0LinkObjId="g_2151560_0" Pin0InfoVect1LinkObjId="SW-195911_0" Pin0InfoVect2LinkObjId="SW-195914_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_264c070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1457,-842 1457,-861 1417,-861 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28fa6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1417,-861 1417,-820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_264c070@0" ObjectIDND1="11556@1" ObjectIDZND0="g_2151560@0" ObjectIDZND1="29785@x" ObjectIDZND2="29788@x" Pin0InfoVect0LinkObjId="g_2151560_0" Pin0InfoVect1LinkObjId="SW-195911_0" Pin0InfoVect2LinkObjId="SW-195914_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_264c070_0" Pin1InfoVect1LinkObjId="g_26d1710_1" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1417,-861 1417,-820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28fa920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1417,-745 1417,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="29785@1" ObjectIDZND0="g_2151560@0" ObjectIDZND1="g_264c070@0" ObjectIDZND2="11556@1" Pin0InfoVect0LinkObjId="g_2151560_0" Pin0InfoVect1LinkObjId="g_264c070_0" Pin0InfoVect2LinkObjId="g_26d1710_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195911_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1417,-745 1417,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28fb410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1417,-773 1417,-820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="29785@x" ObjectIDND1="29788@x" ObjectIDZND0="g_2151560@0" ObjectIDZND1="g_264c070@0" ObjectIDZND2="11556@1" Pin0InfoVect0LinkObjId="g_2151560_0" Pin0InfoVect1LinkObjId="g_264c070_0" Pin0InfoVect2LinkObjId="g_26d1710_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-195911_0" Pin1InfoVect1LinkObjId="SW-195914_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1417,-773 1417,-820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2901b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="651,-391 651,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_26895d0@0" ObjectIDZND0="29792@0" Pin0InfoVect0LinkObjId="SW-195923_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26895d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="651,-391 651,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2901da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="651,-356 651,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29792@1" ObjectIDZND0="29791@1" Pin0InfoVect0LinkObjId="SW-195922_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195923_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="651,-356 651,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2902000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="651,-311 651,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29791@0" ObjectIDZND0="33749@1" Pin0InfoVect0LinkObjId="SW-195923_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195922_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="651,-311 651,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2902260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="651,-269 651,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="33749@0" ObjectIDZND0="29782@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195923_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="651,-269 651,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29024c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1417,-673 1417,-709 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="29786@x" ObjectIDND1="29784@x" ObjectIDZND0="29785@0" Pin0InfoVect0LinkObjId="SW-195911_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-195912_0" Pin1InfoVect1LinkObjId="SW-195910_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1417,-673 1417,-709 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2905790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="682,-459 682,-477 651,-477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="g_26a1520@0" ObjectIDZND0="g_26801e0@0" ObjectIDZND1="g_26895d0@0" ObjectIDZND2="49698@x" Pin0InfoVect0LinkObjId="g_26801e0_0" Pin0InfoVect1LinkObjId="g_26895d0_0" Pin0InfoVect2LinkObjId="EC-CX_YLC.CX_YLC_351_LD_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26a1520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="682,-459 682,-477 651,-477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26a1c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="651,-477 629,-477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="g_26a1520@0" ObjectIDND1="g_26895d0@0" ObjectIDND2="49698@x" ObjectIDZND0="g_26801e0@0" Pin0InfoVect0LinkObjId="g_26801e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_26a1520_0" Pin1InfoVect1LinkObjId="g_26895d0_0" Pin1InfoVect2LinkObjId="EC-CX_YLC.CX_YLC_351_LD_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="651,-477 629,-477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26a1e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="651,-452 651,-478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="g_26895d0@1" ObjectIDZND0="g_26a1520@0" ObjectIDZND1="g_26801e0@0" ObjectIDZND2="49698@x" Pin0InfoVect0LinkObjId="g_26a1520_0" Pin0InfoVect1LinkObjId="g_26801e0_0" Pin0InfoVect2LinkObjId="EC-CX_YLC.CX_YLC_351_LD_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26895d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="651,-452 651,-478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26a2010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="651,-477 651,-557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="g_26a1520@0" ObjectIDND1="g_26801e0@0" ObjectIDND2="g_26895d0@0" ObjectIDZND0="49698@0" Pin0InfoVect0LinkObjId="EC-CX_YLC.CX_YLC_351_LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_26a1520_0" Pin1InfoVect1LinkObjId="g_26801e0_0" Pin1InfoVect2LinkObjId="g_26895d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="651,-477 651,-557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26a2270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1417,-625 1417,-637 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="48325@0" ObjectIDZND0="29784@0" Pin0InfoVect0LinkObjId="SW-195910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29d8800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1417,-625 1417,-637 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26a24d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1417,-474 1417,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="29814@1" ObjectIDZND0="g_266c710@1" Pin0InfoVect0LinkObjId="g_266c710_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26d1500_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1417,-474 1417,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26aaa80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1417,-355 1417,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29790@1" ObjectIDZND0="29789@1" Pin0InfoVect0LinkObjId="SW-195920_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195921_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1417,-355 1417,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26aace0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1417,-305 1417,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29789@0" ObjectIDZND0="33754@1" Pin0InfoVect0LinkObjId="SW-195921_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1417,-305 1417,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26aaf40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1417,-271 1417,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="33754@0" ObjectIDZND0="29783@0" Pin0InfoVect0LinkObjId="g_28612c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195921_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1417,-271 1417,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26ae4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1334,-773 1352,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_26ab7d0@0" ObjectIDZND0="29788@0" Pin0InfoVect0LinkObjId="SW-195914_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26ab7d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1334,-773 1352,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26ae700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1388,-773 1417,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="29788@1" ObjectIDZND0="g_2151560@0" ObjectIDZND1="g_264c070@0" ObjectIDZND2="11556@1" Pin0InfoVect0LinkObjId="g_2151560_0" Pin0InfoVect1LinkObjId="g_264c070_0" Pin0InfoVect2LinkObjId="g_26d1710_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195914_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1388,-773 1417,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29d09d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1399,-390 1417,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_26d22c0@0" ObjectIDZND0="g_266c710@0" ObjectIDZND1="29790@x" ObjectIDZND2="g_29aed50@0" Pin0InfoVect0LinkObjId="g_266c710_0" Pin0InfoVect1LinkObjId="SW-195921_0" Pin0InfoVect2LinkObjId="g_29aed50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26d22c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1399,-390 1417,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29d1690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1417,-404 1417,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_266c710@0" ObjectIDZND0="g_26d22c0@0" ObjectIDZND1="29790@x" ObjectIDZND2="g_29aed50@0" Pin0InfoVect0LinkObjId="g_26d22c0_0" Pin0InfoVect1LinkObjId="SW-195921_0" Pin0InfoVect2LinkObjId="g_29aed50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_266c710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1417,-404 1417,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29d18f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1417,-390 1417,-372 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_26d22c0@0" ObjectIDND1="g_266c710@0" ObjectIDND2="g_29aed50@0" ObjectIDZND0="29790@0" Pin0InfoVect0LinkObjId="SW-195921_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_26d22c0_0" Pin1InfoVect1LinkObjId="g_266c710_0" Pin1InfoVect2LinkObjId="g_29aed50_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1417,-390 1417,-372 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29d1b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1417,-390 1440,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_26d22c0@0" ObjectIDND1="g_266c710@0" ObjectIDND2="29790@x" ObjectIDZND0="g_29aed50@0" Pin0InfoVect0LinkObjId="g_29aed50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_26d22c0_0" Pin1InfoVect1LinkObjId="g_266c710_0" Pin1InfoVect2LinkObjId="SW-195921_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1417,-390 1440,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29d4220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1417,-664 1417,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29784@1" ObjectIDZND0="29785@x" ObjectIDZND1="29786@x" Pin0InfoVect0LinkObjId="SW-195911_0" Pin0InfoVect1LinkObjId="SW-195912_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195910_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1417,-664 1417,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29d4410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1417,-675 1418,-674 1403,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="29785@x" ObjectIDND1="29784@x" ObjectIDZND0="29786@1" Pin0InfoVect0LinkObjId="SW-195912_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-195911_0" Pin1InfoVect1LinkObjId="SW-195910_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1417,-675 1418,-674 1403,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29d7b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1417,-561 1417,-578 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29814@0" ObjectIDZND0="29787@x" ObjectIDZND1="48328@x" Pin0InfoVect0LinkObjId="SW-195913_0" Pin0InfoVect1LinkObjId="SW-311530_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26d1500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1417,-561 1417,-578 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29d7d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1417,-578 1403,-578 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="29814@x" ObjectIDND1="48328@x" ObjectIDZND0="29787@1" Pin0InfoVect0LinkObjId="SW-195913_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_26d1500_0" Pin1InfoVect1LinkObjId="SW-311530_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1417,-578 1403,-578 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29d85a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1417,-578 1417,-606 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="29814@x" ObjectIDND1="29787@x" ObjectIDZND0="48328@0" Pin0InfoVect0LinkObjId="SW-311530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_26d1500_0" Pin1InfoVect1LinkObjId="SW-195913_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1417,-578 1417,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29d8800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1417,-611 1417,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="48328@1" ObjectIDZND0="48325@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-311530_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1417,-611 1417,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a06370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="371,-119 371,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="33748@0" ObjectIDZND0="29812@x" ObjectIDZND1="g_26b7fb0@0" ObjectIDZND2="49699@x" Pin0InfoVect0LinkObjId="SW-195943_0" Pin0InfoVect1LinkObjId="g_26b7fb0_0" Pin0InfoVect2LinkObjId="EC-CX_YLC.CX_YLC_352_LD_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195942_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="371,-119 371,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28fad20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="361,-66 371,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="29812@1" ObjectIDZND0="33748@x" ObjectIDZND1="g_26b7fb0@0" ObjectIDZND2="49699@x" Pin0InfoVect0LinkObjId="SW-195942_0" Pin0InfoVect1LinkObjId="g_26b7fb0_0" Pin0InfoVect2LinkObjId="EC-CX_YLC.CX_YLC_352_LD_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195943_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="361,-66 371,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29d3010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="371,-66 387,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="33748@x" ObjectIDND1="29812@x" ObjectIDND2="49699@x" ObjectIDZND0="g_26b7fb0@0" Pin0InfoVect0LinkObjId="g_26b7fb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-195942_0" Pin1InfoVect1LinkObjId="SW-195943_0" Pin1InfoVect2LinkObjId="EC-CX_YLC.CX_YLC_352_LD_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="371,-66 387,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26b70b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="371,-66 371,-40 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="33748@x" ObjectIDND1="29812@x" ObjectIDND2="g_26b7fb0@0" ObjectIDZND0="49699@0" Pin0InfoVect0LinkObjId="EC-CX_YLC.CX_YLC_352_LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-195942_0" Pin1InfoVect1LinkObjId="SW-195943_0" Pin1InfoVect2LinkObjId="g_26b7fb0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="371,-66 371,-40 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2879600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="527,-66 537,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="29801@1" ObjectIDZND0="33752@x" ObjectIDZND1="g_26b4f20@0" ObjectIDZND2="49702@x" Pin0InfoVect0LinkObjId="SW-195931_0" Pin0InfoVect1LinkObjId="g_26b4f20_0" Pin0InfoVect2LinkObjId="EC-CX_YLC.CX_YLC_303_LD_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195932_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="527,-66 537,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_286b020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="537,-66 553,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="33752@x" ObjectIDND1="29801@x" ObjectIDND2="49702@x" ObjectIDZND0="g_26b4f20@0" Pin0InfoVect0LinkObjId="g_26b4f20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-195931_0" Pin1InfoVect1LinkObjId="SW-195932_0" Pin1InfoVect2LinkObjId="EC-CX_YLC.CX_YLC_303_LD_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="537,-66 553,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_268a0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="537,-66 537,-44 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="33752@x" ObjectIDND1="29801@x" ObjectIDND2="g_26b4f20@0" ObjectIDZND0="49702@0" Pin0InfoVect0LinkObjId="EC-CX_YLC.CX_YLC_303_LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-195931_0" Pin1InfoVect1LinkObjId="SW-195932_0" Pin1InfoVect2LinkObjId="g_26b4f20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="537,-66 537,-44 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2871360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="691,-66 701,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="29795@1" ObjectIDZND0="33750@x" ObjectIDZND1="g_266d3e0@0" ObjectIDZND2="49700@x" Pin0InfoVect0LinkObjId="SW-195925_0" Pin0InfoVect1LinkObjId="g_266d3e0_0" Pin0InfoVect2LinkObjId="EC-CX_YLC.CX_YLC_301_LD_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195926_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="691,-66 701,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_267a090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="701,-66 717,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="33750@x" ObjectIDND1="29795@x" ObjectIDND2="49700@x" ObjectIDZND0="g_266d3e0@0" Pin0InfoVect0LinkObjId="g_266d3e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-195925_0" Pin1InfoVect1LinkObjId="SW-195926_0" Pin1InfoVect2LinkObjId="EC-CX_YLC.CX_YLC_301_LD_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="701,-66 717,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a19940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="701,-66 701,-42 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="33750@x" ObjectIDND1="29795@x" ObjectIDND2="g_266d3e0@0" ObjectIDZND0="49700@0" Pin0InfoVect0LinkObjId="EC-CX_YLC.CX_YLC_301_LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-195925_0" Pin1InfoVect1LinkObjId="SW-195926_0" Pin1InfoVect2LinkObjId="g_266d3e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="701,-66 701,-42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2886500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1530,-66 1540,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="29798@1" ObjectIDZND0="33751@x" ObjectIDZND1="g_26a0ec0@0" ObjectIDZND2="49701@x" Pin0InfoVect0LinkObjId="SW-195928_0" Pin0InfoVect1LinkObjId="g_26a0ec0_0" Pin0InfoVect2LinkObjId="EC-CX_YLC.CX_YLC_302_LD_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195929_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1530,-66 1540,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2871910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1540,-66 1556,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="33751@x" ObjectIDND1="29798@x" ObjectIDND2="49701@x" ObjectIDZND0="g_26a0ec0@0" Pin0InfoVect0LinkObjId="g_26a0ec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-195928_0" Pin1InfoVect1LinkObjId="SW-195929_0" Pin1InfoVect2LinkObjId="EC-CX_YLC.CX_YLC_302_LD_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1540,-66 1556,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_286a300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1540,-66 1540,-34 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="33751@x" ObjectIDND1="29798@x" ObjectIDND2="g_26a0ec0@0" ObjectIDZND0="49701@0" Pin0InfoVect0LinkObjId="EC-CX_YLC.CX_YLC_302_LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-195928_0" Pin1InfoVect1LinkObjId="SW-195929_0" Pin1InfoVect2LinkObjId="g_26a0ec0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1540,-66 1540,-34 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a260e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1694,-66 1704,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="29804@1" ObjectIDZND0="33753@x" ObjectIDZND1="g_266e460@0" ObjectIDZND2="49703@x" Pin0InfoVect0LinkObjId="SW-195934_0" Pin0InfoVect1LinkObjId="g_266e460_0" Pin0InfoVect2LinkObjId="EC-CX_YLC.CX_YLC_304_LD_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195935_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1694,-66 1704,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a14200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1704,-66 1720,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="33753@x" ObjectIDND1="29804@x" ObjectIDND2="49703@x" ObjectIDZND0="g_266e460@0" Pin0InfoVect0LinkObjId="g_266e460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-195934_0" Pin1InfoVect1LinkObjId="SW-195935_0" Pin1InfoVect2LinkObjId="EC-CX_YLC.CX_YLC_304_LD_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1704,-66 1720,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_286eae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1704,-66 1704,-35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="33753@x" ObjectIDND1="29804@x" ObjectIDND2="g_266e460@0" ObjectIDZND0="49703@0" Pin0InfoVect0LinkObjId="EC-CX_YLC.CX_YLC_304_LD_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-195934_0" Pin1InfoVect1LinkObjId="SW-195935_0" Pin1InfoVect2LinkObjId="g_266e460_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1704,-66 1704,-35 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="29783" cx="1704" cy="-248" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29783" cx="1384" cy="-248" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29782" cx="371" cy="-247" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29783" cx="1143" cy="-248" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29783" cx="1540" cy="-248" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29782" cx="537" cy="-247" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29782" cx="651" cy="-247" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29783" cx="1417" cy="-248" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29782" cx="949" cy="-247" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29783" cx="1233" cy="-248" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29782" cx="852" cy="-247" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29782" cx="701" cy="-247" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48325" cx="1417" cy="-625" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48325" cx="1417" cy="-625" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-195948" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 109.000000 -874.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29756" ObjectName="DYN-CX_YLC"/>
     <cge:Meas_Ref ObjectId="195948"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_263b260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -138.000000 -825.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_263b260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -138.000000 -825.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_263b260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -138.000000 -825.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_263b260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -138.000000 -825.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_263b260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -138.000000 -825.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_263b260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -138.000000 -825.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_263b260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -138.000000 -825.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29b3960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29b3960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29b3960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29b3960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29b3960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29b3960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29b3960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29b3960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29b3960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29b3960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29b3960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29b3960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29b3960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29b3960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29b3960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29b3960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_29b3960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -373.000000) translate(0,353)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_250e730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -5.000000 -951.500000) translate(0,16)">滇中冶炼厂变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26970f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1454.000000 -575.000000) translate(0,12)">SFZ11-40000/110GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26970f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1454.000000 -575.000000) translate(0,27)">110±8×1.25%/35kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26970f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1454.000000 -575.000000) translate(0,42)">Ud%=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26970f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1454.000000 -575.000000) translate(0,57)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_213b940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -462.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264b3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1340.000000 -375.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29ac1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1350.000000 54.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26c6170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1514.000000 64.000000) translate(0,12)">2号配用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26c67f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1667.000000 54.000000) translate(0,12)">4号配用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2861520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 662.000000 54.000000) translate(0,12)">1号配用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26af380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 503.000000 56.000000) translate(0,12)">3号配用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26af5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 334.000000 63.000000) translate(0,12)">熔炼电炉变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_214be90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 817.000000 42.000000) translate(0,12)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_214be90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 817.000000 42.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2157040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1199.000000 42.000000) translate(0,12)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2157040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1199.000000 42.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21576c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 667.000000 -572.000000) translate(0,12)">冶</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21576c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 667.000000 -572.000000) translate(0,27)">炼</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21576c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 667.000000 -572.000000) translate(0,42)">厂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21576c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 667.000000 -572.000000) translate(0,57)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2679d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1428.000000 -912.000000) translate(0,12)">谢沙冶线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2675f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1348.000000 -599.000000) translate(0,12)">15117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26761a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1344.000000 -696.000000) translate(0,12)">15160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_262aad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1346.000000 -803.000000) translate(0,12)">K1517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_262af70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1306.000000 -519.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2670ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1716.000000 -172.000000) translate(0,12)">304</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2671110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1553.000000 -171.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2671350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1395.000000 -173.000000) translate(0,12)">3532</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2671590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1242.000000 -182.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2671ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1155.000000 -190.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2671d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 961.000000 -182.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2671f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 859.000000 -178.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26721b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 714.000000 -168.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26723f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -174.000000) translate(0,12)">303</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2672630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 663.000000 -330.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2672870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1645.000000 -92.000000) translate(0,12)">30467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2672ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1482.000000 -92.000000) translate(0,12)">30267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2672cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 645.000000 -93.000000) translate(0,12)">30167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2672f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 480.000000 -91.000000) translate(0,12)">30367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2673170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 314.000000 -92.000000) translate(0,12)">35267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26733b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 383.000000 -170.000000) translate(0,12)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26735f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 387.000000 -269.000000) translate(0,12)">35kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2673b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1672.000000 -268.000000) translate(0,12)">35kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26cf810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1424.000000 -734.000000) translate(0,12)">1516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_26cfd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1562.000000 -536.000000) translate(0,16)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29047f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1426.000000 -658.000000) translate(0,12)">151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26ab1a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1432.000000 -326.000000) translate(0,12)">305</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29d3440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -20.000000 -45.000000) translate(0,12)">15911785300</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29d3440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -20.000000 -45.000000) translate(0,27)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29d3440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -20.000000 -45.000000) translate(0,42)">3398035</text>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-195947">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1289.533537 -489.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29815" ObjectName="SW-CX_YLC.CX_YLC_1010SW"/>
     <cge:Meas_Ref ObjectId="195947"/>
    <cge:TPSR_Ref TObjectID="29815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195943">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 320.000000 -61.000000)" xlink:href="#switch2:shape28_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29812" ObjectName="SW-CX_YLC.CX_YLC_35267SW"/>
     <cge:Meas_Ref ObjectId="195943"/>
    <cge:TPSR_Ref TObjectID="29812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195932">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 486.000000 -61.000000)" xlink:href="#switch2:shape28_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29801" ObjectName="SW-CX_YLC.CX_YLC_30367SW"/>
     <cge:Meas_Ref ObjectId="195932"/>
    <cge:TPSR_Ref TObjectID="29801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195926">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 650.000000 -61.000000)" xlink:href="#switch2:shape28_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29795" ObjectName="SW-CX_YLC.CX_YLC_30167SW"/>
     <cge:Meas_Ref ObjectId="195926"/>
    <cge:TPSR_Ref TObjectID="29795"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195912">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1362.000000 -669.000000)" xlink:href="#switch2:shape28_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29786" ObjectName="SW-CX_YLC.CX_YLC_15160SW"/>
     <cge:Meas_Ref ObjectId="195912"/>
    <cge:TPSR_Ref TObjectID="29786"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195913">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1362.000000 -573.000000)" xlink:href="#switch2:shape28_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29787" ObjectName="SW-CX_YLC.CX_YLC_15117SW"/>
     <cge:Meas_Ref ObjectId="195913"/>
    <cge:TPSR_Ref TObjectID="29787"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195929">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1489.000000 -61.000000)" xlink:href="#switch2:shape28_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29798" ObjectName="SW-CX_YLC.CX_YLC_30267SW"/>
     <cge:Meas_Ref ObjectId="195929"/>
    <cge:TPSR_Ref TObjectID="29798"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195935">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1653.000000 -61.000000)" xlink:href="#switch2:shape28_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29804" ObjectName="SW-CX_YLC.CX_YLC_30467SW"/>
     <cge:Meas_Ref ObjectId="195935"/>
    <cge:TPSR_Ref TObjectID="29804"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195944">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1374.000000 -125.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29813" ObjectName="SW-CX_YLC.CX_YLC_3532XC"/>
     <cge:Meas_Ref ObjectId="195944"/>
    <cge:TPSR_Ref TObjectID="29813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195940">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1223.000000 -132.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29809" ObjectName="SW-CX_YLC.CX_YLC_3902XC"/>
     <cge:Meas_Ref ObjectId="195940"/>
    <cge:TPSR_Ref TObjectID="29809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195939">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 842.000000 -133.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29808" ObjectName="SW-CX_YLC.CX_YLC_3901XC"/>
     <cge:Meas_Ref ObjectId="195939"/>
    <cge:TPSR_Ref TObjectID="29808"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195938">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1133.000000 -143.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29807" ObjectName="SW-CX_YLC.CX_YLC_3122XC"/>
     <cge:Meas_Ref ObjectId="195938"/>
    <cge:TPSR_Ref TObjectID="29807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195934">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1694.000000 -187.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29803" ObjectName="SW-CX_YLC.CX_YLC_304XC"/>
     <cge:Meas_Ref ObjectId="195934"/>
    <cge:TPSR_Ref TObjectID="29803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195934">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1694.000000 -114.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33753" ObjectName="SW-CX_YLC.CX_YLC_304XC1"/>
     <cge:Meas_Ref ObjectId="195934"/>
    <cge:TPSR_Ref TObjectID="33753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195928">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1531.000000 -186.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29797" ObjectName="SW-CX_YLC.CX_YLC_302XC"/>
     <cge:Meas_Ref ObjectId="195928"/>
    <cge:TPSR_Ref TObjectID="29797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195928">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1531.000000 -113.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33751" ObjectName="SW-CX_YLC.CX_YLC_302XC1"/>
     <cge:Meas_Ref ObjectId="195928"/>
    <cge:TPSR_Ref TObjectID="33751"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195942">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 361.000000 -185.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29811" ObjectName="SW-CX_YLC.CX_YLC_352XC"/>
     <cge:Meas_Ref ObjectId="195942"/>
    <cge:TPSR_Ref TObjectID="29811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195942">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 361.000000 -112.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33748" ObjectName="SW-CX_YLC.CX_YLC_352XC1"/>
     <cge:Meas_Ref ObjectId="195942"/>
    <cge:TPSR_Ref TObjectID="33748"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195931">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 527.000000 -189.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29800" ObjectName="SW-CX_YLC.CX_YLC_303XC"/>
     <cge:Meas_Ref ObjectId="195931"/>
    <cge:TPSR_Ref TObjectID="29800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195931">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 527.000000 -116.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33752" ObjectName="SW-CX_YLC.CX_YLC_303XC1"/>
     <cge:Meas_Ref ObjectId="195931"/>
    <cge:TPSR_Ref TObjectID="33752"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195925">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 692.000000 -183.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29794" ObjectName="SW-CX_YLC.CX_YLC_301XC"/>
     <cge:Meas_Ref ObjectId="195925"/>
    <cge:TPSR_Ref TObjectID="29794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195925">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 692.000000 -110.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33750" ObjectName="SW-CX_YLC.CX_YLC_301XC1"/>
     <cge:Meas_Ref ObjectId="195925"/>
    <cge:TPSR_Ref TObjectID="33750"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195937">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 939.000000 -197.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29806" ObjectName="SW-CX_YLC.CX_YLC_312XC"/>
     <cge:Meas_Ref ObjectId="195937"/>
    <cge:TPSR_Ref TObjectID="29806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195937">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 939.000000 -124.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33747" ObjectName="SW-CX_YLC.CX_YLC_312XC1"/>
     <cge:Meas_Ref ObjectId="195937"/>
    <cge:TPSR_Ref TObjectID="33747"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195911">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1408.000000 -704.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29785" ObjectName="SW-CX_YLC.CX_YLC_1516SW"/>
     <cge:Meas_Ref ObjectId="195911"/>
    <cge:TPSR_Ref TObjectID="29785"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195923">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 641.000000 -349.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29792" ObjectName="SW-CX_YLC.CX_YLC_351XC"/>
     <cge:Meas_Ref ObjectId="195923"/>
    <cge:TPSR_Ref TObjectID="29792"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195923">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 641.000000 -262.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33749" ObjectName="SW-CX_YLC.CX_YLC_351XC1"/>
     <cge:Meas_Ref ObjectId="195923"/>
    <cge:TPSR_Ref TObjectID="33749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195921">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1407.000000 -348.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29790" ObjectName="SW-CX_YLC.CX_YLC_305XC"/>
     <cge:Meas_Ref ObjectId="195921"/>
    <cge:TPSR_Ref TObjectID="29790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195921">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1407.000000 -264.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33754" ObjectName="SW-CX_YLC.CX_YLC_305XC1"/>
     <cge:Meas_Ref ObjectId="195921"/>
    <cge:TPSR_Ref TObjectID="33754"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195914">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1347.000000 -768.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29788" ObjectName="SW-CX_YLC.CX_YLC_K1517SW"/>
     <cge:Meas_Ref ObjectId="195914"/>
    <cge:TPSR_Ref TObjectID="29788"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-311530">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.136957 1408.000000 -605.350000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48328" ObjectName="SW-CX_YLC.XB"/>
     <cge:Meas_Ref ObjectId="311530"/>
    <cge:TPSR_Ref TObjectID="48328"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.499614 -0.000000 0.000000 -0.539683 518.000000 48.785714)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.499614 -0.000000 0.000000 -0.539683 518.000000 48.785714)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.499614 -0.000000 0.000000 -0.539683 683.000000 50.785714)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.499614 -0.000000 0.000000 -0.539683 683.000000 50.785714)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.499614 -0.000000 0.000000 -0.539683 1521.000000 58.785714)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.499614 -0.000000 0.000000 -0.539683 1521.000000 58.785714)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.499614 -0.000000 0.000000 -0.539683 1686.000000 53.785714)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.499614 -0.000000 0.000000 -0.539683 1686.000000 53.785714)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_YLC.CX_YLC_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="42231"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1379.000000 -469.000000)" xlink:href="#transformer2:shape21_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1379.000000 -469.000000)" xlink:href="#transformer2:shape21_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="29814" ObjectName="TF-CX_YLC.CX_YLC_1T"/>
    <cge:TPSR_Ref TObjectID="29814"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -57.000000 -903.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-195842" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1553.000000 -494.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195842" ObjectName="CX_YLC:CX_YLC_1T_Tmp1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-195843" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1554.000000 -480.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195843" ObjectName="CX_YLC:CX_YLC_1T_Tmp2"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="170" x="-45" y="-962"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="170" x="-45" y="-962"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-94" y="-979"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-94" y="-979"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_214f6d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1778.000000 242.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2674780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1784.000000 226.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2674a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1768.000000 211.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26751a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1778.000000 272.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2675420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1784.000000 195.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2675ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1778.000000 257.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2673e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1490.000000 624.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26743d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1466.000000 639.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e3430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1469.000000 608.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e36b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1476.000000 654.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e5940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 726.000000 328.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e5e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 702.000000 343.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e6070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 705.000000 312.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e62b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 712.000000 358.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e65e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1491.000000 321.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e6850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1467.000000 336.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e6a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1470.000000 305.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e6cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1477.000000 351.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e7b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 281.000000 -97.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e7de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 291.000000 -82.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e8020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 305.000000 -112.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26c8d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 271.000000 322.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26c8fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 277.000000 306.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26c9210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 261.000000 291.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26c9450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 271.000000 352.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26c9690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 277.000000 275.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26c98d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 271.000000 337.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26ca1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1478.000000 494.000000) translate(0,12)">油温(℃)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26caff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1448.000000 479.000000) translate(0,12)">绕组温度(℃)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26cbcb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1478.000000 509.000000) translate(0,12)">档位(档)：</text>
   <metadata/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_267b780">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1262.533537 -462.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26554b0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1353.000000 -462.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26a1520">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 675.000000 -405.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26895d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 647.000000 -388.000000)" xlink:href="#lightningRod:shape98"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26801e0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 556.500000 -485.500000)" xlink:href="#lightningRod:shape85"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20ae010">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 359.000000 59.000000)" xlink:href="#lightningRod:shape94"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26b7fb0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 441.000000 -58.533537)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26b4f20">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 607.000000 -58.533537)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_266d3e0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 771.000000 -58.533537)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_264c070">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1450.000000 -792.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2151560">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1361.000000 -857.000000)" xlink:href="#lightningRod:shape183"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_266c710">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1413.000000 -401.000000)" xlink:href="#lightningRod:shape98"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2691b30">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1303.000000 -102.533537)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26a0ec0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1610.000000 -58.533537)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_266e460">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1774.000000 -58.533537)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2687920">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1373.000000 35.000000)" xlink:href="#lightningRod:shape94"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2651370">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1226.000000 -40.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_267f750">
    <use class="BV-35KV" transform="matrix(1.135135 -0.000000 0.000000 1.321429 1212.000000 -20.000000)" xlink:href="#lightningRod:shape137"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26b67b0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1271.466463 -67.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26b1280">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 922.000000 -103.533537)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2646d50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 845.000000 -41.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_269e150">
    <use class="BV-35KV" transform="matrix(1.135135 -0.000000 0.000000 1.321429 831.000000 -21.000000)" xlink:href="#lightningRod:shape137"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26c7a00">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 890.466463 -68.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26d22c0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 0.974026 0.000000 1327.512987 -398.500000)" xlink:href="#lightningRod:shape85"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29aed50">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1494.000000 -382.533537)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="170" x="-45" y="-962"/></g>
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-94" y="-979"/></g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="CX_YLC" flowDrawDirect="1" flowShape="0" id="AC-110kV.xieshayeTylc_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1417,-871 1417,-941 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11556" ObjectName="AC-110kV.xieshayeTylc_line"/>
    <cge:TPSR_Ref TObjectID="11556_SS-255"/></metadata>
   <polyline fill="none" opacity="0" points="1417,-871 1417,-941 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-195826" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1530.000000 -653.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195826" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29784"/>
     <cge:Term_Ref ObjectID="42169"/>
    <cge:TPSR_Ref TObjectID="29784"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-195827" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1530.000000 -653.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195827" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29784"/>
     <cge:Term_Ref ObjectID="42169"/>
    <cge:TPSR_Ref TObjectID="29784"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-195823" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1530.000000 -653.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195823" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29784"/>
     <cge:Term_Ref ObjectID="42169"/>
    <cge:TPSR_Ref TObjectID="29784"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-195819" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1530.000000 -653.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195819" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29784"/>
     <cge:Term_Ref ObjectID="42169"/>
    <cge:TPSR_Ref TObjectID="29784"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-195839" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1530.000000 -348.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195839" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29789"/>
     <cge:Term_Ref ObjectID="42179"/>
    <cge:TPSR_Ref TObjectID="29789"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-195840" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1530.000000 -348.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195840" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29789"/>
     <cge:Term_Ref ObjectID="42179"/>
    <cge:TPSR_Ref TObjectID="29789"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-195836" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1530.000000 -348.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195836" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29789"/>
     <cge:Term_Ref ObjectID="42179"/>
    <cge:TPSR_Ref TObjectID="29789"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-195832" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1530.000000 -348.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195832" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29789"/>
     <cge:Term_Ref ObjectID="42179"/>
    <cge:TPSR_Ref TObjectID="29789"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-195855" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 767.000000 -354.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195855" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29791"/>
     <cge:Term_Ref ObjectID="42183"/>
    <cge:TPSR_Ref TObjectID="29791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-195856" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 767.000000 -354.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195856" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29791"/>
     <cge:Term_Ref ObjectID="42183"/>
    <cge:TPSR_Ref TObjectID="29791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-195852" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 767.000000 -354.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195852" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29791"/>
     <cge:Term_Ref ObjectID="42183"/>
    <cge:TPSR_Ref TObjectID="29791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-195848" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 767.000000 -354.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195848" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29791"/>
     <cge:Term_Ref ObjectID="42183"/>
    <cge:TPSR_Ref TObjectID="29791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-195901" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 349.000000 83.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195901" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29810"/>
     <cge:Term_Ref ObjectID="42221"/>
    <cge:TPSR_Ref TObjectID="29810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-195902" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 349.000000 83.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195902" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29810"/>
     <cge:Term_Ref ObjectID="42221"/>
    <cge:TPSR_Ref TObjectID="29810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-195898" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 349.000000 83.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195898" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29810"/>
     <cge:Term_Ref ObjectID="42221"/>
    <cge:TPSR_Ref TObjectID="29810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-195889" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 523.000000 72.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195889" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29799"/>
     <cge:Term_Ref ObjectID="42199"/>
    <cge:TPSR_Ref TObjectID="29799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-195890" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 523.000000 72.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195890" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29799"/>
     <cge:Term_Ref ObjectID="42199"/>
    <cge:TPSR_Ref TObjectID="29799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-195886" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 523.000000 72.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195886" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29799"/>
     <cge:Term_Ref ObjectID="42199"/>
    <cge:TPSR_Ref TObjectID="29799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-195877" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 679.000000 76.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195877" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29793"/>
     <cge:Term_Ref ObjectID="42187"/>
    <cge:TPSR_Ref TObjectID="29793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-195878" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 679.000000 76.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195878" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29793"/>
     <cge:Term_Ref ObjectID="42187"/>
    <cge:TPSR_Ref TObjectID="29793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-195874" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 679.000000 76.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195874" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29793"/>
     <cge:Term_Ref ObjectID="42187"/>
    <cge:TPSR_Ref TObjectID="29793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-195907" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1000.000000 -193.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195907" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29805"/>
     <cge:Term_Ref ObjectID="42211"/>
    <cge:TPSR_Ref TObjectID="29805"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-195908" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1000.000000 -193.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195908" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29805"/>
     <cge:Term_Ref ObjectID="42211"/>
    <cge:TPSR_Ref TObjectID="29805"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-195904" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1000.000000 -193.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195904" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29805"/>
     <cge:Term_Ref ObjectID="42211"/>
    <cge:TPSR_Ref TObjectID="29805"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-195883" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1528.000000 83.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195883" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29796"/>
     <cge:Term_Ref ObjectID="42193"/>
    <cge:TPSR_Ref TObjectID="29796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-195884" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1528.000000 83.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195884" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29796"/>
     <cge:Term_Ref ObjectID="42193"/>
    <cge:TPSR_Ref TObjectID="29796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-195880" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1528.000000 83.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195880" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29796"/>
     <cge:Term_Ref ObjectID="42193"/>
    <cge:TPSR_Ref TObjectID="29796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-195895" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1681.000000 72.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195895" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29802"/>
     <cge:Term_Ref ObjectID="42205"/>
    <cge:TPSR_Ref TObjectID="29802"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-195896" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1681.000000 72.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195896" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29802"/>
     <cge:Term_Ref ObjectID="42205"/>
    <cge:TPSR_Ref TObjectID="29802"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-195892" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1681.000000 72.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195892" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29802"/>
     <cge:Term_Ref ObjectID="42205"/>
    <cge:TPSR_Ref TObjectID="29802"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-195866" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1838.000000 -271.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195866" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29783"/>
     <cge:Term_Ref ObjectID="17483"/>
    <cge:TPSR_Ref TObjectID="29783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-195867" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1838.000000 -271.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195867" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29783"/>
     <cge:Term_Ref ObjectID="17483"/>
    <cge:TPSR_Ref TObjectID="29783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-195868" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1838.000000 -271.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195868" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29783"/>
     <cge:Term_Ref ObjectID="17483"/>
    <cge:TPSR_Ref TObjectID="29783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-195872" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1838.000000 -271.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195872" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29783"/>
     <cge:Term_Ref ObjectID="17483"/>
    <cge:TPSR_Ref TObjectID="29783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-195869" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1838.000000 -271.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195869" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29783"/>
     <cge:Term_Ref ObjectID="17483"/>
    <cge:TPSR_Ref TObjectID="29783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-195873" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1838.000000 -271.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195873" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29783"/>
     <cge:Term_Ref ObjectID="17483"/>
    <cge:TPSR_Ref TObjectID="29783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-195858" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 333.000000 -346.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195858" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29782"/>
     <cge:Term_Ref ObjectID="17482"/>
    <cge:TPSR_Ref TObjectID="29782"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-195859" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 333.000000 -346.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195859" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29782"/>
     <cge:Term_Ref ObjectID="17482"/>
    <cge:TPSR_Ref TObjectID="29782"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-195860" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 333.000000 -346.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195860" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29782"/>
     <cge:Term_Ref ObjectID="17482"/>
    <cge:TPSR_Ref TObjectID="29782"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-195864" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 333.000000 -346.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195864" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29782"/>
     <cge:Term_Ref ObjectID="17482"/>
    <cge:TPSR_Ref TObjectID="29782"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-195861" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 333.000000 -346.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195861" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29782"/>
     <cge:Term_Ref ObjectID="17482"/>
    <cge:TPSR_Ref TObjectID="29782"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-195865" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 333.000000 -346.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195865" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29782"/>
     <cge:Term_Ref ObjectID="17482"/>
    <cge:TPSR_Ref TObjectID="29782"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-195844" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1552.000000 -510.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="195844" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29814"/>
     <cge:Term_Ref ObjectID="42229"/>
    <cge:TPSR_Ref TObjectID="29814"/></metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_YLC"/>
</svg>