<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-202" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="530 -703 1789 1183">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape101">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="39" y2="44"/>
    <polyline DF8003:Layer="PUBLIC" points="16,70 10,58 22,58 16,70 16,69 16,70 "/>
    <circle cx="16" cy="61" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="38" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="8" y2="8"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,39 40,39 40,8 " stroke-width="1"/>
    <circle cx="16" cy="39" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="39" y2="34"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <circle cx="14" cy="13" fillStyle="0" r="13.5" stroke-width="1"/>
    <circle cx="14" cy="34" fillStyle="0" r="14" stroke-width="1"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <circle cx="31" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="49" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="80" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="42" x2="26" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="26" y1="33" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <ellipse cx="31" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="22" x2="30" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape25_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="58" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape25_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="12,76 19,76 16,83 12,76 "/>
   </symbol>
   <symbol id="voltageTransformer:shape79">
    <circle cx="18" cy="24" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="34" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <circle cx="18" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="13" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="15" y2="9"/>
    <polyline points="40,23 28,32 28,36 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="37" y2="46"/>
    <rect height="14" stroke-width="1" width="8" x="30" y="23"/>
    <circle cx="7" cy="24" r="7.5" stroke-width="1"/>
    <circle cx="7" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="37" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="36" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="39" y1="46" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="23" y2="13"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1895600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18966e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1897170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_18980c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1899320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1899f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_189a960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_189b420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_189caa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_189caa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_189e390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_189e390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18a0120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18a0120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_18a1140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_18a2db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1c08fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1c09d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c0a450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c0bc70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c0c5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c0cd20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c0d4e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c0e5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c0ef40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c0fa30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1c103f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1c119c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1c12440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1c13640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c142b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c1a7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c1b5e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1c15c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1c17180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1193" width="1799" x="525" y="-708"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1845" x2="1868" y1="-486" y2="-469"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="22" stroke="rgb(255,255,0)" stroke-width="1" width="12" x="1358" y="-644"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="22" stroke="rgb(255,255,0)" stroke-width="1" width="12" x="1839" y="-403"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="22" stroke="rgb(255,255,0)" stroke-width="1" width="12" x="1983" y="-450"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="22" stroke="rgb(255,255,0)" stroke-width="1" width="12" x="1600" y="-641"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="919" y="-627"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1056.000000 167.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218958">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1387.000000 -247.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34063" ObjectName="SW-MD_SMC.MD_SMC_3021SW"/>
     <cge:Meas_Ref ObjectId="218958"/>
    <cge:TPSR_Ref TObjectID="34063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134402">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1134.608696 72.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24428" ObjectName="SW-MD_SMC.MD_SMC_0901SW"/>
     <cge:Meas_Ref ObjectId="134402"/>
    <cge:TPSR_Ref TObjectID="24428"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134475">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1916.000000 294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24440" ObjectName="SW-MD_SMC.MD_SMC_0856SW"/>
     <cge:Meas_Ref ObjectId="134475"/>
    <cge:TPSR_Ref TObjectID="24440"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134474">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1916.000000 157.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24439" ObjectName="SW-MD_SMC.MD_SMC_0851SW"/>
     <cge:Meas_Ref ObjectId="134474"/>
    <cge:TPSR_Ref TObjectID="24439"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134452">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1724.333333 300.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24437" ObjectName="SW-MD_SMC.MD_SMC_0846SW"/>
     <cge:Meas_Ref ObjectId="134452"/>
    <cge:TPSR_Ref TObjectID="24437"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134451">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1724.333333 163.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24436" ObjectName="SW-MD_SMC.MD_SMC_0841SW"/>
     <cge:Meas_Ref ObjectId="134451"/>
    <cge:TPSR_Ref TObjectID="24436"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134429">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1531.666667 302.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24434" ObjectName="SW-MD_SMC.MD_SMC_0836SW"/>
     <cge:Meas_Ref ObjectId="134429"/>
    <cge:TPSR_Ref TObjectID="24434"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134428">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1531.666667 165.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24433" ObjectName="SW-MD_SMC.MD_SMC_0831SW"/>
     <cge:Meas_Ref ObjectId="134428"/>
    <cge:TPSR_Ref TObjectID="24433"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134406">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1340.000000 305.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24431" ObjectName="SW-MD_SMC.MD_SMC_0826SW"/>
     <cge:Meas_Ref ObjectId="134406"/>
    <cge:TPSR_Ref TObjectID="24431"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134405">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1340.000000 168.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24430" ObjectName="SW-MD_SMC.MD_SMC_0821SW"/>
     <cge:Meas_Ref ObjectId="134405"/>
    <cge:TPSR_Ref TObjectID="24430"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1139.000000 11.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218948">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1398.000000 -427.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34069" ObjectName="SW-MD_SMC.MD_SMC_3816SW"/>
     <cge:Meas_Ref ObjectId="218948"/>
    <cge:TPSR_Ref TObjectID="34069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134323">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1398.000000 -321.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34068" ObjectName="SW-MD_SMC.MD_SMC_3811SW"/>
     <cge:Meas_Ref ObjectId="134323"/>
    <cge:TPSR_Ref TObjectID="34068"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218995">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1640.000000 -426.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34074" ObjectName="SW-MD_SMC.MD_SMC_3826SW"/>
     <cge:Meas_Ref ObjectId="218995"/>
    <cge:TPSR_Ref TObjectID="34074"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218994">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1640.000000 -320.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34073" ObjectName="SW-MD_SMC.MD_SMC_3821SW"/>
     <cge:Meas_Ref ObjectId="218994"/>
    <cge:TPSR_Ref TObjectID="34073"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134407">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1836.000000 -325.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34055" ObjectName="SW-MD_SMC.MD_SMC_3841SW"/>
     <cge:Meas_Ref ObjectId="134407"/>
    <cge:TPSR_Ref TObjectID="34055"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218997">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1588.000000 -486.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34071" ObjectName="SW-MD_SMC.MD_SMC_38267SW"/>
     <cge:Meas_Ref ObjectId="218997"/>
    <cge:TPSR_Ref TObjectID="34071"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134324">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1342.000000 -487.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34066" ObjectName="SW-MD_SMC.MD_SMC_38167SW"/>
     <cge:Meas_Ref ObjectId="134324"/>
    <cge:TPSR_Ref TObjectID="34066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218996">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1597.000000 -569.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34075" ObjectName="SW-MD_SMC.MD_SMC_3829SW"/>
     <cge:Meas_Ref ObjectId="218996"/>
    <cge:TPSR_Ref TObjectID="34075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218949">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1355.000000 -573.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34070" ObjectName="SW-MD_SMC.MD_SMC_3819SW"/>
     <cge:Meas_Ref ObjectId="218949"/>
    <cge:TPSR_Ref TObjectID="34070"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218972">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1929.000000 -332.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24422" ObjectName="SW-MD_SMC.MD_SMC_3901SW"/>
     <cge:Meas_Ref ObjectId="218972"/>
    <cge:TPSR_Ref TObjectID="24422"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218973">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1949.000000 -398.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24423" ObjectName="SW-MD_SMC.MD_SMC_39017SW"/>
     <cge:Meas_Ref ObjectId="218973"/>
    <cge:TPSR_Ref TObjectID="24423"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134327">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1820.000000 -248.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24425" ObjectName="SW-MD_SMC.MD_SMC_3011SW"/>
     <cge:Meas_Ref ObjectId="134327"/>
    <cge:TPSR_Ref TObjectID="24425"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-219014">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1149.000000 166.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34057" ObjectName="SW-MD_SMC.MD_SMC_0811SW"/>
     <cge:Meas_Ref ObjectId="219014"/>
    <cge:TPSR_Ref TObjectID="34057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-219015">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1149.000000 305.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34058" ObjectName="SW-MD_SMC.MD_SMC_0816SW"/>
     <cge:Meas_Ref ObjectId="219015"/>
    <cge:TPSR_Ref TObjectID="34058"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-219035">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2110.000000 164.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34060" ObjectName="SW-MD_SMC.MD_SMC_0861SW"/>
     <cge:Meas_Ref ObjectId="219035"/>
    <cge:TPSR_Ref TObjectID="34060"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-219036">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2110.000000 296.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34061" ObjectName="SW-MD_SMC.MD_SMC_0866SW"/>
     <cge:Meas_Ref ObjectId="219036"/>
    <cge:TPSR_Ref TObjectID="34061"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218965">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1387.000000 85.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34065" ObjectName="SW-MD_SMC.MD_SMC_0021SW"/>
     <cge:Meas_Ref ObjectId="218965"/>
    <cge:TPSR_Ref TObjectID="34065"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134334">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1820.000000 84.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24427" ObjectName="SW-MD_SMC.MD_SMC_0011SW"/>
     <cge:Meas_Ref ObjectId="134334"/>
    <cge:TPSR_Ref TObjectID="24427"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-MD_SMC.MD_SMC_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1018,97 2319,97 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24420" ObjectName="BS-MD_SMC.MD_SMC_9IM"/>
    <cge:TPSR_Ref TObjectID="24420"/></metadata>
   <polyline fill="none" opacity="0" points="1018,97 2319,97 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-MD_SMC.MD_SMC_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1241,-308 2010,-308 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24419" ObjectName="BS-MD_SMC.MD_SMC_3IM"/>
    <cge:TPSR_Ref TObjectID="24419"/></metadata>
   <polyline fill="none" opacity="0" points="1241,-308 2010,-308 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-MD_SMC.081Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1153.000000 405.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37701" ObjectName="EC-MD_SMC.081Ld"/>
    <cge:TPSR_Ref TObjectID="37701"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_SMC.082Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1344.000000 402.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34347" ObjectName="EC-MD_SMC.082Ld"/>
    <cge:TPSR_Ref TObjectID="34347"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_SMC.083Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1536.000000 397.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34308" ObjectName="EC-MD_SMC.083Ld"/>
    <cge:TPSR_Ref TObjectID="34308"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_SMC.084Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1728.000000 396.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34309" ObjectName="EC-MD_SMC.084Ld"/>
    <cge:TPSR_Ref TObjectID="34309"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_SMC.085Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1920.000000 398.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34310" ObjectName="EC-MD_SMC.085Ld"/>
    <cge:TPSR_Ref TObjectID="34310"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_SMC.086Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2114.000000 396.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37702" ObjectName="EC-MD_SMC.086Ld"/>
    <cge:TPSR_Ref TObjectID="37702"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2fed200" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1561.000000 -485.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30f45e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1315.000000 -486.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2facca0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2013.000000 -397.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_3476470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1396,-252 1396,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="34063@0" ObjectIDZND0="34062@1" Pin0InfoVect0LinkObjId="SW-218957_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218958_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1396,-252 1396,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f6cac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1144,67 1144,97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24428@0" ObjectIDZND0="24420@0" Pin0InfoVect0LinkObjId="g_348d1a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134402_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1144,67 1144,97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_348cc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1092,15 1092,23 1144,23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2f65cb0@0" ObjectIDZND0="24428@x" Pin0InfoVect0LinkObjId="SW-134402_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f65cb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1092,15 1092,23 1144,23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3437390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1144,31 1144,23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24428@1" ObjectIDZND0="g_2f65cb0@0" Pin0InfoVect0LinkObjId="g_2f65cb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134402_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1144,31 1144,23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_348d1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1061,120 1061,97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="24420@0" Pin0InfoVect0LinkObjId="g_2f6cac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1061,120 1061,97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2f6d940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1061,160 1061,191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1061,160 1061,191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_343c630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1396,-288 1396,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="34063@1" ObjectIDZND0="24419@0" Pin0InfoVect0LinkObjId="g_31b20b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218958_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1396,-288 1396,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ece7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1925,116 1925,97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24439@1" ObjectIDZND0="24420@0" Pin0InfoVect0LinkObjId="g_2f6cac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134474_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1925,116 1925,97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_304d5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1925,152 1925,186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24439@0" ObjectIDZND0="24438@1" Pin0InfoVect0LinkObjId="SW-134472_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134474_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1925,152 1925,186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fe1440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1925,213 1925,253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24438@0" ObjectIDZND0="24440@1" Pin0InfoVect0LinkObjId="SW-134475_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134472_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1925,213 1925,253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_358a480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1733,122 1733,97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24436@1" ObjectIDZND0="24420@0" Pin0InfoVect0LinkObjId="g_2f6cac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134451_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1733,122 1733,97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_42814c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1733,158 1733,191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24436@0" ObjectIDZND0="24435@1" Pin0InfoVect0LinkObjId="SW-134449_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134451_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1733,158 1733,191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f9b5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1733,218 1733,259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24435@0" ObjectIDZND0="24437@1" Pin0InfoVect0LinkObjId="SW-134452_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134449_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1733,218 1733,259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fddc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1541,124 1541,97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24433@1" ObjectIDZND0="24420@0" Pin0InfoVect0LinkObjId="g_2f6cac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134428_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1541,124 1541,97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_218f790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1541,160 1541,194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24433@0" ObjectIDZND0="24432@1" Pin0InfoVect0LinkObjId="SW-134426_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134428_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1541,160 1541,194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_218f1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1541,221 1541,261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24432@0" ObjectIDZND0="24434@1" Pin0InfoVect0LinkObjId="SW-134429_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134426_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1541,221 1541,261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d6d770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1349,127 1349,97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24430@1" ObjectIDZND0="24420@0" Pin0InfoVect0LinkObjId="g_2f6cac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134405_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1349,127 1349,97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_427ae90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1349,163 1349,197 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24430@0" ObjectIDZND0="24429@1" Pin0InfoVect0LinkObjId="SW-134403_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134405_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1349,163 1349,197 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3075d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1349,224 1349,264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24429@0" ObjectIDZND0="24431@1" Pin0InfoVect0LinkObjId="SW-134406_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134403_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1349,224 1349,264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fb6ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1349,327 1349,381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="24431@x" ObjectIDND1="g_1e93ca0@0" ObjectIDZND0="34347@0" Pin0InfoVect0LinkObjId="EC-MD_SMC.082Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-134406_0" Pin1InfoVect1LinkObjId="g_1e93ca0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1349,327 1349,381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4022d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1349,300 1349,327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24431@0" ObjectIDZND0="g_1e93ca0@0" ObjectIDZND1="34347@x" Pin0InfoVect0LinkObjId="g_1e93ca0_0" Pin0InfoVect1LinkObjId="EC-MD_SMC.082Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134406_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1349,300 1349,327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fb7750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1349,327 1402,327 1402,342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="24431@x" ObjectIDND1="34347@x" ObjectIDZND0="g_1e93ca0@0" Pin0InfoVect0LinkObjId="g_1e93ca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-134406_0" Pin1InfoVect1LinkObjId="EC-MD_SMC.082Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1349,327 1402,327 1402,342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40224c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1541,321 1541,376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="24434@x" ObjectIDND1="g_33248e0@0" ObjectIDZND0="34308@0" Pin0InfoVect0LinkObjId="EC-MD_SMC.083Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-134429_0" Pin1InfoVect1LinkObjId="g_33248e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1541,321 1541,376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fb85b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1541,297 1541,321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24434@0" ObjectIDZND0="g_33248e0@0" ObjectIDZND1="34308@x" Pin0InfoVect0LinkObjId="g_33248e0_0" Pin0InfoVect1LinkObjId="EC-MD_SMC.083Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134429_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1541,297 1541,321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fb8e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1541,321 1596,321 1596,341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="24434@x" ObjectIDND1="34308@x" ObjectIDZND0="g_33248e0@0" Pin0InfoVect0LinkObjId="g_33248e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-134429_0" Pin1InfoVect1LinkObjId="EC-MD_SMC.083Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1541,321 1596,321 1596,341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fb7fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1733,318 1733,375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="24437@x" ObjectIDND1="g_3f79580@0" ObjectIDZND0="34309@0" Pin0InfoVect0LinkObjId="EC-MD_SMC.084Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-134452_0" Pin1InfoVect1LinkObjId="g_3f79580_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1733,318 1733,375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fb96b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1733,295 1733,318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24437@0" ObjectIDZND0="g_3f79580@0" ObjectIDZND1="34309@x" Pin0InfoVect0LinkObjId="g_3f79580_0" Pin0InfoVect1LinkObjId="EC-MD_SMC.084Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134452_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1733,295 1733,318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3081270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1733,318 1788,318 1788,339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="24437@x" ObjectIDND1="34309@x" ObjectIDZND0="g_3f79580@0" Pin0InfoVect0LinkObjId="g_3f79580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-134452_0" Pin1InfoVect1LinkObjId="EC-MD_SMC.084Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1733,318 1788,318 1788,339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_307eec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1925,319 1925,377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="24440@x" ObjectIDND1="g_2dff0e0@0" ObjectIDZND0="34310@0" Pin0InfoVect0LinkObjId="EC-MD_SMC.085Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-134475_0" Pin1InfoVect1LinkObjId="g_2dff0e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1925,319 1925,377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3080a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1925,289 1925,319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24440@0" ObjectIDZND0="g_2dff0e0@0" ObjectIDZND1="34310@x" Pin0InfoVect0LinkObjId="g_2dff0e0_0" Pin0InfoVect1LinkObjId="EC-MD_SMC.085Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134475_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1925,289 1925,319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3fba770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1925,319 1981,319 1981,335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="24440@x" ObjectIDND1="34310@x" ObjectIDZND0="g_2dff0e0@0" Pin0InfoVect0LinkObjId="g_2dff0e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-134475_0" Pin1InfoVect1LinkObjId="EC-MD_SMC.085Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1925,319 1981,319 1981,335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3096f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2119,123 2119,97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="34060@1" ObjectIDZND0="24420@0" Pin0InfoVect0LinkObjId="g_2f6cac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-219035_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2119,123 2119,97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1effa80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1144,23 1144,4 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="24428@x" ObjectIDND1="g_2f65cb0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-134402_0" Pin1InfoVect1LinkObjId="g_2f65cb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1144,23 1144,4 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3f3e300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1144,-37 1144,-61 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_162ec40@0" Pin0InfoVect0LinkObjId="g_162ec40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1144,-37 1144,-61 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3439e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1396,-174 1372,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="34062@x" ObjectIDND1="34129@x" ObjectIDZND0="g_20c3de0@0" Pin0InfoVect0LinkObjId="g_20c3de0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-218957_0" Pin1InfoVect1LinkObjId="g_2f992d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1396,-174 1372,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3470be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1396,-192 1396,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="34062@0" ObjectIDZND0="g_20c3de0@0" ObjectIDZND1="34129@x" Pin0InfoVect0LinkObjId="g_20c3de0_0" Pin0InfoVect1LinkObjId="g_2f992d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218957_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1396,-192 1396,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2efd4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1845,-472 1845,-521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1845,-472 1845,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e17d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1407,-410 1407,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="34067@1" ObjectIDZND0="34069@0" Pin0InfoVect0LinkObjId="SW-218948_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218950_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1407,-410 1407,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ef16e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1407,-308 1407,-326 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24419@0" ObjectIDZND0="34068@0" Pin0InfoVect0LinkObjId="SW-134323_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_343c630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1407,-308 1407,-326 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30475d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1407,-362 1407,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="34068@1" ObjectIDZND0="34067@0" Pin0InfoVect0LinkObjId="SW-218950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134323_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1407,-362 1407,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fc5590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1649,-409 1649,-431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="34072@1" ObjectIDZND0="34074@0" Pin0InfoVect0LinkObjId="SW-218995_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218993_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1649,-409 1649,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f3f960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1649,-308 1649,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24419@0" ObjectIDZND0="34073@0" Pin0InfoVect0LinkObjId="SW-218994_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_343c630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1649,-308 1649,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_188ffd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1649,-361 1649,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="34073@1" ObjectIDZND0="34072@0" Pin0InfoVect0LinkObjId="SW-218993_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218994_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1649,-361 1649,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30efba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1845,-420 1845,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_339d040@0" ObjectIDZND0="34055@1" Pin0InfoVect0LinkObjId="SW-134407_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_339d040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1845,-420 1845,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_31b20b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1845,-330 1845,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="34055@0" ObjectIDZND0="24419@0" Pin0InfoVect0LinkObjId="g_343c630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134407_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1845,-330 1845,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dff390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1649,-467 1649,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="34074@1" ObjectIDZND0="g_2dff580@0" ObjectIDZND1="34075@x" ObjectIDZND2="37803@1" Pin0InfoVect0LinkObjId="g_2dff580_0" Pin0InfoVect1LinkObjId="SW-218996_0" Pin0InfoVect2LinkObjId="g_2fa6320_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218995_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1649,-467 1649,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4069400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1649,-491 1713,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="34074@x" ObjectIDND1="34075@x" ObjectIDND2="37803@1" ObjectIDZND0="g_2dff580@0" Pin0InfoVect0LinkObjId="g_2dff580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-218995_0" Pin1InfoVect1LinkObjId="SW-218996_0" Pin1InfoVect2LinkObjId="g_2fa6320_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1649,-491 1713,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fef710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1407,-468 1407,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="34069@1" ObjectIDZND0="g_343caa0@0" ObjectIDZND1="34070@x" ObjectIDZND2="37789@1" Pin0InfoVect0LinkObjId="g_343caa0_0" Pin0InfoVect1LinkObjId="SW-218949_0" Pin0InfoVect2LinkObjId="g_2ff46b0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218948_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1407,-468 1407,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_30cb9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1649,-491 1629,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2dff580@0" ObjectIDND1="34074@x" ObjectIDND2="34075@x" ObjectIDZND0="34071@1" Pin0InfoVect0LinkObjId="SW-218997_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2dff580_0" Pin1InfoVect1LinkObjId="SW-218995_0" Pin1InfoVect2LinkObjId="SW-218996_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1649,-491 1629,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ff1ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1593,-491 1579,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="34071@0" ObjectIDZND0="g_2fed200@0" Pin0InfoVect0LinkObjId="g_2fed200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218997_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1593,-491 1579,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_343c8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1404,-492 1467,-492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="34069@x" ObjectIDND1="34070@x" ObjectIDND2="37789@1" ObjectIDZND0="g_343caa0@0" Pin0InfoVect0LinkObjId="g_343caa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-218948_0" Pin1InfoVect1LinkObjId="SW-218949_0" Pin1InfoVect2LinkObjId="g_2ff46b0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1404,-492 1467,-492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_339c070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1404,-492 1383,-492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_343caa0@0" ObjectIDND1="34069@x" ObjectIDND2="34070@x" ObjectIDZND0="34066@1" Pin0InfoVect0LinkObjId="SW-134324_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_343caa0_0" Pin1InfoVect1LinkObjId="SW-218948_0" Pin1InfoVect2LinkObjId="SW-218949_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1404,-492 1383,-492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3dd9b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1347,-492 1333,-492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="34066@0" ObjectIDZND0="g_30f45e0@0" Pin0InfoVect0LinkObjId="g_30f45e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134324_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1347,-492 1333,-492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fa60d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1649,-491 1649,-560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="g_2dff580@0" ObjectIDND1="34074@x" ObjectIDND2="34071@x" ObjectIDZND0="34075@x" ObjectIDZND1="37803@1" Pin0InfoVect0LinkObjId="SW-218996_0" Pin0InfoVect1LinkObjId="g_2fa6320_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2dff580_0" Pin1InfoVect1LinkObjId="SW-218995_0" Pin1InfoVect2LinkObjId="SW-218997_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1649,-491 1649,-560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fa6320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1649,-560 1649,-600 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2dff580@0" ObjectIDND1="34074@x" ObjectIDND2="34071@x" ObjectIDZND0="37803@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2dff580_0" Pin1InfoVect1LinkObjId="SW-218995_0" Pin1InfoVect2LinkObjId="SW-218997_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1649,-560 1649,-600 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ffed20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1649,-560 1606,-560 1606,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2dff580@0" ObjectIDND1="34074@x" ObjectIDND2="34071@x" ObjectIDZND0="34075@0" Pin0InfoVect0LinkObjId="SW-218996_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2dff580_0" Pin1InfoVect1LinkObjId="SW-218995_0" Pin1InfoVect2LinkObjId="SW-218997_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1649,-560 1606,-560 1606,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c62320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1606,-610 1606,-649 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="34075@1" ObjectIDZND0="g_2012cb0@0" Pin0InfoVect0LinkObjId="g_2012cb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218996_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1606,-610 1606,-649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3354890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1364,-614 1364,-655 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="34070@1" ObjectIDZND0="g_2f38d00@0" Pin0InfoVect0LinkObjId="g_2f38d00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218949_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1364,-614 1364,-655 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f39570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1407,-564 1364,-564 1364,-578 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_343caa0@0" ObjectIDND1="34069@x" ObjectIDND2="34066@x" ObjectIDZND0="34070@0" Pin0InfoVect0LinkObjId="SW-218949_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_343caa0_0" Pin1InfoVect1LinkObjId="SW-218948_0" Pin1InfoVect2LinkObjId="SW-134324_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1407,-564 1364,-564 1364,-578 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3256f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1407,-490 1407,-564 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="g_343caa0@0" ObjectIDND1="34069@x" ObjectIDND2="34066@x" ObjectIDZND0="34070@x" ObjectIDZND1="37789@1" Pin0InfoVect0LinkObjId="SW-218949_0" Pin0InfoVect1LinkObjId="g_2ff46b0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_343caa0_0" Pin1InfoVect1LinkObjId="SW-218948_0" Pin1InfoVect2LinkObjId="SW-134324_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1407,-490 1407,-564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f77690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1938,-421 1898,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1e52250@0" ObjectIDND1="24422@x" ObjectIDND2="24423@x" ObjectIDZND0="g_2fc1070@0" Pin0InfoVect0LinkObjId="g_2fc1070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1e52250_0" Pin1InfoVect1LinkObjId="SW-218972_0" Pin1InfoVect2LinkObjId="SW-218973_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1938,-421 1898,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2fc0e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1938,-421 1989,-421 1989,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_2fc1070@0" ObjectIDND1="24422@x" ObjectIDND2="24423@x" ObjectIDZND0="g_1e52250@0" Pin0InfoVect0LinkObjId="g_1e52250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2fc1070_0" Pin1InfoVect1LinkObjId="SW-218972_0" Pin1InfoVect2LinkObjId="SW-218973_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1938,-421 1989,-421 1989,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_353e7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1938,-403 1938,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="24422@x" ObjectIDND1="24423@x" ObjectIDZND0="g_1e52250@0" ObjectIDZND1="g_2fc1070@0" Pin0InfoVect0LinkObjId="g_1e52250_0" Pin0InfoVect1LinkObjId="g_2fc1070_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-218972_0" Pin1InfoVect1LinkObjId="SW-218973_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1938,-403 1938,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33830f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1938,-308 1938,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24419@0" ObjectIDZND0="24422@0" Pin0InfoVect0LinkObjId="SW-218972_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_343c630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1938,-308 1938,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3325c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1938,-373 1938,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="24422@1" ObjectIDZND0="g_1e52250@0" ObjectIDZND1="g_2fc1070@0" ObjectIDZND2="24423@x" Pin0InfoVect0LinkObjId="g_1e52250_0" Pin0InfoVect1LinkObjId="g_2fc1070_0" Pin0InfoVect2LinkObjId="SW-218973_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218972_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1938,-373 1938,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fde900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1938,-403 1954,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="24422@x" ObjectIDND1="g_1e52250@0" ObjectIDND2="g_2fc1070@0" ObjectIDZND0="24423@0" Pin0InfoVect0LinkObjId="SW-218973_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-218972_0" Pin1InfoVect1LinkObjId="g_1e52250_0" Pin1InfoVect2LinkObjId="g_2fc1070_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1938,-403 1954,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2faca40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1990,-403 2017,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24423@1" ObjectIDZND0="g_2facca0@0" Pin0InfoVect0LinkObjId="g_2facca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218973_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1990,-403 2017,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_427b2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1829,-229 1829,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24424@1" ObjectIDZND0="24425@0" Pin0InfoVect0LinkObjId="SW-134327_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134325_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1829,-229 1829,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e10ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1829,-289 1829,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24425@1" ObjectIDZND0="24419@0" Pin0InfoVect0LinkObjId="g_343c630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134327_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1829,-289 1829,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30805b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1158,125 1158,97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="34057@1" ObjectIDZND0="24420@0" Pin0InfoVect0LinkObjId="g_2f6cac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-219014_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1158,125 1158,97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e04e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1158,264 1158,225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="34058@1" ObjectIDZND0="34056@0" Pin0InfoVect0LinkObjId="SW-219013_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-219015_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1158,264 1158,225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e05680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1158,198 1158,161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="34056@1" ObjectIDZND0="34057@0" Pin0InfoVect0LinkObjId="SW-219014_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-219013_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1158,198 1158,161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e03fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1158,324 1214,324 1214,350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="37701@x" ObjectIDND1="34058@x" ObjectIDZND0="g_2e07b20@0" Pin0InfoVect0LinkObjId="g_2e07b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-MD_SMC.081Ld_0" Pin1InfoVect1LinkObjId="SW-219015_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1158,324 1214,324 1214,350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e03760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1158,384 1158,324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="37701@0" ObjectIDZND0="g_2e07b20@0" ObjectIDZND1="34058@x" Pin0InfoVect0LinkObjId="g_2e07b20_0" Pin0InfoVect1LinkObjId="SW-219015_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-MD_SMC.081Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1158,384 1158,324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e078c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1158,324 1158,300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="37701@x" ObjectIDND1="g_2e07b20@0" ObjectIDZND0="34058@0" Pin0InfoVect0LinkObjId="SW-219015_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-MD_SMC.081Ld_0" Pin1InfoVect1LinkObjId="g_2e07b20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1158,324 1158,300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3096370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2119,159 2119,185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="34060@0" ObjectIDZND0="34059@1" Pin0InfoVect0LinkObjId="SW-219034_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-219035_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2119,159 2119,185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ff5400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2119,212 2119,255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="34059@0" ObjectIDZND0="34061@1" Pin0InfoVect0LinkObjId="SW-219036_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-219034_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2119,212 2119,255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20ca4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2119,320 2183,320 2183,337 2183,336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="34061@x" ObjectIDND1="37702@x" ObjectIDZND0="g_3470e90@0" Pin0InfoVect0LinkObjId="g_3470e90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-219036_0" Pin1InfoVect1LinkObjId="EC-MD_SMC.086Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2119,320 2183,320 2183,337 2183,336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20ca740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2119,291 2119,320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="34061@0" ObjectIDZND0="g_3470e90@0" ObjectIDZND1="37702@x" Pin0InfoVect0LinkObjId="g_3470e90_0" Pin0InfoVect1LinkObjId="EC-MD_SMC.086Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-219036_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2119,291 2119,320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_343a180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2119,320 2119,375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="34061@x" ObjectIDND1="g_3470e90@0" ObjectIDZND0="37702@0" Pin0InfoVect0LinkObjId="EC-MD_SMC.086Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-219036_0" Pin1InfoVect1LinkObjId="g_3470e90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2119,320 2119,375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f992d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1396,-174 1396,-110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="34062@x" ObjectIDND1="g_20c3de0@0" ObjectIDZND0="34129@1" Pin0InfoVect0LinkObjId="g_3003fb0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-218957_0" Pin1InfoVect1LinkObjId="g_20c3de0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1396,-174 1396,-110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3003fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1396,-15 1396,-30 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="34064@1" ObjectIDZND0="34129@0" Pin0InfoVect0LinkObjId="g_2f992d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218964_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1396,-15 1396,-30 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3257770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1396,97 1396,80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24420@0" ObjectIDZND0="34065@0" Pin0InfoVect0LinkObjId="SW-218965_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f6cac0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1396,97 1396,80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_407b9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1396,44 1396,12 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="34065@1" ObjectIDZND0="34064@0" Pin0InfoVect0LinkObjId="SW-218964_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-218965_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1396,44 1396,12 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3565290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1829,-26 1829,-50 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="24426@1" ObjectIDZND0="34128@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134332_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1829,-26 1829,-50 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30eeda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1829,97 1829,79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="24420@0" ObjectIDZND0="24427@0" Pin0InfoVect0LinkObjId="SW-134334_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2f6cac0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1829,97 1829,79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f86c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1829,43 1829,1 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24427@1" ObjectIDZND0="24426@0" Pin0InfoVect0LinkObjId="SW-134332_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134334_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1829,43 1829,1 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33255c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1829,-129 1829,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="34128@1" ObjectIDZND0="24424@x" ObjectIDZND1="g_16da980@0" Pin0InfoVect0LinkObjId="SW-134325_0" Pin0InfoVect1LinkObjId="g_16da980_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3565290_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1829,-129 1829,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16da720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1829,-186 1829,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="34128@x" ObjectIDND1="g_16da980@0" ObjectIDZND0="24424@0" Pin0InfoVect0LinkObjId="SW-134325_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3565290_0" Pin1InfoVect1LinkObjId="g_16da980_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1829,-186 1829,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3fbae00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1829,-186 1805,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="24424@x" ObjectIDND1="34128@x" ObjectIDZND0="g_16da980@0" Pin0InfoVect0LinkObjId="g_16da980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-134325_0" Pin1InfoVect1LinkObjId="g_3565290_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1829,-186 1805,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ff46b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1407,-564 1407,-599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_343caa0@0" ObjectIDND1="34069@x" ObjectIDND2="34066@x" ObjectIDZND0="37789@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_343caa0_0" Pin1InfoVect1LinkObjId="SW-218948_0" Pin1InfoVect2LinkObjId="SW-134324_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1407,-564 1407,-599 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="24420" cx="1144" cy="97" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24419" cx="1396" cy="-308" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24420" cx="1925" cy="97" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24420" cx="1733" cy="97" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24420" cx="1541" cy="97" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24420" cx="1349" cy="97" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24420" cx="2119" cy="97" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24419" cx="1407" cy="-308" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24419" cx="1649" cy="-308" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24419" cx="1938" cy="-308" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24420" cx="1158" cy="97" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24420" cx="1396" cy="97" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24420" cx="1829" cy="97" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24419" cx="1845" cy="-308" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24419" cx="1829" cy="-308" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24420" cx="1061" cy="97" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-134212" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 838.000000 -536.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24405" ObjectName="DYN-MD_SMC"/>
     <cge:Meas_Ref ObjectId="134212"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimHei" font-size="20" graphid="g_1fe32b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 685.000000 -614.500000) translate(0,16)">飒马场变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35576a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -473.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35576a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -473.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35576a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -473.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35576a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -473.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35576a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -473.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35576a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -473.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35576a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -473.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35576a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -473.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_35576a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -473.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34988a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -35.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34988a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -35.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34988a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -35.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34988a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -35.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34988a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -35.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34988a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -35.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34988a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -35.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34988a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -35.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34988a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -35.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34988a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -35.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34988a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -35.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34988a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -35.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34988a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -35.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34988a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -35.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34988a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -35.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34988a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -35.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34988a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -35.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34988a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -35.000000) translate(0,374)">联系方式：5371197</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_34988a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.000000 -35.000000) translate(0,395)">                 2256</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339ce40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1220.000000 -97.000000) translate(0,12)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339ce40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1220.000000 -97.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339ce40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1220.000000 -97.000000) translate(0,42)">yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_339ce40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1220.000000 -97.000000) translate(0,57)">Ud=7.35%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ec0e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1202.000000 -295.000000) translate(0,12)">35kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6d020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1387.000000 -679.000000) translate(0,12)">35kV飒马场线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fbfbf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1084.000000 -138.000000) translate(0,12)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fbfbf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1084.000000 -138.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1567c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1030.000000 290.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30442e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1897.000000 413.000000) translate(0,12)">10kV松川线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f89d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1708.333333 413.000000) translate(0,12)">10kV凤屯线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3fdcb60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1500.666667 413.000000) translate(0,12)">10kV牌坊线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3076350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1317.000000 413.000000) translate(0,12)">10kV青龙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2f30830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 811.000000 -596.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2f2f4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 811.000000 -631.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3093e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1106.000000 413.000000) translate(0,12)">10kV青龙Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e053d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2076.000000 413.000000) translate(0,12)">10kV化佛山线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f427c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1650.000000 -98.000000) translate(0,12)">SZ11-2000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f427c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1650.000000 -98.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f427c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1650.000000 -98.000000) translate(0,42)">yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f427c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1650.000000 -98.000000) translate(0,57)">Ud=7.02%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ff5710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1633.000000 -675.000000) translate(0,12)">35kV凤飒线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4069f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1910.000000 -533.000000) translate(0,12)">35kVⅠ段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fa5ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1416.000000 -404.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f681c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1414.000000 -351.000000) translate(0,12)">3811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_302ccb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1414.000000 -457.000000) translate(0,12)">3816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dddfb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1657.000000 -350.000000) translate(0,12)">3821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ddcb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1659.000000 -403.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ddd590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1657.000000 -456.000000) translate(0,12)">3826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3499480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1852.000000 -355.000000) translate(0,12)">3841</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20c0e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1852.000000 -355.000000) translate(0,12)">3841</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_402ab80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1588.000000 -517.000000) translate(0,12)">38267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_40214a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1356.000000 -518.000000) translate(0,12)">38167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c62580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1559.000000 -600.000000) translate(0,12)">3829</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3257160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1321.000000 -603.000000) translate(0,12)">3819</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1eb7ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1945.000000 -362.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d307b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1953.000000 -396.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e11110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1838.000000 -223.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6da20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1836.000000 -278.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6dc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1406.000000 -214.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217d610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 985.000000 108.000000) translate(0,12)">10kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217d850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1151.000000 42.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217dea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1164.000000 134.000000) translate(0,12)">0811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217e0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1165.000000 270.000000) translate(0,12)">0816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217c4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1362.000000 203.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217c730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1356.000000 275.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217cd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1356.000000 138.000000) translate(0,12)">0821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217cfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1553.000000 200.000000) translate(0,12)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217bc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1548.000000 135.000000) translate(0,12)">0831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217be90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1548.000000 272.000000) translate(0,12)">0836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f5d8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1746.000000 197.000000) translate(0,12)">084</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f5dad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1740.000000 270.000000) translate(0,12)">0846</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217e7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1740.000000 133.000000) translate(0,12)">0841</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_217e9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1938.000000 192.000000) translate(0,12)">085</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f5e9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1935.000000 264.000000) translate(0,12)">0856</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f5e0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1932.000000 127.000000) translate(0,12)">0851</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f5e310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2135.000000 135.000000) translate(0,12)">0861</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4022770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2127.000000 265.000000) translate(0,12)">0866</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_343a3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1405.000000 -9.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_307f170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 564.000000 -225.000000) translate(0,18)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f30250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1171.000000 204.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_354d290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2131.000000 191.000000) translate(0,12)">086</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1fed960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1438.000000 -101.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f2d710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1865.000000 -106.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f2da90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1838.000000 -20.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f3f980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1836.000000 54.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f3fbc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1403.000000 55.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f3fe00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1405.000000 -9.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_340e140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1403.000000 -277.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2ff5270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 935.500000 -616.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b3b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1807.000000 -555.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2063280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 530.000000 377.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2063280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 530.000000 377.000000) translate(0,38)">心变运二班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_20634c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 650.000000 368.000000) translate(0,16)">13508785260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_20634c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 650.000000 368.000000) translate(0,36)">18787879001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_20634c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 650.000000 368.000000) translate(0,56)">18787879002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2063730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 910.000000 14.500000) translate(0,12)">Uc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2063980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 910.000000 -0.750000) translate(0,12)">Ub(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2063b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 910.000000 -16.000000) translate(0,12)">Ua(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2063dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 922.000000 74.000000) translate(0,12)">F(Hz)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2064010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 902.000000 29.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2064250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 902.000000 45.000000) translate(0,12)">Ubc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2064490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 902.000000 60.000000) translate(0,12)">Uca(kV):</text>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1403,-167 1389,-167 1396,-155 1403,-167 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1403,-136 1389,-136 1396,-148 1403,-136 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1836,-175 1822,-175 1829,-163 1836,-175 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1836,-144 1822,-144 1829,-156 1836,-144 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1414,-515 1400,-515 1407,-527 1414,-515 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1414,-546 1400,-546 1407,-534 1414,-546 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1852,-486 1838,-486 1845,-498 1852,-486 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="rgb(60,120,255)" points="1848,-520 1842,-520 1845,-530 1848,-520 " stroke="rgb(60,120,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1657,-514 1643,-514 1650,-526 1657,-514 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1657,-545 1643,-545 1650,-533 1657,-545 " stroke="rgb(255,255,0)"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_162ec40">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1124.608696 -56.000000)" xlink:href="#voltageTransformer:shape79"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e52250">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1970.000000 -456.000000)" xlink:href="#voltageTransformer:shape79"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-MD_SMC.MD_SMC_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="49997"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1365.000000 -25.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1365.000000 -25.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="34129" ObjectName="TF-MD_SMC.MD_SMC_2T"/>
    <cge:TPSR_Ref TObjectID="34129"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-MD_SMC.MD_SMC_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="49993"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1798.000000 -44.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1798.000000 -44.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="34128" ObjectName="TF-MD_SMC.MD_SMC_1T"/>
    <cge:TPSR_Ref TObjectID="34128"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1046.000000 284.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1046.000000 284.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 640.000000 -566.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-226303" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 665.000000 -469.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226303" ObjectName="MD_SMC:MD_SMC_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-226303" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 666.000000 -430.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226303" ObjectName="MD_SMC:MD_SMC_sumP"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="652" y="-625"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="652" y="-625"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="603" y="-642"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="603" y="-642"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="800" y="-604"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="800" y="-604"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="800" y="-639"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="800" y="-639"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="26" qtmmishow="hidden" width="89" x="562" y="-229"/>
    </a>
   <metadata/><rect fill="white" height="26" opacity="0" stroke="white" transform="" width="89" x="562" y="-229"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1171" y="204"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1171" y="204"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1362" y="203"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1362" y="203"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1553" y="200"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1553" y="200"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1746" y="197"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1746" y="197"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1938" y="192"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1938" y="192"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2131" y="191"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2131" y="191"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="1438" y="-101"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="1438" y="-101"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="1865" y="-106"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="1865" y="-106"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1416" y="-404"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1416" y="-404"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1659" y="-403"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1659" y="-403"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="919" y="-628"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="919" y="-628"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_307dde0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1437.000000 63.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ff00c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1437.000000 78.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e034b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1079.000000 -430.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f31370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1068.000000 -446.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e04b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1093.000000 -460.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1efed30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1469.000000 204.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35b18f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1458.000000 189.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35b1640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1483.000000 174.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dd5bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1867.000000 70.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dd4c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1867.000000 85.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e44e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1895.000000 231.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b1db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1884.000000 216.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20c5220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1909.000000 201.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ddf2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1465.000000 422.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ddf510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1454.000000 406.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ddf750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1479.000000 392.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ddfa80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1708.000000 423.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b2630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1697.000000 407.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20b2870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1722.000000 393.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3489fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1192.000000 353.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_348a220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1200.000000 367.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_348a460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1200.000000 382.750000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_348a6a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1200.000000 398.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_348a8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1210.000000 335.000000) translate(0,12)">F(HZ)</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1165" x2="1159" y1="97" y2="100"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1165" x2="1159" y1="95" y2="98"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1153" x2="1159" y1="95" y2="98"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1153" x2="1159" y1="97" y2="100"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="1165" x2="1159" y1="97" y2="100"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fc25e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1460.000000 6.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fc27f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1449.000000 -9.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fc2a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1474.000000 -24.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fc2d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1898.000000 27.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fc2fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1887.000000 12.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2fc3200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1912.000000 -3.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dd7a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1270.000000 -434.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dd7f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1259.000000 -450.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dd81d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1284.000000 -464.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dd8500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1462.000000 -432.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dd8760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1451.000000 -448.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dd89a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1476.000000 -462.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dd8cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1656.000000 -430.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dd8f30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1645.000000 -446.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dd9170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1670.000000 -460.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3dd94a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1850.000000 -429.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2062630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1839.000000 -445.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2062870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1864.000000 -459.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2062ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2045.000000 -432.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2062e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2034.000000 -448.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2063040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2059.000000 -462.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_FT" endPointId="0" endStationName="MD_SMC" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_fengsa" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1649,-647 1649,-601 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37803" ObjectName="AC-35kV.LN_fengsa"/>
    <cge:TPSR_Ref TObjectID="37803_SS-202"/></metadata>
   <polyline fill="none" opacity="0" points="1649,-647 1649,-601 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_MD" endPointId="0" endStationName="MD_SMC" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_samachang" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1407,-647 1407,-601 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37789" ObjectName="AC-35kV.LN_samachang"/>
    <cge:TPSR_Ref TObjectID="37789_SS-202"/></metadata>
   <polyline fill="none" opacity="0" points="1407,-647 1407,-601 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-219121" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1510.000000 -78.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219121" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34129"/>
     <cge:Term_Ref ObjectID="49998"/>
    <cge:TPSR_Ref TObjectID="34129"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-219122" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1510.000000 -78.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219122" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34129"/>
     <cge:Term_Ref ObjectID="49998"/>
    <cge:TPSR_Ref TObjectID="34129"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-134239" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1934.000000 -83.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134239" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34128"/>
     <cge:Term_Ref ObjectID="49994"/>
    <cge:TPSR_Ref TObjectID="34128"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-134240" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1934.000000 -83.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134240" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34128"/>
     <cge:Term_Ref ObjectID="49994"/>
    <cge:TPSR_Ref TObjectID="34128"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-219106" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1525.000000 -205.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219106" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34062"/>
     <cge:Term_Ref ObjectID="49900"/>
    <cge:TPSR_Ref TObjectID="34062"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-219107" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1525.000000 -205.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219107" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34062"/>
     <cge:Term_Ref ObjectID="49900"/>
    <cge:TPSR_Ref TObjectID="34062"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-219103" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1525.000000 -205.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219103" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34062"/>
     <cge:Term_Ref ObjectID="49900"/>
    <cge:TPSR_Ref TObjectID="34062"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-134230" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1945.000000 -231.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134230" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24424"/>
     <cge:Term_Ref ObjectID="34459"/>
    <cge:TPSR_Ref TObjectID="24424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-134231" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1945.000000 -231.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134231" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24424"/>
     <cge:Term_Ref ObjectID="34459"/>
    <cge:TPSR_Ref TObjectID="24424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-134227" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1945.000000 -231.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134227" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24424"/>
     <cge:Term_Ref ObjectID="34459"/>
    <cge:TPSR_Ref TObjectID="24424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-219129" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1138.000000 433.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219129" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34056"/>
     <cge:Term_Ref ObjectID="49888"/>
    <cge:TPSR_Ref TObjectID="34056"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-219130" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1138.000000 433.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219130" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34056"/>
     <cge:Term_Ref ObjectID="49888"/>
    <cge:TPSR_Ref TObjectID="34056"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-219126" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1138.000000 433.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219126" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34056"/>
     <cge:Term_Ref ObjectID="49888"/>
    <cge:TPSR_Ref TObjectID="34056"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-134259" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1330.000000 435.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134259" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24429"/>
     <cge:Term_Ref ObjectID="34469"/>
    <cge:TPSR_Ref TObjectID="24429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-134260" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1330.000000 435.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134260" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24429"/>
     <cge:Term_Ref ObjectID="34469"/>
    <cge:TPSR_Ref TObjectID="24429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-134256" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1330.000000 435.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134256" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24429"/>
     <cge:Term_Ref ObjectID="34469"/>
    <cge:TPSR_Ref TObjectID="24429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-134265" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1524.000000 434.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134265" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24432"/>
     <cge:Term_Ref ObjectID="34475"/>
    <cge:TPSR_Ref TObjectID="24432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-134266" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1524.000000 434.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134266" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24432"/>
     <cge:Term_Ref ObjectID="34475"/>
    <cge:TPSR_Ref TObjectID="24432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-134262" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1524.000000 434.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134262" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24432"/>
     <cge:Term_Ref ObjectID="34475"/>
    <cge:TPSR_Ref TObjectID="24432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-134271" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1717.000000 432.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134271" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24435"/>
     <cge:Term_Ref ObjectID="34481"/>
    <cge:TPSR_Ref TObjectID="24435"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-134272" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1717.000000 432.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134272" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24435"/>
     <cge:Term_Ref ObjectID="34481"/>
    <cge:TPSR_Ref TObjectID="24435"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-134268" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1717.000000 432.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134268" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24435"/>
     <cge:Term_Ref ObjectID="34481"/>
    <cge:TPSR_Ref TObjectID="24435"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-134277" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1909.000000 432.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134277" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24438"/>
     <cge:Term_Ref ObjectID="34487"/>
    <cge:TPSR_Ref TObjectID="24438"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-134278" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1909.000000 432.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134278" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24438"/>
     <cge:Term_Ref ObjectID="34487"/>
    <cge:TPSR_Ref TObjectID="24438"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-134274" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1909.000000 432.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134274" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24438"/>
     <cge:Term_Ref ObjectID="34487"/>
    <cge:TPSR_Ref TObjectID="24438"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-219135" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2106.000000 431.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219135" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34059"/>
     <cge:Term_Ref ObjectID="49894"/>
    <cge:TPSR_Ref TObjectID="34059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-219136" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2106.000000 431.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219136" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34059"/>
     <cge:Term_Ref ObjectID="49894"/>
    <cge:TPSR_Ref TObjectID="34059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-219132" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2106.000000 431.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219132" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34059"/>
     <cge:Term_Ref ObjectID="49894"/>
    <cge:TPSR_Ref TObjectID="34059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-134241" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1260.000000 -395.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134241" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24419"/>
     <cge:Term_Ref ObjectID="34451"/>
    <cge:TPSR_Ref TObjectID="24419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-134242" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1260.000000 -395.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134242" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24419"/>
     <cge:Term_Ref ObjectID="34451"/>
    <cge:TPSR_Ref TObjectID="24419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-134243" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1260.000000 -395.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134243" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24419"/>
     <cge:Term_Ref ObjectID="34451"/>
    <cge:TPSR_Ref TObjectID="24419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-134244" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1260.000000 -395.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134244" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24419"/>
     <cge:Term_Ref ObjectID="34451"/>
    <cge:TPSR_Ref TObjectID="24419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-134247" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1260.000000 -395.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134247" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24419"/>
     <cge:Term_Ref ObjectID="34451"/>
    <cge:TPSR_Ref TObjectID="24419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-134254" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1522.000000 -423.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134254" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34067"/>
     <cge:Term_Ref ObjectID="49910"/>
    <cge:TPSR_Ref TObjectID="34067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-134311" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1522.000000 -423.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134311" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34067"/>
     <cge:Term_Ref ObjectID="49910"/>
    <cge:TPSR_Ref TObjectID="34067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-134318" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1522.000000 -423.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134318" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34067"/>
     <cge:Term_Ref ObjectID="49910"/>
    <cge:TPSR_Ref TObjectID="34067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-134314" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1764.000000 -423.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134314" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34072"/>
     <cge:Term_Ref ObjectID="49920"/>
    <cge:TPSR_Ref TObjectID="34072"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-219123" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1764.000000 -423.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219123" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34072"/>
     <cge:Term_Ref ObjectID="49920"/>
    <cge:TPSR_Ref TObjectID="34072"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-134298" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1764.000000 -423.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134298" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34072"/>
     <cge:Term_Ref ObjectID="49920"/>
    <cge:TPSR_Ref TObjectID="34072"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-134248" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 964.000000 -16.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134248" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24420"/>
     <cge:Term_Ref ObjectID="34452"/>
    <cge:TPSR_Ref TObjectID="24420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-134249" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 964.000000 -16.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134249" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24420"/>
     <cge:Term_Ref ObjectID="34452"/>
    <cge:TPSR_Ref TObjectID="24420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-134250" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 964.000000 -16.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134250" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24420"/>
     <cge:Term_Ref ObjectID="34452"/>
    <cge:TPSR_Ref TObjectID="24420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-134251" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 964.000000 -16.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134251" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24420"/>
     <cge:Term_Ref ObjectID="34452"/>
    <cge:TPSR_Ref TObjectID="24420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-134252" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 964.000000 -16.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134252" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24420"/>
     <cge:Term_Ref ObjectID="34452"/>
    <cge:TPSR_Ref TObjectID="24420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-134253" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 964.000000 -16.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134253" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24420"/>
     <cge:Term_Ref ObjectID="34452"/>
    <cge:TPSR_Ref TObjectID="24420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-134255" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 964.000000 -16.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134255" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24420"/>
     <cge:Term_Ref ObjectID="34452"/>
    <cge:TPSR_Ref TObjectID="24420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-219119" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1526.000000 -6.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219119" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34064"/>
     <cge:Term_Ref ObjectID="49904"/>
    <cge:TPSR_Ref TObjectID="34064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-219120" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1526.000000 -6.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219120" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34064"/>
     <cge:Term_Ref ObjectID="49904"/>
    <cge:TPSR_Ref TObjectID="34064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-219116" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1526.000000 -6.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219116" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34064"/>
     <cge:Term_Ref ObjectID="49904"/>
    <cge:TPSR_Ref TObjectID="34064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-134236" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1969.000000 -28.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134236" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24426"/>
     <cge:Term_Ref ObjectID="34463"/>
    <cge:TPSR_Ref TObjectID="24426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-134237" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1969.000000 -28.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134237" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24426"/>
     <cge:Term_Ref ObjectID="34463"/>
    <cge:TPSR_Ref TObjectID="24426"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-134233" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1969.000000 -28.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134233" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24426"/>
     <cge:Term_Ref ObjectID="34463"/>
    <cge:TPSR_Ref TObjectID="24426"/></metadata>
   </g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-218957">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1387.000000 -184.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34062" ObjectName="SW-MD_SMC.MD_SMC_302BK"/>
     <cge:Meas_Ref ObjectId="218957"/>
    <cge:TPSR_Ref TObjectID="34062"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134472">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1916.000000 222.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24438" ObjectName="SW-MD_SMC.MD_SMC_085BK"/>
     <cge:Meas_Ref ObjectId="134472"/>
    <cge:TPSR_Ref TObjectID="24438"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134449">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1724.333333 227.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24435" ObjectName="SW-MD_SMC.MD_SMC_084BK"/>
     <cge:Meas_Ref ObjectId="134449"/>
    <cge:TPSR_Ref TObjectID="24435"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134426">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1531.666667 230.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24432" ObjectName="SW-MD_SMC.MD_SMC_083BK"/>
     <cge:Meas_Ref ObjectId="134426"/>
    <cge:TPSR_Ref TObjectID="24432"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134403">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1340.000000 233.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24429" ObjectName="SW-MD_SMC.MD_SMC_082BK"/>
     <cge:Meas_Ref ObjectId="134403"/>
    <cge:TPSR_Ref TObjectID="24429"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218950">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1398.000000 -375.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34067" ObjectName="SW-MD_SMC.MD_SMC_381BK"/>
     <cge:Meas_Ref ObjectId="218950"/>
    <cge:TPSR_Ref TObjectID="34067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218993">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1640.000000 -374.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34072" ObjectName="SW-MD_SMC.MD_SMC_382BK"/>
     <cge:Meas_Ref ObjectId="218993"/>
    <cge:TPSR_Ref TObjectID="34072"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134325">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1820.000000 -194.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24424" ObjectName="SW-MD_SMC.MD_SMC_301BK"/>
     <cge:Meas_Ref ObjectId="134325"/>
    <cge:TPSR_Ref TObjectID="24424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-219013">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1149.000000 234.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34056" ObjectName="SW-MD_SMC.MD_SMC_081BK"/>
     <cge:Meas_Ref ObjectId="219013"/>
    <cge:TPSR_Ref TObjectID="34056"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-219034">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2110.000000 221.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34059" ObjectName="SW-MD_SMC.MD_SMC_086BK"/>
     <cge:Meas_Ref ObjectId="219034"/>
    <cge:TPSR_Ref TObjectID="34059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-218964">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1387.000000 21.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34064" ObjectName="SW-MD_SMC.MD_SMC_002BK"/>
     <cge:Meas_Ref ObjectId="218964"/>
    <cge:TPSR_Ref TObjectID="34064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134332">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1820.000000 10.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24426" ObjectName="SW-MD_SMC.MD_SMC_001BK"/>
     <cge:Meas_Ref ObjectId="134332"/>
    <cge:TPSR_Ref TObjectID="24426"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="652" y="-625"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="603" y="-642"/></g>
   <g href="cx_配调_配网接线图35_牟定.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="800" y="-604"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="800" y="-639"/></g>
   <g href="35kV飒马场变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="26" qtmmishow="hidden" width="89" x="562" y="-229"/></g>
   <g href="35kV飒马场变10kV青龙Ⅱ回线081断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1171" y="204"/></g>
   <g href="35kV飒马场变10kV青龙线082断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1362" y="203"/></g>
   <g href="35kV飒马场变10kV牌坊线083断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1553" y="200"/></g>
   <g href="35kV飒马场变10kV凤屯线084断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1746" y="197"/></g>
   <g href="35kV飒马场变10kV松川线085断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1938" y="192"/></g>
   <g href="35kV飒马场变10kV化佛山线086断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2131" y="191"/></g>
   <g href="35kV飒马场变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="1438" y="-101"/></g>
   <g href="35kV飒马场变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="1865" y="-106"/></g>
   <g href="35kV飒马场变35kV飒马场线381断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1416" y="-404"/></g>
   <g href="35kV飒马场变35kV凤飒线382断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1659" y="-403"/></g>
   <g href="AVC飒马场站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="919" y="-628"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2f65cb0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1084.608696 20.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2dff0e0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1988.000000 331.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f79580">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1795.333333 335.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33248e0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1602.666667 337.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e93ca0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1409.000000 338.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20c3de0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1315.000000 -167.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_339d040">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1829.000000 -496.000000)" xlink:href="#lightningRod:shape101"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2dff580">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1706.000000 -437.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_343caa0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1460.000000 -438.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2012cb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1592.000000 -697.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f38d00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1350.000000 -703.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fc1070">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1891.000000 -417.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e07b20">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1221.000000 346.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3470e90">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 2190.000000 331.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16da980">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1748.000000 -179.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="MD_SMC"/>
</svg>