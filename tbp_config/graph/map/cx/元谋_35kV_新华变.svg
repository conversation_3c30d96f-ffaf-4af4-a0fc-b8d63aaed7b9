<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-240" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-735 -1356 2057 1301">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape2_0">
    <rect height="22" stroke-width="2" width="14" x="-2" y="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="5" y1="26" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="5" y1="2" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="5" y1="26" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="5" y1="2" y2="4"/>
    <rect height="22" stroke-width="2" width="10" x="0" y="4"/>
    <rect height="23" stroke-width="2" width="10" x="0" y="3"/>
   </symbol>
   <symbol id="breaker2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="5" y1="26" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="5" y1="2" y2="4"/>
    <rect height="22" stroke-width="2" width="10" x="0" y="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="1" x2="9" y1="24" y2="9"/>
   </symbol>
   <symbol id="breaker2:shape2-UnNor2">
    <rect height="22" stroke-width="2" width="10" x="0" y="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="5" y1="26" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="5" y1="2" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="1" x2="9" y1="23" y2="8"/>
   </symbol>
   <symbol id="capacitor:shape50">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="2" x2="2" y1="42" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="28" x2="11" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="36" x2="21" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="47" x2="28" y1="60" y2="60"/>
    <polyline points="29,89 31,89 33,88 34,88 36,87 37,86 39,85 40,83 41,81 41,80 42,78 42,76 42,74 41,72 41,71 40,69 39,68 37,66 36,65 34,64 33,64 31,63 29,63 27,63 25,64 24,64 22,65 21,66 19,68 18,69 17,71 17,72 16,74 16,76 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="16" x2="28" y1="76" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="29" x2="29" y1="89" y2="97"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="15" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="55" y2="47"/>
    <polyline arcFlag="1" points="11,15 10,15 9,15 9,15 8,15 8,16 7,16 7,17 6,17 6,18 6,18 6,19 5,20 5,20 5,21 6,22 6,22 6,23 6,23 7,24 7,24 8,25 8,25 9,25 9,26 10,26 11,26 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,25 10,25 9,25 9,25 8,26 8,26 7,26 7,27 6,27 6,28 6,29 6,29 5,30 5,31 5,31 6,32 6,33 6,33 6,34 7,34 7,35 8,35 8,35 9,36 9,36 10,36 11,36 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,36 10,36 9,36 9,37 8,37 8,37 7,38 7,38 6,39 6,39 6,40 6,40 5,41 5,42 5,42 6,43 6,44 6,44 6,45 7,45 7,46 8,46 8,47 9,47 9,47 10,47 11,47 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="28" x2="28" y1="76" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="45" x2="12" y1="2" y2="2"/>
    <rect height="23" stroke-width="0.369608" width="12" x="41" y="29"/>
    <rect height="23" stroke-width="0.369608" width="12" x="22" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="28" x2="11" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="45" x2="45" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="12" x2="12" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="28" x2="28" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="36" x2="21" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="48" x2="45" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="51" x2="43" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="41" x2="53" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="47" y1="21" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="47" x2="47" y1="60" y2="35"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape30_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape13_0">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="38" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape13_1">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="46" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="29" y1="34" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape25_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="58" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape25_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="12,76 19,76 16,83 12,76 "/>
   </symbol>
   <symbol id="voltageTransformer:shape69">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="35" x2="35" y1="23" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="17" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="24" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="25" x2="25" y1="23" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="18" x2="18" y1="23" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="42" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="42" x2="42" y1="23" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="20" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="28" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="4" x2="4" y1="26" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="21" x2="19" y1="17" y2="15"/>
    <circle cx="29" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="28" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="31" x2="29" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="29" x2="29" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="26" x2="29" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7538" x1="28" x2="26" y1="6" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.755439" x1="30" x2="32" y1="6" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="32" x2="26" y1="3" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="8" x2="8" y1="24" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="21" x2="19" y1="6" y2="4"/>
    <circle cx="19" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="18" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="6" y2="4"/>
   </symbol>
   <symbol id="voltageTransformer:shape65">
    <ellipse cx="19" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="46" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="41" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="9" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="8" x2="8" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="11" x2="8" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="5" x2="8" y1="18" y2="20"/>
    <ellipse cx="19" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.246311" x1="5" x2="9" y1="7" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.245503" x1="5" x2="9" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.238574" x1="9" x2="9" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="41" y1="18" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="30" y2="38"/>
    <rect height="13" stroke-width="1" width="7" x="32" y="17"/>
    <ellipse cx="8" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <ellipse cx="8" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <polyline points="27,8 35,8 35,18 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="38" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="37" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="40" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="18" y2="20"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_31862d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3186ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31879e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3188920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3189bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_318a8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_318b020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_318bb40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_254bae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_254bae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_318eb50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_318eb50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31908e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31908e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_3191900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3193500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_31940f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3194eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31957f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3196eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3197ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3198350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3198b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3199bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_319a570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_319b060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_319ba20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_319ce40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_319d9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_319e9e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_319f620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31addf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31a0f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_31a2500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_31a3a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1311" width="2067" x="-740" y="-1361"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="1313" x2="1322" y1="-746" y2="-746"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-215562">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 358.819337 -1140.220669)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32081" ObjectName="SW-YM_XH.YM_XH_35167SW"/>
     <cge:Meas_Ref ObjectId="215562"/>
    <cge:TPSR_Ref TObjectID="32081"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215559">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 302.000000 -1091.220669)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32078" ObjectName="SW-YM_XH.YM_XH_3516SW"/>
     <cge:Meas_Ref ObjectId="215559"/>
    <cge:TPSR_Ref TObjectID="32078"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215561">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 357.819337 -1055.220669)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32080" ObjectName="SW-YM_XH.YM_XH_35160SW"/>
     <cge:Meas_Ref ObjectId="215561"/>
    <cge:TPSR_Ref TObjectID="32080"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215563">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 359.819337 -977.220669)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32082" ObjectName="SW-YM_XH.YM_XH_35117SW"/>
     <cge:Meas_Ref ObjectId="215563"/>
    <cge:TPSR_Ref TObjectID="32082"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215560">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 302.000000 -915.220669)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32079" ObjectName="SW-YM_XH.YM_XH_3511SW"/>
     <cge:Meas_Ref ObjectId="215560"/>
    <cge:TPSR_Ref TObjectID="32079"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215573">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 411.000000 -887.220669)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32084" ObjectName="SW-YM_XH.YM_XH_3121SW"/>
     <cge:Meas_Ref ObjectId="215573"/>
    <cge:TPSR_Ref TObjectID="32084"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215575">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 499.000000 -915.220669)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32086" ObjectName="SW-YM_XH.YM_XH_31217SW"/>
     <cge:Meas_Ref ObjectId="215575"/>
    <cge:TPSR_Ref TObjectID="32086"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215572">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 302.000000 -825.220669)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32083" ObjectName="SW-YM_XH.YM_XH_3011SW"/>
     <cge:Meas_Ref ObjectId="215572"/>
    <cge:TPSR_Ref TObjectID="32083"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215574">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 357.819337 -801.220669)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32085" ObjectName="SW-YM_XH.YM_XH_30117SW"/>
     <cge:Meas_Ref ObjectId="215574"/>
    <cge:TPSR_Ref TObjectID="32085"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215795">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -45.000000 -289.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32113" ObjectName="SW-YM_XH.YM_XH_0546SW"/>
     <cge:Meas_Ref ObjectId="215795"/>
    <cge:TPSR_Ref TObjectID="32113"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215794">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.819337 -429.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32112" ObjectName="SW-YM_XH.YM_XH_05417SW"/>
     <cge:Meas_Ref ObjectId="215794"/>
    <cge:TPSR_Ref TObjectID="32112"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215912">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -232.000000 -339.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32128" ObjectName="SW-YM_XH.YM_XH_0556SW"/>
     <cge:Meas_Ref ObjectId="215912"/>
    <cge:TPSR_Ref TObjectID="32128"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215914">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -233.000000 -149.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32130" ObjectName="SW-YM_XH.YM_XH_05500SW"/>
     <cge:Meas_Ref ObjectId="215914"/>
    <cge:TPSR_Ref TObjectID="32130"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215911">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -192.180663 -462.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32127" ObjectName="SW-YM_XH.YM_XH_05517SW"/>
     <cge:Meas_Ref ObjectId="215911"/>
    <cge:TPSR_Ref TObjectID="32127"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215913">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -189.180663 -309.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32129" ObjectName="SW-YM_XH.YM_XH_05567SW"/>
     <cge:Meas_Ref ObjectId="215913"/>
    <cge:TPSR_Ref TObjectID="32129"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215756">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 157.000000 -321.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32108" ObjectName="SW-YM_XH.YM_XH_0536SW"/>
     <cge:Meas_Ref ObjectId="215756"/>
    <cge:TPSR_Ref TObjectID="32108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215755">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 206.819337 -441.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32107" ObjectName="SW-YM_XH.YM_XH_05317SW"/>
     <cge:Meas_Ref ObjectId="215755"/>
    <cge:TPSR_Ref TObjectID="32107"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215717">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 355.000000 -322.125000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32103" ObjectName="SW-YM_XH.YM_XH_0526SW"/>
     <cge:Meas_Ref ObjectId="215717"/>
    <cge:TPSR_Ref TObjectID="32103"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215716">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 404.819337 -441.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32102" ObjectName="SW-YM_XH.YM_XH_05217SW"/>
     <cge:Meas_Ref ObjectId="215716"/>
    <cge:TPSR_Ref TObjectID="32102"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215678">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 556.000000 -325.125000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32098" ObjectName="SW-YM_XH.YM_XH_0516SW"/>
     <cge:Meas_Ref ObjectId="215678"/>
    <cge:TPSR_Ref TObjectID="32098"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215677">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 605.819337 -444.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32097" ObjectName="SW-YM_XH.YM_XH_05117SW"/>
     <cge:Meas_Ref ObjectId="215677"/>
    <cge:TPSR_Ref TObjectID="32097"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215834">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 813.000000 -326.125000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32118" ObjectName="SW-YM_XH.YM_XH_0616SW"/>
     <cge:Meas_Ref ObjectId="215834"/>
    <cge:TPSR_Ref TObjectID="32118"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215833">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 862.819337 -445.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32117" ObjectName="SW-YM_XH.YM_XH_06117SW"/>
     <cge:Meas_Ref ObjectId="215833"/>
    <cge:TPSR_Ref TObjectID="32117"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215873">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 991.000000 -328.125000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32123" ObjectName="SW-YM_XH.YM_XH_0626SW"/>
     <cge:Meas_Ref ObjectId="215873"/>
    <cge:TPSR_Ref TObjectID="32123"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215872">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1040.819337 -447.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32122" ObjectName="SW-YM_XH.YM_XH_06217SW"/>
     <cge:Meas_Ref ObjectId="215872"/>
    <cge:TPSR_Ref TObjectID="32122"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 165.000000 -1146.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215910">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -232.000000 -551.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32125" ObjectName="SW-YM_XH.YM_XH_055XC"/>
     <cge:Meas_Ref ObjectId="215910"/>
    <cge:TPSR_Ref TObjectID="32125"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215910">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -233.000000 -481.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32126" ObjectName="SW-YM_XH.YM_XH_055XC1"/>
     <cge:Meas_Ref ObjectId="215910"/>
    <cge:TPSR_Ref TObjectID="32126"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215599">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 301.000000 -669.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32089" ObjectName="SW-YM_XH.YM_XH_001XC1"/>
     <cge:Meas_Ref ObjectId="215599"/>
    <cge:TPSR_Ref TObjectID="32089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215599">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 301.000000 -604.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32088" ObjectName="SW-YM_XH.YM_XH_001XC"/>
     <cge:Meas_Ref ObjectId="215599"/>
    <cge:TPSR_Ref TObjectID="32088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215676">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 555.000000 -536.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32095" ObjectName="SW-YM_XH.YM_XH_051XC"/>
     <cge:Meas_Ref ObjectId="215676"/>
    <cge:TPSR_Ref TObjectID="32095"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215676">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 555.000000 -462.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32096" ObjectName="SW-YM_XH.YM_XH_051XC1"/>
     <cge:Meas_Ref ObjectId="215676"/>
    <cge:TPSR_Ref TObjectID="32096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215793">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -46.000000 -533.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32110" ObjectName="SW-YM_XH.YM_XH_054XC"/>
     <cge:Meas_Ref ObjectId="215793"/>
    <cge:TPSR_Ref TObjectID="32110"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215793">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -46.000000 -459.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32111" ObjectName="SW-YM_XH.YM_XH_054XC1"/>
     <cge:Meas_Ref ObjectId="215793"/>
    <cge:TPSR_Ref TObjectID="32111"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215754">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 156.000000 -531.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32105" ObjectName="SW-YM_XH.YM_XH_053XC"/>
     <cge:Meas_Ref ObjectId="215754"/>
    <cge:TPSR_Ref TObjectID="32105"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215754">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 156.000000 -457.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32106" ObjectName="SW-YM_XH.YM_XH_053XC1"/>
     <cge:Meas_Ref ObjectId="215754"/>
    <cge:TPSR_Ref TObjectID="32106"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215715">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 354.000000 -529.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32100" ObjectName="SW-YM_XH.YM_XH_052XC"/>
     <cge:Meas_Ref ObjectId="215715"/>
    <cge:TPSR_Ref TObjectID="32100"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215715">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 354.000000 -455.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32101" ObjectName="SW-YM_XH.YM_XH_052XC1"/>
     <cge:Meas_Ref ObjectId="215715"/>
    <cge:TPSR_Ref TObjectID="32101"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215832">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 812.000000 -533.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32115" ObjectName="SW-YM_XH.YM_XH_061XC"/>
     <cge:Meas_Ref ObjectId="215832"/>
    <cge:TPSR_Ref TObjectID="32115"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215832">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 812.000000 -459.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32116" ObjectName="SW-YM_XH.YM_XH_061XC1"/>
     <cge:Meas_Ref ObjectId="215832"/>
    <cge:TPSR_Ref TObjectID="32116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215871">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 990.000000 -532.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32120" ObjectName="SW-YM_XH.YM_XH_062XC"/>
     <cge:Meas_Ref ObjectId="215871"/>
    <cge:TPSR_Ref TObjectID="32120"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215871">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 990.000000 -458.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32121" ObjectName="SW-YM_XH.YM_XH_062XC1"/>
     <cge:Meas_Ref ObjectId="215871"/>
    <cge:TPSR_Ref TObjectID="32121"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215669">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1114.000000 -695.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32093" ObjectName="SW-YM_XH.YM_XH_0902XC1"/>
     <cge:Meas_Ref ObjectId="215669"/>
    <cge:TPSR_Ref TObjectID="32093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215669">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1114.000000 -632.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32092" ObjectName="SW-YM_XH.YM_XH_0902XC"/>
     <cge:Meas_Ref ObjectId="215669"/>
    <cge:TPSR_Ref TObjectID="32092"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216038">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1175.000000 -538.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32137" ObjectName="SW-YM_XH.YM_XH_0632XC"/>
     <cge:Meas_Ref ObjectId="216038"/>
    <cge:TPSR_Ref TObjectID="32137"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216038">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1175.000000 -464.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32138" ObjectName="SW-YM_XH.YM_XH_0632XC1"/>
     <cge:Meas_Ref ObjectId="216038"/>
    <cge:TPSR_Ref TObjectID="32138"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215940">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 627.000000 -678.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32133" ObjectName="SW-YM_XH.YM_XH_012XC1"/>
     <cge:Meas_Ref ObjectId="215940"/>
    <cge:TPSR_Ref TObjectID="32133"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215940">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 627.000000 -604.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32132" ObjectName="SW-YM_XH.YM_XH_012XC"/>
     <cge:Meas_Ref ObjectId="215940"/>
    <cge:TPSR_Ref TObjectID="32132"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215665">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -137.000000 -681.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32091" ObjectName="SW-YM_XH.YM_XH_0901XC1"/>
     <cge:Meas_Ref ObjectId="215665"/>
    <cge:TPSR_Ref TObjectID="32091"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215665">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -137.000000 -622.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32090" ObjectName="SW-YM_XH.YM_XH_0901XC"/>
     <cge:Meas_Ref ObjectId="215665"/>
    <cge:TPSR_Ref TObjectID="32090"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215941">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 760.000000 -607.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32134" ObjectName="SW-YM_XH.YM_XH_0122XC"/>
     <cge:Meas_Ref ObjectId="215941"/>
    <cge:TPSR_Ref TObjectID="32134"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215941">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 760.000000 -678.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32135" ObjectName="SW-YM_XH.YM_XH_0122XC1"/>
     <cge:Meas_Ref ObjectId="215941"/>
    <cge:TPSR_Ref TObjectID="32135"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-YM_XH.YM_XH_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-286,-589 659,-589 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="32074" ObjectName="BS-YM_XH.YM_XH_9IM"/>
    <cge:TPSR_Ref TObjectID="32074"/></metadata>
   <polyline fill="none" opacity="0" points="-286,-589 659,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YM_XH.YM_XH_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="733,-590 1301,-590 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="32075" ObjectName="BS-YM_XH.YM_XH_9IIM"/>
    <cge:TPSR_Ref TObjectID="32075"/></metadata>
   <polyline fill="none" opacity="0" points="733,-590 1301,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YM_XH.YM_XH_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="554,-892 630,-892 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="32073" ObjectName="BS-YM_XH.YM_XH_3IM"/>
    <cge:TPSR_Ref TObjectID="32073"/></metadata>
   <polyline fill="none" opacity="0" points="554,-892 630,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YM_XH.XM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="305,-1085 316,-1085 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="49493" ObjectName="BS-YM_XH.XM"/>
    <cge:TPSR_Ref TObjectID="49493"/></metadata>
   <polyline fill="none" opacity="0" points="305,-1085 316,-1085 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-YM_XH.YM_XH_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -252.000000 -201.000000)" xlink:href="#capacitor:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41325" ObjectName="CB-YM_XH.YM_XH_Cb1"/>
    <cge:TPSR_Ref TObjectID="41325"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-YM_XH.YM_XH_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="46378"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.980392 -0.000000 0.000000 -1.000000 274.000000 -705.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.980392 -0.000000 0.000000 -1.000000 274.000000 -705.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="32139" ObjectName="TF-YM_XH.YM_XH_1T"/>
    <cge:TPSR_Ref TObjectID="32139"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1170.000000 -263.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1170.000000 -263.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 155.000000 -1045.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 155.000000 -1045.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_344d320">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 340.398535 -1220.220669)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3492c40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -41.000000 -344.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_340e450">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 25.559082 -205.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34132e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -228.000000 -387.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27f1a80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 161.000000 -376.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_318fc90">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 230.559082 -237.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3410ad0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 359.000000 -377.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_346ab50">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 423.559082 -237.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25b3690">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 560.000000 -380.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3132280">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 631.559082 -240.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33faa40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 817.000000 -381.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26c5940">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 884.559082 -241.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32d9ae0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 995.000000 -383.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31944f0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1063.559082 -243.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33f2260">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1180.000000 -374.125000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3450c30">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1230.559082 -395.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3469fc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -108.000000 -730.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_256e7b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -163.000000 -732.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25513f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1084.000000 -774.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_247c260">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1139.000000 -772.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3439230">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -284.000000 -260.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -618.000000 -1193.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-219743" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -596.000000 -1020.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219743" ObjectName="YM_XH:YM_XH_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-215396" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -597.000000 -1098.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215396" ObjectName="YM_XH:YM_XH_351BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-215396" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -597.000000 -1060.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215396" ObjectName="YM_XH:YM_XH_351BK_P"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-215452" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -45.000000 -134.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215452" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32109"/>
     <cge:Term_Ref ObjectID="46316"/>
    <cge:TPSR_Ref TObjectID="32109"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-215453" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -45.000000 -134.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215453" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32109"/>
     <cge:Term_Ref ObjectID="46316"/>
    <cge:TPSR_Ref TObjectID="32109"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-215449" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -45.000000 -134.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215449" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32109"/>
     <cge:Term_Ref ObjectID="46316"/>
    <cge:TPSR_Ref TObjectID="32109"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-215446" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 148.000000 -134.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215446" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32104"/>
     <cge:Term_Ref ObjectID="46306"/>
    <cge:TPSR_Ref TObjectID="32104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-215447" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 148.000000 -134.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215447" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32104"/>
     <cge:Term_Ref ObjectID="46306"/>
    <cge:TPSR_Ref TObjectID="32104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-215443" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 148.000000 -134.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215443" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32104"/>
     <cge:Term_Ref ObjectID="46306"/>
    <cge:TPSR_Ref TObjectID="32104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-215440" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 339.000000 -134.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215440" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32099"/>
     <cge:Term_Ref ObjectID="46296"/>
    <cge:TPSR_Ref TObjectID="32099"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-215441" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 339.000000 -134.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215441" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32099"/>
     <cge:Term_Ref ObjectID="46296"/>
    <cge:TPSR_Ref TObjectID="32099"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-215437" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 339.000000 -134.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215437" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32099"/>
     <cge:Term_Ref ObjectID="46296"/>
    <cge:TPSR_Ref TObjectID="32099"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-215434" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -134.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215434" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32094"/>
     <cge:Term_Ref ObjectID="46286"/>
    <cge:TPSR_Ref TObjectID="32094"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-215435" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -134.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215435" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32094"/>
     <cge:Term_Ref ObjectID="46286"/>
    <cge:TPSR_Ref TObjectID="32094"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-215431" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -134.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215431" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32094"/>
     <cge:Term_Ref ObjectID="46286"/>
    <cge:TPSR_Ref TObjectID="32094"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-215458" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 787.000000 -134.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215458" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32114"/>
     <cge:Term_Ref ObjectID="46326"/>
    <cge:TPSR_Ref TObjectID="32114"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-215459" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 787.000000 -134.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215459" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32114"/>
     <cge:Term_Ref ObjectID="46326"/>
    <cge:TPSR_Ref TObjectID="32114"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-215455" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 787.000000 -134.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215455" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32114"/>
     <cge:Term_Ref ObjectID="46326"/>
    <cge:TPSR_Ref TObjectID="32114"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-215464" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 969.000000 -134.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215464" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32119"/>
     <cge:Term_Ref ObjectID="46336"/>
    <cge:TPSR_Ref TObjectID="32119"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-215465" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 969.000000 -134.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215465" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32119"/>
     <cge:Term_Ref ObjectID="46336"/>
    <cge:TPSR_Ref TObjectID="32119"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-215461" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 969.000000 -134.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215461" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32119"/>
     <cge:Term_Ref ObjectID="46336"/>
    <cge:TPSR_Ref TObjectID="32119"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-216159" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 706.000000 -785.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216159" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32131"/>
     <cge:Term_Ref ObjectID="46360"/>
    <cge:TPSR_Ref TObjectID="32131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-216160" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 706.000000 -785.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216160" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32131"/>
     <cge:Term_Ref ObjectID="46360"/>
    <cge:TPSR_Ref TObjectID="32131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-216156" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 706.000000 -785.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216156" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32131"/>
     <cge:Term_Ref ObjectID="46360"/>
    <cge:TPSR_Ref TObjectID="32131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-215396" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 193.000000 -1037.220669) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215396" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32077"/>
     <cge:Term_Ref ObjectID="30827"/>
    <cge:TPSR_Ref TObjectID="32077"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-215397" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 193.000000 -1037.220669) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215397" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32077"/>
     <cge:Term_Ref ObjectID="30827"/>
    <cge:TPSR_Ref TObjectID="32077"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-215393" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 193.000000 -1037.220669) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215393" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32077"/>
     <cge:Term_Ref ObjectID="30827"/>
    <cge:TPSR_Ref TObjectID="32077"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-215398" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 193.000000 -1037.220669) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215398" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32077"/>
     <cge:Term_Ref ObjectID="30827"/>
    <cge:TPSR_Ref TObjectID="32077"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-215402" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 183.000000 -671.220669) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215402" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32087"/>
     <cge:Term_Ref ObjectID="46272"/>
    <cge:TPSR_Ref TObjectID="32087"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-215403" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 183.000000 -671.220669) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215403" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32087"/>
     <cge:Term_Ref ObjectID="46272"/>
    <cge:TPSR_Ref TObjectID="32087"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-215399" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 183.000000 -671.220669) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215399" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32087"/>
     <cge:Term_Ref ObjectID="46272"/>
    <cge:TPSR_Ref TObjectID="32087"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-215404" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 183.000000 -671.220669) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215404" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32087"/>
     <cge:Term_Ref ObjectID="46272"/>
    <cge:TPSR_Ref TObjectID="32087"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="1" id="ME-215415" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -248.000000 -716.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215415" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32074"/>
     <cge:Term_Ref ObjectID="24032"/>
    <cge:TPSR_Ref TObjectID="32074"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="1" id="ME-215416" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -248.000000 -716.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215416" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32074"/>
     <cge:Term_Ref ObjectID="24032"/>
    <cge:TPSR_Ref TObjectID="32074"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="1" id="ME-215417" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -248.000000 -716.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215417" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32074"/>
     <cge:Term_Ref ObjectID="24032"/>
    <cge:TPSR_Ref TObjectID="32074"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="1" id="ME-215421" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -248.000000 -716.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215421" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32074"/>
     <cge:Term_Ref ObjectID="24032"/>
    <cge:TPSR_Ref TObjectID="32074"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="1" id="ME-215418" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -248.000000 -716.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215418" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32074"/>
     <cge:Term_Ref ObjectID="24032"/>
    <cge:TPSR_Ref TObjectID="32074"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="1" id="ME-215422" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -248.000000 -716.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215422" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32074"/>
     <cge:Term_Ref ObjectID="24032"/>
    <cge:TPSR_Ref TObjectID="32074"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="1" id="ME-215423" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1263.000000 -718.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215423" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32075"/>
     <cge:Term_Ref ObjectID="24033"/>
    <cge:TPSR_Ref TObjectID="32075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="1" id="ME-215424" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1263.000000 -718.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215424" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32075"/>
     <cge:Term_Ref ObjectID="24033"/>
    <cge:TPSR_Ref TObjectID="32075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="1" id="ME-215425" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1263.000000 -718.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215425" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32075"/>
     <cge:Term_Ref ObjectID="24033"/>
    <cge:TPSR_Ref TObjectID="32075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="1" id="ME-215429" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1263.000000 -718.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215429" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32075"/>
     <cge:Term_Ref ObjectID="24033"/>
    <cge:TPSR_Ref TObjectID="32075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="1" id="ME-215426" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1263.000000 -718.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215426" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32075"/>
     <cge:Term_Ref ObjectID="24033"/>
    <cge:TPSR_Ref TObjectID="32075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="1" id="ME-215430" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1263.000000 -718.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215430" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32075"/>
     <cge:Term_Ref ObjectID="24033"/>
    <cge:TPSR_Ref TObjectID="32075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-215407" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 700.000000 -931.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215407" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32073"/>
     <cge:Term_Ref ObjectID="13720"/>
    <cge:TPSR_Ref TObjectID="32073"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-215408" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 700.000000 -931.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215408" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32073"/>
     <cge:Term_Ref ObjectID="13720"/>
    <cge:TPSR_Ref TObjectID="32073"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-215409" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 700.000000 -931.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215409" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32073"/>
     <cge:Term_Ref ObjectID="13720"/>
    <cge:TPSR_Ref TObjectID="32073"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-215410" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 700.000000 -931.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215410" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32073"/>
     <cge:Term_Ref ObjectID="13720"/>
    <cge:TPSR_Ref TObjectID="32073"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-215414" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 700.000000 -931.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215414" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32073"/>
     <cge:Term_Ref ObjectID="13720"/>
    <cge:TPSR_Ref TObjectID="32073"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-215405" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 497.000000 -758.220669) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215405" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32139"/>
     <cge:Term_Ref ObjectID="46379"/>
    <cge:TPSR_Ref TObjectID="32139"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-215406" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 497.000000 -758.220669) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215406" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32139"/>
     <cge:Term_Ref ObjectID="46379"/>
    <cge:TPSR_Ref TObjectID="32139"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-215470" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -259.000000 -101.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215470" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32124"/>
     <cge:Term_Ref ObjectID="46346"/>
    <cge:TPSR_Ref TObjectID="32124"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-215471" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -259.000000 -101.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215471" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32124"/>
     <cge:Term_Ref ObjectID="46346"/>
    <cge:TPSR_Ref TObjectID="32124"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-215467" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -259.000000 -101.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215467" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32124"/>
     <cge:Term_Ref ObjectID="46346"/>
    <cge:TPSR_Ref TObjectID="32124"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-610" y="-1250"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-610" y="-1250"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-655" y="-1269"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-655" y="-1269"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="-18" y="-510"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="-18" y="-510"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="178" y="-508"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="178" y="-508"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="376" y="-508"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="376" y="-508"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="579" y="-524"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="579" y="-524"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="834" y="-512"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="834" y="-512"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1012" y="-514"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1012" y="-514"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="650" y="-662"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="650" y="-662"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="16" qtmmishow="hidden" width="52" x="346" y="-759"/>
    </a>
   <metadata/><rect fill="white" height="16" opacity="0" stroke="white" transform="" width="52" x="346" y="-759"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="16" qtmmishow="hidden" width="26" x="320" y="-1028"/>
    </a>
   <metadata/><rect fill="white" height="16" opacity="0" stroke="white" transform="" width="26" x="320" y="-1028"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="60" x="-699" y="-832"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="60" x="-699" y="-832"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-367" y="-1209"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-367" y="-1209"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-367" y="-1248"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-367" y="-1248"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="-214" y="-538"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="-214" y="-538"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="-445,-1262 -448,-1265 -448,-1207 -445,-1210 -445,-1262" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="-445,-1262 -448,-1265 -379,-1265 -382,-1262 -445,-1262" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="-445,-1210 -448,-1207 -379,-1207 -382,-1210 -445,-1210" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="-382,-1262 -379,-1265 -379,-1207 -382,-1210 -382,-1262" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="52" stroke="rgb(255,255,255)" width="63" x="-445" y="-1262"/>
     <rect fill="none" height="52" qtmmishow="hidden" stroke="rgb(0,0,0)" width="63" x="-445" y="-1262"/>
    </a>
   <metadata/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-610" y="-1250"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-655" y="-1269"/></g>
   <g href="35kV新华变YM_XH_054间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="-18" y="-510"/></g>
   <g href="35kV新华变YM_XH_053间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="178" y="-508"/></g>
   <g href="35kV新华变YM_XH_052间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="376" y="-508"/></g>
   <g href="35kV新华变10kV撒芷线051间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="579" y="-524"/></g>
   <g href="35kV新华变10kV大河边线061间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="834" y="-512"/></g>
   <g href="35kV新华变10kV备用线062间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1012" y="-514"/></g>
   <g href="35kV新华变YM_XH_012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="650" y="-662"/></g>
   <g href="35kV新华变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="16" qtmmishow="hidden" width="52" x="346" y="-759"/></g>
   <g href="35kV新华变35kV平新线351间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="16" qtmmishow="hidden" width="26" x="320" y="-1028"/></g>
   <g href="35kV新华变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="60" x="-699" y="-832"/></g>
   <g href="cx_配调_配网接线图35_元谋.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-367" y="-1209"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-367" y="-1248"/></g>
   <g href="35kV新华变10k1号电容器055间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="-214" y="-538"/></g>
   <g href="AVC新华站.svg" style="fill-opacity:0"><rect height="52" qtmmishow="hidden" stroke="rgb(0,0,0)" width="63" x="-445" y="-1262"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-215558">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 302.000000 -998.220669)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32077" ObjectName="SW-YM_XH.YM_XH_351BK"/>
     <cge:Meas_Ref ObjectId="215558"/>
    <cge:TPSR_Ref TObjectID="32077"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215909">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -232.000000 -509.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32124" ObjectName="SW-YM_XH.YM_XH_055BK"/>
     <cge:Meas_Ref ObjectId="215909"/>
    <cge:TPSR_Ref TObjectID="32124"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215598">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 306.000000 -639.000000)" xlink:href="#breaker2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32087" ObjectName="SW-YM_XH.YM_XH_001BK"/>
     <cge:Meas_Ref ObjectId="215598"/>
    <cge:TPSR_Ref TObjectID="32087"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215675">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 556.000000 -493.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32094" ObjectName="SW-YM_XH.YM_XH_051BK"/>
     <cge:Meas_Ref ObjectId="215675"/>
    <cge:TPSR_Ref TObjectID="32094"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215753">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 157.000000 -488.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32104" ObjectName="SW-YM_XH.YM_XH_053BK"/>
     <cge:Meas_Ref ObjectId="215753"/>
    <cge:TPSR_Ref TObjectID="32104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215714">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 355.000000 -486.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32099" ObjectName="SW-YM_XH.YM_XH_052BK"/>
     <cge:Meas_Ref ObjectId="215714"/>
    <cge:TPSR_Ref TObjectID="32099"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215831">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 813.000000 -490.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32114" ObjectName="SW-YM_XH.YM_XH_061BK"/>
     <cge:Meas_Ref ObjectId="215831"/>
    <cge:TPSR_Ref TObjectID="32114"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215870">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 991.000000 -489.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32119" ObjectName="SW-YM_XH.YM_XH_062BK"/>
     <cge:Meas_Ref ObjectId="215870"/>
    <cge:TPSR_Ref TObjectID="32119"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215792">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -46.000000 -490.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32109" ObjectName="SW-YM_XH.YM_XH_054BK"/>
     <cge:Meas_Ref ObjectId="215792"/>
    <cge:TPSR_Ref TObjectID="32109"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-215939">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 628.000000 -635.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32131" ObjectName="SW-YM_XH.YM_XH_012BK"/>
     <cge:Meas_Ref ObjectId="215939"/>
    <cge:TPSR_Ref TObjectID="32131"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="YM_PT" endPointId="0" endStationName="YM_XH" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_PingXin" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="312,-1291 312,-1326 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34176" ObjectName="AC-35kV.LN_PingXin"/>
    <cge:TPSR_Ref TObjectID="34176_SS-240"/></metadata>
   <polyline fill="none" opacity="0" points="312,-1291 312,-1326 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_346b8d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 409.819337 -1139.220669)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33f2950" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 408.819337 -1054.220669)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_31cf5a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 410.819337 -976.220669)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24b0e40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 502.000000 -970.220669)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_340bed0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 408.819337 -800.220669)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3417fd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 55.819337 -428.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_278edd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -141.180663 -461.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_346ecf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -138.180663 -308.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27ce0e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 257.819337 -440.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27f2960" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 455.819337 -440.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27fa340" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 656.819337 -443.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24c4160" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 913.819337 -444.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32db7a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1091.819337 -446.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24926f0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 -230.000000 -145.180663)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="32073" cx="556" cy="-892" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="32074" cx="-222" cy="-589" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="32074" cx="311" cy="-589" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="32074" cx="565" cy="-589" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="32074" cx="637" cy="-589" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="32075" cx="822" cy="-590" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="32075" cx="1185" cy="-590" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="32074" cx="166" cy="-589" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="32074" cx="364" cy="-589" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="32075" cx="770" cy="-590" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49493" cx="311" cy="-1085" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49493" cx="311" cy="-1085" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(64,64,64)" font-family="SimHei" font-size="20" graphid="g_344a5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -567.000000 -1239.500000) translate(0,16)">新华变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_345a300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_345a300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_345a300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_345a300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_345a300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_345a300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_345a300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_345a300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_345a300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_345a300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_345a300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_345a300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_345a300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_345a300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_345a300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_345a300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_345a300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_345a300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -662.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3422290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -58.000000 -163.000000) translate(0,12)">10kV备用线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33f2760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 127.500000 -163.000000) translate(0,12)">10kV小牛街线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_319c7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 318.500000 -163.000000) translate(0,12)">10kV平坡哨线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24dc820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 538.000000 -163.000000) translate(0,12)">10kV撒芷线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24dcab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 766.500000 -163.000000) translate(0,12)">10kV大河边线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e2e5b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 956.000000 -163.000000) translate(0,12)">10kV备用线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_318ed80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1159.000000 -194.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c4f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 51.000000 -1146.000000) translate(0,12)"> 1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3212970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 92.000000 -792.000000) translate(0,12)"> 1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3212970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 92.000000 -792.000000) translate(0,27)">SZ11—2500/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3212970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 92.000000 -792.000000) translate(0,42)">35000±3×2.5%/10500V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3212970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 92.000000 -792.000000) translate(0,57)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27f6d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -174.000000 -823.000000) translate(0,12)">Ⅰ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33fe530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1075.000000 -866.000000) translate(0,12)">Ⅱ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3448140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -266.000000 -613.000000) translate(0,12)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3448390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1212.000000 -612.000000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2476a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 10.000000 -457.000000) translate(0,12)">05417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ceb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -29.000000 -319.000000) translate(0,12)">0546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25cc680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -18.000000 -510.000000) translate(0,12)">054</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e0d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1056.000000 -478.000000) translate(0,12)">06227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e0fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1007.000000 -358.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2496180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1012.000000 -514.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24963c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 878.000000 -476.000000) translate(0,12)">06127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_279d850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 829.000000 -356.000000) translate(0,12)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_279da90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 834.000000 -512.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_279dcd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -217.000000 -179.000000) translate(0,12)">05500</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342fa20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -177.000000 -493.000000) translate(0,12)">05517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342fc30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -174.000000 -340.000000) translate(0,12)">05567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_342fe70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -216.000000 -369.000000) translate(0,12)">0556</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2788930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 781.000000 -668.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2788b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 650.000000 -662.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27866b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1136.000000 -687.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2786ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 420.000000 -472.000000) translate(0,12)">05217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2479f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 371.000000 -352.000000) translate(0,12)">0526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_247a180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 376.000000 -508.000000) translate(0,12)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_343dab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 621.000000 -475.000000) translate(0,12)">05117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_343dcf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 572.000000 -355.000000) translate(0,12)">0516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_343df30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 579.000000 -524.000000) translate(0,12)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24a7ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -115.000000 -683.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24a7cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 222.000000 -472.000000) translate(0,12)">05317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24a7f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 173.000000 -351.000000) translate(0,12)">0536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25a0af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 178.000000 -508.000000) translate(0,12)">053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25a0d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 347.000000 -757.220669) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25a0f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 361.000000 -832.220669) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24cb7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 318.000000 -855.220669) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24cb9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 515.000000 -945.220669) translate(0,12)">31217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e2990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 418.000000 -918.220669) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e2bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1197.000000 -515.000000) translate(0,12)">0632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27e2e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 375.000000 -1008.220669) translate(0,12)">35117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3403480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 318.000000 -945.220669) translate(0,12)">3511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3403680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 372.000000 -1086.220669) translate(0,12)">35160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34038c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 373.000000 -1171.220669) translate(0,12)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3449c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 320.000000 -1027.220669) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3449e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 318.000000 -1121.220669) translate(0,12)">3516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_254a7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 569.000000 -914.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3454760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 281.000000 -1356.000000) translate(0,12)">35kV平新线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_342d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_342d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_342d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_342d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_342d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_342d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_342d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_342d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_342d950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -709.000000 -1100.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_2796b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -699.000000 -832.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3482ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -356.000000 -1202.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_342c560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -356.000000 -1239.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3407790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -316.000000 -659.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34079f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -301.000000 -643.000000) translate(0,12)">F(Hz):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3407c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -308.000000 -689.000000) translate(0,12)">Uc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27f3360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -308.000000 -704.000000) translate(0,12)">Ub(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27f35a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -308.000000 -718.000000) translate(0,12)">Ua(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27f37e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -308.000000 -674.000000) translate(0,12)">U0(V):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33f5060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1202.000000 -659.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33f52a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1217.000000 -643.000000) translate(0,12)">F(Hz):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33f54e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1210.000000 -689.000000) translate(0,12)">Uc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27fb5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1210.000000 -704.000000) translate(0,12)">Ub(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27fb7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1210.000000 -718.000000) translate(0,12)">Ua(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27fba20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1210.000000 -674.000000) translate(0,12)">U0(V):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3444de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 687.000000 -719.000000) translate(0,12)">分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3478fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -735.000000 -264.500000) translate(0,17)">元谋巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_347f900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -589.000000 -274.000000) translate(0,16)">18787879021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_347f900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -589.000000 -274.000000) translate(0,36)">13908784331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_32179a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -589.000000 -309.000000) translate(0,16)">8386916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34421e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -254.000000 -120.000000) translate(0,12)">1号电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34827f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -214.000000 -538.000000) translate(0,12)">055</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3482d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -431.000000 -1246.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24b78e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 327.000000 -661.000000) translate(0,12)">001</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3495920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -367.000000 100.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3422420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -378.000000 85.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3460c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -353.000000 70.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_346add0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 637.000000 889.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3403e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 653.000000 873.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_278d1d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 645.000000 904.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27f6400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 645.000000 919.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27f6680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 645.000000 933.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27ddf00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 415.000000 745.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24dfd10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 415.000000 760.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3320350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 644.000000 787.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3432730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 633.000000 772.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3404e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 658.000000 757.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32fde70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 120.000000 1023.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32fe1d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 145.000000 1008.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25a85e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 150.000000 992.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24d46f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 131.000000 1038.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24d4a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 107.000000 657.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24d4cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 132.000000 642.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3478b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 137.000000 626.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3478d60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 118.000000 672.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-YM_XH.054Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -45.356070 -201.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34305" ObjectName="EC-YM_XH.054Ld"/>
    <cge:TPSR_Ref TObjectID="34305"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_XH.053Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 156.643930 -233.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34212" ObjectName="EC-YM_XH.053Ld"/>
    <cge:TPSR_Ref TObjectID="34212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_XH.052Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 354.643930 -233.125000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34214" ObjectName="EC-YM_XH.052Ld"/>
    <cge:TPSR_Ref TObjectID="34214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_XH.051Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 555.643930 -236.125000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34213" ObjectName="EC-YM_XH.051Ld"/>
    <cge:TPSR_Ref TObjectID="34213"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_XH.061Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 812.643930 -237.125000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34216" ObjectName="EC-YM_XH.061Ld"/>
    <cge:TPSR_Ref TObjectID="34216"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YM_XH.062Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 990.643930 -239.125000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34306" ObjectName="EC-YM_XH.062Ld"/>
    <cge:TPSR_Ref TObjectID="34306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1175.643930 -227.125000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-154237" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -421.000000 -1164.000000)" xlink:href="#dynamicPoint:shape33"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26171" ObjectName="DYN-YM_XH"/>
     <cge:Meas_Ref ObjectId="154237"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_34335b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 211.000000 -1227.000000)" xlink:href="#voltageTransformer:shape69"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27903a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -119.000000 -777.000000)" xlink:href="#voltageTransformer:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34501b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1128.000000 -819.000000)" xlink:href="#voltageTransformer:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_3479430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="263,-1256 311,-1256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_34335b0@0" ObjectIDZND0="0@x" ObjectIDZND1="32081@x" ObjectIDZND2="32078@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-215562_0" Pin0InfoVect2LinkObjId="SW-215559_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34335b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="263,-1256 311,-1256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_347d980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="347,-1274 311,-1274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_344d320@0" ObjectIDZND0="g_34335b0@0" ObjectIDZND1="0@x" ObjectIDZND2="32081@x" Pin0InfoVect0LinkObjId="g_34335b0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-215562_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_344d320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="347,-1274 311,-1274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3487f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="400,-1145 414,-1145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32081@0" ObjectIDZND0="g_346b8d0@0" Pin0InfoVect0LinkObjId="g_346b8d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215562_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="400,-1145 414,-1145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_348a4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="364,-1145 311,-1145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="32081@1" ObjectIDZND0="0@x" ObjectIDZND1="g_34335b0@0" ObjectIDZND2="g_344d320@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_34335b0_0" Pin0InfoVect2LinkObjId="g_344d320_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215562_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="364,-1145 311,-1145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3470050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="311,-1132 311,-1145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="32078@1" ObjectIDZND0="32081@x" ObjectIDZND1="0@x" ObjectIDZND2="g_34335b0@0" Pin0InfoVect0LinkObjId="SW-215562_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_34335b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215559_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="311,-1132 311,-1145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2db1340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="399,-1060 413,-1060 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32080@0" ObjectIDZND0="g_33f2950@0" Pin0InfoVect0LinkObjId="g_33f2950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215561_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="399,-1060 413,-1060 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2db22f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="311,-1096 311,-1085 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32078@0" ObjectIDZND0="49493@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215559_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="311,-1096 311,-1085 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2db3040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="401,-982 415,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32082@0" ObjectIDZND0="g_31cf5a0@0" Pin0InfoVect0LinkObjId="g_31cf5a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215563_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="401,-982 415,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2debf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="365,-982 311,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="32082@1" ObjectIDZND0="32077@x" ObjectIDZND1="32079@x" Pin0InfoVect0LinkObjId="SW-215558_0" Pin0InfoVect1LinkObjId="SW-215560_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215563_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="365,-982 311,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3442a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="311,-1006 311,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="32077@0" ObjectIDZND0="32082@x" ObjectIDZND1="32079@x" Pin0InfoVect0LinkObjId="SW-215563_0" Pin0InfoVect1LinkObjId="SW-215560_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215558_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="311,-1006 311,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3459050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="311,-982 311,-956 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="32077@x" ObjectIDND1="32082@x" ObjectIDZND0="32079@1" Pin0InfoVect0LinkObjId="SW-215560_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-215558_0" Pin1InfoVect1LinkObjId="SW-215563_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="311,-982 311,-956 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34607c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="508,-975 508,-956 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_24b0e40@0" ObjectIDZND0="32086@1" Pin0InfoVect0LinkObjId="SW-215575_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24b0e40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="508,-975 508,-956 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_348a290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="508,-920 508,-892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="32086@0" ObjectIDZND0="32084@x" ObjectIDZND1="32073@0" Pin0InfoVect0LinkObjId="SW-215573_0" Pin0InfoVect1LinkObjId="g_34098c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215575_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="508,-920 508,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32d9e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="452,-892 508,-892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="32084@1" ObjectIDZND0="32086@x" ObjectIDZND1="32073@0" Pin0InfoVect0LinkObjId="SW-215575_0" Pin0InfoVect1LinkObjId="g_34098c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215573_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="452,-892 508,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34098c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="508,-892 556,-892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="32084@x" ObjectIDND1="32086@x" ObjectIDZND0="32073@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-215573_0" Pin1InfoVect1LinkObjId="SW-215575_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="508,-892 556,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3432090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="311,-893 311,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="32079@x" ObjectIDND1="32084@x" ObjectIDZND0="32083@1" Pin0InfoVect0LinkObjId="SW-215572_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-215560_0" Pin1InfoVect1LinkObjId="SW-215573_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="311,-893 311,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2794dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="399,-806 413,-806 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32085@0" ObjectIDZND0="g_340bed0@0" Pin0InfoVect0LinkObjId="g_340bed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215574_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="399,-806 413,-806 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2794710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="363,-806 311,-806 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="32085@1" ObjectIDZND0="32083@x" ObjectIDZND1="32139@x" Pin0InfoVect0LinkObjId="SW-215572_0" Pin0InfoVect1LinkObjId="g_3436160_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215574_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="363,-806 311,-806 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2793d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="311,-806 311,-830 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="32085@x" ObjectIDND1="32139@x" ObjectIDZND0="32083@0" Pin0InfoVect0LinkObjId="SW-215572_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-215574_0" Pin1InfoVect1LinkObjId="g_3436160_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="311,-806 311,-830 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3475960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-36,-349 -36,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3492c40@0" ObjectIDZND0="32113@1" Pin0InfoVect0LinkObjId="SW-215795_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3492c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-36,-349 -36,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3485210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="33,-259 -36,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_340e450@0" ObjectIDZND0="32113@x" ObjectIDZND1="34305@x" Pin0InfoVect0LinkObjId="SW-215795_0" Pin0InfoVect1LinkObjId="EC-YM_XH.054Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_340e450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="33,-259 -36,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_348c170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-36,-294 -36,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="32113@0" ObjectIDZND0="34305@x" ObjectIDZND1="g_340e450@0" Pin0InfoVect0LinkObjId="EC-YM_XH.054Ld_0" Pin0InfoVect1LinkObjId="g_340e450_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215795_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-36,-294 -36,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3492570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-36,-259 -36,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="32113@x" ObjectIDND1="g_340e450@0" ObjectIDZND0="34305@0" Pin0InfoVect0LinkObjId="EC-YM_XH.054Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-215795_0" Pin1InfoVect1LinkObjId="g_340e450_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-36,-259 -36,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3492210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="46,-434 60,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32112@0" ObjectIDZND0="g_3417fd0@0" Pin0InfoVect0LinkObjId="g_3417fd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215794_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="46,-434 60,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_343d100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-223,-392 -223,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_34132e0@0" ObjectIDZND0="32128@1" Pin0InfoVect0LinkObjId="SW-215912_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34132e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-223,-392 -223,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3281290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-224,-154 -224,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32130@0" ObjectIDZND0="g_24926f0@0" Pin0InfoVect0LinkObjId="g_24926f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215914_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-224,-154 -224,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27f0760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-151,-467 -137,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32127@0" ObjectIDZND0="g_278edd0@0" Pin0InfoVect0LinkObjId="g_278edd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215911_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-151,-467 -137,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3488b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-148,-314 -134,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32129@0" ObjectIDZND0="g_346ecf0@0" Pin0InfoVect0LinkObjId="g_346ecf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215913_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-148,-314 -134,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b2e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="166,-381 166,-362 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_27f1a80@0" ObjectIDZND0="32108@1" Pin0InfoVect0LinkObjId="SW-215756_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27f1a80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="166,-381 166,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24e1c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="238,-291 166,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_318fc90@0" ObjectIDZND0="32108@x" ObjectIDZND1="34212@x" Pin0InfoVect0LinkObjId="SW-215756_0" Pin0InfoVect1LinkObjId="EC-YM_XH.053Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_318fc90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="238,-291 166,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2481db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="166,-326 166,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="32108@0" ObjectIDZND0="34212@x" ObjectIDZND1="g_318fc90@0" Pin0InfoVect0LinkObjId="EC-YM_XH.053Ld_0" Pin0InfoVect1LinkObjId="g_318fc90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215756_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="166,-326 166,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_347d570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="166,-291 166,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="32108@x" ObjectIDND1="g_318fc90@0" ObjectIDZND0="34212@0" Pin0InfoVect0LinkObjId="EC-YM_XH.053Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-215756_0" Pin1InfoVect1LinkObjId="g_318fc90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="166,-291 166,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d52e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="248,-446 262,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32107@0" ObjectIDZND0="g_27ce0e0@0" Pin0InfoVect0LinkObjId="g_27ce0e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215755_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="248,-446 262,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b4630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="364,-381 364,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3410ad0@0" ObjectIDZND0="32103@1" Pin0InfoVect0LinkObjId="SW-215717_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3410ad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="364,-381 364,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_344fb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="431,-291 364,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_346ab50@0" ObjectIDZND0="32103@x" ObjectIDZND1="34214@x" Pin0InfoVect0LinkObjId="SW-215717_0" Pin0InfoVect1LinkObjId="EC-YM_XH.052Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_346ab50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="431,-291 364,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3442c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="364,-327 364,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="32103@0" ObjectIDZND0="34214@x" ObjectIDZND1="g_346ab50@0" Pin0InfoVect0LinkObjId="EC-YM_XH.052Ld_0" Pin0InfoVect1LinkObjId="g_346ab50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215717_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="364,-327 364,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_345fb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="364,-291 364,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="32103@x" ObjectIDND1="g_346ab50@0" ObjectIDZND0="34214@0" Pin0InfoVect0LinkObjId="EC-YM_XH.052Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-215717_0" Pin1InfoVect1LinkObjId="g_346ab50_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="364,-291 364,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32b7d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="446,-446 460,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32102@0" ObjectIDZND0="g_27f2960@0" Pin0InfoVect0LinkObjId="g_27f2960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215716_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="446,-446 460,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_31a2b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="565,-384 565,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_25b3690@0" ObjectIDZND0="32098@1" Pin0InfoVect0LinkObjId="SW-215678_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25b3690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="565,-384 565,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24a29a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="639,-294 565,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_3132280@0" ObjectIDZND0="32098@x" ObjectIDZND1="34213@x" Pin0InfoVect0LinkObjId="SW-215678_0" Pin0InfoVect1LinkObjId="EC-YM_XH.051Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3132280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="639,-294 565,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_344baf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="565,-330 565,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="32098@0" ObjectIDZND0="34213@x" ObjectIDZND1="g_3132280@0" Pin0InfoVect0LinkObjId="EC-YM_XH.051Ld_0" Pin0InfoVect1LinkObjId="g_3132280_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215678_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="565,-330 565,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3470f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="565,-294 565,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="32098@x" ObjectIDND1="g_3132280@0" ObjectIDZND0="34213@0" Pin0InfoVect0LinkObjId="EC-YM_XH.051Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-215678_0" Pin1InfoVect1LinkObjId="g_3132280_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="565,-294 565,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3416d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-449 661,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32097@0" ObjectIDZND0="g_27fa340@0" Pin0InfoVect0LinkObjId="g_27fa340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215677_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="647,-449 661,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33f0f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="822,-385 822,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_33faa40@0" ObjectIDZND0="32118@1" Pin0InfoVect0LinkObjId="SW-215834_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33faa40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="822,-385 822,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33f11f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="892,-295 822,-295 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_26c5940@0" ObjectIDZND0="32118@x" ObjectIDZND1="34216@x" Pin0InfoVect0LinkObjId="SW-215834_0" Pin0InfoVect1LinkObjId="EC-YM_XH.061Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26c5940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="892,-295 822,-295 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3470730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="822,-331 822,-295 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="32118@0" ObjectIDZND0="34216@x" ObjectIDZND1="g_26c5940@0" Pin0InfoVect0LinkObjId="EC-YM_XH.061Ld_0" Pin0InfoVect1LinkObjId="g_26c5940_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215834_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="822,-331 822,-295 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3470960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="822,-295 822,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="32118@x" ObjectIDND1="g_26c5940@0" ObjectIDZND0="34216@0" Pin0InfoVect0LinkObjId="EC-YM_XH.061Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-215834_0" Pin1InfoVect1LinkObjId="g_26c5940_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="822,-295 822,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_345ade0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="904,-450 918,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32117@0" ObjectIDZND0="g_24c4160@0" Pin0InfoVect0LinkObjId="g_24c4160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215833_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="904,-450 918,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24ef380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1000,-387 1000,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_32d9ae0@0" ObjectIDZND0="32123@1" Pin0InfoVect0LinkObjId="SW-215873_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32d9ae0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1000,-387 1000,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27f3b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1071,-297 1000,-297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_31944f0@0" ObjectIDZND0="32123@x" ObjectIDZND1="34306@x" Pin0InfoVect0LinkObjId="SW-215873_0" Pin0InfoVect1LinkObjId="EC-YM_XH.062Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_31944f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1071,-297 1000,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27f3da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1000,-333 1000,-297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="32123@0" ObjectIDZND0="34306@x" ObjectIDZND1="g_31944f0@0" Pin0InfoVect0LinkObjId="EC-YM_XH.062Ld_0" Pin0InfoVect1LinkObjId="g_31944f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215873_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1000,-333 1000,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_312cb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1000,-297 1000,-266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="32123@x" ObjectIDND1="g_31944f0@0" ObjectIDZND0="34306@0" Pin0InfoVect0LinkObjId="EC-YM_XH.062Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-215873_0" Pin1InfoVect1LinkObjId="g_31944f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1000,-297 1000,-266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3217f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1082,-452 1096,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32122@0" ObjectIDZND0="g_32db7a0@0" Pin0InfoVect0LinkObjId="g_32db7a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215872_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1082,-452 1096,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_248a4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1185,-379 1185,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_33f2260@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33f2260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1185,-379 1185,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_248a710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1185,-305 1185,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="load" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1185,-305 1185,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3265690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="311,-920 311,-892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="32079@0" ObjectIDZND0="32083@x" ObjectIDZND1="32084@x" Pin0InfoVect0LinkObjId="SW-215572_0" Pin0InfoVect1LinkObjId="SW-215573_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="311,-920 311,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27968f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="311,-892 416,-892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="32083@x" ObjectIDND1="32079@x" ObjectIDZND0="32084@0" Pin0InfoVect0LinkObjId="SW-215573_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-215572_0" Pin1InfoVect1LinkObjId="SW-215560_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="311,-892 416,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3436160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="311,-806 311,-790 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="32083@x" ObjectIDND1="32085@x" ObjectIDZND0="32139@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-215572_0" Pin1InfoVect1LinkObjId="SW-215574_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="311,-806 311,-790 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27eddd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="170,-1196 170,-1213 311,-1213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="32081@x" ObjectIDZND1="32078@x" ObjectIDZND2="g_34335b0@0" Pin0InfoVect0LinkObjId="SW-215562_0" Pin0InfoVect1LinkObjId="SW-215559_0" Pin0InfoVect2LinkObjId="g_34335b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="170,-1196 170,-1213 311,-1213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3433350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="170,-1151 170,-1138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="170,-1151 170,-1138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2489f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="311,-1213 311,-1145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_34335b0@0" ObjectIDND2="g_344d320@0" ObjectIDZND0="32081@x" ObjectIDZND1="32078@x" Pin0InfoVect0LinkObjId="SW-215562_0" Pin0InfoVect1LinkObjId="SW-215559_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_34335b0_0" Pin1InfoVect2LinkObjId="g_344d320_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="311,-1213 311,-1145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2790140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="311,-1213 311,-1256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="0@x" ObjectIDND1="32081@x" ObjectIDND2="32078@x" ObjectIDZND0="g_34335b0@0" ObjectIDZND1="g_344d320@0" ObjectIDZND2="34176@1" Pin0InfoVect0LinkObjId="g_34335b0_0" Pin0InfoVect1LinkObjId="g_344d320_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-215562_0" Pin1InfoVect2LinkObjId="SW-215559_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="311,-1213 311,-1256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_340da60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-156,-736 -156,-721 -127,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_256e7b0@0" ObjectIDZND0="g_3469fc0@0" ObjectIDZND1="32091@x" Pin0InfoVect0LinkObjId="g_3469fc0_0" Pin0InfoVect1LinkObjId="SW-215665_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_256e7b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-156,-736 -156,-721 -127,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_340dcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-127,-721 -99,-721 -99,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_256e7b0@0" ObjectIDND1="32091@x" ObjectIDZND0="g_3469fc0@1" Pin0InfoVect0LinkObjId="g_3469fc0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_256e7b0_0" Pin1InfoVect1LinkObjId="SW-215665_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-127,-721 -99,-721 -99,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_340df20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-99,-766 -99,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_3469fc0@0" ObjectIDZND0="g_27903a0@0" Pin0InfoVect0LinkObjId="g_27903a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3469fc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-99,-766 -99,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27d36b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1091,-778 1091,-763 1120,-763 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_25513f0@0" ObjectIDZND0="g_247c260@0" ObjectIDZND1="32093@x" Pin0InfoVect0LinkObjId="g_247c260_0" Pin0InfoVect1LinkObjId="SW-215669_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25513f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1091,-778 1091,-763 1120,-763 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27d3910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1120,-763 1148,-763 1148,-777 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_25513f0@0" ObjectIDND1="32093@x" ObjectIDZND0="g_247c260@1" Pin0InfoVect0LinkObjId="g_247c260_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_25513f0_0" Pin1InfoVect1LinkObjId="SW-215669_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1120,-763 1148,-763 1148,-777 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_344ff50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1148,-808 1148,-821 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_247c260@0" ObjectIDZND0="g_34501b0@0" Pin0InfoVect0LinkObjId="g_34501b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_247c260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1148,-808 1148,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3486b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="311,-1292 311,-1274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="34176@1" ObjectIDZND0="g_344d320@0" ObjectIDZND1="g_34335b0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_344d320_0" Pin0InfoVect1LinkObjId="g_34335b0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="311,-1292 311,-1274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3486d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="311,-1274 311,-1256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_344d320@0" ObjectIDND1="34176@1" ObjectIDZND0="g_34335b0@0" ObjectIDZND1="0@x" ObjectIDZND2="32081@x" Pin0InfoVect0LinkObjId="g_34335b0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-215562_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_344d320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="311,-1274 311,-1256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34291f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="10,-434 -36,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="32112@1" ObjectIDZND0="g_3492c40@0" ObjectIDZND1="32111@x" Pin0InfoVect0LinkObjId="g_3492c40_0" Pin0InfoVect1LinkObjId="SW-215793_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215794_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="10,-434 -36,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_340c370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-36,-434 -36,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="32112@x" ObjectIDND1="32111@x" ObjectIDZND0="g_3492c40@1" Pin0InfoVect0LinkObjId="g_3492c40_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-215794_0" Pin1InfoVect1LinkObjId="SW-215793_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-36,-434 -36,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_340c5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1238,-449 1185,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_3450c30@0" ObjectIDZND0="g_33f2260@0" ObjectIDZND1="32138@x" Pin0InfoVect0LinkObjId="g_33f2260_0" Pin0InfoVect1LinkObjId="SW-216038_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3450c30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1238,-449 1185,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_307c570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1185,-432 1185,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_33f2260@1" ObjectIDZND0="g_3450c30@0" ObjectIDZND1="32138@x" Pin0InfoVect0LinkObjId="g_3450c30_0" Pin0InfoVect1LinkObjId="SW-216038_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33f2260_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1185,-432 1185,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c7e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-222,-544 -222,-558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="32124@1" ObjectIDZND0="32125@1" Pin0InfoVect0LinkObjId="SW-215910_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215909_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-222,-544 -222,-558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3482590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-223,-505 -222,-517 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32126@1" ObjectIDZND0="32124@0" Pin0InfoVect0LinkObjId="SW-215909_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215910_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-223,-505 -222,-517 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3415260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-222,-575 -222,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32125@0" ObjectIDZND0="32074@0" Pin0InfoVect0LinkObjId="g_24b7680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-222,-575 -222,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34157f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-187,-467 -223,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="32127@1" ObjectIDZND0="g_34132e0@0" ObjectIDZND1="32126@x" Pin0InfoVect0LinkObjId="g_34132e0_0" Pin0InfoVect1LinkObjId="SW-215910_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215911_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-187,-467 -223,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3487bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-223,-445 -223,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_34132e0@1" ObjectIDZND0="32127@x" ObjectIDZND1="32126@x" Pin0InfoVect0LinkObjId="SW-215911_0" Pin0InfoVect1LinkObjId="SW-215910_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34132e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-223,-445 -223,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2482d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-223,-467 -223,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="32127@x" ObjectIDND1="g_34132e0@0" ObjectIDZND0="32126@0" Pin0InfoVect0LinkObjId="SW-215910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-215911_0" Pin1InfoVect1LinkObjId="g_34132e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-223,-467 -223,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2482fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-184,-314 -223,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="capacitor" ObjectIDND0="32129@1" ObjectIDZND0="32128@x" ObjectIDZND1="g_3439230@0" ObjectIDZND2="41325@x" Pin0InfoVect0LinkObjId="SW-215912_0" Pin0InfoVect1LinkObjId="g_3439230_0" Pin0InfoVect2LinkObjId="CB-YM_XH.YM_XH_Cb1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215913_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-184,-314 -223,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3462d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-223,-297 -223,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="41325@0" ObjectIDZND0="32129@x" ObjectIDZND1="32128@x" ObjectIDZND2="g_3439230@0" Pin0InfoVect0LinkObjId="SW-215913_0" Pin0InfoVect1LinkObjId="SW-215912_0" Pin0InfoVect2LinkObjId="g_3439230_0" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-YM_XH.YM_XH_Cb1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-223,-297 -223,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_34598a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-223,-314 -223,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="32129@x" ObjectIDND1="g_3439230@0" ObjectIDND2="41325@x" ObjectIDZND0="32128@0" Pin0InfoVect0LinkObjId="SW-215912_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-215913_0" Pin1InfoVect1LinkObjId="g_3439230_0" Pin1InfoVect2LinkObjId="CB-YM_XH.YM_XH_Cb1_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-223,-314 -223,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3459b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-277,-314 -223,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_3439230@0" ObjectIDZND0="32129@x" ObjectIDZND1="32128@x" ObjectIDZND2="41325@x" Pin0InfoVect0LinkObjId="SW-215913_0" Pin0InfoVect1LinkObjId="SW-215912_0" Pin0InfoVect2LinkObjId="CB-YM_XH.YM_XH_Cb1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3439230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-277,-314 -223,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3459d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-224,-203 -224,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="32130@1" Pin0InfoVect0LinkObjId="SW-215914_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-224,-203 -224,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24d9ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="311,-710 311,-693 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="32139@1" ObjectIDZND0="32089@0" Pin0InfoVect0LinkObjId="SW-215599_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3436160_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="311,-710 311,-693 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24d9f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="311,-676 311,-667 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32089@1" ObjectIDZND0="32087@0" Pin0InfoVect0LinkObjId="SW-215598_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215599_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="311,-676 311,-667 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24da160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="311,-641 311,-628 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="32087@1" ObjectIDZND0="32088@1" Pin0InfoVect0LinkObjId="SW-215599_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215598_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="311,-641 311,-628 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24b7680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="311,-611 311,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32088@0" ObjectIDZND0="32074@0" Pin0InfoVect0LinkObjId="g_3415260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215599_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="311,-611 311,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33c9dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="611,-449 565,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="32097@1" ObjectIDZND0="g_25b3690@0" ObjectIDZND1="32096@x" Pin0InfoVect0LinkObjId="g_25b3690_0" Pin0InfoVect1LinkObjId="SW-215676_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215677_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="611,-449 565,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24a3e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="565,-437 565,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_25b3690@1" ObjectIDZND0="32097@x" ObjectIDZND1="32096@x" Pin0InfoVect0LinkObjId="SW-215677_0" Pin0InfoVect1LinkObjId="SW-215676_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25b3690_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="565,-437 565,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25a5510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="565,-528 565,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="32094@1" ObjectIDZND0="32095@1" Pin0InfoVect0LinkObjId="SW-215676_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215675_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="565,-528 565,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_32d8cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="565,-560 565,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32095@0" ObjectIDZND0="32074@0" Pin0InfoVect0LinkObjId="g_3415260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215676_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="565,-560 565,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_348d840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="565,-449 565,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="32097@x" ObjectIDND1="g_25b3690@0" ObjectIDZND0="32096@0" Pin0InfoVect0LinkObjId="SW-215676_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-215677_0" Pin1InfoVect1LinkObjId="g_25b3690_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="565,-449 565,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_348da70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="565,-486 565,-501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32096@1" ObjectIDZND0="32094@0" Pin0InfoVect0LinkObjId="SW-215675_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215676_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="565,-486 565,-501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2495150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1046,-452 1000,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="32122@1" ObjectIDZND0="g_32d9ae0@0" ObjectIDZND1="32121@x" Pin0InfoVect0LinkObjId="g_32d9ae0_0" Pin0InfoVect1LinkObjId="SW-215871_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215872_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1046,-452 1000,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3428220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1000,-452 1000,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="32122@x" ObjectIDND1="32121@x" ObjectIDZND0="g_32d9ae0@1" Pin0InfoVect0LinkObjId="g_32d9ae0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-215872_0" Pin1InfoVect1LinkObjId="SW-215871_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1000,-452 1000,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24b6900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="868,-450 822,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="32117@1" ObjectIDZND0="g_33faa40@0" ObjectIDZND1="32116@x" Pin0InfoVect0LinkObjId="g_33faa40_0" Pin0InfoVect1LinkObjId="SW-215832_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215833_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="868,-450 822,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24b6b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="822,-450 822,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="32117@x" ObjectIDND1="32116@x" ObjectIDZND0="g_33faa40@1" Pin0InfoVect0LinkObjId="g_33faa40_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-215833_0" Pin1InfoVect1LinkObjId="SW-215832_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="822,-450 822,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27979f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-36,-466 -36,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="32111@0" ObjectIDZND0="32112@x" ObjectIDZND1="g_3492c40@0" Pin0InfoVect0LinkObjId="SW-215794_0" Pin0InfoVect1LinkObjId="g_3492c40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215793_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-36,-466 -36,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2797c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-36,-498 -36,-483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="32109@0" ObjectIDZND0="32111@1" Pin0InfoVect0LinkObjId="SW-215793_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215792_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-36,-498 -36,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2797eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-36,-590 -36,-557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="32074@0" ObjectIDZND0="32110@0" Pin0InfoVect0LinkObjId="SW-215793_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3415260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-36,-590 -36,-557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3490110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-36,-540 -36,-525 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32110@1" ObjectIDZND0="32109@1" Pin0InfoVect0LinkObjId="SW-215792_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215793_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-36,-540 -36,-525 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3490920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="166,-589 166,-555 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="32074@0" ObjectIDZND0="32105@0" Pin0InfoVect0LinkObjId="SW-215754_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3415260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="166,-589 166,-555 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3490b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="166,-538 166,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32105@1" ObjectIDZND0="32104@1" Pin0InfoVect0LinkObjId="SW-215753_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215754_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="166,-538 166,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3490de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="166,-496 166,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="32104@0" ObjectIDZND0="32106@1" Pin0InfoVect0LinkObjId="SW-215754_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215753_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="166,-496 166,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33fd620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="364,-589 364,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="32074@0" ObjectIDZND0="32100@0" Pin0InfoVect0LinkObjId="SW-215715_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3415260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="364,-589 364,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33fd880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="364,-536 364,-521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32100@1" ObjectIDZND0="32099@1" Pin0InfoVect0LinkObjId="SW-215714_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215715_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="364,-536 364,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33fdae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="364,-494 364,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="32099@0" ObjectIDZND0="32101@1" Pin0InfoVect0LinkObjId="SW-215715_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215714_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="364,-494 364,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33fdd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="822,-590 822,-557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="32075@0" ObjectIDZND0="32115@0" Pin0InfoVect0LinkObjId="SW-215832_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3405f20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="822,-590 822,-557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27e6d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="822,-466 822,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="32116@0" ObjectIDZND0="32117@x" ObjectIDZND1="g_33faa40@0" Pin0InfoVect0LinkObjId="SW-215833_0" Pin0InfoVect1LinkObjId="g_33faa40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215832_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="822,-466 822,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27e6f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="822,-540 822,-525 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32115@1" ObjectIDZND0="32114@1" Pin0InfoVect0LinkObjId="SW-215831_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215832_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="822,-540 822,-525 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27e71c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="822,-498 822,-483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="32114@0" ObjectIDZND0="32116@1" Pin0InfoVect0LinkObjId="SW-215832_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215831_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="822,-498 822,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27e7420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1000,-590 1000,-556 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="32075@0" ObjectIDZND0="32120@0" Pin0InfoVect0LinkObjId="SW-215871_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3405f20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1000,-590 1000,-556 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27e7680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1000,-465 1000,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="32121@0" ObjectIDZND0="32122@x" ObjectIDZND1="g_32d9ae0@0" Pin0InfoVect0LinkObjId="SW-215872_0" Pin0InfoVect1LinkObjId="g_32d9ae0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215871_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1000,-465 1000,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27e78e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1000,-539 1000,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32120@1" ObjectIDZND0="32119@1" Pin0InfoVect0LinkObjId="SW-215870_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215871_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1000,-539 1000,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27e7b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1000,-497 1000,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="32119@0" ObjectIDZND0="32121@1" Pin0InfoVect0LinkObjId="SW-215871_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1000,-497 1000,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3405a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1185,-449 1185,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_3450c30@0" ObjectIDND1="g_33f2260@0" ObjectIDZND0="32138@0" Pin0InfoVect0LinkObjId="SW-216038_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3450c30_0" Pin1InfoVect1LinkObjId="g_33f2260_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1185,-449 1185,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3405cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1185,-488 1185,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="32138@1" ObjectIDZND0="32137@1" Pin0InfoVect0LinkObjId="SW-216038_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216038_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1185,-488 1185,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3405f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1185,-562 1185,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32137@0" ObjectIDZND0="32075@0" Pin0InfoVect0LinkObjId="g_33af990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216038_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1185,-562 1185,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_247fef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="637,-589 637,-611 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="32074@0" ObjectIDZND0="32132@0" Pin0InfoVect0LinkObjId="SW-215940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3415260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="637,-589 637,-611 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2480150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="637,-628 637,-643 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32132@1" ObjectIDZND0="32131@0" Pin0InfoVect0LinkObjId="SW-215939_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215940_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="637,-628 637,-643 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24803b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="637,-670 637,-685 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="32131@1" ObjectIDZND0="32133@1" Pin0InfoVect0LinkObjId="SW-215940_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215939_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="637,-670 637,-685 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2480610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1124,-591 1124,-639 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="32075@0" ObjectIDZND0="32092@0" Pin0InfoVect0LinkObjId="SW-215669_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3405f20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1124,-591 1124,-639 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2480870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1124,-656 1124,-702 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="32092@1" ObjectIDZND0="32093@1" Pin0InfoVect0LinkObjId="SW-215669_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215669_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1124,-656 1124,-702 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2480ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1124,-719 1124,-763 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="32093@0" ObjectIDZND0="g_25513f0@0" ObjectIDZND1="g_247c260@0" Pin0InfoVect0LinkObjId="g_25513f0_0" Pin0InfoVect1LinkObjId="g_247c260_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215669_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1124,-719 1124,-763 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_340f700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-127,-590 -127,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="32074@0" ObjectIDZND0="32090@0" Pin0InfoVect0LinkObjId="SW-215665_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3415260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-127,-590 -127,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_340f960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-127,-646 -127,-688 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="32090@1" ObjectIDZND0="32091@1" Pin0InfoVect0LinkObjId="SW-215665_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215665_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-127,-646 -127,-688 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_340fbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-127,-705 -127,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="32091@0" ObjectIDZND0="g_3469fc0@0" ObjectIDZND1="g_256e7b0@0" Pin0InfoVect0LinkObjId="g_3469fc0_0" Pin0InfoVect1LinkObjId="g_256e7b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215665_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-127,-705 -127,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33af990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="770,-614 770,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32134@0" ObjectIDZND0="32075@0" Pin0InfoVect0LinkObjId="g_3405f20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215941_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="770,-614 770,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33afbf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="637,-702 637,-724 770,-724 770,-702 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="32133@0" ObjectIDZND0="32135@0" Pin0InfoVect0LinkObjId="SW-215941_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="637,-702 637,-724 770,-724 770,-702 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33afe60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="770,-685 770,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="32135@1" ObjectIDZND0="32134@1" Pin0InfoVect0LinkObjId="SW-215941_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215941_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="770,-685 770,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33b00c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="212,-446 166,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="32107@1" ObjectIDZND0="32106@x" ObjectIDZND1="g_27f1a80@0" Pin0InfoVect0LinkObjId="SW-215754_0" Pin0InfoVect1LinkObjId="g_27f1a80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215755_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="212,-446 166,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27dfbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="166,-464 166,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="32106@0" ObjectIDZND0="32107@x" ObjectIDZND1="g_27f1a80@0" Pin0InfoVect0LinkObjId="SW-215755_0" Pin0InfoVect1LinkObjId="g_27f1a80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215754_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="166,-464 166,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27dfe30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="166,-446 166,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="32107@x" ObjectIDND1="32106@x" ObjectIDZND0="g_27f1a80@1" Pin0InfoVect0LinkObjId="g_27f1a80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-215755_0" Pin1InfoVect1LinkObjId="SW-215754_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="166,-446 166,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27e0090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="410,-446 364,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="32102@1" ObjectIDZND0="32101@x" ObjectIDZND1="g_3410ad0@0" Pin0InfoVect0LinkObjId="SW-215715_0" Pin0InfoVect1LinkObjId="g_3410ad0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215716_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="410,-446 364,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2db6b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="364,-462 364,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="32101@0" ObjectIDZND0="32102@x" ObjectIDZND1="g_3410ad0@0" Pin0InfoVect0LinkObjId="SW-215716_0" Pin0InfoVect1LinkObjId="g_3410ad0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-215715_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="364,-462 364,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2db6dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="364,-446 364,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="32102@x" ObjectIDND1="32101@x" ObjectIDZND0="g_3410ad0@1" Pin0InfoVect0LinkObjId="g_3410ad0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-215716_0" Pin1InfoVect1LinkObjId="SW-215715_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="364,-446 364,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35c4930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="312,-1060 363,-1060 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49493@0" ObjectIDND1="32077@x" ObjectIDZND0="32080@1" Pin0InfoVect0LinkObjId="SW-215561_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2db22f0_0" Pin1InfoVect1LinkObjId="SW-215558_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="312,-1060 363,-1060 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35c40a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="311,-1085 311,-1062 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="49493@0" ObjectIDZND0="32077@x" ObjectIDZND1="32080@x" Pin0InfoVect0LinkObjId="SW-215558_0" Pin0InfoVect1LinkObjId="SW-215561_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2db22f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="311,-1085 311,-1062 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35c5b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="311,-1062 311,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="49493@0" ObjectIDND1="32080@x" ObjectIDZND0="32077@1" Pin0InfoVect0LinkObjId="SW-215558_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2db22f0_0" Pin1InfoVect1LinkObjId="SW-215561_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="311,-1062 311,-1033 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="YM_XH"/>
</svg>