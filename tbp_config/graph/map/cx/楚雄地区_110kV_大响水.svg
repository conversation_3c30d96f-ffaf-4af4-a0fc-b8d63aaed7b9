<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-153" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3115 -1291 2083 1408">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape11">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.974359" x1="16" x2="92" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="92" x2="92" y1="22" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="52" x2="52" y1="22" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="22" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="107" x2="76" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="107" x2="76" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="68" x2="36" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="68" x2="37" y1="22" y2="22"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="hydroGenerator:shape3">
    <polyline arcFlag="1" points="25,25 25,26 25,27 25,28 24,29 24,30 23,31 23,31 22,32 21,32 20,33 19,33 18,33 17,34 16,33 15,33 14,33 13,32 13,32 12,31 11,31 11,30 10,29 10,28 10,27 9,26 10,25 " stroke-width="1.14"/>
    <circle cx="24" cy="24" fillStyle="0" r="24" stroke-width="0.5"/>
    <polyline points="40,25 41,24 40,24 40,23 40,22 39,21 39,20 38,19 37,19 37,18 36,18 35,17 34,17 33,17 32,17 31,17 30,18 29,18 28,19 27,19 27,20 26,21 26,22 25,23 25,24 25,24 25,25 " stroke-width="1.14"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="55" x2="55" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="54" x2="46" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="59" x2="59" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="62" x2="62" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="19" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="39" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="58" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="8" y2="37"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="5" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="3" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="8" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="1" x2="12" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape190">
    <ellipse cx="24" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="28" x2="25" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="25" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="33" x2="39" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="35" x2="33" y1="29" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="36" x2="39" y1="29" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="40" x2="37" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="34" x2="37" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="37" x2="37" y1="20" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="28" x2="25" y1="34" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="32" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="25" y1="34" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="13" x2="5" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="10" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="12" x2="6" y1="4" y2="4"/>
    <ellipse cx="35" cy="30" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="24" cy="30" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="35" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <rect height="9" stroke-width="1" width="5" x="7" y="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="0" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="15" y1="12" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="19" y2="19"/>
    <polyline points="9,7 9,10 " stroke-width="0.676923"/>
    <polyline points="10,19 10,31 25,31 " stroke-width="0.676923"/>
   </symbol>
   <symbol id="lightningRod:shape186">
    <circle cx="15" cy="80" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="41" x2="39" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="43" x2="37" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="34" x2="46" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="59" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="19" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="57" y2="53"/>
    <circle cx="15" cy="58" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="43" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="15,16 21,28 9,28 15,16 15,16 15,16 "/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,59 40,59 40,30 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="53" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="10" y1="87" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="18" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="14" y1="81" y2="87"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="60" x2="21" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="15" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="6" x2="6" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="4" x2="4" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="6" y2="9"/>
    <rect height="13" stroke-width="0.424575" width="29" x="15" y="1"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="13" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="9" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape37">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,63 0,73 10,73 5,63 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,28 0,18 10,18 5,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="86" y2="6"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape7_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="18" x2="43" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="18" x2="9" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="5" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="9" x2="9" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape7_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="transformer:shape3_0">
    <circle cx="57" cy="31" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="63" x2="56" y1="29" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="71" x2="63" y1="36" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="63" x2="63" y1="20" y2="29"/>
   </symbol>
   <symbol id="transformer:shape3_1">
    <circle cx="26" cy="30" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="11" x2="26" y1="28" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="11" x2="26" y1="28" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="26" x2="26" y1="19" y2="36"/>
   </symbol>
   <symbol id="transformer:shape3-2">
    <circle cx="41" cy="60" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="33" y1="67" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="48" x2="40" y1="74" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="40" y1="58" y2="67"/>
   </symbol>
   <symbol id="transformer2:shape2_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape2_1">
    <circle cx="13" cy="18" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="18" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="9" y1="20" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="18" y1="20" y2="12"/>
   </symbol>
   <symbol id="voltageTransformer:shape21">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="28" y1="11" y2="5"/>
    <circle cx="15" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="26" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="11" y2="5"/>
   </symbol>
   <symbol id="voltageTransformer:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="3" x2="9" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="3" y1="14" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="14" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="24" y1="13" y2="11"/>
    <circle cx="7" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="15" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="24" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="15" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="6" y2="4"/>
   </symbol>
   <symbol id="voltageTransformer:shape79">
    <circle cx="18" cy="24" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="34" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <circle cx="18" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="13" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="15" y2="9"/>
    <polyline points="40,23 28,32 28,36 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="37" y2="46"/>
    <rect height="14" stroke-width="1" width="8" x="30" y="23"/>
    <circle cx="7" cy="24" r="7.5" stroke-width="1"/>
    <circle cx="7" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="37" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="36" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="39" y1="46" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="23" y2="13"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2c7db70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_27ee4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c7fb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c806c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d48580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d49170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d49940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2d4a330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_26b2910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_26b2910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d4dd50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d4dd50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d4fb80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d4fb80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2d50b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d52820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d53470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d54350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d54c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d563f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d570f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d579b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d58170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d59250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d59bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d5a6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d5b080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d5c500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d5d0a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d5e0d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d5ed10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d6d4e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d60600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2d61bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2d63120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1418" width="2093" x="3110" y="-1296"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-586 4111,-597 4122,-597 4116,-586 4116,-587 4116,-586 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-498 4111,-487 4122,-487 4116,-498 4116,-497 4116,-498 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4955,-153 4950,-164 4961,-164 4955,-153 4955,-154 4955,-153 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4955,-96 4950,-85 4961,-85 4955,-96 4955,-95 4955,-96 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4852,-153 4847,-164 4858,-164 4852,-153 4852,-154 4852,-153 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4852,-96 4847,-85 4858,-85 4852,-96 4852,-95 4852,-96 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4747,-153 4742,-164 4753,-164 4747,-153 4747,-154 4747,-153 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4747,-96 4742,-85 4753,-85 4747,-96 4747,-95 4747,-96 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4638,-153 4633,-164 4644,-164 4638,-153 4638,-154 4638,-153 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4638,-96 4633,-85 4644,-85 4638,-96 4638,-95 4638,-96 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4095,-140 4090,-151 4101,-151 4095,-140 4095,-141 4095,-140 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4095,-60 4090,-49 4101,-49 4095,-60 4095,-59 4095,-60 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-140 3853,-151 3864,-151 3858,-140 3858,-141 3858,-140 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-60 3853,-49 3864,-49 3858,-60 3858,-59 3858,-60 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3617,-140 3612,-151 3623,-151 3617,-140 3617,-141 3617,-140 " stroke="rgb(50,205,50)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3617,-60 3612,-49 3623,-49 3617,-60 3617,-59 3617,-60 " stroke="rgb(50,205,50)"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-193256">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4734.000000 -528.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29333" ObjectName="SW-WD_DXS.WD_DXS_311BK"/>
     <cge:Meas_Ref ObjectId="193256"/>
    <cge:TPSR_Ref TObjectID="29333"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94045">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4455.578000 -552.874055)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19822" ObjectName="SW-WD_DXS.WD_DXS_301BK"/>
     <cge:Meas_Ref ObjectId="94045"/>
    <cge:TPSR_Ref TObjectID="19822"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193249">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4107.191445 -436.874055)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23691" ObjectName="SW-WD_DXS.WD_DXS_601BK"/>
     <cge:Meas_Ref ObjectId="193249"/>
    <cge:TPSR_Ref TObjectID="23691"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193257">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4736.000000 -739.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29334" ObjectName="SW-WD_DXS.WD_DXS_313BK"/>
     <cge:Meas_Ref ObjectId="193257"/>
    <cge:TPSR_Ref TObjectID="29334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193255">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4738.000000 -974.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29332" ObjectName="SW-WD_DXS.WD_DXS_314BK"/>
     <cge:Meas_Ref ObjectId="193255"/>
    <cge:TPSR_Ref TObjectID="29332"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193250">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4738.000000 -1087.627204)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29331" ObjectName="SW-WD_DXS.WD_DXS_315BK"/>
     <cge:Meas_Ref ObjectId="193250"/>
    <cge:TPSR_Ref TObjectID="29331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94042">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4738.000000 -1199.055416)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19818" ObjectName="SW-WD_DXS.WD_DXS_316BK"/>
     <cge:Meas_Ref ObjectId="94042"/>
    <cge:TPSR_Ref TObjectID="19818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193254">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4946.000000 -230.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29398" ObjectName="SW-WD_DXS.WD_DXS_014BK"/>
     <cge:Meas_Ref ObjectId="193254"/>
    <cge:TPSR_Ref TObjectID="29398"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193252">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4843.000000 -230.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29397" ObjectName="SW-WD_DXS.WD_DXS_013BK"/>
     <cge:Meas_Ref ObjectId="193252"/>
    <cge:TPSR_Ref TObjectID="29397"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193253">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4738.000000 -230.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29396" ObjectName="SW-WD_DXS.WD_DXS_012BK"/>
     <cge:Meas_Ref ObjectId="193253"/>
    <cge:TPSR_Ref TObjectID="29396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193251">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4629.000000 -230.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29395" ObjectName="SW-WD_DXS.WD_DXS_011BK"/>
     <cge:Meas_Ref ObjectId="193251"/>
    <cge:TPSR_Ref TObjectID="29395"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193260">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4389.000000 -230.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29347" ObjectName="SW-WD_DXS.WD_DXS_602BK"/>
     <cge:Meas_Ref ObjectId="193260"/>
    <cge:TPSR_Ref TObjectID="29347"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-200312">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4086.000000 -200.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19776" ObjectName="SW-WD_DXS.WD_DXS_613BK"/>
     <cge:Meas_Ref ObjectId="200312"/>
    <cge:TPSR_Ref TObjectID="19776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-93964">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3849.000000 -200.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19773" ObjectName="SW-WD_DXS.WD_DXS_612BK"/>
     <cge:Meas_Ref ObjectId="93964"/>
    <cge:TPSR_Ref TObjectID="19773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-93961">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3608.000000 -200.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19770" ObjectName="SW-WD_DXS.WD_DXS_611BK"/>
     <cge:Meas_Ref ObjectId="93961"/>
    <cge:TPSR_Ref TObjectID="19770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193258">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4785.000000 -827.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29335" ObjectName="SW-WD_DXS.WD_DXS_312BK"/>
     <cge:Meas_Ref ObjectId="193258"/>
    <cge:TPSR_Ref TObjectID="29335"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193259">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4451.578000 -409.874055)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29336" ObjectName="SW-WD_DXS.WD_DXS_001BK"/>
     <cge:Meas_Ref ObjectId="193259"/>
    <cge:TPSR_Ref TObjectID="29336"/></metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-WD_DXS.WD_DXS_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="43412"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4091.578000 -602.000000)" xlink:href="#transformer:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="43414"/>
     </metadata>
     <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4091.578000 -602.000000)" xlink:href="#transformer:shape3_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="43416"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4091.578000 -602.000000)" xlink:href="#transformer:shape3-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="30500" ObjectName="TF-WD_DXS.WD_DXS_1T"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-WD_DXS.WD_DXS_6ⅠM">
    <g class="BV-3KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3512,-349 4439,-349 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="19827" ObjectName="BS-WD_DXS.WD_DXS_6ⅠM"/>
    <cge:TPSR_Ref TObjectID="19827"/></metadata>
   <polyline fill="none" opacity="0" points="3512,-349 4439,-349 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-WD_DXS.WD_DXS_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4505,-349 5124,-349 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="19826" ObjectName="BS-WD_DXS.WD_DXS_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="19826"/></metadata>
   <polyline fill="none" opacity="0" points="4505,-349 5124,-349 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-WD_DXS.WD_DXS_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4633,-1279 4633,-878 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="19825" ObjectName="BS-WD_DXS.WD_DXS_3ⅠM"/>
    <cge:TPSR_Ref TObjectID="19825"/></metadata>
   <polyline fill="none" opacity="0" points="4633,-1279 4633,-878 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-WD_DXS.WD_DXS_3ⅡM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4633,-811 4633,-444 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="29321" ObjectName="BS-WD_DXS.WD_DXS_3ⅡM"/>
    <cge:TPSR_Ref TObjectID="29321"/></metadata>
   <polyline fill="none" opacity="0" points="4633,-811 4633,-444 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3652.000000 -511.000000)" xlink:href="#capacitor:shape11"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4159.000000 -20.000000)" xlink:href="#transformer2:shape2_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4159.000000 -20.000000)" xlink:href="#transformer2:shape2_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3922.000000 -20.000000)" xlink:href="#transformer2:shape2_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3922.000000 -20.000000)" xlink:href="#transformer2:shape2_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3681.000000 -20.000000)" xlink:href="#transformer2:shape2_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3681.000000 -20.000000)" xlink:href="#transformer2:shape2_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="HydroGenerator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4071.000000 21.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3834.000000 21.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3593.000000 21.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_24c7160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4688,-538 4632,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29376@0" ObjectIDZND0="29321@0" Pin0InfoVect0LinkObjId="g_2650ed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193284_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4688,-538 4632,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_264edc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4724,-538 4743,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29376@1" ObjectIDZND0="29333@1" Pin0InfoVect0LinkObjId="SW-193256_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193284_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4724,-538 4743,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_264efb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4770,-538 4788,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29333@0" ObjectIDZND0="29377@0" Pin0InfoVect0LinkObjId="SW-193283_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193256_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4770,-538 4788,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_264f1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4847,-480 4847,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29378@0" ObjectIDZND0="g_266d530@0" Pin0InfoVect0LinkObjId="g_266d530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193282_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4847,-480 4847,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_264f390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4951,-574 4974,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_26b7370@0" ObjectIDZND0="g_24c6d10@0" Pin0InfoVect0LinkObjId="g_24c6d10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26b7370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4951,-574 4974,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2650ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-792 4632,-792 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29383@0" ObjectIDZND0="29321@0" Pin0InfoVect0LinkObjId="g_24c7160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193289_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-792 4632,-792 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_26c3d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-417 4116,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29330@1" ObjectIDZND0="23691@0" Pin0InfoVect0LinkObjId="SW-193249_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-200313_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-417 4116,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26bd380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4039,-611 4039,-621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_26bb0b0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="CB-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26bb0b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4039,-611 4039,-621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2669370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4491,-562 4514,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19822@0" ObjectIDZND0="19824@0" Pin0InfoVect0LinkObjId="SW-94047_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94045_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4491,-562 4514,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2669bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5085,-237 5060,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2310a00@0" ObjectIDZND0="g_233bd10@0" ObjectIDZND1="29367@x" ObjectIDZND2="29401@x" Pin0InfoVect0LinkObjId="g_233bd10_0" Pin0InfoVect1LinkObjId="SW-193273_0" Pin0InfoVect2LinkObjId="SW-193304_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2310a00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5085,-237 5060,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_266a430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4847,-516 4847,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="29378@1" ObjectIDZND0="29377@x" ObjectIDZND1="g_26a6a40@0" ObjectIDZND2="g_26b7370@0" Pin0InfoVect0LinkObjId="SW-193283_0" Pin0InfoVect1LinkObjId="g_26a6a40_0" Pin0InfoVect2LinkObjId="g_26b7370_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193282_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4847,-516 4847,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_266ac80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4824,-538 4847,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="29377@1" ObjectIDZND0="29378@x" ObjectIDZND1="g_26a6a40@0" ObjectIDZND2="g_26b7370@0" Pin0InfoVect0LinkObjId="SW-193282_0" Pin0InfoVect1LinkObjId="g_26a6a40_0" Pin0InfoVect2LinkObjId="g_26b7370_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193283_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4824,-538 4847,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_266ae70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4870,-516 4870,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_26a6a40@0" ObjectIDZND0="29378@x" ObjectIDZND1="29377@x" ObjectIDZND2="g_26b7370@0" Pin0InfoVect0LinkObjId="SW-193282_0" Pin0InfoVect1LinkObjId="SW-193283_0" Pin0InfoVect2LinkObjId="g_26b7370_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26a6a40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4870,-516 4870,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_266b6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4847,-538 4870,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="29378@x" ObjectIDND1="29377@x" ObjectIDZND0="g_26a6a40@0" ObjectIDZND1="g_26b7370@0" ObjectIDZND2="38122@1" Pin0InfoVect0LinkObjId="g_26a6a40_0" Pin0InfoVect1LinkObjId="g_26b7370_0" Pin0InfoVect2LinkObjId="g_266b8b0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193282_0" Pin1InfoVect1LinkObjId="SW-193283_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4847,-538 4870,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_266b8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4888,-538 5048,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="29378@x" ObjectIDND1="29377@x" ObjectIDND2="g_26a6a40@0" ObjectIDZND0="38122@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-193282_0" Pin1InfoVect1LinkObjId="SW-193283_0" Pin1InfoVect2LinkObjId="g_26a6a40_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4888,-538 5048,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_268f1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4920,-575 4888,-575 4888,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_26b7370@1" ObjectIDZND0="29378@x" ObjectIDZND1="29377@x" ObjectIDZND2="g_26a6a40@0" Pin0InfoVect0LinkObjId="SW-193282_0" Pin0InfoVect1LinkObjId="SW-193283_0" Pin0InfoVect2LinkObjId="g_26a6a40_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26b7370_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4920,-575 4888,-575 4888,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_268f3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4870,-538 4888,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="29378@x" ObjectIDND1="29377@x" ObjectIDND2="g_26a6a40@0" ObjectIDZND0="g_26b7370@0" ObjectIDZND1="38122@1" Pin0InfoVect0LinkObjId="g_26b7370_0" Pin0InfoVect1LinkObjId="g_266b8b0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-193282_0" Pin1InfoVect1LinkObjId="SW-193283_0" Pin1InfoVect2LinkObjId="g_26a6a40_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4870,-538 4888,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2666890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4690,-749 4632,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29381@0" ObjectIDZND0="29321@0" Pin0InfoVect0LinkObjId="g_24c7160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193287_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4690,-749 4632,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2666ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4726,-749 4745,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29381@1" ObjectIDZND0="29334@1" Pin0InfoVect0LinkObjId="SW-193257_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193287_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4726,-749 4745,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2666cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4772,-749 4790,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29334@0" ObjectIDZND0="29380@0" Pin0InfoVect0LinkObjId="SW-193286_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193257_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4772,-749 4790,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2666ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4849,-691 4849,-676 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29379@0" ObjectIDZND0="g_2667700@0" Pin0InfoVect0LinkObjId="g_2667700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193285_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4849,-691 4849,-676 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2667110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4950,-798 4973,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_265afc0@0" ObjectIDZND0="g_2665f40@0" Pin0InfoVect0LinkObjId="g_2665f40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_265afc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4950,-798 4973,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26322f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4849,-727 4849,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="29379@1" ObjectIDZND0="29380@x" ObjectIDZND1="g_265a410@0" ObjectIDZND2="g_265afc0@0" Pin0InfoVect0LinkObjId="SW-193286_0" Pin0InfoVect1LinkObjId="g_265a410_0" Pin0InfoVect2LinkObjId="g_265afc0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193285_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4849,-727 4849,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2632510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-749 4849,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="29380@1" ObjectIDZND0="29379@x" ObjectIDZND1="g_265a410@0" ObjectIDZND2="g_265afc0@0" Pin0InfoVect0LinkObjId="SW-193285_0" Pin0InfoVect1LinkObjId="g_265a410_0" Pin0InfoVect2LinkObjId="g_265afc0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193286_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-749 4849,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2632730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4872,-727 4872,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_265a410@0" ObjectIDZND0="29380@x" ObjectIDZND1="29379@x" ObjectIDZND2="g_265afc0@0" Pin0InfoVect0LinkObjId="SW-193286_0" Pin0InfoVect1LinkObjId="SW-193285_0" Pin0InfoVect2LinkObjId="g_265afc0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_265a410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4872,-727 4872,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2632950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4849,-749 4872,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="29380@x" ObjectIDND1="29379@x" ObjectIDZND0="g_265a410@0" ObjectIDZND1="g_265afc0@0" ObjectIDZND2="38121@1" Pin0InfoVect0LinkObjId="g_265a410_0" Pin0InfoVect1LinkObjId="g_265afc0_0" Pin0InfoVect2LinkObjId="g_2632b70_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193286_0" Pin1InfoVect1LinkObjId="SW-193285_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4849,-749 4872,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2632b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4891,-749 5042,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_265afc0@0" ObjectIDND1="29380@x" ObjectIDND2="29379@x" ObjectIDZND0="38121@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_265afc0_0" Pin1InfoVect1LinkObjId="SW-193286_0" Pin1InfoVect2LinkObjId="SW-193285_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4891,-749 5042,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2632d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4919,-798 4890,-798 4890,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_265afc0@1" ObjectIDZND0="29380@x" ObjectIDZND1="29379@x" ObjectIDZND2="g_265a410@0" Pin0InfoVect0LinkObjId="SW-193286_0" Pin0InfoVect1LinkObjId="SW-193285_0" Pin0InfoVect2LinkObjId="g_265a410_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_265afc0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4919,-798 4890,-798 4890,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2632fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4872,-749 4890,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="29380@x" ObjectIDND1="29379@x" ObjectIDND2="g_265a410@0" ObjectIDZND0="g_265afc0@0" ObjectIDZND1="38121@1" Pin0InfoVect0LinkObjId="g_265afc0_0" Pin0InfoVect1LinkObjId="g_2632b70_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-193286_0" Pin1InfoVect1LinkObjId="SW-193285_0" Pin1InfoVect2LinkObjId="g_265a410_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4872,-749 4890,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_269bfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4728,-984 4747,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29375@1" ObjectIDZND0="29332@1" Pin0InfoVect0LinkObjId="SW-193255_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193281_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4728,-984 4747,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_269c1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4774,-984 4792,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29332@0" ObjectIDZND0="29374@0" Pin0InfoVect0LinkObjId="SW-193280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193255_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4774,-984 4792,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_269c3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-926 4851,-911 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29373@0" ObjectIDZND0="g_269cc00@0" Pin0InfoVect0LinkObjId="g_269cc00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193279_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-926 4851,-911 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_269c610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4952,-1033 4975,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_269aec0@0" ObjectIDZND0="g_269b660@0" Pin0InfoVect0LinkObjId="g_269b660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_269aec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4952,-1033 4975,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2695ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-962 4851,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="29373@1" ObjectIDZND0="29374@x" ObjectIDZND1="g_269a310@0" ObjectIDZND2="g_269aec0@0" Pin0InfoVect0LinkObjId="SW-193280_0" Pin0InfoVect1LinkObjId="g_269a310_0" Pin0InfoVect2LinkObjId="g_269aec0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193279_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-962 4851,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2695ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4828,-984 4851,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="29374@1" ObjectIDZND0="29373@x" ObjectIDZND1="g_269a310@0" ObjectIDZND2="g_269aec0@0" Pin0InfoVect0LinkObjId="SW-193279_0" Pin0InfoVect1LinkObjId="g_269a310_0" Pin0InfoVect2LinkObjId="g_269aec0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193280_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4828,-984 4851,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26960e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4874,-962 4874,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_269a310@0" ObjectIDZND0="29373@x" ObjectIDZND1="29374@x" ObjectIDZND2="g_269aec0@0" Pin0InfoVect0LinkObjId="SW-193279_0" Pin0InfoVect1LinkObjId="SW-193280_0" Pin0InfoVect2LinkObjId="g_269aec0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_269a310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4874,-962 4874,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2696300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-984 4874,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="29373@x" ObjectIDND1="29374@x" ObjectIDZND0="g_269a310@0" ObjectIDZND1="g_269aec0@0" ObjectIDZND2="38120@1" Pin0InfoVect0LinkObjId="g_269a310_0" Pin0InfoVect1LinkObjId="g_269aec0_0" Pin0InfoVect2LinkObjId="g_2696520_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193279_0" Pin1InfoVect1LinkObjId="SW-193280_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-984 4874,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2696520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4892,-984 5039,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_269a310@0" ObjectIDND1="29373@x" ObjectIDND2="29374@x" ObjectIDZND0="38120@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_269a310_0" Pin1InfoVect1LinkObjId="SW-193279_0" Pin1InfoVect2LinkObjId="SW-193280_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4892,-984 5039,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_268a030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4921,-1033 4892,-1033 4892,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_269aec0@1" ObjectIDZND0="g_269a310@0" ObjectIDZND1="29373@x" ObjectIDZND2="29374@x" Pin0InfoVect0LinkObjId="g_269a310_0" Pin0InfoVect1LinkObjId="SW-193279_0" Pin0InfoVect2LinkObjId="SW-193280_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_269aec0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4921,-1033 4892,-1033 4892,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_268a250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4874,-984 4892,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_269a310@0" ObjectIDND1="29373@x" ObjectIDND2="29374@x" ObjectIDZND0="g_269aec0@0" ObjectIDZND1="38120@1" Pin0InfoVect0LinkObjId="g_269aec0_0" Pin0InfoVect1LinkObjId="g_2696520_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_269a310_0" Pin1InfoVect1LinkObjId="SW-193279_0" Pin1InfoVect2LinkObjId="SW-193280_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4874,-984 4892,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25d2840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4728,-1097 4747,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29370@1" ObjectIDZND0="29331@1" Pin0InfoVect0LinkObjId="SW-193250_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193276_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4728,-1097 4747,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25d2a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4774,-1097 4792,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29331@0" ObjectIDZND0="29371@0" Pin0InfoVect0LinkObjId="SW-193278_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4774,-1097 4792,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25d2c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-1039 4851,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29372@0" ObjectIDZND0="g_25d3490@0" Pin0InfoVect0LinkObjId="g_25d3490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193277_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-1039 4851,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25d2ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4955,-1146 4978,-1146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_257ae60@0" ObjectIDZND0="g_25d1ef0@0" Pin0InfoVect0LinkObjId="g_25d1ef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_257ae60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4955,-1146 4978,-1146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26ad770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-1075 4851,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="29372@1" ObjectIDZND0="29371@x" ObjectIDZND1="g_257a2b0@0" ObjectIDZND2="g_257ae60@0" Pin0InfoVect0LinkObjId="SW-193278_0" Pin0InfoVect1LinkObjId="g_257a2b0_0" Pin0InfoVect2LinkObjId="g_257ae60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193277_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-1075 4851,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26ad990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4828,-1097 4851,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="29371@1" ObjectIDZND0="29372@x" ObjectIDZND1="g_257a2b0@0" ObjectIDZND2="g_257ae60@0" Pin0InfoVect0LinkObjId="SW-193277_0" Pin0InfoVect1LinkObjId="g_257a2b0_0" Pin0InfoVect2LinkObjId="g_257ae60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193278_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4828,-1097 4851,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26adbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4874,-1075 4874,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_257a2b0@0" ObjectIDZND0="29372@x" ObjectIDZND1="29371@x" ObjectIDZND2="g_257ae60@0" Pin0InfoVect0LinkObjId="SW-193277_0" Pin0InfoVect1LinkObjId="SW-193278_0" Pin0InfoVect2LinkObjId="g_257ae60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_257a2b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4874,-1075 4874,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26addd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-1097 4874,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="29372@x" ObjectIDND1="29371@x" ObjectIDZND0="g_257a2b0@0" ObjectIDZND1="g_257ae60@0" ObjectIDZND2="38119@1" Pin0InfoVect0LinkObjId="g_257a2b0_0" Pin0InfoVect1LinkObjId="g_257ae60_0" Pin0InfoVect2LinkObjId="g_26adff0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193277_0" Pin1InfoVect1LinkObjId="SW-193278_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-1097 4874,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26adff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4892,-1097 5040,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_257a2b0@0" ObjectIDND1="29372@x" ObjectIDND2="29371@x" ObjectIDZND0="38119@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_257a2b0_0" Pin1InfoVect1LinkObjId="SW-193277_0" Pin1InfoVect2LinkObjId="SW-193278_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4892,-1097 5040,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26ae210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4924,-1146 4892,-1146 4892,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_257ae60@1" ObjectIDZND0="g_257a2b0@0" ObjectIDZND1="29372@x" ObjectIDZND2="29371@x" Pin0InfoVect0LinkObjId="g_257a2b0_0" Pin0InfoVect1LinkObjId="SW-193277_0" Pin0InfoVect2LinkObjId="SW-193278_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_257ae60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4924,-1146 4892,-1146 4892,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26ae430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4874,-1097 4892,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_257a2b0@0" ObjectIDND1="29372@x" ObjectIDND2="29371@x" ObjectIDZND0="g_257ae60@0" ObjectIDZND1="38119@1" Pin0InfoVect0LinkObjId="g_257ae60_0" Pin0InfoVect1LinkObjId="g_26adff0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_257a2b0_0" Pin1InfoVect1LinkObjId="SW-193277_0" Pin1InfoVect2LinkObjId="SW-193278_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4874,-1097 4892,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25b5a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4728,-1209 4747,-1209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="19820@1" ObjectIDZND0="19818@1" Pin0InfoVect0LinkObjId="SW-94042_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94044_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4728,-1209 4747,-1209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25b5c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4774,-1209 4792,-1209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="19818@0" ObjectIDZND0="19819@0" Pin0InfoVect0LinkObjId="SW-94043_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94042_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4774,-1209 4792,-1209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25b5ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-1151 4851,-1136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29328@0" ObjectIDZND0="g_25b6830@0" Pin0InfoVect0LinkObjId="g_25b6830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-200315_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-1151 4851,-1136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25b6120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4952,-1258 4975,-1258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_25b51a0@0" ObjectIDZND0="g_25b5540@0" Pin0InfoVect0LinkObjId="g_25b5540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25b51a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4952,-1258 4975,-1258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25b73d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-1187 4851,-1209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="29328@1" ObjectIDZND0="19819@x" ObjectIDZND1="g_26b01e0@0" ObjectIDZND2="g_25b51a0@0" Pin0InfoVect0LinkObjId="SW-94043_0" Pin0InfoVect1LinkObjId="g_26b01e0_0" Pin0InfoVect2LinkObjId="g_25b51a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-200315_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-1187 4851,-1209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25b7630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4828,-1209 4851,-1209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="19819@1" ObjectIDZND0="29328@x" ObjectIDZND1="g_26b01e0@0" ObjectIDZND2="g_25b51a0@0" Pin0InfoVect0LinkObjId="SW-200315_0" Pin0InfoVect1LinkObjId="g_26b01e0_0" Pin0InfoVect2LinkObjId="g_25b51a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94043_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4828,-1209 4851,-1209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25b7890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4874,-1187 4874,-1209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_26b01e0@0" ObjectIDZND0="29328@x" ObjectIDZND1="19819@x" ObjectIDZND2="g_25b51a0@0" Pin0InfoVect0LinkObjId="SW-200315_0" Pin0InfoVect1LinkObjId="SW-94043_0" Pin0InfoVect2LinkObjId="g_25b51a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26b01e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4874,-1187 4874,-1209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25b7af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-1209 4874,-1209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="29328@x" ObjectIDND1="19819@x" ObjectIDZND0="g_26b01e0@0" ObjectIDZND1="g_25b51a0@0" ObjectIDZND2="38111@1" Pin0InfoVect0LinkObjId="g_26b01e0_0" Pin0InfoVect1LinkObjId="g_25b51a0_0" Pin0InfoVect2LinkObjId="g_25b7d50_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-200315_0" Pin1InfoVect1LinkObjId="SW-94043_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-1209 4874,-1209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25b7d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4892,-1209 5041,-1209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_26b01e0@0" ObjectIDND1="29328@x" ObjectIDND2="19819@x" ObjectIDZND0="38111@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_26b01e0_0" Pin1InfoVect1LinkObjId="SW-200315_0" Pin1InfoVect2LinkObjId="SW-94043_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4892,-1209 5041,-1209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26156d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4921,-1258 4892,-1258 4892,-1209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_25b51a0@1" ObjectIDZND0="g_26b01e0@0" ObjectIDZND1="29328@x" ObjectIDZND2="19819@x" Pin0InfoVect0LinkObjId="g_26b01e0_0" Pin0InfoVect1LinkObjId="SW-200315_0" Pin0InfoVect2LinkObjId="SW-94043_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25b51a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4921,-1258 4892,-1258 4892,-1209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2615930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4874,-1209 4892,-1209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_26b01e0@0" ObjectIDND1="29328@x" ObjectIDND2="19819@x" ObjectIDZND0="g_25b51a0@0" ObjectIDZND1="38111@1" Pin0InfoVect0LinkObjId="g_25b51a0_0" Pin0InfoVect1LinkObjId="g_25b7d50_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_26b01e0_0" Pin1InfoVect1LinkObjId="SW-200315_0" Pin1InfoVect2LinkObjId="SW-94043_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4874,-1209 4892,-1209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2616850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4469,-731 4446,-731 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2616480@0" ObjectIDZND0="g_2615b90@0" Pin0InfoVect0LinkObjId="g_2615b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2616480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4469,-731 4446,-731 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2616e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4632,-757 4594,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29321@0" ObjectIDZND0="29369@1" Pin0InfoVect0LinkObjId="SW-193275_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24c7160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4632,-757 4594,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2612390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4537,-757 4558,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2616480@0" ObjectIDND1="g_2616ab0@0" ObjectIDZND0="29369@0" Pin0InfoVect0LinkObjId="SW-193275_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2616480_0" Pin1InfoVect1LinkObjId="g_2616ab0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4537,-757 4558,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2614df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-349 4116,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19827@0" ObjectIDZND0="29330@0" Pin0InfoVect0LinkObjId="SW-200313_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_262a690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-349 4116,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2568860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4690,-631 4632,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29384@0" ObjectIDZND0="29321@0" Pin0InfoVect0LinkObjId="g_24c7160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4690,-631 4632,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2663410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4868,-631 4810,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2568ac0@1" ObjectIDZND0="g_2662c50@0" Pin0InfoVect0LinkObjId="g_2662c50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2568ac0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4868,-631 4810,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2663670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4779,-631 4726,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2662c50@1" ObjectIDZND0="29384@1" Pin0InfoVect0LinkObjId="SW-193290_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2662c50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4779,-631 4726,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25ebf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-325 5060,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29367@1" ObjectIDZND0="19826@0" Pin0InfoVect0LinkObjId="g_25e8d10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193273_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5060,-325 5060,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25ec1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5031,-172 5031,-193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_25ec670@0" ObjectIDZND0="g_233bd10@0" Pin0InfoVect0LinkObjId="g_233bd10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25ec670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5031,-172 5031,-193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25ec410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5031,-224 5031,-237 5060,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_233bd10@1" ObjectIDZND0="g_2310a00@0" ObjectIDZND1="29367@x" ObjectIDZND2="29401@x" Pin0InfoVect0LinkObjId="g_2310a00_0" Pin0InfoVect1LinkObjId="SW-193273_0" Pin0InfoVect2LinkObjId="SW-193304_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_233bd10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5031,-224 5031,-237 5060,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25e7dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-237 5060,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2310a00@0" ObjectIDND1="g_233bd10@0" ObjectIDZND0="29367@x" ObjectIDZND1="29401@x" Pin0InfoVect0LinkObjId="SW-193273_0" Pin0InfoVect1LinkObjId="SW-193304_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2310a00_0" Pin1InfoVect1LinkObjId="g_233bd10_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5060,-237 5060,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25e8020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-264 5060,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2310a00@0" ObjectIDND1="g_233bd10@0" ObjectIDND2="29401@x" ObjectIDZND0="29367@0" Pin0InfoVect0LinkObjId="SW-193273_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2310a00_0" Pin1InfoVect1LinkObjId="g_233bd10_0" Pin1InfoVect2LinkObjId="SW-193304_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5060,-264 5060,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25e8280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5123,-264 5114,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_25e6aa0@0" ObjectIDZND0="29401@1" Pin0InfoVect0LinkObjId="SW-193304_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25e6aa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5123,-264 5114,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25e84e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5078,-264 5060,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="29401@0" ObjectIDZND0="g_2310a00@0" ObjectIDZND1="g_233bd10@0" ObjectIDZND2="29367@x" Pin0InfoVect0LinkObjId="g_2310a00_0" Pin0InfoVect1LinkObjId="g_233bd10_0" Pin0InfoVect2LinkObjId="SW-193273_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193304_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5078,-264 5060,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25e8d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4955,-333 4955,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29364@1" ObjectIDZND0="19826@0" Pin0InfoVect0LinkObjId="g_25ebf50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193269_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4955,-333 4955,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_267f110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4924,-224 4955,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_267e3a0@0" ObjectIDZND0="29365@x" ObjectIDZND1="29398@x" Pin0InfoVect0LinkObjId="SW-193270_0" Pin0InfoVect1LinkObjId="SW-193254_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_267e3a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4924,-224 4955,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2624d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5018,-281 5009,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2624300@0" ObjectIDZND0="29363@1" Pin0InfoVect0LinkObjId="SW-193268_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2624300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5018,-281 5009,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2624ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4973,-281 4955,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29363@0" ObjectIDZND0="29398@x" ObjectIDZND1="29364@x" Pin0InfoVect0LinkObjId="SW-193254_0" Pin0InfoVect1LinkObjId="SW-193269_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193268_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4973,-281 4955,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2628010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4955,-265 4955,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29398@1" ObjectIDZND0="29363@x" ObjectIDZND1="29364@x" Pin0InfoVect0LinkObjId="SW-193268_0" Pin0InfoVect1LinkObjId="SW-193269_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193254_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4955,-265 4955,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_259fee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4955,-281 4955,-297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="29363@x" ObjectIDND1="29398@x" ObjectIDZND0="29364@0" Pin0InfoVect0LinkObjId="SW-193269_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193268_0" Pin1InfoVect1LinkObjId="SW-193254_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4955,-281 4955,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25a09b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4955,-210 4955,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="29365@1" ObjectIDZND0="g_267e3a0@0" ObjectIDZND1="29398@x" Pin0InfoVect0LinkObjId="g_267e3a0_0" Pin0InfoVect1LinkObjId="SW-193254_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193270_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4955,-210 4955,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25a0c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4955,-224 4955,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_267e3a0@0" ObjectIDND1="29365@x" ObjectIDZND0="29398@0" Pin0InfoVect0LinkObjId="SW-193254_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_267e3a0_0" Pin1InfoVect1LinkObjId="SW-193270_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4955,-224 4955,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_261f320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4955,-6 4955,-39 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" ObjectIDND0="41641@0" ObjectIDZND0="29366@0" Pin0InfoVect0LinkObjId="SW-248340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_DXS.014Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4955,-6 4955,-39 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_261f5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4955,-75 4955,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="29366@1" ObjectIDZND0="29365@0" Pin0InfoVect0LinkObjId="SW-193270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-248340_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4955,-75 4955,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_261f7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4852,-333 4852,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29356@1" ObjectIDZND0="19826@0" Pin0InfoVect0LinkObjId="g_25ebf50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193265_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4852,-333 4852,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b1a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4821,-224 4852,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_25b0cf0@0" ObjectIDZND0="29357@x" ObjectIDZND1="29397@x" Pin0InfoVect0LinkObjId="SW-193266_0" Pin0InfoVect1LinkObjId="SW-193252_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25b0cf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4821,-224 4852,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2642500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4915,-281 4906,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2641ad0@0" ObjectIDZND0="29355@1" Pin0InfoVect0LinkObjId="SW-193264_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2641ad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4915,-281 4906,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2642760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4870,-281 4852,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29355@0" ObjectIDZND0="29397@x" ObjectIDZND1="29356@x" Pin0InfoVect0LinkObjId="SW-193252_0" Pin0InfoVect1LinkObjId="SW-193265_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193264_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4870,-281 4852,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2644e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4852,-265 4852,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29397@1" ObjectIDZND0="29355@x" ObjectIDZND1="29356@x" Pin0InfoVect0LinkObjId="SW-193264_0" Pin0InfoVect1LinkObjId="SW-193265_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193252_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4852,-265 4852,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2645070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4852,-281 4852,-297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="29397@x" ObjectIDND1="29355@x" ObjectIDZND0="29356@0" Pin0InfoVect0LinkObjId="SW-193265_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193252_0" Pin1InfoVect1LinkObjId="SW-193264_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4852,-281 4852,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26452d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4852,-210 4852,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="29357@1" ObjectIDZND0="g_25b0cf0@0" ObjectIDZND1="29397@x" Pin0InfoVect0LinkObjId="g_25b0cf0_0" Pin0InfoVect1LinkObjId="SW-193252_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193266_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4852,-210 4852,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2645530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4852,-224 4852,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="29357@x" ObjectIDND1="g_25b0cf0@0" ObjectIDZND0="29397@0" Pin0InfoVect0LinkObjId="SW-193252_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193266_0" Pin1InfoVect1LinkObjId="g_25b0cf0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4852,-224 4852,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2572170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4852,-6 4852,-39 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" ObjectIDND0="41640@0" ObjectIDZND0="29358@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_DXS.013Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4852,-6 4852,-39 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25723d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4852,-75 4852,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="29358@1" ObjectIDZND0="29357@0" Pin0InfoVect0LinkObjId="SW-193266_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4852,-75 4852,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2573750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4747,-333 4747,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29360@1" ObjectIDZND0="19826@0" Pin0InfoVect0LinkObjId="g_25ebf50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193271_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4747,-333 4747,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2604c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4716,-224 4747,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_2603ee0@0" ObjectIDZND0="29361@x" ObjectIDZND1="29396@x" Pin0InfoVect0LinkObjId="SW-193272_0" Pin0InfoVect1LinkObjId="SW-193253_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2603ee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4716,-224 4747,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2605940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4810,-281 4801,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2604eb0@0" ObjectIDZND0="29359@1" Pin0InfoVect0LinkObjId="SW-193267_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2604eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4810,-281 4801,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2605ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4765,-281 4747,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29359@0" ObjectIDZND0="29396@x" ObjectIDZND1="29360@x" Pin0InfoVect0LinkObjId="SW-193253_0" Pin0InfoVect1LinkObjId="SW-193271_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193267_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4765,-281 4747,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_255dfc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4747,-265 4747,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29396@1" ObjectIDZND0="29359@x" ObjectIDZND1="29360@x" Pin0InfoVect0LinkObjId="SW-193267_0" Pin0InfoVect1LinkObjId="SW-193271_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193253_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4747,-265 4747,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_255e220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4747,-281 4747,-297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="29396@x" ObjectIDND1="29359@x" ObjectIDZND0="29360@0" Pin0InfoVect0LinkObjId="SW-193271_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193253_0" Pin1InfoVect1LinkObjId="SW-193267_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4747,-281 4747,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_255e480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4747,-210 4747,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="29361@1" ObjectIDZND0="g_2603ee0@0" ObjectIDZND1="29396@x" Pin0InfoVect0LinkObjId="g_2603ee0_0" Pin0InfoVect1LinkObjId="SW-193253_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193272_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4747,-210 4747,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_255e6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4747,-224 4747,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="29361@x" ObjectIDND1="g_2603ee0@0" ObjectIDZND0="29396@0" Pin0InfoVect0LinkObjId="SW-193253_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193272_0" Pin1InfoVect1LinkObjId="g_2603ee0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4747,-224 4747,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2561be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4747,-6 4747,-39 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" ObjectIDND0="41638@0" ObjectIDZND0="29362@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_DXS.012Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4747,-6 4747,-39 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2561e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4747,-75 4747,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="29362@1" ObjectIDZND0="29361@0" Pin0InfoVect0LinkObjId="SW-193272_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4747,-75 4747,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2563d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4638,-333 4638,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29353@1" ObjectIDZND0="19826@0" Pin0InfoVect0LinkObjId="g_25ebf50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193262_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4638,-333 4638,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25eeec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4607,-224 4638,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_25aadd0@0" ObjectIDZND0="29354@x" ObjectIDZND1="29395@x" Pin0InfoVect0LinkObjId="SW-193263_0" Pin0InfoVect1LinkObjId="SW-193251_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25aadd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4607,-224 4638,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25efb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4701,-281 4692,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_25ef120@0" ObjectIDZND0="29348@1" Pin0InfoVect0LinkObjId="SW-193261_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25ef120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4701,-281 4692,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25efdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4656,-281 4638,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29348@0" ObjectIDZND0="29395@x" ObjectIDZND1="29353@x" Pin0InfoVect0LinkObjId="SW-193251_0" Pin0InfoVect1LinkObjId="SW-193262_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193261_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4656,-281 4638,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25f2560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4638,-265 4638,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29395@1" ObjectIDZND0="29348@x" ObjectIDZND1="29353@x" Pin0InfoVect0LinkObjId="SW-193261_0" Pin0InfoVect1LinkObjId="SW-193262_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193251_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4638,-265 4638,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25f27c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4638,-281 4638,-297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="29395@x" ObjectIDND1="29348@x" ObjectIDZND0="29353@0" Pin0InfoVect0LinkObjId="SW-193262_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193251_0" Pin1InfoVect1LinkObjId="SW-193261_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4638,-281 4638,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25f2a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4638,-210 4638,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="29354@1" ObjectIDZND0="g_25aadd0@0" ObjectIDZND1="29395@x" Pin0InfoVect0LinkObjId="g_25aadd0_0" Pin0InfoVect1LinkObjId="SW-193251_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193263_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4638,-210 4638,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25f2c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4638,-224 4638,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="29354@x" ObjectIDND1="g_25aadd0@0" ObjectIDZND0="29395@0" Pin0InfoVect0LinkObjId="SW-193251_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193263_0" Pin1InfoVect1LinkObjId="g_25aadd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4638,-224 4638,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2628cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4638,-6 4638,-39 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" ObjectIDND0="41639@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="CB-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_DXS.011Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4638,-6 4638,-39 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2628ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4638,-75 4638,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="29354@0" Pin0InfoVect0LinkObjId="SW-193263_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4638,-75 4638,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_262a690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4398,-330 4398,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29391@1" ObjectIDZND0="19827@0" Pin0InfoVect0LinkObjId="g_25c0a20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193305_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4398,-330 4398,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_262fb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4461,-280 4452,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_262f100@0" ObjectIDZND0="29390@1" Pin0InfoVect0LinkObjId="SW-193303_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_262f100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4461,-280 4452,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2526830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4416,-280 4398,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29390@0" ObjectIDZND0="29347@x" ObjectIDZND1="29391@x" Pin0InfoVect0LinkObjId="SW-193260_0" Pin0InfoVect1LinkObjId="SW-193305_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193303_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4416,-280 4398,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2526a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4398,-264 4398,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29347@1" ObjectIDZND0="29390@x" ObjectIDZND1="29391@x" Pin0InfoVect0LinkObjId="SW-193303_0" Pin0InfoVect1LinkObjId="SW-193305_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193260_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4398,-264 4398,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2526cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4398,-280 4398,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="29347@x" ObjectIDND1="29390@x" ObjectIDZND0="29391@0" Pin0InfoVect0LinkObjId="SW-193305_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193260_0" Pin1InfoVect1LinkObjId="SW-193303_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4398,-280 4398,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_252a4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4367,-224 4398,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_2529750@0" ObjectIDZND0="29389@x" ObjectIDZND1="29347@x" Pin0InfoVect0LinkObjId="SW-200314_0" Pin0InfoVect1LinkObjId="SW-193260_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2529750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4367,-224 4398,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_252a720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4398,-210 4398,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="29389@1" ObjectIDZND0="g_2529750@0" ObjectIDZND1="29347@x" Pin0InfoVect0LinkObjId="g_2529750_0" Pin0InfoVect1LinkObjId="SW-193260_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-200314_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4398,-210 4398,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_252a980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4398,-224 4398,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="29389@x" ObjectIDND1="g_2529750@0" ObjectIDZND0="29347@0" Pin0InfoVect0LinkObjId="SW-193260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-200314_0" Pin1InfoVect1LinkObjId="g_2529750_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4398,-224 4398,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_25c0a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4289,-330 4289,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29388@1" ObjectIDZND0="19827@0" Pin0InfoVect0LinkObjId="g_262a690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193299_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4289,-330 4289,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_25c1ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4289,-131 4289,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_252c2d0@1" ObjectIDZND0="g_25c1250@0" Pin0InfoVect0LinkObjId="g_25c1250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_252c2d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4289,-131 4289,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_25c27c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4345,-266 4336,-266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_25c1d30@0" ObjectIDZND0="29399@1" Pin0InfoVect0LinkObjId="SW-193294_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25c1d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4345,-266 4336,-266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_25c4f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4300,-266 4289,-266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="29399@0" ObjectIDZND0="g_25c1250@0" ObjectIDZND1="29388@x" Pin0InfoVect0LinkObjId="g_25c1250_0" Pin0InfoVect1LinkObjId="SW-193299_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193294_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4300,-266 4289,-266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_25c5a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4289,-230 4289,-266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_25c1250@1" ObjectIDZND0="29399@x" ObjectIDZND1="29388@x" Pin0InfoVect0LinkObjId="SW-193294_0" Pin0InfoVect1LinkObjId="SW-193299_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25c1250_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4289,-230 4289,-266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_25c5ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4289,-266 4289,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="29399@x" ObjectIDND1="g_25c1250@0" ObjectIDZND0="29388@0" Pin0InfoVect0LinkObjId="SW-193299_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193294_0" Pin1InfoVect1LinkObjId="g_25c1250_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4289,-266 4289,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_25c6530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4095,-330 4095,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19777@1" ObjectIDZND0="19827@0" Pin0InfoVect0LinkObjId="g_262a690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-93968_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4095,-330 4095,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_25cd6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4095,-234 4095,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="19776@1" ObjectIDZND0="19777@x" ObjectIDZND1="29320@x" Pin0InfoVect0LinkObjId="SW-93968_0" Pin0InfoVect1LinkObjId="SW-193293_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-200312_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4095,-234 4095,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_25cd900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4095,-272 4095,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="19776@x" ObjectIDND1="29320@x" ObjectIDZND0="19777@0" Pin0InfoVect0LinkObjId="SW-93968_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-200312_0" Pin1InfoVect1LinkObjId="SW-193293_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4095,-272 4095,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_25cdb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-178 4095,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="hydroGenerator" EndDevType2="lightningRod" ObjectIDND0="g_25ce650@0" ObjectIDZND0="19776@x" ObjectIDZND1="0@x" ObjectIDZND2="g_254eeb0@0" Pin0InfoVect0LinkObjId="SW-200312_0" Pin0InfoVect1LinkObjId="CB-0_0" Pin0InfoVect2LinkObjId="g_254eeb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25ce650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4080,-178 4095,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_254ce60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4095,-178 4095,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="hydroGenerator" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="g_25ce650@0" ObjectIDND1="0@x" ObjectIDND2="g_254eeb0@0" ObjectIDZND0="19776@0" Pin0InfoVect0LinkObjId="SW-200312_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25ce650_0" Pin1InfoVect1LinkObjId="CB-0_0" Pin1InfoVect2LinkObjId="g_254eeb0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4095,-178 4095,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_254d0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4095,-28 4095,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_25ce650@0" ObjectIDZND1="19776@x" ObjectIDZND2="g_254eeb0@0" Pin0InfoVect0LinkObjId="g_25ce650_0" Pin0InfoVect1LinkObjId="SW-200312_0" Pin0InfoVect2LinkObjId="g_254eeb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4095,-28 4095,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_254f670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-81 4132,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_254d300@0" ObjectIDZND0="g_254eeb0@0" Pin0InfoVect0LinkObjId="g_254eeb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_254d300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-81 4132,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2550090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4172,-67 4172,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_254f8d0@0" Pin0InfoVect0LinkObjId="g_254f8d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4172,-67 4172,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_25539d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4172,-125 4172,-114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="19778@0" ObjectIDZND0="g_254f8d0@1" Pin0InfoVect0LinkObjId="g_254f8d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-93969_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4172,-125 4172,-114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2553c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-128 4132,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="hydroGenerator" ObjectIDND0="g_254eeb0@1" ObjectIDZND0="g_25ce650@0" ObjectIDZND1="19776@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_25ce650_0" Pin0InfoVect1LinkObjId="SW-200312_0" Pin0InfoVect2LinkObjId="CB-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_254eeb0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-128 4132,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2554c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4095,-178 4132,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="hydroGenerator" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_25ce650@0" ObjectIDND1="19776@x" ObjectIDND2="0@x" ObjectIDZND0="g_254eeb0@0" ObjectIDZND1="19778@x" Pin0InfoVect0LinkObjId="g_254eeb0_0" Pin0InfoVect1LinkObjId="SW-93969_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25ce650_0" Pin1InfoVect1LinkObjId="SW-200312_0" Pin1InfoVect2LinkObjId="CB-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4095,-178 4132,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2554e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-178 4172,-178 4172,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="g_254eeb0@0" ObjectIDND1="g_25ce650@0" ObjectIDND2="19776@x" ObjectIDZND0="19778@1" Pin0InfoVect0LinkObjId="SW-93969_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_254eeb0_0" Pin1InfoVect1LinkObjId="g_25ce650_0" Pin1InfoVect2LinkObjId="SW-200312_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-178 4172,-178 4172,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_25568a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4035,-273 4044,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_25561d0@0" ObjectIDZND0="29320@0" Pin0InfoVect0LinkObjId="SW-193293_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25561d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4035,-273 4044,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2556a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-273 4095,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29320@1" ObjectIDZND0="19776@x" ObjectIDZND1="19777@x" Pin0InfoVect0LinkObjId="SW-200312_0" Pin0InfoVect1LinkObjId="SW-93968_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193293_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4080,-273 4095,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2556f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-330 3858,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19774@1" ObjectIDZND0="19827@0" Pin0InfoVect0LinkObjId="g_262a690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-93965_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-330 3858,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_25816c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-234 3858,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="19773@1" ObjectIDZND0="19774@x" ObjectIDZND1="29319@x" Pin0InfoVect0LinkObjId="SW-93965_0" Pin0InfoVect1LinkObjId="SW-193292_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-93964_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-234 3858,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2581920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-272 3858,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="19773@x" ObjectIDND1="29319@x" ObjectIDZND0="19774@0" Pin0InfoVect0LinkObjId="SW-93965_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-93964_0" Pin1InfoVect1LinkObjId="SW-193292_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-272 3858,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2581b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-178 3858,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="hydroGenerator" EndDevType2="lightningRod" ObjectIDND0="g_2581de0@0" ObjectIDZND0="19773@x" ObjectIDZND1="0@x" ObjectIDZND2="g_2585af0@0" Pin0InfoVect0LinkObjId="SW-93964_0" Pin0InfoVect1LinkObjId="CB-0_0" Pin0InfoVect2LinkObjId="g_2585af0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2581de0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-178 3858,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_25839a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-178 3858,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="hydroGenerator" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2581de0@0" ObjectIDND1="0@x" ObjectIDND2="g_2585af0@0" ObjectIDZND0="19773@0" Pin0InfoVect0LinkObjId="SW-93964_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2581de0_0" Pin1InfoVect1LinkObjId="CB-0_0" Pin1InfoVect2LinkObjId="g_2585af0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-178 3858,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2583c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-28 3858,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2581de0@0" ObjectIDZND1="19773@x" ObjectIDZND2="g_2585af0@0" Pin0InfoVect0LinkObjId="g_2581de0_0" Pin0InfoVect1LinkObjId="SW-93964_0" Pin0InfoVect2LinkObjId="g_2585af0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-28 3858,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2586370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3895,-81 3895,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2583e60@0" ObjectIDZND0="g_2585af0@0" Pin0InfoVect0LinkObjId="g_2585af0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2583e60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3895,-81 3895,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2586e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3935,-67 3935,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_25865d0@0" Pin0InfoVect0LinkObjId="g_25865d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3935,-67 3935,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_258aa90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3935,-125 3935,-114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="19775@0" ObjectIDZND0="g_25865d0@1" Pin0InfoVect0LinkObjId="g_25865d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-93966_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3935,-125 3935,-114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_258acf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3895,-128 3895,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="hydroGenerator" ObjectIDND0="g_2585af0@1" ObjectIDZND0="g_2581de0@0" ObjectIDZND1="19773@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2581de0_0" Pin0InfoVect1LinkObjId="SW-93964_0" Pin0InfoVect2LinkObjId="CB-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2585af0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3895,-128 3895,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_258b580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-178 3895,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="hydroGenerator" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2581de0@0" ObjectIDND1="19773@x" ObjectIDND2="0@x" ObjectIDZND0="g_2585af0@0" ObjectIDZND1="19775@x" Pin0InfoVect0LinkObjId="g_2585af0_0" Pin0InfoVect1LinkObjId="SW-93966_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2581de0_0" Pin1InfoVect1LinkObjId="SW-93964_0" Pin1InfoVect2LinkObjId="CB-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-178 3895,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_258b770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3895,-178 3935,-178 3935,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="g_2585af0@0" ObjectIDND1="g_2581de0@0" ObjectIDND2="19773@x" ObjectIDZND0="19775@1" Pin0InfoVect0LinkObjId="SW-93966_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2585af0_0" Pin1InfoVect1LinkObjId="g_2581de0_0" Pin1InfoVect2LinkObjId="SW-93964_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3895,-178 3935,-178 3935,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_258c4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3798,-273 3807,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_258bc80@0" ObjectIDZND0="29319@0" Pin0InfoVect0LinkObjId="SW-193292_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_258bc80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3798,-273 3807,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_258c6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-273 3858,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29319@1" ObjectIDZND0="19773@x" ObjectIDZND1="19774@x" Pin0InfoVect0LinkObjId="SW-93964_0" Pin0InfoVect1LinkObjId="SW-93965_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193292_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-273 3858,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_258f3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3617,-330 3617,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19771@1" ObjectIDZND0="19827@0" Pin0InfoVect0LinkObjId="g_262a690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-93962_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3617,-330 3617,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2532eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3617,-234 3617,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="19770@1" ObjectIDZND0="19771@x" ObjectIDZND1="29318@x" Pin0InfoVect0LinkObjId="SW-93962_0" Pin0InfoVect1LinkObjId="SW-193291_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-93961_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3617,-234 3617,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2533110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3617,-272 3617,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="19770@x" ObjectIDND1="29318@x" ObjectIDZND0="19771@0" Pin0InfoVect0LinkObjId="SW-93962_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-93961_0" Pin1InfoVect1LinkObjId="SW-193291_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3617,-272 3617,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2533370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3602,-178 3617,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_25335d0@0" ObjectIDZND0="19770@x" ObjectIDZND1="g_25377c0@0" ObjectIDZND2="19772@x" Pin0InfoVect0LinkObjId="SW-93961_0" Pin0InfoVect1LinkObjId="g_25377c0_0" Pin0InfoVect2LinkObjId="SW-93963_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25335d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3602,-178 3617,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2535570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3617,-178 3617,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="g_25335d0@0" ObjectIDND1="g_25377c0@0" ObjectIDND2="19772@x" ObjectIDZND0="19770@0" Pin0InfoVect0LinkObjId="SW-93961_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25335d0_0" Pin1InfoVect1LinkObjId="g_25377c0_0" Pin1InfoVect2LinkObjId="SW-93963_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3617,-178 3617,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_25357d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3617,-28 3617,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="19770@x" ObjectIDZND1="g_25335d0@0" ObjectIDZND2="g_25377c0@0" Pin0InfoVect0LinkObjId="SW-93961_0" Pin0InfoVect1LinkObjId="g_25335d0_0" Pin0InfoVect2LinkObjId="g_25377c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3617,-28 3617,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2538040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3654,-81 3654,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2535a30@0" ObjectIDZND0="g_25377c0@0" Pin0InfoVect0LinkObjId="g_25377c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2535a30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3654,-81 3654,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2538b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-67 3694,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_25382a0@0" Pin0InfoVect0LinkObjId="g_25382a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-67 3694,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_253c760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3694,-125 3694,-114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="19772@0" ObjectIDZND0="g_25382a0@1" Pin0InfoVect0LinkObjId="g_25382a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-93963_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3694,-125 3694,-114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_253c9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3654,-128 3654,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="hydroGenerator" ObjectIDND0="g_25377c0@1" ObjectIDZND0="19770@x" ObjectIDZND1="g_25335d0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-93961_0" Pin0InfoVect1LinkObjId="g_25335d0_0" Pin0InfoVect2LinkObjId="CB-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25377c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3654,-128 3654,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_253d250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3617,-178 3654,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="hydroGenerator" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="19770@x" ObjectIDND1="g_25335d0@0" ObjectIDND2="0@x" ObjectIDZND0="g_25377c0@0" ObjectIDZND1="19772@x" Pin0InfoVect0LinkObjId="g_25377c0_0" Pin0InfoVect1LinkObjId="SW-93963_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-93961_0" Pin1InfoVect1LinkObjId="g_25335d0_0" Pin1InfoVect2LinkObjId="CB-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3617,-178 3654,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_253d440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3654,-178 3694,-178 3694,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="hydroGenerator" EndDevType0="switch" ObjectIDND0="19770@x" ObjectIDND1="g_25335d0@0" ObjectIDND2="0@x" ObjectIDZND0="19772@1" Pin0InfoVect0LinkObjId="SW-93963_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-93961_0" Pin1InfoVect1LinkObjId="g_25335d0_0" Pin1InfoVect2LinkObjId="CB-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3654,-178 3694,-178 3694,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_253e190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3557,-273 3566,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_253d950@0" ObjectIDZND0="29318@0" Pin0InfoVect0LinkObjId="SW-193291_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_253d950_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3557,-273 3566,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_253e3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3602,-273 3617,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="29318@1" ObjectIDZND0="19771@x" ObjectIDZND1="19770@x" Pin0InfoVect0LinkObjId="SW-93962_0" Pin0InfoVect1LinkObjId="SW-93961_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193291_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3602,-273 3617,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_270f270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3704,-348 3704,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19827@0" ObjectIDZND0="29387@0" Pin0InfoVect0LinkObjId="SW-193298_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_262a690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3704,-348 3704,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_27133e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3881,-491 3881,-445 3919,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_270faa0@0" ObjectIDZND0="g_2712630@0" ObjectIDZND1="29385@x" ObjectIDZND2="29386@x" Pin0InfoVect0LinkObjId="g_2712630_0" Pin0InfoVect1LinkObjId="SW-193296_0" Pin0InfoVect2LinkObjId="SW-193297_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_270faa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3881,-491 3881,-445 3919,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2716600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3859,-415 3868,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2715b70@0" ObjectIDZND0="29385@0" Pin0InfoVect0LinkObjId="SW-193296_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2715b70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3859,-415 3868,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_27170f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3950,-479 3950,-445 3919,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2712630@0" ObjectIDZND0="g_270faa0@0" ObjectIDZND1="29385@x" ObjectIDZND2="29386@x" Pin0InfoVect0LinkObjId="g_270faa0_0" Pin0InfoVect1LinkObjId="SW-193296_0" Pin0InfoVect2LinkObjId="SW-193297_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2712630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3950,-479 3950,-445 3919,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2717350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3919,-445 3919,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_270faa0@0" ObjectIDND1="g_2712630@0" ObjectIDZND0="29385@x" ObjectIDZND1="29386@x" Pin0InfoVect0LinkObjId="SW-193296_0" Pin0InfoVect1LinkObjId="SW-193297_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_270faa0_0" Pin1InfoVect1LinkObjId="g_2712630_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3919,-445 3919,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2717e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3904,-415 3919,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="29385@1" ObjectIDZND0="g_270faa0@0" ObjectIDZND1="g_2712630@0" ObjectIDZND2="29386@x" Pin0InfoVect0LinkObjId="g_270faa0_0" Pin0InfoVect1LinkObjId="g_2712630_0" Pin0InfoVect2LinkObjId="SW-193297_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193296_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3904,-415 3919,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_27180a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3919,-415 3919,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_270faa0@0" ObjectIDND1="g_2712630@0" ObjectIDND2="29385@x" ObjectIDZND0="29386@1" Pin0InfoVect0LinkObjId="SW-193297_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_270faa0_0" Pin1InfoVect1LinkObjId="g_2712630_0" Pin1InfoVect2LinkObjId="SW-193296_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3919,-415 3919,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_271c680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3644,-415 3653,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_271bbf0@0" ObjectIDZND0="29400@0" Pin0InfoVect0LinkObjId="SW-193295_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_271bbf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3644,-415 3653,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_271c8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3689,-415 3704,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="29400@1" ObjectIDZND0="29387@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-193298_0" Pin0InfoVect1LinkObjId="CB-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193295_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3689,-415 3704,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_24c7710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3704,-402 3704,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="29387@1" ObjectIDZND0="29400@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-193295_0" Pin0InfoVect1LinkObjId="CB-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193298_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3704,-402 3704,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_24c7970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3704,-415 3704,-474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="29400@x" ObjectIDND1="29387@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="CB-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193295_0" Pin1InfoVect1LinkObjId="SW-193298_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3704,-415 3704,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24ca0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-731 4537,-731 4537,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2616480@1" ObjectIDZND0="29369@x" ObjectIDZND1="g_2616ab0@0" Pin0InfoVect0LinkObjId="SW-193275_0" Pin0InfoVect1LinkObjId="g_2616ab0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2616480_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-731 4537,-731 4537,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24ca2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4537,-757 4537,-796 4502,-796 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="29369@x" ObjectIDND1="g_2616480@0" ObjectIDZND0="g_2616ab0@0" Pin0InfoVect0LinkObjId="g_2616ab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193275_0" Pin1InfoVect1LinkObjId="g_2616480_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4537,-757 4537,-796 4502,-796 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24cf4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4469,-919 4446,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_24cec60@0" ObjectIDZND0="g_24cc350@0" Pin0InfoVect0LinkObjId="g_24cc350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24cec60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4469,-919 4446,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24d29e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4537,-945 4558,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_24cec60@0" ObjectIDND1="g_24cf740@0" ObjectIDZND0="29368@0" Pin0InfoVect0LinkObjId="SW-193274_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24cec60_0" Pin1InfoVect1LinkObjId="g_24cf740_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4537,-945 4558,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24d2c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-919 4537,-919 4537,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_24cec60@1" ObjectIDZND0="29368@x" ObjectIDZND1="g_24cf740@0" Pin0InfoVect0LinkObjId="SW-193274_0" Pin0InfoVect1LinkObjId="g_24cf740_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24cec60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-919 4537,-919 4537,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24d2ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4537,-945 4537,-984 4502,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="29368@x" ObjectIDND1="g_24cec60@0" ObjectIDZND0="g_24cf740@0" Pin0InfoVect0LinkObjId="g_24cf740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193274_0" Pin1InfoVect1LinkObjId="g_24cec60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4537,-945 4537,-984 4502,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24d42b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4692,-1209 4632,-1209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19820@0" ObjectIDZND0="19825@0" Pin0InfoVect0LinkObjId="g_24d44a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94044_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4692,-1209 4632,-1209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24d44a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4692,-1097 4632,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29370@0" ObjectIDZND0="19825@0" Pin0InfoVect0LinkObjId="g_24d42b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193276_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4692,-1097 4632,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24d4690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4692,-984 4632,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29375@0" ObjectIDZND0="19825@0" Pin0InfoVect0LinkObjId="g_24d42b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193281_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4692,-984 4632,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24d48c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-900 4632,-900 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29382@0" ObjectIDZND0="19825@0" Pin0InfoVect0LinkObjId="g_24d42b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193288_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-900 4632,-900 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24d4af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4594,-945 4632,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29368@1" ObjectIDZND0="19825@0" Pin0InfoVect0LinkObjId="g_24d42b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193274_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4594,-945 4632,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24d84e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4039,-657 4039,-669 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="transformer" ObjectIDND0="0@1" ObjectIDZND0="g_26c3f80@0" ObjectIDZND1="g_26c1ec0@0" ObjectIDZND2="30500@x" Pin0InfoVect0LinkObjId="g_26c3f80_0" Pin0InfoVect1LinkObjId="g_26c1ec0_0" Pin0InfoVect2LinkObjId="g_24d91a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4039,-657 4039,-669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24d8f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4039,-669 4010,-669 4010,-659 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_26c1ec0@0" ObjectIDND2="30500@x" ObjectIDZND0="g_26c3f80@0" Pin0InfoVect0LinkObjId="g_26c3f80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="CB-0_0" Pin1InfoVect1LinkObjId="g_26c1ec0_0" Pin1InfoVect2LinkObjId="g_24d91a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4039,-669 4010,-669 4010,-659 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24d91a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-656 4067,-669 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_26c1ec0@0" ObjectIDZND0="30500@x" ObjectIDZND1="0@x" ObjectIDZND2="g_26c3f80@0" Pin0InfoVect0LinkObjId="g_2505e20_0" Pin0InfoVect1LinkObjId="CB-0_0" Pin0InfoVect2LinkObjId="g_26c3f80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26c1ec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-656 4067,-669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_24d9c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,-669 4067,-669 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="30500@x" ObjectIDZND0="g_26c1ec0@0" ObjectIDZND1="0@x" ObjectIDZND2="g_26c3f80@0" Pin0InfoVect0LinkObjId="g_26c1ec0_0" Pin0InfoVect1LinkObjId="CB-0_0" Pin0InfoVect2LinkObjId="g_26c3f80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24d91a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4133,-669 4067,-669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_24d9ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4067,-669 4039,-669 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_26c1ec0@0" ObjectIDND1="30500@x" ObjectIDZND0="0@x" ObjectIDZND1="g_26c3f80@0" Pin0InfoVect0LinkObjId="CB-0_0" Pin0InfoVect1LinkObjId="g_26c3f80_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_26c1ec0_0" Pin1InfoVect1LinkObjId="g_24d91a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4067,-669 4039,-669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_24da130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3919,-364 3919,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29386@0" ObjectIDZND0="19827@0" Pin0InfoVect0LinkObjId="g_262a690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193297_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3919,-364 3919,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24f1a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4794,-835 4794,-792 4725,-792 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29335@0" ObjectIDZND0="29383@1" Pin0InfoVect0LinkObjId="SW-193289_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193258_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4794,-835 4794,-792 4725,-792 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24f1cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4725,-900 4794,-900 4794,-862 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29382@1" ObjectIDZND0="29335@1" Pin0InfoVect0LinkObjId="SW-193258_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193288_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4725,-900 4794,-900 4794,-862 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2504b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4398,-174 4398,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" ObjectIDND0="29389@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="CB-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-200314_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4398,-174 4398,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2505740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4550,-562 4633,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="19824@1" ObjectIDZND0="29321@0" Pin0InfoVect0LinkObjId="g_24c7160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94047_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4550,-562 4633,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2505e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4405,-562 4325,-562 4325,-766 4132,-766 4132,-686 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="19823@0" ObjectIDZND0="30500@2" Pin0InfoVect0LinkObjId="g_24d91a0_2" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94046_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4405,-562 4325,-562 4325,-766 4132,-766 4132,-686 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2510d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4572,-419 4572,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29393@1" ObjectIDZND0="19826@0" Pin0InfoVect0LinkObjId="g_25ebf50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193301_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4572,-419 4572,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2511550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4519,-391 4519,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="29394@0" ObjectIDZND0="29393@x" ObjectIDZND1="29336@x" Pin0InfoVect0LinkObjId="SW-193301_0" Pin0InfoVect1LinkObjId="SW-193259_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4519,-391 4519,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2512040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4536,-419 4519,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="29393@0" ObjectIDZND0="29394@x" ObjectIDZND1="29336@x" Pin0InfoVect0LinkObjId="SW-193300_0" Pin0InfoVect1LinkObjId="SW-193259_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193301_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4536,-419 4519,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25122a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4519,-419 4487,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="29394@x" ObjectIDND1="29393@x" ObjectIDZND0="29336@0" Pin0InfoVect0LinkObjId="SW-193259_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193300_0" Pin1InfoVect1LinkObjId="SW-193301_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4519,-419 4487,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2512500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4431,-396 4431,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_250ce70@0" ObjectIDZND0="29392@x" ObjectIDZND1="29336@x" Pin0InfoVect0LinkObjId="SW-193302_0" Pin0InfoVect1LinkObjId="SW-193259_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_250ce70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4431,-396 4431,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2512ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4421,-419 4431,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="29392@1" ObjectIDZND0="g_250ce70@0" ObjectIDZND1="29336@x" Pin0InfoVect0LinkObjId="g_250ce70_0" Pin0InfoVect1LinkObjId="SW-193259_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193302_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4421,-419 4431,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2513250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4431,-419 4460,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_250ce70@0" ObjectIDND1="29392@x" ObjectIDZND0="29336@1" Pin0InfoVect0LinkObjId="SW-193259_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_250ce70_0" Pin1InfoVect1LinkObjId="SW-193302_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4431,-419 4460,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2514160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4385,-419 4302,-419 4302,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="29392@0" ObjectIDZND0="g_25139f0@0" Pin0InfoVect0LinkObjId="g_25139f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193302_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4385,-419 4302,-419 4302,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25143c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4302,-519 4302,-549 4151,-549 4151,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" ObjectIDND0="g_25139f0@1" ObjectIDZND0="30500@0" Pin0InfoVect0LinkObjId="g_24d91a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25139f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4302,-519 4302,-549 4151,-549 4151,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_25153e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4102,-480 4116,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="transformer" ObjectIDND0="g_2514630@0" ObjectIDZND0="23691@x" ObjectIDZND1="30500@x" Pin0InfoVect0LinkObjId="SW-193249_0" Pin0InfoVect1LinkObjId="g_24d91a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2514630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4102,-480 4116,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2515ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-472 4116,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="transformer" ObjectIDND0="23691@1" ObjectIDZND0="g_2514630@0" ObjectIDZND1="30500@x" Pin0InfoVect0LinkObjId="g_2514630_0" Pin0InfoVect1LinkObjId="g_24d91a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193249_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-472 4116,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2516130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4116,-480 4116,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="transformer" ObjectIDND0="g_2514630@0" ObjectIDND1="23691@x" ObjectIDZND0="30500@1" Pin0InfoVect0LinkObjId="g_24d91a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2514630_0" Pin1InfoVect1LinkObjId="SW-193249_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4116,-480 4116,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2517140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4452,-586 4452,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_2516390@0" ObjectIDZND0="19823@x" ObjectIDZND1="19822@x" Pin0InfoVect0LinkObjId="SW-94046_0" Pin0InfoVect1LinkObjId="SW-94045_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2516390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4452,-586 4452,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2517c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4441,-562 4452,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="19823@1" ObjectIDZND0="g_2516390@0" ObjectIDZND1="19822@x" Pin0InfoVect0LinkObjId="g_2516390_0" Pin0InfoVect1LinkObjId="SW-94045_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94046_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4441,-562 4452,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2517e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4452,-562 4464,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_2516390@0" ObjectIDND1="19823@x" ObjectIDZND0="19822@1" Pin0InfoVect0LinkObjId="SW-94045_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2516390_0" Pin1InfoVect1LinkObjId="SW-94046_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4452,-562 4464,-562 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="29321" cx="4632" cy="-538" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29321" cx="4632" cy="-792" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29321" cx="4632" cy="-749" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29321" cx="4632" cy="-757" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29321" cx="4632" cy="-631" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19826" cx="5060" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19826" cx="4955" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19826" cx="4852" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19826" cx="4747" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19826" cx="4638" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19827" cx="4398" cy="-348" fill="rgb(154,205,50)" r="4" stroke="rgb(154,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19827" cx="4289" cy="-348" fill="rgb(154,205,50)" r="4" stroke="rgb(154,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19827" cx="4095" cy="-348" fill="rgb(154,205,50)" r="4" stroke="rgb(154,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19827" cx="3858" cy="-348" fill="rgb(154,205,50)" r="4" stroke="rgb(154,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19827" cx="3617" cy="-348" fill="rgb(154,205,50)" r="4" stroke="rgb(154,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19827" cx="3704" cy="-348" fill="rgb(154,205,50)" r="4" stroke="rgb(154,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19827" cx="4116" cy="-349" fill="rgb(154,205,50)" r="4" stroke="rgb(154,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19825" cx="4632" cy="-1209" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19825" cx="4632" cy="-1097" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19825" cx="4632" cy="-984" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19825" cx="4632" cy="-900" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19825" cx="4632" cy="-945" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19827" cx="3919" cy="-348" fill="rgb(154,205,50)" r="4" stroke="rgb(154,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29321" cx="4633" cy="-562" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19826" cx="4572" cy="-348" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-93921" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3416.500000 -1084.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19754" ObjectName="DYN-WD_DXS"/>
     <cge:Meas_Ref ObjectId="93921"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26c1200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26c1200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26c1200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26c1200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26c1200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26c1200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26c1200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3162.000000 -1028.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c58a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c58a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c58a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c58a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c58a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c58a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c58a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c58a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c58a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c58a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c58a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c58a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c58a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c58a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c58a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c58a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22c58a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -589.000000) translate(0,353)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_26c9300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3263.000000 -1167.500000) translate(0,16)">大响水电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26c0fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5078.000000 -346.000000) translate(0,15)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_233e040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3411.500000 -1166.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264f580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4959.000000 -564.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2654200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4750.000000 -856.000000) translate(0,12)">分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26c1b90" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4966.142857 -743.800000) translate(0,15)">近大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26c1d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3889.000000 -757.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26c1d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3889.000000 -757.000000) translate(0,33)">SSZ20-16000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26c1d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3889.000000 -757.000000) translate(0,51)">38.5kV/10.5kV/6kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26c1d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3889.000000 -757.000000) translate(0,69)">YN,y0,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26c1d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3889.000000 -757.000000) translate(0,87)">U1-2:5.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26c1d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3889.000000 -757.000000) translate(0,105)">U1-3:10.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26c1d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3889.000000 -757.000000) translate(0,123)">U2-3:4.0%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26baec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4021.000000 -588.000000) translate(0,12)">1200</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2667330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4961.000000 -787.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_269c830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4963.000000 -1022.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25d30c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4969.000000 -1135.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25b6380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4965.000000 -1247.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2615390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5009.000000 -1204.000000) translate(0,12)">田大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264a230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5011.000000 -1097.000000) translate(0,12)">大白线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264a790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5009.000000 -980.000000) translate(0,12)">大插线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_264acf0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4966.142857 -535.800000) translate(0,15)">大猫高线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_26655e0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4944.142857 -653.800000) translate(0,15)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26286a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4644.000000 -65.000000) translate(0,12)">0116</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25c5f00" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4249.142857 -34.800000) translate(0,15)">6kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25cacf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4097.000000 -319.000000) translate(0,12)">6131</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25cb1e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4106.000000 -229.000000) translate(0,12)">613</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2553e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4177.000000 -152.000000) translate(0,12)">6132</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2555020" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4185.142857 -81.800000) translate(0,15)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_255ae20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3860.000000 -319.000000) translate(0,12)">6121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_255b450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3869.000000 -229.000000) translate(0,12)">612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_258af50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3940.000000 -152.000000) translate(0,12)">6122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_258b960" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3948.142857 -81.800000) translate(0,15)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25306a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3619.000000 -319.000000) translate(0,12)">6111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2530b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3628.000000 -229.000000) translate(0,12)">611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_253cc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3699.000000 -152.000000) translate(0,12)">6112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_253d630" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3707.142857 -81.800000) translate(0,15)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2540ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3497.000000 -27.000000) translate(0,10)">1号发电机参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2540ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3497.000000 -27.000000) translate(0,22)">TWS-K148/98-4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2540ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3497.000000 -27.000000) translate(0,34)">4000kW  6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_2540ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3497.000000 -27.000000) translate(0,46)">COSΦ=0.8  458A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2708cf0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3582.142857 31.200000) translate(0,15)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2709970" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3817.142857 31.200000) translate(0,15)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2709dd0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4056.142857 31.200000) translate(0,15)">3号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_270a010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3737.000000 -31.000000) translate(0,10)">2号发电机参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_270a010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3737.000000 -31.000000) translate(0,22)">TWS-K148/98-4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_270a010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3737.000000 -31.000000) translate(0,34)">4000kW  6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_270a010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3737.000000 -31.000000) translate(0,46)">COSΦ=0.8  458A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_270a500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3976.000000 -29.000000) translate(0,10)">3号发电机参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_270a500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3976.000000 -29.000000) translate(0,22)">TWS-K148/98-4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_270a500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3976.000000 -29.000000) translate(0,34)">4000kW  6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_270a500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3976.000000 -29.000000) translate(0,46)">COSΦ=0.8  458A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_271d210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3668.000000 -531.000000) translate(0,15)">6kV电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24c8670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3876.000000 -577.000000) translate(0,15)">6kV母线电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24c8670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3876.000000 -577.000000) translate(0,33)">压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24cb8e0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4430.142857 -701.800000) translate(0,15)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24cb8e0" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4430.142857 -701.800000) translate(0,33)"> 电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24d3100" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4430.142857 -889.800000) translate(0,15)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24d3100" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 4430.142857 -889.800000) translate(0,33)"> 电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24da910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4748.000000 -1234.000000) translate(0,12)">316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24daf40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4805.000000 -1235.000000) translate(0,12)">3166</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24db180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4694.000000 -1235.000000) translate(0,12)">3161</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24db3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4799.000000 -1175.000000) translate(0,12)">31667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24db800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4749.000000 -1122.000000) translate(0,12)">315</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24dbc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4695.000000 -1123.000000) translate(0,12)">3151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24dbec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4794.000000 -1123.000000) translate(0,12)">3156</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24dc100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4800.000000 -1062.000000) translate(0,12)">31567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24dc340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4749.000000 -1008.000000) translate(0,12)">314</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24dc680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4794.000000 -1010.000000) translate(0,12)">3146</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24dcae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4695.000000 -1010.000000) translate(0,12)">3141</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24dcd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4797.000000 -949.000000) translate(0,12)">31467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24dcf60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4560.000000 -971.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24dd480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4638.000000 -1279.000000) translate(0,12)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24de010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4691.000000 -926.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24de220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4691.000000 -818.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24de460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4646.000000 -464.000000) translate(0,12)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24de9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4746.000000 -773.000000) translate(0,12)">313</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24dec00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4692.000000 -775.000000) translate(0,12)">3132</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24dee40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4792.000000 -775.000000) translate(0,12)">3136</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24df080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4795.000000 -715.000000) translate(0,12)">31367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24df2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4692.000000 -657.000000) translate(0,12)">3171</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24df500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4745.000000 -562.000000) translate(0,12)">311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24df740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4790.000000 -564.000000) translate(0,12)">3116</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24df980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4690.000000 -564.000000) translate(0,12)">3112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24dfbc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4794.000000 -503.000000) translate(0,12)">31167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24dfe00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4466.000000 -587.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e0040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4408.000000 -588.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e0280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4517.000000 -588.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e04c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4126.000000 -466.000000) translate(0,12)">601</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e0700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4125.000000 -405.000000) translate(0,12)">6011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e0940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4180.000000 -642.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24e1390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3489.000000 -340.000000) translate(0,15)">6kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e1850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3711.000000 -391.000000) translate(0,12)">6151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e1a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3652.000000 -441.000000) translate(0,12)">61517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e1cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3926.000000 -389.000000) translate(0,12)">6901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e1f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3866.000000 -441.000000) translate(0,12)">69017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e2150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3565.000000 -299.000000) translate(0,12)">61117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e2390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3805.000000 -299.000000) translate(0,12)">61217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e25d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4042.000000 -299.000000) translate(0,12)">61317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e2810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4296.000000 -319.000000) translate(0,12)">6141</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e2a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4299.000000 -292.000000) translate(0,12)">61417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e2c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4407.000000 -259.000000) translate(0,12)">602</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e2ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4405.000000 -199.000000) translate(0,12)">6026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e3110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4422.000000 -304.000000) translate(0,12)">60217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e3350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4405.000000 -319.000000) translate(0,12)">6021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e3590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4647.000000 -259.000000) translate(0,12)">011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e37d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4645.000000 -199.000000) translate(0,12)">0112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e3a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4645.000000 -322.000000) translate(0,12)">0111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e3c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4655.000000 -307.000000) translate(0,12)">01117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e3e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4756.000000 -259.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e40d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4754.000000 -64.000000) translate(0,12)">0126</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e4310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4754.000000 -199.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e4550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4754.000000 -322.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e4790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4763.000000 -307.000000) translate(0,12)">01217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e49d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4861.000000 -259.000000) translate(0,12)">013</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e4c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4859.000000 -64.000000) translate(0,12)">0136</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e4e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4859.000000 -199.000000) translate(0,12)">0132</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e5090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4868.000000 -307.000000) translate(0,12)">01317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e52d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4859.000000 -322.000000) translate(0,12)">0131</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e5510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4964.000000 -259.000000) translate(0,12)">014</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e5750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4962.000000 -64.000000) translate(0,12)">0146</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e5990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4962.000000 -199.000000) translate(0,12)">0142</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e5bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4972.000000 -307.000000) translate(0,12)">01417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e5e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4962.000000 -322.000000) translate(0,12)">0141</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e6050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5067.000000 -314.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e6290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5076.000000 -290.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e64d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4559.000000 -783.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f1f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4805.000000 -856.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24f6290" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3283.142857 -250.800000) translate(0,15)">4794</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24faff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4497.000000 -1291.000000) translate(0,12)">Ub（kV）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24fbcb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4497.000000 -1275.000000) translate(0,12)">Uc（kV）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24fc4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4503.000000 -486.000000) translate(0,12)">Uc（kV）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24fc720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4503.000000 -502.000000) translate(0,12)">Ub（kV）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24fce40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3460.000000 -375.000000) translate(0,12)">Uc（kV）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24fd050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3460.000000 -391.000000) translate(0,12)">Ub（kV）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24fd840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5028.000000 -379.000000) translate(0,12)">Uc（kV）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24fda40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5028.000000 -395.000000) translate(0,12)">Ub（kV）：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2502060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4606.000000 29.000000) translate(0,15)">大沙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25029d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4713.000000 29.000000) translate(0,15)">乐茂河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2503560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4924.000000 29.000000) translate(0,15)">小电站线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2503bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4810.000000 29.000000) translate(0,15)">二级站线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2504440" transform="matrix(0.987013 -0.000000 -0.000000 0.938462 3280.142857 -221.800000) translate(0,15)">8833075</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2504dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4375.000000 -97.000000) translate(0,15)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250a180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4465.000000 -447.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250a7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4388.000000 -447.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_250a9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4532.000000 -445.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25134b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4482.000000 -383.000000) translate(0,12)">00117</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3116" y="-1198"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="3395" y="-1180"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(60,120,255)" stroke-width="0.416609" width="14" x="3874" y="-480"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(60,120,255)" stroke-width="0.416609" width="14" x="3697" y="-459"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_24c6d10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4969.000000 -566.500000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2665f40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4968.000000 -790.500000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_269b660">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4970.000000 -1025.500000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25d1ef0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4973.000000 -1138.127204)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25b5540">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4970.000000 -1249.555416)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_254d300">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4117.000000 -59.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2583e60">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3880.000000 -59.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2535a30">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3639.000000 -59.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_270faa0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3862.000000 -486.000000)" xlink:href="#voltageTransformer:shape79"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-WD_DXS.014Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4946.000000 21.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41641" ObjectName="EC-WD_DXS.014Ld"/>
    <cge:TPSR_Ref TObjectID="41641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_DXS.013Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4843.000000 21.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41640" ObjectName="EC-WD_DXS.013Ld"/>
    <cge:TPSR_Ref TObjectID="41640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_DXS.012Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4738.000000 21.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41638" ObjectName="EC-WD_DXS.012Ld"/>
    <cge:TPSR_Ref TObjectID="41638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_DXS.011Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4629.000000 21.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41639" ObjectName="EC-WD_DXS.011Ld"/>
    <cge:TPSR_Ref TObjectID="41639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4389.000000 -109.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_233bd10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5022.000000 -188.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2310a00">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 5079.000000 -241.400000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26a6a40">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4861.500000 -520.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26b7370">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4956.500000 -565.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26c1ec0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4061.000000 -599.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26c3f80">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4003.000000 -601.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_265a410">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4863.500000 -731.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_265afc0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4955.500000 -789.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_269a310">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4865.500000 -966.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_269aec0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4957.500000 -1024.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_257a2b0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4865.500000 -1079.127204)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_257ae60">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4960.500000 -1137.127204)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26b01e0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4865.500000 -1191.555416)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25b51a0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4957.500000 -1248.555416)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2615b90">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.477273 1.564103 -0.000000 4387.935897 -695.500000)" xlink:href="#lightningRod:shape190"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2616480">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4464.500000 -740.444584)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2616ab0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4507.000000 -805.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2568ac0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4962.500000 -616.500000)" xlink:href="#lightningRod:shape186"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2662c50">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4815.500000 -622.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25ec670">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5007.000000 -135.000000)" xlink:href="#lightningRod:shape190"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_267e3a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4917.000000 -166.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25b0cf0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4814.000000 -166.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2603ee0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4709.000000 -166.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25aadd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4600.000000 -166.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2529750">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4360.000000 -166.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_252c2d0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4274.000000 -36.000000)" xlink:href="#lightningRod:shape186"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25c1250">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4280.000000 -196.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25ce650">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4021.000000 -170.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_254eeb0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4123.000000 -92.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_254f8d0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4163.000000 -78.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2581de0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3784.000000 -170.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2585af0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3886.000000 -92.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25865d0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3926.000000 -78.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25335d0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3543.000000 -170.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25377c0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3645.000000 -92.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25382a0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3685.000000 -78.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2712630">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3943.000000 -474.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24cc350">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.477273 1.564103 -0.000000 4387.935897 -883.500000)" xlink:href="#lightningRod:shape190"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24cec60">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4464.500000 -928.444584)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24cf740">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4507.000000 -993.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_250ce70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4374.000000 -389.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25139f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4297.000000 -434.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2514630">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4043.000000 -472.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2516390">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4445.000000 -582.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-93948" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3599.000000 57.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93948" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19770"/>
     <cge:Term_Ref ObjectID="27554"/>
    <cge:TPSR_Ref TObjectID="19770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-93949" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3599.000000 57.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93949" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19770"/>
     <cge:Term_Ref ObjectID="27554"/>
    <cge:TPSR_Ref TObjectID="19770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-93947" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3599.000000 57.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93947" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19770"/>
     <cge:Term_Ref ObjectID="27554"/>
    <cge:TPSR_Ref TObjectID="19770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-93946" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3599.000000 57.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93946" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19770"/>
     <cge:Term_Ref ObjectID="27554"/>
    <cge:TPSR_Ref TObjectID="19770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-93953" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3834.000000 57.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93953" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19773"/>
     <cge:Term_Ref ObjectID="27560"/>
    <cge:TPSR_Ref TObjectID="19773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-93954" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3834.000000 57.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93954" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19773"/>
     <cge:Term_Ref ObjectID="27560"/>
    <cge:TPSR_Ref TObjectID="19773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-93952" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3834.000000 57.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93952" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19773"/>
     <cge:Term_Ref ObjectID="27560"/>
    <cge:TPSR_Ref TObjectID="19773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-93951" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3834.000000 57.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93951" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19773"/>
     <cge:Term_Ref ObjectID="27560"/>
    <cge:TPSR_Ref TObjectID="19773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-93958" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4071.000000 57.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93958" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19776"/>
     <cge:Term_Ref ObjectID="27566"/>
    <cge:TPSR_Ref TObjectID="19776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-93959" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4071.000000 57.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93959" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19776"/>
     <cge:Term_Ref ObjectID="27566"/>
    <cge:TPSR_Ref TObjectID="19776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-93957" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4071.000000 57.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93957" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19776"/>
     <cge:Term_Ref ObjectID="27566"/>
    <cge:TPSR_Ref TObjectID="19776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-93956" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4071.000000 57.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93956" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19776"/>
     <cge:Term_Ref ObjectID="27566"/>
    <cge:TPSR_Ref TObjectID="19776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-200472" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -1231.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200472" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19818"/>
     <cge:Term_Ref ObjectID="27574"/>
    <cge:TPSR_Ref TObjectID="19818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-200473" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -1231.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200473" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19818"/>
     <cge:Term_Ref ObjectID="27574"/>
    <cge:TPSR_Ref TObjectID="19818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-200470" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -1231.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200470" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19818"/>
     <cge:Term_Ref ObjectID="27574"/>
    <cge:TPSR_Ref TObjectID="19818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-200465" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -1115.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200465" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29331"/>
     <cge:Term_Ref ObjectID="27521"/>
    <cge:TPSR_Ref TObjectID="29331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-200466" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -1115.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200466" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29331"/>
     <cge:Term_Ref ObjectID="27521"/>
    <cge:TPSR_Ref TObjectID="29331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-200463" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -1115.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200463" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29331"/>
     <cge:Term_Ref ObjectID="27521"/>
    <cge:TPSR_Ref TObjectID="29331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-200458" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -1005.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200458" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29332"/>
     <cge:Term_Ref ObjectID="27535"/>
    <cge:TPSR_Ref TObjectID="29332"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-200459" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -1005.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200459" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29332"/>
     <cge:Term_Ref ObjectID="27535"/>
    <cge:TPSR_Ref TObjectID="29332"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-200456" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -1005.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200456" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29332"/>
     <cge:Term_Ref ObjectID="27535"/>
    <cge:TPSR_Ref TObjectID="29332"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-200444" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -767.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200444" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29334"/>
     <cge:Term_Ref ObjectID="41328"/>
    <cge:TPSR_Ref TObjectID="29334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-200445" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -767.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200445" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29334"/>
     <cge:Term_Ref ObjectID="41328"/>
    <cge:TPSR_Ref TObjectID="29334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-200442" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -767.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200442" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29334"/>
     <cge:Term_Ref ObjectID="41328"/>
    <cge:TPSR_Ref TObjectID="29334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-200437" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -558.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200437" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29333"/>
     <cge:Term_Ref ObjectID="41326"/>
    <cge:TPSR_Ref TObjectID="29333"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-200438" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -558.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200438" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29333"/>
     <cge:Term_Ref ObjectID="41326"/>
    <cge:TPSR_Ref TObjectID="29333"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-200435" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5167.000000 -558.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200435" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29333"/>
     <cge:Term_Ref ObjectID="41326"/>
    <cge:TPSR_Ref TObjectID="29333"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-200402" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4620.500000 64.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200402" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29395"/>
     <cge:Term_Ref ObjectID="41854"/>
    <cge:TPSR_Ref TObjectID="29395"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-200403" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4620.500000 64.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200403" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29395"/>
     <cge:Term_Ref ObjectID="41854"/>
    <cge:TPSR_Ref TObjectID="29395"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-200400" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4620.500000 64.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200400" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29395"/>
     <cge:Term_Ref ObjectID="41854"/>
    <cge:TPSR_Ref TObjectID="29395"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-200409" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4728.000000 64.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200409" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29396"/>
     <cge:Term_Ref ObjectID="41856"/>
    <cge:TPSR_Ref TObjectID="29396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-200410" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4728.000000 64.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200410" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29396"/>
     <cge:Term_Ref ObjectID="41856"/>
    <cge:TPSR_Ref TObjectID="29396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-200407" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4728.000000 64.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200407" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29396"/>
     <cge:Term_Ref ObjectID="41856"/>
    <cge:TPSR_Ref TObjectID="29396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-200430" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4835.500000 64.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200430" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29397"/>
     <cge:Term_Ref ObjectID="41858"/>
    <cge:TPSR_Ref TObjectID="29397"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-200431" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4835.500000 64.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200431" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29397"/>
     <cge:Term_Ref ObjectID="41858"/>
    <cge:TPSR_Ref TObjectID="29397"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-200428" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4835.500000 64.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200428" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29397"/>
     <cge:Term_Ref ObjectID="41858"/>
    <cge:TPSR_Ref TObjectID="29397"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-200423" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4943.000000 64.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200423" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29398"/>
     <cge:Term_Ref ObjectID="41860"/>
    <cge:TPSR_Ref TObjectID="29398"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-200424" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4943.000000 64.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200424" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29398"/>
     <cge:Term_Ref ObjectID="41860"/>
    <cge:TPSR_Ref TObjectID="29398"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-200421" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4943.000000 64.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200421" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29398"/>
     <cge:Term_Ref ObjectID="41860"/>
    <cge:TPSR_Ref TObjectID="29398"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-200451" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4940.000000 -879.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200451" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29335"/>
     <cge:Term_Ref ObjectID="41762"/>
    <cge:TPSR_Ref TObjectID="29335"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-200452" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4940.000000 -879.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200452" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29335"/>
     <cge:Term_Ref ObjectID="41762"/>
    <cge:TPSR_Ref TObjectID="29335"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-200449" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4940.000000 -879.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200449" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29335"/>
     <cge:Term_Ref ObjectID="41762"/>
    <cge:TPSR_Ref TObjectID="29335"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="1" id="ME-94055" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4565.000000 -1290.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94055" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19825"/>
     <cge:Term_Ref ObjectID="27640"/>
    <cge:TPSR_Ref TObjectID="19825"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="1" id="ME-94056" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4565.000000 -1290.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94056" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19825"/>
     <cge:Term_Ref ObjectID="27640"/>
    <cge:TPSR_Ref TObjectID="19825"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="1" id="ME-94062" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4573.000000 -503.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94062" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29321"/>
     <cge:Term_Ref ObjectID="16271"/>
    <cge:TPSR_Ref TObjectID="29321"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="1" id="ME-94065" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4573.000000 -503.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94065" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29321"/>
     <cge:Term_Ref ObjectID="16271"/>
    <cge:TPSR_Ref TObjectID="29321"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="1" id="ME-94063" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3530.000000 -390.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94063" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19827"/>
     <cge:Term_Ref ObjectID="27642"/>
    <cge:TPSR_Ref TObjectID="19827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="1" id="ME-94064" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3530.000000 -390.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94064" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19827"/>
     <cge:Term_Ref ObjectID="27642"/>
    <cge:TPSR_Ref TObjectID="19827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="1" id="ME-94059" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5103.000000 -393.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94059" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19826"/>
     <cge:Term_Ref ObjectID="27641"/>
    <cge:TPSR_Ref TObjectID="19826"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="1" id="ME-94060" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5103.000000 -393.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94060" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19826"/>
     <cge:Term_Ref ObjectID="27641"/>
    <cge:TPSR_Ref TObjectID="19826"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_县调直调.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3235" y="-1178"/></g>
   <g href="cx_索引_接线图_县调直调.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3186" y="-1195"/></g>
   <g href="AVC田心站.svg" style="fill-opacity:0"><rect height="43" qtmmishow="hidden" width="73" x="3395" y="-1180"/></g>
   <g href="35kV大响水电站10kV大沙线011断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4647" y="-259"/></g>
   <g href="35kV大响水电站10kV乐茂河线012断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4756" y="-259"/></g>
   <g href="35kV大响水电站10kV二级站线013断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4861" y="-259"/></g>
   <g href="35kV大响水电站10kV小电站线014断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4964" y="-259"/></g>
   <g href="35kV大响水电站35kV大猫高线311间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4745" y="-562"/></g>
   <g href="35kV大响水电站35kV近大线313间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4746" y="-773"/></g>
   <g href="35kV大响水电站35kV母线分段312间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4805" y="-856"/></g>
   <g href="35kV大响水电站35kV大插线314间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4749" y="-1008"/></g>
   <g href="35kV大响水电站35kV大白线315间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4749" y="-1122"/></g>
   <g href="35kV大响水电站35kV田大线316间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4748" y="-1234"/></g>
   <g href="35kV大响水电站1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="4180" y="-642"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e68f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5103.000000 1230.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e7a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5092.000000 1215.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e8720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5117.000000 1200.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e9120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5103.000000 1115.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e93e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5092.000000 1100.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e9620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5117.000000 1085.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e9a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5103.000000 1006.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e9d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5092.000000 991.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e9f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5117.000000 976.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24ea360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5103.000000 767.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24ea620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5092.000000 752.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24ea860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5117.000000 737.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24eac80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5103.000000 562.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24eaf40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5092.000000 547.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24eb180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5117.000000 532.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24ed160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4524.000000 -64.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24ed420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4513.000000 -79.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24ed660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4538.000000 -94.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24ef650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4879.000000 877.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24ef8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4868.000000 862.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24efb20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4893.000000 847.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f4780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3777.000000 -58.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f4db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3766.000000 -73.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f4ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3791.000000 -88.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f5230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3778.000000 -102.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f5920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4016.000000 -58.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f5bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4005.000000 -73.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f5e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4030.000000 -88.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f6050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4017.000000 -102.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f6b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3539.000000 -57.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f6e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3528.000000 -72.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f7050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3553.000000 -87.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f7290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3540.000000 -101.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f75c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4173.000000 486.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f7830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4162.000000 471.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f7a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4187.000000 456.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f7cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4174.000000 442.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24fa160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4359.000000 -52.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24fa3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4348.000000 -67.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24fa610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4373.000000 -82.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24fa850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4360.000000 -96.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_TX" endPointId="0" endStationName="WD_DXS" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_tianda" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5041,-1209 5068,-1209 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38111" ObjectName="AC-35kV.LN_tianda"/>
    <cge:TPSR_Ref TObjectID="38111_SS-153"/></metadata>
   <polyline fill="none" opacity="0" points="5041,-1209 5068,-1209 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="WD_DXS" endPointId="0" endStationName="WD_BL" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_dabai" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5040,-1097 5067,-1097 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38119" ObjectName="AC-35kV.LN_dabai"/>
    <cge:TPSR_Ref TObjectID="38119_SS-153"/></metadata>
   <polyline fill="none" opacity="0" points="5040,-1097 5067,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="WD_DXS" endPointId="0" endStationName="WD_CD" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_dacha" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5037,-984 5064,-984 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38120" ObjectName="AC-35kV.LN_dacha"/>
    <cge:TPSR_Ref TObjectID="38120_SS-153"/></metadata>
   <polyline fill="none" opacity="0" points="5037,-984 5064,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="WD_DXS" endPointId="0" endStationName="WD_JC" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_jinda" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5043,-749 5070,-749 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38121" ObjectName="AC-35kV.LN_jinda"/>
    <cge:TPSR_Ref TObjectID="38121_SS-153"/></metadata>
   <polyline fill="none" opacity="0" points="5043,-749 5070,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="WD_DXS" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_damaogaoTdxs" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5048,-538 5075,-538 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38122" ObjectName="AC-35kV.LN_damaogaoTdxs"/>
    <cge:TPSR_Ref TObjectID="38122_SS-153"/></metadata>
   <polyline fill="none" opacity="0" points="5048,-538 5075,-538 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3215.000000 -1116.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3292.538462 -987.966362) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3292.538462 -945.966362) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-94051" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4233.000000 -486.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94051" ObjectName="WD_DXS:WD_DXS_601BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-94053" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4233.000000 -471.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94053" ObjectName="WD_DXS:WD_DXS_601BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-94049" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4233.000000 -456.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94049" ObjectName="WD_DXS:WD_DXS_601BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-94048" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4233.000000 -441.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94048" ObjectName="WD_DXS:WD_DXS_601BK_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-94061" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4419.000000 52.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94061" ObjectName="WD_DXS:WD_DXS_602BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-200396" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4419.000000 67.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200396" ObjectName="WD_DXS:WD_DXS_602BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-94057" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4419.000000 82.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94057" ObjectName="WD_DXS:WD_DXS_602BK_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-94052" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4419.000000 97.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94052" ObjectName="WD_DXS:WD_DXS_602BK_Ua"/>
    </metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-193284">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4683.000000 -533.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29376" ObjectName="SW-WD_DXS.WD_DXS_3112SW"/>
     <cge:Meas_Ref ObjectId="193284"/>
    <cge:TPSR_Ref TObjectID="29376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193283">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4783.000000 -533.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29377" ObjectName="SW-WD_DXS.WD_DXS_3116SW"/>
     <cge:Meas_Ref ObjectId="193283"/>
    <cge:TPSR_Ref TObjectID="29377"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193289">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4684.000000 -787.105793)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29383" ObjectName="SW-WD_DXS.WD_DXS_3122SW"/>
     <cge:Meas_Ref ObjectId="193289"/>
    <cge:TPSR_Ref TObjectID="29383"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193288">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4684.000000 -895.105793)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29382" ObjectName="SW-WD_DXS.WD_DXS_3121SW"/>
     <cge:Meas_Ref ObjectId="193288"/>
    <cge:TPSR_Ref TObjectID="29382"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94046">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4399.578000 -556.874055)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19823" ObjectName="SW-WD_DXS.WD_DXS_3016SW"/>
     <cge:Meas_Ref ObjectId="94046"/>
    <cge:TPSR_Ref TObjectID="19823"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4030.000000 -616.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193282">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4838.000000 -475.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29378" ObjectName="SW-WD_DXS.WD_DXS_31167SW"/>
     <cge:Meas_Ref ObjectId="193282"/>
    <cge:TPSR_Ref TObjectID="29378"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193287">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4685.000000 -744.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29381" ObjectName="SW-WD_DXS.WD_DXS_3132SW"/>
     <cge:Meas_Ref ObjectId="193287"/>
    <cge:TPSR_Ref TObjectID="29381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193286">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4785.000000 -744.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29380" ObjectName="SW-WD_DXS.WD_DXS_3136SW"/>
     <cge:Meas_Ref ObjectId="193286"/>
    <cge:TPSR_Ref TObjectID="29380"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193285">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4840.000000 -686.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29379" ObjectName="SW-WD_DXS.WD_DXS_31367SW"/>
     <cge:Meas_Ref ObjectId="193285"/>
    <cge:TPSR_Ref TObjectID="29379"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193281">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4687.000000 -979.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29375" ObjectName="SW-WD_DXS.WD_DXS_3141SW"/>
     <cge:Meas_Ref ObjectId="193281"/>
    <cge:TPSR_Ref TObjectID="29375"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193280">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4787.000000 -979.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29374" ObjectName="SW-WD_DXS.WD_DXS_3146SW"/>
     <cge:Meas_Ref ObjectId="193280"/>
    <cge:TPSR_Ref TObjectID="29374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193279">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4842.000000 -921.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29373" ObjectName="SW-WD_DXS.WD_DXS_31467SW"/>
     <cge:Meas_Ref ObjectId="193279"/>
    <cge:TPSR_Ref TObjectID="29373"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193276">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4687.000000 -1091.627204)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29370" ObjectName="SW-WD_DXS.WD_DXS_3151SW"/>
     <cge:Meas_Ref ObjectId="193276"/>
    <cge:TPSR_Ref TObjectID="29370"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193278">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4787.000000 -1091.627204)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29371" ObjectName="SW-WD_DXS.WD_DXS_3156SW"/>
     <cge:Meas_Ref ObjectId="193278"/>
    <cge:TPSR_Ref TObjectID="29371"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193277">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4842.000000 -1033.627204)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29372" ObjectName="SW-WD_DXS.WD_DXS_31567SW"/>
     <cge:Meas_Ref ObjectId="193277"/>
    <cge:TPSR_Ref TObjectID="29372"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94044">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4687.000000 -1204.055416)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19820" ObjectName="SW-WD_DXS.WD_DXS_3161SW"/>
     <cge:Meas_Ref ObjectId="94044"/>
    <cge:TPSR_Ref TObjectID="19820"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94043">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4787.000000 -1204.055416)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19819" ObjectName="SW-WD_DXS.WD_DXS_3166SW"/>
     <cge:Meas_Ref ObjectId="94043"/>
    <cge:TPSR_Ref TObjectID="19819"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-200315">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4842.000000 -1146.055416)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29328" ObjectName="SW-WD_DXS.WD_DXS_31667SW"/>
     <cge:Meas_Ref ObjectId="200315"/>
    <cge:TPSR_Ref TObjectID="29328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193275">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4553.000000 -752.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29369" ObjectName="SW-WD_DXS.WD_DXS_3902SW"/>
     <cge:Meas_Ref ObjectId="193275"/>
    <cge:TPSR_Ref TObjectID="29369"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-200313">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4106.578000 -376.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29330" ObjectName="SW-WD_DXS.WD_DXS_6011SW"/>
     <cge:Meas_Ref ObjectId="200313"/>
    <cge:TPSR_Ref TObjectID="29330"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193290">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4685.000000 -626.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29384" ObjectName="SW-WD_DXS.WD_DXS_3171SW"/>
     <cge:Meas_Ref ObjectId="193290"/>
    <cge:TPSR_Ref TObjectID="29384"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193273">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5051.000000 -284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29367" ObjectName="SW-WD_DXS.WD_DXS_0901SW"/>
     <cge:Meas_Ref ObjectId="193273"/>
    <cge:TPSR_Ref TObjectID="29367"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193304">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5073.000000 -259.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29401" ObjectName="SW-WD_DXS.WD_DXS_09017SW"/>
     <cge:Meas_Ref ObjectId="193304"/>
    <cge:TPSR_Ref TObjectID="29401"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193269">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4946.000000 -292.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29364" ObjectName="SW-WD_DXS.WD_DXS_0141SW"/>
     <cge:Meas_Ref ObjectId="193269"/>
    <cge:TPSR_Ref TObjectID="29364"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193270">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4946.000000 -169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29365" ObjectName="SW-WD_DXS.WD_DXS_0142SW"/>
     <cge:Meas_Ref ObjectId="193270"/>
    <cge:TPSR_Ref TObjectID="29365"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193268">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4968.000000 -276.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29363" ObjectName="SW-WD_DXS.WD_DXS_01417SW"/>
     <cge:Meas_Ref ObjectId="193268"/>
    <cge:TPSR_Ref TObjectID="29363"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-248340">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4946.000000 -34.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29366" ObjectName="SW-WD_DXS.WD_DXS_0146SW"/>
     <cge:Meas_Ref ObjectId="248340"/>
    <cge:TPSR_Ref TObjectID="29366"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193265">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4843.000000 -292.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29356" ObjectName="SW-WD_DXS.WD_DXS_0131SW"/>
     <cge:Meas_Ref ObjectId="193265"/>
    <cge:TPSR_Ref TObjectID="29356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193266">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4843.000000 -169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29357" ObjectName="SW-WD_DXS.WD_DXS_0132SW"/>
     <cge:Meas_Ref ObjectId="193266"/>
    <cge:TPSR_Ref TObjectID="29357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193264">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4865.000000 -276.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29355" ObjectName="SW-WD_DXS.WD_DXS_01317SW"/>
     <cge:Meas_Ref ObjectId="193264"/>
    <cge:TPSR_Ref TObjectID="29355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4843.000000 -34.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29358" ObjectName="SW-WD_DXS.WD_DXS_0136SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="29358"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193271">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4738.000000 -292.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29360" ObjectName="SW-WD_DXS.WD_DXS_0121SW"/>
     <cge:Meas_Ref ObjectId="193271"/>
    <cge:TPSR_Ref TObjectID="29360"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193272">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4738.000000 -169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29361" ObjectName="SW-WD_DXS.WD_DXS_0122SW"/>
     <cge:Meas_Ref ObjectId="193272"/>
    <cge:TPSR_Ref TObjectID="29361"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193267">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4760.000000 -276.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29359" ObjectName="SW-WD_DXS.WD_DXS_01217SW"/>
     <cge:Meas_Ref ObjectId="193267"/>
    <cge:TPSR_Ref TObjectID="29359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4738.000000 -34.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29362" ObjectName="SW-WD_DXS.WD_DXS_0126SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="29362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193262">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4629.000000 -292.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29353" ObjectName="SW-WD_DXS.WD_DXS_0111SW"/>
     <cge:Meas_Ref ObjectId="193262"/>
    <cge:TPSR_Ref TObjectID="29353"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193263">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4629.000000 -169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29354" ObjectName="SW-WD_DXS.WD_DXS_0112SW"/>
     <cge:Meas_Ref ObjectId="193263"/>
    <cge:TPSR_Ref TObjectID="29354"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193261">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4651.000000 -276.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29348" ObjectName="SW-WD_DXS.WD_DXS_01117SW"/>
     <cge:Meas_Ref ObjectId="193261"/>
    <cge:TPSR_Ref TObjectID="29348"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4629.000000 -34.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193305">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4389.000000 -289.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29391" ObjectName="SW-WD_DXS.WD_DXS_6021SW"/>
     <cge:Meas_Ref ObjectId="193305"/>
    <cge:TPSR_Ref TObjectID="29391"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193303">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4411.000000 -275.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29390" ObjectName="SW-WD_DXS.WD_DXS_60217SW"/>
     <cge:Meas_Ref ObjectId="193303"/>
    <cge:TPSR_Ref TObjectID="29390"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-200314">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4389.000000 -169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29389" ObjectName="SW-WD_DXS.WD_DXS_6026SW"/>
     <cge:Meas_Ref ObjectId="200314"/>
    <cge:TPSR_Ref TObjectID="29389"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193299">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4280.000000 -289.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29388" ObjectName="SW-WD_DXS.WD_DXS_6141SW"/>
     <cge:Meas_Ref ObjectId="193299"/>
    <cge:TPSR_Ref TObjectID="29388"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193294">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4295.000000 -261.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29399" ObjectName="SW-WD_DXS.WD_DXS_61417SW"/>
     <cge:Meas_Ref ObjectId="193294"/>
    <cge:TPSR_Ref TObjectID="29399"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-93968">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4086.000000 -289.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19777" ObjectName="SW-WD_DXS.WD_DXS_6131SW"/>
     <cge:Meas_Ref ObjectId="93968"/>
    <cge:TPSR_Ref TObjectID="19777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193293">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4039.000000 -268.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29320" ObjectName="SW-WD_DXS.WD_DXS_61317SW"/>
     <cge:Meas_Ref ObjectId="193293"/>
    <cge:TPSR_Ref TObjectID="29320"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-93969">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4163.000000 -120.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19778" ObjectName="SW-WD_DXS.WD_DXS_6132SW"/>
     <cge:Meas_Ref ObjectId="93969"/>
    <cge:TPSR_Ref TObjectID="19778"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-93965">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3849.000000 -289.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19774" ObjectName="SW-WD_DXS.WD_DXS_6121SW"/>
     <cge:Meas_Ref ObjectId="93965"/>
    <cge:TPSR_Ref TObjectID="19774"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193292">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3802.000000 -268.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29319" ObjectName="SW-WD_DXS.WD_DXS_61217SW"/>
     <cge:Meas_Ref ObjectId="193292"/>
    <cge:TPSR_Ref TObjectID="29319"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-93966">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3926.000000 -120.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19775" ObjectName="SW-WD_DXS.WD_DXS_6122SW"/>
     <cge:Meas_Ref ObjectId="93966"/>
    <cge:TPSR_Ref TObjectID="19775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-93962">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3608.000000 -289.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19771" ObjectName="SW-WD_DXS.WD_DXS_6111SW"/>
     <cge:Meas_Ref ObjectId="93962"/>
    <cge:TPSR_Ref TObjectID="19771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193291">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3561.000000 -268.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29318" ObjectName="SW-WD_DXS.WD_DXS_61117SW"/>
     <cge:Meas_Ref ObjectId="193291"/>
    <cge:TPSR_Ref TObjectID="29318"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-93963">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3685.000000 -120.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19772" ObjectName="SW-WD_DXS.WD_DXS_6112SW"/>
     <cge:Meas_Ref ObjectId="93963"/>
    <cge:TPSR_Ref TObjectID="19772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193297">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3910.000000 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29386" ObjectName="SW-WD_DXS.WD_DXS_6901SW"/>
     <cge:Meas_Ref ObjectId="193297"/>
    <cge:TPSR_Ref TObjectID="29386"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193298">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3695.000000 -361.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29387" ObjectName="SW-WD_DXS.WD_DXS_6151SW"/>
     <cge:Meas_Ref ObjectId="193298"/>
    <cge:TPSR_Ref TObjectID="29387"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193296">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3863.000000 -410.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29385" ObjectName="SW-WD_DXS.WD_DXS_69017SW"/>
     <cge:Meas_Ref ObjectId="193296"/>
    <cge:TPSR_Ref TObjectID="29385"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193295">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3648.000000 -410.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29400" ObjectName="SW-WD_DXS.WD_DXS_61517SW"/>
     <cge:Meas_Ref ObjectId="193295"/>
    <cge:TPSR_Ref TObjectID="29400"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193274">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4553.000000 -940.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29368" ObjectName="SW-WD_DXS.WD_DXS_3901SW"/>
     <cge:Meas_Ref ObjectId="193274"/>
    <cge:TPSR_Ref TObjectID="29368"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94047">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4509.000000 -557.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19824" ObjectName="SW-WD_DXS.WD_DXS_3011SW"/>
     <cge:Meas_Ref ObjectId="94047"/>
    <cge:TPSR_Ref TObjectID="19824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193302">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4379.578000 -413.874055)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29392" ObjectName="SW-WD_DXS.WD_DXS_0016SW"/>
     <cge:Meas_Ref ObjectId="193302"/>
    <cge:TPSR_Ref TObjectID="29392"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193301">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4531.000000 -414.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29393" ObjectName="SW-WD_DXS.WD_DXS_0011SW"/>
     <cge:Meas_Ref ObjectId="193301"/>
    <cge:TPSR_Ref TObjectID="29393"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193300">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4468.000000 -384.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29394" ObjectName="SW-WD_DXS.WD_DXS_00117SW"/>
     <cge:Meas_Ref ObjectId="193300"/>
    <cge:TPSR_Ref TObjectID="29394"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3235" y="-1178"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3235" y="-1178"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3186" y="-1195"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3186" y="-1195"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="43" qtmmishow="hidden" width="73" x="3395" y="-1180"/>
    </a>
   <metadata/><rect fill="white" height="43" opacity="0" stroke="white" transform="" width="73" x="3395" y="-1180"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4647" y="-259"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4647" y="-259"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4756" y="-259"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4756" y="-259"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4861" y="-259"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4861" y="-259"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4964" y="-259"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4964" y="-259"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4745" y="-562"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4745" y="-562"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4746" y="-773"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4746" y="-773"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4805" y="-856"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4805" y="-856"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4749" y="-1008"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4749" y="-1008"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4749" y="-1122"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4749" y="-1122"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4748" y="-1234"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4748" y="-1234"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="4180" y="-642"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="4180" y="-642"/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_26bb0b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4033.000000 -593.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_266d530" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4841.000000 -447.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2667700" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4843.000000 -658.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_269cc00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4845.000000 -893.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25d3490" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4845.000000 -1005.627204)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25b6830" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4845.000000 -1118.055416)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25e6aa0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5119.000000 -258.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2624300" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5014.000000 -275.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2641ad0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4911.000000 -275.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2604eb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4806.000000 -275.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25ef120" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4697.000000 -275.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_262f100" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4457.000000 -274.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25c1d30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4341.000000 -260.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25561d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4017.000000 -267.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_258bc80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3780.000000 -267.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_253d950" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3539.000000 -267.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2715b70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3841.000000 -409.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_271bbf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3626.000000 -409.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="WD_DXS"/>
</svg>