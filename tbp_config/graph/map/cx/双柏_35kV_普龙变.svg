<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-163" aopId="3933702" id="thSvg" product="E8000V2" version="1.0" viewBox="-283 -1125 1873 1179">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="37" x2="37" y1="122" y2="130"/>
    <polyline arcFlag="1" points="37,122 35,122 33,121 32,121 30,120 29,119 27,118 26,116 25,114 25,113 24,111 24,109 24,107 25,105 25,104 26,102 27,101 29,99 30,98 32,97 33,97 35,96 37,96 39,96 41,97 42,97 44,98 45,99 47,101 48,102 49,104 49,105 50,107 50,109 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="50" x2="38" y1="109" y2="109"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.915724" x1="37" x2="37" y1="109" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="23" y2="14"/>
    <polyline arcFlag="1" points="13,23 12,23 12,23 11,23 10,23 10,24 9,24 8,25 8,25 8,26 7,27 7,27 7,28 7,29 7,30 7,30 8,31 8,32 8,32 9,33 10,33 10,34 11,34 12,34 12,34 13,34 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,34 12,34 12,34 11,34 10,34 10,35 9,35 8,36 8,36 8,37 7,38 7,38 7,39 7,40 7,41 7,41 8,42 8,43 8,43 9,44 10,44 10,45 11,45 12,45 12,45 13,45 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,45 12,45 12,45 11,45 10,45 10,46 9,46 8,47 8,47 8,48 7,49 7,49 7,50 7,51 7,52 7,52 8,53 8,54 8,54 9,55 10,55 10,56 11,56 12,56 12,56 13,56 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="65" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="65" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.393258" x1="21" x2="56" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="13" y2="13"/>
    <rect height="26" stroke-width="0.398039" width="12" x="31" y="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="20" x2="20" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="2" x2="2" y1="56" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="57" x2="57" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.504561" x1="37" x2="37" y1="16" y2="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="5" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="3" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="8" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="1" x2="12" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <rect height="10" stroke-width="0.416609" width="28" x="23" y="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="23" x2="49" y1="30" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <rect height="14" stroke-width="0.416609" width="26" x="19" y="-2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="12" x2="48" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="48" x2="18" y1="5" y2="34"/>
    <rect height="9" stroke-width="0.416609" width="29" x="21" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
   </symbol>
   <symbol id="switch2:shape26-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="12" x2="48" y1="5" y2="5"/>
    <rect height="14" stroke-width="0.416609" width="26" x="19" y="-2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape25_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
   </symbol>
   <symbol id="switch2:shape25_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape25_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="58" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape25_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="12,76 19,76 16,83 12,76 "/>
   </symbol>
   <symbol id="transformer2:shape96_0">
    <circle cx="35" cy="81" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="109" y2="140"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="137" y2="137"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="140" y2="135"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="140" y2="140"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="34,57 34,42 " stroke-width="4.70108"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="59" x2="34" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="62" x2="59" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="63" x2="55" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="53" x2="65" y1="26" y2="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="34,57 59,57 59,28 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="34,14 40,27 28,27 34,14 34,15 34,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="35" x2="35" y1="69" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="35" x2="43" y1="77" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="27" x2="35" y1="85" y2="77"/>
   </symbol>
   <symbol id="transformer2:shape96_1">
    <ellipse cx="35" cy="112" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="34" y1="109" y2="117"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="42" y1="117" y2="125"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="125" y2="117"/>
   </symbol>
   <symbol id="transformer2:shape13_0">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="38" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape13_1">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="46" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="29" y1="34" y2="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape82">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="23" x2="23" y1="54" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="38" x2="50" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="45" x2="43" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="47" x2="41" y1="5" y2="5"/>
    <polyline points="44,17 44,9 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="33,28 44,28 44,17 32,17 " stroke-width="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="29" x2="32" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="32" x2="32" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="34" x2="32" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="24" x2="18" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.755439" x1="22" x2="24" y1="31" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7538" x1="20" x2="18" y1="31" y2="28"/>
    <circle cx="22" cy="29" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="28" y1="31" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="19" x2="22" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="22" x2="22" y1="17" y2="15"/>
    <ellipse cx="21" cy="19" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="24" x2="22" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="27" y1="31" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="29" x2="32" y1="30" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="32" x2="32" y1="28" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="34" x2="32" y1="30" y2="28"/>
    <ellipse cx="31" cy="19" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="32" cy="27" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="31" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="16" x2="16" y1="54" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="15" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="40" x2="40" y1="54" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="33" x2="33" y1="54" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="33" y1="48" y2="48"/>
   </symbol>
   <symbol id="voltageTransformer:shape98">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="22" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="39" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="36" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="37" y1="24" y2="24"/>
    <circle cx="7" cy="12" r="7.5" stroke-width="1"/>
    <circle cx="7" cy="24" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="10" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="4" y1="10" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="10" y2="14"/>
    <circle cx="18" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="35" y1="12" y2="12"/>
    <circle cx="18" cy="24" r="7.5" stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2e45f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e70e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2e71470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2e72260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2e73100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2e73a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e743e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2e74d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2582b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2582b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e78100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e78100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e7a000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e7a000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2e7b010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e7cca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2e7d8f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2e7e650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2e7ef70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e80930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e813a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e81cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2e824e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e835c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e83f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e84a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2e853f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2e868d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2e873f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2e88440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2e89090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2e973a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2e97e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2e8b8d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2e8ceb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1189" width="1883" x="-288" y="-1130"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1129" x2="1129" y1="-72" y2="-42"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1129" x2="1187" y1="-42" y2="-42"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1188" x2="1188" y1="-58" y2="-41"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-110339">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 765.032258 -476.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21326" ObjectName="SW-SB_PL.SB_PL_001BK"/>
     <cge:Meas_Ref ObjectId="110339"/>
    <cge:TPSR_Ref TObjectID="21326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110430">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 765.032258 -814.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21328" ObjectName="SW-SB_PL.SB_PL_321BK"/>
     <cge:Meas_Ref ObjectId="110430"/>
    <cge:TPSR_Ref TObjectID="21328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110502">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 263.000000 -263.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21340" ObjectName="SW-SB_PL.SB_PL_021BK"/>
     <cge:Meas_Ref ObjectId="110502"/>
    <cge:TPSR_Ref TObjectID="21340"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110481">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 498.000000 -263.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21337" ObjectName="SW-SB_PL.SB_PL_022BK"/>
     <cge:Meas_Ref ObjectId="110481"/>
    <cge:TPSR_Ref TObjectID="21337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110460">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 691.000000 -261.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21334" ObjectName="SW-SB_PL.SB_PL_023BK"/>
     <cge:Meas_Ref ObjectId="110460"/>
    <cge:TPSR_Ref TObjectID="21334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110523">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 922.000000 -263.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21343" ObjectName="SW-SB_PL.SB_PL_024BK"/>
     <cge:Meas_Ref ObjectId="110523"/>
    <cge:TPSR_Ref TObjectID="21343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110612">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1178.000000 -302.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21351" ObjectName="SW-SB_PL.SB_PL_025BK"/>
     <cge:Meas_Ref ObjectId="110612"/>
    <cge:TPSR_Ref TObjectID="21351"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_24e4ea0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 810.032258 -990.000000)" xlink:href="#voltageTransformer:shape82"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23b8380">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1329.000000 -590.000000)" xlink:href="#voltageTransformer:shape98"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="SB_PL" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_damaidiTpl" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="774,-1054 774,-1094 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37761" ObjectName="AC-35kV.LN_damaidiTpl"/>
    <cge:TPSR_Ref TObjectID="37761_SS-163"/></metadata>
   <polyline fill="none" opacity="0" points="774,-1054 774,-1094 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-SB_PL.021Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 267.000000 -35.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34348" ObjectName="EC-SB_PL.021Ld"/>
    <cge:TPSR_Ref TObjectID="34348"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_PL.022Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 502.000000 -35.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34349" ObjectName="EC-SB_PL.022Ld"/>
    <cge:TPSR_Ref TObjectID="34349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_PL.023Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 695.000000 -33.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34350" ObjectName="EC-SB_PL.023Ld"/>
    <cge:TPSR_Ref TObjectID="34350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_PL.024Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 926.000000 -35.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34351" ObjectName="EC-SB_PL.024Ld"/>
    <cge:TPSR_Ref TObjectID="34351"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_21aff10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 830.032258 -633.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24fb400" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 572.032258 -797.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_246b720" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 830.032258 -865.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23b4a70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 830.032258 -952.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24cb320" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1092.000000 -224.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24e39f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1123.000000 -121.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24823f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 678.032258 -789.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_24672d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="824,-639 834,-639 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21325@1" ObjectIDZND0="g_21aff10@0" Pin0InfoVect0LinkObjId="g_21aff10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110328_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="824,-639 834,-639 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24fba30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,-726 578,-748 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="21348@x" ObjectIDZND0="21349@0" Pin0InfoVect0LinkObjId="SW-110575_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110574_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="578,-726 578,-748 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24fbc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,-784 578,-802 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21349@1" ObjectIDZND0="g_24fb400@0" Pin0InfoVect0LinkObjId="g_24fb400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110575_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="578,-784 578,-802 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_246bed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="774,-871 788,-871 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="21331@x" ObjectIDND1="21328@x" ObjectIDZND0="21332@0" Pin0InfoVect0LinkObjId="SW-110441_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-110440_0" Pin1InfoVect1LinkObjId="SW-110430_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="774,-871 788,-871 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_246c0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="824,-871 834,-871 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21332@1" ObjectIDZND0="g_246b720@0" Pin0InfoVect0LinkObjId="g_246b720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110441_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="824,-871 834,-871 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23b5220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="824,-958 834,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21333@1" ObjectIDZND0="g_23b4a70@0" Pin0InfoVect0LinkObjId="g_23b4a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110442_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="824,-958 834,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_247ad30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="646,-947 646,-979 694,-979 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="21358@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_24e4ea0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="646,-947 646,-979 694,-979 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23e11b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="272,-417 272,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21321@0" ObjectIDZND0="21341@1" Pin0InfoVect0LinkObjId="SW-110505_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24cc280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="272,-417 272,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23e13a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="272,-348 272,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21341@0" ObjectIDZND0="21340@1" Pin0InfoVect0LinkObjId="SW-110502_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110505_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="272,-348 272,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23e1590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="272,-271 272,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21340@0" ObjectIDZND0="21342@1" Pin0InfoVect0LinkObjId="SW-110506_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110502_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="272,-271 272,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23e1780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="272,-125 316,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="21342@x" ObjectIDND1="34348@x" ObjectIDZND0="g_23dff00@0" Pin0InfoVect0LinkObjId="g_23dff00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-110506_0" Pin1InfoVect1LinkObjId="EC-SB_PL.021Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="272,-125 316,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23e1fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="272,-189 272,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="21342@0" ObjectIDZND0="34348@x" ObjectIDZND1="g_23dff00@0" Pin0InfoVect0LinkObjId="EC-SB_PL.021Ld_0" Pin0InfoVect1LinkObjId="g_23dff00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110506_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="272,-189 272,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23e21c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="272,-125 272,-56 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="21342@x" ObjectIDND1="g_23dff00@0" ObjectIDZND0="34348@0" Pin0InfoVect0LinkObjId="EC-SB_PL.021Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-110506_0" Pin1InfoVect1LinkObjId="g_23dff00_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="272,-125 272,-56 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2484c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="507,-417 507,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21321@0" ObjectIDZND0="21338@1" Pin0InfoVect0LinkObjId="SW-110484_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24cc280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="507,-417 507,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2484ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="507,-348 507,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21338@0" ObjectIDZND0="21337@1" Pin0InfoVect0LinkObjId="SW-110481_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110484_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="507,-348 507,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24850c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="507,-125 551,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="34349@x" ObjectIDND1="0@x" ObjectIDND2="21339@x" ObjectIDZND0="g_2450e70@0" Pin0InfoVect0LinkObjId="g_2450e70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-SB_PL.022Ld_0" Pin1InfoVect1LinkObjId="g_24e4ea0_0" Pin1InfoVect2LinkObjId="SW-110485_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="507,-125 551,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24852e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="507,-125 507,-56 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_2450e70@0" ObjectIDND1="0@x" ObjectIDND2="21339@x" ObjectIDZND0="34349@0" Pin0InfoVect0LinkObjId="EC-SB_PL.022Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2450e70_0" Pin1InfoVect1LinkObjId="g_24e4ea0_0" Pin1InfoVect2LinkObjId="SW-110485_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="507,-125 507,-56 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24efae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="700,-417 700,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21321@0" ObjectIDZND0="21335@1" Pin0InfoVect0LinkObjId="SW-110463_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24cc280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="700,-417 700,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24efd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="700,-346 700,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21335@0" ObjectIDZND0="21334@1" Pin0InfoVect0LinkObjId="SW-110460_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110463_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="700,-346 700,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24eff20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="700,-273 700,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21334@0" ObjectIDZND0="21336@1" Pin0InfoVect0LinkObjId="SW-110464_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="700,-273 700,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24f0140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="700,-123 744,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="21336@x" ObjectIDND1="34350@x" ObjectIDZND0="g_24ee4a0@0" Pin0InfoVect0LinkObjId="g_24ee4a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-110464_0" Pin1InfoVect1LinkObjId="EC-SB_PL.023Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="700,-123 744,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24f0360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="700,-187 700,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="21336@0" ObjectIDZND0="g_24ee4a0@0" ObjectIDZND1="34350@x" Pin0InfoVect0LinkObjId="g_24ee4a0_0" Pin0InfoVect1LinkObjId="EC-SB_PL.023Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110464_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="700,-187 700,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24f0580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="700,-123 700,-54 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="21336@x" ObjectIDND1="g_24ee4a0@0" ObjectIDZND0="34350@0" Pin0InfoVect0LinkObjId="EC-SB_PL.023Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-110464_0" Pin1InfoVect1LinkObjId="g_24ee4a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="700,-123 700,-54 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24d4440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="931,-417 931,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21321@0" ObjectIDZND0="21344@1" Pin0InfoVect0LinkObjId="SW-110526_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24cc280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="931,-417 931,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24d4660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="931,-348 931,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21344@0" ObjectIDZND0="21343@1" Pin0InfoVect0LinkObjId="SW-110523_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110526_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="931,-348 931,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24d4880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="931,-271 931,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21343@0" ObjectIDZND0="21345@1" Pin0InfoVect0LinkObjId="SW-110527_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110523_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="931,-271 931,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24d4aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="931,-125 975,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="34351@x" ObjectIDND1="21345@x" ObjectIDZND0="g_24b0d50@0" Pin0InfoVect0LinkObjId="g_24b0d50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-SB_PL.024Ld_0" Pin1InfoVect1LinkObjId="SW-110527_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="931,-125 975,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24d4cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="931,-192 931,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="21345@0" ObjectIDZND0="g_24b0d50@0" ObjectIDZND1="34351@x" Pin0InfoVect0LinkObjId="g_24b0d50_0" Pin0InfoVect1LinkObjId="EC-SB_PL.024Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110527_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="931,-192 931,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24d4ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="931,-125 931,-56 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_24b0d50@0" ObjectIDND1="21345@x" ObjectIDZND0="34351@0" Pin0InfoVect0LinkObjId="EC-SB_PL.024Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24b0d50_0" Pin1InfoVect1LinkObjId="SW-110527_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="931,-125 931,-56 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2477ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="449,-115 449,-131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="21359@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_24e4ea0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="449,-115 449,-131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2477ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="449,-183 449,-190 508,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="0@0" ObjectIDZND0="21339@x" ObjectIDZND1="g_2450e70@0" ObjectIDZND2="34349@x" Pin0InfoVect0LinkObjId="SW-110485_0" Pin0InfoVect1LinkObjId="g_2450e70_0" Pin0InfoVect2LinkObjId="EC-SB_PL.022Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24e4ea0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="449,-183 449,-190 508,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24780e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="507,-239 507,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21339@1" ObjectIDZND0="21337@0" Pin0InfoVect0LinkObjId="SW-110481_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110485_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="507,-239 507,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24cc280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1187,-393 1187,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21352@1" ObjectIDZND0="21321@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110615_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1187,-393 1187,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24cc4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1187,-337 1187,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21351@1" ObjectIDZND0="21352@0" Pin0InfoVect0LinkObjId="SW-110615_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110612_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1187,-337 1187,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24cc6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1187,-287 1187,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21353@1" ObjectIDZND0="21351@0" Pin0InfoVect0LinkObjId="SW-110612_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110617_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1187,-287 1187,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24cd1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1187,-230 1187,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="capacitor" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_24cbd50@0" ObjectIDND1="41591@x" ObjectIDND2="21354@x" ObjectIDZND0="21353@0" Pin0InfoVect0LinkObjId="SW-110617_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_24cbd50_0" Pin1InfoVect1LinkObjId="CB-SB_PL.SB_PL_Cb1_0" Pin1InfoVect2LinkObjId="SW-110618_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1187,-230 1187,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24cd410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1187,-207 1230,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="lightningRod" ObjectIDND0="21353@x" ObjectIDND1="21354@x" ObjectIDND2="41591@x" ObjectIDZND0="g_24cbd50@0" Pin0InfoVect0LinkObjId="g_24cbd50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-110617_0" Pin1InfoVect1LinkObjId="SW-110618_0" Pin1InfoVect2LinkObjId="CB-SB_PL.SB_PL_Cb1_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1187,-207 1230,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24e2b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1187,-207 1187,-230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="capacitor" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_24cbd50@0" ObjectIDND1="41591@x" ObjectIDZND0="21353@x" ObjectIDZND1="21354@x" Pin0InfoVect0LinkObjId="SW-110617_0" Pin0InfoVect1LinkObjId="SW-110618_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24cbd50_0" Pin1InfoVect1LinkObjId="CB-SB_PL.SB_PL_Cb1_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1187,-207 1187,-230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24e2de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1187,-188 1187,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="41591@0" ObjectIDZND0="g_24cbd50@0" ObjectIDZND1="21353@x" ObjectIDZND2="21354@x" Pin0InfoVect0LinkObjId="g_24cbd50_0" Pin0InfoVect1LinkObjId="SW-110617_0" Pin0InfoVect2LinkObjId="SW-110618_0" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-SB_PL.SB_PL_Cb1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1187,-188 1187,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24e3040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1110,-230 1131,-230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_24cb320@0" ObjectIDZND0="21354@0" Pin0InfoVect0LinkObjId="SW-110618_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24cb320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1110,-230 1131,-230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24e32a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1167,-230 1187,-230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="capacitor" ObjectIDND0="21354@1" ObjectIDZND0="21353@x" ObjectIDZND1="g_24cbd50@0" ObjectIDZND2="41591@x" Pin0InfoVect0LinkObjId="SW-110617_0" Pin0InfoVect1LinkObjId="g_24cbd50_0" Pin0InfoVect2LinkObjId="CB-SB_PL.SB_PL_Cb1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110618_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1167,-230 1187,-230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24e4170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1129,-126 1129,-109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_24e39f0@0" ObjectIDZND0="21355@1" Pin0InfoVect0LinkObjId="SW-110619_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24e39f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1129,-126 1129,-109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24e4a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="686,-726 774,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="21348@1" ObjectIDZND0="21329@x" ObjectIDZND1="48773@0" Pin0InfoVect0LinkObjId="SW-110438_0" Pin0InfoVect1LinkObjId="g_2324370_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110574_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="686,-726 774,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24647a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="578,-726 528,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="21349@x" ObjectIDND1="21348@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-110575_0" Pin1InfoVect1LinkObjId="SW-110574_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="578,-726 528,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2464a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="650,-726 578,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="21348@0" ObjectIDZND0="21349@x" Pin0InfoVect0LinkObjId="SW-110575_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110574_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="650,-726 578,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2482e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="710,-795 696,-795 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21330@0" ObjectIDZND0="g_24823f0@0" Pin0InfoVect0LinkObjId="g_24823f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110439_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="710,-795 696,-795 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2483970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="774,-743 774,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="21329@0" ObjectIDZND0="21348@x" ObjectIDZND1="48773@0" Pin0InfoVect0LinkObjId="SW-110574_0" Pin0InfoVect1LinkObjId="g_2324370_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110438_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="774,-743 774,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2483bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="746,-795 774,-795 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="21330@1" ObjectIDZND0="21329@x" ObjectIDZND1="21328@x" Pin0InfoVect0LinkObjId="SW-110438_0" Pin0InfoVect1LinkObjId="SW-110430_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110439_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="746,-795 774,-795 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_245b170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="774,-822 774,-795 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="21328@0" ObjectIDZND0="21330@x" ObjectIDZND1="21329@x" Pin0InfoVect0LinkObjId="SW-110439_0" Pin0InfoVect1LinkObjId="SW-110438_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="774,-822 774,-795 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_245b3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="774,-795 774,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="21330@x" ObjectIDND1="21328@x" ObjectIDZND0="21329@1" Pin0InfoVect0LinkObjId="SW-110438_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-110439_0" Pin1InfoVect1LinkObjId="SW-110430_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="774,-795 774,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_245bea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="774,-849 774,-871 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="21328@1" ObjectIDZND0="21332@x" ObjectIDZND1="21331@x" Pin0InfoVect0LinkObjId="SW-110441_0" Pin0InfoVect1LinkObjId="SW-110440_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110430_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="774,-849 774,-871 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_245c100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="774,-906 774,-871 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="21331@0" ObjectIDZND0="21332@x" ObjectIDZND1="21328@x" Pin0InfoVect0LinkObjId="SW-110441_0" Pin0InfoVect1LinkObjId="SW-110430_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="774,-906 774,-871 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_245c910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="213,-417 213,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21321@0" ObjectIDZND0="21346@0" Pin0InfoVect0LinkObjId="SW-110558_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24cc280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="213,-417 213,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2446750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="213,-513 213,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="21346@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110558_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="213,-513 213,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2447e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1348,-417 1348,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21321@0" ObjectIDZND0="21347@0" Pin0InfoVect0LinkObjId="SW-110568_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24cc280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1348,-417 1348,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24f3600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="507,-203 507,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="21339@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2450e70@0" ObjectIDZND2="34349@x" Pin0InfoVect0LinkObjId="g_24e4ea0_0" Pin0InfoVect1LinkObjId="g_2450e70_0" Pin0InfoVect2LinkObjId="EC-SB_PL.022Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110485_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="507,-203 507,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24f37f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="507,-190 507,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="0@x" ObjectIDND1="21339@x" ObjectIDZND0="g_2450e70@0" ObjectIDZND1="34349@x" Pin0InfoVect0LinkObjId="g_2450e70_0" Pin0InfoVect1LinkObjId="EC-SB_PL.022Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24e4ea0_0" Pin1InfoVect1LinkObjId="SW-110485_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="507,-190 507,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23bb290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1316,-517 1316,-510 1348,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_23ba4e0@0" ObjectIDZND0="21347@x" ObjectIDZND1="g_23bbfe0@0" Pin0InfoVect0LinkObjId="SW-110568_0" Pin0InfoVect1LinkObjId="g_23bbfe0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23ba4e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1316,-517 1316,-510 1348,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23bbd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1348,-494 1348,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="21347@1" ObjectIDZND0="g_23ba4e0@0" ObjectIDZND1="g_23bbfe0@0" Pin0InfoVect0LinkObjId="g_23ba4e0_0" Pin0InfoVect1LinkObjId="g_23bbfe0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110568_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1348,-494 1348,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24b4b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1348,-510 1348,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_23ba4e0@0" ObjectIDND1="21347@x" ObjectIDZND0="g_23bbfe0@0" Pin0InfoVect0LinkObjId="g_23bbfe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_23ba4e0_0" Pin1InfoVect1LinkObjId="SW-110568_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1348,-510 1348,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24b4da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1348,-575 1348,-595 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_23bbfe0@1" ObjectIDZND0="g_23b8380@0" Pin0InfoVect0LinkObjId="g_23b8380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23bbfe0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1348,-575 1348,-595 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24b8180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="788,-958 774,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="21333@0" ObjectIDZND0="21331@x" ObjectIDZND1="g_24e4ea0@0" ObjectIDZND2="g_24b6e10@0" Pin0InfoVect0LinkObjId="SW-110440_0" Pin0InfoVect1LinkObjId="g_24e4ea0_0" Pin0InfoVect2LinkObjId="g_24b6e10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110442_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="788,-958 774,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24b8c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="774,-958 774,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="21333@x" ObjectIDND1="g_24e4ea0@0" ObjectIDND2="g_24b6e10@0" ObjectIDZND0="21331@1" Pin0InfoVect0LinkObjId="SW-110440_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-110442_0" Pin1InfoVect1LinkObjId="g_24e4ea0_0" Pin1InfoVect2LinkObjId="g_24b6e10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="774,-958 774,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24b8ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="815,-1037 774,-1037 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_24e4ea0@0" ObjectIDZND0="g_24b6e10@0" ObjectIDZND1="0@x" ObjectIDZND2="21333@x" Pin0InfoVect0LinkObjId="g_24b6e10_0" Pin0InfoVect1LinkObjId="g_24e4ea0_0" Pin0InfoVect2LinkObjId="SW-110442_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24e4ea0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="815,-1037 774,-1037 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_242f210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="774,-1054 774,-1037 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="37761@1" ObjectIDZND0="g_24e4ea0@0" ObjectIDZND1="g_24b6e10@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_24e4ea0_0" Pin0InfoVect1LinkObjId="g_24b6e10_0" Pin0InfoVect2LinkObjId="g_24e4ea0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="774,-1054 774,-1037 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_242f470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="743,-1037 774,-1037 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_24b6e10@0" ObjectIDZND0="g_24e4ea0@0" ObjectIDZND1="0@x" ObjectIDZND2="21333@x" Pin0InfoVect0LinkObjId="g_24e4ea0_0" Pin0InfoVect1LinkObjId="g_24e4ea0_0" Pin0InfoVect2LinkObjId="SW-110442_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24b6e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="743,-1037 774,-1037 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_242f6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="746,-979 774,-979 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="0@0" ObjectIDZND0="g_24e4ea0@0" ObjectIDZND1="g_24b6e10@0" ObjectIDZND2="37761@1" Pin0InfoVect0LinkObjId="g_24e4ea0_0" Pin0InfoVect1LinkObjId="g_24b6e10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24e4ea0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="746,-979 774,-979 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24301a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="774,-1037 774,-979 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_24e4ea0@0" ObjectIDND1="g_24b6e10@0" ObjectIDND2="37761@1" ObjectIDZND0="0@x" ObjectIDZND1="21333@x" ObjectIDZND2="21331@x" Pin0InfoVect0LinkObjId="g_24e4ea0_0" Pin0InfoVect1LinkObjId="SW-110442_0" Pin0InfoVect2LinkObjId="SW-110440_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_24e4ea0_0" Pin1InfoVect1LinkObjId="g_24b6e10_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="774,-1037 774,-979 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2430400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="774,-979 774,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_24e4ea0@0" ObjectIDND1="g_24b6e10@0" ObjectIDND2="37761@1" ObjectIDZND0="21333@x" ObjectIDZND1="21331@x" Pin0InfoVect0LinkObjId="SW-110442_0" Pin0InfoVect1LinkObjId="SW-110440_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_24e4ea0_0" Pin1InfoVect1LinkObjId="g_24b6e10_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="774,-979 774,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2475640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="774,-706 774,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="48773@0" ObjectIDZND0="21329@x" ObjectIDZND1="21348@x" Pin0InfoVect0LinkObjId="SW-110438_0" Pin0InfoVect1LinkObjId="SW-110574_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2324370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="774,-706 774,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2475830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="774,-417 774,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21321@0" ObjectIDZND0="21327@0" Pin0InfoVect0LinkObjId="SW-110342_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24cc280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="774,-417 774,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2475a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="774,-469 774,-484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21327@1" ObjectIDZND0="21326@0" Pin0InfoVect0LinkObjId="SW-110339_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110342_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="774,-469 774,-484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2475c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="774,-511 774,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="21326@1" ObjectIDZND0="21357@1" Pin0InfoVect0LinkObjId="g_2476380_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110339_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="774,-511 774,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2476380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="788,-639 774,-639 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="21325@0" ObjectIDZND0="21357@x" ObjectIDZND1="21324@x" Pin0InfoVect0LinkObjId="g_2475c10_0" Pin0InfoVect1LinkObjId="SW-110326_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110328_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="788,-639 774,-639 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2476e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="774,-613 774,-639 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="21357@0" ObjectIDZND0="21325@x" ObjectIDZND1="21324@x" Pin0InfoVect0LinkObjId="SW-110328_0" Pin0InfoVect1LinkObjId="SW-110326_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2475c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="774,-613 774,-639 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2477070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="774,-639 774,-657 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21357@x" ObjectIDND1="21325@x" ObjectIDZND0="21324@0" Pin0InfoVect0LinkObjId="SW-110326_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2475c10_0" Pin1InfoVect1LinkObjId="SW-110328_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="774,-639 774,-657 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2324370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="774,-693 774,-706 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21324@1" ObjectIDZND0="48773@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110326_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="774,-693 774,-706 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="21321" cx="272" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21321" cx="507" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21321" cx="700" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21321" cx="931" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21321" cx="1187" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21321" cx="213" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21321" cx="1348" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21321" cx="774" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48773" cx="774" cy="-706" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48773" cx="774" cy="-706" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-110061" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 33.000000 -970.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21305" ObjectName="DYN-SB_PL"/>
     <cge:Meas_Ref ObjectId="110061"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_254d590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_254d590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_254d590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_254d590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_254d590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_254d590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_254d590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_254d590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_254d590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22fbdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22fbdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22fbdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22fbdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22fbdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22fbdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22fbdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22fbdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22fbdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22fbdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22fbdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22fbdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22fbdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22fbdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22fbdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22fbdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22fbdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22fbdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_23ec150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -114.000000 -1049.500000) translate(0,16)">普龙变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_21adbe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 25.000000 -440.000000) translate(0,15)">10kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24fbe10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 577.000000 -714.000000) translate(0,15)">35kV内桥</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_247af20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 752.000000 -1125.000000) translate(0,15)">35kV大麦T普线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_247b250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 873.000000 -1031.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_247b450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 251.000000 -25.000000) translate(0,15)">城街线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23e23b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 472.000000 -25.000000) translate(0,15)">河门口线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2485c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 672.000000 -23.000000) translate(0,15)">埂井线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24f0ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 904.000000 -25.000000) translate(0,15)">普大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24e3500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1136.000000 -39.000000) translate(0,15)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24e43d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1320.000000 -649.000000) translate(0,15)">母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_24e4bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -114.000000) translate(0,17)">7820137</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24469b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 155.000000 -563.000000) translate(0,15)">接分段二期断路器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_247fbf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 797.032258 -984.000000) translate(0,12)">32167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2480200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 720.032258 -821.000000) translate(0,12)">32117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2480620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 788.032258 -842.000000) translate(0,12)">321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2480860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 781.032258 -931.000000) translate(0,12)">3216</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2480aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 786.032258 -897.000000) translate(0,12)">32160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24bb610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 781.032258 -768.000000) translate(0,12)">3211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24bba70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 652.032258 -752.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24bbcb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 585.032258 -773.000000) translate(0,12)">31217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24bbef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 781.032258 -682.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24bc130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 786.032258 -665.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24bc370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 783.032258 -505.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24bc5b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 781.032258 -458.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24bc7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 281.000000 -292.000000) translate(0,12)">021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24bca30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 279.000000 -373.000000) translate(0,12)">0211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24bcc70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 279.000000 -214.000000) translate(0,12)">0216</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24bceb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 220.000000 -502.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24bd0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 516.000000 -292.000000) translate(0,12)">022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24bd330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 514.000000 -373.000000) translate(0,12)">0221</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24bd570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 514.000000 -228.000000) translate(0,12)">0226</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24bd7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 709.000000 -290.000000) translate(0,12)">023</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24bd9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 707.000000 -371.000000) translate(0,12)">0231</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24bdc30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 707.000000 -212.000000) translate(0,12)">0236</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24bde70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 940.000000 -292.000000) translate(0,12)">024</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24be1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 938.000000 -373.000000) translate(0,12)">0241</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24be610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 938.000000 -214.000000) translate(0,12)">0246</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24be850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1196.000000 -331.000000) translate(0,12)">025</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24beb90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1194.000000 -382.000000) translate(0,12)">0251</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24beff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1194.000000 -276.000000) translate(0,12)">0256</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2448280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1129.000000 -256.000000) translate(0,12)">02567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24484c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1075.000000 -93.000000) translate(0,12)">02500</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2448700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1355.000000 -483.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2448bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 420.000000 -122.000000) translate(0,12)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2448bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 420.000000 -122.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2448bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 420.000000 -122.000000) translate(0,42)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2448bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 420.000000 -122.000000) translate(0,57)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2448bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 420.000000 -122.000000) translate(0,72)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2448bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 420.000000 -122.000000) translate(0,87)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2448bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 420.000000 -122.000000) translate(0,102)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23f6d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 489.000000 -619.000000) translate(0,15)">主变参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23f6d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 489.000000 -619.000000) translate(0,33)">SZ11-2500/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23f6d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 489.000000 -619.000000) translate(0,51)">3500/10500V</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23f6d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 489.000000 -619.000000) translate(0,69)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23f6d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 489.000000 -619.000000) translate(0,87)">标准档电流41.2/137.5  Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23f6d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 489.000000 -619.000000) translate(0,105)">Ud%=7.12%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_239ea70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 895.000000 -754.000000) translate(0,12)">TA变比 200/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_239fa30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 90.000000 -1054.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_23a0f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 89.000000 -1013.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_249a220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -244.000000 -713.000000) translate(0,16)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2432b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -264.000000 -79.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2432b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -264.000000 -79.000000) translate(0,38)">心变运三班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_24350f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -89.500000) translate(0,17)">18787878955</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_24350f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -89.500000) translate(0,38)">18787878953</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_24350f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -89.500000) translate(0,59)">18787878979</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2474270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 823.032258 -601.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2474970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 511.032258 -936.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2474d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 680.000000 -964.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2421850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 106.500000 -946.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24224b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -283.000000 -345.000000) translate(0,12)">河门口022A相电流现场采集有缺陷,</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24224b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -283.000000 -345.000000) translate(0,27)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24224b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -283.000000 -345.000000) translate(0,42)">缺陷消除前用C相替代(A相60号改为空,62原C相改为A相)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2cd0ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 822.000000 -715.000000) translate(0,12)">XM</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="90" y="-957"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-SB_PL.SB_PL_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="60,-417 1590,-417 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="21321" ObjectName="BS-SB_PL.SB_PL_9IM"/>
    <cge:TPSR_Ref TObjectID="21321"/></metadata>
   <polyline fill="none" opacity="0" points="60,-417 1590,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-SB_PL.XM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="768,-706 780,-706 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48773" ObjectName="BS-SB_PL.XM"/>
    <cge:TPSR_Ref TObjectID="48773"/></metadata>
   <polyline fill="none" opacity="0" points="768,-706 780,-706 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-SB_PL.SB_PL_Zyb2">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="29859"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 434.000000 -22.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 434.000000 -22.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="21359" ObjectName="TF-SB_PL.SB_PL_Zyb2"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-SB_PL.SB_PL_Zyb1">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="29855"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.861538 -0.000000 0.000000 -0.638151 616.032258 -860.133880)" xlink:href="#transformer2:shape96_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.861538 -0.000000 0.000000 -0.638151 616.032258 -860.133880)" xlink:href="#transformer2:shape96_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="21358" ObjectName="TF-SB_PL.SB_PL_Zyb1"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-SB_PL.SB_PL_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="29851"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.069444 -0.000000 0.000000 -1.088889 734.032258 -521.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.069444 -0.000000 0.000000 -1.088889 734.032258 -521.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="21357" ObjectName="TF-SB_PL.SB_PL_1T"/>
    <cge:TPSR_Ref TObjectID="21357"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -161.000000 -1001.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-250477" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -132.000000 -822.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250477" ObjectName="SB_PL:SB_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-250478" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -132.000000 -785.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="250478" ObjectName="SB_PL:SB_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-110202" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -133.000000 -905.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110202" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-110202" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -132.000000 -865.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110202" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="141" x="-151" y="-1060"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="141" x="-151" y="-1060"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-198" y="-1077"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-198" y="-1077"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="281" y="-292"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="281" y="-292"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="799" y="-844"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="799" y="-844"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="79" y="-1062"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="79" y="-1062"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="81" y="-1020"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="81" y="-1020"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="516" y="-292"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="516" y="-292"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="709" y="-290"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="709" y="-290"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="940" y="-292"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="940" y="-292"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1196" y="-331"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1196" y="-331"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="26" qtmmishow="hidden" width="87" x="-247" y="-716"/>
    </a>
   <metadata/><rect fill="white" height="26" opacity="0" stroke="white" transform="" width="87" x="-247" y="-716"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="834" y="-605"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="834" y="-605"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="89" y="-958"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="89" y="-958"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_244a190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 637.000000 514.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24a86b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 626.000000 499.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24a9240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 651.000000 484.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24a9bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 204.000000 -9.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24a9e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 193.000000 -24.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24aa090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 218.000000 -39.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24aa890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 829.000000 583.000000) translate(0,12)">档位(档):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24ab140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 829.000000 567.000000) translate(0,12)">温度(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f17c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 15.000000 524.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f1dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 15.000000 510.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f2320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 15.000000 496.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f2880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 21.000000 481.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f2b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 7.000000 465.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24b5bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 905.000000 817.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24b5e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 905.000000 803.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24b6090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 911.000000 788.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24b62d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 897.000000 772.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24b6510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 905.000000 876.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24b6750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 894.000000 861.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24b6990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 919.000000 846.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24b6bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 905.000000 831.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2430d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 439.000000 -7.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2430f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 428.000000 -22.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2431170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 453.000000 -37.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24314a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 634.000000 -6.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2431700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 623.000000 -21.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2431940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 648.000000 -36.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2431c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 867.000000 -7.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2431ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 856.000000 -22.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2432110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 881.000000 -37.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2432440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1116.000000 -2.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24326a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1105.000000 -17.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24328e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1130.000000 -32.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-SB_PL.SB_PL_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1150.000000 -57.000000)" xlink:href="#capacitor:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41591" ObjectName="CB-SB_PL.SB_PL_Cb1"/>
    <cge:TPSR_Ref TObjectID="41591"/></metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-110187" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 686.032258 -515.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110187" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21326"/>
     <cge:Term_Ref ObjectID="29787"/>
    <cge:TPSR_Ref TObjectID="21326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-110188" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 686.032258 -515.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110188" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21326"/>
     <cge:Term_Ref ObjectID="29787"/>
    <cge:TPSR_Ref TObjectID="21326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-110179" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 686.032258 -515.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110179" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21326"/>
     <cge:Term_Ref ObjectID="29787"/>
    <cge:TPSR_Ref TObjectID="21326"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-110241" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 259.000000 9.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110241" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21340"/>
     <cge:Term_Ref ObjectID="29815"/>
    <cge:TPSR_Ref TObjectID="21340"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-110242" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 259.000000 9.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110242" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21340"/>
     <cge:Term_Ref ObjectID="29815"/>
    <cge:TPSR_Ref TObjectID="21340"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-110231" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 259.000000 9.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110231" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21340"/>
     <cge:Term_Ref ObjectID="29815"/>
    <cge:TPSR_Ref TObjectID="21340"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-110228" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 493.000000 9.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110228" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21337"/>
     <cge:Term_Ref ObjectID="29809"/>
    <cge:TPSR_Ref TObjectID="21337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-110229" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 493.000000 9.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110229" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21337"/>
     <cge:Term_Ref ObjectID="29809"/>
    <cge:TPSR_Ref TObjectID="21337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-110218" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 493.000000 9.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110218" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21337"/>
     <cge:Term_Ref ObjectID="29809"/>
    <cge:TPSR_Ref TObjectID="21337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-110215" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 688.000000 9.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110215" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21334"/>
     <cge:Term_Ref ObjectID="29803"/>
    <cge:TPSR_Ref TObjectID="21334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-110216" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 688.000000 9.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110216" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21334"/>
     <cge:Term_Ref ObjectID="29803"/>
    <cge:TPSR_Ref TObjectID="21334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-110205" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 688.000000 9.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110205" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21334"/>
     <cge:Term_Ref ObjectID="29803"/>
    <cge:TPSR_Ref TObjectID="21334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-110254" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 919.000000 9.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110254" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21343"/>
     <cge:Term_Ref ObjectID="29821"/>
    <cge:TPSR_Ref TObjectID="21343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-110255" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 919.000000 9.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110255" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21343"/>
     <cge:Term_Ref ObjectID="29821"/>
    <cge:TPSR_Ref TObjectID="21343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-110244" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 919.000000 9.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110244" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21343"/>
     <cge:Term_Ref ObjectID="29821"/>
    <cge:TPSR_Ref TObjectID="21343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-110263" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 70.000000 -524.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110263" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21321"/>
     <cge:Term_Ref ObjectID="29779"/>
    <cge:TPSR_Ref TObjectID="21321"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-110264" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 70.000000 -524.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110264" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21321"/>
     <cge:Term_Ref ObjectID="29779"/>
    <cge:TPSR_Ref TObjectID="21321"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-110265" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 70.000000 -524.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110265" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21321"/>
     <cge:Term_Ref ObjectID="29779"/>
    <cge:TPSR_Ref TObjectID="21321"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-110273" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 70.000000 -524.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110273" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21321"/>
     <cge:Term_Ref ObjectID="29779"/>
    <cge:TPSR_Ref TObjectID="21321"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-110270" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 70.000000 -524.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110270" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21321"/>
     <cge:Term_Ref ObjectID="29779"/>
    <cge:TPSR_Ref TObjectID="21321"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-110202" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 955.032258 -875.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110202" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21328"/>
     <cge:Term_Ref ObjectID="29791"/>
    <cge:TPSR_Ref TObjectID="21328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-110203" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 955.032258 -875.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110203" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21328"/>
     <cge:Term_Ref ObjectID="29791"/>
    <cge:TPSR_Ref TObjectID="21328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-110192" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 955.032258 -875.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110192" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21328"/>
     <cge:Term_Ref ObjectID="29791"/>
    <cge:TPSR_Ref TObjectID="21328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-110195" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 955.032258 -875.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110195" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21328"/>
     <cge:Term_Ref ObjectID="29791"/>
    <cge:TPSR_Ref TObjectID="21328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-110196" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 955.032258 -875.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110196" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21328"/>
     <cge:Term_Ref ObjectID="29791"/>
    <cge:TPSR_Ref TObjectID="21328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-110197" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 955.032258 -875.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110197" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21328"/>
     <cge:Term_Ref ObjectID="29791"/>
    <cge:TPSR_Ref TObjectID="21328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-110201" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 955.032258 -875.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110201" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21328"/>
     <cge:Term_Ref ObjectID="29791"/>
    <cge:TPSR_Ref TObjectID="21328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-110198" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 955.032258 -875.000000) translate(0,117)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110198" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21328"/>
     <cge:Term_Ref ObjectID="29791"/>
    <cge:TPSR_Ref TObjectID="21328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-110191" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 900.000000 -581.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110191" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21357"/>
     <cge:Term_Ref ObjectID="29852"/>
    <cge:TPSR_Ref TObjectID="21357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-110190" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 900.000000 -581.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110190" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21357"/>
     <cge:Term_Ref ObjectID="29852"/>
    <cge:TPSR_Ref TObjectID="21357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-110285" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1170.000000 2.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110285" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21351"/>
     <cge:Term_Ref ObjectID="29837"/>
    <cge:TPSR_Ref TObjectID="21351"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-110286" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1170.000000 2.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110286" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21351"/>
     <cge:Term_Ref ObjectID="29837"/>
    <cge:TPSR_Ref TObjectID="21351"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-110275" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1170.000000 2.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="110275" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21351"/>
     <cge:Term_Ref ObjectID="29837"/>
    <cge:TPSR_Ref TObjectID="21351"/></metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-110326">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 765.032258 -652.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21324" ObjectName="SW-SB_PL.SB_PL_3011SW"/>
     <cge:Meas_Ref ObjectId="110326"/>
    <cge:TPSR_Ref TObjectID="21324"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110328">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 783.032258 -634.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21325" ObjectName="SW-SB_PL.SB_PL_30117SW"/>
     <cge:Meas_Ref ObjectId="110328"/>
    <cge:TPSR_Ref TObjectID="21325"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110342">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 765.032258 -428.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21327" ObjectName="SW-SB_PL.SB_PL_0011SW"/>
     <cge:Meas_Ref ObjectId="110342"/>
    <cge:TPSR_Ref TObjectID="21327"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110574">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 645.032258 -721.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21348" ObjectName="SW-SB_PL.SB_PL_3121SW"/>
     <cge:Meas_Ref ObjectId="110574"/>
    <cge:TPSR_Ref TObjectID="21348"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110575">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 569.032258 -743.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21349" ObjectName="SW-SB_PL.SB_PL_31217SW"/>
     <cge:Meas_Ref ObjectId="110575"/>
    <cge:TPSR_Ref TObjectID="21349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110438">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 765.032258 -738.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21329" ObjectName="SW-SB_PL.SB_PL_3211SW"/>
     <cge:Meas_Ref ObjectId="110438"/>
    <cge:TPSR_Ref TObjectID="21329"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110440">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 765.032258 -901.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21331" ObjectName="SW-SB_PL.SB_PL_3216SW"/>
     <cge:Meas_Ref ObjectId="110440"/>
    <cge:TPSR_Ref TObjectID="21331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110441">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 783.032258 -866.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21332" ObjectName="SW-SB_PL.SB_PL_32160SW"/>
     <cge:Meas_Ref ObjectId="110441"/>
    <cge:TPSR_Ref TObjectID="21332"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110442">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 783.032258 -953.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21333" ObjectName="SW-SB_PL.SB_PL_32167SW"/>
     <cge:Meas_Ref ObjectId="110442"/>
    <cge:TPSR_Ref TObjectID="21333"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 689.032258 -974.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110505">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 263.000000 -343.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21341" ObjectName="SW-SB_PL.SB_PL_0211SW"/>
     <cge:Meas_Ref ObjectId="110505"/>
    <cge:TPSR_Ref TObjectID="21341"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110506">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 263.000000 -184.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21342" ObjectName="SW-SB_PL.SB_PL_0216SW"/>
     <cge:Meas_Ref ObjectId="110506"/>
    <cge:TPSR_Ref TObjectID="21342"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110484">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 498.000000 -343.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21338" ObjectName="SW-SB_PL.SB_PL_0221SW"/>
     <cge:Meas_Ref ObjectId="110484"/>
    <cge:TPSR_Ref TObjectID="21338"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110485">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 498.000000 -198.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21339" ObjectName="SW-SB_PL.SB_PL_0226SW"/>
     <cge:Meas_Ref ObjectId="110485"/>
    <cge:TPSR_Ref TObjectID="21339"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110463">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 691.000000 -341.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21335" ObjectName="SW-SB_PL.SB_PL_0231SW"/>
     <cge:Meas_Ref ObjectId="110463"/>
    <cge:TPSR_Ref TObjectID="21335"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110464">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 691.000000 -182.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21336" ObjectName="SW-SB_PL.SB_PL_0236SW"/>
     <cge:Meas_Ref ObjectId="110464"/>
    <cge:TPSR_Ref TObjectID="21336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110526">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 922.000000 -343.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21344" ObjectName="SW-SB_PL.SB_PL_0241SW"/>
     <cge:Meas_Ref ObjectId="110526"/>
    <cge:TPSR_Ref TObjectID="21344"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110527">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 922.000000 -187.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21345" ObjectName="SW-SB_PL.SB_PL_0246SW"/>
     <cge:Meas_Ref ObjectId="110527"/>
    <cge:TPSR_Ref TObjectID="21345"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 442.000000 -126.000000)" xlink:href="#switch2:shape25_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110615">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1178.000000 -352.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21352" ObjectName="SW-SB_PL.SB_PL_0251SW"/>
     <cge:Meas_Ref ObjectId="110615"/>
    <cge:TPSR_Ref TObjectID="21352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110617">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1178.000000 -246.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21353" ObjectName="SW-SB_PL.SB_PL_0256SW"/>
     <cge:Meas_Ref ObjectId="110617"/>
    <cge:TPSR_Ref TObjectID="21353"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110619">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1120.000000 -68.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21355" ObjectName="SW-SB_PL.SB_PL_02500SW"/>
     <cge:Meas_Ref ObjectId="110619"/>
    <cge:TPSR_Ref TObjectID="21355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110618">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1126.000000 -225.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21354" ObjectName="SW-SB_PL.SB_PL_02567SW"/>
     <cge:Meas_Ref ObjectId="110618"/>
    <cge:TPSR_Ref TObjectID="21354"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110439">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 705.032258 -790.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21330" ObjectName="SW-SB_PL.SB_PL_32117SW"/>
     <cge:Meas_Ref ObjectId="110439"/>
    <cge:TPSR_Ref TObjectID="21330"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110558">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 204.000000 -472.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21346" ObjectName="SW-SB_PL.SB_PL_0121SW"/>
     <cge:Meas_Ref ObjectId="110558"/>
    <cge:TPSR_Ref TObjectID="21346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110568">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1339.000000 -453.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21347" ObjectName="SW-SB_PL.SB_PL_0901SW"/>
     <cge:Meas_Ref ObjectId="110568"/>
    <cge:TPSR_Ref TObjectID="21347"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="141" x="-151" y="-1060"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-198" y="-1077"/></g>
   <g href="35kV普龙变10kV城街线021间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="281" y="-292"/></g>
   <g href="35kV普龙变35kV大麦T普线321间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="799" y="-844"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="79" y="-1062"/></g>
   <g href="cx_配调_配网接线图35_双柏.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="81" y="-1020"/></g>
   <g href="35kV普龙变10kV河门口线022间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="516" y="-292"/></g>
   <g href="35kV普龙变10kV埂井线023间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="709" y="-290"/></g>
   <g href="35kV普龙变10kV普大线024间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="940" y="-292"/></g>
   <g href="35kV普龙变10kV1号电容器025间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1196" y="-331"/></g>
   <g href="35kV普龙变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="26" qtmmishow="hidden" width="87" x="-247" y="-716"/></g>
   <g href="35kV普龙变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="834" y="-605"/></g>
   <g href="AVC普龙站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="89" y="-958"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_23dff00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 309.000000 -67.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2450e70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 544.000000 -67.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24ee4a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 737.000000 -65.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24b0d50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 968.000000 -67.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24cbd50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1223.000000 -149.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23ba4e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1309.000000 -513.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23bbfe0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1343.000000 -525.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24b6e10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 686.032258 -1030.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="SB_PL"/>
</svg>