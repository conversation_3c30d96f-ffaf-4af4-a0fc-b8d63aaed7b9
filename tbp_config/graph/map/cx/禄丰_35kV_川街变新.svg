<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-100" aopId="3932930" id="thSvg" product="E8000V2" version="1.0" viewBox="3124 -1195 2137 1139">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape25">
    <polyline arcFlag="1" points="26,105 24,105 22,104 21,104 19,103 18,102 16,101 15,99 14,97 14,96 13,94 13,92 13,90 14,88 14,87 15,85 16,84 18,82 19,81 21,80 22,80 24,79 26,79 28,79 30,80 31,80 33,81 34,82 36,84 37,85 38,87 38,88 39,90 39,92 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="39" x2="26" y1="92" y2="92"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="26" x2="26" y1="105" y2="116"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="19" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="60" y2="52"/>
    <polyline arcFlag="1" points="43,19 44,19 45,19 45,19 46,19 46,20 47,20 47,21 48,21 48,22 48,22 48,23 49,24 49,24 49,25 48,26 48,26 48,27 48,27 47,28 47,28 46,29 46,29 45,29 45,30 44,30 43,30 " stroke-width="1"/>
    <polyline arcFlag="1" points="43,41 44,41 45,41 45,42 46,42 46,42 47,43 47,43 48,44 48,44 48,45 48,45 49,46 49,47 49,47 48,48 48,49 48,49 48,50 47,50 47,51 46,51 46,52 45,52 45,52 44,52 43,52 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.423529" x1="26" x2="26" y1="92" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="9" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44164" x1="26" x2="43" y1="13" y2="13"/>
    <rect height="23" stroke-width="0.369608" width="12" x="20" y="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.356919" x1="7" x2="43" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="9" x2="9" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="42" x2="42" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="26" x2="26" y1="19" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="17" x2="33" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="18" x2="33" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="21" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="7" x2="7" y1="60" y2="35"/>
    <rect height="24" stroke-width="0.398039" width="12" x="1" y="29"/>
    <polyline arcFlag="1" points="43,30 44,30 45,30 45,30 46,31 46,31 47,31 47,32 48,32 48,33 48,34 48,34 49,35 49,36 49,36 48,37 48,38 48,38 48,39 47,39 47,40 46,40 46,40 45,41 45,41 44,41 43,41 " stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="59" x2="24" y1="7" y2="7"/>
    <rect height="12" stroke-width="1" width="26" x="18" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="5" x2="5" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="17" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="8" x2="8" y1="12" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape185">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="48" x2="45" y1="55" y2="53"/>
    <ellipse cx="34" cy="41" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="15" x2="15" y1="9" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="21" y1="9" y2="9"/>
    <g class="BV-0KV" stationId="-1">
     <rect height="13" stroke-width="1" width="6" x="12" y="18"/>
    </g>
    <polyline points="27,29 11,21 3,21 1,21 " stroke-width="1"/>
    <polyline points="38,47 37,49 15,49 15,31 " stroke-width="1"/>
    <ellipse cx="45" cy="41" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="45" x2="43" y1="44" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="46" x2="48" y1="44" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="48" x2="43" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="34" x2="34" y1="41" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="31" x2="34" y1="43" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="37" x2="34" y1="43" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="18" x2="12" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="16" x2="14" y1="2" y2="2"/>
    <ellipse cx="34" cy="52" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="45" cy="52" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="32" x2="34" y1="55" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="35" x2="35" y1="53" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="37" x2="34" y1="55" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="46" x2="46" y1="53" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="43" x2="45" y1="55" y2="53"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape45_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="25" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="9" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="25" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="9" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="-15" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-16,39 6,17 6,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="-15" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-16,39 6,17 6,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape37_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="22" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape37_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="21" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
   </symbol>
   <symbol id="switch2:shape37-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <circle cx="10" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="23" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape37-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="23" y1="10" y2="10"/>
    <circle cx="10" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="14" x2="5" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="9" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="5" x2="14" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="4" y1="10" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="13" x2="22" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="4" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="22" x2="13" y1="9" y2="0"/>
   </symbol>
   <symbol id="switch2:shape38-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="2" y1="10" y2="10"/>
    <circle cx="15" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="12" x2="3" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="11" x2="20" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="20" x2="11" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="3" x2="12" y1="19" y2="10"/>
   </symbol>
   <symbol id="switch2:shape38-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="3" x2="12" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="20" x2="11" y1="10" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="11" x2="20" y1="19" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="12" x2="3" y1="10" y2="1"/>
    <circle cx="15" cy="10" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="2" y1="10" y2="10"/>
   </symbol>
   <symbol id="transformer:shape28_0">
    <ellipse cx="69" cy="49" fillStyle="0" rx="25.5" ry="26" stroke-width="0.535277"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="1" stroke-width="1" x1="0" x2="65" y1="50" y2="98"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.505539" x1="71" x2="80" y1="39" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.505539" x1="71" x2="62" y1="39" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.505539" x1="80" x2="62" y1="55" y2="55"/>
   </symbol>
   <symbol id="transformer:shape28_1">
    <ellipse cx="35" cy="31" fillStyle="0" rx="25.5" ry="26.5" stroke-width="0.535277"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.505539" x1="36" x2="45" y1="15" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.505539" x1="36" x2="27" y1="15" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="27" x2="45" y1="31" y2="31"/>
   </symbol>
   <symbol id="transformer:shape28-2">
    <ellipse cx="35" cy="68" fillStyle="0" rx="25.5" ry="26.5" stroke-width="0.524983"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.505539" x1="35" x2="43" y1="76" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.505539" x1="35" x2="27" y1="76" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.505539" x1="35" x2="35" y1="85" y2="76"/>
   </symbol>
   <symbol id="transformer2:shape13_0">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="38" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape13_1">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="46" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="29" y1="34" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape25_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="58" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape25_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="12,76 19,76 16,83 12,76 "/>
   </symbol>
   <symbol id="transformer2:shape35_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,26 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="38" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="16" y1="51" y2="26"/>
    <polyline DF8003:Layer="PUBLIC" points="16,14 10,26 22,26 16,14 16,15 16,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="56" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="51"/>
   </symbol>
   <symbol id="transformer2:shape35_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="82" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="81" y2="76"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bc1a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bc2380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1bc2d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1bc3a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1bc4c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1bc5820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bc63d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bc6ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bc7330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bc7cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bc7cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bc9760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bc9760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1bca400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bcc0b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1bccd00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1bcdbe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1bce4c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bcfc80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bd0480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bd0b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1bd1300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bd23e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bd2dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bd38b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1bd4270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1bd5810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1bd62d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1bd7540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1bd81d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1be69d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1bd9af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1bdab50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1bdc0a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1149" width="2147" x="3119" y="-1200"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="3693" x2="3724" y1="-283" y2="-283"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4992" x2="5023" y1="-281" y2="-281"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="5252" x2="5261" y1="-665" y2="-665"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="320" stroke="rgb(213,0,0)" stroke-width="1" width="275" x="4625" y="-1099"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="3481" y="-1182"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-53199">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4243.000000 -921.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17506" ObjectName="SW-LF_CJ.LF_CJ_3311SW"/>
     <cge:Meas_Ref ObjectId="53199"/>
    <cge:TPSR_Ref TObjectID="17506"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53198">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4243.000000 -1017.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17507" ObjectName="SW-LF_CJ.LF_CJ_3316SW"/>
     <cge:Meas_Ref ObjectId="53198"/>
    <cge:TPSR_Ref TObjectID="17507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53197">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4263.000000 -1069.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17508" ObjectName="SW-LF_CJ.LF_CJ_33167SW"/>
     <cge:Meas_Ref ObjectId="53197"/>
    <cge:TPSR_Ref TObjectID="17508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53242">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4040.000000 -931.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9303" ObjectName="SW-CX_CJ.CX_CJ_3901SW"/>
     <cge:Meas_Ref ObjectId="53242"/>
    <cge:TPSR_Ref TObjectID="9303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53243">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4085.000000 -946.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9304" ObjectName="SW-CX_CJ.CX_CJ_39017SW"/>
     <cge:Meas_Ref ObjectId="53243"/>
    <cge:TPSR_Ref TObjectID="9304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53223">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3854.000000 -849.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9300" ObjectName="SW-LF_CJ.LF_CJ_3011SW"/>
     <cge:Meas_Ref ObjectId="53223"/>
    <cge:TPSR_Ref TObjectID="9300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235047">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3853.139241 -566.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39279" ObjectName="SW-LF_CJ.LF_CJ_001XC1"/>
     <cge:Meas_Ref ObjectId="235047"/>
    <cge:TPSR_Ref TObjectID="39279"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235047">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3853.139241 -640.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39281" ObjectName="SW-LF_CJ.LF_CJ_001XC"/>
     <cge:Meas_Ref ObjectId="235047"/>
    <cge:TPSR_Ref TObjectID="39281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234984">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3523.139241 -476.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39268" ObjectName="SW-LF_CJ.LF_CJ_0901XC"/>
     <cge:Meas_Ref ObjectId="234984"/>
    <cge:TPSR_Ref TObjectID="39268"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234984">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3523.139241 -391.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39275" ObjectName="SW-LF_CJ.LF_CJ_0901XC1"/>
     <cge:Meas_Ref ObjectId="234984"/>
    <cge:TPSR_Ref TObjectID="39275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235293">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3717.139241 -482.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39282" ObjectName="SW-LF_CJ.LF_CJ_031XC"/>
     <cge:Meas_Ref ObjectId="235293"/>
    <cge:TPSR_Ref TObjectID="39282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235293">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3717.139241 -408.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39283" ObjectName="SW-LF_CJ.LF_CJ_031XC1"/>
     <cge:Meas_Ref ObjectId="235293"/>
    <cge:TPSR_Ref TObjectID="39283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235295">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3739.000000 -387.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39241" ObjectName="SW-LF_CJ.LF_CJ_03160SW"/>
     <cge:Meas_Ref ObjectId="235295"/>
    <cge:TPSR_Ref TObjectID="39241"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235296">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3664.000000 -261.000000)" xlink:href="#switch2:shape45_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39242" ObjectName="SW-LF_CJ.LF_CJ_03167SW"/>
     <cge:Meas_Ref ObjectId="235296"/>
    <cge:TPSR_Ref TObjectID="39242"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235294">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3718.000000 -262.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39240" ObjectName="SW-LF_CJ.LF_CJ_0316SW"/>
     <cge:Meas_Ref ObjectId="235294"/>
    <cge:TPSR_Ref TObjectID="39240"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235149">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3902.139241 -482.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9290" ObjectName="SW-LF_CJ.LF_CJ_032XC"/>
     <cge:Meas_Ref ObjectId="235149"/>
    <cge:TPSR_Ref TObjectID="9290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235149">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3902.139241 -408.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9291" ObjectName="SW-LF_CJ.LF_CJ_032XC1"/>
     <cge:Meas_Ref ObjectId="235149"/>
    <cge:TPSR_Ref TObjectID="9291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235151">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3924.000000 -387.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39230" ObjectName="SW-LF_CJ.LF_CJ_03260SW"/>
     <cge:Meas_Ref ObjectId="235151"/>
    <cge:TPSR_Ref TObjectID="39230"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235150">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3903.000000 -269.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39229" ObjectName="SW-LF_CJ.LF_CJ_0326SW"/>
     <cge:Meas_Ref ObjectId="235150"/>
    <cge:TPSR_Ref TObjectID="39229"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235337">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4194.139241 -480.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39286" ObjectName="SW-LF_CJ.LF_CJ_012XC"/>
     <cge:Meas_Ref ObjectId="235337"/>
    <cge:TPSR_Ref TObjectID="39286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235337">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4194.139241 -405.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39287" ObjectName="SW-LF_CJ.LF_CJ_012XC1"/>
     <cge:Meas_Ref ObjectId="235337"/>
    <cge:TPSR_Ref TObjectID="39287"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235338">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4303.139241 -427.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39289" ObjectName="SW-LF_CJ.LF_CJ_0122XC1"/>
     <cge:Meas_Ref ObjectId="235338"/>
    <cge:TPSR_Ref TObjectID="39289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235338">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4303.139241 -479.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39288" ObjectName="SW-LF_CJ.LF_CJ_0122XC"/>
     <cge:Meas_Ref ObjectId="235338"/>
    <cge:TPSR_Ref TObjectID="39288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235120">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4066.139241 -408.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9294" ObjectName="SW-LF_CJ.LF_CJ_033XC1"/>
     <cge:Meas_Ref ObjectId="235120"/>
    <cge:TPSR_Ref TObjectID="9294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235122">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4088.000000 -387.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39228" ObjectName="SW-LF_CJ.LF_CJ_03360SW"/>
     <cge:Meas_Ref ObjectId="235122"/>
    <cge:TPSR_Ref TObjectID="39228"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235121">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4067.000000 -269.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39227" ObjectName="SW-LF_CJ.LF_CJ_0336SW"/>
     <cge:Meas_Ref ObjectId="235121"/>
    <cge:TPSR_Ref TObjectID="39227"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235120">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4066.139241 -482.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9293" ObjectName="SW-LF_CJ.LF_CJ_033XC"/>
     <cge:Meas_Ref ObjectId="235120"/>
    <cge:TPSR_Ref TObjectID="9293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235178">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4380.139241 -408.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9282" ObjectName="SW-LF_CJ.LF_CJ_041XC1"/>
     <cge:Meas_Ref ObjectId="235178"/>
    <cge:TPSR_Ref TObjectID="9282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235180">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4402.000000 -387.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39232" ObjectName="SW-LF_CJ.LF_CJ_04160SW"/>
     <cge:Meas_Ref ObjectId="235180"/>
    <cge:TPSR_Ref TObjectID="39232"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235179">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4381.000000 -269.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39231" ObjectName="SW-LF_CJ.LF_CJ_0416SW"/>
     <cge:Meas_Ref ObjectId="235179"/>
    <cge:TPSR_Ref TObjectID="39231"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235178">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4380.139241 -482.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9281" ObjectName="SW-LF_CJ.LF_CJ_041XC"/>
     <cge:Meas_Ref ObjectId="235178"/>
    <cge:TPSR_Ref TObjectID="9281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235206">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4535.139241 -407.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9279" ObjectName="SW-LF_CJ.LF_CJ_042XC1"/>
     <cge:Meas_Ref ObjectId="235206"/>
    <cge:TPSR_Ref TObjectID="9279"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235208">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4557.000000 -386.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39234" ObjectName="SW-LF_CJ.LF_CJ_04260SW"/>
     <cge:Meas_Ref ObjectId="235208"/>
    <cge:TPSR_Ref TObjectID="39234"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235207">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4536.000000 -268.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39233" ObjectName="SW-LF_CJ.LF_CJ_0426SW"/>
     <cge:Meas_Ref ObjectId="235207"/>
    <cge:TPSR_Ref TObjectID="39233"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235206">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4535.139241 -481.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9278" ObjectName="SW-LF_CJ.LF_CJ_042XC"/>
     <cge:Meas_Ref ObjectId="235206"/>
    <cge:TPSR_Ref TObjectID="9278"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235235">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4687.139241 -406.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9288" ObjectName="SW-LF_CJ.LF_CJ_043XC1"/>
     <cge:Meas_Ref ObjectId="235235"/>
    <cge:TPSR_Ref TObjectID="9288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235237">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4709.000000 -385.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39236" ObjectName="SW-LF_CJ.LF_CJ_04360SW"/>
     <cge:Meas_Ref ObjectId="235237"/>
    <cge:TPSR_Ref TObjectID="39236"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235236">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4688.000000 -267.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39235" ObjectName="SW-LF_CJ.LF_CJ_0436SW"/>
     <cge:Meas_Ref ObjectId="235236"/>
    <cge:TPSR_Ref TObjectID="39235"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235235">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4687.139241 -480.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9287" ObjectName="SW-LF_CJ.LF_CJ_043XC"/>
     <cge:Meas_Ref ObjectId="235235"/>
    <cge:TPSR_Ref TObjectID="9287"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235266">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4863.000000 -385.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39238" ObjectName="SW-LF_CJ.LF_CJ_04460SW"/>
     <cge:Meas_Ref ObjectId="235266"/>
    <cge:TPSR_Ref TObjectID="39238"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235265">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4842.000000 -267.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39237" ObjectName="SW-LF_CJ.LF_CJ_0446SW"/>
     <cge:Meas_Ref ObjectId="235265"/>
    <cge:TPSR_Ref TObjectID="39237"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235264">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4841.139241 -480.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9284" ObjectName="SW-LF_CJ.LF_CJ_044XC"/>
     <cge:Meas_Ref ObjectId="235264"/>
    <cge:TPSR_Ref TObjectID="9284"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235264">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4841.139241 -406.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9285" ObjectName="SW-LF_CJ.LF_CJ_044XC1"/>
     <cge:Meas_Ref ObjectId="235264"/>
    <cge:TPSR_Ref TObjectID="9285"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235315">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5016.139241 -480.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39284" ObjectName="SW-LF_CJ.LF_CJ_045XC"/>
     <cge:Meas_Ref ObjectId="235315"/>
    <cge:TPSR_Ref TObjectID="39284"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235315">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5016.139241 -406.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39285" ObjectName="SW-LF_CJ.LF_CJ_045XC1"/>
     <cge:Meas_Ref ObjectId="235315"/>
    <cge:TPSR_Ref TObjectID="39285"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235317">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5038.000000 -385.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39245" ObjectName="SW-LF_CJ.LF_CJ_04560SW"/>
     <cge:Meas_Ref ObjectId="235317"/>
    <cge:TPSR_Ref TObjectID="39245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235318">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4963.000000 -259.000000)" xlink:href="#switch2:shape45_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39246" ObjectName="SW-LF_CJ.LF_CJ_04567SW"/>
     <cge:Meas_Ref ObjectId="235318"/>
    <cge:TPSR_Ref TObjectID="39246"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235316">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5017.000000 -260.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39244" ObjectName="SW-LF_CJ.LF_CJ_0456SW"/>
     <cge:Meas_Ref ObjectId="235316"/>
    <cge:TPSR_Ref TObjectID="39244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234992">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5141.139241 -473.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39294" ObjectName="SW-LF_CJ.LF_CJ_0461XC"/>
     <cge:Meas_Ref ObjectId="234992"/>
    <cge:TPSR_Ref TObjectID="39294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234992">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5141.139241 -398.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39295" ObjectName="SW-LF_CJ.LF_CJ_0461XC1"/>
     <cge:Meas_Ref ObjectId="234992"/>
    <cge:TPSR_Ref TObjectID="39295"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235111">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4356.139241 -545.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39280" ObjectName="SW-LF_CJ.LF_CJ_002XC1"/>
     <cge:Meas_Ref ObjectId="235111"/>
    <cge:TPSR_Ref TObjectID="39280"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235111">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4356.139241 -619.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39278" ObjectName="SW-LF_CJ.LF_CJ_002XC"/>
     <cge:Meas_Ref ObjectId="235111"/>
    <cge:TPSR_Ref TObjectID="39278"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235102">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4357.000000 -850.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39224" ObjectName="SW-LF_CJ.LF_CJ_3021SW"/>
     <cge:Meas_Ref ObjectId="235102"/>
    <cge:TPSR_Ref TObjectID="39224"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235103">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4379.000000 -842.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39225" ObjectName="SW-LF_CJ.LF_CJ_30217SW"/>
     <cge:Meas_Ref ObjectId="235103"/>
    <cge:TPSR_Ref TObjectID="39225"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4178.000000 -836.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234989">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4914.139241 -556.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39291" ObjectName="SW-LF_CJ.LF_CJ_0902XC"/>
     <cge:Meas_Ref ObjectId="234989"/>
    <cge:TPSR_Ref TObjectID="39291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234989">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4913.139241 -637.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39292" ObjectName="SW-LF_CJ.LF_CJ_0902XC1"/>
     <cge:Meas_Ref ObjectId="234989"/>
    <cge:TPSR_Ref TObjectID="39292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235503">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4468.000000 -708.000000)" xlink:href="#switch2:shape37_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39276" ObjectName="SW-LF_CJ.LF_CJ_602XC"/>
     <cge:Meas_Ref ObjectId="235503"/>
    <cge:TPSR_Ref TObjectID="39276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235503">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4537.000000 -709.000000)" xlink:href="#switch2:shape38_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39277" ObjectName="SW-LF_CJ.LF_CJ_602XC1"/>
     <cge:Meas_Ref ObjectId="235503"/>
    <cge:TPSR_Ref TObjectID="39277"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4764.000000 -910.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4764.000000 -783.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-LF_CJ.LF_CJ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-906 4507,-906 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9274" ObjectName="BS-LF_CJ.LF_CJ_3IM"/>
    <cge:TPSR_Ref TObjectID="9274"/></metadata>
   <polyline fill="none" opacity="0" points="3737,-906 4507,-906 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_CJ.LF_CJ_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3488,-530 4228,-530 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9275" ObjectName="BS-LF_CJ.LF_CJ_9IM"/>
    <cge:TPSR_Ref TObjectID="9275"/></metadata>
   <polyline fill="none" opacity="0" points="3488,-530 4228,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_CJ.LF_CJ_9ⅡM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4292,-530 5219,-530 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="39221" ObjectName="BS-LF_CJ.LF_CJ_9ⅡM"/>
    <cge:TPSR_Ref TObjectID="39221"/></metadata>
   <polyline fill="none" opacity="0" points="4292,-530 5219,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4677,-980 4863,-980 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4677,-980 4863,-980 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-LF_CJ.CX_CJ_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3701.000000 -131.000000)" xlink:href="#capacitor:shape25"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39296" ObjectName="CB-LF_CJ.CX_CJ_Cb1"/>
    <cge:TPSR_Ref TObjectID="39296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-LF_CJ.CX_CJ_Cb2">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5000.000000 -129.000000)" xlink:href="#capacitor:shape25"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39297" ObjectName="CB-LF_CJ.CX_CJ_Cb2"/>
    <cge:TPSR_Ref TObjectID="39297"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-LF_CJ.LF_CJ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="24330"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.942460 -0.000000 0.000000 -1.000000 3827.000000 -691.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.942460 -0.000000 0.000000 -1.000000 3827.000000 -691.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="17700" ObjectName="TF-LF_CJ.LF_CJ_1T"/>
    <cge:TPSR_Ref TObjectID="17700"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5136.000000 -232.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5136.000000 -232.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4167.000000 -720.000000)" xlink:href="#transformer2:shape35_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4167.000000 -720.000000)" xlink:href="#transformer2:shape35_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_13cb4e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4177.673667 -1092.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_131f460">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4040.000000 -1022.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1320840">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4088.000000 -1024.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13f5c90">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4083.000000 -1139.000000)" xlink:href="#lightningRod:shape185"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13d1600">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 3567.000000 -290.000000)" xlink:href="#lightningRod:shape185"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_140a4b0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3822.000000 -609.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_140bd50">
    <use class="BV-10KV" transform="matrix(0.722222 -0.000000 0.000000 -0.809524 3527.000000 -432.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13fb190">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3492.000000 -399.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13eded0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3673.000000 -319.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_138fef0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 3876.000000 -318.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1372070">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3953.000000 -189.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13bd300">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4040.000000 -318.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13c0b10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4117.000000 -189.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13a0e80">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4354.000000 -318.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13b60e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4431.000000 -189.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13787f0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4509.000000 -317.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13170a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4586.000000 -188.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13634b0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4661.000000 -316.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1366cc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4738.000000 -187.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_130a260">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4815.000000 -316.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12dd760">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4892.000000 -187.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1493e80">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4972.000000 -317.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1274150">
    <use class="BV-10KV" transform="matrix(0.722222 -0.000000 0.000000 -0.809524 5145.000000 -436.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_127a520">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5192.000000 -318.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12c5e70">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4325.000000 -588.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12da1e0">
    <use class="BV-10KV" transform="matrix(0.722222 -0.000000 0.000000 -0.809524 4918.000000 -602.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12dae10">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4957.000000 -758.000000)" xlink:href="#lightningRod:shape185"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15c47f0">
    <use class="BV-6KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 4686.500000 -764.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15c55a0">
    <use class="BV-6KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4733.000000 -650.000000)" xlink:href="#lightningRod:shape185"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15c8220">
    <use class="BV-6KV" transform="matrix(0.000000 -0.722222 -0.809524 -0.000000 4662.500000 -677.500000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19d50b0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4885.000000 -533.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3175.000000 -1099.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-80988" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="32" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3258.000000 -949.000000) translate(0,26)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80988" ObjectName="LF_CJ:LF_CJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-80989" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="32" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3258.000000 -908.000000) translate(0,26)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80989" ObjectName="LF_CJ:LF_CJ_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-80988" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="32" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3257.000000 -1031.000000) translate(0,26)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80988" ObjectName="LF_CJ:LF_CJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-80988" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="32" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3258.000000 -992.000000) translate(0,26)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80988" ObjectName="LF_CJ:LF_CJ_sumP"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-234917" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5032.000000 -87.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234917" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39243"/>
     <cge:Term_Ref ObjectID="58920"/>
    <cge:TPSR_Ref TObjectID="39243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-234912" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5032.000000 -87.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234912" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39243"/>
     <cge:Term_Ref ObjectID="58920"/>
    <cge:TPSR_Ref TObjectID="39243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-234911" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3724.000000 -86.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234911" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39239"/>
     <cge:Term_Ref ObjectID="58912"/>
    <cge:TPSR_Ref TObjectID="39239"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-234906" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3724.000000 -86.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234906" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39239"/>
     <cge:Term_Ref ObjectID="58912"/>
    <cge:TPSR_Ref TObjectID="39239"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-234924" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3779.000000 -1020.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234924" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9274"/>
     <cge:Term_Ref ObjectID="13352"/>
    <cge:TPSR_Ref TObjectID="9274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-234925" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3779.000000 -1020.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234925" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9274"/>
     <cge:Term_Ref ObjectID="13352"/>
    <cge:TPSR_Ref TObjectID="9274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-234926" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3779.000000 -1020.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234926" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9274"/>
     <cge:Term_Ref ObjectID="13352"/>
    <cge:TPSR_Ref TObjectID="9274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-234930" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3779.000000 -1020.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234930" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9274"/>
     <cge:Term_Ref ObjectID="13352"/>
    <cge:TPSR_Ref TObjectID="9274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-234927" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3779.000000 -1020.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234927" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9274"/>
     <cge:Term_Ref ObjectID="13352"/>
    <cge:TPSR_Ref TObjectID="9274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-234931" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3779.000000 -1020.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234931" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9274"/>
     <cge:Term_Ref ObjectID="13352"/>
    <cge:TPSR_Ref TObjectID="9274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-53089" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4373.000000 -1011.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53089" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17504"/>
     <cge:Term_Ref ObjectID="16337"/>
    <cge:TPSR_Ref TObjectID="17504"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-53090" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4373.000000 -1011.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53090" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17504"/>
     <cge:Term_Ref ObjectID="16337"/>
    <cge:TPSR_Ref TObjectID="17504"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-53087" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4373.000000 -1011.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53087" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17504"/>
     <cge:Term_Ref ObjectID="16337"/>
    <cge:TPSR_Ref TObjectID="17504"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-235528" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3979.000000 -640.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="235528" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39222"/>
     <cge:Term_Ref ObjectID="58878"/>
    <cge:TPSR_Ref TObjectID="39222"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-234850" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3979.000000 -640.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234850" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39222"/>
     <cge:Term_Ref ObjectID="58878"/>
    <cge:TPSR_Ref TObjectID="39222"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-234847" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3979.000000 -640.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234847" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39222"/>
     <cge:Term_Ref ObjectID="58878"/>
    <cge:TPSR_Ref TObjectID="39222"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-235527" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3979.000000 -640.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="235527" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39222"/>
     <cge:Term_Ref ObjectID="58878"/>
    <cge:TPSR_Ref TObjectID="39222"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-234856" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4564.000000 -878.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234856" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39223"/>
     <cge:Term_Ref ObjectID="58880"/>
    <cge:TPSR_Ref TObjectID="39223"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-234857" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4564.000000 -878.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234857" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39223"/>
     <cge:Term_Ref ObjectID="58880"/>
    <cge:TPSR_Ref TObjectID="39223"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-235531" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4564.000000 -878.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="235531" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39223"/>
     <cge:Term_Ref ObjectID="58880"/>
    <cge:TPSR_Ref TObjectID="39223"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-234855" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4564.000000 -878.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234855" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39223"/>
     <cge:Term_Ref ObjectID="58880"/>
    <cge:TPSR_Ref TObjectID="39223"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-234864" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4497.000000 -630.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234864" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39226"/>
     <cge:Term_Ref ObjectID="58886"/>
    <cge:TPSR_Ref TObjectID="39226"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-235550" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4497.000000 -630.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="235550" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39226"/>
     <cge:Term_Ref ObjectID="58886"/>
    <cge:TPSR_Ref TObjectID="39226"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-235547" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4497.000000 -630.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="235547" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39226"/>
     <cge:Term_Ref ObjectID="58886"/>
    <cge:TPSR_Ref TObjectID="39226"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-235549" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4497.000000 -630.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="235549" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39226"/>
     <cge:Term_Ref ObjectID="58886"/>
    <cge:TPSR_Ref TObjectID="39226"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-235555" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3895.000000 -141.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="235555" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9289"/>
     <cge:Term_Ref ObjectID="13379"/>
    <cge:TPSR_Ref TObjectID="9289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-234876" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3895.000000 -141.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234876" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9289"/>
     <cge:Term_Ref ObjectID="13379"/>
    <cge:TPSR_Ref TObjectID="9289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-234872" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3895.000000 -141.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234872" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9289"/>
     <cge:Term_Ref ObjectID="13379"/>
    <cge:TPSR_Ref TObjectID="9289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-234880" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4090.000000 -141.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234880" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9292"/>
     <cge:Term_Ref ObjectID="13385"/>
    <cge:TPSR_Ref TObjectID="9292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-234881" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4090.000000 -141.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234881" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9292"/>
     <cge:Term_Ref ObjectID="13385"/>
    <cge:TPSR_Ref TObjectID="9292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-234877" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4090.000000 -141.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234877" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9292"/>
     <cge:Term_Ref ObjectID="13385"/>
    <cge:TPSR_Ref TObjectID="9292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-234886" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4400.000000 -143.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234886" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9280"/>
     <cge:Term_Ref ObjectID="13361"/>
    <cge:TPSR_Ref TObjectID="9280"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-234887" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4400.000000 -143.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234887" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9280"/>
     <cge:Term_Ref ObjectID="13361"/>
    <cge:TPSR_Ref TObjectID="9280"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-234882" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4400.000000 -143.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234882" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9280"/>
     <cge:Term_Ref ObjectID="13361"/>
    <cge:TPSR_Ref TObjectID="9280"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-234892" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4552.000000 -142.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234892" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9277"/>
     <cge:Term_Ref ObjectID="13355"/>
    <cge:TPSR_Ref TObjectID="9277"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-234893" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4552.000000 -142.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234893" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9277"/>
     <cge:Term_Ref ObjectID="13355"/>
    <cge:TPSR_Ref TObjectID="9277"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-234888" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4552.000000 -142.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234888" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9277"/>
     <cge:Term_Ref ObjectID="13355"/>
    <cge:TPSR_Ref TObjectID="9277"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-234898" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4699.000000 -141.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234898" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9286"/>
     <cge:Term_Ref ObjectID="13373"/>
    <cge:TPSR_Ref TObjectID="9286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-234899" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4699.000000 -141.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234899" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9286"/>
     <cge:Term_Ref ObjectID="13373"/>
    <cge:TPSR_Ref TObjectID="9286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-234894" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4699.000000 -141.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234894" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9286"/>
     <cge:Term_Ref ObjectID="13373"/>
    <cge:TPSR_Ref TObjectID="9286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-234904" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4855.000000 -142.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234904" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9283"/>
     <cge:Term_Ref ObjectID="13367"/>
    <cge:TPSR_Ref TObjectID="9283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-234905" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4855.000000 -142.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234905" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9283"/>
     <cge:Term_Ref ObjectID="13367"/>
    <cge:TPSR_Ref TObjectID="9283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-234900" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4855.000000 -142.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234900" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9283"/>
     <cge:Term_Ref ObjectID="13367"/>
    <cge:TPSR_Ref TObjectID="9283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-234940" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5201.000000 -636.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234940" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39221"/>
     <cge:Term_Ref ObjectID="58877"/>
    <cge:TPSR_Ref TObjectID="39221"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-234941" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5201.000000 -636.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234941" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39221"/>
     <cge:Term_Ref ObjectID="58877"/>
    <cge:TPSR_Ref TObjectID="39221"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-234942" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5201.000000 -636.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234942" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39221"/>
     <cge:Term_Ref ObjectID="58877"/>
    <cge:TPSR_Ref TObjectID="39221"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-234946" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5201.000000 -636.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234946" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39221"/>
     <cge:Term_Ref ObjectID="58877"/>
    <cge:TPSR_Ref TObjectID="39221"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-234943" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5201.000000 -636.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234943" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39221"/>
     <cge:Term_Ref ObjectID="58877"/>
    <cge:TPSR_Ref TObjectID="39221"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-234947" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5201.000000 -636.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234947" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39221"/>
     <cge:Term_Ref ObjectID="58877"/>
    <cge:TPSR_Ref TObjectID="39221"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-234932" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3497.000000 -633.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234932" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9275"/>
     <cge:Term_Ref ObjectID="13353"/>
    <cge:TPSR_Ref TObjectID="9275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-234933" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3497.000000 -633.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234933" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9275"/>
     <cge:Term_Ref ObjectID="13353"/>
    <cge:TPSR_Ref TObjectID="9275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-234934" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3497.000000 -633.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234934" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9275"/>
     <cge:Term_Ref ObjectID="13353"/>
    <cge:TPSR_Ref TObjectID="9275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-234938" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3497.000000 -633.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234938" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9275"/>
     <cge:Term_Ref ObjectID="13353"/>
    <cge:TPSR_Ref TObjectID="9275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-234935" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3497.000000 -633.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234935" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9275"/>
     <cge:Term_Ref ObjectID="13353"/>
    <cge:TPSR_Ref TObjectID="9275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-234939" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3497.000000 -633.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234939" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9275"/>
     <cge:Term_Ref ObjectID="13353"/>
    <cge:TPSR_Ref TObjectID="9275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-234852" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4066.000000 -763.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234852" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17700"/>
     <cge:Term_Ref ObjectID="24331"/>
    <cge:TPSR_Ref TObjectID="17700"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-234865" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4066.000000 -763.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234865" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17700"/>
     <cge:Term_Ref ObjectID="24331"/>
    <cge:TPSR_Ref TObjectID="17700"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-235560" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4475.000000 -816.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="235560" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39248"/>
     <cge:Term_Ref ObjectID="58982"/>
    <cge:TPSR_Ref TObjectID="39248"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-235559" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4475.000000 -816.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="235559" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39248"/>
     <cge:Term_Ref ObjectID="58982"/>
    <cge:TPSR_Ref TObjectID="39248"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-235542" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4516.000000 -697.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="235542" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39250"/>
     <cge:Term_Ref ObjectID="58934"/>
    <cge:TPSR_Ref TObjectID="39250"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-235543" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4516.000000 -697.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="235543" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39250"/>
     <cge:Term_Ref ObjectID="58934"/>
    <cge:TPSR_Ref TObjectID="39250"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-235536" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4516.000000 -697.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="235536" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39250"/>
     <cge:Term_Ref ObjectID="58934"/>
    <cge:TPSR_Ref TObjectID="39250"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-53100" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3982.000000 -848.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53100" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9299"/>
     <cge:Term_Ref ObjectID="13399"/>
    <cge:TPSR_Ref TObjectID="9299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-53101" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3982.000000 -848.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53101" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9299"/>
     <cge:Term_Ref ObjectID="13399"/>
    <cge:TPSR_Ref TObjectID="9299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-53097" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3982.000000 -848.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53097" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9299"/>
     <cge:Term_Ref ObjectID="13399"/>
    <cge:TPSR_Ref TObjectID="9299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-79889" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3982.000000 -848.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79889" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9299"/>
     <cge:Term_Ref ObjectID="13399"/>
    <cge:TPSR_Ref TObjectID="9299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-234918" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4243.000000 -377.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234918" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39247"/>
     <cge:Term_Ref ObjectID="58928"/>
    <cge:TPSR_Ref TObjectID="39247"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3192" y="-1178"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3192" y="-1178"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3143" y="-1195"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3143" y="-1195"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4263" y="-995"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4263" y="-995"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="70" x="3902" y="-746"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="70" x="3902" y="-746"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3346" y="-1157"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3346" y="-1157"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3346" y="-1192"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3346" y="-1192"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="62" x="4263" y="-743"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="62" x="4263" y="-743"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3739" y="-468"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3739" y="-468"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3923" y="-468"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3923" y="-468"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4088" y="-468"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4088" y="-468"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4215" y="-465"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4215" y="-465"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4403" y="-468"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4403" y="-468"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4557" y="-467"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4557" y="-467"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4708" y="-466"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4708" y="-466"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4863" y="-466"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4863" y="-466"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5038" y="-466"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5038" y="-466"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="3480" y="-1183"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="3480" y="-1183"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="100" x="3166" y="-821"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="100" x="3166" y="-821"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3192" y="-1178"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3143" y="-1195"/></g>
   <g href="35kV川街变35kV川街支线331间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4263" y="-995"/></g>
   <g href="35kV川街变1号主变间隔接线图.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="70" x="3902" y="-746"/></g>
   <g href="cx_配调_配网接线图35_禄丰.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3346" y="-1157"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3346" y="-1192"/></g>
   <g href="35kV川街变2号主变间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="62" x="4263" y="-743"/></g>
   <g href="35kV川街变10kV_1号电容器组031间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3739" y="-468"/></g>
   <g href="35kV川街变10kV九渡线032间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3923" y="-468"/></g>
   <g href="35kV川街变10kV备用线033间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4088" y="-468"/></g>
   <g href="35kV川街变10kV分段012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4215" y="-465"/></g>
   <g href="35kV川街变10kV恐龙谷专线041间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4403" y="-468"/></g>
   <g href="35kV川街变10kV长田线042间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4557" y="-467"/></g>
   <g href="35kV川街变10kV大村线043间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4708" y="-466"/></g>
   <g href="35kV川街变10kV街区线044间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4863" y="-466"/></g>
   <g href="35kV川街变10kV_2号电容器组045间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5038" y="-466"/></g>
   <g href="AVC川街站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="3480" y="-1183"/></g>
   <g href="35kV川街变GG虚设备间隔接线图_0.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="100" x="3166" y="-821"/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3727,-352 3722,-342 3732,-342 3727,-352 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3727,-364 3722,-374 3732,-374 3727,-364 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3912,-352 3907,-342 3917,-342 3912,-352 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3912,-364 3907,-374 3917,-374 3912,-364 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4076,-352 4071,-342 4081,-342 4076,-352 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4076,-364 4071,-374 4081,-374 4076,-364 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4390,-352 4385,-342 4395,-342 4390,-352 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4390,-364 4385,-374 4395,-374 4390,-364 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4545,-351 4540,-341 4550,-341 4545,-351 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4545,-363 4540,-373 4550,-373 4545,-363 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4697,-350 4692,-340 4702,-340 4697,-350 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4697,-362 4692,-372 4702,-372 4697,-362 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4851,-362 4846,-372 4856,-372 4851,-362 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4851,-350 4846,-340 4856,-340 4851,-350 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5026,-350 5021,-340 5031,-340 5026,-350 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5026,-362 5021,-372 5031,-372 5026,-362 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5151,-364 5146,-374 5156,-374 5151,-364 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5151,-352 5146,-342 5156,-342 5151,-352 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4366,-794 4361,-804 4371,-804 4366,-794 " stroke="rgb(238,238,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4366,-794 4361,-784 4371,-784 4366,-794 " stroke="rgb(238,238,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4656,-719 4666,-714 4666,-724 4656,-719 " stroke="rgb(238,238,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4646,-719 4636,-714 4636,-724 4646,-719 " stroke="rgb(238,238,0)" stroke-width="1"/>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-LF_CJ.LF_CJ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="58931"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4330.000000 -670.000000)" xlink:href="#transformer:shape28_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="58933"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4330.000000 -670.000000)" xlink:href="#transformer:shape28_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="58983"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4330.000000 -670.000000)" xlink:href="#transformer:shape28-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="39248" ObjectName="TF-LF_CJ.LF_CJ_2T"/>
    <cge:TPSR_Ref TObjectID="39248"/></metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-LF_CJ.LD_CJ_032">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3903.000000 -194.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18150" ObjectName="EC-LF_CJ.LD_CJ_032"/>
    <cge:TPSR_Ref TObjectID="18150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4067.000000 -194.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_CJ.LD_CJ_041">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4381.000000 -194.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18145" ObjectName="EC-LF_CJ.LD_CJ_041"/>
    <cge:TPSR_Ref TObjectID="18145"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_CJ.LD_CJ_042">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4536.000000 -193.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18149" ObjectName="EC-LF_CJ.LD_CJ_042"/>
    <cge:TPSR_Ref TObjectID="18149"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_CJ.LD_CJ_043">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4688.000000 -192.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18146" ObjectName="EC-LF_CJ.LD_CJ_043"/>
    <cge:TPSR_Ref TObjectID="18146"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_CJ.LD_CJ_044">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4842.000000 -192.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18148" ObjectName="EC-LF_CJ.LD_CJ_044"/>
    <cge:TPSR_Ref TObjectID="18148"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_13057d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4252,-906 4252,-926 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9274@0" ObjectIDZND0="17506@0" Pin0InfoVect0LinkObjId="SW-53199_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_138dbf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4252,-906 4252,-926 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12fc8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4252,-962 4252,-977 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="17506@1" ObjectIDZND0="17504@0" Pin0InfoVect0LinkObjId="SW-53195_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53199_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4252,-962 4252,-977 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1342580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4252,-1004 4252,-1022 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17504@1" ObjectIDZND0="17507@0" Pin0InfoVect0LinkObjId="SW-53198_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53195_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4252,-1004 4252,-1022 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13cb2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4304,-1074 4317,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="17508@1" ObjectIDZND0="g_12e7df0@0" Pin0InfoVect0LinkObjId="g_12e7df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53197_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4304,-1074 4317,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1460a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4049,-906 4049,-936 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9274@0" ObjectIDZND0="9303@0" Pin0InfoVect0LinkObjId="SW-53242_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_138dbf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4049,-906 4049,-936 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1460c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4049,-972 4049,-998 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="9303@1" ObjectIDZND0="g_1320840@0" ObjectIDZND1="g_131f460@0" ObjectIDZND2="9304@x" Pin0InfoVect0LinkObjId="g_1320840_0" Pin0InfoVect1LinkObjId="g_131f460_0" Pin0InfoVect2LinkObjId="SW-53243_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53242_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4049,-972 4049,-998 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_131e990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4049,-998 4094,-998 4094,-987 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1320840@0" ObjectIDND1="g_131f460@0" ObjectIDND2="9303@x" ObjectIDZND0="9304@1" Pin0InfoVect0LinkObjId="SW-53243_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1320840_0" Pin1InfoVect1LinkObjId="g_131f460_0" Pin1InfoVect2LinkObjId="SW-53242_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4049,-998 4094,-998 4094,-987 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_131eb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4094,-951 4094,-938 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9304@0" ObjectIDZND0="g_12e85a0@0" Pin0InfoVect0LinkObjId="g_12e85a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53243_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4094,-951 4094,-938 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_131f990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4049,-1058 4049,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_131f460@1" ObjectIDZND0="g_13f5c90@0" Pin0InfoVect0LinkObjId="g_13f5c90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_131f460_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4049,-1058 4049,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_131fb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4049,-1016 4095,-1016 4095,-1029 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9303@x" ObjectIDND1="9304@x" ObjectIDND2="g_131f460@0" ObjectIDZND0="g_1320840@0" Pin0InfoVect0LinkObjId="g_1320840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-53242_0" Pin1InfoVect1LinkObjId="SW-53243_0" Pin1InfoVect2LinkObjId="g_131f460_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4049,-1016 4095,-1016 4095,-1029 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1320460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4049,-998 4049,-1016 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="9303@x" ObjectIDND1="9304@x" ObjectIDZND0="g_1320840@0" ObjectIDZND1="g_131f460@0" Pin0InfoVect0LinkObjId="g_1320840_0" Pin0InfoVect1LinkObjId="g_131f460_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-53242_0" Pin1InfoVect1LinkObjId="SW-53243_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4049,-998 4049,-1016 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1320650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4049,-1016 4049,-1027 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1320840@0" ObjectIDND1="9303@x" ObjectIDND2="9304@x" ObjectIDZND0="g_131f460@0" Pin0InfoVect0LinkObjId="g_131f460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1320840_0" Pin1InfoVect1LinkObjId="SW-53242_0" Pin1InfoVect2LinkObjId="SW-53243_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4049,-1016 4049,-1027 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_138da00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3863,-829 3863,-854 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9299@1" ObjectIDZND0="9300@0" Pin0InfoVect0LinkObjId="SW-53223_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53220_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3863,-829 3863,-854 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_138dbf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3863,-890 3863,-906 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="9300@1" ObjectIDZND0="9274@0" Pin0InfoVect0LinkObjId="g_12bd060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53223_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3863,-890 3863,-906 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_138dde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3863,-776 3863,-801 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="17700@0" ObjectIDZND0="9299@0" Pin0InfoVect0LinkObjId="SW-53220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_140bb30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3863,-776 3863,-801 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_138dfd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3863,-529 3863,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9275@0" ObjectIDZND0="39279@0" Pin0InfoVect0LinkObjId="SW-235047_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13e9850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3863,-529 3863,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_138e1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3727,-530 3727,-506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9275@0" ObjectIDZND0="39282@0" Pin0InfoVect0LinkObjId="SW-235293_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13e9850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3727,-530 3727,-506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12e7a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4268,-1074 4252,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="17508@0" ObjectIDZND0="17507@x" ObjectIDZND1="g_13cb4e0@0" ObjectIDZND2="18144@1" Pin0InfoVect0LinkObjId="SW-53198_0" Pin0InfoVect1LinkObjId="g_13cb4e0_0" Pin0InfoVect2LinkObjId="g_13c2440_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53197_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4268,-1074 4252,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12e7c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4236,-1099 4252,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_13cb4e0@0" ObjectIDZND0="17508@x" ObjectIDZND1="17507@x" ObjectIDZND2="18144@1" Pin0InfoVect0LinkObjId="SW-53197_0" Pin0InfoVect1LinkObjId="SW-53198_0" Pin0InfoVect2LinkObjId="g_13c2440_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13cb4e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4236,-1099 4252,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13c1970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4252,-1058 4252,-1074 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="17507@1" ObjectIDZND0="17508@x" ObjectIDZND1="g_13cb4e0@0" ObjectIDZND2="18144@1" Pin0InfoVect0LinkObjId="SW-53197_0" Pin0InfoVect1LinkObjId="g_13cb4e0_0" Pin0InfoVect2LinkObjId="g_13c2440_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-53198_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4252,-1058 4252,-1074 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13c2250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4252,-1074 4252,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="17508@x" ObjectIDND1="17507@x" ObjectIDZND0="g_13cb4e0@0" ObjectIDZND1="18144@1" Pin0InfoVect0LinkObjId="g_13cb4e0_0" Pin0InfoVect1LinkObjId="g_13c2440_1" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-53197_0" Pin1InfoVect1LinkObjId="SW-53198_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4252,-1074 4252,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13c2440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4252,-1099 4252,-1122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_13cb4e0@0" ObjectIDND1="17508@x" ObjectIDND2="17507@x" ObjectIDZND0="18144@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13cb4e0_0" Pin1InfoVect1LinkObjId="SW-53197_0" Pin1InfoVect2LinkObjId="SW-53198_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4252,-1099 4252,-1122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13e9850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3533,-500 3533,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39268@0" ObjectIDZND0="9275@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234984_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3533,-500 3533,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1409e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3863,-632 3863,-647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39222@1" ObjectIDZND0="39281@1" Pin0InfoVect0LinkObjId="SW-235047_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235046_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3863,-632 3863,-647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_140a070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3863,-590 3863,-605 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39279@1" ObjectIDZND0="39222@0" Pin0InfoVect0LinkObjId="SW-235046_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235047_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3863,-590 3863,-605 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_140a290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3862,-681 3814,-681 3814,-667 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="39281@x" ObjectIDND1="17700@x" ObjectIDZND0="g_140a4b0@0" Pin0InfoVect0LinkObjId="g_140a4b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-235047_0" Pin1InfoVect1LinkObjId="g_140bb30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3862,-681 3814,-681 3814,-667 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_140b910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3863,-664 3863,-681 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="39281@0" ObjectIDZND0="g_140a4b0@0" ObjectIDZND1="17700@x" Pin0InfoVect0LinkObjId="g_140a4b0_0" Pin0InfoVect1LinkObjId="g_140bb30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235047_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3863,-664 3863,-681 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_140bb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3863,-681 3863,-696 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_140a4b0@0" ObjectIDND1="39281@x" ObjectIDZND0="17700@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_140a4b0_0" Pin1InfoVect1LinkObjId="SW-235047_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3863,-681 3863,-696 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13fad50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3533,-415 3533,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39275@1" ObjectIDZND0="g_140bd50@1" Pin0InfoVect0LinkObjId="g_140bd50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234984_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3533,-415 3533,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13faf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3533,-349 3533,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_13d1600@0" ObjectIDZND0="39275@0" Pin0InfoVect0LinkObjId="SW-234984_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13d1600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3533,-349 3533,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13fbd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3532,-471 3484,-471 3484,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_140bd50@0" ObjectIDND1="39268@x" ObjectIDZND0="g_13fb190@0" Pin0InfoVect0LinkObjId="g_13fb190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_140bd50_0" Pin1InfoVect1LinkObjId="SW-234984_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3532,-471 3484,-471 3484,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13fc810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3533,-462 3533,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_140bd50@0" ObjectIDZND0="g_13fb190@0" ObjectIDZND1="39268@x" Pin0InfoVect0LinkObjId="g_13fb190_0" Pin0InfoVect1LinkObjId="SW-234984_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_140bd50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3533,-462 3533,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13fca30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3533,-471 3533,-483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_13fb190@0" ObjectIDND1="g_140bd50@0" ObjectIDZND0="39268@1" Pin0InfoVect0LinkObjId="SW-234984_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_13fb190_0" Pin1InfoVect1LinkObjId="g_140bd50_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3533,-471 3533,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_135e180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3727,-474 3727,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39239@1" ObjectIDZND0="39282@1" Pin0InfoVect0LinkObjId="SW-235293_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235292_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3727,-474 3727,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_135e3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3727,-432 3727,-447 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39283@1" ObjectIDZND0="39239@0" Pin0InfoVect0LinkObjId="SW-235292_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235293_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3727,-432 3727,-447 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_145f100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3780,-392 3793,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="39241@1" ObjectIDZND0="g_145f5c0@0" Pin0InfoVect0LinkObjId="g_145f5c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235295_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3780,-392 3793,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_145f360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3744,-392 3727,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="39241@0" ObjectIDZND0="39283@x" ObjectIDZND1="g_13eded0@0" ObjectIDZND2="39240@x" Pin0InfoVect0LinkObjId="SW-235293_0" Pin0InfoVect1LinkObjId="g_13eded0_0" Pin0InfoVect2LinkObjId="SW-235294_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235295_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3744,-392 3727,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_135cb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3727,-415 3727,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="39283@0" ObjectIDZND0="39241@x" ObjectIDZND1="g_13eded0@0" ObjectIDZND2="39240@x" Pin0InfoVect0LinkObjId="SW-235295_0" Pin0InfoVect1LinkObjId="g_13eded0_0" Pin0InfoVect2LinkObjId="SW-235294_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235293_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3727,-415 3727,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13eda10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3727,-392 3727,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="39283@x" ObjectIDND1="39241@x" ObjectIDND2="g_13eded0@0" ObjectIDZND0="39240@1" Pin0InfoVect0LinkObjId="SW-235294_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-235293_0" Pin1InfoVect1LinkObjId="SW-235295_0" Pin1InfoVect2LinkObjId="g_13eded0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3727,-392 3727,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13edc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3665,-377 3665,-392 3727,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_13eded0@0" ObjectIDZND0="39283@x" ObjectIDZND1="39241@x" ObjectIDZND2="39240@x" Pin0InfoVect0LinkObjId="SW-235293_0" Pin0InfoVect1LinkObjId="SW-235295_0" Pin0InfoVect2LinkObjId="SW-235294_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13eded0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3665,-377 3665,-392 3727,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13599b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3673,-266 3673,-126 3727,-126 3727,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="capacitor" ObjectIDZND0="39296@0" Pin0InfoVect0LinkObjId="CB-LF_CJ.CX_CJ_Cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3673,-266 3673,-126 3727,-126 3727,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1359c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3727,-256 3697,-256 3697,-266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="39240@x" ObjectIDND1="39296@x" ObjectIDZND0="39242@0" Pin0InfoVect0LinkObjId="SW-235296_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-235294_0" Pin1InfoVect1LinkObjId="CB-LF_CJ.CX_CJ_Cb1_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3727,-256 3697,-256 3697,-266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_131b760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3727,-267 3727,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="39240@0" ObjectIDZND0="39242@x" ObjectIDZND1="39296@x" Pin0InfoVect0LinkObjId="SW-235296_0" Pin0InfoVect1LinkObjId="CB-LF_CJ.CX_CJ_Cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235294_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3727,-267 3727,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_131b9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3727,-256 3727,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="39242@x" ObjectIDND1="39240@x" ObjectIDZND0="39296@1" Pin0InfoVect0LinkObjId="CB-LF_CJ.CX_CJ_Cb1_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-235296_0" Pin1InfoVect1LinkObjId="SW-235294_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3727,-256 3727,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_131d080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3697,-311 3697,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_131bc20@0" ObjectIDZND0="39242@1" Pin0InfoVect0LinkObjId="SW-235296_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_131bc20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3697,-311 3697,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12b7dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3912,-474 3912,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9289@1" ObjectIDZND0="9290@1" Pin0InfoVect0LinkObjId="SW-235149_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235148_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3912,-474 3912,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12b8030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3912,-432 3912,-447 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9291@1" ObjectIDZND0="9289@0" Pin0InfoVect0LinkObjId="SW-235148_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235149_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3912,-432 3912,-447 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_137abe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3965,-392 3978,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="39230@1" ObjectIDZND0="g_137b0a0@0" Pin0InfoVect0LinkObjId="g_137b0a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235151_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3965,-392 3978,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_137ae40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3929,-392 3912,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="39230@0" ObjectIDZND0="9291@x" ObjectIDZND1="39229@x" ObjectIDZND2="g_138fef0@0" Pin0InfoVect0LinkObjId="SW-235149_0" Pin0InfoVect1LinkObjId="SW-235150_0" Pin0InfoVect2LinkObjId="g_138fef0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235151_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3929,-392 3912,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_138f7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3912,-415 3912,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="9291@0" ObjectIDZND0="39230@x" ObjectIDZND1="39229@x" ObjectIDZND2="g_138fef0@0" Pin0InfoVect0LinkObjId="SW-235151_0" Pin0InfoVect1LinkObjId="SW-235150_0" Pin0InfoVect2LinkObjId="g_138fef0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235149_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3912,-415 3912,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_138fa30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3912,-392 3912,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="39230@x" ObjectIDND1="9291@x" ObjectIDND2="g_138fef0@0" ObjectIDZND0="39229@1" Pin0InfoVect0LinkObjId="SW-235150_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-235151_0" Pin1InfoVect1LinkObjId="SW-235149_0" Pin1InfoVect2LinkObjId="g_138fef0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3912,-392 3912,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_138fc90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3868,-376 3868,-392 3912,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_138fef0@0" ObjectIDZND0="39230@x" ObjectIDZND1="9291@x" ObjectIDZND2="39229@x" Pin0InfoVect0LinkObjId="SW-235151_0" Pin0InfoVect1LinkObjId="SW-235149_0" Pin0InfoVect2LinkObjId="SW-235150_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_138fef0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3868,-376 3868,-392 3912,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1390ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3912,-530 3912,-506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9275@0" ObjectIDZND0="9290@0" Pin0InfoVect0LinkObjId="SW-235149_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13e9850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3912,-530 3912,-506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1372e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3912,-261 3960,-261 3960,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="39229@x" ObjectIDND1="18150@x" ObjectIDZND0="g_1372070@0" Pin0InfoVect0LinkObjId="g_1372070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-235150_0" Pin1InfoVect1LinkObjId="EC-LF_CJ.LD_CJ_032_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3912,-261 3960,-261 3960,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1373910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3912,-274 3912,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="39229@0" ObjectIDZND0="g_1372070@0" ObjectIDZND1="18150@x" Pin0InfoVect0LinkObjId="g_1372070_0" Pin0InfoVect1LinkObjId="EC-LF_CJ.LD_CJ_032_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3912,-274 3912,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_141df30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3912,-261 3912,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1372070@0" ObjectIDND1="39229@x" ObjectIDZND0="18150@0" Pin0InfoVect0LinkObjId="EC-LF_CJ.LD_CJ_032_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1372070_0" Pin1InfoVect1LinkObjId="SW-235150_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3912,-261 3912,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_133e850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4204,-471 4204,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39247@1" ObjectIDZND0="39286@1" Pin0InfoVect0LinkObjId="SW-235337_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235336_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4204,-471 4204,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_133eab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4204,-429 4204,-444 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39287@1" ObjectIDZND0="39247@0" Pin0InfoVect0LinkObjId="SW-235336_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235337_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4204,-429 4204,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_136bfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4204,-530 4204,-504 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9275@0" ObjectIDZND0="39286@0" Pin0InfoVect0LinkObjId="SW-235337_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13e9850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4204,-530 4204,-504 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_129d5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4313,-530 4313,-503 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="39221@0" ObjectIDZND0="39288@0" Pin0InfoVect0LinkObjId="SW-235338_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_127b530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4313,-530 4313,-503 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12a0680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4204,-412 4204,-385 4313,-385 4313,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="39287@0" ObjectIDZND0="39289@0" Pin0InfoVect0LinkObjId="SW-235338_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235337_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4204,-412 4204,-385 4313,-385 4313,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12a08f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4313,-451 4313,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="39289@1" ObjectIDZND0="39288@1" Pin0InfoVect0LinkObjId="SW-235338_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235338_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4313,-451 4313,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_134c490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4076,-474 4076,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9292@1" ObjectIDZND0="9293@1" Pin0InfoVect0LinkObjId="SW-235120_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235119_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4076,-474 4076,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_134c6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4076,-432 4076,-447 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9294@1" ObjectIDZND0="9292@0" Pin0InfoVect0LinkObjId="SW-235119_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235120_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4076,-432 4076,-447 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12a2240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4129,-392 4142,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="39228@1" ObjectIDZND0="g_12a2700@0" Pin0InfoVect0LinkObjId="g_12a2700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235122_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4129,-392 4142,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12a24a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-392 4076,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="39228@0" ObjectIDZND0="9294@x" ObjectIDZND1="g_13bd300@0" ObjectIDZND2="39227@x" Pin0InfoVect0LinkObjId="SW-235120_0" Pin0InfoVect1LinkObjId="g_13bd300_0" Pin0InfoVect2LinkObjId="SW-235121_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235122_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-392 4076,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13bcbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4076,-415 4076,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="9294@0" ObjectIDZND0="39228@x" ObjectIDZND1="g_13bd300@0" ObjectIDZND2="39227@x" Pin0InfoVect0LinkObjId="SW-235122_0" Pin0InfoVect1LinkObjId="g_13bd300_0" Pin0InfoVect2LinkObjId="SW-235121_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4076,-415 4076,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13bce40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4076,-392 4076,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="9294@x" ObjectIDND1="39228@x" ObjectIDND2="g_13bd300@0" ObjectIDZND0="39227@1" Pin0InfoVect0LinkObjId="SW-235121_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-235120_0" Pin1InfoVect1LinkObjId="SW-235122_0" Pin1InfoVect2LinkObjId="g_13bd300_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4076,-392 4076,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13bd0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4032,-376 4032,-392 4076,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_13bd300@0" ObjectIDZND0="9294@x" ObjectIDZND1="39228@x" ObjectIDZND2="39227@x" Pin0InfoVect0LinkObjId="SW-235120_0" Pin0InfoVect1LinkObjId="SW-235122_0" Pin0InfoVect2LinkObjId="SW-235121_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13bd300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4032,-376 4032,-392 4076,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13be0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4076,-530 4076,-506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9275@0" ObjectIDZND0="9293@0" Pin0InfoVect0LinkObjId="SW-235120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13e9850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4076,-530 4076,-506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13530b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4076,-261 4124,-261 4124,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="39227@x" ObjectIDND1="0@x" ObjectIDZND0="g_13c0b10@0" Pin0InfoVect0LinkObjId="g_13c0b10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-235121_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4076,-261 4124,-261 4124,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1353310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4076,-274 4076,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="39227@0" ObjectIDZND0="g_13c0b10@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_13c0b10_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235121_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4076,-274 4076,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1353570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4076,-261 4076,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="39227@x" ObjectIDND1="g_13c0b10@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-235121_0" Pin1InfoVect1LinkObjId="g_13c0b10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4076,-261 4076,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_130f3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4390,-474 4390,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9280@1" ObjectIDZND0="9281@1" Pin0InfoVect0LinkObjId="SW-235178_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235177_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4390,-474 4390,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_130f640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4390,-432 4390,-447 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9282@1" ObjectIDZND0="9280@0" Pin0InfoVect0LinkObjId="SW-235177_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235178_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4390,-432 4390,-447 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_139d2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-392 4456,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="39232@1" ObjectIDZND0="g_139d7a0@0" Pin0InfoVect0LinkObjId="g_139d7a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235180_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-392 4456,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_139d540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4407,-392 4390,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="39232@0" ObjectIDZND0="9282@x" ObjectIDZND1="g_13a0e80@0" ObjectIDZND2="39231@x" Pin0InfoVect0LinkObjId="SW-235178_0" Pin0InfoVect1LinkObjId="g_13a0e80_0" Pin0InfoVect2LinkObjId="SW-235179_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4407,-392 4390,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13a0760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4390,-415 4390,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="9282@0" ObjectIDZND0="39232@x" ObjectIDZND1="g_13a0e80@0" ObjectIDZND2="39231@x" Pin0InfoVect0LinkObjId="SW-235180_0" Pin0InfoVect1LinkObjId="g_13a0e80_0" Pin0InfoVect2LinkObjId="SW-235179_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235178_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4390,-415 4390,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13a09c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4390,-392 4390,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="9282@x" ObjectIDND1="39232@x" ObjectIDND2="g_13a0e80@0" ObjectIDZND0="39231@1" Pin0InfoVect0LinkObjId="SW-235179_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-235178_0" Pin1InfoVect1LinkObjId="SW-235180_0" Pin1InfoVect2LinkObjId="g_13a0e80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4390,-392 4390,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13a0c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4346,-376 4346,-392 4390,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_13a0e80@0" ObjectIDZND0="9282@x" ObjectIDZND1="39232@x" ObjectIDZND2="39231@x" Pin0InfoVect0LinkObjId="SW-235178_0" Pin0InfoVect1LinkObjId="SW-235180_0" Pin0InfoVect2LinkObjId="SW-235179_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13a0e80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4346,-376 4346,-392 4390,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13a1c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4390,-530 4390,-506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="39221@0" ObjectIDZND0="9281@0" Pin0InfoVect0LinkObjId="SW-235178_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_127b530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4390,-530 4390,-506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13b6e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4390,-261 4438,-261 4438,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="39231@x" ObjectIDND1="18145@x" ObjectIDZND0="g_13b60e0@0" Pin0InfoVect0LinkObjId="g_13b60e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-235179_0" Pin1InfoVect1LinkObjId="EC-LF_CJ.LD_CJ_041_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4390,-261 4438,-261 4438,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13b70f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4390,-274 4390,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="39231@0" ObjectIDZND0="g_13b60e0@0" ObjectIDZND1="18145@x" Pin0InfoVect0LinkObjId="g_13b60e0_0" Pin0InfoVect1LinkObjId="EC-LF_CJ.LD_CJ_041_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235179_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4390,-274 4390,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13b7350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4390,-261 4390,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="39231@x" ObjectIDND1="g_13b60e0@0" ObjectIDZND0="18145@0" Pin0InfoVect0LinkObjId="EC-LF_CJ.LD_CJ_041_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-235179_0" Pin1InfoVect1LinkObjId="g_13b60e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4390,-261 4390,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1338aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-431 4545,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9279@1" ObjectIDZND0="9277@0" Pin0InfoVect0LinkObjId="SW-234833_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235206_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-431 4545,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1374c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4598,-391 4611,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="39234@1" ObjectIDZND0="g_1375110@0" Pin0InfoVect0LinkObjId="g_1375110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235208_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4598,-391 4611,-391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1374eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4562,-391 4545,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="39234@0" ObjectIDZND0="9279@x" ObjectIDZND1="g_13787f0@0" ObjectIDZND2="39233@x" Pin0InfoVect0LinkObjId="SW-235206_0" Pin0InfoVect1LinkObjId="g_13787f0_0" Pin0InfoVect2LinkObjId="SW-235207_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235208_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4562,-391 4545,-391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13780d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-414 4545,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="9279@0" ObjectIDZND0="39234@x" ObjectIDZND1="g_13787f0@0" ObjectIDZND2="39233@x" Pin0InfoVect0LinkObjId="SW-235208_0" Pin0InfoVect1LinkObjId="g_13787f0_0" Pin0InfoVect2LinkObjId="SW-235207_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235206_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-414 4545,-391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1378330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-391 4545,-309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="9279@x" ObjectIDND1="39234@x" ObjectIDND2="g_13787f0@0" ObjectIDZND0="39233@1" Pin0InfoVect0LinkObjId="SW-235207_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-235206_0" Pin1InfoVect1LinkObjId="SW-235208_0" Pin1InfoVect2LinkObjId="g_13787f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-391 4545,-309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1378590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4501,-375 4501,-391 4545,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_13787f0@0" ObjectIDZND0="9279@x" ObjectIDZND1="39234@x" ObjectIDZND2="39233@x" Pin0InfoVect0LinkObjId="SW-235206_0" Pin0InfoVect1LinkObjId="SW-235208_0" Pin0InfoVect2LinkObjId="SW-235207_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13787f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4501,-375 4501,-391 4545,-391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13795a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-530 4545,-505 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="39221@0" ObjectIDZND0="9278@0" Pin0InfoVect0LinkObjId="SW-235206_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_127b530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-530 4545,-505 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1317e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-260 4593,-260 4593,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="39233@x" ObjectIDND1="18149@x" ObjectIDZND0="g_13170a0@0" Pin0InfoVect0LinkObjId="g_13170a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-235207_0" Pin1InfoVect1LinkObjId="EC-LF_CJ.LD_CJ_042_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-260 4593,-260 4593,-246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13180b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-273 4545,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="39233@0" ObjectIDZND0="g_13170a0@0" ObjectIDZND1="18149@x" Pin0InfoVect0LinkObjId="g_13170a0_0" Pin0InfoVect1LinkObjId="EC-LF_CJ.LD_CJ_042_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235207_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-273 4545,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1318310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-260 4545,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="39233@x" ObjectIDND1="g_13170a0@0" ObjectIDZND0="18149@0" Pin0InfoVect0LinkObjId="EC-LF_CJ.LD_CJ_042_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-235207_0" Pin1InfoVect1LinkObjId="g_13170a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-260 4545,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12eab50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4545,-473 4545,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9277@1" ObjectIDZND0="9278@1" Pin0InfoVect0LinkObjId="SW-235206_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234833_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4545,-473 4545,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12ef4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4750,-390 4763,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="39236@1" ObjectIDZND0="g_12ef9b0@0" Pin0InfoVect0LinkObjId="g_12ef9b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235237_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4750,-390 4763,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12ef750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4714,-390 4697,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="39236@0" ObjectIDZND0="9288@x" ObjectIDZND1="g_13634b0@0" ObjectIDZND2="39235@x" Pin0InfoVect0LinkObjId="SW-235235_0" Pin0InfoVect1LinkObjId="g_13634b0_0" Pin0InfoVect2LinkObjId="SW-235236_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235237_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4714,-390 4697,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1362d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-413 4697,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="9288@0" ObjectIDZND0="39236@x" ObjectIDZND1="g_13634b0@0" ObjectIDZND2="39235@x" Pin0InfoVect0LinkObjId="SW-235237_0" Pin0InfoVect1LinkObjId="g_13634b0_0" Pin0InfoVect2LinkObjId="SW-235236_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235235_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-413 4697,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1362ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-390 4697,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="9288@x" ObjectIDND1="39236@x" ObjectIDND2="g_13634b0@0" ObjectIDZND0="39235@1" Pin0InfoVect0LinkObjId="SW-235236_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-235235_0" Pin1InfoVect1LinkObjId="SW-235237_0" Pin1InfoVect2LinkObjId="g_13634b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-390 4697,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1363250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4653,-374 4653,-390 4697,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_13634b0@0" ObjectIDZND0="9288@x" ObjectIDZND1="39236@x" ObjectIDZND2="39235@x" Pin0InfoVect0LinkObjId="SW-235235_0" Pin0InfoVect1LinkObjId="SW-235237_0" Pin0InfoVect2LinkObjId="SW-235236_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13634b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4653,-374 4653,-390 4697,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1364260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-530 4697,-504 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="39221@0" ObjectIDZND0="9287@0" Pin0InfoVect0LinkObjId="SW-235235_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_127b530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-530 4697,-504 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1395a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-259 4745,-259 4745,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="39235@x" ObjectIDND1="18146@x" ObjectIDZND0="g_1366cc0@0" Pin0InfoVect0LinkObjId="g_1366cc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-235236_0" Pin1InfoVect1LinkObjId="EC-LF_CJ.LD_CJ_043_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-259 4745,-259 4745,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1395c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-272 4697,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="39235@0" ObjectIDZND0="g_1366cc0@0" ObjectIDZND1="18146@x" Pin0InfoVect0LinkObjId="g_1366cc0_0" Pin0InfoVect1LinkObjId="EC-LF_CJ.LD_CJ_043_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235236_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-272 4697,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1395ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-259 4697,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="39235@x" ObjectIDND1="g_1366cc0@0" ObjectIDZND0="18146@0" Pin0InfoVect0LinkObjId="EC-LF_CJ.LD_CJ_043_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-235236_0" Pin1InfoVect1LinkObjId="g_1366cc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-259 4697,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_139b040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-472 4697,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9286@1" ObjectIDZND0="9287@1" Pin0InfoVect0LinkObjId="SW-235235_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235234_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-472 4697,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_139b2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4697,-430 4697,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9288@1" ObjectIDZND0="9286@0" Pin0InfoVect0LinkObjId="SW-235234_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235235_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4697,-430 4697,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13066c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4904,-390 4917,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="39238@1" ObjectIDZND0="g_1306b80@0" Pin0InfoVect0LinkObjId="g_1306b80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235266_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4904,-390 4917,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1306920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4868,-390 4851,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="39238@0" ObjectIDZND0="9285@x" ObjectIDZND1="g_130a260@0" ObjectIDZND2="39237@x" Pin0InfoVect0LinkObjId="SW-235264_0" Pin0InfoVect1LinkObjId="g_130a260_0" Pin0InfoVect2LinkObjId="SW-235265_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235266_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4868,-390 4851,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1309b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-413 4851,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="9285@0" ObjectIDZND0="39238@x" ObjectIDZND1="g_130a260@0" ObjectIDZND2="39237@x" Pin0InfoVect0LinkObjId="SW-235266_0" Pin0InfoVect1LinkObjId="g_130a260_0" Pin0InfoVect2LinkObjId="SW-235265_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235264_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-413 4851,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1309da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-390 4851,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="9285@x" ObjectIDND1="39238@x" ObjectIDND2="g_130a260@0" ObjectIDZND0="39237@1" Pin0InfoVect0LinkObjId="SW-235265_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-235264_0" Pin1InfoVect1LinkObjId="SW-235266_0" Pin1InfoVect2LinkObjId="g_130a260_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-390 4851,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_130a000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4807,-374 4807,-390 4851,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_130a260@0" ObjectIDZND0="9285@x" ObjectIDZND1="39238@x" ObjectIDZND2="39237@x" Pin0InfoVect0LinkObjId="SW-235264_0" Pin0InfoVect1LinkObjId="SW-235266_0" Pin0InfoVect2LinkObjId="SW-235265_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_130a260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4807,-374 4807,-390 4851,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_130b010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-530 4851,-504 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="39221@0" ObjectIDZND0="9284@0" Pin0InfoVect0LinkObjId="SW-235264_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_127b530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-530 4851,-504 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12de510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-259 4899,-259 4899,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="39237@x" ObjectIDND1="18148@x" ObjectIDZND0="g_12dd760@0" Pin0InfoVect0LinkObjId="g_12dd760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-235265_0" Pin1InfoVect1LinkObjId="EC-LF_CJ.LD_CJ_044_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-259 4899,-259 4899,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12de770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-272 4851,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="39237@0" ObjectIDZND0="g_12dd760@0" ObjectIDZND1="18148@x" Pin0InfoVect0LinkObjId="g_12dd760_0" Pin0InfoVect1LinkObjId="EC-LF_CJ.LD_CJ_044_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235265_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-272 4851,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12de9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-259 4851,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="39237@x" ObjectIDND1="g_12dd760@0" ObjectIDZND0="18148@0" Pin0InfoVect0LinkObjId="EC-LF_CJ.LD_CJ_044_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-235265_0" Pin1InfoVect1LinkObjId="g_12dd760_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-259 4851,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12e3b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-472 4851,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9283@1" ObjectIDZND0="9284@1" Pin0InfoVect0LinkObjId="SW-235264_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235263_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-472 4851,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12e3da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4851,-430 4851,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9285@1" ObjectIDZND0="9283@0" Pin0InfoVect0LinkObjId="SW-235263_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235264_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4851,-430 4851,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_148d230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5026,-472 5026,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39243@1" ObjectIDZND0="39284@1" Pin0InfoVect0LinkObjId="SW-235315_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235314_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5026,-472 5026,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_148d490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5026,-430 5026,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39285@1" ObjectIDZND0="39243@0" Pin0InfoVect0LinkObjId="SW-235314_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235315_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5026,-430 5026,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1490560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5079,-390 5092,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="39245@1" ObjectIDZND0="g_1490a20@0" Pin0InfoVect0LinkObjId="g_1490a20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235317_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5079,-390 5092,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14907c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5043,-390 5026,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="39245@0" ObjectIDZND0="39285@x" ObjectIDZND1="g_1493e80@0" ObjectIDZND2="39244@x" Pin0InfoVect0LinkObjId="SW-235315_0" Pin0InfoVect1LinkObjId="g_1493e80_0" Pin0InfoVect2LinkObjId="SW-235316_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235317_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5043,-390 5026,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1493760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5026,-413 5026,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="39285@0" ObjectIDZND0="39245@x" ObjectIDZND1="g_1493e80@0" ObjectIDZND2="39244@x" Pin0InfoVect0LinkObjId="SW-235317_0" Pin0InfoVect1LinkObjId="g_1493e80_0" Pin0InfoVect2LinkObjId="SW-235316_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235315_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5026,-413 5026,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14939c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5026,-390 5026,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="39285@x" ObjectIDND1="39245@x" ObjectIDND2="g_1493e80@0" ObjectIDZND0="39244@1" Pin0InfoVect0LinkObjId="SW-235316_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-235315_0" Pin1InfoVect1LinkObjId="SW-235317_0" Pin1InfoVect2LinkObjId="g_1493e80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5026,-390 5026,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1493c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4964,-375 4964,-390 5026,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1493e80@0" ObjectIDZND0="39285@x" ObjectIDZND1="39245@x" ObjectIDZND2="39244@x" Pin0InfoVect0LinkObjId="SW-235315_0" Pin0InfoVect1LinkObjId="SW-235317_0" Pin0InfoVect2LinkObjId="SW-235316_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1493e80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4964,-375 4964,-390 5026,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_126e450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4972,-264 4972,-124 5026,-124 5026,-134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="capacitor" ObjectIDZND0="39297@0" Pin0InfoVect0LinkObjId="CB-LF_CJ.CX_CJ_Cb2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4972,-264 4972,-124 5026,-124 5026,-134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_126e6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5026,-254 4996,-254 4996,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="39244@x" ObjectIDND1="39297@x" ObjectIDZND0="39246@0" Pin0InfoVect0LinkObjId="SW-235318_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-235316_0" Pin1InfoVect1LinkObjId="CB-LF_CJ.CX_CJ_Cb2_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5026,-254 4996,-254 4996,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_126e900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5026,-265 5026,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="39244@0" ObjectIDZND0="39246@x" ObjectIDZND1="39297@x" Pin0InfoVect0LinkObjId="SW-235318_0" Pin0InfoVect1LinkObjId="CB-LF_CJ.CX_CJ_Cb2_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235316_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5026,-265 5026,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_126eb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5026,-254 5026,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="39244@x" ObjectIDND1="39246@x" ObjectIDZND0="39297@1" Pin0InfoVect0LinkObjId="CB-LF_CJ.CX_CJ_Cb2_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-235316_0" Pin1InfoVect1LinkObjId="SW-235318_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5026,-254 5026,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12701e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4996,-309 4996,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_126edc0@0" ObjectIDZND0="39246@1" Pin0InfoVect0LinkObjId="SW-235318_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_126edc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4996,-309 4996,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1272bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5026,-530 5026,-504 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="39221@0" ObjectIDZND0="39284@0" Pin0InfoVect0LinkObjId="SW-235315_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_127b530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5026,-530 5026,-504 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_127a2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5151,-419 5151,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39295@1" ObjectIDZND0="g_1274150@1" Pin0InfoVect0LinkObjId="g_1274150_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234992_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5151,-419 5151,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_127b2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5151,-390 5199,-390 5199,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="39295@x" ObjectIDZND0="g_127a520@0" Pin0InfoVect0LinkObjId="g_127a520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-234992_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5151,-390 5199,-390 5199,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_127b530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5151,-497 5151,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39294@0" ObjectIDZND0="39221@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234992_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5151,-497 5151,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_127da70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5151,-459 5151,-480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1274150@0" ObjectIDZND0="39294@1" Pin0InfoVect0LinkObjId="SW-234992_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1274150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5151,-459 5151,-480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1280390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5151,-325 5151,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="g_127a520@0" ObjectIDZND1="39295@x" Pin0InfoVect0LinkObjId="g_127a520_0" Pin0InfoVect1LinkObjId="SW-234992_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5151,-325 5151,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12805b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5151,-390 5151,-405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_127a520@0" ObjectIDND1="0@x" ObjectIDZND0="39295@0" Pin0InfoVect0LinkObjId="SW-234992_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_127a520_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5151,-390 5151,-405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12bd060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4366,-891 4366,-906 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39224@1" ObjectIDZND0="9274@0" Pin0InfoVect0LinkObjId="g_138dbf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235102_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4366,-891 4366,-906 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12bd2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4366,-765 4366,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="breaker" ObjectIDND0="39248@2" ObjectIDZND0="39223@0" Pin0InfoVect0LinkObjId="SW-235101_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12c6e80_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4366,-765 4366,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12bd520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4366,-532 4366,-554 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="39221@0" ObjectIDZND0="39280@0" Pin0InfoVect0LinkObjId="SW-235111_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_127b530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4366,-532 4366,-554 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12c5750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4366,-611 4366,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39226@1" ObjectIDZND0="39278@1" Pin0InfoVect0LinkObjId="SW-235111_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235110_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4366,-611 4366,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12c59b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4366,-569 4366,-584 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39280@1" ObjectIDZND0="39226@0" Pin0InfoVect0LinkObjId="SW-235110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235111_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4366,-569 4366,-584 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12c5c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4365,-660 4317,-660 4317,-646 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer" EndDevType0="lightningRod" ObjectIDND0="39278@x" ObjectIDND1="39248@x" ObjectIDZND0="g_12c5e70@0" Pin0InfoVect0LinkObjId="g_12c5e70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-235111_0" Pin1InfoVect1LinkObjId="g_12c6e80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4365,-660 4317,-660 4317,-646 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12c6c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4366,-643 4366,-660 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer" ObjectIDND0="39278@0" ObjectIDZND0="g_12c5e70@0" ObjectIDZND1="39248@x" Pin0InfoVect0LinkObjId="g_12c5e70_0" Pin0InfoVect1LinkObjId="g_12c6e80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235111_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4366,-643 4366,-660 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12c6e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4366,-660 4366,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="transformer" ObjectIDND0="39278@x" ObjectIDND1="g_12c5e70@0" ObjectIDZND0="39248@1" Pin0InfoVect0LinkObjId="g_15bae40_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-235111_0" Pin1InfoVect1LinkObjId="g_12c5e70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4366,-660 4366,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12cad10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4420,-847 4433,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="39225@1" ObjectIDZND0="g_12cb1d0@0" Pin0InfoVect0LinkObjId="g_12cb1d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235103_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4420,-847 4433,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12caf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4384,-847 4368,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="39225@0" ObjectIDZND0="39224@x" ObjectIDZND1="39223@x" Pin0InfoVect0LinkObjId="SW-235102_0" Pin0InfoVect1LinkObjId="SW-235101_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235103_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4384,-847 4368,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12cea20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4366,-837 4366,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="39223@1" ObjectIDZND0="39225@x" ObjectIDZND1="39224@x" Pin0InfoVect0LinkObjId="SW-235103_0" Pin0InfoVect1LinkObjId="SW-235102_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235101_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4366,-837 4366,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12cec80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4366,-847 4366,-855 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="39225@x" ObjectIDND1="39223@x" ObjectIDZND0="39224@0" Pin0InfoVect0LinkObjId="SW-235102_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-235103_0" Pin1InfoVect1LinkObjId="SW-235101_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4366,-847 4366,-855 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12cf220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4184,-906 4184,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9274@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_138dbf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4184,-906 4184,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12cf480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4183,-841 4183,-814 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4183,-841 4183,-814 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12d41c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4924,-530 4924,-561 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="39221@0" ObjectIDZND0="39291@0" Pin0InfoVect0LinkObjId="SW-234989_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_127b530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4924,-530 4924,-561 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12dabb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4923,-661 4923,-699 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="39292@0" ObjectIDZND0="g_12dae10@0" Pin0InfoVect0LinkObjId="g_12dae10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234989_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4923,-661 4923,-699 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_15bae40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4472,-717 4425,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="39276@0" ObjectIDZND0="39248@0" Pin0InfoVect0LinkObjId="g_12c6e80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235503_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4472,-717 4425,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_15c0fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4502,-718 4489,-718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39250@1" ObjectIDZND0="39276@1" Pin0InfoVect0LinkObjId="SW-235503_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235502_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4502,-718 4489,-718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_15c3050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4541,-718 4529,-718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39277@0" ObjectIDZND0="39250@0" Pin0InfoVect0LinkObjId="SW-235502_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-235503_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4541,-718 4529,-718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_15c4330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-719 4560,-719 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_15c47f0@0" ObjectIDND1="g_15c8220@0" ObjectIDND2="0@x" ObjectIDZND0="39277@1" Pin0InfoVect0LinkObjId="SW-235503_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_15c47f0_0" Pin1InfoVect1LinkObjId="g_15c8220_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-719 4560,-719 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_15c4590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4628,-757 4604,-757 4604,-719 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_15c47f0@0" ObjectIDZND0="39277@x" ObjectIDZND1="g_15c8220@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-235503_0" Pin0InfoVect1LinkObjId="g_15c8220_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15c47f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4628,-757 4604,-757 4604,-719 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_15c7fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4632,-684 4604,-684 4604,-719 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_15c8220@0" ObjectIDZND0="39277@x" ObjectIDZND1="g_15c47f0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-235503_0" Pin0InfoVect1LinkObjId="g_15c47f0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15c8220_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4632,-684 4604,-684 4604,-719 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_15c8bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4674,-684 4658,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_15c55a0@0" ObjectIDZND0="g_15c8220@1" Pin0InfoVect0LinkObjId="g_15c8220_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15c55a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4674,-684 4658,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d4ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4925,-599 4877,-599 4877,-591 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="39291@x" ObjectIDND1="g_12da1e0@0" ObjectIDZND0="g_19d50b0@0" Pin0InfoVect0LinkObjId="g_19d50b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-234989_0" Pin1InfoVect1LinkObjId="g_12da1e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4925,-599 4877,-599 4877,-591 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d5a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4923,-630 4923,-644 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_12da1e0@0" ObjectIDZND0="39292@1" Pin0InfoVect0LinkObjId="SW-234989_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12da1e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4923,-630 4923,-644 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d6560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4924,-580 4924,-599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="39291@1" ObjectIDZND0="g_19d50b0@0" ObjectIDZND1="g_12da1e0@0" Pin0InfoVect0LinkObjId="g_19d50b0_0" Pin0InfoVect1LinkObjId="g_12da1e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234989_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4924,-580 4924,-599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d67c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4924,-599 4924,-606 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_19d50b0@0" ObjectIDND1="39291@x" ObjectIDZND0="g_12da1e0@1" Pin0InfoVect0LinkObjId="g_12da1e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_19d50b0_0" Pin1InfoVect1LinkObjId="SW-234989_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4924,-599 4924,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_19ec0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-719 4773,-719 4773,-788 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="39277@x" ObjectIDND1="g_15c47f0@0" ObjectIDND2="g_15c8220@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-235503_0" Pin1InfoVect1LinkObjId="g_15c47f0_0" Pin1InfoVect2LinkObjId="g_15c8220_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-719 4773,-719 4773,-788 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19ec300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4773,-824 4773,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4773,-824 4773,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19ee5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4773,-915 4773,-889 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4773,-915 4773,-889 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19ee830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4773,-951 4773,-980 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4773,-951 4773,-980 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-52549" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3396.000000 -1075.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9259" ObjectName="DYN-LF_CJ"/>
     <cge:Meas_Ref ObjectId="52549"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13f3760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3994.500000 747.000000) translate(0,12)">温度(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13f3a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3995.000000 762.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13f42b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4329.000000 982.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13f4510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4308.500000 997.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13f4f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4315.000000 1012.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13f5840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3852.000000 113.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13f59b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3831.500000 128.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13f5b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3838.000000 143.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15d2df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4404.500000 802.000000) translate(0,12)">温度(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15d3050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4405.000000 817.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15d3380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3723.500000 1007.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15d3ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3724.000000 1022.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15d3f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3714.000000 961.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15d41a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3722.000000 976.000000) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15d43e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3724.000000 991.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15d4960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3744.000000 946.000000) translate(0,12)">HZ:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15d6f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3661.500000 87.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15d7160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3682.000000 72.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15da220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3440.500000 619.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15da4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3441.000000 634.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15da6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3431.000000 573.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15da930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3439.000000 588.000000) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15dab70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3441.000000 603.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15dadb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3461.000000 558.000000) translate(0,12)">HZ:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15db0e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5144.500000 623.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15db360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5145.000000 638.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15db5a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5135.000000 577.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15db7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5143.000000 592.000000) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15dba20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5145.000000 607.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15dbc60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5165.000000 562.000000) translate(0,12)">HZ:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d7490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4462.000000 667.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d76f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4441.500000 682.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d7930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4448.000000 697.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3805.000000 31.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d7c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 107.000000 657.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d7f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 132.000000 642.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d8150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 137.000000 626.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d8e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 118.000000 672.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3807.000000 -179.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d91a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 107.000000 657.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d9450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 132.000000 642.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d9690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 137.000000 626.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d98d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 118.000000 672.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4384.000000 -210.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19da0b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 107.000000 657.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19da350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 132.000000 642.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19da590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 137.000000 626.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19da7d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 118.000000 672.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4313.000000 41.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19dab00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 107.000000 657.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19dadb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 132.000000 642.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19daff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 137.000000 626.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19db230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 118.000000 672.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e1690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4052.000000 112.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e1c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4031.500000 127.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e1ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4038.000000 142.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e21f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4361.000000 113.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e2450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4340.500000 128.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e2690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4347.000000 143.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e29c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4513.000000 113.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e2c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4492.500000 128.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e2e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4499.000000 143.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e3190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4660.000000 112.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e33f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4639.500000 127.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e3630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4646.000000 142.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e3960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4817.000000 112.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e3bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4796.500000 127.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e3e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4803.000000 142.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e4130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4973.500000 88.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e4390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4994.000000 73.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="LF_CJ" endPointId="0" endStationName="PAS_T3" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_JinYangTcj" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4252,-1121 4252,-1165 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18144" ObjectName="AC-35kV.LN_JinYangTcj"/>
    <cge:TPSR_Ref TObjectID="18144_SS-100"/></metadata>
   <polyline fill="none" opacity="0" points="4252,-1121 4252,-1165 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="9274" cx="3863" cy="-906" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9275" cx="3727" cy="-530" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9275" cx="3533" cy="-530" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9275" cx="3912" cy="-530" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9275" cx="4204" cy="-530" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9275" cx="4076" cy="-530" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39221" cx="4313" cy="-530" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39221" cx="4390" cy="-530" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39221" cx="4545" cy="-530" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39221" cx="4697" cy="-530" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39221" cx="4851" cy="-530" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39221" cx="5151" cy="-530" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9274" cx="4366" cy="-906" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39221" cx="4366" cy="-530" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39221" cx="4924" cy="-530" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9275" cx="3863" cy="-530" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="9274" cx="4184" cy="-906" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4773" cy="-980" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-53195">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4242.673667 -969.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17504" ObjectName="SW-LF_CJ.LF_CJ_331BK"/>
     <cge:Meas_Ref ObjectId="53195"/>
    <cge:TPSR_Ref TObjectID="17504"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-53220">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3854.000000 -793.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9299" ObjectName="SW-LF_CJ.LF_CJ_301BK"/>
     <cge:Meas_Ref ObjectId="53220"/>
    <cge:TPSR_Ref TObjectID="9299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235046">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3854.000000 -597.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39222" ObjectName="SW-LF_CJ.LF_CJ_001BK"/>
     <cge:Meas_Ref ObjectId="235046"/>
    <cge:TPSR_Ref TObjectID="39222"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235292">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3718.000000 -439.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39239" ObjectName="SW-LF_CJ.LF_CJ_031BK"/>
     <cge:Meas_Ref ObjectId="235292"/>
    <cge:TPSR_Ref TObjectID="39239"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235148">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3903.000000 -439.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9289" ObjectName="SW-LF_CJ.LF_CJ_032BK"/>
     <cge:Meas_Ref ObjectId="235148"/>
    <cge:TPSR_Ref TObjectID="9289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235336">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4195.000000 -436.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39247" ObjectName="SW-LF_CJ.LF_CJ_012BK"/>
     <cge:Meas_Ref ObjectId="235336"/>
    <cge:TPSR_Ref TObjectID="39247"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235119">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4067.000000 -439.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9292" ObjectName="SW-LF_CJ.LF_CJ_033BK"/>
     <cge:Meas_Ref ObjectId="235119"/>
    <cge:TPSR_Ref TObjectID="9292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235177">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4381.000000 -439.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9280" ObjectName="SW-LF_CJ.LF_CJ_041BK"/>
     <cge:Meas_Ref ObjectId="235177"/>
    <cge:TPSR_Ref TObjectID="9280"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-234833">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4536.000000 -438.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9277" ObjectName="SW-LF_CJ.LF_CJ_042BK"/>
     <cge:Meas_Ref ObjectId="234833"/>
    <cge:TPSR_Ref TObjectID="9277"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235234">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4688.000000 -437.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9286" ObjectName="SW-LF_CJ.LF_CJ_043BK"/>
     <cge:Meas_Ref ObjectId="235234"/>
    <cge:TPSR_Ref TObjectID="9286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235263">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4842.000000 -437.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9283" ObjectName="SW-LF_CJ.LF_CJ_044BK"/>
     <cge:Meas_Ref ObjectId="235263"/>
    <cge:TPSR_Ref TObjectID="9283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235314">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5017.000000 -437.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39243" ObjectName="SW-LF_CJ.LF_CJ_045BK"/>
     <cge:Meas_Ref ObjectId="235314"/>
    <cge:TPSR_Ref TObjectID="39243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235101">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4357.000000 -801.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39223" ObjectName="SW-LF_CJ.LF_CJ_302BK"/>
     <cge:Meas_Ref ObjectId="235101"/>
    <cge:TPSR_Ref TObjectID="39223"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235110">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4357.000000 -576.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39226" ObjectName="SW-LF_CJ.LF_CJ_002BK"/>
     <cge:Meas_Ref ObjectId="235110"/>
    <cge:TPSR_Ref TObjectID="39226"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-235502">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4494.000000 -708.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39250" ObjectName="SW-LF_CJ.LF_CJ_602BK"/>
     <cge:Meas_Ref ObjectId="235502"/>
    <cge:TPSR_Ref TObjectID="39250"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4764.000000 -854.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_142c5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_142c5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_142c5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_142c5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_142c5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_142c5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_142c5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_142c5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_142c5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1404490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1404490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1404490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1404490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1404490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1404490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1404490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1404490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1404490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1404490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1404490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1404490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1404490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1404490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1404490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1404490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1404490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1404490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1433f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3227.000000 -1167.500000) translate(0,16)">川街变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_109c550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4209.000000 -1194.000000) translate(0,18)">川街支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_14428a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3953.000000 -1168.000000) translate(0,18)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_138e3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4053.666667 -189.000000) translate(0,18)">备用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_138e8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3996.000000 -961.000000) translate(0,15)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12e69f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4101.000000 -978.000000) translate(0,15)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12e6d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3870.000000 -881.000000) translate(0,15)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12e7050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4355.000000 -931.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12e7200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3872.000000 -824.000000) translate(0,15)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12e7530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3607.000000 -557.000000) translate(0,15)">10kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12e76e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5055.000000 -560.000000) translate(0,15)">10kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13c0da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4263.000000 -995.000000) translate(0,12)">331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13c10d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4259.000000 -951.000000) translate(0,12)">3311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_13c2630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3902.000000 -746.000000) translate(0,16)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13e9300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3262.000000 -236.000000) translate(0,17)">4725345</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13e9300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3262.000000 -236.000000) translate(0,38)">18887829209</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_13e96a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3899.000000 -715.000000) translate(0,15)">SZ11-4000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13eae20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4259.000000 -1047.000000) translate(0,12)">3316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13eb010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4266.000000 -1100.000000) translate(0,12)">33167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_13f3cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3357.000000 -1149.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_13f3f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3357.000000 -1184.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1280810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5115.000000 -227.000000) translate(0,16)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1281870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3444.000000 -281.000000) translate(0,15)">10kVⅠ母电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_12cf6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4141.000000 -715.000000) translate(0,16)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b7140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3875.000000 -626.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b7770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4378.000000 -605.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b7b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4328.000000 -831.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b7fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4320.000000 -882.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15b8210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4386.000000 -873.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15b9da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4263.000000 -743.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15c9190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4504.000000 -742.000000) translate(0,12)">602</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15c9cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3545.000000 -456.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ca3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3739.000000 -468.000000) translate(0,12)">031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ca5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3736.000000 -292.000000) translate(0,12)">0316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ca830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3617.000000 -294.000000) translate(0,12)">03167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15caa70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3753.000000 -418.000000) translate(0,12)">03160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15cae90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3923.000000 -468.000000) translate(0,12)">032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15cb110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3932.000000 -417.000000) translate(0,12)">03260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15cb350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3920.000000 -299.000000) translate(0,12)">0326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15cb590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4088.000000 -468.000000) translate(0,12)">033</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15cb7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4097.000000 -417.000000) translate(0,12)">03360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15cba10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4085.000000 -299.000000) translate(0,12)">0336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15cbc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4215.000000 -465.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15cbe90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4322.000000 -474.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15cc0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4403.000000 -468.000000) translate(0,12)">041</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15cc310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4408.000000 -418.000000) translate(0,12)">04160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15cc550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4399.000000 -299.000000) translate(0,12)">0416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15cc790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4557.000000 -467.000000) translate(0,12)">042</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15cc9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4566.000000 -417.000000) translate(0,12)">04260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ccc10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4553.000000 -298.000000) translate(0,12)">0426</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15cce50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4708.000000 -466.000000) translate(0,12)">043</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15cd090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4723.000000 -416.000000) translate(0,12)">04360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15cd2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4706.000000 -297.000000) translate(0,12)">0436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15cd510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4863.000000 -466.000000) translate(0,12)">044</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15cd750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4870.000000 -417.000000) translate(0,12)">04460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15cd990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4860.000000 -297.000000) translate(0,12)">0446</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15cdbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5038.000000 -466.000000) translate(0,12)">045</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15cdf10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5046.000000 -416.000000) translate(0,12)">04560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ce370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5034.000000 -290.000000) translate(0,12)">0456</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ce5b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4916.000000 -291.000000) translate(0,12)">04567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ce7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5164.000000 -460.000000) translate(0,12)">0461</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15cea30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4853.000000 -777.000000) translate(0,15)">10kVⅡ母电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15cec80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4459.000000 -770.000000) translate(0,15)">狮子山铜矿线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15cfe30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4938.000000 -616.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15d11c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3665.000000 -109.000000) translate(0,15)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15d1c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4987.000000 -114.000000) translate(0,15)">2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15dc950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3659.000000 -781.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15dc950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3659.000000 -781.000000) translate(0,33)">SZ11-4000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15dc950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3659.000000 -781.000000) translate(0,51)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15dc950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3659.000000 -781.000000) translate(0,69)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19d19f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4139.000000 -678.000000) translate(0,15)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19d19f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4139.000000 -678.000000) translate(0,33)">SZ11-10000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19d19f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4139.000000 -678.000000) translate(0,51)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19d19f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4139.000000 -678.000000) translate(0,69)">±2X2.5%/6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19d19f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4139.000000 -678.000000) translate(0,87)">YN，d11，d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_19d2be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3495.000000 -1170.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" graphid="g_19d36e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3166.000000 -821.000000) translate(0,20)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_19ddc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3876.000000 -190.000000) translate(0,18)">九渡线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_19de930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4333.000000 -188.000000) translate(0,18)">恐龙谷专线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_19df7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4510.000000 -190.000000) translate(0,18)">长田线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_19e0060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4668.000000 -188.000000) translate(0,18)">大村线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_19e08d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4820.000000 -187.000000) translate(0,18)">街区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_19e46b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3124.000000 -183.000000) translate(0,17)">禄丰巡维中心</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_19e46b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3124.000000 -183.000000) translate(0,38)">禄丰变值班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_19e6550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3262.000000 -172.500000) translate(0,17)">13908784381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19e6e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4199.000000 -378.000000) translate(0,12)">Ia(A):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_19e7070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3265.000000 -258.500000) translate(0,17)">4617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19ef220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4730.000000 -882.000000) translate(0,12)">620</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19ef850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4730.000000 -947.000000) translate(0,12)">6202</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19efa90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4726.000000 -821.000000) translate(0,12)">6206</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19efcd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4734.000000 -1013.000000) translate(0,15)">6kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="22" graphid="g_19eff10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4686.000000 -1076.000000) translate(0,18)">玉溪铜矿</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="22" graphid="g_19eff10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4686.000000 -1076.000000) translate(0,40)">110kV狮子山变</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_12e7df0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4313.000000 -1068.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12e85a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4088.000000 -920.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_145f5c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3789.000000 -386.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_131bc20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3691.000000 -306.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_131c650" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3667.000000 -297.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_137b0a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3974.000000 -386.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12a2700" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4138.000000 -386.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_139d7a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4452.000000 -386.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1375110" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4607.000000 -385.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12ef9b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4759.000000 -384.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1306b80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4913.000000 -384.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1490a20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5088.000000 -384.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_126edc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4990.000000 -304.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_126f7b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4966.000000 -295.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12cb1d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4429.000000 -841.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="LF_CJ"/>
</svg>