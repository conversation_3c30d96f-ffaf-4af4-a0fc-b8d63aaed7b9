<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-48" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3137 -1258 2137 1284">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="29" x2="29" y1="7" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="25" x2="25" y1="6" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="4" x2="22" y1="9" y2="9"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="13" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="27" y2="9"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="2" x2="2" y1="11" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="26" x2="9" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="9" x2="9" y1="18" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="5" x2="5" y1="13" y2="5"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape56">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="17" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="52" x2="52" y1="52" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="19" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.504561" x1="26" x2="26" y1="16" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="43" x2="43" y1="2" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="8" x2="8" y1="2" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.708333" x1="7" x2="7" y1="63" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="8" x2="42" y1="64" y2="64"/>
    <rect height="26" stroke-width="0.398039" width="12" x="20" y="32"/>
    <rect height="28" stroke-width="0.398039" width="12" x="1" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="26" x2="41" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="9" x2="42" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.501492" x1="26" x2="26" y1="74" y2="24"/>
    <polyline arcFlag="1" points="43,42 44,42 45,42 45,42 46,43 46,43 47,44 47,44 48,45 48,45 48,46 48,47 49,48 49,49 49,49 48,50 48,51 48,52 48,52 47,53 47,53 46,54 46,54 45,55 45,55 44,55 43,55 " stroke-width="1"/>
    <polyline arcFlag="1" points="43,29 44,29 45,29 45,29 46,30 46,30 47,31 47,31 48,32 48,32 48,33 48,34 49,35 49,36 49,36 48,37 48,38 48,39 48,39 47,40 47,40 46,41 46,41 45,42 45,42 44,42 43,42 " stroke-width="1"/>
    <polyline arcFlag="1" points="43,17 44,17 45,17 45,17 46,18 46,18 47,19 47,19 48,20 48,20 48,21 48,22 49,23 49,24 49,24 48,25 48,26 48,27 48,27 47,28 47,28 46,29 46,29 45,30 45,30 44,30 43,30 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="63" y2="55"/>
   </symbol>
   <symbol id="lightningRod:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="58" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="8" y2="37"/>
   </symbol>
   <symbol id="lightningRod:shape106">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="40" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="54" x2="43" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="42" x2="42" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="35" x2="35" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.676705" x1="34" x2="16" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="16" x2="16" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="9" x2="9" y1="48" y2="31"/>
    <circle cx="30" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="26" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="20" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="0" x2="9" y1="40" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape115">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0319731" x1="9" x2="13" y1="31" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0178798" x1="13" x2="13" y1="35" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0319731" x1="18" x2="13" y1="31" y2="35"/>
    <ellipse cx="13" cy="34" rx="12.5" ry="11.5" stroke-width="0.118558"/>
    <circle cx="13" cy="17" r="12" stroke-width="0.120929"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0326126" x1="18" x2="13" y1="14" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0326126" x1="9" x2="13" y1="14" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0182374" x1="13" x2="13" y1="17" y2="21"/>
   </symbol>
   <symbol id="lightningRod:shape105">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="13" x2="13" y1="39" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44" x1="13" x2="13" y1="5" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="0" x2="12" y1="26" y2="26"/>
    <polyline points="13,39 15,39 17,38 18,38 20,37 21,36 23,35 24,33 25,31 25,30 26,28 26,26 26,24 25,22 25,21 24,19 23,18 21,16 20,15 18,14 17,14 15,13 13,13 11,13 9,14 8,14 6,15 5,16 3,18 2,19 1,21 1,22 0,24 0,26 " stroke-width="0.0972"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="8" x2="8" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape146">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
    <text font-family="SimSun" font-size="15" graphid="g_29c84c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="1" x2="12" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="54" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="3" y1="58" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="5" y1="61" y2="61"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape123">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="2" y2="2"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="20" x2="20" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="5" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="10" x2="8" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="8" y1="4" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="11" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="16" x2="14" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="14" y1="15" y2="18"/>
    <ellipse cx="19" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <ellipse cx="14" cy="16" fillStyle="0" rx="9" ry="7.5" stroke-width="0.155709"/>
   </symbol>
   <symbol id="lightningRod:shape151">
    <ellipse cx="28" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="21,7 5,7 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.240771" x1="3" x2="7" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.240771" x1="0" x2="10" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.206229" x1="4" x2="6" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="39" x2="39" y1="16" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="36" x2="39" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="42" x2="39" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="28" x2="28" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="25" x2="28" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="31" x2="28" y1="11" y2="9"/>
    <ellipse cx="38" cy="16" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="18" cy="14" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="27" x2="27" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="24" x2="27" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="30" x2="27" y1="24" y2="22"/>
    <ellipse cx="27" cy="21" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="17" x2="20" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="16" x2="14" y1="13" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="14" x2="20" y1="16" y2="16"/>
   </symbol>
   <symbol id="lightningRod:shape10">
    <ellipse cx="21" cy="20" rx="20" ry="19.5" stroke-width="2.00007"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.00001" x1="13" x2="20" y1="27" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.00001" x1="28" x2="21" y1="27" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="28" x2="31" y1="26" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="21" x2="21" y1="12" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="14" y1="24" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="28" x2="20" y1="7" y2="13"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <circle cx="14" cy="13" fillStyle="0" r="13.5" stroke-width="1"/>
    <circle cx="14" cy="34" fillStyle="0" r="14" stroke-width="1"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape13_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="transformer2:shape33_0">
    <circle cx="27" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.117188" x1="6" x2="4" y1="44" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3" x1="8" x2="3" y1="46" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470358" x1="1" x2="10" y1="49" y2="49"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,57 6,57 6,49 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="27,14 33,27 20,27 27,14 27,15 27,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="23" y1="56" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="31" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="27" y1="57" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape33_1">
    <circle cx="27" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="23" y1="82" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="31" y1="81" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="27" y1="76" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="24" y1="76" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="25" y1="86" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="33" y1="86" y2="83"/>
   </symbol>
   <symbol id="transformer2:shape12_0">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="80" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="82" y2="87"/>
   </symbol>
   <symbol id="transformer2:shape12_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape13_0">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="29" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="46" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape13_1">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="37" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="37" y1="74" y2="66"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_23be810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23eb170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_23ebb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_236fe60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2370eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2371990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_23723b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2439880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_236e7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_236e7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_243ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_243ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24360c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24360c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_24370e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2438d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_250ff40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2510cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2511440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_24171b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_25132e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2513ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24125d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2413390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2413d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2414800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24151c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_24783b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2415c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2440690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24412b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2689790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2442080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_23904c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2391aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1294" width="2147" x="3132" y="-1263"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(212,170,191)" stroke-width="1" x1="4367" x2="4367" y1="-160" y2="-160"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(212,170,191)" stroke-width="1" x1="4627" x2="4627" y1="-153" y2="-153"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-43652">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4039.870857 -915.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7284" ObjectName="SW-CX_FT.CX_FT_101BK"/>
     <cge:Meas_Ref ObjectId="43652"/>
    <cge:TPSR_Ref TObjectID="7284"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43643">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4262.000000 -913.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7275" ObjectName="SW-CX_FT.CX_FT_161BK"/>
     <cge:Meas_Ref ObjectId="43643"/>
    <cge:TPSR_Ref TObjectID="7275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43678">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3642.983729 -356.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7304" ObjectName="SW-CX_FT.CX_FT_361BK"/>
     <cge:Meas_Ref ObjectId="43678"/>
    <cge:TPSR_Ref TObjectID="7304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43667">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4021.268228 -358.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7296" ObjectName="SW-CX_FT.CX_FT_364BK"/>
     <cge:Meas_Ref ObjectId="43667"/>
    <cge:TPSR_Ref TObjectID="7296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43662">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4142.757918 -357.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7292" ObjectName="SW-CX_FT.CX_FT_365BK"/>
     <cge:Meas_Ref ObjectId="43662"/>
    <cge:TPSR_Ref TObjectID="7292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43660">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4280.419001 -359.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7291" ObjectName="SW-CX_FT.CX_FT_366BK"/>
     <cge:Meas_Ref ObjectId="43660"/>
    <cge:TPSR_Ref TObjectID="7291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43675">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3768.561330 -354.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7302" ObjectName="SW-CX_FT.CX_FT_362BK"/>
     <cge:Meas_Ref ObjectId="43675"/>
    <cge:TPSR_Ref TObjectID="7302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43672">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3893.046332 -354.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7300" ObjectName="SW-CX_FT.CX_FT_363BK"/>
     <cge:Meas_Ref ObjectId="43672"/>
    <cge:TPSR_Ref TObjectID="7300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56852">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4591.620948 -915.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10477" ObjectName="SW-CX_FT.CX_FT_102BK"/>
     <cge:Meas_Ref ObjectId="56852"/>
    <cge:TPSR_Ref TObjectID="10477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56863">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4693.983729 -358.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10485" ObjectName="SW-CX_FT.CX_FT_381BK"/>
     <cge:Meas_Ref ObjectId="56863"/>
    <cge:TPSR_Ref TObjectID="10485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56866">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5153.757918 -359.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10488" ObjectName="SW-CX_FT.CX_FT_384BK"/>
     <cge:Meas_Ref ObjectId="56866"/>
    <cge:TPSR_Ref TObjectID="10488"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56864">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4819.561330 -356.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10486" ObjectName="SW-CX_FT.CX_FT_382BK"/>
     <cge:Meas_Ref ObjectId="56864"/>
    <cge:TPSR_Ref TObjectID="10486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56865">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4944.046332 -356.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10487" ObjectName="SW-CX_FT.CX_FT_383BK"/>
     <cge:Meas_Ref ObjectId="56865"/>
    <cge:TPSR_Ref TObjectID="10487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56874">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4559.983729 -358.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10490" ObjectName="SW-CX_FT.CX_FT_386BK"/>
     <cge:Meas_Ref ObjectId="56874"/>
    <cge:TPSR_Ref TObjectID="10490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56859">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4498.983729 -343.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10484" ObjectName="SW-CX_FT.CX_FT_312BK"/>
     <cge:Meas_Ref ObjectId="56859"/>
    <cge:TPSR_Ref TObjectID="10484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43658">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4101.000000 -511.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7290" ObjectName="SW-CX_FT.CX_FT_301BK"/>
     <cge:Meas_Ref ObjectId="43658"/>
    <cge:TPSR_Ref TObjectID="7290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56858">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4654.000000 -510.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10483" ObjectName="SW-CX_FT.CX_FT_302BK"/>
     <cge:Meas_Ref ObjectId="56858"/>
    <cge:TPSR_Ref TObjectID="10483"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-220421">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3515.983729 -357.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34422" ObjectName="SW-CX_FT.CX_FT_367BK"/>
     <cge:Meas_Ref ObjectId="220421"/>
    <cge:TPSR_Ref TObjectID="34422"/></metadata>
   </g>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_FT.CX_FT_GN1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3647.000000 -164.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15931" ObjectName="SM-CX_FT.CX_FT_GN1"/>
    <cge:TPSR_Ref TObjectID="15931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_FT.CX_FT_GN2">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3773.000000 -167.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15932" ObjectName="SM-CX_FT.CX_FT_GN2"/>
    <cge:TPSR_Ref TObjectID="15932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_FT.CX_FT_GN3">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3897.000000 -169.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43283" ObjectName="SM-CX_FT.CX_FT_GN3"/>
    <cge:TPSR_Ref TObjectID="43283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_FT.CX_FT_GN4">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4698.000000 -169.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43284" ObjectName="SM-CX_FT.CX_FT_GN4"/>
    <cge:TPSR_Ref TObjectID="43284"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_FT.CX_FT_GN5">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4824.000000 -164.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43285" ObjectName="SM-CX_FT.CX_FT_GN5"/>
    <cge:TPSR_Ref TObjectID="43285"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_FT.CX_FT_GN6">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4948.000000 -167.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43286" ObjectName="SM-CX_FT.CX_FT_GN6"/>
    <cge:TPSR_Ref TObjectID="43286"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_ZX" endPointId="0" endStationName="CX_FT" flowDrawDirect="1" flowShape="0" id="AC-110kV.zifeng_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4271,-1182 4271,-1221 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11563" ObjectName="AC-110kV.zifeng_line"/>
    <cge:TPSR_Ref TObjectID="11563_SS-48"/></metadata>
   <polyline fill="none" opacity="0" points="4271,-1182 4271,-1221 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_29c1a90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3711.000000 -314.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ec4e40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4089.000000 -311.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_400ff10" refnum="0">
    <use class="BV-0KV" transform="matrix(0.967742 -0.000000 0.000000 -1.000000 4212.000000 -312.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a51c00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4088.000000 -195.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29bb980" refnum="0">
    <use class="BV-0KV" transform="matrix(0.967742 -0.000000 0.000000 -1.000000 4210.000000 -195.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34738c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4054.000000 -640.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f94c80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4182.000000 -1028.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a06620" refnum="0">
    <use class="BV-0KV" transform="matrix(0.967742 -0.000000 0.000000 -1.000000 3837.000000 -312.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f18ae0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3961.000000 -312.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ef82f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4585.000000 -640.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35dc590" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4353.000000 -117.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3eb9670" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4762.000000 -316.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ebeb30" refnum="0">
    <use class="BV-0KV" transform="matrix(0.967742 -0.000000 0.000000 -1.000000 5223.000000 -314.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32a1340" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5113.000000 -164.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2502620" refnum="0">
    <use class="BV-0KV" transform="matrix(0.967742 -0.000000 0.000000 -1.000000 5221.000000 -165.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e87160" refnum="0">
    <use class="BV-0KV" transform="matrix(0.967742 -0.000000 0.000000 -1.000000 4888.000000 -314.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e8cfb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5012.000000 -314.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b79b10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4612.000000 -108.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34b2150" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3960.000000 -956.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34b2960" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4355.000000 -914.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34b33c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3960.000000 -892.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34b3e50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4182.000000 -956.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34b48e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4182.000000 -893.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2359b90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4512.000000 -892.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_235a620" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4354.000000 -844.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_235b0b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4512.000000 -956.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33c7830" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3584.000000 -315.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_4464120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4049,-819 4049,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="7273@0" ObjectIDZND0="7285@0" Pin0InfoVect0LinkObjId="SW-43653_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4049,-819 4049,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3e62560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-819 4271,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="7273@0" ObjectIDZND0="7277@0" Pin0InfoVect0LinkObjId="SW-43645_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-819 4271,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_356e670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-881 4271,-902 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="7277@1" ObjectIDZND0="7275@x" ObjectIDZND1="7280@x" Pin0InfoVect0LinkObjId="SW-43643_0" Pin0InfoVect1LinkObjId="SW-43648_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43645_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-881 4271,-902 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_33f9570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-902 4271,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="7277@x" ObjectIDND1="7280@x" ObjectIDZND0="7275@0" Pin0InfoVect0LinkObjId="SW-43643_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43645_0" Pin1InfoVect1LinkObjId="SW-43648_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-902 4271,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ebf3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3652,-323 3670,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="27596@x" ObjectIDND1="g_359e270@0" ObjectIDZND0="7305@0" Pin0InfoVect0LinkObjId="SW-43680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43679_0" Pin1InfoVect1LinkObjId="g_359e270_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3652,-323 3670,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ebf650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3706,-323 3716,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7305@1" ObjectIDZND0="g_29c1a90@0" Pin0InfoVect0LinkObjId="g_29c1a90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43680_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3706,-323 3716,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3ebf8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3738,-482 3692,-482 3692,-517 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="7274@0" ObjectIDND1="23696@x" ObjectIDZND0="g_266a3d0@0" Pin0InfoVect0LinkObjId="g_266a3d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3d047f0_0" Pin1InfoVect1LinkObjId="SW-43682_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3738,-482 3692,-482 3692,-517 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a4fb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4399,-443 4399,-408 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="7274@0" ObjectIDZND0="7308@0" Pin0InfoVect0LinkObjId="SW-43683_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d047f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4399,-443 4399,-408 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4012290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4084,-320 4094,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7297@1" ObjectIDZND0="g_3ec4e40@0" Pin0InfoVect0LinkObjId="g_3ec4e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43669_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4084,-320 4094,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_40109a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4207,-321 4217,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7293@1" ObjectIDZND0="g_400ff10@0" Pin0InfoVect0LinkObjId="g_400ff10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43664_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4207,-321 4217,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a52630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4030,-204 4047,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="7298@x" ObjectIDND1="g_3f1a610@0" ObjectIDZND0="7299@0" Pin0InfoVect0LinkObjId="SW-43671_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43670_0" Pin1InfoVect1LinkObjId="g_3f1a610_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4030,-204 4047,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a52860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4083,-204 4093,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7299@1" ObjectIDZND0="g_2a51c00@0" Pin0InfoVect0LinkObjId="g_2a51c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43671_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4083,-204 4093,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29bc3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-204 4169,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="7294@x" ObjectIDND1="g_2667c20@0" ObjectIDZND0="7295@0" Pin0InfoVect0LinkObjId="SW-43666_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43665_0" Pin1InfoVect1LinkObjId="g_2667c20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-204 4169,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29bc610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4205,-204 4215,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7295@1" ObjectIDZND0="g_29bb980@0" Pin0InfoVect0LinkObjId="g_29bb980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43666_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4205,-204 4215,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_343d7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-733 4063,-733 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="7309@x" ObjectIDZND0="g_2b56c60@0" ObjectIDZND1="g_343cdf0@0" ObjectIDZND2="7289@x" Pin0InfoVect0LinkObjId="g_2b56c60_0" Pin0InfoVect1LinkObjId="g_343cdf0_0" Pin0InfoVect2LinkObjId="SW-43657_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-733 4063,-733 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_343d9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4025,-733 4025,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="7309@x" ObjectIDND1="7289@x" ObjectIDND2="g_343cdf0@0" ObjectIDZND0="g_2b56c60@0" Pin0InfoVect0LinkObjId="g_2b56c60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-43657_0" Pin1InfoVect2LinkObjId="g_343cdf0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4025,-733 4025,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_343dc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4063,-733 4025,-733 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="7309@x" ObjectIDND1="7289@x" ObjectIDZND0="g_2b56c60@0" ObjectIDZND1="g_343cdf0@0" Pin0InfoVect0LinkObjId="g_2b56c60_0" Pin0InfoVect1LinkObjId="g_343cdf0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-43657_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4063,-733 4025,-733 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_343de70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4025,-733 3983,-733 3983,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="7309@x" ObjectIDND1="7289@x" ObjectIDND2="g_2b56c60@0" ObjectIDZND0="g_343cdf0@0" Pin0InfoVect0LinkObjId="g_343cdf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-43657_0" Pin1InfoVect2LinkObjId="g_2b56c60_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4025,-733 3983,-733 3983,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3473400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4063,-733 4063,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="transformer2" EndDevType0="switch" ObjectIDND0="g_2b56c60@0" ObjectIDND1="g_343cdf0@0" ObjectIDND2="7309@x" ObjectIDZND0="7289@1" Pin0InfoVect0LinkObjId="SW-43657_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b56c60_0" Pin1InfoVect1LinkObjId="g_343cdf0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4063,-733 4063,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3473660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4063,-679 4063,-666 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7289@0" ObjectIDZND0="g_34738c0@0" Pin0InfoVect0LinkObjId="g_34738c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43657_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4063,-679 4063,-666 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3474310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4049,-950 4049,-965 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="7284@1" ObjectIDZND0="7286@x" ObjectIDZND1="7288@x" Pin0InfoVect0LinkObjId="SW-43654_0" Pin0InfoVect1LinkObjId="SW-43656_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43652_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4049,-950 4049,-965 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3474570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4049,-965 4049,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="7284@x" ObjectIDND1="7288@x" ObjectIDZND0="7286@0" Pin0InfoVect0LinkObjId="SW-43654_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43652_0" Pin1InfoVect1LinkObjId="SW-43656_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4049,-965 4049,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34747d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-948 4271,-965 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="7275@1" ObjectIDZND0="7276@x" ObjectIDZND1="7279@x" Pin0InfoVect0LinkObjId="SW-43644_0" Pin0InfoVect1LinkObjId="SW-43647_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43643_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-948 4271,-965 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3474a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-965 4271,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="7275@x" ObjectIDND1="7279@x" ObjectIDZND0="7276@0" Pin0InfoVect0LinkObjId="SW-43644_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43643_0" Pin1InfoVect1LinkObjId="SW-43647_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-965 4271,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3474c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-902 4258,-902 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="7275@x" ObjectIDND1="7277@x" ObjectIDZND0="7280@1" Pin0InfoVect0LinkObjId="SW-43648_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43643_0" Pin1InfoVect1LinkObjId="SW-43645_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-902 4258,-902 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3474ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4222,-902 4207,-902 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7280@0" ObjectIDZND0="g_34b48e0@0" Pin0InfoVect0LinkObjId="g_34b48e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43648_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4222,-902 4207,-902 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f121e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4049,-901 4036,-901 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="7284@x" ObjectIDND1="7285@x" ObjectIDZND0="7287@1" Pin0InfoVect0LinkObjId="SW-43655_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43652_0" Pin1InfoVect1LinkObjId="SW-43653_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4049,-901 4036,-901 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f12440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4000,-901 3985,-901 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7287@0" ObjectIDZND0="g_34b33c0@0" Pin0InfoVect0LinkObjId="g_34b33c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43655_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4000,-901 3985,-901 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f126a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4049,-883 4049,-901 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="7285@1" ObjectIDZND0="7284@x" ObjectIDZND1="7287@x" Pin0InfoVect0LinkObjId="SW-43652_0" Pin0InfoVect1LinkObjId="SW-43655_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43653_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4049,-883 4049,-901 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f12900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4049,-901 4049,-923 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="7285@x" ObjectIDND1="7287@x" ObjectIDZND0="7284@0" Pin0InfoVect0LinkObjId="SW-43652_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43653_0" Pin1InfoVect1LinkObjId="SW-43655_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4049,-901 4049,-923 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f95710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4222,-1037 4207,-1037 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7278@0" ObjectIDZND0="g_3f94c80@0" Pin0InfoVect0LinkObjId="g_3f94c80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43646_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4222,-1037 4207,-1037 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f95970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4049,-965 4036,-965 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="7284@x" ObjectIDND1="7286@x" ObjectIDZND0="7288@1" Pin0InfoVect0LinkObjId="SW-43656_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43652_0" Pin1InfoVect1LinkObjId="SW-43654_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4049,-965 4036,-965 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3f95bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4000,-965 3985,-965 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7288@0" ObjectIDZND0="g_34b2150@0" Pin0InfoVect0LinkObjId="g_34b2150_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43656_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4000,-965 3985,-965 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34dffd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-965 4258,-965 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="7275@x" ObjectIDND1="7276@x" ObjectIDZND0="7279@1" Pin0InfoVect0LinkObjId="SW-43647_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43643_0" Pin1InfoVect1LinkObjId="SW-43644_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-965 4258,-965 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_34e0230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4222,-965 4207,-965 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7279@0" ObjectIDZND0="g_34b3e50@0" Pin0InfoVect0LinkObjId="g_34b3e50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43647_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4222,-965 4207,-965 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3d016e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-853 4430,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="7273@0" ObjectIDND1="7281@x" ObjectIDZND0="7282@1" Pin0InfoVect0LinkObjId="SW-43650_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-43649_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-853 4430,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3d01940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4394,-853 4379,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7282@0" ObjectIDZND0="g_235a620@0" Pin0InfoVect0LinkObjId="g_235a620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4394,-853 4379,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3d01ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-819 4443,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="7273@0" ObjectIDZND0="7281@x" ObjectIDZND1="7282@x" Pin0InfoVect0LinkObjId="SW-43649_0" Pin0InfoVect1LinkObjId="SW-43650_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-819 4443,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3d04330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4395,-923 4380,-923 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7283@0" ObjectIDZND0="g_34b2960@0" Pin0InfoVect0LinkObjId="g_34b2960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43651_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4395,-923 4380,-923 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3d04590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-853 4443,-869 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="7273@0" ObjectIDND1="7282@x" ObjectIDZND0="7281@0" Pin0InfoVect0LinkObjId="SW-43649_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-43650_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-853 4443,-869 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d047f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3652,-424 3652,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27595@0" ObjectIDZND0="7274@0" Pin0InfoVect0LinkObjId="g_2a07570_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43679_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3652,-424 3652,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33eca20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3652,-391 3652,-407 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7304@1" ObjectIDZND0="27595@1" Pin0InfoVect0LinkObjId="SW-43679_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43678_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3652,-391 3652,-407 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33efba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3652,-350 3652,-364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27596@0" ObjectIDZND0="7304@0" Pin0InfoVect0LinkObjId="SW-43678_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43679_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3652,-350 3652,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33efe00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3652,-323 3652,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_359e270@0" ObjectIDND1="7305@x" ObjectIDZND0="27596@1" Pin0InfoVect0LinkObjId="SW-43679_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_359e270_0" Pin1InfoVect1LinkObjId="SW-43680_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3652,-323 3652,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a070b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3778,-321 3796,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="10583@x" ObjectIDND1="g_359ec90@0" ObjectIDZND0="7303@0" Pin0InfoVect0LinkObjId="SW-43677_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43676_0" Pin1InfoVect1LinkObjId="g_359ec90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3778,-321 3796,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a07310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3832,-321 3842,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7303@1" ObjectIDZND0="g_2a06620@0" Pin0InfoVect0LinkObjId="g_2a06620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43677_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3832,-321 3842,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a07570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3778,-422 3778,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15902@0" ObjectIDZND0="7274@0" Pin0InfoVect0LinkObjId="g_3d047f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43676_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3778,-422 3778,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a077d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3778,-389 3778,-405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7302@1" ObjectIDZND0="15902@1" Pin0InfoVect0LinkObjId="SW-43676_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43675_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3778,-389 3778,-405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a07a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3778,-348 3778,-362 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="10583@0" ObjectIDZND0="7302@0" Pin0InfoVect0LinkObjId="SW-43675_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43676_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3778,-348 3778,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a07c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3778,-321 3778,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_359ec90@0" ObjectIDND1="7303@x" ObjectIDZND0="10583@1" Pin0InfoVect0LinkObjId="SW-43676_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_359ec90_0" Pin1InfoVect1LinkObjId="SW-43677_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3778,-321 3778,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f19570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3902,-321 3920,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="10582@x" ObjectIDND1="g_359f6b0@0" ObjectIDZND0="7301@0" Pin0InfoVect0LinkObjId="SW-43674_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43673_0" Pin1InfoVect1LinkObjId="g_359f6b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3902,-321 3920,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f197d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3956,-321 3966,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7301@1" ObjectIDZND0="g_3f18ae0@0" Pin0InfoVect0LinkObjId="g_3f18ae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43674_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3956,-321 3966,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f19a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3902,-422 3902,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15903@0" ObjectIDZND0="7274@0" Pin0InfoVect0LinkObjId="g_3d047f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43673_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3902,-422 3902,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f19c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3902,-389 3902,-405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7300@1" ObjectIDZND0="15903@1" Pin0InfoVect0LinkObjId="SW-43673_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43672_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3902,-389 3902,-405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f19ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3902,-348 3902,-362 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="10582@0" ObjectIDZND0="7300@0" Pin0InfoVect0LinkObjId="SW-43672_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43673_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3902,-348 3902,-362 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f1a150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3902,-321 3902,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_359f6b0@0" ObjectIDND1="7301@x" ObjectIDZND0="10582@1" Pin0InfoVect0LinkObjId="SW-43673_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_359f6b0_0" Pin1InfoVect1LinkObjId="SW-43674_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3902,-321 3902,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f1a3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4030,-204 4030,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3f1a610@0" ObjectIDND1="7299@x" ObjectIDZND0="7298@0" Pin0InfoVect0LinkObjId="SW-43670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3f1a610_0" Pin1InfoVect1LinkObjId="SW-43671_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4030,-204 4030,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f1b830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4030,-132 4030,-147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_29c7e20@0" ObjectIDZND0="g_3f1a610@0" Pin0InfoVect0LinkObjId="g_3f1a610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29c7e20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4030,-132 4030,-147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e8f860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4030,-190 4030,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3f1a610@1" ObjectIDZND0="7298@x" ObjectIDZND1="7299@x" Pin0InfoVect0LinkObjId="SW-43670_0" Pin0InfoVect1LinkObjId="SW-43671_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3f1a610_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4030,-190 4030,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e8fac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-443 4152,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="7274@0" ObjectIDZND0="15905@0" Pin0InfoVect0LinkObjId="SW-43663_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d047f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-443 4152,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e8fd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-408 4152,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15905@1" ObjectIDZND0="7292@1" Pin0InfoVect0LinkObjId="SW-43662_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43663_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-408 4152,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e8ff80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-365 4152,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7292@0" ObjectIDZND0="10580@0" Pin0InfoVect0LinkObjId="SW-43663_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43662_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-365 4152,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e901e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-215 4152,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="7294@0" ObjectIDZND0="g_2667c20@0" ObjectIDZND1="7295@x" Pin0InfoVect0LinkObjId="g_2667c20_0" Pin0InfoVect1LinkObjId="SW-43666_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43665_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-215 4152,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e90440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-204 4152,-195 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="7294@x" ObjectIDND1="7295@x" ObjectIDZND0="g_2667c20@1" Pin0InfoVect0LinkObjId="g_2667c20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43665_0" Pin1InfoVect1LinkObjId="SW-43666_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-204 4152,-195 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e906a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-153 4152,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2667c20@0" ObjectIDZND0="g_29bece0@0" Pin0InfoVect0LinkObjId="g_29bece0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2667c20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-153 4152,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b579a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3738,-443 3738,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="7274@0" ObjectIDZND0="g_266a3d0@0" ObjectIDZND1="23696@x" Pin0InfoVect0LinkObjId="g_266a3d0_0" Pin0InfoVect1LinkObjId="SW-43682_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d047f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3738,-443 3738,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b5b510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5004,-817 5004,-796 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2b5a960@0" Pin0InfoVect0LinkObjId="g_2b5a960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29c1a90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5004,-817 5004,-796 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b5c4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5004,-751 5004,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2b5a960@1" ObjectIDZND0="g_2b5b770@0" Pin0InfoVect0LinkObjId="g_2b5b770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b5a960_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5004,-751 5004,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b5c720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5004,-684 5004,-672 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2b5b770@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_29c1a90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b5b770_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5004,-684 5004,-672 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_22d1c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-754 4110,-1076 4049,-1076 4049,-1018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="7309@1" ObjectIDZND0="7286@1" Pin0InfoVect0LinkObjId="SW-43654_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-754 4110,-1076 4049,-1076 4049,-1018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_231c0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4601,-819 4601,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="7273@0" ObjectIDZND0="10478@0" Pin0InfoVect0LinkObjId="SW-56853_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4601,-819 4601,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ef4cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4650,-733 4594,-733 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="10476@x" ObjectIDZND0="g_35daba0@0" ObjectIDZND1="g_231e3a0@0" ObjectIDZND2="10482@x" Pin0InfoVect0LinkObjId="g_35daba0_0" Pin0InfoVect1LinkObjId="g_231e3a0_0" Pin0InfoVect2LinkObjId="SW-56857_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4650,-733 4594,-733 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ef4f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4556,-733 4556,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="10476@x" ObjectIDND1="10482@x" ObjectIDND2="g_231e3a0@0" ObjectIDZND0="g_35daba0@0" Pin0InfoVect0LinkObjId="g_35daba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-56857_0" Pin1InfoVect2LinkObjId="g_231e3a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4556,-733 4556,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ef5170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4594,-733 4556,-733 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="10476@x" ObjectIDND1="10482@x" ObjectIDZND0="g_35daba0@0" ObjectIDZND1="g_231e3a0@0" Pin0InfoVect0LinkObjId="g_35daba0_0" Pin0InfoVect1LinkObjId="g_231e3a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-56857_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4594,-733 4556,-733 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ef53d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4556,-733 4514,-733 4514,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="10476@x" ObjectIDND1="10482@x" ObjectIDND2="g_35daba0@0" ObjectIDZND0="g_231e3a0@0" Pin0InfoVect0LinkObjId="g_231e3a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-56857_0" Pin1InfoVect2LinkObjId="g_35daba0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4556,-733 4514,-733 4514,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ef7e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4594,-733 4594,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="transformer2" EndDevType0="switch" ObjectIDND0="g_35daba0@0" ObjectIDND1="g_231e3a0@0" ObjectIDND2="10476@x" ObjectIDZND0="10482@1" Pin0InfoVect0LinkObjId="SW-56857_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_35daba0_0" Pin1InfoVect1LinkObjId="g_231e3a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4594,-733 4594,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ef8090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4594,-679 4594,-666 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10482@0" ObjectIDZND0="g_3ef82f0@0" Pin0InfoVect0LinkObjId="g_3ef82f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56857_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4594,-679 4594,-666 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ef8d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4601,-950 4601,-965 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="10477@1" ObjectIDZND0="10479@x" ObjectIDZND1="10481@x" Pin0InfoVect0LinkObjId="SW-56854_0" Pin0InfoVect1LinkObjId="SW-56856_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56852_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4601,-950 4601,-965 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3ef8fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4601,-965 4601,-982 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="10477@x" ObjectIDND1="10481@x" ObjectIDZND0="10479@0" Pin0InfoVect0LinkObjId="SW-56854_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56852_0" Pin1InfoVect1LinkObjId="SW-56856_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4601,-965 4601,-982 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3efb730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4601,-901 4588,-901 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="10477@x" ObjectIDND1="10478@x" ObjectIDZND0="10480@1" Pin0InfoVect0LinkObjId="SW-56855_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56852_0" Pin1InfoVect1LinkObjId="SW-56853_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4601,-901 4588,-901 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3efb990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4552,-901 4537,-901 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10480@0" ObjectIDZND0="g_2359b90@0" Pin0InfoVect0LinkObjId="g_2359b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56855_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4552,-901 4537,-901 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3efbbf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4601,-883 4601,-901 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="10478@1" ObjectIDZND0="10477@x" ObjectIDZND1="10480@x" Pin0InfoVect0LinkObjId="SW-56852_0" Pin0InfoVect1LinkObjId="SW-56855_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56853_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4601,-883 4601,-901 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3efbe50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4601,-901 4601,-923 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="10478@x" ObjectIDND1="10480@x" ObjectIDZND0="10477@0" Pin0InfoVect0LinkObjId="SW-56852_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56853_0" Pin1InfoVect1LinkObjId="SW-56855_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4601,-901 4601,-923 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35d8320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4601,-965 4588,-965 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="10477@x" ObjectIDND1="10479@x" ObjectIDZND0="10481@1" Pin0InfoVect0LinkObjId="SW-56856_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56852_0" Pin1InfoVect1LinkObjId="SW-56854_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4601,-965 4588,-965 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35d8580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4552,-965 4537,-965 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10481@0" ObjectIDZND0="g_235b0b0@0" Pin0InfoVect0LinkObjId="g_235b0b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56856_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4552,-965 4537,-965 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_35db910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4664,-754 4664,-1077 4601,-1077 4601,-1018 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="10476@1" ObjectIDZND0="10479@1" Pin0InfoVect0LinkObjId="SW-56854_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4664,-754 4664,-1077 4601,-1077 4601,-1018 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35dbb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3738,-482 3738,-509 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="g_266a3d0@0" ObjectIDND1="7274@0" ObjectIDZND0="23696@0" Pin0InfoVect0LinkObjId="SW-43682_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_266a3d0_0" Pin1InfoVect1LinkObjId="g_3d047f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3738,-482 3738,-509 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35dbde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3738,-526 3738,-552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="23696@1" ObjectIDZND0="g_266b140@1" Pin0InfoVect0LinkObjId="g_266b140_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43682_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3738,-526 3738,-552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_35dc040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4362,-150 4362,-143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_35dc590@0" Pin0InfoVect0LinkObjId="g_35dc590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4362,-150 4362,-143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3eba100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4757,-325 4767,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10493@1" ObjectIDZND0="g_3eb9670@0" Pin0InfoVect0LinkObjId="g_3eb9670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56873_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4757,-325 4767,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_329c150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5163,-323 5182,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_32b6ad0@0" ObjectIDND1="27598@x" ObjectIDZND0="10496@0" Pin0InfoVect0LinkObjId="SW-56878_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_32b6ad0_0" Pin1InfoVect1LinkObjId="SW-179929_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5163,-323 5182,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_329c3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5218,-323 5228,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10496@1" ObjectIDZND0="g_3ebeb30@0" Pin0InfoVect0LinkObjId="g_3ebeb30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56878_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5218,-323 5228,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2503050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5163,-174 5180,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3e8e880@0" ObjectIDND1="10492@x" ObjectIDZND0="10497@0" Pin0InfoVect0LinkObjId="SW-56879_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3e8e880_0" Pin1InfoVect1LinkObjId="SW-56872_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5163,-174 5180,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2503280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5216,-174 5226,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10497@1" ObjectIDZND0="g_2502620@0" Pin0InfoVect0LinkObjId="g_2502620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56879_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5216,-174 5226,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2508810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4703,-254 4749,-254 4749,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="lightningRod" ObjectIDND0="g_29c6a20@0" ObjectIDND1="43284@x" ObjectIDZND0="g_29c5cb0@0" Pin0InfoVect0LinkObjId="g_29c5cb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29c6a20_0" Pin1InfoVect1LinkObjId="SM-CX_FT.CX_FT_GN4_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4703,-254 4749,-254 4749,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33afc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4703,-426 4703,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15908@0" ObjectIDZND0="10505@0" Pin0InfoVect0LinkObjId="g_3e880b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56867_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4703,-426 4703,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33afec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4703,-393 4703,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10485@1" ObjectIDZND0="15908@1" Pin0InfoVect0LinkObjId="SW-56867_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56863_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4703,-393 4703,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33b3180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4703,-352 4703,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="10586@0" ObjectIDZND0="10485@0" Pin0InfoVect0LinkObjId="SW-56863_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56867_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4703,-352 4703,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33b33e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4703,-190 4703,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="43284@0" ObjectIDZND0="g_29c5cb0@0" ObjectIDZND1="g_29c6a20@0" Pin0InfoVect0LinkObjId="g_29c5cb0_0" Pin0InfoVect1LinkObjId="g_29c6a20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_FT.CX_FT_GN4_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4703,-190 4703,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e87bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4829,-323 4847,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_32b3090@0" ObjectIDND1="15909@x" ObjectIDZND0="10494@0" Pin0InfoVect0LinkObjId="SW-56876_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_32b3090_0" Pin1InfoVect1LinkObjId="SW-56868_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4829,-323 4847,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e87e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4883,-323 4893,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10494@1" ObjectIDZND0="g_3e87160@0" Pin0InfoVect0LinkObjId="g_3e87160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56876_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4883,-323 4893,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e880b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4829,-424 4829,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10587@0" ObjectIDZND0="10505@0" Pin0InfoVect0LinkObjId="g_33afc60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56868_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4829,-424 4829,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e88310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4829,-391 4829,-407 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10486@1" ObjectIDZND0="10587@1" Pin0InfoVect0LinkObjId="SW-56868_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56864_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4829,-391 4829,-407 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e88570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4829,-350 4829,-364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15909@0" ObjectIDZND0="10486@0" Pin0InfoVect0LinkObjId="SW-56864_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56868_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4829,-350 4829,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e887d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4829,-323 4829,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_32b3090@0" ObjectIDND1="10494@x" ObjectIDZND0="15909@1" Pin0InfoVect0LinkObjId="SW-56868_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_32b3090_0" Pin1InfoVect1LinkObjId="SW-56876_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4829,-323 4829,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e8da40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4953,-323 4971,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_32b60b0@0" ObjectIDND1="15910@x" ObjectIDZND0="10495@0" Pin0InfoVect0LinkObjId="SW-56877_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_32b60b0_0" Pin1InfoVect1LinkObjId="SW-56869_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4953,-323 4971,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e8dca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5007,-323 5017,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10495@1" ObjectIDZND0="g_3e8cfb0@0" Pin0InfoVect0LinkObjId="g_3e8cfb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56877_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5007,-323 5017,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e8df00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4953,-424 4953,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10588@0" ObjectIDZND0="10505@0" Pin0InfoVect0LinkObjId="g_33afc60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56869_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4953,-424 4953,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e8e160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4953,-391 4953,-407 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10487@1" ObjectIDZND0="10588@1" Pin0InfoVect0LinkObjId="SW-56869_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56865_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4953,-391 4953,-407 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e8e3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4953,-350 4953,-364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15910@0" ObjectIDZND0="10487@0" Pin0InfoVect0LinkObjId="SW-56865_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56869_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4953,-350 4953,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3e8e620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4953,-323 4953,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_32b60b0@0" ObjectIDND1="10495@x" ObjectIDZND0="15910@1" Pin0InfoVect0LinkObjId="SW-56869_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_32b60b0_0" Pin1InfoVect1LinkObjId="SW-56877_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4953,-323 4953,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29c29a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5163,-443 5163,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10505@0" ObjectIDZND0="27597@0" Pin0InfoVect0LinkObjId="SW-179929_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33afc60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5163,-443 5163,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29c2c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5163,-410 5163,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27597@1" ObjectIDZND0="10488@1" Pin0InfoVect0LinkObjId="SW-56866_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179929_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5163,-410 5163,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29c2e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5163,-367 5163,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10488@0" ObjectIDZND0="27598@1" Pin0InfoVect0LinkObjId="SW-179929_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56866_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5163,-367 5163,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29c30c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5163,-336 5163,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="27598@0" ObjectIDZND0="g_32b6ad0@0" ObjectIDZND1="10496@x" Pin0InfoVect0LinkObjId="g_32b6ad0_0" Pin0InfoVect1LinkObjId="SW-56878_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-179929_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5163,-336 5163,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29c3320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5163,-185 5163,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="10492@0" ObjectIDZND0="g_3e8e880@0" ObjectIDZND1="10497@x" Pin0InfoVect0LinkObjId="g_3e8e880_0" Pin0InfoVect1LinkObjId="SW-56879_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56872_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5163,-185 5163,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29c7440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4703,-263 4703,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="g_29c6a20@0" ObjectIDZND0="g_29c5cb0@0" ObjectIDZND1="43284@x" Pin0InfoVect0LinkObjId="g_29c5cb0_0" Pin0InfoVect1LinkObjId="SM-CX_FT.CX_FT_GN4_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29c6a20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4703,-263 4703,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29c9310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5163,-80 5163,-104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_29c8920@0" ObjectIDZND0="g_3e8e880@0" Pin0InfoVect0LinkObjId="g_3e8e880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29c8920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5163,-80 5163,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29c9500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5163,-147 5163,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3e8e880@1" ObjectIDZND0="10492@x" ObjectIDZND1="10497@x" Pin0InfoVect0LinkObjId="SW-56872_0" Pin0InfoVect1LinkObjId="SW-56879_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3e8e880_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5163,-147 5163,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29c96f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5054,-99 5054,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2505a10@0" ObjectIDZND0="g_29c3580@0" Pin0InfoVect0LinkObjId="g_29c3580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2505a10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5054,-99 5054,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29c98e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5054,-155 5054,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_29c3580@1" ObjectIDZND0="10491@x" ObjectIDZND1="10498@x" Pin0InfoVect0LinkObjId="SW-56871_0" Pin0InfoVect1LinkObjId="SW-56880_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29c3580_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5054,-155 5054,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29c9b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5054,-173 5069,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_29c3580@0" ObjectIDND1="10491@x" ObjectIDZND0="10498@0" Pin0InfoVect0LinkObjId="SW-56880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29c3580_0" Pin1InfoVect1LinkObjId="SW-56871_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5054,-173 5069,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29c9d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5054,-173 5054,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_29c3580@0" ObjectIDND1="10498@x" ObjectIDZND0="10491@0" Pin0InfoVect0LinkObjId="SW-56871_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29c3580_0" Pin1InfoVect1LinkObjId="SW-56880_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5054,-173 5054,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29c9f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5105,-173 5118,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10498@1" ObjectIDZND0="g_32a1340@0" Pin0InfoVect0LinkObjId="g_32a1340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56880_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5105,-173 5118,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b75be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4569,-426 4569,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="27599@0" ObjectIDZND0="10505@0" Pin0InfoVect0LinkObjId="g_33afc60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56875_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4569,-426 4569,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b75e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4569,-393 4569,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10490@1" ObjectIDZND0="27599@1" Pin0InfoVect0LinkObjId="SW-56875_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56874_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4569,-393 4569,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b79100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4569,-352 4569,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="27600@0" ObjectIDZND0="10490@0" Pin0InfoVect0LinkObjId="SW-56874_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56875_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4569,-352 4569,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b79360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4569,-334 4569,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="27600@1" ObjectIDZND0="g_32b8330@0" Pin0InfoVect0LinkObjId="g_32b8330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56875_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4569,-334 4569,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b795c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-143 4621,-134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_2b79b10@0" Pin0InfoVect0LinkObjId="g_2b79b10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-143 4621,-134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34acb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4508,-411 4508,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10800@0" ObjectIDZND0="10505@0" Pin0InfoVect0LinkObjId="g_33afc60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56861_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4508,-411 4508,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34acd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4399,-339 4399,-284 4508,-284 4508,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="7308@1" ObjectIDZND0="15901@1" Pin0InfoVect0LinkObjId="SW-56861_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43683_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4399,-339 4399,-284 4508,-284 4508,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34acfe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4508,-337 4508,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15901@0" ObjectIDZND0="10484@0" Pin0InfoVect0LinkObjId="SW-56859_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56861_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4508,-337 4508,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34ad240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4508,-378 4508,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10484@1" ObjectIDZND0="10800@1" Pin0InfoVect0LinkObjId="SW-56861_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56859_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4508,-378 4508,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34b0e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-480 4110,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15730@1" ObjectIDZND0="7274@0" Pin0InfoVect0LinkObjId="g_3d047f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43659_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-480 4110,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34b1060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4663,-479 4663,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="15731@1" ObjectIDZND0="10505@0" Pin0InfoVect0LinkObjId="g_33afc60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56860_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4663,-479 4663,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3590d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-923 4443,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="7281@x" ObjectIDND1="7283@x" ObjectIDZND0="g_358f740@0" Pin0InfoVect0LinkObjId="g_358f740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43649_0" Pin1InfoVect1LinkObjId="SW-43651_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-923 4443,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3590fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-905 4443,-923 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="7281@1" ObjectIDZND0="g_358f740@0" ObjectIDZND1="7283@x" Pin0InfoVect0LinkObjId="g_358f740_0" Pin0InfoVect1LinkObjId="SW-43651_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43649_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-905 4443,-923 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_3591200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4443,-923 4431,-923 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_358f740@0" ObjectIDND1="7281@x" ObjectIDZND0="7283@1" Pin0InfoVect0LinkObjId="SW-43651_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_358f740_0" Pin1InfoVect1LinkObjId="SW-43649_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4443,-923 4431,-923 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3591c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4875,-612 4875,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_359a200@0" ObjectIDZND0="g_34adae0@0" Pin0InfoVect0LinkObjId="g_34adae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_359a200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4875,-612 4875,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3591e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4875,-443 4875,-484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="10505@0" ObjectIDZND0="g_358dad0@0" ObjectIDZND1="23697@x" Pin0InfoVect0LinkObjId="g_358dad0_0" Pin0InfoVect1LinkObjId="SW-56862_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33afc60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4875,-443 4875,-484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3592000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4875,-484 4829,-484 4829,-519 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="10505@0" ObjectIDND1="23697@x" ObjectIDZND0="g_358dad0@0" Pin0InfoVect0LinkObjId="g_358dad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33afc60_0" Pin1InfoVect1LinkObjId="SW-56862_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4875,-484 4829,-484 4829,-519 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35921f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4875,-484 4875,-514 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="g_358dad0@0" ObjectIDND1="10505@0" ObjectIDZND0="23697@0" Pin0InfoVect0LinkObjId="SW-56862_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_358dad0_0" Pin1InfoVect1LinkObjId="g_33afc60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4875,-484 4875,-514 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3592420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4875,-531 4875,-555 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="23697@1" ObjectIDZND0="g_34adae0@1" Pin0InfoVect0LinkObjId="g_34adae0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56862_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4875,-531 4875,-555 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3596190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4290,-260 4362,-260 4362,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDZND0="g_3596380@1" Pin0InfoVect0LinkObjId="g_3596380_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29c1a90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4290,-260 4362,-260 4362,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3597490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4362,-198 4362,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_3596380@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3596380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4362,-198 4362,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35976f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4569,-257 4621,-257 4621,-243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_3596aa0@1" Pin0InfoVect0LinkObjId="g_3596aa0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4569,-257 4621,-257 4621,-243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3597950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-190 4621,-180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_3596aa0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3596aa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-190 4621,-180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3599fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3739,-583 3739,-601 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_266b140@0" ObjectIDZND0="g_3597bb0@0" Pin0InfoVect0LinkObjId="g_3597bb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_266b140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3739,-583 3739,-601 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_359c5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4703,-325 4703,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="10586@x" ObjectIDND1="10493@x" ObjectIDZND0="g_29c6a20@1" Pin0InfoVect0LinkObjId="g_29c6a20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56867_0" Pin1InfoVect1LinkObjId="SW-56873_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4703,-325 4703,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_359c850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4703,-334 4703,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="10586@1" ObjectIDZND0="g_29c6a20@0" ObjectIDZND1="10493@x" Pin0InfoVect0LinkObjId="g_29c6a20_0" Pin0InfoVect1LinkObjId="SW-56873_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56867_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4703,-334 4703,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_359cab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4703,-325 4721,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="10586@x" ObjectIDND1="g_29c6a20@0" ObjectIDZND0="10493@0" Pin0InfoVect0LinkObjId="SW-56873_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56867_0" Pin1InfoVect1LinkObjId="g_29c6a20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4703,-325 4721,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_359cd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-334 4152,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="10580@1" ObjectIDZND0="g_32b2670@0" ObjectIDZND1="7293@x" Pin0InfoVect0LinkObjId="g_32b2670_0" Pin0InfoVect1LinkObjId="SW-43664_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43663_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-334 4152,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_359cf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-321 4171,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="10580@x" ObjectIDND1="g_32b2670@0" ObjectIDZND0="7293@0" Pin0InfoVect0LinkObjId="SW-43664_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43663_0" Pin1InfoVect1LinkObjId="g_32b2670_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-321 4171,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_359d1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3948,-231 3948,-250 3902,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="g_2669660@0" ObjectIDZND0="g_359f6b0@0" ObjectIDZND1="43283@x" Pin0InfoVect0LinkObjId="g_359f6b0_0" Pin0InfoVect1LinkObjId="SM-CX_FT.CX_FT_GN3_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2669660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3948,-231 3948,-250 3902,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_359d430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3902,-250 3902,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="generator" ObjectIDND0="g_359f6b0@0" ObjectIDND1="g_2669660@0" ObjectIDZND0="43283@0" Pin0InfoVect0LinkObjId="SM-CX_FT.CX_FT_GN3_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_359f6b0_0" Pin1InfoVect1LinkObjId="g_2669660_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3902,-250 3902,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_359d690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3698,-233 3698,-252 3652,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="g_358cfa0@0" ObjectIDZND0="g_359e270@0" ObjectIDZND1="15931@x" Pin0InfoVect0LinkObjId="g_359e270_0" Pin0InfoVect1LinkObjId="SM-CX_FT.CX_FT_GN1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_358cfa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3698,-233 3698,-252 3652,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_359d8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3652,-252 3652,-185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="generator" ObjectIDND0="g_359e270@0" ObjectIDND1="g_358cfa0@0" ObjectIDZND0="15931@0" Pin0InfoVect0LinkObjId="SM-CX_FT.CX_FT_GN1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_359e270_0" Pin1InfoVect1LinkObjId="g_358cfa0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3652,-252 3652,-185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_359db50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3824,-231 3824,-250 3778,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="g_26688f0@0" ObjectIDZND0="g_359ec90@0" ObjectIDZND1="15932@x" Pin0InfoVect0LinkObjId="g_359ec90_0" Pin0InfoVect1LinkObjId="SM-CX_FT.CX_FT_GN2_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26688f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3824,-231 3824,-250 3778,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_359ddb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3778,-250 3778,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="generator" ObjectIDND0="g_359ec90@0" ObjectIDND1="g_26688f0@0" ObjectIDZND0="15932@0" Pin0InfoVect0LinkObjId="SM-CX_FT.CX_FT_GN2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_359ec90_0" Pin1InfoVect1LinkObjId="g_26688f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3778,-250 3778,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_359e010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4030,-320 4048,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="10581@x" ObjectIDND1="g_32b1de0@0" ObjectIDZND0="7297@0" Pin0InfoVect0LinkObjId="SW-43669_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43668_0" Pin1InfoVect1LinkObjId="g_32b1de0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4030,-320 4048,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32b3ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3652,-323 3652,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="27596@x" ObjectIDND1="7305@x" ObjectIDZND0="g_359e270@1" Pin0InfoVect0LinkObjId="g_359e270_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43679_0" Pin1InfoVect1LinkObjId="SW-43680_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3652,-323 3652,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32b3d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3652,-262 3652,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="g_359e270@0" ObjectIDZND0="g_358cfa0@0" ObjectIDZND1="15931@x" Pin0InfoVect0LinkObjId="g_358cfa0_0" Pin0InfoVect1LinkObjId="SM-CX_FT.CX_FT_GN1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_359e270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3652,-262 3652,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32b3f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3778,-248 3778,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="lightningRod" ObjectIDND0="g_26688f0@0" ObjectIDND1="15932@x" ObjectIDZND0="g_359ec90@0" Pin0InfoVect0LinkObjId="g_359ec90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_26688f0_0" Pin1InfoVect1LinkObjId="SM-CX_FT.CX_FT_GN2_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3778,-248 3778,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32b41d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3778,-313 3778,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_359ec90@1" ObjectIDZND0="10583@x" ObjectIDZND1="7303@x" Pin0InfoVect0LinkObjId="SW-43676_0" Pin0InfoVect1LinkObjId="SW-43677_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_359ec90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3778,-313 3778,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32b4430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3902,-250 3902,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="lightningRod" ObjectIDND0="g_2669660@0" ObjectIDND1="43283@x" ObjectIDZND0="g_359f6b0@0" Pin0InfoVect0LinkObjId="g_359f6b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2669660_0" Pin1InfoVect1LinkObjId="SM-CX_FT.CX_FT_GN3_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3902,-250 3902,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32b4690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3902,-313 3902,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_359f6b0@1" ObjectIDZND0="10582@x" ObjectIDZND1="7301@x" Pin0InfoVect0LinkObjId="SW-43673_0" Pin0InfoVect1LinkObjId="SW-43674_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_359f6b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3902,-313 3902,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32b48f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4030,-320 4030,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="10581@x" ObjectIDND1="7297@x" ObjectIDZND0="g_32b1de0@1" Pin0InfoVect0LinkObjId="g_32b1de0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43668_0" Pin1InfoVect1LinkObjId="SW-43669_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4030,-320 4030,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32b4b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4030,-259 4030,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_32b1de0@0" ObjectIDZND0="7298@1" Pin0InfoVect0LinkObjId="SW-43670_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32b1de0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4030,-259 4030,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32b4db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-321 4152,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="10580@x" ObjectIDND1="7293@x" ObjectIDZND0="g_32b2670@1" Pin0InfoVect0LinkObjId="g_32b2670_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43663_0" Pin1InfoVect1LinkObjId="SW-43664_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-321 4152,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32b5010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4152,-260 4152,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_32b2670@0" ObjectIDZND0="7294@1" Pin0InfoVect0LinkObjId="SW-43665_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32b2670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4152,-260 4152,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32b5270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5054,-223 5054,-246 5163,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="10491@1" ObjectIDZND0="g_32b6ad0@0" ObjectIDZND1="10492@x" Pin0InfoVect0LinkObjId="g_32b6ad0_0" Pin0InfoVect1LinkObjId="SW-56872_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56871_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5054,-223 5054,-246 5163,-246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32b54d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5163,-246 5163,-221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_32b6ad0@0" ObjectIDND1="10491@x" ObjectIDZND0="10492@1" Pin0InfoVect0LinkObjId="SW-56872_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_32b6ad0_0" Pin1InfoVect1LinkObjId="SW-56871_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5163,-246 5163,-221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32b5730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4999,-233 4999,-252 4953,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="g_29c4f40@0" ObjectIDZND0="g_32b60b0@0" ObjectIDZND1="43286@x" Pin0InfoVect0LinkObjId="g_32b60b0_0" Pin0InfoVect1LinkObjId="SM-CX_FT.CX_FT_GN6_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29c4f40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4999,-233 4999,-252 4953,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32b5990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4953,-252 4953,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="generator" ObjectIDND0="g_29c4f40@0" ObjectIDND1="g_32b60b0@0" ObjectIDZND0="43286@0" Pin0InfoVect0LinkObjId="SM-CX_FT.CX_FT_GN6_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29c4f40_0" Pin1InfoVect1LinkObjId="g_32b60b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4953,-252 4953,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32b5bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4875,-233 4875,-252 4829,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="generator" ObjectIDND0="g_29c41d0@0" ObjectIDZND0="g_32b3090@0" ObjectIDZND1="43285@x" Pin0InfoVect0LinkObjId="g_32b3090_0" Pin0InfoVect1LinkObjId="SM-CX_FT.CX_FT_GN5_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29c41d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4875,-233 4875,-252 4829,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32b5e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4829,-252 4829,-185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="generator" ObjectIDND0="g_29c41d0@0" ObjectIDND1="g_32b3090@0" ObjectIDZND0="43285@0" Pin0InfoVect0LinkObjId="SM-CX_FT.CX_FT_GN5_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29c41d0_0" Pin1InfoVect1LinkObjId="g_32b3090_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4829,-252 4829,-185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32b74f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4829,-252 4829,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="lightningRod" ObjectIDND0="g_29c41d0@0" ObjectIDND1="43285@x" ObjectIDZND0="g_32b3090@0" Pin0InfoVect0LinkObjId="g_32b3090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29c41d0_0" Pin1InfoVect1LinkObjId="SM-CX_FT.CX_FT_GN5_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4829,-252 4829,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32b7750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4829,-316 4829,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_32b3090@1" ObjectIDZND0="15909@x" ObjectIDZND1="10494@x" Pin0InfoVect0LinkObjId="SW-56868_0" Pin0InfoVect1LinkObjId="SW-56876_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32b3090_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4829,-316 4829,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32b79b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4953,-251 4953,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="generator" EndDevType0="lightningRod" ObjectIDND0="g_29c4f40@0" ObjectIDND1="43286@x" ObjectIDZND0="g_32b60b0@0" Pin0InfoVect0LinkObjId="g_32b60b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29c4f40_0" Pin1InfoVect1LinkObjId="SM-CX_FT.CX_FT_GN6_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4953,-251 4953,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32b7c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4953,-314 4953,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_32b60b0@1" ObjectIDZND0="15910@x" ObjectIDZND1="10495@x" Pin0InfoVect0LinkObjId="SW-56869_0" Pin0InfoVect1LinkObjId="SW-56877_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32b60b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4953,-314 4953,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32b7e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5163,-245 5163,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="10491@x" ObjectIDND1="10492@x" ObjectIDZND0="g_32b6ad0@0" Pin0InfoVect0LinkObjId="g_32b6ad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-56871_0" Pin1InfoVect1LinkObjId="SW-56872_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5163,-245 5163,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32b80d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5163,-310 5163,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_32b6ad0@1" ObjectIDZND0="10496@x" ObjectIDZND1="27598@x" Pin0InfoVect0LinkObjId="SW-56878_0" Pin0InfoVect1LinkObjId="SW-179929_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32b6ad0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5163,-310 5163,-323 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_32b9330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4258,-1037 4271,-1037 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="7278@1" ObjectIDZND0="g_34e0490@0" ObjectIDZND1="g_2b55f50@0" ObjectIDZND2="11563@1" Pin0InfoVect0LinkObjId="g_34e0490_0" Pin0InfoVect1LinkObjId="g_2b55f50_0" Pin0InfoVect2LinkObjId="g_32ba170_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43646_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4258,-1037 4271,-1037 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_32b9590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-1018 4271,-1037 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="7276@1" ObjectIDZND0="g_34e0490@0" ObjectIDZND1="g_2b55f50@0" ObjectIDZND2="11563@1" Pin0InfoVect0LinkObjId="g_34e0490_0" Pin0InfoVect1LinkObjId="g_2b55f50_0" Pin0InfoVect2LinkObjId="g_32ba170_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43644_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-1018 4271,-1037 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_32b97f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-1141 4271,-1141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_34e0490@0" ObjectIDZND0="7276@x" ObjectIDZND1="7278@x" ObjectIDZND2="g_2b55f50@0" Pin0InfoVect0LinkObjId="SW-43644_0" Pin0InfoVect1LinkObjId="SW-43646_0" Pin0InfoVect2LinkObjId="g_2b55f50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34e0490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-1141 4271,-1141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_32b9a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-1037 4271,-1141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="7276@x" ObjectIDND1="7278@x" ObjectIDZND0="g_34e0490@0" ObjectIDZND1="g_2b55f50@0" ObjectIDZND2="11563@1" Pin0InfoVect0LinkObjId="g_34e0490_0" Pin0InfoVect1LinkObjId="g_2b55f50_0" Pin0InfoVect2LinkObjId="g_32ba170_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43644_0" Pin1InfoVect1LinkObjId="SW-43646_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-1037 4271,-1141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_32b9cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4303,-1125 4303,-1151 4271,-1151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2b55f50@0" ObjectIDZND0="7276@x" ObjectIDZND1="7278@x" ObjectIDZND2="g_34e0490@0" Pin0InfoVect0LinkObjId="SW-43644_0" Pin0InfoVect1LinkObjId="SW-43646_0" Pin0InfoVect2LinkObjId="g_34e0490_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b55f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4303,-1125 4303,-1151 4271,-1151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_32b9f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-1141 4271,-1151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="7276@x" ObjectIDND1="7278@x" ObjectIDND2="g_34e0490@0" ObjectIDZND0="g_2b55f50@0" ObjectIDZND1="11563@1" Pin0InfoVect0LinkObjId="g_2b55f50_0" Pin0InfoVect1LinkObjId="g_32ba170_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-43644_0" Pin1InfoVect1LinkObjId="SW-43646_0" Pin1InfoVect2LinkObjId="g_34e0490_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-1141 4271,-1151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_32ba170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-1151 4271,-1185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="7276@x" ObjectIDND1="7278@x" ObjectIDND2="g_34e0490@0" ObjectIDZND0="11563@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-43644_0" Pin1InfoVect1LinkObjId="SW-43646_0" Pin1InfoVect2LinkObjId="g_34e0490_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-1151 4271,-1185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32bc600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-566 4110,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="10578@1" ObjectIDZND0="7290@1" Pin0InfoVect0LinkObjId="SW-43658_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43659_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-566 4110,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32bc860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-519 4110,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7290@0" ObjectIDZND0="15730@0" Pin0InfoVect0LinkObjId="SW-43659_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43658_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-519 4110,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34e3b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4663,-565 4663,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="10585@1" ObjectIDZND0="10483@1" Pin0InfoVect0LinkObjId="SW-56858_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56860_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4663,-565 4663,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34e3d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4663,-518 4663,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10483@0" ObjectIDZND0="15731@0" Pin0InfoVect0LinkObjId="SW-56860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56858_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4663,-518 4663,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34e8360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4110,-675 4110,-582 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="7309@0" ObjectIDZND0="10578@0" Pin0InfoVect0LinkObjId="SW-43659_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4110,-675 4110,-582 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34e8550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4663,-674 4663,-582 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="10476@0" ObjectIDZND0="10585@0" Pin0InfoVect0LinkObjId="SW-56860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4663,-674 4663,-582 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_326d140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4030,-443 4030,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="7274@0" ObjectIDZND0="15904@0" Pin0InfoVect0LinkObjId="SW-43668_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d047f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4030,-443 4030,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_326d3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4030,-410 4030,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15904@1" ObjectIDZND0="7296@1" Pin0InfoVect0LinkObjId="SW-43667_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43668_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4030,-410 4030,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3270620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4030,-366 4030,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7296@0" ObjectIDZND0="10581@0" Pin0InfoVect0LinkObjId="SW-43668_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43667_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4030,-366 4030,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3270880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4030,-333 4030,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="10581@1" ObjectIDZND0="g_32b1de0@0" ObjectIDZND1="7297@x" Pin0InfoVect0LinkObjId="g_32b1de0_0" Pin0InfoVect1LinkObjId="SW-43669_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43668_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4030,-333 4030,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3df3ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4289,-443 4289,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="7274@0" ObjectIDZND0="15912@0" Pin0InfoVect0LinkObjId="SW-43661_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3d047f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4289,-443 4289,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3df4120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4289,-410 4289,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="15912@1" ObjectIDZND0="7291@1" Pin0InfoVect0LinkObjId="SW-43660_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43661_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4289,-410 4289,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3df7440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4289,-272 4289,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="10579@1" Pin0InfoVect0LinkObjId="SW-43661_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29c1a90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4289,-272 4289,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3df76a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4289,-352 4289,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="10579@0" ObjectIDZND0="7291@0" Pin0InfoVect0LinkObjId="SW-43660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43661_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4289,-352 4289,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c82c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3525,-324 3543,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="34428@x" ObjectIDND1="g_3f07190@0" ObjectIDND2="g_3efe430@0" ObjectIDZND0="34424@0" Pin0InfoVect0LinkObjId="SW-220423_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-220422_0" Pin1InfoVect1LinkObjId="g_3f07190_0" Pin1InfoVect2LinkObjId="g_3efe430_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3525,-324 3543,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c8520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3579,-324 3589,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="34424@1" ObjectIDZND0="g_33c7830@0" Pin0InfoVect0LinkObjId="g_33c7830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-220423_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3579,-324 3589,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c8780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3525,-425 3525,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="34423@0" ObjectIDZND0="7274@0" Pin0InfoVect0LinkObjId="g_3d047f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-220422_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3525,-425 3525,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c89e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3525,-392 3525,-408 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="34422@1" ObjectIDZND0="34423@1" Pin0InfoVect0LinkObjId="SW-220422_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-220421_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3525,-392 3525,-408 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3efd700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3525,-351 3525,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="34428@0" ObjectIDZND0="34422@0" Pin0InfoVect0LinkObjId="SW-220421_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-220422_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3525,-351 3525,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3efd960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3525,-324 3525,-333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="34424@x" ObjectIDND1="g_3f07190@0" ObjectIDND2="g_3efe430@0" ObjectIDZND0="34428@1" Pin0InfoVect0LinkObjId="SW-220422_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-220423_0" Pin1InfoVect1LinkObjId="g_3f07190_0" Pin1InfoVect2LinkObjId="g_3efe430_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3525,-324 3525,-333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3efee90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3572,-277 3572,-296 3526,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3efe430@0" ObjectIDZND0="34428@x" ObjectIDZND1="34424@x" ObjectIDZND2="g_3f07190@0" Pin0InfoVect0LinkObjId="SW-220422_0" Pin0InfoVect1LinkObjId="SW-220423_0" Pin0InfoVect2LinkObjId="g_3f07190_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3efe430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3572,-277 3572,-296 3526,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f06630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3525,-310 3506,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="34428@x" ObjectIDND1="34424@x" ObjectIDND2="g_3efe430@0" ObjectIDZND0="g_3f07190@0" Pin0InfoVect0LinkObjId="g_3f07190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-220422_0" Pin1InfoVect1LinkObjId="SW-220423_0" Pin1InfoVect2LinkObjId="g_3efe430_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3525,-310 3506,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f06fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3525,-310 3525,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3f07190@0" ObjectIDND1="g_3efe430@0" ObjectIDND2="g_3eff0f0@0" ObjectIDZND0="34428@x" ObjectIDZND1="34424@x" Pin0InfoVect0LinkObjId="SW-220422_0" Pin0InfoVect1LinkObjId="SW-220423_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3f07190_0" Pin1InfoVect1LinkObjId="g_3efe430_0" Pin1InfoVect2LinkObjId="g_3eff0f0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3525,-310 3525,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f08060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3525,-276 3525,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3eff0f0@1" ObjectIDZND0="g_3efe430@0" ObjectIDZND1="34428@x" ObjectIDZND2="34424@x" Pin0InfoVect0LinkObjId="g_3efe430_0" Pin0InfoVect1LinkObjId="SW-220422_0" Pin0InfoVect2LinkObjId="SW-220423_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3eff0f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3525,-276 3525,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f082c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3525,-296 3525,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3efe430@0" ObjectIDND1="g_3eff0f0@0" ObjectIDZND0="34428@x" ObjectIDZND1="34424@x" ObjectIDZND2="g_3f07190@0" Pin0InfoVect0LinkObjId="SW-220422_0" Pin0InfoVect1LinkObjId="SW-220423_0" Pin0InfoVect2LinkObjId="g_3f07190_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3efe430_0" Pin1InfoVect1LinkObjId="g_3eff0f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3525,-296 3525,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3f08520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3525,-183 3525,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_3eff0f0@0" Pin0InfoVect0LinkObjId="g_3eff0f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3525,-183 3525,-223 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="7273" cx="4443" cy="-819" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="5004" cy="-817" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10505" cx="4703" cy="-443" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10505" cx="4829" cy="-443" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10505" cx="4663" cy="-443" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10505" cx="4875" cy="-443" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10505" cx="4953" cy="-443" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7273" cx="4271" cy="-819" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7273" cx="4601" cy="-819" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10505" cx="4569" cy="-443" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10505" cx="5163" cy="-443" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10505" cx="4508" cy="-443" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7273" cx="4049" cy="-819" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7274" cx="4399" cy="-443" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7274" cx="3652" cy="-443" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7274" cx="3778" cy="-443" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7274" cx="3902" cy="-443" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7274" cx="4152" cy="-443" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7274" cx="3738" cy="-443" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7274" cx="4110" cy="-443" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7274" cx="4030" cy="-443" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7274" cx="4289" cy="-443" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7274" cx="3525" cy="-443" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-37317" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3445.000000 -1074.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5899" ObjectName="DYN-CX_FT"/>
     <cge:Meas_Ref ObjectId="37317"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3f1c440" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4280.000000 -1217.000000) translate(0,12)">紫凤线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3602330" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3866.000000 -158.000000) translate(0,15)">凤屯III回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3602e00" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4226.000000 -175.000000) translate(0,15)">35kV1号场用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_264aec0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3734.000000 -158.000000) translate(0,15)">凤屯II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_264b4e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3608.000000 -158.000000) translate(0,15)">凤屯I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_40124f0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3977.000000 -91.000000) translate(0,15)">1号补偿变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_233e850" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4104.000000 -64.000000) translate(0,15)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_343ae80" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4930.000000 -842.000000) translate(0,12)">35kV飒马场变10kV凤屯线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_343c2d0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3670.000000 -663.000000) translate(0,15)">35kVI段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_343c830" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4443.000000 -311.000000) translate(0,15)">分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e90900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4057.870857 -955.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e90e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4055.870857 -874.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e910d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4055.870857 -1009.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e91310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3986.870857 -928.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e91550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3994.870857 -994.000000) translate(0,12)">10160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e91790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4041.000000 -637.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e919d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4280.000000 -944.000000) translate(0,12)">161</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e91c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4278.000000 -872.000000) translate(0,12)">1611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e91e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4278.000000 -1009.000000) translate(0,12)">1616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e92090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4209.000000 -929.000000) translate(0,12)">16117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e922d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4215.000000 -994.000000) translate(0,12)">16160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e92510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4215.000000 -1066.000000) translate(0,12)">16167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e92750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4450.000000 -919.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e92990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4389.000000 -882.000000) translate(0,12)">19010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e92bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4392.000000 -953.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e92e10" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4404.000000 -996.000000) translate(0,12)">110kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e93240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4126.870857 -539.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22d16b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3948.000000 -73.000000) translate(0,15)">动态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22d1e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4031.870857 -758.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22d2700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3171.000000 -568.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22d2700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3171.000000 -568.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22d2700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3171.000000 -568.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22d2700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3171.000000 -568.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22d2700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3171.000000 -568.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22d2700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3171.000000 -568.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22d2700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3171.000000 -568.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22d2700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3171.000000 -568.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22d2700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3171.000000 -568.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22d2700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3171.000000 -568.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22d2700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3171.000000 -568.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22d2700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3171.000000 -568.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22d2700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3171.000000 -568.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22d2700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3171.000000 -568.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22d2700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3171.000000 -568.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22d2700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3171.000000 -568.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22d2700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3171.000000 -568.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22d2700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3171.000000 -568.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22d2a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -1047.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22d2a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -1047.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22d2a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -1047.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22d2a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -1047.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22d2a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -1047.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22d2a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -1047.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22d2a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3163.000000 -1047.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_22d3820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3305.000000 -1154.500000) translate(0,16)">凤屯升压站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35dcfe0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4906.000000 -158.000000) translate(0,15)">大尖峰III回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35ddc20" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4785.000000 -158.000000) translate(0,15)">大尖峰II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_35ddea0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4662.000000 -158.000000) translate(0,15)">大尖峰I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3eba360" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5144.000000 -40.000000) translate(0,15)">2号补偿变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2508320" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5007.000000 -18.000000) translate(0,15)">2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29c76a0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5120.000000 -18.000000) translate(0,15)">动态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_34ad4a0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4807.000000 -665.000000) translate(0,15)">35kVII段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b12c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4610.000000 -944.000000) translate(0,12)">102</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b1850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4608.000000 -872.000000) translate(0,12)">1021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b1a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4608.000000 -1007.000000) translate(0,12)">1026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b1cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4550.000000 -927.000000) translate(0,12)">10217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34b1f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4550.000000 -991.000000) translate(0,12)">10260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_235bb40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4672.000000 -538.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_235c030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3664.000000 -384.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_235c270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3679.000000 -349.000000) translate(0,12)">36137</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_235c4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3787.000000 -383.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_235c6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3805.000000 -347.000000) translate(0,12)">36237</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_235c930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3911.000000 -383.000000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_235cb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3929.000000 -347.000000) translate(0,12)">36337</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_235cdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4040.000000 -387.000000) translate(0,12)">364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_235cff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4037.000000 -239.000000) translate(0,12)">6</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_235d230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4057.000000 -346.000000) translate(0,12)">36437</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_235d470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4056.000000 -230.000000) translate(0,12)">36467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_235d6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4161.000000 -386.000000) translate(0,12)">365</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_235d8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4180.000000 -347.000000) translate(0,12)">36537</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_235db30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4178.000000 -230.000000) translate(0,12)">36567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_235dd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4159.000000 -240.000000) translate(0,12)">6</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_235dfb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4299.000000 -388.000000) translate(0,12)">366</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_235e1f0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4323.000000 -118.000000) translate(0,15)">户外小电阻柜1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_235f390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4411.000000 -381.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_235f610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4517.000000 -372.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_235f850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4578.000000 -387.000000) translate(0,12)">386</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_235fa90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4712.000000 -387.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_235fcd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4730.000000 -351.000000) translate(0,12)">38137</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_235ff10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4838.000000 -385.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2360150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4856.000000 -349.000000) translate(0,12)">38237</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2360390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4962.000000 -385.000000) translate(0,12)">383</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23605d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5172.000000 -388.000000) translate(0,12)">384</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2360810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5170.000000 -210.000000) translate(0,12)">6</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2360a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5191.000000 -349.000000) translate(0,12)">38437</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2360c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5189.000000 -200.000000) translate(0,12)">38467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2360ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5061.000000 -212.000000) translate(0,12)">3856</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2361110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5072.000000 -194.000000) translate(0,12)">38567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2361350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4968.000000 -349.000000) translate(0,12)">38337</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_358a3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3580.000000 -463.000000) translate(0,12)">35kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_358a5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4488.000000 -463.000000) translate(0,12)">35kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_358a840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3861.000000 -838.000000) translate(0,12)">110kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_358baf0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4529.000000 -107.000000) translate(0,15)">户外小电阻柜2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_358bd00" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4951.000000 -566.000000) translate(0,15)">10kV2号场用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_358bf50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4585.000000 -760.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_358c180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4158.000000 -760.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_358c180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4158.000000 -760.000000) translate(0,33)">SZ11-50000/121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_358c180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4158.000000 -760.000000) translate(0,51)">121±8×1.25%/36.75kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_358c180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4158.000000 -760.000000) translate(0,69)">50000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_358c180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4158.000000 -760.000000) translate(0,87)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_358c180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4158.000000 -760.000000) translate(0,105)">Ud%=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_358c7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4700.000000 -776.000000) translate(0,15)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_358c7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4700.000000 -776.000000) translate(0,33)">SZ11-50000/121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_358c7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4700.000000 -776.000000) translate(0,51)">121±8×1.25%/36.75kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_358c7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4700.000000 -776.000000) translate(0,69)">50000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_358c7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4700.000000 -776.000000) translate(0,87)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_358c7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4700.000000 -776.000000) translate(0,105)">Ud%=10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_358e820" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4255.000000 -1258.000000) translate(0,12)">JL/GIA-240/30-24/7</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3591460" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4122.000000 -635.000000) translate(0,15)">YJV-1×240</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3592650" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3589.000000 -138.000000) translate(0,12)">(1--10号发电机）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3593580" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3711.000000 -138.000000) translate(0,12)">(11--22号发电机）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3594550" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3848.000000 -137.000000) translate(0,12)">(23--33号发电机）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3594790" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3969.000000 -52.000000) translate(0,15)">(-3--3MVar）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3594b60" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4650.000000 -140.000000) translate(0,12)">(1--12号发电机）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3594f90" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4771.000000 -140.000000) translate(0,12)">(13--23号发电机）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35951d0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4901.000000 -140.000000) translate(0,12)">(24--33号发电机）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3595410" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4122.000000 -47.000000) translate(0,15)">(6MVar）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3595640" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4207.000000 -264.000000) translate(0,15)">660KVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3595880" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4593.000000 -281.000000) translate(0,15)">500KVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3595ac0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5183.000000 -133.000000) translate(0,15)">7500KVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3595d00" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5130.000000 3.000000) translate(0,15)">(-7.5--7.5MVar）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3595f50" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5016.000000 2.000000) translate(0,15)">(4.5MVar）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_2aa0f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3159.000000 -759.000000) translate(0,15)">全站检修停电前应挂“全站检修”牌，“禁止刷新”牌</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_2aa0f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3159.000000 -759.000000) translate(0,33)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_2aa0f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3159.000000 -759.000000) translate(0,51)">全站检修完工后仅可摘除“禁止刷新”牌</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_2aa0f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3159.000000 -759.000000) translate(0,69)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_2aa0f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3159.000000 -759.000000) translate(0,87)">全站检修复电后才可以摘除“全站检修”牌</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aa1960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4598.000000 -702.000000) translate(0,12)">1020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_33ba040" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3276.000000 -215.000000) translate(0,15)">15987867510</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3efdbc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3537.000000 -387.000000) translate(0,12)">367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3efe1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3548.000000 -350.000000) translate(0,12)">36767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f05740" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3502.000000 -144.000000) translate(0,15)">凤飒线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3f08780" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3174.000000 -860.000000) translate(0,15)">风场实时功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_264f7e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5036.000000 -663.000000) translate(0,15)">160KVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_264fe10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3557.000000 -1077.000000) translate(0,16)">AGC/AVC</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3138" y="-629"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3150" y="-1069"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3150" y="-1189"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="36" stroke="rgb(212,170,191)" stroke-width="0.446429" width="15" x="4354" y="-187"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="36" stroke="rgb(212,170,191)" stroke-width="0.446429" width="15" x="4613" y="-180"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_FT.CX_FT_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3469,-443 4430,-443 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="7274" ObjectName="BS-CX_FT.CX_FT_3IM"/>
    <cge:TPSR_Ref TObjectID="7274"/></metadata>
   <polyline fill="none" opacity="0" points="3469,-443 4430,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_FT.CX_FT_1IM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3861,-819 4715,-819 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="7273" ObjectName="BS-CX_FT.CX_FT_1IM"/>
    <cge:TPSR_Ref TObjectID="7273"/></metadata>
   <polyline fill="none" opacity="0" points="3861,-819 4715,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4927,-817 5093,-817 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4927,-817 5093,-817 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_FT.CX_FT_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4488,-443 5220,-443 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="10505" ObjectName="BS-CX_FT.CX_FT_3IIM"/>
    <cge:TPSR_Ref TObjectID="10505"/></metadata>
   <polyline fill="none" opacity="0" points="4488,-443 5220,-443 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_29bece0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4125.757918 -71.000000)" xlink:href="#lightningRod:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_343cdf0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3977.000000 -658.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34e0490">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4183.000000 -1101.000000)" xlink:href="#lightningRod:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f1a610">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4043.268228 -194.000000)" xlink:href="#lightningRod:shape115"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2667c20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4138.757918 -148.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26688f0">
    <use class="BV-35KV" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 3818.000000 -173.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2669660">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3941.000000 -173.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_266a3d0">
    <use class="BV-35KV" transform="matrix(0.928571 -0.000000 0.000000 1.000000 3686.000000 -575.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_266b140">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3747.864137 -588.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b55f50">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4296.000000 -1067.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b56c60">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4018.000000 -657.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b5a960">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4999.000000 -746.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b5b770">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4999.000000 -679.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_231e3a0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4508.000000 -658.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35daba0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4549.000000 -657.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2505a10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5027.757918 -26.000000)" xlink:href="#lightningRod:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3e8e880">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 5176.268228 -151.000000)" xlink:href="#lightningRod:shape115"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29c3580">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5040.757918 -108.000000)" xlink:href="#lightningRod:shape105"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29c41d0">
    <use class="BV-35KV" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4869.000000 -175.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29c4f40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4992.000000 -175.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29c5cb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4742.000000 -177.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29c6a20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4698.000000 -258.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29c7e20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4013.464407 -102.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29c8920">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5146.464407 -50.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34adae0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4883.864137 -591.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_358cfa0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3691.000000 -175.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_358dad0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4822.000000 -515.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_358f740">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4430.000000 -981.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3596380">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4357.000000 -193.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3596aa0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4616.000000 -185.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3597bb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3711.000000 -599.000000)" xlink:href="#lightningRod:shape151"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_359a200">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4847.000000 -610.000000)" xlink:href="#lightningRod:shape151"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_359e270">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3647.000000 -257.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_359ec90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3773.000000 -255.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_359f6b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3897.000000 -255.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32b1de0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4025.000000 -254.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32b2670">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4147.000000 -255.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32b3090">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4824.000000 -258.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32b60b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4948.000000 -256.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32b6ad0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5158.000000 -252.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32b8330">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4548.000000 -235.000000)" xlink:href="#lightningRod:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3efe430">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3565.000000 -219.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3eff0f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3520.000000 -218.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3f07190">
    <use class="BV-35KV" transform="matrix(-0.000000 -0.724138 0.680660 -0.000000 3473.340330 -300.362069)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-43461" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4038.000000 -1138.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43461" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7284"/>
     <cge:Term_Ref ObjectID="10531"/>
    <cge:TPSR_Ref TObjectID="7284"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-43462" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4038.000000 -1138.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43462" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7284"/>
     <cge:Term_Ref ObjectID="10531"/>
    <cge:TPSR_Ref TObjectID="7284"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-43463" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4038.000000 -1138.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43463" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7284"/>
     <cge:Term_Ref ObjectID="10531"/>
    <cge:TPSR_Ref TObjectID="7284"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-43449" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4325.000000 -1142.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43449" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7275"/>
     <cge:Term_Ref ObjectID="10513"/>
    <cge:TPSR_Ref TObjectID="7275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-43450" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4325.000000 -1142.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43450" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7275"/>
     <cge:Term_Ref ObjectID="10513"/>
    <cge:TPSR_Ref TObjectID="7275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-43451" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4325.000000 -1142.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43451" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7275"/>
     <cge:Term_Ref ObjectID="10513"/>
    <cge:TPSR_Ref TObjectID="7275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-56882" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4682.000000 -952.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56882" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10477"/>
     <cge:Term_Ref ObjectID="14591"/>
    <cge:TPSR_Ref TObjectID="10477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-56883" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4682.000000 -952.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56883" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10477"/>
     <cge:Term_Ref ObjectID="14591"/>
    <cge:TPSR_Ref TObjectID="10477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-56881" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4682.000000 -952.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56881" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10477"/>
     <cge:Term_Ref ObjectID="14591"/>
    <cge:TPSR_Ref TObjectID="10477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-43489" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3649.000000 -112.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43489" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7304"/>
     <cge:Term_Ref ObjectID="10571"/>
    <cge:TPSR_Ref TObjectID="7304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-43490" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3649.000000 -112.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43490" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7304"/>
     <cge:Term_Ref ObjectID="10571"/>
    <cge:TPSR_Ref TObjectID="7304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-43491" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3649.000000 -112.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43491" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7304"/>
     <cge:Term_Ref ObjectID="10571"/>
    <cge:TPSR_Ref TObjectID="7304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-43485" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3760.000000 -114.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43485" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7302"/>
     <cge:Term_Ref ObjectID="10567"/>
    <cge:TPSR_Ref TObjectID="7302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-43486" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3760.000000 -114.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43486" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7302"/>
     <cge:Term_Ref ObjectID="10567"/>
    <cge:TPSR_Ref TObjectID="7302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-43487" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3760.000000 -114.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43487" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7302"/>
     <cge:Term_Ref ObjectID="10567"/>
    <cge:TPSR_Ref TObjectID="7302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-43481" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3877.000000 -116.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43481" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7300"/>
     <cge:Term_Ref ObjectID="10563"/>
    <cge:TPSR_Ref TObjectID="7300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-43482" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3877.000000 -116.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43482" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7300"/>
     <cge:Term_Ref ObjectID="10563"/>
    <cge:TPSR_Ref TObjectID="7300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-43483" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3877.000000 -116.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43483" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7300"/>
     <cge:Term_Ref ObjectID="10563"/>
    <cge:TPSR_Ref TObjectID="7300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-43477" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4004.000000 -19.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43477" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7296"/>
     <cge:Term_Ref ObjectID="10555"/>
    <cge:TPSR_Ref TObjectID="7296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-43478" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4004.000000 -19.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43478" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7296"/>
     <cge:Term_Ref ObjectID="10555"/>
    <cge:TPSR_Ref TObjectID="7296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-43479" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4004.000000 -19.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43479" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7296"/>
     <cge:Term_Ref ObjectID="10555"/>
    <cge:TPSR_Ref TObjectID="7296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-43473" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4137.000000 -19.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43473" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7292"/>
     <cge:Term_Ref ObjectID="10547"/>
    <cge:TPSR_Ref TObjectID="7292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-43474" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4137.000000 -19.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43474" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7292"/>
     <cge:Term_Ref ObjectID="10547"/>
    <cge:TPSR_Ref TObjectID="7292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-43475" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4137.000000 -19.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43475" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7292"/>
     <cge:Term_Ref ObjectID="10547"/>
    <cge:TPSR_Ref TObjectID="7292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-56890" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4463.000000 -274.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56890" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10484"/>
     <cge:Term_Ref ObjectID="14605"/>
    <cge:TPSR_Ref TObjectID="10484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-56891" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4463.000000 -274.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56891" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10484"/>
     <cge:Term_Ref ObjectID="14605"/>
    <cge:TPSR_Ref TObjectID="10484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-56889" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4463.000000 -274.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56889" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10484"/>
     <cge:Term_Ref ObjectID="14605"/>
    <cge:TPSR_Ref TObjectID="10484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-56907" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4676.000000 -115.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56907" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10485"/>
     <cge:Term_Ref ObjectID="14607"/>
    <cge:TPSR_Ref TObjectID="10485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-56903" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4676.000000 -115.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56903" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10485"/>
     <cge:Term_Ref ObjectID="14607"/>
    <cge:TPSR_Ref TObjectID="10485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-56901" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4676.000000 -115.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56901" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10485"/>
     <cge:Term_Ref ObjectID="14607"/>
    <cge:TPSR_Ref TObjectID="10485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-56902" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4807.000000 -115.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56902" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10486"/>
     <cge:Term_Ref ObjectID="14609"/>
    <cge:TPSR_Ref TObjectID="10486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-56908" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4807.000000 -115.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56908" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10486"/>
     <cge:Term_Ref ObjectID="14609"/>
    <cge:TPSR_Ref TObjectID="10486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-56904" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4807.000000 -115.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56904" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10486"/>
     <cge:Term_Ref ObjectID="14609"/>
    <cge:TPSR_Ref TObjectID="10486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-56906" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4933.000000 -117.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56906" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10487"/>
     <cge:Term_Ref ObjectID="14611"/>
    <cge:TPSR_Ref TObjectID="10487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-56909" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4933.000000 -117.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56909" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10487"/>
     <cge:Term_Ref ObjectID="14611"/>
    <cge:TPSR_Ref TObjectID="10487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-56905" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4933.000000 -117.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56905" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10487"/>
     <cge:Term_Ref ObjectID="14611"/>
    <cge:TPSR_Ref TObjectID="10487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-56898" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5219.000000 -82.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56898" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10488"/>
     <cge:Term_Ref ObjectID="14613"/>
    <cge:TPSR_Ref TObjectID="10488"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-56899" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5219.000000 -82.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56899" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10488"/>
     <cge:Term_Ref ObjectID="14613"/>
    <cge:TPSR_Ref TObjectID="10488"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-56897" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5219.000000 -82.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56897" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10488"/>
     <cge:Term_Ref ObjectID="14613"/>
    <cge:TPSR_Ref TObjectID="10488"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-43454" prefix="Ua  " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3763.000000 -854.000000) translate(0,12)">Ua   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43454" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7273"/>
     <cge:Term_Ref ObjectID="10509"/>
    <cge:TPSR_Ref TObjectID="7273"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-43455" prefix="Ub " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3763.000000 -854.000000) translate(0,27)">Ub  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43455" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7273"/>
     <cge:Term_Ref ObjectID="10509"/>
    <cge:TPSR_Ref TObjectID="7273"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-43456" prefix="Uc " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3763.000000 -854.000000) translate(0,42)">Uc  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43456" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7273"/>
     <cge:Term_Ref ObjectID="10509"/>
    <cge:TPSR_Ref TObjectID="7273"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-43457" prefix="Uab " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3763.000000 -854.000000) translate(0,57)">Uab  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43457" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7273"/>
     <cge:Term_Ref ObjectID="10509"/>
    <cge:TPSR_Ref TObjectID="7273"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-43460" prefix="Hz " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3763.000000 -854.000000) translate(0,72)">Hz  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43460" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7273"/>
     <cge:Term_Ref ObjectID="10509"/>
    <cge:TPSR_Ref TObjectID="7273"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-43469" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4264.000000 -114.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43469" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7291"/>
     <cge:Term_Ref ObjectID="10545"/>
    <cge:TPSR_Ref TObjectID="7291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-43470" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4264.000000 -114.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43470" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7291"/>
     <cge:Term_Ref ObjectID="10545"/>
    <cge:TPSR_Ref TObjectID="7291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-43471" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4264.000000 -114.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43471" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7291"/>
     <cge:Term_Ref ObjectID="10545"/>
    <cge:TPSR_Ref TObjectID="7291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-43493" prefix="Ua  " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3555.000000 -537.000000) translate(0,12)">Ua   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43493" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7274"/>
     <cge:Term_Ref ObjectID="10510"/>
    <cge:TPSR_Ref TObjectID="7274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-43494" prefix="Ub " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3555.000000 -537.000000) translate(0,27)">Ub  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43494" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7274"/>
     <cge:Term_Ref ObjectID="10510"/>
    <cge:TPSR_Ref TObjectID="7274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-43495" prefix="Uc " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3555.000000 -537.000000) translate(0,42)">Uc  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43495" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7274"/>
     <cge:Term_Ref ObjectID="10510"/>
    <cge:TPSR_Ref TObjectID="7274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-43496" prefix="Uab " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3555.000000 -537.000000) translate(0,57)">Uab  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43496" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7274"/>
     <cge:Term_Ref ObjectID="10510"/>
    <cge:TPSR_Ref TObjectID="7274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-43499" prefix="Hz " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3555.000000 -537.000000) translate(0,72)">Hz  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43499" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7274"/>
     <cge:Term_Ref ObjectID="10510"/>
    <cge:TPSR_Ref TObjectID="7274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-56892" prefix="Ua  " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5113.000000 -518.000000) translate(0,12)">Ua   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56892" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10505"/>
     <cge:Term_Ref ObjectID="14647"/>
    <cge:TPSR_Ref TObjectID="10505"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-56893" prefix="Ub " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5113.000000 -518.000000) translate(0,27)">Ub  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56893" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10505"/>
     <cge:Term_Ref ObjectID="14647"/>
    <cge:TPSR_Ref TObjectID="10505"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-56894" prefix="Uc " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5113.000000 -518.000000) translate(0,42)">Uc  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56894" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10505"/>
     <cge:Term_Ref ObjectID="14647"/>
    <cge:TPSR_Ref TObjectID="10505"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-56895" prefix="Uab " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5113.000000 -518.000000) translate(0,57)">Uab  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56895" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10505"/>
     <cge:Term_Ref ObjectID="14647"/>
    <cge:TPSR_Ref TObjectID="10505"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-56896" prefix="Hz " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5113.000000 -518.000000) translate(0,72)">Hz  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56896" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10505"/>
     <cge:Term_Ref ObjectID="14647"/>
    <cge:TPSR_Ref TObjectID="10505"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-43465" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4238.000000 -555.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43465" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7290"/>
     <cge:Term_Ref ObjectID="10543"/>
    <cge:TPSR_Ref TObjectID="7290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-43466" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4238.000000 -555.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43466" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7290"/>
     <cge:Term_Ref ObjectID="10543"/>
    <cge:TPSR_Ref TObjectID="7290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-43467" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4238.000000 -555.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43467" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7290"/>
     <cge:Term_Ref ObjectID="10543"/>
    <cge:TPSR_Ref TObjectID="7290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-56886" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4702.000000 -555.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56886" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10483"/>
     <cge:Term_Ref ObjectID="14603"/>
    <cge:TPSR_Ref TObjectID="10483"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-56887" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4702.000000 -555.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56887" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10483"/>
     <cge:Term_Ref ObjectID="14603"/>
    <cge:TPSR_Ref TObjectID="10483"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-56885" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4702.000000 -555.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="56885" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10483"/>
     <cge:Term_Ref ObjectID="14603"/>
    <cge:TPSR_Ref TObjectID="10483"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-220449" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3505.000000 -113.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="220449" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34422"/>
     <cge:Term_Ref ObjectID="44551"/>
    <cge:TPSR_Ref TObjectID="34422"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-220450" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3505.000000 -113.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="220450" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34422"/>
     <cge:Term_Ref ObjectID="44551"/>
    <cge:TPSR_Ref TObjectID="34422"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-220446" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3505.000000 -113.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="220446" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="34422"/>
     <cge:Term_Ref ObjectID="44551"/>
    <cge:TPSR_Ref TObjectID="34422"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="AVC凤屯风电场.svg" style="fill-opacity:0"><rect height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="3531" y="-1085"/></g>
   <g href="cx_索引_接线图_地调直调_风电.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3279" y="-1165"/></g>
   <g href="cx_索引_接线图_地调直调_风电.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3230" y="-1182"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e93650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3982.000000 1137.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e93910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3971.000000 1122.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e93b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3996.000000 1107.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e93f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4185.000000 554.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e94230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4174.000000 539.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e94470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4199.000000 524.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3588630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4396.000000 273.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35888c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4385.000000 258.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3588b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4410.000000 243.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a9d7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3429.000000 114.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a9da50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3418.000000 99.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a9dc90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3443.000000 84.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.095238 -0.000000 0.000000 -0.979798 4260.000000 -181.000000)" xlink:href="#transformer2:shape33_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.095238 -0.000000 0.000000 -0.979798 4260.000000 -181.000000)" xlink:href="#transformer2:shape33_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4989.000000 -579.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4989.000000 -579.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_FT.CX_FT_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="10642"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4072.620948 -670.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4072.620948 -670.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="7309" ObjectName="TF-CX_FT.CX_FT_1T"/>
    <cge:TPSR_Ref TObjectID="7309"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_FT.CX_FT_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="14589"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4625.620948 -669.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4625.620948 -669.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="10476" ObjectName="TF-CX_FT.CX_FT_2T"/>
    <cge:TPSR_Ref TObjectID="10476"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="3531,-1085 3528,-1088 3528,-1034 3531,-1037 3531,-1085" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="3531,-1085 3528,-1088 3677,-1088 3674,-1085 3531,-1085" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(112,119,119)" points="3531,-1037 3528,-1034 3677,-1034 3674,-1037 3531,-1037" stroke="rgb(112,119,119)"/>
     <polygon fill="rgb(112,119,119)" points="3674,-1085 3677,-1088 3677,-1034 3674,-1037 3674,-1085" stroke="rgb(112,119,119)"/>
     <rect fill="rgb(224,238,238)" height="48" stroke="rgb(224,238,238)" width="143" x="3531" y="-1085"/>
     <rect fill="none" height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="3531" y="-1085"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3279" y="-1165"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3279" y="-1165"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3230" y="-1182"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3230" y="-1182"/></g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-43653">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4040.000000 -842.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7285" ObjectName="SW-CX_FT.CX_FT_1011SW"/>
     <cge:Meas_Ref ObjectId="43653"/>
    <cge:TPSR_Ref TObjectID="7285"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43659">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4099.870857 -474.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15730" ObjectName="SW-CX_FT.CX_FT_301XC1"/>
     <cge:Meas_Ref ObjectId="43659"/>
    <cge:TPSR_Ref TObjectID="15730"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43659">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4099.870857 -558.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10578" ObjectName="SW-CX_FT.CX_FT_301XC"/>
     <cge:Meas_Ref ObjectId="43659"/>
    <cge:TPSR_Ref TObjectID="10578"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43654">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4040.000000 -977.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7286" ObjectName="SW-CX_FT.CX_FT_1016SW"/>
     <cge:Meas_Ref ObjectId="43654"/>
    <cge:TPSR_Ref TObjectID="7286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43645">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4262.000000 -840.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7277" ObjectName="SW-CX_FT.CX_FT_1611SW"/>
     <cge:Meas_Ref ObjectId="43645"/>
    <cge:TPSR_Ref TObjectID="7277"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43648">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.857143 4217.000000 -898.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7280" ObjectName="SW-CX_FT.CX_FT_16117SW"/>
     <cge:Meas_Ref ObjectId="43648"/>
    <cge:TPSR_Ref TObjectID="7280"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43644">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4262.000000 -977.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7276" ObjectName="SW-CX_FT.CX_FT_1616SW"/>
     <cge:Meas_Ref ObjectId="43644"/>
    <cge:TPSR_Ref TObjectID="7276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43649">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4434.000000 -864.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7281" ObjectName="SW-CX_FT.CX_FT_1901SW"/>
     <cge:Meas_Ref ObjectId="43649"/>
    <cge:TPSR_Ref TObjectID="7281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43680">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.021739 -0.000000 0.000000 -1.000000 3664.000000 -318.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7305" ObjectName="SW-CX_FT.CX_FT_36137SW"/>
     <cge:Meas_Ref ObjectId="43680"/>
    <cge:TPSR_Ref TObjectID="7305"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43683">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4392.325421 -345.000000)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7308" ObjectName="SW-CX_FT.CX_FT_3121XC"/>
     <cge:Meas_Ref ObjectId="43683"/>
    <cge:TPSR_Ref TObjectID="7308"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43669">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.021739 -0.000000 0.000000 -1.000000 4043.000000 -315.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7297" ObjectName="SW-CX_FT.CX_FT_36437SW"/>
     <cge:Meas_Ref ObjectId="43669"/>
    <cge:TPSR_Ref TObjectID="7297"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43664">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.021739 -0.000000 0.000000 -1.000000 4166.000000 -316.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7293" ObjectName="SW-CX_FT.CX_FT_36537SW"/>
     <cge:Meas_Ref ObjectId="43664"/>
    <cge:TPSR_Ref TObjectID="7293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43670">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 4022.000000 -209.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7298" ObjectName="SW-CX_FT.CX_FT_3646SW"/>
     <cge:Meas_Ref ObjectId="43670"/>
    <cge:TPSR_Ref TObjectID="7298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43671">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.021739 -0.000000 0.000000 -1.000000 4041.000000 -199.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7299" ObjectName="SW-CX_FT.CX_FT_36467SW"/>
     <cge:Meas_Ref ObjectId="43671"/>
    <cge:TPSR_Ref TObjectID="7299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43665">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4143.000000 -210.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7294" ObjectName="SW-CX_FT.CX_FT_3656SW"/>
     <cge:Meas_Ref ObjectId="43665"/>
    <cge:TPSR_Ref TObjectID="7294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43666">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.021739 -0.000000 0.000000 -1.000000 4163.000000 -199.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7295" ObjectName="SW-CX_FT.CX_FT_36567SW"/>
     <cge:Meas_Ref ObjectId="43666"/>
    <cge:TPSR_Ref TObjectID="7295"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43657">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4054.000000 -674.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7289" ObjectName="SW-CX_FT.CX_FT_1010SW"/>
     <cge:Meas_Ref ObjectId="43657"/>
    <cge:TPSR_Ref TObjectID="7289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43655">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.021739 -0.000000 0.000000 -0.857143 3995.000000 -897.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7287" ObjectName="SW-CX_FT.CX_FT_10117SW"/>
     <cge:Meas_Ref ObjectId="43655"/>
    <cge:TPSR_Ref TObjectID="7287"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43646">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.857143 4217.000000 -1033.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7278" ObjectName="SW-CX_FT.CX_FT_16167SW"/>
     <cge:Meas_Ref ObjectId="43646"/>
    <cge:TPSR_Ref TObjectID="7278"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43656">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.043478 -0.000000 0.000000 -0.857143 3995.000000 -961.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7288" ObjectName="SW-CX_FT.CX_FT_10160SW"/>
     <cge:Meas_Ref ObjectId="43656"/>
    <cge:TPSR_Ref TObjectID="7288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43647">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.857143 4217.000000 -961.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7279" ObjectName="SW-CX_FT.CX_FT_16160SW"/>
     <cge:Meas_Ref ObjectId="43647"/>
    <cge:TPSR_Ref TObjectID="7279"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43650">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.857143 4389.000000 -849.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7282" ObjectName="SW-CX_FT.CX_FT_19010SW"/>
     <cge:Meas_Ref ObjectId="43650"/>
    <cge:TPSR_Ref TObjectID="7282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43651">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.857143 4390.000000 -919.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7283" ObjectName="SW-CX_FT.CX_FT_19017SW"/>
     <cge:Meas_Ref ObjectId="43651"/>
    <cge:TPSR_Ref TObjectID="7283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43679">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3641.983729 -326.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27596" ObjectName="SW-CX_FT.CX_FT_361XC1"/>
     <cge:Meas_Ref ObjectId="43679"/>
    <cge:TPSR_Ref TObjectID="27596"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43677">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.021739 -0.000000 0.000000 -1.000000 3790.000000 -316.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7303" ObjectName="SW-CX_FT.CX_FT_36237SW"/>
     <cge:Meas_Ref ObjectId="43677"/>
    <cge:TPSR_Ref TObjectID="7303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43674">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.021739 -0.000000 0.000000 -1.000000 3914.000000 -316.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7301" ObjectName="SW-CX_FT.CX_FT_36337SW"/>
     <cge:Meas_Ref ObjectId="43674"/>
    <cge:TPSR_Ref TObjectID="7301"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43682">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3747.864137 -533.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23696" ObjectName="SW-CX_FT.CX_FT_3ITVXC"/>
     <cge:Meas_Ref ObjectId="43682"/>
    <cge:TPSR_Ref TObjectID="23696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56853">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4592.000000 -842.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10478" ObjectName="SW-CX_FT.CX_FT_1021SW"/>
     <cge:Meas_Ref ObjectId="56853"/>
    <cge:TPSR_Ref TObjectID="10478"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56860">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4652.620948 -473.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15731" ObjectName="SW-CX_FT.CX_FT_302XC1"/>
     <cge:Meas_Ref ObjectId="56860"/>
    <cge:TPSR_Ref TObjectID="15731"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56860">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4652.620948 -558.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10585" ObjectName="SW-CX_FT.CX_FT_302XC"/>
     <cge:Meas_Ref ObjectId="56860"/>
    <cge:TPSR_Ref TObjectID="10585"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56857">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4585.000000 -674.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10482" ObjectName="SW-CX_FT.CX_FT_1020SW"/>
     <cge:Meas_Ref ObjectId="56857"/>
    <cge:TPSR_Ref TObjectID="10482"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56855">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.021739 -0.000000 0.000000 -0.857143 4547.000000 -897.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10480" ObjectName="SW-CX_FT.CX_FT_10217SW"/>
     <cge:Meas_Ref ObjectId="56855"/>
    <cge:TPSR_Ref TObjectID="10480"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56856">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.021739 -0.000000 0.000000 -0.857143 4547.000000 -961.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10481" ObjectName="SW-CX_FT.CX_FT_10260SW"/>
     <cge:Meas_Ref ObjectId="56856"/>
    <cge:TPSR_Ref TObjectID="10481"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56873">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.021739 -0.000000 0.000000 -1.000000 4716.000000 -320.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10493" ObjectName="SW-CX_FT.CX_FT_38137SW"/>
     <cge:Meas_Ref ObjectId="56873"/>
    <cge:TPSR_Ref TObjectID="10493"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56878">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.021739 -0.000000 0.000000 -1.000000 5176.000000 -318.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10496" ObjectName="SW-CX_FT.CX_FT_38437SW"/>
     <cge:Meas_Ref ObjectId="56878"/>
    <cge:TPSR_Ref TObjectID="10496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56871">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -1.000000 5046.000000 -182.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10491" ObjectName="SW-CX_FT.CX_FT_3856SW"/>
     <cge:Meas_Ref ObjectId="56871"/>
    <cge:TPSR_Ref TObjectID="10491"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56880">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.021739 -0.000000 0.000000 -1.000000 5063.000000 -168.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10498" ObjectName="SW-CX_FT.CX_FT_38567SW"/>
     <cge:Meas_Ref ObjectId="56880"/>
    <cge:TPSR_Ref TObjectID="10498"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56872">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5154.000000 -180.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10492" ObjectName="SW-CX_FT.CX_FT_3846SW"/>
     <cge:Meas_Ref ObjectId="56872"/>
    <cge:TPSR_Ref TObjectID="10492"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56879">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.021739 -0.000000 0.000000 -1.000000 5174.000000 -169.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10497" ObjectName="SW-CX_FT.CX_FT_38467SW"/>
     <cge:Meas_Ref ObjectId="56879"/>
    <cge:TPSR_Ref TObjectID="10497"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56867">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4692.983729 -402.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15908" ObjectName="SW-CX_FT.CX_FT_381XC"/>
     <cge:Meas_Ref ObjectId="56867"/>
    <cge:TPSR_Ref TObjectID="15908"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56867">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4692.983729 -328.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10586" ObjectName="SW-CX_FT.CX_FT_381XC1"/>
     <cge:Meas_Ref ObjectId="56867"/>
    <cge:TPSR_Ref TObjectID="10586"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56876">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.021739 -0.000000 0.000000 -1.000000 4841.000000 -318.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10494" ObjectName="SW-CX_FT.CX_FT_38237SW"/>
     <cge:Meas_Ref ObjectId="56876"/>
    <cge:TPSR_Ref TObjectID="10494"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56877">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.021739 -0.000000 0.000000 -1.000000 4965.000000 -318.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10495" ObjectName="SW-CX_FT.CX_FT_38337SW"/>
     <cge:Meas_Ref ObjectId="56877"/>
    <cge:TPSR_Ref TObjectID="10495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56875">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4558.983729 -328.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27600" ObjectName="SW-CX_FT.CX_FT_386XC1"/>
     <cge:Meas_Ref ObjectId="56875"/>
    <cge:TPSR_Ref TObjectID="27600"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56861">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4497.983729 -387.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10800" ObjectName="SW-CX_FT.CX_FT_312XC"/>
     <cge:Meas_Ref ObjectId="56861"/>
    <cge:TPSR_Ref TObjectID="10800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56861">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4497.983729 -313.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15901" ObjectName="SW-CX_FT.CX_FT_312XC1"/>
     <cge:Meas_Ref ObjectId="56861"/>
    <cge:TPSR_Ref TObjectID="15901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56862">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4884.864137 -538.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23697" ObjectName="SW-CX_FT.CX_FT_3IITVXC"/>
     <cge:Meas_Ref ObjectId="56862"/>
    <cge:TPSR_Ref TObjectID="23697"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43668">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4020.046332 -403.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15904" ObjectName="SW-CX_FT.CX_FT_364XC"/>
     <cge:Meas_Ref ObjectId="43668"/>
    <cge:TPSR_Ref TObjectID="15904"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43668">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4020.046332 -327.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10581" ObjectName="SW-CX_FT.CX_FT_364XC1"/>
     <cge:Meas_Ref ObjectId="43668"/>
    <cge:TPSR_Ref TObjectID="10581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43661">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4279.046332 -403.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15912" ObjectName="SW-CX_FT.CX_FT_366XC"/>
     <cge:Meas_Ref ObjectId="43661"/>
    <cge:TPSR_Ref TObjectID="15912"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43661">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4279.046332 -328.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10579" ObjectName="SW-CX_FT.CX_FT_366XC1"/>
     <cge:Meas_Ref ObjectId="43661"/>
    <cge:TPSR_Ref TObjectID="10579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43676">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3768.046332 -398.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15902" ObjectName="SW-CX_FT.CX_FT_362XC"/>
     <cge:Meas_Ref ObjectId="43676"/>
    <cge:TPSR_Ref TObjectID="15902"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43673">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3892.046332 -398.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15903" ObjectName="SW-CX_FT.CX_FT_363XC"/>
     <cge:Meas_Ref ObjectId="43673"/>
    <cge:TPSR_Ref TObjectID="15903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43663">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4142.046332 -401.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15905" ObjectName="SW-CX_FT.CX_FT_365XC"/>
     <cge:Meas_Ref ObjectId="43663"/>
    <cge:TPSR_Ref TObjectID="15905"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43676">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3767.983729 -324.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10583" ObjectName="SW-CX_FT.CX_FT_362XC1"/>
     <cge:Meas_Ref ObjectId="43676"/>
    <cge:TPSR_Ref TObjectID="10583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43673">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3891.983729 -324.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10582" ObjectName="SW-CX_FT.CX_FT_363XC1"/>
     <cge:Meas_Ref ObjectId="43673"/>
    <cge:TPSR_Ref TObjectID="10582"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43663">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4141.983729 -328.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10580" ObjectName="SW-CX_FT.CX_FT_365XC1"/>
     <cge:Meas_Ref ObjectId="43663"/>
    <cge:TPSR_Ref TObjectID="10580"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56868">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4818.983729 -400.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10587" ObjectName="SW-CX_FT.CX_FT_382XC"/>
     <cge:Meas_Ref ObjectId="56868"/>
    <cge:TPSR_Ref TObjectID="10587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56869">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4942.983729 -400.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10588" ObjectName="SW-CX_FT.CX_FT_383XC"/>
     <cge:Meas_Ref ObjectId="56869"/>
    <cge:TPSR_Ref TObjectID="10588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56868">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4818.983729 -326.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15909" ObjectName="SW-CX_FT.CX_FT_382XC1"/>
     <cge:Meas_Ref ObjectId="56868"/>
    <cge:TPSR_Ref TObjectID="15909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56869">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4942.983729 -326.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15910" ObjectName="SW-CX_FT.CX_FT_383XC1"/>
     <cge:Meas_Ref ObjectId="56869"/>
    <cge:TPSR_Ref TObjectID="15910"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56854">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4592.000000 -977.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10479" ObjectName="SW-CX_FT.CX_FT_1026SW"/>
     <cge:Meas_Ref ObjectId="56854"/>
    <cge:TPSR_Ref TObjectID="10479"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43679">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3642.046332 -400.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27595" ObjectName="SW-CX_FT.CX_FT_361XC"/>
     <cge:Meas_Ref ObjectId="43679"/>
    <cge:TPSR_Ref TObjectID="27595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56875">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4559.000000 -402.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27599" ObjectName="SW-CX_FT.CX_FT_386XC"/>
     <cge:Meas_Ref ObjectId="56875"/>
    <cge:TPSR_Ref TObjectID="27599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179929">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5153.000000 -403.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27597" ObjectName="SW-CX_FT.CX_FT_384XC"/>
     <cge:Meas_Ref ObjectId="179929"/>
    <cge:TPSR_Ref TObjectID="27597"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-179929">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5153.000000 -329.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27598" ObjectName="SW-CX_FT.CX_FT_384XC1"/>
     <cge:Meas_Ref ObjectId="179929"/>
    <cge:TPSR_Ref TObjectID="27598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-220423">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.021739 -0.000000 0.000000 -1.000000 3537.000000 -319.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34424" ObjectName="SW-CX_FT.CX_FT_36767SW"/>
     <cge:Meas_Ref ObjectId="220423"/>
    <cge:TPSR_Ref TObjectID="34424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-220422">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3514.983729 -327.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34428" ObjectName="SW-CX_FT.CX_FT_367XC1"/>
     <cge:Meas_Ref ObjectId="220422"/>
    <cge:TPSR_Ref TObjectID="34428"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-220422">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3515.046332 -401.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34423" ObjectName="SW-CX_FT.CX_FT_367XC"/>
     <cge:Meas_Ref ObjectId="220422"/>
    <cge:TPSR_Ref TObjectID="34423"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3267.000000 -1106.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-78585" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3287.538462 -1006.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78585" ObjectName="CX_FT:CX_FT_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-79720" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3284.538462 -964.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79720" ObjectName="CX_FT:CX_FT_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-240040" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3312.538462 -860.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="240040" ObjectName="CX_FT:CX_FT_GG_P_821"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_FT"/>
</svg>