<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-101" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3140 -1313 2955 1379">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="99" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape57">
    <polyline points="83,116 83,132 44,132 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="80" x2="87" y1="52" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="74" x2="92" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="83" x2="83" y1="73" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="81" x2="85" y1="48" y2="48"/>
    <circle cx="83" cy="85" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="83" x2="83" y1="101" y2="105"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="83" x2="87" y1="105" y2="109"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="79" x2="83" y1="109" y2="105"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="83" x2="83" y1="81" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="83" x2="87" y1="85" y2="89"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="79" x2="83" y1="89" y2="85"/>
    <ellipse cx="83" cy="103" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="44" x2="44" y1="2" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="77" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="77" x2="77" y1="20" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="67" x2="87" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="67" x2="87" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="77" x2="77" y1="2" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="10" x2="10" y1="20" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="0" x2="20" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="0" x2="20" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="10" x2="10" y1="2" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="44" x2="44" y1="20" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="34" x2="54" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="34" x2="54" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="77" x2="77" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="83" x2="117" y1="85" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="117" x2="117" y1="85" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="77" x2="117" y1="2" y2="2"/>
    <polyline points="44,132 44,148 " stroke-width="1"/>
    <polyline points="44,27 44,132 " stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="59" x2="24" y1="7" y2="7"/>
    <rect height="12" stroke-width="1" width="26" x="18" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="5" x2="5" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="17" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="8" x2="8" y1="12" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape188">
    <polyline DF8003:Layer="PUBLIC" points="19,18 10,0 1,19 19,18 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="-15" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-16,39 6,17 6,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="-15" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-16,39 6,17 6,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape21_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="28" x2="36" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="25" x2="25" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="5" x2="5" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="25" x2="9" y1="23" y2="23"/>
   </symbol>
   <symbol id="switch2:shape21_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="26" x2="26" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="6" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="10" x2="10" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="26" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="3" x2="3" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="36" x2="36" y1="34" y2="14"/>
   </symbol>
   <symbol id="switch2:shape21-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="28" x2="36" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="25" x2="25" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="5" x2="5" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="25" x2="9" y1="23" y2="23"/>
   </symbol>
   <symbol id="switch2:shape21-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="26" x2="26" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="6" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="10" x2="10" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="26" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="3" x2="3" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="36" x2="36" y1="34" y2="14"/>
   </symbol>
   <symbol id="switch2:shape30_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape9_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="2" x2="7" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="2" x2="7" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="2" x2="7" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="5" y1="32" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="12" x2="5" y1="27" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="5" y1="13" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="0" x2="9" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="3" x2="6" y1="2" y2="2"/>
   </symbol>
   <symbol id="switch2:shape9_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="2" x2="7" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="3" x2="6" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="0" x2="9" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="5" y1="13" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" visibility="hidden" x1="12" x2="5" y1="27" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="5" y1="32" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="2" x2="7" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="2" x2="7" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="5" y1="27" y2="13"/>
   </symbol>
   <symbol id="switch2:shape9-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="3" x2="6" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="0" x2="9" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="5" y1="13" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" visibility="hidden" x1="12" x2="5" y1="27" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="5" y1="32" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="2" x2="7" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="2" x2="7" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="2" x2="7" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.85714" x1="3" x2="6" y1="27" y2="13"/>
   </symbol>
   <symbol id="switch2:shape9-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="3" x2="6" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="0" x2="9" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="5" y1="13" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" visibility="hidden" x1="12" x2="5" y1="27" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="5" y1="32" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="2" x2="7" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="2" x2="7" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="2" x2="7" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.85714" x1="3" x2="6" y1="27" y2="13"/>
   </symbol>
   <symbol id="switch2:shape27_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
   </symbol>
   <symbol id="switch2:shape27_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
   </symbol>
   <symbol id="switch2:shape27-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
   </symbol>
   <symbol id="switch2:shape27-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
   </symbol>
   <symbol id="switch2:shape31_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="5" y2="5"/>
    <circle cx="14" cy="21" fillStyle="0" r="4" stroke-width="0.619048"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="2" x2="26" y1="9" y2="33"/>
   </symbol>
   <symbol id="switch2:shape31_1">
    <circle cx="14" cy="21" fillStyle="0" r="4" stroke-width="0.619048"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="14" y1="37" y2="5"/>
   </symbol>
   <symbol id="switch2:shape31-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="5" y2="5"/>
    <circle cx="14" cy="21" fillStyle="0" r="4" stroke-width="0.619048"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="2" x2="26" y1="9" y2="33"/>
   </symbol>
   <symbol id="switch2:shape31-UnNor2">
    <circle cx="14" cy="21" fillStyle="0" r="4" stroke-width="0.619048"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="27" x2="1" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="14" y1="37" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape41_0">
    <circle cx="16" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="56" y2="98"/>
    <polyline DF8003:Layer="PUBLIC" points="15,84 21,71 8,71 15,84 15,83 15,84 "/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="32,42 41,42 41,70 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="35" x2="47" y1="73" y2="73"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="45" x2="37" y1="75" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="44" x2="41" y1="78" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="15" y1="45" y2="72"/>
    <polyline DF8003:Layer="PUBLIC" points="17,11 21,20 11,20 17,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="17" y2="17"/>
   </symbol>
   <symbol id="transformer2:shape41_1">
    <circle cx="17" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="41" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="41" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="12" y1="41" y2="45"/>
   </symbol>
   <symbol id="transformer2:shape58_0">
    <circle cx="15" cy="68" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,65 48,65 48,36 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="22" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="22" y2="22"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,35 33,53 48,53 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="46" x2="50" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="52" y1="34" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="54" x2="42" y1="36" y2="36"/>
    <polyline DF8003:Layer="PUBLIC" points="15,22 9,35 21,35 15,22 15,23 15,22 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="52" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="65" y2="70"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="65" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="10" y1="65" y2="70"/>
   </symbol>
   <symbol id="transformer2:shape58_1">
    <circle cx="15" cy="90" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.255102" x1="12" x2="12" y1="86" y2="97"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.255102" x1="20" x2="12" y1="91" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.255102" x1="20" x2="12" y1="91" y2="97"/>
   </symbol>
   <symbol id="transformer2:shape13_0">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="38" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape13_1">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="46" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="29" y1="34" y2="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape104">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="39" y1="41" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="36" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="38" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="37" y1="43" y2="43"/>
    <ellipse cx="8" cy="12" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="8" cy="24" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="10" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="4" y1="10" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="10" y2="14"/>
    <ellipse cx="19" cy="12" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <ellipse cx="19" cy="24" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="44" y1="19" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="44" y1="28" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="39" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="39" y1="12" y2="18"/>
    <rect height="13" stroke-width="1" width="5" x="37" y="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape21">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="27" y1="11" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="11" y2="5"/>
    <circle cx="27" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="13" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="voltageTransformer:shape60">
    <ellipse cx="19" cy="18" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="42" y2="50"/>
    <rect height="13" stroke-width="1" width="7" x="16" y="29"/>
    <ellipse cx="8" cy="17" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <ellipse cx="19" cy="8" rx="7.5" ry="7" stroke-width="0.66594"/>
    <ellipse cx="8" cy="8" rx="7.5" ry="7" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="22" y1="52" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="21" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="24" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="9" x2="9" y1="5" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.103806" x1="6" x2="9" y1="9" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="6" x2="9" y1="6" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="8" x2="10" y1="19" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="5" x2="8" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="8" x2="8" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="20" x2="22" y1="19" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="17" x2="20" y1="21" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="20" x2="20" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="21" x2="23" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="18" x2="21" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="21" x2="21" y1="7" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="14" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="35" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="35" y2="33"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d457b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d46180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d46b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d476a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d48990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d49630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d49e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1d4a830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_145d780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_145d780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d4d9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d4d9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d4ed10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d4ed10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1d4f900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d513a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1d51f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1d52e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d53720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d55110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d558f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d55f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d568d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d57a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d583d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d58ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1d59880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1d5ad70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1d5b890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1d5c8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d5d530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d6b960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d5eae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1d5f720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1d60ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1389" width="2965" x="3135" y="-1318"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="5678" x2="5678" y1="-1017" y2="-1017"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="5678" x2="5678" y1="-1017" y2="-1017"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="5694" x2="5694" y1="-991" y2="-991"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="5694" x2="5694" y1="-1017" y2="-1017"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="5694" x2="5694" y1="-1017" y2="-1017"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="5678" x2="5678" y1="-992" y2="-992"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4489" x2="4489" y1="-1135" y2="-1045"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4455" x2="4489" y1="-1064" y2="-1064"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4455" x2="4455" y1="-1064" y2="-1098"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.611465" x1="4461" x2="4449" y1="-1112" y2="-1112"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.251748" x1="4455" x2="4455" y1="-1112" y2="-1103"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.39375" x1="4451" x2="4459" y1="-1115" y2="-1115"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.25" x1="4453" x2="4457" y1="-1118" y2="-1118"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="5368" x2="5368" y1="-1150" y2="-1045"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5334" x2="5368" y1="-1064" y2="-1064"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5334" x2="5334" y1="-1064" y2="-1098"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.611465" x1="5340" x2="5328" y1="-1112" y2="-1112"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.251748" x1="5334" x2="5334" y1="-1112" y2="-1103"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.39375" x1="5330" x2="5338" y1="-1115" y2="-1115"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="0.25" x1="5332" x2="5336" y1="-1118" y2="-1118"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4624" x2="4624" y1="-661" y2="-619"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4514" x2="4514" y1="-624" y2="-614"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4514" x2="4624" y1="-619" y2="-619"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4368" x2="4485" y1="-531" y2="-531"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.621622" x1="4368" x2="4368" y1="-554" y2="-531"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.621622" x1="4485" x2="4485" y1="-553" y2="-530"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5825" x2="5825" y1="-625" y2="-612"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5825" x2="5931" y1="-619" y2="-619"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="5931" x2="5931" y1="-619" y2="-694"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.253333" x1="5759" x2="5759" y1="-678" y2="-697"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="0.464211" x1="5759" x2="5808" y1="-678" y2="-678"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="6086" x2="6095" y1="-536" y2="-536"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-128067">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4084.095850 -1041.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17643" ObjectName="SW-LF_BC.LF_BC_362BK"/>
     <cge:Meas_Ref ObjectId="128067"/>
    <cge:TPSR_Ref TObjectID="17643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128071">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5586.883374 -1041.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17647" ObjectName="SW-LF_BC.LF_BC_361BK"/>
     <cge:Meas_Ref ObjectId="128071"/>
    <cge:TPSR_Ref TObjectID="17647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128567">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5135.500000 -475.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16987" ObjectName="SW-LF_BC.LF_BC_012BK"/>
     <cge:Meas_Ref ObjectId="128567"/>
    <cge:TPSR_Ref TObjectID="16987"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-79076">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5569.611024 -883.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16844" ObjectName="SW-LF_BC.LF_BC_301BK"/>
     <cge:Meas_Ref ObjectId="79076"/>
    <cge:TPSR_Ref TObjectID="16844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77751">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5568.611024 -631.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16845" ObjectName="SW-LF_BC.LF_BC_001BK"/>
     <cge:Meas_Ref ObjectId="77751"/>
    <cge:TPSR_Ref TObjectID="16845"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127845">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5223.093909 -864.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16863" ObjectName="SW-LF_BC.LF_BC_302BK"/>
     <cge:Meas_Ref ObjectId="127845"/>
    <cge:TPSR_Ref TObjectID="16863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77821">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5222.093909 -638.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16858" ObjectName="SW-LF_BC.LF_BC_002BK"/>
     <cge:Meas_Ref ObjectId="77821"/>
    <cge:TPSR_Ref TObjectID="16858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128315">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4769.806932 -637.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16869" ObjectName="SW-LF_BC.LF_BC_003BK"/>
     <cge:Meas_Ref ObjectId="128315"/>
    <cge:TPSR_Ref TObjectID="16869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128321">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4769.291895 -882.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16868" ObjectName="SW-LF_BC.LF_BC_303BK"/>
     <cge:Meas_Ref ObjectId="128321"/>
    <cge:TPSR_Ref TObjectID="16868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128507">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5737.000000 -453.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16918" ObjectName="SW-LF_BC.LF_BC_044BK"/>
     <cge:Meas_Ref ObjectId="128507"/>
    <cge:TPSR_Ref TObjectID="16918"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128447">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5585.000000 -453.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16935" ObjectName="SW-LF_BC.LF_BC_043BK"/>
     <cge:Meas_Ref ObjectId="128447"/>
    <cge:TPSR_Ref TObjectID="16935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128385">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5442.000000 -451.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16892" ObjectName="SW-LF_BC.LF_BC_042BK"/>
     <cge:Meas_Ref ObjectId="128385"/>
    <cge:TPSR_Ref TObjectID="16892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128325">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5304.000000 -457.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16882" ObjectName="SW-LF_BC.LF_BC_041BK"/>
     <cge:Meas_Ref ObjectId="128325"/>
    <cge:TPSR_Ref TObjectID="16882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128726">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4848.000000 -451.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17053" ObjectName="SW-LF_BC.LF_BC_034BK"/>
     <cge:Meas_Ref ObjectId="128726"/>
    <cge:TPSR_Ref TObjectID="17053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128667">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4718.000000 -459.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17009" ObjectName="SW-LF_BC.LF_BC_033BK"/>
     <cge:Meas_Ref ObjectId="128667"/>
    <cge:TPSR_Ref TObjectID="17009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128614">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4573.000000 -454.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16943" ObjectName="SW-LF_BC.LF_BC_035BK"/>
     <cge:Meas_Ref ObjectId="128614"/>
    <cge:TPSR_Ref TObjectID="16943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128786">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3817.000000 -449.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17076" ObjectName="SW-LF_BC.LF_BC_031BK"/>
     <cge:Meas_Ref ObjectId="128786"/>
    <cge:TPSR_Ref TObjectID="17076"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-79092">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.700000 -0.000000 0.000000 -0.647059 3622.000000 -387.705882)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17654" ObjectName="SW-LF_BC.LF_BC_032BK"/>
     <cge:Meas_Ref ObjectId="79092"/>
    <cge:TPSR_Ref TObjectID="17654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199835">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3992.000000 -452.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30474" ObjectName="SW-LF_BC.LF_BC_038BK"/>
     <cge:Meas_Ref ObjectId="199835"/>
    <cge:TPSR_Ref TObjectID="30474"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199739">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4144.000000 -450.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30472" ObjectName="SW-LF_BC.LF_BC_036BK"/>
     <cge:Meas_Ref ObjectId="199739"/>
    <cge:TPSR_Ref TObjectID="30472"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199788">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4292.000000 -449.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30473" ObjectName="SW-LF_BC.LF_BC_037BK"/>
     <cge:Meas_Ref ObjectId="199788"/>
    <cge:TPSR_Ref TObjectID="30473"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199716">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4948.000000 -1151.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23659" ObjectName="SW-LF_BC.LF_BC_312BK"/>
     <cge:Meas_Ref ObjectId="199716"/>
    <cge:TPSR_Ref TObjectID="23659"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1442840">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5789.000000 -785.000000)" xlink:href="#voltageTransformer:shape104"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12fb5b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4483.000000 -786.000000)" xlink:href="#voltageTransformer:shape104"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_165ff60">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4161.000000 -1193.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1660910">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4475.000000 -1133.000000)" xlink:href="#voltageTransformer:shape60"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1946e90">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5354.000000 -1147.000000)" xlink:href="#voltageTransformer:shape60"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19498a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5664.000000 -1195.000000)" xlink:href="#voltageTransformer:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5229,-976 5229,-1033 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="5229,-976 5229,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5229,-1085 5229,-1125 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="5229,-1085 5229,-1125 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_QF" endPointId="0" endStationName="LF_BC" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_BiCheng" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="5596,-1246 5596,-1271 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18074" ObjectName="AC-35kV.LN_BiCheng"/>
    <cge:TPSR_Ref TObjectID="18074_SS-101"/></metadata>
   <polyline fill="none" opacity="0" points="5596,-1246 5596,-1271 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="LF_BC" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_BiChengT" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4093,-1246 4093,-1281 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18067" ObjectName="AC-35kV.LN_BiChengT"/>
    <cge:TPSR_Ref TObjectID="18067_SS-101"/></metadata>
   <polyline fill="none" opacity="0" points="4093,-1246 4093,-1281 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-LF_BC.LD_044Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5737.000000 -56.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34089" ObjectName="EC-LF_BC.LD_044Ld"/>
    <cge:TPSR_Ref TObjectID="34089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_BC.LD_043Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5585.000000 -56.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34088" ObjectName="EC-LF_BC.LD_043Ld"/>
    <cge:TPSR_Ref TObjectID="34088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_BC.LD_034Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4848.000000 -56.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34091" ObjectName="EC-LF_BC.LD_034Ld"/>
    <cge:TPSR_Ref TObjectID="34091"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3620.000000 -52.179487)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_BC.LD_038Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3992.000000 -57.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37629" ObjectName="EC-LF_BC.LD_038Ld"/>
    <cge:TPSR_Ref TObjectID="37629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_BC.LD_036Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4144.000000 -55.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37628" ObjectName="EC-LF_BC.LD_036Ld"/>
    <cge:TPSR_Ref TObjectID="37628"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_BC.LD_037Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4292.000000 -54.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37627" ObjectName="EC-LF_BC.LD_037Ld"/>
    <cge:TPSR_Ref TObjectID="37627"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_BC.LD_042Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5442.000000 -56.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34090" ObjectName="EC-LF_BC.LD_042Ld"/>
    <cge:TPSR_Ref TObjectID="34090"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_BC.LD_041Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5304.000000 -47.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34087" ObjectName="EC-LF_BC.LD_041Ld"/>
    <cge:TPSR_Ref TObjectID="34087"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_BC.LD_033Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4717.000000 -67.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34092" ObjectName="EC-LF_BC.LD_033Ld"/>
    <cge:TPSR_Ref TObjectID="34092"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_147fa50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4131.095850 -1059.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1480380" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5630.883374 -1057.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1472df0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5619.095986 -846.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1473720" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5273.578871 -834.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1474050" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4820.291895 -846.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14010c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5925.000000 -688.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1506070" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4618.000000 -688.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1618ff0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4805.000000 -1007.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1619a40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5150.000000 -1014.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16238b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4515.722412 -1144.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_162ac90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5394.722412 -1144.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_149bbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-979 4093,-999 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="30496@0" ObjectIDZND0="17644@0" Pin0InfoVect0LinkObjId="SW-128068_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16172f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-979 4093,-999 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1450b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-1035 4093,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="17644@1" ObjectIDZND0="17643@0" Pin0InfoVect0LinkObjId="SW-128067_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128068_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-1035 4093,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12e8b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-1076 4093,-1093 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17643@1" ObjectIDZND0="17645@0" Pin0InfoVect0LinkObjId="SW-128069_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128067_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-1076 4093,-1093 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_142be40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-1129 4093,-1146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="17645@1" ObjectIDZND0="17646@x" ObjectIDZND1="g_14938a0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-128070_0" Pin0InfoVect1LinkObjId="g_14938a0_0" Pin0InfoVect2LinkObjId="g_1442840_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128069_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-1129 4093,-1146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_141e130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-1146 4137,-1146 4137,-1127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="17645@x" ObjectIDND1="g_14938a0@0" ObjectIDND2="0@x" ObjectIDZND0="17646@1" Pin0InfoVect0LinkObjId="SW-128070_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-128069_0" Pin1InfoVect1LinkObjId="g_14938a0_0" Pin1InfoVect2LinkObjId="g_1442840_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-1146 4137,-1146 4137,-1127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_141e320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4137,-1091 4137,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="17646@0" ObjectIDZND0="g_147fa50@0" Pin0InfoVect0LinkObjId="g_147fa50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4137,-1091 4137,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14936b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4166,-1202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_165ff60@0" Pin0InfoVect0LinkObjId="g_165ff60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1442840_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4166,-1202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_144f8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5596,-977 5596,-999 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17417@0" ObjectIDZND0="17649@0" Pin0InfoVect0LinkObjId="SW-128942_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5596,-977 5596,-999 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13e34a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5596,-1076 5596,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17647@1" ObjectIDZND0="17648@0" Pin0InfoVect0LinkObjId="SW-128072_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128071_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5596,-1076 5596,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_142f7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5637,-1090 5637,-1075 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="17650@0" ObjectIDZND0="g_1480380@0" Pin0InfoVect0LinkObjId="g_1480380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128073_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5637,-1090 5637,-1075 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1431740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5653,-1204 5669,-1204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_19498a0@0" Pin0InfoVect0LinkObjId="g_19498a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1442840_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5653,-1204 5669,-1204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1484a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5027,-574 5025,-578 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" ObjectIDND0="17419@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_147f610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5027,-574 5025,-578 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1484c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5145,-560 5145,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16986@0" ObjectIDZND0="17418@0" Pin0InfoVect0LinkObjId="g_13d1b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128569_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5145,-560 5145,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1486510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5145,-524 5145,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16986@1" ObjectIDZND0="16987@1" Pin0InfoVect0LinkObjId="SW-128567_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128569_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5145,-524 5145,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13d0080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5625,-877 5625,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23627@0" ObjectIDZND0="g_1472df0@0" Pin0InfoVect0LinkObjId="g_1472df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-79080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5625,-877 5625,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13d1930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5577,-639 5577,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16845@0" ObjectIDZND0="16851@0" Pin0InfoVect0LinkObjId="SW-77761_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77751_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5577,-639 5577,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13d1b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5577,-584 5577,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16851@1" ObjectIDZND0="17418@0" Pin0InfoVect0LinkObjId="g_1484c60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77761_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5577,-584 5577,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_143f1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-882 4826,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="17653@0" ObjectIDZND0="g_1474050@0" Pin0InfoVect0LinkObjId="g_1474050_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127959_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-882 4826,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_145c6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5232,-977 5232,-961 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17417@0" ObjectIDZND0="16866@1" Pin0InfoVect0LinkObjId="SW-127846_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5232,-977 5232,-961 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13357f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5232,-925 5232,-914 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="16866@0" ObjectIDZND0="16863@x" ObjectIDZND1="17652@x" Pin0InfoVect0LinkObjId="SW-127845_0" Pin0InfoVect1LinkObjId="SW-127849_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127846_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5232,-925 5232,-914 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1335a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5232,-914 5232,-899 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="16866@x" ObjectIDND1="17652@x" ObjectIDZND0="16863@1" Pin0InfoVect0LinkObjId="SW-127845_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127846_0" Pin1InfoVect1LinkObjId="SW-127849_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5232,-914 5232,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1394d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5232,-914 5279,-914 5279,-901 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="16866@x" ObjectIDND1="16863@x" ObjectIDZND0="17652@1" Pin0InfoVect0LinkObjId="SW-127849_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127846_0" Pin1InfoVect1LinkObjId="SW-127845_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5232,-914 5279,-914 5279,-901 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1394f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5279,-865 5279,-852 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="17652@0" ObjectIDZND0="g_1473720@0" Pin0InfoVect0LinkObjId="g_1473720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127849_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5279,-865 5279,-852 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1397a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5231,-646 5231,-627 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16858@0" ObjectIDZND0="16861@0" Pin0InfoVect0LinkObjId="SW-77824_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77821_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5231,-646 5231,-627 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1397cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5231,-700 5231,-673 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16862@1" ObjectIDZND0="16858@1" Pin0InfoVect0LinkObjId="SW-77821_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77825_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5231,-700 5231,-673 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1397ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5231,-591 5231,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16861@1" ObjectIDZND0="17418@0" Pin0InfoVect0LinkObjId="g_1484c60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77824_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5231,-591 5231,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_147f1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4779,-645 4779,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16869@0" ObjectIDZND0="16875@0" Pin0InfoVect0LinkObjId="SW-128317_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128315_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4779,-645 4779,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_147f3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4779,-688 4779,-672 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28994@1" ObjectIDZND0="16869@1" Pin0InfoVect0LinkObjId="SW-128315_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128318_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4779,-688 4779,-672 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_147f610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4779,-590 4779,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16875@1" ObjectIDZND0="17419@0" Pin0InfoVect0LinkObjId="g_1507230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128317_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4779,-590 4779,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_147f830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-1184 4093,-1184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="g_14938a0@0" ObjectIDZND0="0@x" ObjectIDZND1="18067@1" ObjectIDZND2="17645@x" Pin0InfoVect0LinkObjId="g_1442840_0" Pin0InfoVect1LinkObjId="g_12dc230_1" Pin0InfoVect2LinkObjId="SW-128069_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14938a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4080,-1184 4093,-1184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13416a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4121,-1203 4093,-1203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_14938a0@0" ObjectIDZND1="17645@x" ObjectIDZND2="17646@x" Pin0InfoVect0LinkObjId="g_14938a0_0" Pin0InfoVect1LinkObjId="SW-128069_0" Pin0InfoVect2LinkObjId="SW-128070_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1442840_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4121,-1203 4093,-1203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_148e010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5145,-482 5145,-446 5025,-447 5025,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16987@0" ObjectIDZND0="16990@1" Pin0InfoVect0LinkObjId="SW-128611_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128567_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5145,-482 5145,-446 5025,-447 5025,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_148e9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5577,-977 5578,-968 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17417@0" ObjectIDZND0="16857@1" Pin0InfoVect0LinkObjId="SW-79077_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5577,-977 5578,-968 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1380d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5577,-666 5577,-678 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16845@1" ObjectIDZND0="16852@1" Pin0InfoVect0LinkObjId="SW-77762_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77751_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5577,-666 5577,-678 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1445a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5808,-768 5808,-790 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_1445180@0" ObjectIDZND0="g_1442840@0" Pin0InfoVect0LinkObjId="g_1442840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1445180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5808,-768 5808,-790 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13fde40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5808,-737 5808,-664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1445180@1" ObjectIDZND0="29016@x" ObjectIDZND1="29015@x" Pin0InfoVect0LinkObjId="SW-191864_0" Pin0InfoVect1LinkObjId="SW-191863_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1445180_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5808,-737 5808,-664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13fe030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5808,-664 5808,-640 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1445180@0" ObjectIDND1="29016@x" ObjectIDZND0="29015@1" Pin0InfoVect0LinkObjId="SW-191863_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1445180_0" Pin1InfoVect1LinkObjId="SW-191864_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5808,-664 5808,-640 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1400c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5808,-664 5878,-664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1445180@0" ObjectIDND1="29015@x" ObjectIDZND0="29016@0" Pin0InfoVect0LinkObjId="SW-191864_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1445180_0" Pin1InfoVect1LinkObjId="SW-191863_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5808,-664 5878,-664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1400e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5914,-664 5931,-664 5931,-661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="29016@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191864_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5914,-664 5931,-664 5931,-661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1434830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5809,-605 5809,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29015@0" ObjectIDZND0="17418@0" Pin0InfoVect0LinkObjId="g_1484c60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191863_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5809,-605 5809,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14232f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5746,-575 5746,-549 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17418@0" ObjectIDZND0="17658@0" Pin0InfoVect0LinkObjId="SW-128509_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1484c60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5746,-575 5746,-549 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1425630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5746,-513 5746,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="17658@1" ObjectIDZND0="16918@1" Pin0InfoVect0LinkObjId="SW-128507_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128509_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5746,-513 5746,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1425890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5746,-461 5746,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16918@0" ObjectIDZND0="17661@0" Pin0InfoVect0LinkObjId="SW-128063_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128507_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5746,-461 5746,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_139b690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5746,-300 5746,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_138b770@1" ObjectIDZND0="17662@0" Pin0InfoVect0LinkObjId="SW-78001_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_138b770_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5746,-300 5746,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_139e810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5774,-232 5746,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="29003@1" ObjectIDZND0="17662@x" ObjectIDZND1="g_13a0370@0" ObjectIDZND2="34089@x" Pin0InfoVect0LinkObjId="SW-78001_0" Pin0InfoVect1LinkObjId="g_13a0370_0" Pin0InfoVect2LinkObjId="EC-LF_BC.LD_044Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128513_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5774,-232 5746,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13a0180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5746,-240 5746,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="17662@1" ObjectIDZND0="29003@x" ObjectIDZND1="g_13a0370@0" ObjectIDZND2="34089@x" Pin0InfoVect0LinkObjId="SW-128513_0" Pin0InfoVect1LinkObjId="g_13a0370_0" Pin0InfoVect2LinkObjId="EC-LF_BC.LD_044Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78001_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5746,-240 5746,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13a4700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5594,-575 5594,-549 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17418@0" ObjectIDZND0="16934@0" Pin0InfoVect0LinkObjId="SW-128449_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1484c60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5594,-575 5594,-549 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13a6a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5594,-513 5594,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16934@1" ObjectIDZND0="16935@1" Pin0InfoVect0LinkObjId="SW-128447_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128449_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5594,-513 5594,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13a6ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5594,-461 5594,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16935@0" ObjectIDZND0="17659@0" Pin0InfoVect0LinkObjId="SW-128061_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128447_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5594,-461 5594,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1355ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5594,-300 5594,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_13ab5f0@1" ObjectIDZND0="17660@0" Pin0InfoVect0LinkObjId="SW-78018_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13ab5f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5594,-300 5594,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1358d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5623,-233 5594,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="29001@1" ObjectIDZND0="17660@x" ObjectIDZND1="g_13596b0@0" ObjectIDZND2="34088@x" Pin0InfoVect0LinkObjId="SW-78018_0" Pin0InfoVect1LinkObjId="g_13596b0_0" Pin0InfoVect2LinkObjId="EC-LF_BC.LD_043Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128453_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5623,-233 5594,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13594c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5594,-240 5594,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="17660@1" ObjectIDZND0="29001@x" ObjectIDZND1="g_13596b0@0" ObjectIDZND2="34088@x" Pin0InfoVect0LinkObjId="SW-128453_0" Pin0InfoVect1LinkObjId="g_13596b0_0" Pin0InfoVect2LinkObjId="EC-LF_BC.LD_043Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78018_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5594,-240 5594,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13cdc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5451,-575 5451,-547 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17418@0" ObjectIDZND0="16909@0" Pin0InfoVect0LinkObjId="SW-128387_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1484c60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5451,-575 5451,-547 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13b0c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5451,-511 5451,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16909@1" ObjectIDZND0="16892@1" Pin0InfoVect0LinkObjId="SW-128385_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128387_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5451,-511 5451,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13b0ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5451,-459 5451,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16892@0" ObjectIDZND0="16913@0" Pin0InfoVect0LinkObjId="SW-77996_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128385_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5451,-459 5451,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13b8b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5451,-298 5451,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_13b5740@1" ObjectIDZND0="16914@0" Pin0InfoVect0LinkObjId="SW-77975_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13b5740_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5451,-298 5451,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12bed90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5313,-575 5313,-549 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17418@0" ObjectIDZND0="16881@0" Pin0InfoVect0LinkObjId="SW-128327_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1484c60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5313,-575 5313,-549 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12c1030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5313,-513 5313,-492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16881@1" ObjectIDZND0="16882@1" Pin0InfoVect0LinkObjId="SW-128325_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128327_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5313,-513 5313,-492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12c1290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5313,-465 5313,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16882@0" ObjectIDZND0="16885@0" Pin0InfoVect0LinkObjId="SW-77968_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128325_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5313,-465 5313,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_135e170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5313,-302 5313,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_12c5be0@1" ObjectIDZND0="28996@0" Pin0InfoVect0LinkObjId="SW-77965_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12c5be0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5313,-302 5313,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1315b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4857,-574 4857,-547 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17419@0" ObjectIDZND0="17424@0" Pin0InfoVect0LinkObjId="SW-128728_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_147f610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4857,-574 4857,-547 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1317e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4857,-511 4857,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="17424@1" ObjectIDZND0="17053@1" Pin0InfoVect0LinkObjId="SW-128726_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128728_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4857,-511 4857,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13180e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4857,-459 4857,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17053@0" ObjectIDZND0="17423@0" Pin0InfoVect0LinkObjId="SW-79378_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128726_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4857,-459 4857,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_131fe40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4857,-300 4857,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_131ca30@1" ObjectIDZND0="17052@0" Pin0InfoVect0LinkObjId="SW-78136_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_131ca30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4857,-300 4857,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13454b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4888,-233 4857,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="29010@1" ObjectIDZND0="17052@x" ObjectIDZND1="g_1345e40@0" ObjectIDZND2="34091@x" Pin0InfoVect0LinkObjId="SW-78136_0" Pin0InfoVect1LinkObjId="g_1345e40_0" Pin0InfoVect2LinkObjId="EC-LF_BC.LD_034Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128732_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4888,-233 4857,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1345c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4857,-240 4857,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="17052@1" ObjectIDZND0="29010@x" ObjectIDZND1="g_1345e40@0" ObjectIDZND2="34091@x" Pin0InfoVect0LinkObjId="SW-128732_0" Pin0InfoVect1LinkObjId="g_1345e40_0" Pin0InfoVect2LinkObjId="EC-LF_BC.LD_034Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78136_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4857,-240 4857,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1350320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4727,-574 4727,-555 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17419@0" ObjectIDZND0="23634@0" Pin0InfoVect0LinkObjId="SW-128066_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_147f610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4727,-574 4727,-555 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_136f940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4727,-519 4727,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23634@1" ObjectIDZND0="17009@1" Pin0InfoVect0LinkObjId="SW-128667_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128066_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4727,-519 4727,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_136fba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4727,-467 4727,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17009@0" ObjectIDZND0="17421@0" Pin0InfoVect0LinkObjId="SW-79354_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128667_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4727,-467 4727,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1377900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4727,-308 4727,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_13744f0@1" ObjectIDZND0="17422@0" Pin0InfoVect0LinkObjId="SW-78092_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13744f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4727,-308 4727,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12f02c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4582,-574 4582,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17419@0" ObjectIDZND0="29020@0" Pin0InfoVect0LinkObjId="SW-128616_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_147f610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4582,-574 4582,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12f2600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4582,-514 4582,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29020@1" ObjectIDZND0="16943@1" Pin0InfoVect0LinkObjId="SW-128614_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128616_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4582,-514 4582,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12f2860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4582,-462 4582,-438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16943@0" ObjectIDZND0="23633@0" Pin0InfoVect0LinkObjId="SW-128065_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128614_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4582,-462 4582,-438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13242a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4582,-303 4582,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_12f71b0@1" ObjectIDZND0="29008@0" Pin0InfoVect0LinkObjId="SW-78026_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12f71b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4582,-303 4582,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_132b320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3629,-296 3629,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1327f10@1" ObjectIDZND0="17656@0" Pin0InfoVect0LinkObjId="SW-128161_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1327f10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3629,-296 3629,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_132e480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3660,-229 3629,-229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="17655@1" ObjectIDZND0="17656@x" ObjectIDZND1="g_132e940@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-128161_0" Pin0InfoVect1LinkObjId="g_132e940_0" Pin0InfoVect2LinkObjId="g_1442840_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128160_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3660,-229 3629,-229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_132e6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3629,-236 3629,-229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="17656@1" ObjectIDZND0="17655@x" ObjectIDZND1="g_132e940@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-128160_0" Pin0InfoVect1LinkObjId="g_132e940_0" Pin0InfoVect2LinkObjId="g_1442840_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128161_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3629,-236 3629,-229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1333f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3826,-574 3826,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17419@0" ObjectIDZND0="17075@0" Pin0InfoVect0LinkObjId="SW-128788_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_147f610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3826,-574 3826,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14f8b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3826,-506 3826,-484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="17075@1" ObjectIDZND0="17076@1" Pin0InfoVect0LinkObjId="SW-128786_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128788_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3826,-506 3826,-484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14fd610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3826,-457 3826,-424 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="17076@0" ObjectIDZND0="29012@0" Pin0InfoVect0LinkObjId="SW-128789_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128786_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3826,-457 3826,-424 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15033c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4501,-664 4501,-635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="29018@x" ObjectIDND1="g_1501fd0@0" ObjectIDND2="g_12fdf10@0" ObjectIDZND0="29017@1" Pin0InfoVect0LinkObjId="SW-191865_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-191866_0" Pin1InfoVect1LinkObjId="g_1501fd0_0" Pin1InfoVect2LinkObjId="g_12fdf10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4501,-664 4501,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1505e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4501,-664 4571,-664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1501fd0@0" ObjectIDND1="g_12fdf10@0" ObjectIDND2="29017@x" ObjectIDZND0="29018@0" Pin0InfoVect0LinkObjId="SW-191866_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1501fd0_0" Pin1InfoVect1LinkObjId="g_12fdf10_0" Pin1InfoVect2LinkObjId="SW-191865_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4501,-664 4571,-664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1507230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4502,-605 4502,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29017@0" ObjectIDZND0="17419@0" Pin0InfoVect0LinkObjId="g_147f610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191865_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4502,-605 4502,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12dbd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3629,-348 3629,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_1327f10@0" ObjectIDZND0="17654@0" Pin0InfoVect0LinkObjId="SW-79092_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1327f10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3629,-348 3629,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12dbfd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-1184 4093,-1203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="g_14938a0@0" ObjectIDND1="17645@x" ObjectIDND2="17646@x" ObjectIDZND0="0@x" ObjectIDZND1="18067@1" Pin0InfoVect0LinkObjId="g_1442840_0" Pin0InfoVect1LinkObjId="g_12dc230_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_14938a0_0" Pin1InfoVect1LinkObjId="SW-128069_0" Pin1InfoVect2LinkObjId="SW-128070_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-1184 4093,-1203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12dc230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-1203 4093,-1247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_14938a0@0" ObjectIDND1="17645@x" ObjectIDND2="17646@x" ObjectIDZND0="18067@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_14938a0_0" Pin1InfoVect1LinkObjId="SW-128069_0" Pin1InfoVect2LinkObjId="SW-128070_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-1203 4093,-1247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12dc490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-1146 4093,-1184 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="17645@x" ObjectIDND1="17646@x" ObjectIDZND0="g_14938a0@0" ObjectIDZND1="0@x" ObjectIDZND2="18067@1" Pin0InfoVect0LinkObjId="g_14938a0_0" Pin0InfoVect1LinkObjId="g_1442840_0" Pin0InfoVect2LinkObjId="g_12dc230_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-128069_0" Pin1InfoVect1LinkObjId="SW-128070_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-1146 4093,-1184 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12dc6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5596,-1143 5596,-1248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="powerLine" ObjectIDND0="17650@x" ObjectIDND1="17648@x" ObjectIDZND0="18074@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-128073_0" Pin1InfoVect1LinkObjId="SW-128072_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5596,-1143 5596,-1248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12dd1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5637,-1126 5637,-1143 5596,-1143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="17650@1" ObjectIDZND0="17648@x" ObjectIDZND1="18074@1" Pin0InfoVect0LinkObjId="SW-128072_0" Pin0InfoVect1LinkObjId="g_12dc6f0_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128073_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5637,-1126 5637,-1143 5596,-1143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12dd440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5596,-1143 5596,-1127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" EndDevType0="switch" ObjectIDND0="17650@x" ObjectIDND1="18074@1" ObjectIDZND0="17648@1" Pin0InfoVect0LinkObjId="SW-128072_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-128073_0" Pin1InfoVect1LinkObjId="g_12dc6f0_1" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5596,-1143 5596,-1127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12dd6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5608,-1205 5591,-1205 5591,-1181 5576,-1181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1431930@0" Pin0InfoVect0LinkObjId="g_1431930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1442840_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5608,-1205 5591,-1205 5591,-1181 5576,-1181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12f8fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5864,-516 5864,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29019@0" ObjectIDZND0="17418@0" Pin0InfoVect0LinkObjId="g_1484c60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191867_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5864,-516 5864,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12fe790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4502,-769 4502,-791 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_12fdf10@0" ObjectIDZND0="g_12fb5b0@0" Pin0InfoVect0LinkObjId="g_12fb5b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12fdf10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4502,-769 4502,-791 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12ff430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5596,-1035 5596,-1049 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="17649@1" ObjectIDZND0="17647@0" Pin0InfoVect0LinkObjId="SW-128071_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128942_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5596,-1035 5596,-1049 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_130b4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4001,-574 4001,-548 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17419@0" ObjectIDZND0="30483@0" Pin0InfoVect0LinkObjId="SW-199837_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_147f610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4001,-574 4001,-548 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_130d810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4001,-512 4001,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="30483@1" ObjectIDZND0="30474@1" Pin0InfoVect0LinkObjId="SW-199835_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199837_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4001,-512 4001,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_130da70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4001,-460 4001,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="30474@0" ObjectIDZND0="30484@0" Pin0InfoVect0LinkObjId="SW-199839_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199835_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4001,-460 4001,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15e3a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4153,-574 4153,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17419@0" ObjectIDZND0="30475@0" Pin0InfoVect0LinkObjId="SW-199741_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_147f610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4153,-574 4153,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15e5da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4153,-510 4153,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="30475@1" ObjectIDZND0="30472@1" Pin0InfoVect0LinkObjId="SW-199739_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199741_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4153,-510 4153,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15e6000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4153,-458 4153,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="30472@0" ObjectIDZND0="30479@0" Pin0InfoVect0LinkObjId="SW-199743_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199739_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4153,-458 4153,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15eddb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4153,-299 4153,-275 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_15ea9a0@1" ObjectIDZND0="30478@0" Pin0InfoVect0LinkObjId="SW-199747_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ea9a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4153,-299 4153,-275 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15f0f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4184,-232 4153,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="30476@1" ObjectIDZND0="30478@x" ObjectIDZND1="g_15f19f0@0" ObjectIDZND2="37628@x" Pin0InfoVect0LinkObjId="SW-199747_0" Pin0InfoVect1LinkObjId="g_15f19f0_0" Pin0InfoVect2LinkObjId="EC-LF_BC.LD_036Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199748_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4184,-232 4153,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15f1800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4153,-239 4153,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="30478@1" ObjectIDZND0="30476@x" ObjectIDZND1="g_15f19f0@0" ObjectIDZND2="37628@x" Pin0InfoVect0LinkObjId="SW-199748_0" Pin0InfoVect1LinkObjId="g_15f19f0_0" Pin0InfoVect2LinkObjId="EC-LF_BC.LD_036Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199747_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4153,-239 4153,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15fc6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4301,-574 4301,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17419@0" ObjectIDZND0="30480@0" Pin0InfoVect0LinkObjId="SW-199790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_147f610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4301,-574 4301,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15fe9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4301,-509 4301,-484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="30480@1" ObjectIDZND0="30473@1" Pin0InfoVect0LinkObjId="SW-199788_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199790_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4301,-509 4301,-484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15fec50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4301,-457 4301,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="30473@0" ObjectIDZND0="30482@0" Pin0InfoVect0LinkObjId="SW-199792_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199788_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4301,-457 4301,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16064b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4301,-299 4301,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_16035f0@1" ObjectIDZND0="37627@0" Pin0InfoVect0LinkObjId="EC-LF_BC.LD_037Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16035f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4301,-299 4301,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16066d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4001,-303 4001,-84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_1312410@1" ObjectIDZND0="37629@0" Pin0InfoVect0LinkObjId="EC-LF_BC.LD_038Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1312410_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4001,-303 4001,-84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1607e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5480,-231 5451,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="28999@1" ObjectIDZND0="16914@x" ObjectIDZND1="g_13bd2f0@0" ObjectIDZND2="34090@x" Pin0InfoVect0LinkObjId="SW-77975_0" Pin0InfoVect1LinkObjId="g_13bd2f0_0" Pin0InfoVect2LinkObjId="EC-LF_BC.LD_042Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128393_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5480,-231 5451,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16080d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5451,-231 5451,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="28999@x" ObjectIDND1="g_13bd2f0@0" ObjectIDND2="34090@x" ObjectIDZND0="16914@1" Pin0InfoVect0LinkObjId="SW-77975_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-128393_0" Pin1InfoVect1LinkObjId="g_13bd2f0_0" Pin1InfoVect2LinkObjId="EC-LF_BC.LD_042Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5451,-231 5451,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1608bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5344,-235 5313,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="16886@1" ObjectIDZND0="28996@x" ObjectIDZND1="g_1361110@0" ObjectIDZND2="g_1957270@0" Pin0InfoVect0LinkObjId="SW-77965_0" Pin0InfoVect1LinkObjId="g_1361110_0" Pin0InfoVect2LinkObjId="g_1957270_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128331_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5344,-235 5313,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1608e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5313,-235 5313,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="16886@x" ObjectIDND1="g_1361110@0" ObjectIDND2="g_1957270@0" ObjectIDZND0="28996@1" Pin0InfoVect0LinkObjId="SW-77965_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-128331_0" Pin1InfoVect1LinkObjId="g_1361110_0" Pin1InfoVect2LinkObjId="g_1957270_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5313,-235 5313,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_160bf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5232,-872 5232,-832 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="16863@0" ObjectIDZND0="17131@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127845_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5232,-872 5232,-832 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_160dca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4368,-562 4368,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_160e1d0@0" ObjectIDZND0="17419@0" Pin0InfoVect0LinkObjId="g_147f610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_160e1d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4368,-562 4368,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_160e5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4485,-574 4485,-561 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="17419@0" ObjectIDZND0="g_160ed60@0" Pin0InfoVect0LinkObjId="g_160ed60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_147f610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4485,-574 4485,-561 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1617090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5105,-977 5105,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17417@0" ObjectIDZND0="30492@0" Pin0InfoVect0LinkObjId="SW-199717_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5105,-977 5105,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16172f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4860,-1012 4860,-979 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="30494@0" ObjectIDZND0="30496@0" Pin0InfoVect0LinkObjId="g_194ebb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199718_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4860,-1012 4860,-979 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1617de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5105,-1044 5105,-1118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="30492@1" ObjectIDZND0="23659@x" ObjectIDZND1="30493@x" Pin0InfoVect0LinkObjId="SW-199716_0" Pin0InfoVect1LinkObjId="SW-199719_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199717_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5105,-1044 5105,-1118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1618040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5105,-1118 5105,-1161 4984,-1161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="30492@x" ObjectIDND1="30493@x" ObjectIDZND0="23659@0" Pin0InfoVect0LinkObjId="SW-199716_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-199717_0" Pin1InfoVect1LinkObjId="SW-199719_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5105,-1118 5105,-1161 4984,-1161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1618b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4957,-1161 4860,-1161 4860,-1120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="23659@1" ObjectIDZND0="30494@x" ObjectIDZND1="30495@x" Pin0InfoVect0LinkObjId="SW-199718_0" Pin0InfoVect1LinkObjId="SW-199720_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199716_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4957,-1161 4860,-1161 4860,-1120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1618d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4860,-1120 4860,-1048 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="23659@x" ObjectIDND1="30495@x" ObjectIDZND0="30494@1" Pin0InfoVect0LinkObjId="SW-199718_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-199716_0" Pin1InfoVect1LinkObjId="SW-199720_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4860,-1120 4860,-1048 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_161f490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5105,-1118 5156,-1118 5156,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="30492@x" ObjectIDND1="23659@x" ObjectIDZND0="30493@1" Pin0InfoVect0LinkObjId="SW-199719_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-199717_0" Pin1InfoVect1LinkObjId="SW-199716_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5105,-1118 5156,-1118 5156,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_161f6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5156,-1044 5156,-1032 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="30493@0" ObjectIDZND0="g_1619a40@0" Pin0InfoVect0LinkObjId="g_1619a40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199719_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5156,-1044 5156,-1032 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_161f950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4860,-1120 4811,-1120 4810,-1071 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="23659@x" ObjectIDND1="30494@x" ObjectIDZND0="30495@1" Pin0InfoVect0LinkObjId="SW-199720_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-199716_0" Pin1InfoVect1LinkObjId="SW-199718_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4860,-1120 4811,-1120 4810,-1071 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_161fbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4810,-1035 4811,-1025 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="30495@0" ObjectIDZND0="g_1618ff0@0" Pin0InfoVect0LinkObjId="g_1618ff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4810,-1035 4811,-1025 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1620b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4490,-979 4490,-998 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="30496@0" ObjectIDZND0="30486@1" Pin0InfoVect0LinkObjId="SW-199700_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16172f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4490,-979 4490,-998 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1620cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4490,-1034 4490,-1045 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="30486@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4490,-1034 4490,-1045 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16233f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4475,-1064 4522,-1064 4522,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="30487@1" Pin0InfoVect0LinkObjId="SW-199701_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4475,-1064 4522,-1064 4522,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1623650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4522,-1113 4522,-1126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="30487@0" ObjectIDZND0="g_16238b0@0" Pin0InfoVect0LinkObjId="g_16238b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199701_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4522,-1113 4522,-1126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1627b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5368,-977 5368,-998 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17417@0" ObjectIDZND0="28995@1" Pin0InfoVect0LinkObjId="SW-128056_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5368,-977 5368,-998 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1627d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5368,-1034 5368,-1045 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="28995@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128056_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5368,-1034 5368,-1045 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_162a7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5368,-1064 5401,-1064 5401,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="23629@1" Pin0InfoVect0LinkObjId="SW-128057_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5368,-1064 5401,-1064 5401,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_162aa30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5401,-1113 5401,-1126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23629@0" ObjectIDZND0="g_162ac90@0" Pin0InfoVect0LinkObjId="g_162ac90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128057_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5401,-1113 5401,-1126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1635ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3660,-341 3660,-380 3604,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="23635@0" ObjectIDZND0="g_163a2e0@0" Pin0InfoVect0LinkObjId="g_163a2e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128159_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3660,-341 3660,-380 3604,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16382c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3853,-324 3853,-356 3826,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="29011@0" ObjectIDZND0="29012@x" ObjectIDZND1="g_1501280@0" ObjectIDZND2="g_1639660@0" Pin0InfoVect0LinkObjId="SW-128789_0" Pin0InfoVect1LinkObjId="g_1501280_0" Pin0InfoVect2LinkObjId="g_1639660_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3853,-324 3853,-356 3826,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1638f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3826,-388 3826,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="29012@1" ObjectIDZND0="29011@x" ObjectIDZND1="g_1501280@0" ObjectIDZND2="g_1639660@0" Pin0InfoVect0LinkObjId="SW-128790_0" Pin0InfoVect1LinkObjId="g_1501280_0" Pin0InfoVect2LinkObjId="g_1639660_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128789_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3826,-388 3826,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16391a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3826,-356 3826,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="29011@x" ObjectIDND1="29012@x" ObjectIDND2="g_1639660@0" ObjectIDZND0="g_1501280@0" Pin0InfoVect0LinkObjId="g_1501280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-128790_0" Pin1InfoVect1LinkObjId="SW-128789_0" Pin1InfoVect2LinkObjId="g_1639660_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3826,-356 3826,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1639400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3826,-356 3792,-356 3792,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="29011@x" ObjectIDND1="29012@x" ObjectIDND2="g_1501280@0" ObjectIDZND0="g_1639660@0" Pin0InfoVect0LinkObjId="g_1639660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-128790_0" Pin1InfoVect1LinkObjId="SW-128789_0" Pin1InfoVect2LinkObjId="g_1501280_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3826,-356 3792,-356 3792,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_163af50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4032,-346 4032,-385 4001,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="30485@0" ObjectIDZND0="30484@x" ObjectIDZND1="g_1312410@0" ObjectIDZND2="g_130dcd0@0" Pin0InfoVect0LinkObjId="SW-199839_0" Pin0InfoVect1LinkObjId="g_1312410_0" Pin0InfoVect2LinkObjId="g_130dcd0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199841_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4032,-346 4032,-385 4001,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_163bc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4001,-399 4001,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="30484@1" ObjectIDZND0="30485@x" ObjectIDZND1="g_1312410@0" ObjectIDZND2="g_130dcd0@0" Pin0InfoVect0LinkObjId="SW-199841_0" Pin0InfoVect1LinkObjId="g_1312410_0" Pin0InfoVect2LinkObjId="g_130dcd0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199839_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4001,-399 4001,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_163bea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4001,-385 4001,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="30485@x" ObjectIDND1="30484@x" ObjectIDND2="g_130dcd0@0" ObjectIDZND0="g_1312410@0" Pin0InfoVect0LinkObjId="g_1312410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-199841_0" Pin1InfoVect1LinkObjId="SW-199839_0" Pin1InfoVect2LinkObjId="g_130dcd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4001,-385 4001,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_163c100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4001,-385 3975,-385 3975,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="30485@x" ObjectIDND1="30484@x" ObjectIDND2="g_1312410@0" ObjectIDZND0="g_130dcd0@0" Pin0InfoVect0LinkObjId="g_130dcd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-199841_0" Pin1InfoVect1LinkObjId="SW-199839_0" Pin1InfoVect2LinkObjId="g_1312410_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4001,-385 3975,-385 3975,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_163c360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4184,-344 4184,-381 4153,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="30477@0" ObjectIDZND0="30479@x" ObjectIDZND1="g_15ea9a0@0" ObjectIDZND2="g_15e6260@0" Pin0InfoVect0LinkObjId="SW-199743_0" Pin0InfoVect1LinkObjId="g_15ea9a0_0" Pin0InfoVect2LinkObjId="g_15e6260_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199745_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4184,-344 4184,-381 4153,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_163d050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4153,-397 4153,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="30479@1" ObjectIDZND0="g_15ea9a0@0" ObjectIDZND1="30477@x" ObjectIDZND2="g_15e6260@0" Pin0InfoVect0LinkObjId="g_15ea9a0_0" Pin0InfoVect1LinkObjId="SW-199745_0" Pin0InfoVect2LinkObjId="g_15e6260_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199743_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4153,-397 4153,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_163d290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4153,-381 4153,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="30479@x" ObjectIDND1="30477@x" ObjectIDND2="g_15e6260@0" ObjectIDZND0="g_15ea9a0@0" Pin0InfoVect0LinkObjId="g_15ea9a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-199743_0" Pin1InfoVect1LinkObjId="SW-199745_0" Pin1InfoVect2LinkObjId="g_15e6260_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4153,-381 4153,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_163d4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4128,-366 4128,-382 4153,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_15e6260@0" ObjectIDZND0="30479@x" ObjectIDZND1="g_15ea9a0@0" ObjectIDZND2="30477@x" Pin0InfoVect0LinkObjId="SW-199743_0" Pin0InfoVect1LinkObjId="g_15ea9a0_0" Pin0InfoVect2LinkObjId="SW-199745_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15e6260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4128,-366 4128,-382 4153,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_163d750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-343 4332,-381 4301,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="30481@0" ObjectIDZND0="30482@x" ObjectIDZND1="g_16035f0@0" ObjectIDZND2="g_15feeb0@0" Pin0InfoVect0LinkObjId="SW-199792_0" Pin0InfoVect1LinkObjId="g_16035f0_0" Pin0InfoVect2LinkObjId="g_15feeb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199794_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-343 4332,-381 4301,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_163e440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4301,-396 4301,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="30482@1" ObjectIDZND0="g_16035f0@0" ObjectIDZND1="30481@x" ObjectIDZND2="g_15feeb0@0" Pin0InfoVect0LinkObjId="g_16035f0_0" Pin0InfoVect1LinkObjId="SW-199794_0" Pin0InfoVect2LinkObjId="g_15feeb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199792_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4301,-396 4301,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_163e6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4301,-381 4301,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="30482@x" ObjectIDND1="30481@x" ObjectIDND2="g_15feeb0@0" ObjectIDZND0="g_16035f0@0" Pin0InfoVect0LinkObjId="g_16035f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-199792_0" Pin1InfoVect1LinkObjId="SW-199794_0" Pin1InfoVect2LinkObjId="g_15feeb0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4301,-381 4301,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_163e900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4276,-365 4276,-381 4302,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_15feeb0@0" ObjectIDZND0="30482@x" ObjectIDZND1="g_16035f0@0" ObjectIDZND2="30481@x" Pin0InfoVect0LinkObjId="SW-199792_0" Pin0InfoVect1LinkObjId="g_16035f0_0" Pin0InfoVect2LinkObjId="SW-199794_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15feeb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4276,-365 4276,-381 4302,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_163eb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4613,-348 4613,-389 4582,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="29007@0" ObjectIDZND0="23633@x" ObjectIDZND1="g_12f71b0@0" ObjectIDZND2="g_12f2ac0@0" Pin0InfoVect0LinkObjId="SW-128065_0" Pin0InfoVect1LinkObjId="g_12f71b0_0" Pin0InfoVect2LinkObjId="g_12f2ac0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128617_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4613,-348 4613,-389 4582,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_163f850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4582,-402 4582,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="23633@1" ObjectIDZND0="29007@x" ObjectIDZND1="g_12f71b0@0" ObjectIDZND2="g_12f2ac0@0" Pin0InfoVect0LinkObjId="SW-128617_0" Pin0InfoVect1LinkObjId="g_12f71b0_0" Pin0InfoVect2LinkObjId="g_12f2ac0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128065_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4582,-402 4582,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_163fa90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4582,-389 4582,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="29007@x" ObjectIDND1="23633@x" ObjectIDND2="g_12f2ac0@0" ObjectIDZND0="g_12f71b0@0" Pin0InfoVect0LinkObjId="g_12f71b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-128617_0" Pin1InfoVect1LinkObjId="SW-128065_0" Pin1InfoVect2LinkObjId="g_12f2ac0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4582,-389 4582,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_163fcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4582,-389 4557,-389 4557,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="29007@x" ObjectIDND1="23633@x" ObjectIDND2="g_12f71b0@0" ObjectIDZND0="g_12f2ac0@0" Pin0InfoVect0LinkObjId="g_12f2ac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-128617_0" Pin1InfoVect1LinkObjId="SW-128065_0" Pin1InfoVect2LinkObjId="g_12f71b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4582,-389 4557,-389 4557,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_163ff50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4758,-353 4758,-390 4727,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="29005@0" ObjectIDZND0="17421@x" ObjectIDZND1="g_13744f0@0" ObjectIDZND2="g_136fe00@0" Pin0InfoVect0LinkObjId="SW-79354_0" Pin0InfoVect1LinkObjId="g_13744f0_0" Pin0InfoVect2LinkObjId="g_136fe00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-79355_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4758,-353 4758,-390 4727,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1640c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4727,-406 4727,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="17421@1" ObjectIDZND0="29005@x" ObjectIDZND1="g_13744f0@0" ObjectIDZND2="g_136fe00@0" Pin0InfoVect0LinkObjId="SW-79355_0" Pin0InfoVect1LinkObjId="g_13744f0_0" Pin0InfoVect2LinkObjId="g_136fe00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-79354_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4727,-406 4727,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1640ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4727,-390 4727,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="29005@x" ObjectIDND1="17421@x" ObjectIDND2="g_136fe00@0" ObjectIDZND0="g_13744f0@0" Pin0InfoVect0LinkObjId="g_13744f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-79355_0" Pin1InfoVect1LinkObjId="SW-79354_0" Pin1InfoVect2LinkObjId="g_136fe00_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4727,-390 4727,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1641100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4727,-390 4702,-390 4702,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="29005@x" ObjectIDND1="17421@x" ObjectIDND2="g_13744f0@0" ObjectIDZND0="g_136fe00@0" Pin0InfoVect0LinkObjId="g_136fe00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-79355_0" Pin1InfoVect1LinkObjId="SW-79354_0" Pin1InfoVect2LinkObjId="g_13744f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4727,-390 4702,-390 4702,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1641360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4888,-345 4888,-385 4857,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="29009@0" ObjectIDZND0="17423@x" ObjectIDZND1="g_131ca30@0" ObjectIDZND2="g_1318340@0" Pin0InfoVect0LinkObjId="SW-79378_0" Pin0InfoVect1LinkObjId="g_131ca30_0" Pin0InfoVect2LinkObjId="g_1318340_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-79379_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4888,-345 4888,-385 4857,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1642050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4857,-398 4857,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="17423@1" ObjectIDZND0="29009@x" ObjectIDZND1="g_131ca30@0" ObjectIDZND2="g_1318340@0" Pin0InfoVect0LinkObjId="SW-79379_0" Pin0InfoVect1LinkObjId="g_131ca30_0" Pin0InfoVect2LinkObjId="g_1318340_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-79378_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4857,-398 4857,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16422b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4857,-385 4857,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="29009@x" ObjectIDND1="17423@x" ObjectIDND2="g_1318340@0" ObjectIDZND0="g_131ca30@0" Pin0InfoVect0LinkObjId="g_131ca30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-79379_0" Pin1InfoVect1LinkObjId="SW-79378_0" Pin1InfoVect2LinkObjId="g_1318340_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4857,-385 4857,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1642510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4857,-385 4832,-385 4832,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="29009@x" ObjectIDND1="17423@x" ObjectIDND2="g_131ca30@0" ObjectIDZND0="g_1318340@0" Pin0InfoVect0LinkObjId="g_1318340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-79379_0" Pin1InfoVect1LinkObjId="SW-79378_0" Pin1InfoVect2LinkObjId="g_131ca30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4857,-385 4832,-385 4832,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1642770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5344,-347 5344,-382 5313,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="28997@0" ObjectIDZND0="16885@x" ObjectIDZND1="g_12c5be0@0" ObjectIDZND2="g_12c14f0@0" Pin0InfoVect0LinkObjId="SW-77968_0" Pin0InfoVect1LinkObjId="g_12c5be0_0" Pin0InfoVect2LinkObjId="g_12c14f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77969_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5344,-347 5344,-382 5313,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1643460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5313,-400 5313,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="16885@1" ObjectIDZND0="28997@x" ObjectIDZND1="g_12c5be0@0" ObjectIDZND2="g_12c14f0@0" Pin0InfoVect0LinkObjId="SW-77969_0" Pin0InfoVect1LinkObjId="g_12c5be0_0" Pin0InfoVect2LinkObjId="g_12c14f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77968_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5313,-400 5313,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16436c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5313,-382 5313,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28997@x" ObjectIDND1="16885@x" ObjectIDND2="g_12c14f0@0" ObjectIDZND0="g_12c5be0@0" Pin0InfoVect0LinkObjId="g_12c5be0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-77969_0" Pin1InfoVect1LinkObjId="SW-77968_0" Pin1InfoVect2LinkObjId="g_12c14f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5313,-382 5313,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1643920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5313,-382 5288,-382 5288,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="28997@x" ObjectIDND1="16885@x" ObjectIDND2="g_12c5be0@0" ObjectIDZND0="g_12c14f0@0" Pin0InfoVect0LinkObjId="g_12c14f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-77969_0" Pin1InfoVect1LinkObjId="SW-77968_0" Pin1InfoVect2LinkObjId="g_12c5be0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5313,-382 5288,-382 5288,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1643b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5623,-345 5623,-381 5594,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="29002@0" ObjectIDZND0="17659@x" ObjectIDZND1="g_13ab5f0@0" ObjectIDZND2="g_13a6f00@0" Pin0InfoVect0LinkObjId="SW-128061_0" Pin0InfoVect1LinkObjId="g_13ab5f0_0" Pin0InfoVect2LinkObjId="g_13a6f00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128062_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5623,-345 5623,-381 5594,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1644870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5594,-400 5594,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="17659@1" ObjectIDZND0="29002@x" ObjectIDZND1="g_13ab5f0@0" ObjectIDZND2="g_13a6f00@0" Pin0InfoVect0LinkObjId="SW-128062_0" Pin0InfoVect1LinkObjId="g_13ab5f0_0" Pin0InfoVect2LinkObjId="g_13a6f00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128061_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5594,-400 5594,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1644ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5594,-381 5594,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="29002@x" ObjectIDND1="17659@x" ObjectIDND2="g_13a6f00@0" ObjectIDZND0="g_13ab5f0@0" Pin0InfoVect0LinkObjId="g_13ab5f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-128062_0" Pin1InfoVect1LinkObjId="SW-128061_0" Pin1InfoVect2LinkObjId="g_13a6f00_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5594,-381 5594,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1644d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5594,-381 5567,-381 5567,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="29002@x" ObjectIDND1="17659@x" ObjectIDND2="g_13ab5f0@0" ObjectIDZND0="g_13a6f00@0" Pin0InfoVect0LinkObjId="g_13a6f00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-128062_0" Pin1InfoVect1LinkObjId="SW-128061_0" Pin1InfoVect2LinkObjId="g_13ab5f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5594,-381 5567,-381 5567,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1644f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5775,-345 5775,-384 5746,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="29004@0" ObjectIDZND0="17661@x" ObjectIDZND1="g_138b770@0" ObjectIDZND2="g_1425af0@0" Pin0InfoVect0LinkObjId="SW-128063_0" Pin0InfoVect1LinkObjId="g_138b770_0" Pin0InfoVect2LinkObjId="g_1425af0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128064_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5775,-345 5775,-384 5746,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1645c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5746,-400 5746,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="17661@1" ObjectIDZND0="29004@x" ObjectIDZND1="g_138b770@0" ObjectIDZND2="g_1425af0@0" Pin0InfoVect0LinkObjId="SW-128064_0" Pin0InfoVect1LinkObjId="g_138b770_0" Pin0InfoVect2LinkObjId="g_1425af0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128063_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5746,-400 5746,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1645ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5746,-384 5746,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="29004@x" ObjectIDND1="17661@x" ObjectIDND2="g_1425af0@0" ObjectIDZND0="g_138b770@0" Pin0InfoVect0LinkObjId="g_138b770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-128064_0" Pin1InfoVect1LinkObjId="SW-128063_0" Pin1InfoVect2LinkObjId="g_1425af0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5746,-384 5746,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1646140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5746,-384 5719,-384 5719,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="29004@x" ObjectIDND1="17661@x" ObjectIDND2="g_138b770@0" ObjectIDZND0="g_1425af0@0" Pin0InfoVect0LinkObjId="g_1425af0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-128064_0" Pin1InfoVect1LinkObjId="SW-128063_0" Pin1InfoVect2LinkObjId="g_138b770_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5746,-384 5719,-384 5719,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16463a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5480,-343 5480,-381 5451,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="29000@0" ObjectIDZND0="16913@x" ObjectIDZND1="g_13b5740@0" ObjectIDZND2="g_13b1100@0" Pin0InfoVect0LinkObjId="SW-77996_0" Pin0InfoVect1LinkObjId="g_13b5740_0" Pin0InfoVect2LinkObjId="g_13b1100_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77997_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5480,-343 5480,-381 5451,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16470b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5451,-398 5451,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="16913@1" ObjectIDZND0="29000@x" ObjectIDZND1="g_13b5740@0" ObjectIDZND2="g_13b1100@0" Pin0InfoVect0LinkObjId="SW-77997_0" Pin0InfoVect1LinkObjId="g_13b5740_0" Pin0InfoVect2LinkObjId="g_13b1100_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77996_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5451,-398 5451,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1647310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5451,-381 5451,-351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="29000@x" ObjectIDND1="16913@x" ObjectIDND2="g_13b1100@0" ObjectIDZND0="g_13b5740@0" Pin0InfoVect0LinkObjId="g_13b5740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-77997_0" Pin1InfoVect1LinkObjId="SW-77996_0" Pin1InfoVect2LinkObjId="g_13b1100_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5451,-381 5451,-351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1647570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5451,-381 5424,-381 5424,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="29000@x" ObjectIDND1="16913@x" ObjectIDND2="g_13b5740@0" ObjectIDZND0="g_13b1100@0" Pin0InfoVect0LinkObjId="g_13b1100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-77997_0" Pin1InfoVect1LinkObjId="SW-77996_0" Pin1InfoVect2LinkObjId="g_13b5740_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5451,-381 5424,-381 5424,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16477d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5773,-195 5773,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29003@0" ObjectIDZND0="17426@0" Pin0InfoVect0LinkObjId="g_1647a30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128513_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5773,-195 5773,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1647a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5344,-199 5344,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16886@0" ObjectIDZND0="17426@0" Pin0InfoVect0LinkObjId="g_16477d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128331_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5344,-199 5344,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1647c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5479,-194 5479,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28999@0" ObjectIDZND0="17426@0" Pin0InfoVect0LinkObjId="g_16477d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128393_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5479,-194 5479,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1647ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5622,-196 5622,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29001@0" ObjectIDZND0="17426@0" Pin0InfoVect0LinkObjId="g_16477d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128453_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5622,-196 5622,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1648150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4758,-205 4758,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29006@0" ObjectIDZND0="17426@0" Pin0InfoVect0LinkObjId="g_16477d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4758,-205 4758,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16483b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4888,-197 4888,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29010@0" ObjectIDZND0="17426@0" Pin0InfoVect0LinkObjId="g_16477d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128732_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4888,-197 4888,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_164aec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4184,-196 4184,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="30476@0" ObjectIDZND0="17426@0" Pin0InfoVect0LinkObjId="g_16477d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199748_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4184,-196 4184,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_164b6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3660,-193 3660,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="17655@0" ObjectIDZND0="17426@0" Pin0InfoVect0LinkObjId="g_16477d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-128160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3660,-193 3660,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_164bf20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3663,-141 3629,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_132e940@0" ObjectIDZND0="17656@x" ObjectIDZND1="17655@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-128161_0" Pin0InfoVect1LinkObjId="SW-128160_0" Pin0InfoVect2LinkObjId="g_1442840_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_132e940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3663,-141 3629,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_164ca10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3629,-229 3629,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="17656@x" ObjectIDND1="17655@x" ObjectIDZND0="g_132e940@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_132e940_0" Pin0InfoVect1LinkObjId="g_1442840_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-128161_0" Pin1InfoVect1LinkObjId="SW-128160_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3629,-229 3629,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_164cc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3629,-141 3629,-79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_132e940@0" ObjectIDND1="17656@x" ObjectIDND2="17655@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_1442840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_132e940_0" Pin1InfoVect1LinkObjId="SW-128161_0" Pin1InfoVect2LinkObjId="SW-128160_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3629,-141 3629,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_164ced0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4187,-144 4153,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_15f19f0@0" ObjectIDZND0="30476@x" ObjectIDZND1="30478@x" ObjectIDZND2="37628@x" Pin0InfoVect0LinkObjId="SW-199748_0" Pin0InfoVect1LinkObjId="SW-199747_0" Pin0InfoVect2LinkObjId="EC-LF_BC.LD_036Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15f19f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4187,-144 4153,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_164d9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4153,-232 4153,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="30476@x" ObjectIDND1="30478@x" ObjectIDZND0="g_15f19f0@0" ObjectIDZND1="37628@x" Pin0InfoVect0LinkObjId="g_15f19f0_0" Pin0InfoVect1LinkObjId="EC-LF_BC.LD_036Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-199748_0" Pin1InfoVect1LinkObjId="SW-199747_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4153,-232 4153,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_164dc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4153,-144 4153,-82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_15f19f0@0" ObjectIDND1="30476@x" ObjectIDND2="30478@x" ObjectIDZND0="37628@0" Pin0InfoVect0LinkObjId="EC-LF_BC.LD_036Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_15f19f0_0" Pin1InfoVect1LinkObjId="SW-199748_0" Pin1InfoVect2LinkObjId="SW-199747_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4153,-144 4153,-82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_164de80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4889,-145 4857,-145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_1345e40@0" ObjectIDZND0="29010@x" ObjectIDZND1="17052@x" ObjectIDZND2="34091@x" Pin0InfoVect0LinkObjId="SW-128732_0" Pin0InfoVect1LinkObjId="SW-78136_0" Pin0InfoVect2LinkObjId="EC-LF_BC.LD_034Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1345e40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4889,-145 4857,-145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_164e970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4857,-233 4857,-145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="29010@x" ObjectIDND1="17052@x" ObjectIDZND0="g_1345e40@0" ObjectIDZND1="34091@x" Pin0InfoVect0LinkObjId="g_1345e40_0" Pin0InfoVect1LinkObjId="EC-LF_BC.LD_034Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-128732_0" Pin1InfoVect1LinkObjId="SW-78136_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4857,-233 4857,-145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_164ebd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4857,-145 4857,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_1345e40@0" ObjectIDND1="29010@x" ObjectIDND2="17052@x" ObjectIDZND0="34091@0" Pin0InfoVect0LinkObjId="EC-LF_BC.LD_034Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1345e40_0" Pin1InfoVect1LinkObjId="SW-128732_0" Pin1InfoVect2LinkObjId="SW-78136_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4857,-145 4857,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_164ee30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4726,-150 4727,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_137a8a0@0" ObjectIDND1="34092@x" ObjectIDZND0="29006@x" ObjectIDZND1="17422@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-78092_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_137a8a0_0" Pin1InfoVect1LinkObjId="EC-LF_BC.LD_033Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4726,-150 4727,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_164f920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4758,-150 4726,-150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_137a8a0@0" ObjectIDZND0="29006@x" ObjectIDZND1="17422@x" ObjectIDZND2="34092@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-78092_0" Pin0InfoVect2LinkObjId="EC-LF_BC.LD_033Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_137a8a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4758,-150 4726,-150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_164fb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4726,-150 4726,-94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="29006@x" ObjectIDND1="17422@x" ObjectIDND2="g_137a8a0@0" ObjectIDZND0="34092@0" Pin0InfoVect0LinkObjId="EC-LF_BC.LD_033Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-78092_0" Pin1InfoVect2LinkObjId="g_137a8a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4726,-150 4726,-94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1650670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4758,-241 4727,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="29006@1" ObjectIDZND0="g_137a8a0@0" ObjectIDZND1="34092@x" ObjectIDZND2="17422@x" Pin0InfoVect0LinkObjId="g_137a8a0_0" Pin0InfoVect1LinkObjId="EC-LF_BC.LD_033Ld_0" Pin0InfoVect2LinkObjId="SW-78092_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4758,-241 4727,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16508d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4727,-241 4727,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_137a8a0@0" ObjectIDND1="34092@x" ObjectIDND2="29006@x" ObjectIDZND0="17422@1" Pin0InfoVect0LinkObjId="SW-78092_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_137a8a0_0" Pin1InfoVect1LinkObjId="EC-LF_BC.LD_033Ld_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4727,-241 4727,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1650b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5347,-147 5313,-147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1361110@0" ObjectIDZND0="16886@x" ObjectIDZND1="28996@x" ObjectIDZND2="g_1957270@0" Pin0InfoVect0LinkObjId="SW-128331_0" Pin0InfoVect1LinkObjId="SW-77965_0" Pin0InfoVect2LinkObjId="g_1957270_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1361110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5347,-147 5313,-147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1651620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5313,-239 5313,-147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="16886@x" ObjectIDND1="28996@x" ObjectIDZND0="g_1361110@0" ObjectIDZND1="g_1957270@0" Pin0InfoVect0LinkObjId="g_1361110_0" Pin0InfoVect1LinkObjId="g_1957270_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-128331_0" Pin1InfoVect1LinkObjId="SW-77965_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5313,-239 5313,-147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1651880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5483,-143 5451,-143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_13bd2f0@0" ObjectIDZND0="28999@x" ObjectIDZND1="16914@x" ObjectIDZND2="34090@x" Pin0InfoVect0LinkObjId="SW-128393_0" Pin0InfoVect1LinkObjId="SW-77975_0" Pin0InfoVect2LinkObjId="EC-LF_BC.LD_042Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13bd2f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5483,-143 5451,-143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1652370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5451,-233 5451,-143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="28999@x" ObjectIDND1="16914@x" ObjectIDZND0="g_13bd2f0@0" ObjectIDZND1="34090@x" Pin0InfoVect0LinkObjId="g_13bd2f0_0" Pin0InfoVect1LinkObjId="EC-LF_BC.LD_042Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-128393_0" Pin1InfoVect1LinkObjId="SW-77975_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5451,-233 5451,-143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16525d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5451,-143 5451,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_13bd2f0@0" ObjectIDND1="28999@x" ObjectIDND2="16914@x" ObjectIDZND0="34090@0" Pin0InfoVect0LinkObjId="EC-LF_BC.LD_042Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13bd2f0_0" Pin1InfoVect1LinkObjId="SW-128393_0" Pin1InfoVect2LinkObjId="SW-77975_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5451,-143 5451,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1652830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5626,-145 5594,-145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_13596b0@0" ObjectIDZND0="29001@x" ObjectIDZND1="17660@x" ObjectIDZND2="34088@x" Pin0InfoVect0LinkObjId="SW-128453_0" Pin0InfoVect1LinkObjId="SW-78018_0" Pin0InfoVect2LinkObjId="EC-LF_BC.LD_043Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13596b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5626,-145 5594,-145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1653320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5594,-233 5594,-145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="29001@x" ObjectIDND1="17660@x" ObjectIDZND0="g_13596b0@0" ObjectIDZND1="34088@x" Pin0InfoVect0LinkObjId="g_13596b0_0" Pin0InfoVect1LinkObjId="EC-LF_BC.LD_043Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-128453_0" Pin1InfoVect1LinkObjId="SW-78018_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5594,-233 5594,-145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1653580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5594,-145 5594,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_13596b0@0" ObjectIDND1="29001@x" ObjectIDND2="17660@x" ObjectIDZND0="34088@0" Pin0InfoVect0LinkObjId="EC-LF_BC.LD_043Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13596b0_0" Pin1InfoVect1LinkObjId="SW-128453_0" Pin1InfoVect2LinkObjId="SW-78018_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5594,-145 5594,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16537e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5778,-145 5746,-145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_13a0370@0" ObjectIDZND0="17662@x" ObjectIDZND1="29003@x" ObjectIDZND2="34089@x" Pin0InfoVect0LinkObjId="SW-78001_0" Pin0InfoVect1LinkObjId="SW-128513_0" Pin0InfoVect2LinkObjId="EC-LF_BC.LD_044Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13a0370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5778,-145 5746,-145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16542d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5746,-233 5746,-145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="17662@x" ObjectIDND1="29003@x" ObjectIDZND0="g_13a0370@0" ObjectIDZND1="34089@x" Pin0InfoVect0LinkObjId="g_13a0370_0" Pin0InfoVect1LinkObjId="EC-LF_BC.LD_044Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-78001_0" Pin1InfoVect1LinkObjId="SW-128513_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5746,-233 5746,-145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1654530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5746,-145 5746,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_13a0370@0" ObjectIDND1="17662@x" ObjectIDND2="29003@x" ObjectIDZND0="34089@0" Pin0InfoVect0LinkObjId="EC-LF_BC.LD_044Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13a0370_0" Pin1InfoVect1LinkObjId="SW-78001_0" Pin1InfoVect2LinkObjId="SW-128513_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5746,-145 5746,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_194b000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4779,-802 4667,-802 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="17136@x" ObjectIDZND0="g_194a2d0@0" Pin0InfoVect0LinkObjId="g_194a2d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_194f3e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4779,-802 4667,-802 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_194b260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4779,-730 4733,-730 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer2" EndDevType0="lightningRod" ObjectIDND0="28994@x" ObjectIDND1="g_194cfc0@0" ObjectIDND2="17136@x" ObjectIDZND0="g_194c210@0" Pin0InfoVect0LinkObjId="g_194c210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-128318_0" Pin1InfoVect1LinkObjId="g_194cfc0_0" Pin1InfoVect2LinkObjId="g_194f3e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4779,-730 4733,-730 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_194bd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4779,-730 4779,-724 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="transformer2" EndDevType0="switch" ObjectIDND0="g_194c210@0" ObjectIDND1="g_194cfc0@0" ObjectIDND2="17136@x" ObjectIDZND0="28994@0" Pin0InfoVect0LinkObjId="SW-128318_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_194c210_0" Pin1InfoVect1LinkObjId="g_194cfc0_0" Pin1InfoVect2LinkObjId="g_194f3e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4779,-730 4779,-724 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_194bfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4779,-736 4818,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="17136@x" ObjectIDND1="28994@x" ObjectIDND2="g_194c210@0" ObjectIDZND0="g_194cfc0@0" Pin0InfoVect0LinkObjId="g_194cfc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_194f3e0_0" Pin1InfoVect1LinkObjId="SW-128318_0" Pin1InfoVect2LinkObjId="g_194c210_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4779,-736 4818,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_194dd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4778,-877 4778,-890 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_194df90@1" ObjectIDZND0="16868@0" Pin0InfoVect0LinkObjId="SW-128321_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_194df90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4778,-877 4778,-890 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_194ebb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4778,-968 4778,-979 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16879@1" ObjectIDZND0="30496@0" Pin0InfoVect0LinkObjId="g_16172f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199688_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4778,-968 4778,-979 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_194f3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4778,-838 4779,-822 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_194df90@0" ObjectIDZND0="17136@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_194df90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4778,-838 4779,-822 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_194fed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4779,-742 4779,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="17136@1" ObjectIDZND0="g_194cfc0@0" ObjectIDZND1="28994@x" ObjectIDZND2="g_194c210@0" Pin0InfoVect0LinkObjId="g_194cfc0_0" Pin0InfoVect1LinkObjId="SW-128318_0" Pin0InfoVect2LinkObjId="g_194c210_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_194f3e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4779,-742 4779,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1950130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4779,-736 4779,-730 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_194cfc0@0" ObjectIDND1="17136@x" ObjectIDZND0="28994@x" ObjectIDZND1="g_194c210@0" Pin0InfoVect0LinkObjId="SW-128318_0" Pin0InfoVect1LinkObjId="g_194c210_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_194cfc0_0" Pin1InfoVect1LinkObjId="g_194f3e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4779,-736 4779,-730 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1950390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4624,-693 4624,-664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" EndDevType1="earth" EndDevType2="switch" ObjectIDND0="g_1506070@0" ObjectIDZND0="29018@x" ObjectIDZND1="g_1506070@0" ObjectIDZND2="29018@x" Pin0InfoVect0LinkObjId="SW-191866_0" Pin0InfoVect1LinkObjId="g_1506070_0" Pin0InfoVect2LinkObjId="SW-191866_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1506070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4624,-693 4624,-664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1950e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4607,-664 4624,-664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" EndDevType1="earth" EndDevType2="switch" ObjectIDND0="29018@1" ObjectIDZND0="g_1506070@0" ObjectIDZND1="g_1506070@0" ObjectIDZND2="29018@x" Pin0InfoVect0LinkObjId="g_1506070_0" Pin0InfoVect1LinkObjId="g_1506070_0" Pin0InfoVect2LinkObjId="SW-191866_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191866_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4607,-664 4624,-664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19510e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4624,-664 4624,-661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" BeginDevType1="switch" EndDevType0="earth" EndDevType1="switch" ObjectIDND0="g_1506070@0" ObjectIDND1="29018@x" ObjectIDZND0="g_1506070@0" ObjectIDZND1="29018@x" Pin0InfoVect0LinkObjId="g_1506070_0" Pin0InfoVect1LinkObjId="SW-191866_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1506070_0" Pin1InfoVect1LinkObjId="SW-191866_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4624,-664 4624,-661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1951340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4452,-692 4452,-677 4501,-677 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1501fd0@0" ObjectIDZND0="g_12fdf10@0" ObjectIDZND1="29018@x" ObjectIDZND2="29017@x" Pin0InfoVect0LinkObjId="g_12fdf10_0" Pin0InfoVect1LinkObjId="SW-191866_0" Pin0InfoVect2LinkObjId="SW-191865_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1501fd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4452,-692 4452,-677 4501,-677 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1951e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4501,-737 4501,-677 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_12fdf10@1" ObjectIDZND0="g_1501fd0@0" ObjectIDZND1="29018@x" ObjectIDZND2="29017@x" Pin0InfoVect0LinkObjId="g_1501fd0_0" Pin0InfoVect1LinkObjId="SW-191866_0" Pin0InfoVect2LinkObjId="SW-191865_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12fdf10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4501,-737 4501,-677 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1952090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4501,-677 4501,-664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1501fd0@0" ObjectIDND1="g_12fdf10@0" ObjectIDZND0="29018@x" ObjectIDZND1="29017@x" Pin0InfoVect0LinkObjId="SW-191866_0" Pin0InfoVect1LinkObjId="SW-191865_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1501fd0_0" Pin1InfoVect1LinkObjId="g_12fdf10_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4501,-677 4501,-664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1953a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3629,-542 3629,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="breaker" BeginDevType2="busSection" EndDevType0="busSection" EndDevType1="busSection" EndDevType2="breaker" ObjectIDND0="17654@x" ObjectIDND1="17654@x" ObjectIDND2="17419@0" ObjectIDZND0="17419@0" ObjectIDZND1="17419@0" ObjectIDZND2="17654@x" Pin0InfoVect0LinkObjId="g_147f610_0" Pin0InfoVect1LinkObjId="g_147f610_0" Pin0InfoVect2LinkObjId="SW-79092_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-79092_0" Pin1InfoVect1LinkObjId="SW-79092_0" Pin1InfoVect2LinkObjId="g_147f610_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3629,-542 3629,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1953ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3629,-550 3629,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="breaker" BeginDevType2="busSection" EndDevType0="busSection" ObjectIDND0="17654@x" ObjectIDND1="17654@x" ObjectIDND2="17419@0" ObjectIDZND0="17419@0" Pin0InfoVect0LinkObjId="g_147f610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-79092_0" Pin1InfoVect1LinkObjId="SW-79092_0" Pin1InfoVect2LinkObjId="g_147f610_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3629,-550 3629,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1954770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3629,-451 3629,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" EndDevType1="busSection" EndDevType2="breaker" ObjectIDND0="17654@1" ObjectIDZND0="17419@0" ObjectIDZND1="17419@0" ObjectIDZND2="17654@x" Pin0InfoVect0LinkObjId="g_147f610_0" Pin0InfoVect1LinkObjId="g_147f610_0" Pin0InfoVect2LinkObjId="SW-79092_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-79092_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3629,-451 3629,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19549d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3629,-542 3629,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="breaker" EndDevType0="breaker" EndDevType1="busSection" ObjectIDND0="17419@0" ObjectIDND1="17654@x" ObjectIDZND0="17654@x" ObjectIDZND1="17419@0" Pin0InfoVect0LinkObjId="SW-79092_0" Pin0InfoVect1LinkObjId="g_147f610_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_147f610_0" Pin1InfoVect1LinkObjId="SW-79092_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3629,-542 3629,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1955900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4594,-202 4583,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="g_1954c30@0" ObjectIDZND0="29008@x" ObjectIDZND1="17426@0" Pin0InfoVect0LinkObjId="SW-78026_0" Pin0InfoVect1LinkObjId="g_16477d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1954c30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4594,-202 4583,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19563f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4582,-243 4582,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="busSection" ObjectIDND0="29008@1" ObjectIDZND0="g_1954c30@0" ObjectIDZND1="17426@0" Pin0InfoVect0LinkObjId="g_1954c30_0" Pin0InfoVect1LinkObjId="g_16477d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78026_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4582,-243 4582,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1956650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4582,-202 4582,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="g_1954c30@0" ObjectIDND1="29008@x" ObjectIDZND0="17426@0" Pin0InfoVect0LinkObjId="g_16477d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1954c30_0" Pin1InfoVect1LinkObjId="SW-78026_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4582,-202 4582,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1957c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5313,-147 5313,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1361110@0" ObjectIDND1="16886@x" ObjectIDND2="28996@x" ObjectIDZND0="g_1957270@0" Pin0InfoVect0LinkObjId="g_1957270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1361110_0" Pin1InfoVect1LinkObjId="SW-128331_0" Pin1InfoVect2LinkObjId="SW-77965_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5313,-147 5313,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1957e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5313,-83 5313,-74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_1957270@1" ObjectIDZND0="34087@0" Pin0InfoVect0LinkObjId="EC-LF_BC.LD_041Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1957270_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5313,-83 5313,-74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1958060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5864,-480 5864,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="29019@1" ObjectIDZND0="g_13d6d40@0" Pin0InfoVect0LinkObjId="g_13d6d40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-191867_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5864,-480 5864,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19582c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5864,-421 5864,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_13d6d40@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_1442840_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13d6d40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5864,-421 5864,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1958520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5577,-724 5531,-724 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="16852@x" ObjectIDND1="17130@x" ObjectIDZND0="g_1958780@0" Pin0InfoVect0LinkObjId="g_1958780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-77762_0" Pin1InfoVect1LinkObjId="g_195a240_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5577,-724 5531,-724 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1959fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5577,-714 5577,-724 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="16852@0" ObjectIDZND0="g_1958780@0" ObjectIDZND1="17130@x" Pin0InfoVect0LinkObjId="g_1958780_0" Pin0InfoVect1LinkObjId="g_195a240_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-77762_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5577,-714 5577,-724 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_195a240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5577,-724 5577,-719 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_1958780@0" ObjectIDND1="16852@x" ObjectIDZND0="17130@1" Pin0InfoVect0LinkObjId="g_195b6b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1958780_0" Pin1InfoVect1LinkObjId="SW-77762_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5577,-724 5577,-719 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_195a4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5577,-724 5577,-738 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="transformer2" ObjectIDND0="g_1958780@0" ObjectIDND1="16852@x" ObjectIDND2="17130@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1958780_0" Pin1InfoVect1LinkObjId="SW-77762_0" Pin1InfoVect2LinkObjId="g_195a240_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5577,-724 5577,-738 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_195b450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5578,-873 5578,-891 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_195a700@0" ObjectIDZND0="16844@0" Pin0InfoVect0LinkObjId="SW-79076_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_195a700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5578,-873 5578,-891 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_195b6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5578,-841 5578,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_195a700@1" ObjectIDZND0="17130@0" Pin0InfoVect0LinkObjId="g_195a240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_195a700_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5578,-841 5578,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_195c530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5232,-745 5186,-745 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="17131@x" ObjectIDND1="16862@x" ObjectIDZND0="g_195c720@0" Pin0InfoVect0LinkObjId="g_195c720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_160bf50_0" Pin1InfoVect1LinkObjId="SW-77825_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5232,-745 5186,-745 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_195db30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5232,-752 5232,-745 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="17131@1" ObjectIDZND0="g_195c720@0" ObjectIDZND1="16862@x" Pin0InfoVect0LinkObjId="g_195c720_0" Pin0InfoVect1LinkObjId="SW-77825_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_160bf50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5232,-752 5232,-745 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_195dd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5232,-745 5231,-734 5231,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_195c720@0" ObjectIDND1="17131@x" ObjectIDZND0="16862@0" Pin0InfoVect0LinkObjId="SW-77825_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_195c720_0" Pin1InfoVect1LinkObjId="g_160bf50_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5232,-745 5231,-734 5231,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1976540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4778,-926 4826,-926 4826,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="16879@x" ObjectIDND1="16868@x" ObjectIDZND0="17653@1" Pin0InfoVect0LinkObjId="SW-127959_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-199688_0" Pin1InfoVect1LinkObjId="SW-128321_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4778,-926 4826,-926 4826,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1976f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4778,-932 4778,-926 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="16879@0" ObjectIDZND0="16868@x" ObjectIDZND1="17653@x" Pin0InfoVect0LinkObjId="SW-128321_0" Pin0InfoVect1LinkObjId="SW-127959_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-199688_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4778,-932 4778,-926 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1977170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4778,-926 4778,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="16879@x" ObjectIDND1="17653@x" ObjectIDZND0="16868@1" Pin0InfoVect0LinkObjId="SW-128321_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-199688_0" Pin1InfoVect1LinkObjId="SW-127959_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4778,-926 4778,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1981a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3826,-255 3826,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_1501280@1" ObjectIDZND0="17128@0" Pin0InfoVect0LinkObjId="CB-LF_BC.LF_BC_031_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1501280_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3826,-255 3826,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1982250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5625,-913 5625,-925 5578,-925 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="23627@1" ObjectIDZND0="16857@x" ObjectIDZND1="16844@x" Pin0InfoVect0LinkObjId="SW-79077_0" Pin0InfoVect1LinkObjId="SW-79076_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-79080_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5625,-913 5625,-925 5578,-925 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1982d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5578,-932 5578,-925 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="16857@0" ObjectIDZND0="23627@x" ObjectIDZND1="16844@x" Pin0InfoVect0LinkObjId="SW-79080_0" Pin0InfoVect1LinkObjId="SW-79076_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-79077_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5578,-932 5578,-925 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1982f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5578,-925 5578,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="23627@x" ObjectIDND1="16857@x" ObjectIDZND0="16844@1" Pin0InfoVect0LinkObjId="SW-79076_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-79080_0" Pin1InfoVect1LinkObjId="SW-79077_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5578,-925 5578,-919 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectPoint_Layer"/><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-78946" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5413.095870 -890.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78946" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16863"/>
     <cge:Term_Ref ObjectID="24284"/>
    <cge:TPSR_Ref TObjectID="16863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-78947" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5413.095870 -890.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78947" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16863"/>
     <cge:Term_Ref ObjectID="24284"/>
    <cge:TPSR_Ref TObjectID="16863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-78942" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5413.095870 -890.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78942" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16863"/>
     <cge:Term_Ref ObjectID="24284"/>
    <cge:TPSR_Ref TObjectID="16863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-127716" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5691.564897 -1045.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127716" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17647"/>
     <cge:Term_Ref ObjectID="24266"/>
    <cge:TPSR_Ref TObjectID="17647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-127717" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5691.564897 -1045.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127717" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17647"/>
     <cge:Term_Ref ObjectID="24266"/>
    <cge:TPSR_Ref TObjectID="17647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-127708" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5691.564897 -1045.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127708" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17647"/>
     <cge:Term_Ref ObjectID="24266"/>
    <cge:TPSR_Ref TObjectID="17647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-127629" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5355.095870 -761.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127629" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17131"/>
     <cge:Term_Ref ObjectID="24000"/>
    <cge:TPSR_Ref TObjectID="17131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-127635" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5355.095870 -761.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127635" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17131"/>
     <cge:Term_Ref ObjectID="24000"/>
    <cge:TPSR_Ref TObjectID="17131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-127622" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5688.259587 -761.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127622" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17130"/>
     <cge:Term_Ref ObjectID="23993"/>
    <cge:TPSR_Ref TObjectID="17130"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-127628" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5688.259587 -761.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127628" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17130"/>
     <cge:Term_Ref ObjectID="23993"/>
    <cge:TPSR_Ref TObjectID="17130"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-127636" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4944.000000 -778.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127636" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17136"/>
     <cge:Term_Ref ObjectID="24001"/>
    <cge:TPSR_Ref TObjectID="17136"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-199932" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4944.000000 -778.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="199932" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17136"/>
     <cge:Term_Ref ObjectID="24001"/>
    <cge:TPSR_Ref TObjectID="17136"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-199920" prefix="Ua  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3774.000000 -1101.000000) translate(0,12)">Ua   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="199920" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30496"/>
     <cge:Term_Ref ObjectID="43405"/>
    <cge:TPSR_Ref TObjectID="30496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-199922" prefix="Ub " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3774.000000 -1101.000000) translate(0,27)">Ub  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="199922" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30496"/>
     <cge:Term_Ref ObjectID="43405"/>
    <cge:TPSR_Ref TObjectID="30496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-199921" prefix="Uc " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3774.000000 -1101.000000) translate(0,42)">Uc  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="199921" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30496"/>
     <cge:Term_Ref ObjectID="43405"/>
    <cge:TPSR_Ref TObjectID="30496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-199926" prefix="3Uo " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3774.000000 -1101.000000) translate(0,57)">3Uo  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="199926" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30496"/>
     <cge:Term_Ref ObjectID="43405"/>
    <cge:TPSR_Ref TObjectID="30496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-199925" prefix="Uab " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3774.000000 -1101.000000) translate(0,72)">Uab  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="199925" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30496"/>
     <cge:Term_Ref ObjectID="43405"/>
    <cge:TPSR_Ref TObjectID="30496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-78966" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5846.000000 -1030.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78966" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17417"/>
     <cge:Term_Ref ObjectID="16016"/>
    <cge:TPSR_Ref TObjectID="17417"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-78967" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5846.000000 -1030.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78967" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17417"/>
     <cge:Term_Ref ObjectID="16016"/>
    <cge:TPSR_Ref TObjectID="17417"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-78968" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5846.000000 -1030.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78968" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17417"/>
     <cge:Term_Ref ObjectID="16016"/>
    <cge:TPSR_Ref TObjectID="17417"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-78985" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5846.000000 -1030.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78985" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17417"/>
     <cge:Term_Ref ObjectID="16016"/>
    <cge:TPSR_Ref TObjectID="17417"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-78969" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5846.000000 -1030.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78969" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17417"/>
     <cge:Term_Ref ObjectID="16016"/>
    <cge:TPSR_Ref TObjectID="17417"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-78978" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3622.000000 -693.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78978" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17419"/>
     <cge:Term_Ref ObjectID="16029"/>
    <cge:TPSR_Ref TObjectID="17419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-78979" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3622.000000 -693.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78979" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17419"/>
     <cge:Term_Ref ObjectID="16029"/>
    <cge:TPSR_Ref TObjectID="17419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-78980" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3622.000000 -693.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78980" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17419"/>
     <cge:Term_Ref ObjectID="16029"/>
    <cge:TPSR_Ref TObjectID="17419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-78984" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3622.000000 -693.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78984" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17419"/>
     <cge:Term_Ref ObjectID="16029"/>
    <cge:TPSR_Ref TObjectID="17419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-78981" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3622.000000 -693.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78981" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17419"/>
     <cge:Term_Ref ObjectID="16029"/>
    <cge:TPSR_Ref TObjectID="17419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-78982" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3622.000000 -693.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78982" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17419"/>
     <cge:Term_Ref ObjectID="16029"/>
    <cge:TPSR_Ref TObjectID="17419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-78983" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3622.000000 -693.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78983" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17419"/>
     <cge:Term_Ref ObjectID="16029"/>
    <cge:TPSR_Ref TObjectID="17419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-78972" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6048.000000 -668.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78972" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17418"/>
     <cge:Term_Ref ObjectID="16017"/>
    <cge:TPSR_Ref TObjectID="17418"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-78973" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6048.000000 -668.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78973" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17418"/>
     <cge:Term_Ref ObjectID="16017"/>
    <cge:TPSR_Ref TObjectID="17418"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-78974" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6048.000000 -668.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78974" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17418"/>
     <cge:Term_Ref ObjectID="16017"/>
    <cge:TPSR_Ref TObjectID="17418"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-78986" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6048.000000 -668.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78986" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17418"/>
     <cge:Term_Ref ObjectID="16017"/>
    <cge:TPSR_Ref TObjectID="17418"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-78975" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6048.000000 -668.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78975" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17418"/>
     <cge:Term_Ref ObjectID="16017"/>
    <cge:TPSR_Ref TObjectID="17418"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-78976" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6048.000000 -668.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78976" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17418"/>
     <cge:Term_Ref ObjectID="16017"/>
    <cge:TPSR_Ref TObjectID="17418"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-78977" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 6048.000000 -668.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78977" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17418"/>
     <cge:Term_Ref ObjectID="16017"/>
    <cge:TPSR_Ref TObjectID="17418"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-127706" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4249.000000 -1074.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127706" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17643"/>
     <cge:Term_Ref ObjectID="24260"/>
    <cge:TPSR_Ref TObjectID="17643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-127707" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4249.000000 -1074.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127707" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17643"/>
     <cge:Term_Ref ObjectID="24260"/>
    <cge:TPSR_Ref TObjectID="17643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-127701" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4249.000000 -1074.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127701" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17643"/>
     <cge:Term_Ref ObjectID="24260"/>
    <cge:TPSR_Ref TObjectID="17643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-128183" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4940.000000 -1232.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="128183" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23659"/>
     <cge:Term_Ref ObjectID="43395"/>
    <cge:TPSR_Ref TObjectID="23659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-78934" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5782.000000 -910.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78934" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16844"/>
     <cge:Term_Ref ObjectID="24282"/>
    <cge:TPSR_Ref TObjectID="16844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-78935" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5782.000000 -910.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78935" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16844"/>
     <cge:Term_Ref ObjectID="24282"/>
    <cge:TPSR_Ref TObjectID="16844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-78930" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5782.000000 -910.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78930" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16844"/>
     <cge:Term_Ref ObjectID="24282"/>
    <cge:TPSR_Ref TObjectID="16844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-78940" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5672.000000 -675.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78940" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16845"/>
     <cge:Term_Ref ObjectID="16030"/>
    <cge:TPSR_Ref TObjectID="16845"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-78941" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5672.000000 -675.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78941" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16845"/>
     <cge:Term_Ref ObjectID="16030"/>
    <cge:TPSR_Ref TObjectID="16845"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-78936" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5672.000000 -675.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78936" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16845"/>
     <cge:Term_Ref ObjectID="16030"/>
    <cge:TPSR_Ref TObjectID="16845"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-78952" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5327.000000 -674.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78952" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16858"/>
     <cge:Term_Ref ObjectID="16040"/>
    <cge:TPSR_Ref TObjectID="16858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-78953" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5327.000000 -674.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78953" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16858"/>
     <cge:Term_Ref ObjectID="16040"/>
    <cge:TPSR_Ref TObjectID="16858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-78948" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5327.000000 -674.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78948" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16858"/>
     <cge:Term_Ref ObjectID="16040"/>
    <cge:TPSR_Ref TObjectID="16858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-78964" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4883.000000 -674.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78964" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16869"/>
     <cge:Term_Ref ObjectID="23913"/>
    <cge:TPSR_Ref TObjectID="16869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-78965" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4883.000000 -674.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78965" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16869"/>
     <cge:Term_Ref ObjectID="23913"/>
    <cge:TPSR_Ref TObjectID="16869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-78960" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4883.000000 -674.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78960" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16869"/>
     <cge:Term_Ref ObjectID="23913"/>
    <cge:TPSR_Ref TObjectID="16869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-78958" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4971.000000 -912.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78958" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16868"/>
     <cge:Term_Ref ObjectID="23907"/>
    <cge:TPSR_Ref TObjectID="16868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-78959" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4971.000000 -912.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78959" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16868"/>
     <cge:Term_Ref ObjectID="23907"/>
    <cge:TPSR_Ref TObjectID="16868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-78954" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4971.000000 -912.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78954" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16868"/>
     <cge:Term_Ref ObjectID="23907"/>
    <cge:TPSR_Ref TObjectID="16868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-127729" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3632.000000 -8.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127729" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17654"/>
     <cge:Term_Ref ObjectID="24274"/>
    <cge:TPSR_Ref TObjectID="17654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-127730" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3632.000000 -8.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127730" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17654"/>
     <cge:Term_Ref ObjectID="24274"/>
    <cge:TPSR_Ref TObjectID="17654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-127721" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3632.000000 -8.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127721" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17654"/>
     <cge:Term_Ref ObjectID="24274"/>
    <cge:TPSR_Ref TObjectID="17654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-199908" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3997.000000 18.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="199908" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30474"/>
     <cge:Term_Ref ObjectID="43367"/>
    <cge:TPSR_Ref TObjectID="30474"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-199907" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3997.000000 18.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="199907" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30474"/>
     <cge:Term_Ref ObjectID="43367"/>
    <cge:TPSR_Ref TObjectID="30474"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-199916" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3997.000000 18.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="199916" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30474"/>
     <cge:Term_Ref ObjectID="43367"/>
    <cge:TPSR_Ref TObjectID="30474"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-199887" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4143.000000 18.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="199887" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30472"/>
     <cge:Term_Ref ObjectID="43363"/>
    <cge:TPSR_Ref TObjectID="30472"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-199886" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4143.000000 18.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="199886" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30472"/>
     <cge:Term_Ref ObjectID="43363"/>
    <cge:TPSR_Ref TObjectID="30472"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-199878" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4143.000000 18.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="199878" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30472"/>
     <cge:Term_Ref ObjectID="43363"/>
    <cge:TPSR_Ref TObjectID="30472"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-199904" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4284.000000 18.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="199904" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30473"/>
     <cge:Term_Ref ObjectID="43365"/>
    <cge:TPSR_Ref TObjectID="30473"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-199903" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4284.000000 18.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="199903" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30473"/>
     <cge:Term_Ref ObjectID="43365"/>
    <cge:TPSR_Ref TObjectID="30473"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-199897" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4284.000000 18.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="199897" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30473"/>
     <cge:Term_Ref ObjectID="43365"/>
    <cge:TPSR_Ref TObjectID="30473"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-79021" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4564.000000 -104.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79021" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16943"/>
     <cge:Term_Ref ObjectID="23979"/>
    <cge:TPSR_Ref TObjectID="16943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-79022" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4564.000000 -104.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79022" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16943"/>
     <cge:Term_Ref ObjectID="23979"/>
    <cge:TPSR_Ref TObjectID="16943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-79017" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4564.000000 -104.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79017" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16943"/>
     <cge:Term_Ref ObjectID="23979"/>
    <cge:TPSR_Ref TObjectID="16943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-79027" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4721.000000 -15.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79027" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17009"/>
     <cge:Term_Ref ObjectID="23983"/>
    <cge:TPSR_Ref TObjectID="17009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-79028" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4721.000000 -15.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79028" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17009"/>
     <cge:Term_Ref ObjectID="23983"/>
    <cge:TPSR_Ref TObjectID="17009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-79023" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4721.000000 -15.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79023" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17009"/>
     <cge:Term_Ref ObjectID="23983"/>
    <cge:TPSR_Ref TObjectID="17009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-79033" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4864.000000 -15.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79033" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17053"/>
     <cge:Term_Ref ObjectID="23991"/>
    <cge:TPSR_Ref TObjectID="17053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-79034" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4864.000000 -15.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79034" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17053"/>
     <cge:Term_Ref ObjectID="23991"/>
    <cge:TPSR_Ref TObjectID="17053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-79029" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4864.000000 -15.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79029" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17053"/>
     <cge:Term_Ref ObjectID="23991"/>
    <cge:TPSR_Ref TObjectID="17053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-78991" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5306.000000 -10.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78991" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16882"/>
     <cge:Term_Ref ObjectID="23927"/>
    <cge:TPSR_Ref TObjectID="16882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-78992" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5306.000000 -10.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78992" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16882"/>
     <cge:Term_Ref ObjectID="23927"/>
    <cge:TPSR_Ref TObjectID="16882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-78987" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5306.000000 -10.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78987" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16882"/>
     <cge:Term_Ref ObjectID="23927"/>
    <cge:TPSR_Ref TObjectID="16882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-78997" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5445.000000 -10.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78997" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16892"/>
     <cge:Term_Ref ObjectID="23935"/>
    <cge:TPSR_Ref TObjectID="16892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-78998" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5445.000000 -10.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78998" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16892"/>
     <cge:Term_Ref ObjectID="23935"/>
    <cge:TPSR_Ref TObjectID="16892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-78993" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5445.000000 -10.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78993" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16892"/>
     <cge:Term_Ref ObjectID="23935"/>
    <cge:TPSR_Ref TObjectID="16892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-79003" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5602.000000 -10.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79003" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16935"/>
     <cge:Term_Ref ObjectID="23945"/>
    <cge:TPSR_Ref TObjectID="16935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-79040" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5602.000000 -10.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79040" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16935"/>
     <cge:Term_Ref ObjectID="23945"/>
    <cge:TPSR_Ref TObjectID="16935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-78999" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5602.000000 -10.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78999" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16935"/>
     <cge:Term_Ref ObjectID="23945"/>
    <cge:TPSR_Ref TObjectID="16935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-79009" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5760.000000 -10.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79009" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16918"/>
     <cge:Term_Ref ObjectID="23953"/>
    <cge:TPSR_Ref TObjectID="16918"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-79010" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5760.000000 -10.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79010" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16918"/>
     <cge:Term_Ref ObjectID="23953"/>
    <cge:TPSR_Ref TObjectID="16918"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-79005" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5760.000000 -10.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79005" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16918"/>
     <cge:Term_Ref ObjectID="23953"/>
    <cge:TPSR_Ref TObjectID="16918"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-79015" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5120.000000 -411.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79015" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16987"/>
     <cge:Term_Ref ObjectID="23961"/>
    <cge:TPSR_Ref TObjectID="16987"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-79016" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5120.000000 -411.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79016" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16987"/>
     <cge:Term_Ref ObjectID="23961"/>
    <cge:TPSR_Ref TObjectID="16987"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-79011" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5120.000000 -411.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79011" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16987"/>
     <cge:Term_Ref ObjectID="23961"/>
    <cge:TPSR_Ref TObjectID="16987"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-79039" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3815.000000 34.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79039" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17076"/>
     <cge:Term_Ref ObjectID="24254"/>
    <cge:TPSR_Ref TObjectID="17076"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-79035" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3815.000000 34.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79035" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17076"/>
     <cge:Term_Ref ObjectID="24254"/>
    <cge:TPSR_Ref TObjectID="17076"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3278" y="-1297"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3278" y="-1297"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3217" y="-1313"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3217" y="-1313"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="4055" y="-1072"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="4055" y="-1072"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="22" qtmmishow="hidden" width="36" x="4950" y="-1201"/>
    </a>
   <metadata/><rect fill="white" height="22" opacity="0" stroke="white" transform="" width="36" x="4950" y="-1201"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="5557" y="-1079"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="5557" y="-1079"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="99" x="4850" y="-829"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="99" x="4850" y="-829"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="19" qtmmishow="hidden" width="104" x="5266" y="-821"/>
    </a>
   <metadata/><rect fill="white" height="19" opacity="0" stroke="white" transform="" width="104" x="5266" y="-821"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="99" x="5624" y="-827"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="99" x="5624" y="-827"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3638" y="-429"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3638" y="-429"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3835" y="-478"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3835" y="-478"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="35" x="4020" y="-482"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="35" x="4020" y="-482"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="19" qtmmishow="hidden" width="32" x="4167" y="-481"/>
    </a>
   <metadata/><rect fill="white" height="19" opacity="0" stroke="white" transform="" width="32" x="4167" y="-481"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="19" qtmmishow="hidden" width="32" x="4319" y="-478"/>
    </a>
   <metadata/><rect fill="white" height="19" opacity="0" stroke="white" transform="" width="32" x="4319" y="-478"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="4601" y="-483"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="4601" y="-483"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="19" qtmmishow="hidden" width="31" x="4746" y="-488"/>
    </a>
   <metadata/><rect fill="white" height="19" opacity="0" stroke="white" transform="" width="31" x="4746" y="-488"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="31" x="4872" y="-481"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="31" x="4872" y="-481"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="5103" y="-504"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="5103" y="-504"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="31" x="5333" y="-485"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="31" x="5333" y="-485"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="31" x="5460" y="-480"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="31" x="5460" y="-480"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="31" x="5611" y="-482"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="31" x="5611" y="-482"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="32" x="5761" y="-483"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="32" x="5761" y="-483"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="19" qtmmishow="hidden" width="76" x="3177" y="-822"/>
    </a>
   <metadata/><rect fill="white" height="19" opacity="0" stroke="white" transform="" width="76" x="3177" y="-822"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3454" y="-1257"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3454" y="-1257"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3454" y="-1292"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3454" y="-1292"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="3472" y="-1186"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="3472" y="-1186"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3278" y="-1297"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3217" y="-1313"/></g>
   <g href="35kV碧城变35kV碧城T接线362间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="4055" y="-1072"/></g>
   <g href="35kV碧城变分段312间隔接线图_0.svg" style="fill-opacity:0"><rect height="22" qtmmishow="hidden" width="36" x="4950" y="-1201"/></g>
   <g href="35kV碧城变35kV碧城线361间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="5557" y="-1079"/></g>
   <g href="35kV碧城变3号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="99" x="4850" y="-829"/></g>
   <g href="35kV碧城变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="19" qtmmishow="hidden" width="104" x="5266" y="-821"/></g>
   <g href="35kV碧城变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="99" x="5624" y="-827"/></g>
   <g href="35kV碧城变科联钛选厂线032间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3638" y="-429"/></g>
   <g href="35kV碧城变1号电容器031间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3835" y="-478"/></g>
   <g href="35kV碧城变碧城工业园区_III_回线038间隔接线图_0.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="35" x="4020" y="-482"/></g>
   <g href="35kV碧城变碧城工业园区_I_I_回线036间隔接线图_0.svg" style="fill-opacity:0"><rect height="19" qtmmishow="hidden" width="32" x="4167" y="-481"/></g>
   <g href="35kV碧城变碧城工业园区_I_回线037间隔接线图_0.svg" style="fill-opacity:0"><rect height="19" qtmmishow="hidden" width="32" x="4319" y="-478"/></g>
   <g href="35kV碧城变旁路线035间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="4601" y="-483"/></g>
   <g href="35kV碧城变碧城II回线033间隔接线图_0.svg" style="fill-opacity:0"><rect height="19" qtmmishow="hidden" width="31" x="4746" y="-488"/></g>
   <g href="35kV碧城变10kV硅铁线034间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="31" x="4872" y="-481"/></g>
   <g href="35kV碧城变10kV分段012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="5103" y="-504"/></g>
   <g href="35kV碧城变10kV温泉线041间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="31" x="5333" y="-485"/></g>
   <g href="35kV碧城变10kV碧城I回线042间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="31" x="5460" y="-480"/></g>
   <g href="35kV碧城变10kV董家湾线043间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="31" x="5611" y="-482"/></g>
   <g href="35kV碧城变10kV城区线044间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="32" x="5761" y="-483"/></g>
   <g href="35kV碧城变公用间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="19" qtmmishow="hidden" width="76" x="3177" y="-822"/></g>
   <g href="cx_配调_配网接线图35_禄丰.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3454" y="-1257"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3454" y="-1292"/></g>
   <g href="AVC碧城站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="3472" y="-1186"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(60,120,255)" stroke-width="0.416667" width="14" x="4482" y="-1101"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(60,120,255)" stroke-width="0.416667" width="14" x="4448" y="-1103"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(255,255,0)" stroke-width="0.416667" width="14" x="5361" y="-1101"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(60,120,255)" stroke-width="0.416667" width="14" x="5327" y="-1103"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="3473" y="-1185"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-LF_BC.LF_BC_10IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5098,-575 5918,-575 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17418" ObjectName="BS-LF_BC.LF_BC_10IM"/>
    <cge:TPSR_Ref TObjectID="17418"/></metadata>
   <polyline fill="none" opacity="0" points="5098,-575 5918,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5669,-978 5672,-978 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="5669,-978 5672,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_BC.LF_BC_10M">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3528,-164 5929,-164 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17426" ObjectName="BS-LF_BC.LF_BC_10M"/>
    <cge:TPSR_Ref TObjectID="17426"/></metadata>
   <polyline fill="none" opacity="0" points="3528,-164 5929,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_BC.LF_BC_35IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-977 5728,-977 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17417" ObjectName="BS-LF_BC.LF_BC_35IM"/>
    <cge:TPSR_Ref TObjectID="17417"/></metadata>
   <polyline fill="none" opacity="0" points="5060,-977 5728,-977 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_BC.LF_BC_10IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3555,-574 4378,-574 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17419" ObjectName="BS-LF_BC.LF_BC_10IIM"/>
    <cge:TPSR_Ref TObjectID="17419"/></metadata>
   <polyline fill="none" opacity="0" points="3555,-574 4378,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_BC.LF_BC_10IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4472,-574 5042,-574 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17419" ObjectName="BS-LF_BC.LF_BC_10IIM"/>
    <cge:TPSR_Ref TObjectID="17419"/></metadata>
   <polyline fill="none" opacity="0" points="4472,-574 5042,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_BC.LF_BC_35IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3717,-979 4900,-979 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="30496" ObjectName="BS-LF_BC.LF_BC_35IIM"/>
    <cge:TPSR_Ref TObjectID="30496"/></metadata>
   <polyline fill="none" opacity="0" points="3717,-979 4900,-979 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5213.000000 -1120.000000)" xlink:href="#transformer2:shape41_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5213.000000 -1120.000000)" xlink:href="#transformer2:shape41_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5849.000000 -282.000000)" xlink:href="#transformer2:shape58_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5849.000000 -282.000000)" xlink:href="#transformer2:shape58_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-LF_BC.LF_BC_3T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="24003"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4741.000000 -737.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4741.000000 -737.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="17136" ObjectName="TF-LF_BC.LF_BC_3T"/>
    <cge:TPSR_Ref TObjectID="17136"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-LF_BC.LF_BC_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="23999"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5194.000000 -747.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5194.000000 -747.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="17131" ObjectName="TF-LF_BC.LF_BC_2T"/>
    <cge:TPSR_Ref TObjectID="17131"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-LF_BC.LF_BC_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="23995"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5540.000000 -734.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5540.000000 -734.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="17130" ObjectName="TF-LF_BC.LF_BC_1T"/>
    <cge:TPSR_Ref TObjectID="17130"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3266.000000 -1233.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-80944" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="27" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3292.000000 -946.000000) translate(0,22)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80944" ObjectName="LF_BC:LF_BC_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-80830" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="27" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3291.000000 -986.000000) translate(0,22)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80830" ObjectName="LF_BC:LF_BC_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-226249" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="27" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3291.000000 -1064.000000) translate(0,22)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226249" ObjectName="LF_BC:LF_BC_sumP2"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-226249" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="27" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3291.000000 -1026.000000) translate(0,22)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226249" ObjectName="LF_BC:LF_BC_sumP2"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-52550" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3423.000000 -1167.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9260" ObjectName="DYN-LF_BC"/>
     <cge:Meas_Ref ObjectId="52550"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_195f170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4187.000000 1074.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1960410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4176.000000 1059.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19612d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4201.000000 1044.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1961c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5274.000000 747.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1962870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5274.000000 762.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1963190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3712.000000 1102.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1963a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3712.000000 1088.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1963ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3712.000000 1074.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1964540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3718.000000 1059.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19647c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3704.000000 1044.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1965b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5778.000000 1033.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1965d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5778.000000 1019.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1965f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5778.000000 1005.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19661d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5784.000000 990.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1966410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5770.000000 975.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1967aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5631.000000 1045.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1967d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5620.000000 1030.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1967f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5645.000000 1015.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19688b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5721.000000 910.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1968b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5710.000000 895.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1968db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5735.000000 880.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19697a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5355.000000 894.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1969a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5344.000000 879.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1969c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5369.000000 864.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1969f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5613.000000 746.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196a1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5613.000000 761.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196a520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4861.000000 766.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196a780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4861.000000 781.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196aba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5609.000000 677.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196ae60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5598.000000 662.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196b0a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5623.000000 647.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196c010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5270.000000 676.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196c2d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5259.000000 661.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196c510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5284.000000 646.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196c930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4822.000000 676.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196cbf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4811.000000 661.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196ce30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4836.000000 646.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196d7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4906.000000 911.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196dab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4895.000000 896.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196dcf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4920.000000 881.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196e740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3572.000000 8.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196e9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3561.000000 -7.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196ec10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3586.000000 -22.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1973380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5062.000000 410.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1973610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5051.000000 395.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1973850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5076.000000 380.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1974190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3760.000000 -51.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19743c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3748.000000 -32.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19747e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3940.000000 -18.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1974aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3929.000000 -33.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1974ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3954.000000 -48.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1975100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4506.000000 103.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19753c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4495.000000 88.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1975600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4520.000000 73.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1975a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4661.000000 15.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1975ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4650.000000 0.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1975f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4675.000000 -15.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19774f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4086.000000 -18.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1977af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4075.000000 -33.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1977d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4100.000000 -48.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1978150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4228.000000 -18.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1978410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4217.000000 -33.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1978650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4242.000000 -48.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1978a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4807.000000 15.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1978d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4796.000000 0.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1978f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4821.000000 -15.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1979390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5250.000000 10.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1979650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5239.000000 -5.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1979890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5264.000000 -20.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1979cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5388.000000 10.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1979f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5377.000000 -5.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_197a1b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5402.000000 -20.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_197a5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5544.000000 10.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_197a890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5533.000000 -5.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_197aad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5558.000000 -20.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_197aef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5703.000000 10.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_197b1b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5692.000000 -5.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_197b3f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5717.000000 -20.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-LF_BC.LF_BC_031">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3782.000000 -14.000000)" xlink:href="#capacitor:shape57"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17128" ObjectName="CB-LF_BC.LF_BC_031"/>
    <cge:TPSR_Ref TObjectID="17128"/></metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="17418" cx="5231" cy="-575" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17418" cx="5746" cy="-575" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17418" cx="5594" cy="-575" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17418" cx="5451" cy="-575" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17418" cx="5313" cy="-575" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17418" cx="5145" cy="-575" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17418" cx="5809" cy="-575" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17418" cx="5864" cy="-575" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17417" cx="5232" cy="-977" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17417" cx="5596" cy="-977" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17419" cx="5027" cy="-574" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17419" cx="4857" cy="-574" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17419" cx="4727" cy="-574" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17419" cx="4582" cy="-574" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17419" cx="4502" cy="-574" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17419" cx="4001" cy="-574" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17419" cx="4153" cy="-574" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17419" cx="4301" cy="-574" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17419" cx="4779" cy="-574" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30496" cx="4093" cy="-979" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17419" cx="4368" cy="-574" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17419" cx="4485" cy="-574" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17417" cx="5105" cy="-977" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30496" cx="4860" cy="-979" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30496" cx="4490" cy="-979" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17417" cx="5368" cy="-977" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17419" cx="3826" cy="-574" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17419" cx="3629" cy="-574" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17426" cx="5773" cy="-164" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17426" cx="5344" cy="-164" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17426" cx="5479" cy="-164" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17426" cx="5622" cy="-164" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17426" cx="4758" cy="-164" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17426" cx="4888" cy="-164" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17426" cx="4582" cy="-164" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17426" cx="4184" cy="-164" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17426" cx="3660" cy="-164" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30496" cx="4778" cy="-979" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17417" cx="5577" cy="-977" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-128068">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4084.095850 -994.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17644" ObjectName="SW-LF_BC.LF_BC_3621SW"/>
     <cge:Meas_Ref ObjectId="128068"/>
    <cge:TPSR_Ref TObjectID="17644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128069">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4084.095850 -1088.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17645" ObjectName="SW-LF_BC.LF_BC_3622SW"/>
     <cge:Meas_Ref ObjectId="128069"/>
    <cge:TPSR_Ref TObjectID="17645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128070">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4128.095850 -1086.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17646" ObjectName="SW-LF_BC.LF_BC_36227SW"/>
     <cge:Meas_Ref ObjectId="128070"/>
    <cge:TPSR_Ref TObjectID="17646"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4171.095850 -1197.375000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128942">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5586.883374 -994.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17649" ObjectName="SW-LF_BC.LF_BC_3611SW"/>
     <cge:Meas_Ref ObjectId="128942"/>
    <cge:TPSR_Ref TObjectID="17649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128072">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5586.883374 -1086.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17648" ObjectName="SW-LF_BC.LF_BC_3612SW"/>
     <cge:Meas_Ref ObjectId="128072"/>
    <cge:TPSR_Ref TObjectID="17648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128073">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5627.883374 -1085.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17650" ObjectName="SW-LF_BC.LF_BC_36127SW"/>
     <cge:Meas_Ref ObjectId="128073"/>
    <cge:TPSR_Ref TObjectID="17650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5658.318477 -1199.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-79077">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5569.095986 -927.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16857" ObjectName="SW-LF_BC.LF_BC_3011SW"/>
     <cge:Meas_Ref ObjectId="79077"/>
    <cge:TPSR_Ref TObjectID="16857"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-79080">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5616.095986 -872.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23627" ObjectName="SW-LF_BC.LF_BC_3017SW"/>
     <cge:Meas_Ref ObjectId="79080"/>
    <cge:TPSR_Ref TObjectID="23627"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199688">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4769.291895 -927.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16879" ObjectName="SW-LF_BC.LF_BC_3032SW"/>
     <cge:Meas_Ref ObjectId="199688"/>
    <cge:TPSR_Ref TObjectID="16879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127959">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4817.291895 -877.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17653" ObjectName="SW-LF_BC.LF_BC_30327SW"/>
     <cge:Meas_Ref ObjectId="127959"/>
    <cge:TPSR_Ref TObjectID="17653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127846">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5223.578871 -920.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16866" ObjectName="SW-LF_BC.LF_BC_3021SW"/>
     <cge:Meas_Ref ObjectId="127846"/>
    <cge:TPSR_Ref TObjectID="16866"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127849">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5270.578871 -860.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17652" ObjectName="SW-LF_BC.LF_BC_30217SW"/>
     <cge:Meas_Ref ObjectId="127849"/>
    <cge:TPSR_Ref TObjectID="17652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5224.000000 -1032.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77762">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5541.259587 -673.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16852" ObjectName="SW-LF_BC.LF_BC_0016SW"/>
     <cge:Meas_Ref ObjectId="77762"/>
    <cge:TPSR_Ref TObjectID="16852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77761">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5541.259587 -579.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16851" ObjectName="SW-LF_BC.LF_BC_0011SW"/>
     <cge:Meas_Ref ObjectId="77761"/>
    <cge:TPSR_Ref TObjectID="16851"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77825">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5195.095870 -695.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16862" ObjectName="SW-LF_BC.LF_BC_0022SW"/>
     <cge:Meas_Ref ObjectId="77825"/>
    <cge:TPSR_Ref TObjectID="16862"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77824">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5195.095870 -586.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16861" ObjectName="SW-LF_BC.LF_BC_0021SW"/>
     <cge:Meas_Ref ObjectId="77824"/>
    <cge:TPSR_Ref TObjectID="16861"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128318">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4743.307153 -683.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28994" ObjectName="SW-LF_BC.LF_BC_0036SW"/>
     <cge:Meas_Ref ObjectId="128318"/>
    <cge:TPSR_Ref TObjectID="28994"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128317">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4743.307153 -585.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16875" ObjectName="SW-LF_BC.LF_BC_0032SW"/>
     <cge:Meas_Ref ObjectId="128317"/>
    <cge:TPSR_Ref TObjectID="16875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191864">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5873.000000 -659.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29016" ObjectName="SW-LF_BC.LF_BC_09017SW"/>
     <cge:Meas_Ref ObjectId="191864"/>
    <cge:TPSR_Ref TObjectID="29016"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128509">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5710.000000 -508.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17658" ObjectName="SW-LF_BC.LF_BC_0441SW"/>
     <cge:Meas_Ref ObjectId="128509"/>
    <cge:TPSR_Ref TObjectID="17658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128063">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5710.000000 -395.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17661" ObjectName="SW-LF_BC.LF_BC_0443SW"/>
     <cge:Meas_Ref ObjectId="128063"/>
    <cge:TPSR_Ref TObjectID="17661"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128064">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5770.000000 -313.000000)" xlink:href="#switch2:shape9_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29004" ObjectName="SW-LF_BC.LF_BC_04437SW"/>
     <cge:Meas_Ref ObjectId="128064"/>
    <cge:TPSR_Ref TObjectID="29004"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78001">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5737.000000 -235.000000)" xlink:href="#switch2:shape27_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17662" ObjectName="SW-LF_BC.LF_BC_0446SW"/>
     <cge:Meas_Ref ObjectId="78001"/>
    <cge:TPSR_Ref TObjectID="17662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128513">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5764.000000 -190.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29003" ObjectName="SW-LF_BC.LF_BC_0445SW"/>
     <cge:Meas_Ref ObjectId="128513"/>
    <cge:TPSR_Ref TObjectID="29003"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128449">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5558.000000 -508.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16934" ObjectName="SW-LF_BC.LF_BC_0431SW"/>
     <cge:Meas_Ref ObjectId="128449"/>
    <cge:TPSR_Ref TObjectID="16934"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128061">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5558.000000 -395.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17659" ObjectName="SW-LF_BC.LF_BC_0433SW"/>
     <cge:Meas_Ref ObjectId="128061"/>
    <cge:TPSR_Ref TObjectID="17659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128062">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5618.000000 -313.000000)" xlink:href="#switch2:shape9_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29002" ObjectName="SW-LF_BC.LF_BC_04337SW"/>
     <cge:Meas_Ref ObjectId="128062"/>
    <cge:TPSR_Ref TObjectID="29002"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78018">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5585.000000 -235.000000)" xlink:href="#switch2:shape27_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17660" ObjectName="SW-LF_BC.LF_BC_0436SW"/>
     <cge:Meas_Ref ObjectId="78018"/>
    <cge:TPSR_Ref TObjectID="17660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128453">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5613.000000 -191.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29001" ObjectName="SW-LF_BC.LF_BC_0435SW"/>
     <cge:Meas_Ref ObjectId="128453"/>
    <cge:TPSR_Ref TObjectID="29001"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128387">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5415.000000 -506.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16909" ObjectName="SW-LF_BC.LF_BC_0421SW"/>
     <cge:Meas_Ref ObjectId="128387"/>
    <cge:TPSR_Ref TObjectID="16909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77996">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5415.000000 -393.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16913" ObjectName="SW-LF_BC.LF_BC_0423SW"/>
     <cge:Meas_Ref ObjectId="77996"/>
    <cge:TPSR_Ref TObjectID="16913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77997">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5475.000000 -311.000000)" xlink:href="#switch2:shape9_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29000" ObjectName="SW-LF_BC.LF_BC_04237SW"/>
     <cge:Meas_Ref ObjectId="77997"/>
    <cge:TPSR_Ref TObjectID="29000"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77975">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5442.000000 -233.000000)" xlink:href="#switch2:shape27_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16914" ObjectName="SW-LF_BC.LF_BC_0426SW"/>
     <cge:Meas_Ref ObjectId="77975"/>
    <cge:TPSR_Ref TObjectID="16914"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128393">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5470.000000 -189.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28999" ObjectName="SW-LF_BC.LF_BC_0425SW"/>
     <cge:Meas_Ref ObjectId="128393"/>
    <cge:TPSR_Ref TObjectID="28999"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128327">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5277.000000 -508.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16881" ObjectName="SW-LF_BC.LF_BC_0411SW"/>
     <cge:Meas_Ref ObjectId="128327"/>
    <cge:TPSR_Ref TObjectID="16881"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77968">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5277.000000 -395.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16885" ObjectName="SW-LF_BC.LF_BC_0413SW"/>
     <cge:Meas_Ref ObjectId="77968"/>
    <cge:TPSR_Ref TObjectID="16885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77969">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5339.000000 -315.000000)" xlink:href="#switch2:shape9_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28997" ObjectName="SW-LF_BC.LF_BC_04137SW"/>
     <cge:Meas_Ref ObjectId="77969"/>
    <cge:TPSR_Ref TObjectID="28997"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-77965">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5304.000000 -237.000000)" xlink:href="#switch2:shape27_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28996" ObjectName="SW-LF_BC.LF_BC_0416SW"/>
     <cge:Meas_Ref ObjectId="77965"/>
    <cge:TPSR_Ref TObjectID="28996"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128331">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5335.000000 -194.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16886" ObjectName="SW-LF_BC.LF_BC_0415SW"/>
     <cge:Meas_Ref ObjectId="128331"/>
    <cge:TPSR_Ref TObjectID="16886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191867">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5828.000000 -475.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29019" ObjectName="SW-LF_BC.LF_BC_0451SW"/>
     <cge:Meas_Ref ObjectId="191867"/>
    <cge:TPSR_Ref TObjectID="29019"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128728">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4821.000000 -506.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17424" ObjectName="SW-LF_BC.LF_BC_0342SW"/>
     <cge:Meas_Ref ObjectId="128728"/>
    <cge:TPSR_Ref TObjectID="17424"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-79378">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4821.000000 -393.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17423" ObjectName="SW-LF_BC.LF_BC_0343SW"/>
     <cge:Meas_Ref ObjectId="79378"/>
    <cge:TPSR_Ref TObjectID="17423"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-79379">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4883.000000 -313.000000)" xlink:href="#switch2:shape9_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29009" ObjectName="SW-LF_BC.LF_BC_03437SW"/>
     <cge:Meas_Ref ObjectId="79379"/>
    <cge:TPSR_Ref TObjectID="29009"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78136">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4848.000000 -235.000000)" xlink:href="#switch2:shape27_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17052" ObjectName="SW-LF_BC.LF_BC_0346SW"/>
     <cge:Meas_Ref ObjectId="78136"/>
    <cge:TPSR_Ref TObjectID="17052"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128732">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4879.000000 -192.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29010" ObjectName="SW-LF_BC.LF_BC_0345SW"/>
     <cge:Meas_Ref ObjectId="128732"/>
    <cge:TPSR_Ref TObjectID="29010"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128066">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4691.000000 -514.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23634" ObjectName="SW-LF_BC.LF_BC_0332SW"/>
     <cge:Meas_Ref ObjectId="128066"/>
    <cge:TPSR_Ref TObjectID="23634"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-79354">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4691.000000 -401.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17421" ObjectName="SW-LF_BC.LF_BC_0333SW"/>
     <cge:Meas_Ref ObjectId="79354"/>
    <cge:TPSR_Ref TObjectID="17421"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-79355">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4753.000000 -321.000000)" xlink:href="#switch2:shape9_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29005" ObjectName="SW-LF_BC.LF_BC_03337SW"/>
     <cge:Meas_Ref ObjectId="79355"/>
    <cge:TPSR_Ref TObjectID="29005"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78092">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4718.000000 -243.000000)" xlink:href="#switch2:shape27_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17422" ObjectName="SW-LF_BC.LF_BC_0336SW"/>
     <cge:Meas_Ref ObjectId="78092"/>
    <cge:TPSR_Ref TObjectID="17422"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4749.000000 -200.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29006" ObjectName="SW-LF_BC.LF_BC_0335SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="29006"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128616">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4546.000000 -509.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29020" ObjectName="SW-LF_BC.LF_BC_0352SW"/>
     <cge:Meas_Ref ObjectId="128616"/>
    <cge:TPSR_Ref TObjectID="29020"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128065">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4546.000000 -397.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23633" ObjectName="SW-LF_BC.LF_BC_0352SW"/>
     <cge:Meas_Ref ObjectId="128065"/>
    <cge:TPSR_Ref TObjectID="23633"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128617">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4608.000000 -316.000000)" xlink:href="#switch2:shape9_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29007" ObjectName="SW-LF_BC.LF_BC_03537SW"/>
     <cge:Meas_Ref ObjectId="128617"/>
    <cge:TPSR_Ref TObjectID="29007"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78026">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4573.000000 -238.000000)" xlink:href="#switch2:shape27_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29008" ObjectName="SW-LF_BC.LF_BC_0355SW"/>
     <cge:Meas_Ref ObjectId="78026"/>
    <cge:TPSR_Ref TObjectID="29008"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128159">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3655.000000 -309.000000)" xlink:href="#switch2:shape9_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23635" ObjectName="SW-LF_BC.LF_BC_03227SW"/>
     <cge:Meas_Ref ObjectId="128159"/>
    <cge:TPSR_Ref TObjectID="23635"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128161">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3620.000000 -231.179487)" xlink:href="#switch2:shape27_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17656" ObjectName="SW-LF_BC.LF_BC_0326SW"/>
     <cge:Meas_Ref ObjectId="128161"/>
    <cge:TPSR_Ref TObjectID="17656"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128160">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3651.000000 -188.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17655" ObjectName="SW-LF_BC.LF_BC_0325SW"/>
     <cge:Meas_Ref ObjectId="128160"/>
    <cge:TPSR_Ref TObjectID="17655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128788">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3790.000000 -501.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="17075" ObjectName="SW-LF_BC.LF_BC_0312SW"/>
     <cge:Meas_Ref ObjectId="128788"/>
    <cge:TPSR_Ref TObjectID="17075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128789">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3790.000000 -383.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29012" ObjectName="SW-LF_BC.LF_BC_0316SW"/>
     <cge:Meas_Ref ObjectId="128789"/>
    <cge:TPSR_Ref TObjectID="29012"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128790">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3848.000000 -292.000000)" xlink:href="#switch2:shape9_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29011" ObjectName="SW-LF_BC.LF_BC_03167SW"/>
     <cge:Meas_Ref ObjectId="128790"/>
    <cge:TPSR_Ref TObjectID="29011"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191866">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4566.000000 -659.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29018" ObjectName="SW-LF_BC.LF_BC_090127SW"/>
     <cge:Meas_Ref ObjectId="191866"/>
    <cge:TPSR_Ref TObjectID="29018"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128611">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4989.000000 -533.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16990" ObjectName="SW-LF_BC.LF_BC_0122SW"/>
     <cge:Meas_Ref ObjectId="128611"/>
    <cge:TPSR_Ref TObjectID="16990"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128569">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5109.000000 -519.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16986" ObjectName="SW-LF_BC.LF_BC_0121SW"/>
     <cge:Meas_Ref ObjectId="128569"/>
    <cge:TPSR_Ref TObjectID="16986"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191863">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5795.000000 -600.000000)" xlink:href="#switch2:shape31_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29015" ObjectName="SW-LF_BC.LF_BC_0901SW"/>
     <cge:Meas_Ref ObjectId="191863"/>
    <cge:TPSR_Ref TObjectID="29015"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191865">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4488.000000 -600.000000)" xlink:href="#switch2:shape31_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29017" ObjectName="SW-LF_BC.LF_BC_0902SW"/>
     <cge:Meas_Ref ObjectId="191865"/>
    <cge:TPSR_Ref TObjectID="29017"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199837">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3965.000000 -507.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30483" ObjectName="SW-LF_BC.LF_BC_0382SW"/>
     <cge:Meas_Ref ObjectId="199837"/>
    <cge:TPSR_Ref TObjectID="30483"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199839">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3965.000000 -394.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30484" ObjectName="SW-LF_BC.LF_BC_0383SW"/>
     <cge:Meas_Ref ObjectId="199839"/>
    <cge:TPSR_Ref TObjectID="30484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199841">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4027.000000 -314.000000)" xlink:href="#switch2:shape9_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30485" ObjectName="SW-LF_BC.LF_BC_03837SW"/>
     <cge:Meas_Ref ObjectId="199841"/>
    <cge:TPSR_Ref TObjectID="30485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199741">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4117.000000 -505.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30475" ObjectName="SW-LF_BC.LF_BC_0362SW"/>
     <cge:Meas_Ref ObjectId="199741"/>
    <cge:TPSR_Ref TObjectID="30475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199743">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4117.000000 -392.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30479" ObjectName="SW-LF_BC.LF_BC_0363SW"/>
     <cge:Meas_Ref ObjectId="199743"/>
    <cge:TPSR_Ref TObjectID="30479"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199745">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4179.000000 -312.000000)" xlink:href="#switch2:shape9_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30477" ObjectName="SW-LF_BC.LF_BC_03637SW"/>
     <cge:Meas_Ref ObjectId="199745"/>
    <cge:TPSR_Ref TObjectID="30477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199747">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4144.000000 -234.000000)" xlink:href="#switch2:shape27_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30478" ObjectName="SW-LF_BC.LF_BC_0366SW"/>
     <cge:Meas_Ref ObjectId="199747"/>
    <cge:TPSR_Ref TObjectID="30478"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199748">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4175.000000 -191.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30476" ObjectName="SW-LF_BC.LF_BC_0365SW"/>
     <cge:Meas_Ref ObjectId="199748"/>
    <cge:TPSR_Ref TObjectID="30476"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199790">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4265.000000 -504.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30480" ObjectName="SW-LF_BC.LF_BC_0372SW"/>
     <cge:Meas_Ref ObjectId="199790"/>
    <cge:TPSR_Ref TObjectID="30480"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199792">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4265.000000 -391.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30482" ObjectName="SW-LF_BC.LF_BC_0373SW"/>
     <cge:Meas_Ref ObjectId="199792"/>
    <cge:TPSR_Ref TObjectID="30482"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199794">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4327.000000 -311.000000)" xlink:href="#switch2:shape9_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30481" ObjectName="SW-LF_BC.LF_BC_03737SW"/>
     <cge:Meas_Ref ObjectId="199794"/>
    <cge:TPSR_Ref TObjectID="30481"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199717">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5096.000000 -1003.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30492" ObjectName="SW-LF_BC.LF_BC_3121SW"/>
     <cge:Meas_Ref ObjectId="199717"/>
    <cge:TPSR_Ref TObjectID="30492"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199718">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4851.000000 -1007.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30494" ObjectName="SW-LF_BC.LF_BC_3122SW"/>
     <cge:Meas_Ref ObjectId="199718"/>
    <cge:TPSR_Ref TObjectID="30494"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199720">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4801.000000 -1030.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30495" ObjectName="SW-LF_BC.LF_BC_31227SW"/>
     <cge:Meas_Ref ObjectId="199720"/>
    <cge:TPSR_Ref TObjectID="30495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199719">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5147.000000 -1039.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30493" ObjectName="SW-LF_BC.LF_BC_31217SW"/>
     <cge:Meas_Ref ObjectId="199719"/>
    <cge:TPSR_Ref TObjectID="30493"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199700">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4498.912670 -1039.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30486" ObjectName="SW-LF_BC.LF_BC_3902SW"/>
     <cge:Meas_Ref ObjectId="199700"/>
    <cge:TPSR_Ref TObjectID="30486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-199701">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4512.722412 -1118.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30487" ObjectName="SW-LF_BC.LF_BC_39027SW"/>
     <cge:Meas_Ref ObjectId="199701"/>
    <cge:TPSR_Ref TObjectID="30487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128056">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 5376.912670 -1039.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28995" ObjectName="SW-LF_BC.LF_BC_3901SW"/>
     <cge:Meas_Ref ObjectId="128056"/>
    <cge:TPSR_Ref TObjectID="28995"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-128057">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5391.722412 -1118.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23629" ObjectName="SW-LF_BC.LF_BC_39017SW"/>
     <cge:Meas_Ref ObjectId="128057"/>
    <cge:TPSR_Ref TObjectID="23629"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14b9ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3171.000000 -1061.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14b9ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3171.000000 -1061.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14b9ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3171.000000 -1061.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14b9ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3171.000000 -1061.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14b9ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3171.000000 -1061.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14b9ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3171.000000 -1061.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14b9ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3171.000000 -1061.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14b9ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3171.000000 -1061.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_14b9ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3171.000000 -1061.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1214620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -707.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1214620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -707.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1214620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -707.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1214620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -707.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1214620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -707.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1214620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -707.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1214620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -707.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1214620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -707.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1214620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -707.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1214620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -707.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1214620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -707.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1214620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -707.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1214620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -707.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1214620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -707.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1214620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -707.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1214620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -707.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1214620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -707.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1214620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3165.000000 -707.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1459070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3303.000000 -1286.500000) translate(0,16)">碧城变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1169170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4043.000000 -1302.000000) translate(0,18)">35kV碧城T接线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_147b760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4133.000000 -1188.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_147b970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5542.000000 -1293.000000) translate(0,18)">35kV碧城线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_143f3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5645.000000 -953.000000) translate(0,18)">35kV I 段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_147bbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5941.000000 -558.000000) translate(0,18)">10kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_143f800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3780.000000 -1.000000) translate(0,18)">1号电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1395170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3452.000000 -564.000000) translate(0,18)">10kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1395660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4154.000000 -1119.000000) translate(0,15)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1337e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4046.000000 -1122.000000) translate(0,15)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1395810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4047.000000 -1026.000000) translate(0,15)">3622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1395a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5646.000000 -1116.000000) translate(0,15)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1395be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5538.000000 -1119.000000) translate(0,15)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1395d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5542.000000 -1026.000000) translate(0,15)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1474980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5034.000000 -553.000000) translate(0,15)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1474d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5150.000000 -554.000000) translate(0,15)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1474f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4737.291895 -667.000000) translate(0,15)">003</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1475150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4703.984742 -712.000000) translate(0,15)">0036</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1475330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4705.984742 -619.000000) translate(0,15)">0032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1475510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4739.000000 -911.000000) translate(0,15)">303</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14756f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4725.291895 -954.000000) translate(0,15)">3032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14758d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5188.578871 -894.000000) translate(0,15)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1475ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5181.578871 -953.000000) translate(0,15)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1475c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5186.578871 -669.000000) translate(0,15)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1475e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5153.483001 -728.000000) translate(0,15)">0026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1476050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5152.483001 -618.000000) translate(0,15)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1476230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5536.095986 -912.000000) translate(0,15)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1476410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5530.095986 -959.000000) translate(0,15)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14765f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5532.095986 -659.000000) translate(0,15)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14767d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5634.000000 -901.000000) translate(0,15)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1340860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4837.000000 -909.000000) translate(0,15)">30327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1340a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5287.000000 -895.000000) translate(0,15)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13412d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3284.000000 -356.000000) translate(0,17)">4851569</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_13412d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3284.000000 -356.000000) translate(0,38)">18887829204</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_13414b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5655.000000 -1193.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_13433c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5498.000000 -609.000000) translate(0,15)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_148dc10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5170.000000 -1241.000000) translate(0,15)">35KV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14180c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5492.000000 -703.000000) translate(0,15)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14186f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5619.000000 -803.000000) translate(0,15)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14423c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5258.000000 -791.000000) translate(0,15)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1442600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4835.000000 -805.000000) translate(0,15)">SZ11-10000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_13fd1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5768.000000 -854.000000) translate(0,18)">10kVI段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1401b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5870.000000 -691.000000) translate(0,15)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1402370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5750.000000 -631.000000) translate(0,15)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_139ea70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5784.000000 -223.000000) translate(0,15)">0445</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1358f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5632.000000 -223.000000) translate(0,15)">0435</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_13bcdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5489.000000 -219.000000) translate(0,15)">0425</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1360bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5353.000000 -225.000000) translate(0,15)">0415</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1345710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4897.000000 -217.000000) translate(0,15)">0345</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_137a360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4766.000000 -229.000000) translate(0,15)">0335</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1502d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4433.000000 -856.000000) translate(0,18)">10kVII段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1506b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4566.000000 -689.000000) translate(0,15)">09027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1506ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4445.000000 -631.000000) translate(0,15)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12cd320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5878.000000 -506.000000) translate(0,15)">0451</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12cd950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5759.000000 -541.000000) translate(0,15)">0441</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12cdb90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5758.000000 -427.000000) translate(0,15)">0443</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12cddd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5756.000000 -269.000000) translate(0,15)">0446</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12ce010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5611.000000 -269.000000) translate(0,15)">0436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12ce250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5609.000000 -427.000000) translate(0,15)">0433</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12ce490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5604.000000 -539.000000) translate(0,15)">0431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12ce6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5462.000000 -537.000000) translate(0,15)">0421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12ce910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5463.000000 -421.000000) translate(0,15)">0423</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12ceb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5787.000000 -340.000000) translate(0,15)">04437</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12ced90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5637.000000 -341.000000) translate(0,15)">04337</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12cefd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5493.000000 -343.000000) translate(0,15)">04237</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12cf210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5356.000000 -342.000000) translate(0,15)">04137</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12cf450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4900.000000 -342.000000) translate(0,15)">03437</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12cf690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4866.000000 -426.000000) translate(0,15)">0343</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12cf8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4868.000000 -533.000000) translate(0,15)">0342</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12cfb10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4738.000000 -543.000000) translate(0,15)">0332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12cfd50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4743.000000 -431.000000) translate(0,15)">0333</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12cff90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4768.000000 -347.000000) translate(0,15)">03337</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12d01d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -539.000000) translate(0,15)">0352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12d0410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -426.000000) translate(0,15)">0353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12d0650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4626.000000 -346.000000) translate(0,15)">03537</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12d0890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3833.000000 -530.000000) translate(0,15)">0312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12d0ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3837.000000 -412.000000) translate(0,15)">0316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12d0d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3865.000000 -318.000000) translate(0,15)">03167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_12d0f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5723.000000 -43.000000) translate(0,18)">城区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_12d1500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5568.000000 -42.000000) translate(0,18)">董家湾线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1335610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5409.000000 -47.000000) translate(0,18)">碧城I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_12d2a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5285.000000 -42.000000) translate(0,18)">温泉线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_12d3330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4835.000000 -45.000000) translate(0,18)">硅铁线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_12d3bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4675.000000 -51.000000) translate(0,18)">碧城II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_12d3e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4558.000000 -140.000000) translate(0,18)">旁路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_12d4590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3568.000000 -44.000000) translate(0,18)">科联钛选厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_12d5850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5836.000000 -272.000000) translate(0,18)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12d6650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5324.000000 -271.000000) translate(0,15)">0416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12d6aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5463.000000 -269.000000) translate(0,15)">0426</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12d6ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5326.000000 -539.000000) translate(0,15)">0411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12d6f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5326.000000 -426.000000) translate(0,15)">0413</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12d7160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4868.000000 -271.000000) translate(0,15)">0346</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12d73a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4738.000000 -274.000000) translate(0,15)">0336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12d75e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4591.000000 -270.000000) translate(0,15)">0355</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1313880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4051.000000 -336.000000) translate(0,15)">03837</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1314250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4016.000000 -427.000000) translate(0,15)">0383</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_13144d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4012.000000 -534.000000) translate(0,15)">0382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15f11d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4193.000000 -219.000000) translate(0,15)">0365</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15f2500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4193.000000 -336.000000) translate(0,15)">03637</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15f2b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4168.000000 -425.000000) translate(0,15)">0363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15f2d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4164.000000 -532.000000) translate(0,15)">0362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15f2fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4163.000000 -268.000000) translate(0,15)">0366</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1604aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4351.000000 -334.000000) translate(0,15)">03737</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_16050d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4316.000000 -424.000000) translate(0,15)">0373</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1605310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4312.000000 -531.000000) translate(0,15)">0372</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_160c1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3722.000000 -1010.000000) translate(0,18)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_160cb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4253.000000 -34.000000) translate(0,18)">碧城工业园</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_160cb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4253.000000 -34.000000) translate(0,40)">区 I 回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_160d770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3944.000000 -48.000000) translate(0,18)">碧城工业园</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_160d770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3944.000000 -48.000000) translate(0,40)">区 III 回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_160d980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4106.000000 -37.000000) translate(0,18)">碧城工业园</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_160d980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4106.000000 -37.000000) translate(0,40)">区 I I 回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_160de90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4874.000000 -600.000000) translate(0,18)">10kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_161fe10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5055.578871 -1031.000000) translate(0,15)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1620440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4873.578871 -1036.000000) translate(0,15)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1620680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4749.578871 -1070.000000) translate(0,15)">31227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_16208c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5162.578871 -1069.000000) translate(0,15)">31217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_162fa90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4505.578871 -1016.000000) translate(0,15)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_16300c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4528.578871 -1102.000000) translate(0,15)">39027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1630300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5379.578871 -1026.000000) translate(0,15)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1630540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5405.578871 -1107.000000) translate(0,15)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1632120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4420.000000 -1221.000000) translate(0,18)">35kVII段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1632370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5314.000000 -1227.000000) translate(0,18)">35kVI段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="18" graphid="g_16325b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4055.000000 -1072.000000) translate(0,15)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="22" graphid="g_1632920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4950.000000 -1201.000000) translate(0,18)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="18" graphid="g_1632db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5557.000000 -1079.000000) translate(0,15)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="18" graphid="g_16331b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4850.000000 -829.000000) translate(0,15)">3号主变间隔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="19" graphid="g_1633ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5266.000000 -821.000000) translate(0,16)">2号主变间隔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="18" graphid="g_16356e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5624.000000 -827.000000) translate(0,15)">1号主变间隔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="18" graphid="g_1635ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3638.000000 -429.000000) translate(0,15)">032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_1636240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3670.000000 -333.000000) translate(0,12)">03227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_16367d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3636.000000 -261.000000) translate(0,12)">0326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_1636d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3667.000000 -218.000000) translate(0,12)">0325</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="17" graphid="g_16374b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3835.000000 -478.000000) translate(0,14)">031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="20" graphid="g_1654790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4020.000000 -482.000000) translate(0,16)">038</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="19" graphid="g_16557b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4167.000000 -481.000000) translate(0,16)">036</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="19" graphid="g_1656560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4319.000000 -478.000000) translate(0,16)">037</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="18" graphid="g_1656d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4601.000000 -483.000000) translate(0,15)">035</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="19" graphid="g_16572f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4746.000000 -488.000000) translate(0,16)">033</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="18" graphid="g_1657730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4872.000000 -481.000000) translate(0,15)">034</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="18" graphid="g_1657b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5103.000000 -504.000000) translate(0,15)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="18" graphid="g_1657fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5333.000000 -485.000000) translate(0,15)">041</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="18" graphid="g_16582e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5460.000000 -480.000000) translate(0,15)">042</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="18" graphid="g_1658520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5611.000000 -482.000000) translate(0,15)">043</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="18" graphid="g_1658760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5761.000000 -483.000000) translate(0,15)">044</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="19" graphid="g_1658ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3178.000000 -822.000000) translate(0,16)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_165a060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3465.000000 -1249.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_165b170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3465.000000 -1284.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_1952720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4528.000000 -821.000000) translate(0,8)">U</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_19568b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5068.000000 -440.000000) translate(0,18)">分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_195bd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5834.000000 -819.000000) translate(0,8)">U</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_197b710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -305.000000) translate(0,17)">禄丰巡维中心</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_197b710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -305.000000) translate(0,38)">腰站变值班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_197de80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3291.000000 -294.500000) translate(0,17)">13508785653</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1985ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3489.000000 -1174.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1986e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3567.000000 -665.500000) translate(0,12)">Uc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19872d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3567.000000 -680.750000) translate(0,12)">Ub(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1987510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3567.000000 -696.000000) translate(0,12)">Ua(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1987750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3573.000000 -648.250000) translate(0,12)">U0(V):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1987990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3559.000000 -633.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1987bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3559.000000 -617.000000) translate(0,12)">Ubc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1987e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3559.000000 -602.000000) translate(0,12)">Uca(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1988050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5991.000000 -639.500000) translate(0,12)">Uc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1988290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5991.000000 -654.750000) translate(0,12)">Ub(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19884d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5991.000000 -670.000000) translate(0,12)">Ua(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1988710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5997.000000 -622.250000) translate(0,12)">U0(V):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1988950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5983.000000 -607.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1988b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5983.000000 -591.000000) translate(0,12)">Ubc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1988dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5983.000000 -576.000000) translate(0,12)">Uca(kV):</text>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_14938a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4021.095850 -1177.375000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1431930">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5517.318477 -1175.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1445180">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 5817.000000 -773.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1445c60">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 5766.000000 -751.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1425af0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5712.000000 -313.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_138b770">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5741.500000 -295.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13a0370">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5770.500000 -87.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13a6f00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5560.000000 -313.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13ab5f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5589.500000 -295.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13596b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5618.500000 -87.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13b1100">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5417.000000 -311.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13b5740">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5446.500000 -293.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13bd2f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5475.500000 -85.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12c14f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5281.000000 -315.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12c5be0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5308.500000 -297.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1361110">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5339.500000 -89.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13d6d40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5855.000000 -416.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1318340">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4825.000000 -313.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_131ca30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4852.500000 -295.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1345e40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4881.500000 -87.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_136fe00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4695.000000 -321.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13744f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4722.500000 -303.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_137a8a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4750.500000 -92.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12f2ac0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4550.000000 -316.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12f71b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4577.500000 -298.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1327f10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3624.500000 -290.179487)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_132e940">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3655.500000 -83.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1501280">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3821.500000 -250.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1501fd0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4460.000000 -751.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12fdf10">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4511.000000 -774.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_130dcd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3968.000000 -322.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1312410">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3996.500000 -296.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15e6260">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4121.000000 -312.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15ea9a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4148.500000 -294.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15f19f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4179.500000 -86.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15feeb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4269.000000 -311.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16035f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4296.500000 -293.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_160e1d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.416667 4358.000000 -554.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_160ed60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.416667 4475.000000 -553.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1639660">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3785.000000 -287.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_163a2e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3597.000000 -326.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_194a2d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4660.000000 -748.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_194c210">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4676.000000 -723.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_194cfc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4814.000000 -728.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_194df90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4768.000000 -833.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1954c30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4590.000000 -194.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1957270">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5308.500000 -78.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1958780">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5474.000000 -717.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_195a700">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.619048 5573.500000 -838.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_195c720">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5129.000000 -738.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="LF_BC"/>
</svg>